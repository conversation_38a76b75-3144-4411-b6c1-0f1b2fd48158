# Project Overview

SimiDigital is a comprehensive application designed to streamline digital workflows and processes. It includes a variety of shared components that enhance functionality and user experience.

## Installation and Setup

To set up the project, follow these steps:

1. **Clone the repository**:
   ```bash
   git clone [repository-url]
   cd simidigital
   ```

2. **Install dependencies using Yarn (recommended)**:
   ```bash
   yarn install
   ```

3. **Start the development server**:
   ```bash
   yarn dev
   ```

## Project Structure

The project is organized into several key directories:

- **src/**: Contains the main source code for the application, including components, layouts, and pages.
- **public/**: Includes static assets such as images, icons, and fonts.
- **docs/**: Documentation files for shared components and other project aspects.
- **k8s/**: Kubernetes configuration files for deployment.

## Development Workflow

- **Running the Project**: Use `yarn dev` to start the development server.
- **Building for Production**: Use `yarn build` to compile the project for production.
- **Testing**: Run tests using `yarn test`.

## Shared Components

This project includes several shared components that are used across different parts of the application. Below is a summary of these components with links to their detailed documentation.

### ApprovalStepper
A component that visually represents the steps in an approval process.
- [Detailed Documentation](docs/ApprovalStepper.md)

### ApproveRejectButtonGroup
A component that manages approval actions within a workflow, allowing users to submit, approve, or revert stages.
- [Detailed Documentation](docs/ApproveRejectButtonGroup.md)

For more information on each component, please refer to their respective documentation files linked above.