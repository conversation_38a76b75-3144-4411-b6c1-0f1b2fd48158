@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,600;1,400;1,600&display=swap');

@font-face {
  font-family: 'HkGrotesk';
  src: url('/fonts/hk-grotesk-wide/hkgroteskwide-black.ttf') format('truetype');
}

@font-face {
  font-family: 'RobotoMedium';
  src: url('/fonts/Roboto/Roboto-Medium.ttf') format('truetype');
}

@font-face {
  font-family: 'RobotoRegular';
  src: url('/fonts/Roboto/Roboto-Regular.ttf') format('truetype');
}

@layer components {
  .cta-btn {
    @apply max-h-8  cursor-pointer rounded-md border border-white p-1 text-sm font-semibold text-white hover:brightness-125 disabled:cursor-not-allowed disabled:bg-gray-500 disabled:hover:brightness-100;
    transition: transform 0.3s ease-in-out;
  }

  /* FA Module Enhanced Modern Styles */
  .fa-card {
    @apply overflow-hidden rounded-2xl border border-slate-200/60 bg-white shadow-sm;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .fa-card:hover {
    @apply shadow-md border-slate-300/60;
  }

  .fa-card-header {
    @apply border-b border-slate-200/60 bg-gradient-to-r from-slate-50 to-blue-50/50 px-8 py-6;
  }

  .fa-card-title {
    @apply flex items-center gap-3 text-lg font-semibold text-slate-900;
  }

  .fa-card-content {
    @apply p-8;
  }

  .fa-table {
    @apply min-w-full overflow-hidden rounded-xl border border-slate-200/60 bg-white shadow-sm;
  }

  .fa-table-header {
    @apply bg-gradient-to-r from-slate-50 to-blue-50/30 text-sm font-semibold text-slate-900 border-b border-slate-200/60;
  }

  .fa-table-header-cell {
    @apply border-r border-slate-200/60 px-6 py-4 text-left font-semibold text-slate-900 last:border-r-0;
  }

  .fa-table-body {
    @apply divide-y divide-slate-200/60;
  }

  .fa-table-row {
    @apply cursor-pointer transition-colors duration-200 hover:bg-slate-50/80;
  }

  .fa-table-cell {
    @apply border-r border-slate-200/60 px-6 py-4 text-sm text-slate-700 last:border-r-0;
  }

  .fa-input-group {
    @apply space-y-2;
  }

  .fa-input-label {
    @apply mb-2 block text-sm font-semibold text-slate-700;
  }

  .fa-input {
    @apply w-full rounded-lg border border-slate-300 px-4 py-3 shadow-sm transition-all duration-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10 hover:border-slate-400;
    background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  }

  .fa-button-primary {
    @apply inline-flex items-center gap-2 rounded-lg bg-blue-600 px-4 py-2.5 font-semibold text-white shadow-sm transition-all duration-200 hover:bg-blue-700 hover:shadow-md focus:ring-4 focus:ring-blue-500/20;
  }

  .fa-button-secondary {
    @apply inline-flex items-center gap-2 rounded-lg bg-slate-100 px-4 py-2.5 font-semibold text-slate-700 shadow-sm transition-all duration-200 hover:bg-slate-200 hover:shadow-md focus:ring-4 focus:ring-slate-500/20;
  }

  .fa-button-danger {
    @apply inline-flex items-center gap-2 rounded-lg bg-red-600 px-4 py-2.5 font-semibold text-white shadow-sm transition-all duration-200 hover:bg-red-700 hover:shadow-md focus:ring-4 focus:ring-red-500/20;
  }

  .fa-section {
    @apply mb-8 overflow-hidden rounded-2xl border border-slate-200/60 bg-white shadow-sm;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .fa-section:hover {
    @apply shadow-md border-slate-300/60;
  }

  .fa-section-header {
    @apply flex items-center justify-between border-b border-slate-200/60 bg-gradient-to-r from-slate-50 to-blue-50/30 px-8 py-6;
  }

  .fa-section-title {
    @apply flex items-center gap-3 text-lg font-semibold text-slate-900;
  }

  .fa-section-content {
    @apply p-8;
  }

  .fa-stats-card {
    @apply rounded-xl border border-slate-200/60 bg-white p-6 shadow-sm transition-all duration-200 hover:shadow-md hover:border-slate-300/60;
  }

  .fa-stats-value {
    @apply text-3xl font-bold text-slate-900;
  }

  .fa-stats-label {
    @apply mt-2 text-sm font-medium text-slate-600;
  }

  .fa-badge {
    @apply inline-flex items-center rounded-full px-3 py-1 text-xs font-semibold;
  }

  .fa-badge-success {
    @apply bg-green-100 text-green-800 border border-green-200;
  }

  .fa-badge-warning {
    @apply bg-amber-100 text-amber-800 border border-amber-200;
  }

  .fa-badge-error {
    @apply bg-red-100 text-red-800 border border-red-200;
  }

  .fa-badge-info {
    @apply bg-blue-100 text-blue-800 border border-blue-200;
  }

  .fa-dialog {
    @apply mx-auto max-w-2xl rounded-2xl border border-slate-200/60 bg-white shadow-xl;
  }

  .fa-dialog-header {
    @apply rounded-t-2xl bg-gradient-to-r from-slate-50 to-blue-50/50 px-8 py-6 border-b border-slate-200/60;
  }

  .fa-dialog-title {
    @apply text-lg font-semibold text-slate-900;
  }

  .fa-dialog-content {
    @apply p-8;
  }

  .fa-dialog-actions {
    @apply flex justify-end gap-3 rounded-b-2xl bg-gradient-to-r from-slate-50 to-blue-50/30 px-8 py-6 border-t border-slate-200/60;
  }

  /* Enhanced Form Styling */
  .fa-form-section {
    @apply bg-gradient-to-br from-slate-50 to-blue-50/30 rounded-xl p-6 border border-slate-100 mb-6;
  }

  .fa-form-section-title {
    @apply text-lg font-semibold text-slate-900 mb-4 flex items-center gap-3;
  }

  .fa-form-grid {
    @apply grid grid-cols-1 md:grid-cols-2 gap-6;
  }

  .fa-form-field {
    @apply space-y-2;
  }

  .fa-form-field-full {
    @apply col-span-full space-y-2;
  }

  .fa-form-help-text {
    @apply text-xs text-slate-500 mt-1;
  }

  /* Enhanced Approval Workflow */
  .fa-approval-step {
    @apply flex flex-col items-center text-center p-4 rounded-lg transition-all duration-200;
  }

  .fa-approval-step.active {
    @apply bg-blue-50 border border-blue-200;
  }

  .fa-approval-step.completed {
    @apply bg-green-50 border border-green-200;
  }

  /* Enhanced Timeline */
  .fa-timeline {
    @apply space-y-4;
  }

  .fa-timeline-item {
    @apply flex gap-4 p-4 rounded-lg bg-slate-50 border border-slate-200 transition-all duration-200 hover:shadow-sm;
  }

  .fa-timeline-icon {
    @apply w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0;
  }

  .fa-timeline-content {
    @apply flex-1 min-w-0;
  }

  .fa-timeline-title {
    @apply font-semibold text-slate-900 text-sm;
  }

  .fa-timeline-description {
    @apply text-sm text-slate-600 mt-1;
  }

  .fa-timeline-time {
    @apply text-xs text-slate-500 mt-2;
  }

  /* Responsive Design Enhancements */
  @media (max-width: 768px) {
    .fa-section {
      @apply rounded-xl;
    }

    .fa-card {
      @apply rounded-xl;
    }

    .fa-section-header,
    .fa-card-header {
      @apply px-4 py-4;
    }

    .fa-section-content,
    .fa-card-content {
      @apply p-4;
    }

    .fa-table-header-cell,
    .fa-table-cell {
      @apply px-3 py-3;
    }
  }

  /* Animation Utilities */
  .fa-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .fa-slide-up {
    animation: slideUp 0.3s ease-in-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from { 
      opacity: 0; 
      transform: translateY(20px); 
    }
    to { 
      opacity: 1; 
      transform: translateY(0); 
    }
  }
}
