[{"id": 1, "initiative": "5G Dual Network – NGBH E-Lan", "projectName": "5G Dual Network – NGBH E-Lan", "projectOwner": "<PERSON>", "projectSponsor": "<PERSON><PERSON><PERSON>", "status": "On Track", "completion": 0, "startDate": "1-Feb-25", "endDate": "15-Mar-25", "description": "5G Dual Network – NGBH E-Lan project", "ocm": {"startPlanDate": "1-Feb-25", "planDate": "15-Feb-25", "startActualDate": "1-Feb-25", "actualDate": "1-Apr-25", "planPercentage": 10.0, "actualPercentage": 0.0}, "pcm_concept": {"startPlanDate": "NA", "planDate": "NA", "startActualDate": "NA", "actualDate": "NA", "planPercentage": 0.0, "actualPercentage": 0.0}, "poc": {"startPlanDate": "", "planDate": "15-Mar-25", "startActualDate": "", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "be": {"startPlanDate": "", "planDate": "1-Mar-25", "startActualDate": "", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "ia": {"startPlanDate": "", "planDate": "20-Feb-25", "startActualDate": "", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "pcm_biz": {"startPlanDate": "", "planDate": "1-Mar-25", "startActualDate": "", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "dev": {"startPlanDate": "", "planDate": "31-Mar-25", "startActualDate": "", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "proc": {"startPlanDate": "", "planDate": "N/A", "startActualDate": "", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "launch_checkpoint": {"startPlanDate": "", "planDate": "31-Mar-25", "startActualDate": "", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "launch_rfs": {"startPlanDate": "", "planDate": "Date?", "startActualDate": "", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "pir": {"startPlanDate": "", "planDate": "Date?", "startActualDate": "", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "total": {"plan": 90, "actual": 0}, "achievement": 0.0, "planRFS": "Date?", "revisedRFS": "Date?", "actualRFS": "Date?", "delay": {"vsPlanned": "0", "vsRevised": "0"}, "timelyCompletion": {"vsPlanned": "Not Started", "vsRevised": "Not Started"}, "highlight": "Project in early stages. OCM phase delayed.", "support": "Need support for OCM phase completion."}, {"id": 2, "initiative": "5G MVNE", "projectName": "5G MVNE", "projectOwner": "<PERSON><PERSON><PERSON>", "projectSponsor": "<PERSON><PERSON><PERSON>", "status": "Not Started", "completion": 0, "startDate": "Date?", "endDate": "Date?", "description": "5G MVNE project", "ocm": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "pcm_concept": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "poc": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "be": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "ia": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "pcm_biz": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "dev": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 20.0, "actualPercentage": 0.0}, "proc": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "launch_checkpoint": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "launch_rfs": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "pir": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "total": {"plan": 100, "actual": 0}, "achievement": 0.0, "planRFS": "Date?", "revisedRFS": "Date?", "actualRFS": "Date?", "delay": {"vsPlanned": "0", "vsRevised": "0"}, "timelyCompletion": {"vsPlanned": "Not Started", "vsRevised": "Not Started"}, "highlight": "Project not yet started.", "support": "Need project kickoff and resource planning."}, {"id": 3, "initiative": "FWA", "projectName": "FWA", "projectOwner": "<PERSON><PERSON><PERSON><PERSON>", "projectSponsor": "<PERSON><PERSON><PERSON>", "status": "Not Started", "completion": 0, "startDate": "Date?", "endDate": "Date?", "description": "FWA project", "ocm": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "pcm_concept": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "poc": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "be": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "ia": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "pcm_biz": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "dev": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 20.0, "actualPercentage": 0.0}, "proc": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "launch_checkpoint": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "launch_rfs": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "pir": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "total": {"plan": 100, "actual": 0}, "achievement": 0.0, "planRFS": "Date?", "revisedRFS": "Date?", "actualRFS": "Date?", "delay": {"vsPlanned": "0", "vsRevised": "0"}, "timelyCompletion": {"vsPlanned": "Not Started", "vsRevised": "Not Started"}, "highlight": "Project initiation pending.", "support": "Resource allocation needed."}, {"id": 4, "initiative": "Pole Sharing", "projectName": "Pole Sharing", "projectOwner": "<PERSON><PERSON>", "projectSponsor": "<PERSON><PERSON><PERSON>", "status": "Not Started", "completion": 0, "startDate": "Date?", "endDate": "Date?", "description": "Pole Sharing project", "ocm": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "pcm_concept": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "poc": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "be": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "ia": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "pcm_biz": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "dev": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 20.0, "actualPercentage": 0.0}, "proc": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "launch_checkpoint": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "launch_rfs": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "pir": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "total": {"plan": 100, "actual": 0}, "achievement": 0.0, "planRFS": "Date?", "revisedRFS": "Date?", "actualRFS": "Date?", "delay": {"vsPlanned": "0", "vsRevised": "0"}, "timelyCompletion": {"vsPlanned": "Not Started", "vsRevised": "Not Started"}, "highlight": "Planning phase pending.", "support": "Initial planning support required."}, {"id": 5, "initiative": "Partner Scorecard", "projectName": "Partner Scorecard", "projectOwner": "", "projectSponsor": "Amilia", "status": "Not Started", "completion": 0, "startDate": "Date?", "endDate": "Date?", "description": "Partner Scorecard project", "ocm": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "pcm_concept": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "poc": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "be": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "ia": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "pcm_biz": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "dev": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 20.0, "actualPercentage": 0.0}, "proc": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "launch_checkpoint": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "launch_rfs": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "pir": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "total": {"plan": 100, "actual": 0}, "achievement": 0.0, "planRFS": "Date?", "revisedRFS": "Date?", "actualRFS": "Date?", "delay": {"vsPlanned": "0", "vsRevised": "0"}, "timelyCompletion": {"vsPlanned": "Not Started", "vsRevised": "Not Started"}, "highlight": "Project initiation pending.", "support": "Need project owner assignment."}, {"id": 6, "initiative": "LOA Tier 2 approval in SIMI", "projectName": "LOA Tier 2 approval in SIMI", "projectOwner": "", "projectSponsor": "Amilia", "status": "Not Started", "completion": 0, "startDate": "Date?", "endDate": "Date?", "description": "LOA Tier 2 approval in SIMI project", "ocm": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "pcm_concept": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "poc": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "be": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "ia": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "pcm_biz": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "dev": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 20.0, "actualPercentage": 0.0}, "proc": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "launch_checkpoint": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "launch_rfs": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "pir": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "total": {"plan": 100, "actual": 0}, "achievement": 0.0, "planRFS": "Date?", "revisedRFS": "Date?", "actualRFS": "Date?", "delay": {"vsPlanned": "0", "vsRevised": "0"}, "timelyCompletion": {"vsPlanned": "Not Started", "vsRevised": "Not Started"}, "highlight": "Project not started.", "support": "Need project owner assignment and requirements."}, {"id": 7, "initiative": "PO automation for global partner for non SUS", "projectName": "PO automation for global partner for non SUS", "projectOwner": "", "projectSponsor": "Amilia", "status": "Not Started", "completion": 0, "startDate": "Date?", "endDate": "Date?", "description": "PO automation for global partner for non SUS project", "ocm": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "pcm_concept": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "poc": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "be": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "ia": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "pcm_biz": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "dev": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 20.0, "actualPercentage": 0.0}, "proc": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "launch_checkpoint": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "launch_rfs": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "pir": {"startPlanDate": "Date?", "planDate": "Date?", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "total": {"plan": 100, "actual": 0}, "achievement": 0.0, "planRFS": "Date?", "revisedRFS": "Date?", "actualRFS": "Date?", "delay": {"vsPlanned": "0", "vsRevised": "0"}, "timelyCompletion": {"vsPlanned": "Not Started", "vsRevised": "Not Started"}, "highlight": "Project not started.", "support": "Need project owner assignment and requirements."}]