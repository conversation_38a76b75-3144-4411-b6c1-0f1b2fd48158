// Next, React, Tailwind
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Material UI
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Breadcrumbs,
  Link as MuiLink,
  Box
} from '@mui/material';
import {
  Home,
  Assignment,
  Assessment,
  Description,
  TableChart,
  PictureAsPdf
} from '@mui/icons-material';

// Components
import Layout from '../../layouts/module/project-tracker';
import { EmptyState } from '../../components/ui/empty-state';

// Others
import { setBreadCrumbsList } from '../../utils/store/simiReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  const dispatch = useDispatch();

  useEffect(() => {
    // Set breadcrumbs
    dispatch(setBreadCrumbsList([
      { label: 'Home', path: '/', icon: <Home fontSize="small" /> },
      { label: 'Project Tracker', path: '/project-tracker', icon: <Assignment fontSize="small" /> },
      { label: 'Reports', path: '/project-tracker/reports', icon: <Assessment fontSize="small" /> }
    ]));
  }, [dispatch]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="h4" className="text-gray-900 dark:text-white font-bold">
                Project Reports
              </Typography>
              <Typography variant="body1" className="text-gray-600 dark:text-gray-400 mt-1">
                Generate and export project reports
              </Typography>
            </div>
          </div>

          {/* Breadcrumbs */}
          <div className="mt-4">
            <Breadcrumbs 
              aria-label="breadcrumb"
              className="text-gray-600 dark:text-gray-400"
            >
              <MuiLink
                color="inherit"
                href="/"
                className="flex items-center hover:text-blue-600"
                underline="hover"
              >
                <Home className="mr-1" fontSize="small" />
                Home
              </MuiLink>
              <MuiLink
                color="inherit"
                href="/project-tracker"
                className="flex items-center hover:text-blue-600"
                underline="hover"
              >
                <Assignment className="mr-1" fontSize="small" />
                Project Tracker
              </MuiLink>
              <Typography color="text.primary" className="flex items-center">
                <Assessment className="mr-1" fontSize="small" />
                Reports
              </Typography>
            </Breadcrumbs>
          </div>
        </div>

        {/* Content */}
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent className="p-8">
                <EmptyState
                  title="Reports Coming Soon"
                  description="Project reporting features are currently under development. This page will include customizable reports, export functionality, and automated report generation."
                  icon={<Assessment className="w-16 h-16 text-gray-400" />}
                />
              </CardContent>
            </Card>
          </Grid>

          {/* Placeholder cards for future report features */}
          <Grid item xs={12} md={6} lg={4}>
            <Card className="h-full">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <Description className="w-8 h-8 text-blue-500 mr-3" />
                  <Typography variant="h6" className="text-gray-900 dark:text-white">
                    Project Summary Reports
                  </Typography>
                </div>
                <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                  Generate comprehensive project summaries including status, milestones, and team information.
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6} lg={4}>
            <Card className="h-full">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <TableChart className="w-8 h-8 text-green-500 mr-3" />
                  <Typography variant="h6" className="text-gray-900 dark:text-white">
                    Data Export
                  </Typography>
                </div>
                <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                  Export project data in various formats including CSV, Excel, and JSON for external analysis.
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6} lg={4}>
            <Card className="h-full">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <PictureAsPdf className="w-8 h-8 text-red-500 mr-3" />
                  <Typography variant="h6" className="text-gray-900 dark:text-white">
                    PDF Reports
                  </Typography>
                </div>
                <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                  Generate professional PDF reports for stakeholders and project documentation.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </div>
    </div>
  );
}
