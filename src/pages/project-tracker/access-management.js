// Next, React, Tailwind
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Material UI
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Avatar,
  Tooltip,
  Breadcrumbs,
  Link as MuiLink
} from '@mui/material';
import {
  Home,
  Assignment,
  Edit,
  Delete,
  Add,
  AdminPanelSettings,
  Security,
  People
} from '@mui/icons-material';

// Components
import Layout from '../../layouts/module/project-tracker';
import { EnhancedButton } from '../../components/fa/ui/EnhancedButton';
import { EmptyState } from '../../components/ui/empty-state';

// Others
import { setBreadCrumbsList } from '../../utils/store/simiReducer';
import { useSnackbar } from '../../components/Shared/snackbar';
import axios from '../../utils/axios';
import { PROJECT_TRACKER_ENDPOINT } from '../../utils/project-tracker';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    role: '',
    permissions: [],
    isActive: true
  });

  useEffect(() => {
    dispatch(setBreadCrumbsList([
      { label: 'Home', path: '/', icon: <Home fontSize="small" /> },
      { label: 'Project Tracker', path: '/project-tracker', icon: <Assignment fontSize="small" /> },
      { label: 'Access Management', path: '/project-tracker/access-management', icon: <AdminPanelSettings fontSize="small" /> }
    ]));

    fetchData();
  }, [dispatch]);

  const fetchData = async () => {
    setIsLoading(true);
    try {
      const [usersRes, rolesRes, permissionsRes] = await Promise.all([
        axios.get(`${PROJECT_TRACKER_ENDPOINT}/admin/users`),
        axios.get(`${PROJECT_TRACKER_ENDPOINT}/admin/roles`),
        axios.get(`${PROJECT_TRACKER_ENDPOINT}/admin/permissions`)
      ]);

      setUsers(usersRes?.data?.data || []);
      setRoles(rolesRes?.data?.data || []);
      setPermissions(permissionsRes?.data?.data || []);
    } catch (error) {
      enqueueSnackbar('Failed to fetch access data', { variant: 'error' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenDialog = (user = null) => {
    if (user) {
      setEditingUser(user);
      setFormData({
        name: user.name || '',
        email: user.email || '',
        role: user.role || '',
        permissions: user.permissions || [],
        isActive: user.isActive !== false
      });
    } else {
      setEditingUser(null);
      setFormData({
        name: '',
        email: '',
        role: '',
        permissions: [],
        isActive: true
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingUser(null);
  };

  const handleFormChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handlePermissionToggle = (permission) => {
    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permission)
        ? prev.permissions.filter(p => p !== permission)
        : [...prev.permissions, permission]
    }));
  };

  const handleSubmit = async () => {
    try {
      if (editingUser) {
        await axios.patch(`${PROJECT_TRACKER_ENDPOINT}/admin/users/${editingUser.id}`, formData);
        enqueueSnackbar('User updated successfully', { variant: 'success' });
      } else {
        await axios.post(`${PROJECT_TRACKER_ENDPOINT}/admin/users`, formData);
        enqueueSnackbar('User created successfully', { variant: 'success' });
      }
      
      await fetchData();
      handleCloseDialog();
    } catch (error) {
      enqueueSnackbar('Failed to save user', { variant: 'error' });
    }
  };

  const handleDeleteUser = async (userId) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      try {
        await axios.delete(`${PROJECT_TRACKER_ENDPOINT}/admin/users/${userId}`);
        enqueueSnackbar('User deleted successfully', { variant: 'success' });
        await fetchData();
      } catch (error) {
        enqueueSnackbar('Failed to delete user', { variant: 'error' });
      }
    }
  };

  const getRoleColor = (role) => {
    const colors = {
      admin: 'text-red-600 bg-red-100',
      manager: 'text-purple-600 bg-purple-100',
      user: 'text-blue-600 bg-blue-100',
      viewer: 'text-gray-600 bg-gray-100'
    };
    return colors[role] || colors.user;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto px-4 py-6">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
            <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <Typography variant="h3" className="font-bold text-gray-900 dark:text-white mb-2">
                Access Management
              </Typography>
              <Typography variant="body1" className="text-gray-600 dark:text-gray-400">
                Manage user access and permissions for the Project Tracker module
              </Typography>
            </div>
            
            <EnhancedButton
              variant="contained"
              startIcon={<Add />}
              onClick={() => handleOpenDialog()}
            >
              Add User
            </EnhancedButton>
          </div>

          {/* Breadcrumbs */}
          <div className="mt-4">
            <Breadcrumbs 
              aria-label="breadcrumb"
              className="text-gray-600 dark:text-gray-400"
            >
              <MuiLink
                color="inherit"
                href="/"
                className="flex items-center hover:text-blue-600"
                underline="hover"
              >
                <Home className="mr-1" fontSize="small" />
                Home
              </MuiLink>
              <MuiLink
                color="inherit"
                href="/project-tracker"
                className="flex items-center hover:text-blue-600"
                underline="hover"
              >
                <Assignment className="mr-1" fontSize="small" />
                Project Tracker
              </MuiLink>
              <Typography color="text.primary" className="flex items-center">
                <AdminPanelSettings className="mr-1" fontSize="small" />
                Access Management
              </Typography>
            </Breadcrumbs>
          </div>
        </div>

        {/* Stats Cards */}
        <Grid container spacing={3} className="mb-6">
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent className="text-center">
                <People className="w-12 h-12 text-blue-600 mx-auto mb-2" />
                <Typography variant="h4" className="font-bold text-gray-900 dark:text-white">
                  {users.length}
                </Typography>
                <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                  Total Users
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent className="text-center">
                <AdminPanelSettings className="w-12 h-12 text-purple-600 mx-auto mb-2" />
                <Typography variant="h4" className="font-bold text-gray-900 dark:text-white">
                  {users.filter(u => u.role === 'admin').length}
                </Typography>
                <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                  Administrators
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent className="text-center">
                <Security className="w-12 h-12 text-green-600 mx-auto mb-2" />
                <Typography variant="h4" className="font-bold text-gray-900 dark:text-white">
                  {users.filter(u => u.isActive !== false).length}
                </Typography>
                <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                  Active Users
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent className="text-center">
                <Assignment className="w-12 h-12 text-orange-600 mx-auto mb-2" />
                <Typography variant="h4" className="font-bold text-gray-900 dark:text-white">
                  {roles.length}
                </Typography>
                <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                  Available Roles
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Users Table */}
        <Card>
          <CardContent className="p-0">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <Typography variant="h6" className="text-gray-900 dark:text-white">
                User Management
              </Typography>
            </div>

            {users.length === 0 ? (
              <div className="p-6">
                <EmptyState
                  title="No users found"
                  description="Get started by adding your first user"
                  action={
                    <EnhancedButton
                      variant="contained"
                      onClick={() => handleOpenDialog()}
                    >
                      Add User
                    </EnhancedButton>
                  }
                />
              </div>
            ) : (
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>User</TableCell>
                    <TableCell>Role</TableCell>
                    <TableCell>Permissions</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar>{user.name?.charAt(0)}</Avatar>
                          <div>
                            <Typography variant="subtitle2" className="text-gray-900 dark:text-white">
                              {user.name}
                            </Typography>
                            <Typography variant="caption" className="text-gray-500">
                              {user.email}
                            </Typography>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={user.role}
                          size="small"
                          className={`${getRoleColor(user.role)} font-medium`}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {user.permissions?.slice(0, 3).map((permission) => (
                            <Chip
                              key={permission}
                              label={permission}
                              size="small"
                              variant="outlined"
                              className="text-xs"
                            />
                          ))}
                          {user.permissions?.length > 3 && (
                            <Chip
                              label={`+${user.permissions.length - 3}`}
                              size="small"
                              variant="outlined"
                              className="text-xs"
                            />
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={user.isActive !== false ? 'Active' : 'Inactive'}
                          size="small"
                          color={user.isActive !== false ? 'success' : 'error'}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Tooltip title="Edit User">
                            <IconButton
                              size="small"
                              onClick={() => handleOpenDialog(user)}
                            >
                              <Edit fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete User">
                            <IconButton
                              size="small"
                              onClick={() => handleDeleteUser(user.id)}
                              className="text-red-600 hover:bg-red-50"
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* User Dialog */}
        <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>
            {editingUser ? 'Edit User' : 'Add New User'}
          </DialogTitle>
          
          <DialogContent className="space-y-4">
            <TextField
              fullWidth
              label="Name"
              value={formData.name}
              onChange={(e) => handleFormChange('name', e.target.value)}
              margin="normal"
            />
            
            <TextField
              fullWidth
              label="Email"
              type="email"
              value={formData.email}
              onChange={(e) => handleFormChange('email', e.target.value)}
              margin="normal"
            />

            <FormControl fullWidth margin="normal">
              <InputLabel>Role</InputLabel>
              <Select
                value={formData.role}
                onChange={(e) => handleFormChange('role', e.target.value)}
                label="Role"
              >
                {roles.map((role) => (
                  <MenuItem key={role.id} value={role.name}>
                    {role.display_name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <div>
              <Typography variant="subtitle2" className="mb-2">
                Permissions
              </Typography>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {permissions.map((permission) => (
                  <FormControlLabel
                    key={permission.id}
                    control={
                      <Switch
                        checked={formData.permissions.includes(permission.name)}
                        onChange={() => handlePermissionToggle(permission.name)}
                      />
                    }
                    label={
                      <div>
                        <Typography variant="body2">{permission.display_name}</Typography>
                        <Typography variant="caption" className="text-gray-500">
                          {permission.description}
                        </Typography>
                      </div>
                    }
                  />
                ))}
              </div>
            </div>

            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={(e) => handleFormChange('isActive', e.target.checked)}
                />
              }
              label="Active User"
            />
          </DialogContent>

          <DialogActions>
            <EnhancedButton
              variant="outlined"
              onClick={handleCloseDialog}
            >
              Cancel
            </EnhancedButton>
            <EnhancedButton
              variant="contained"
              onClick={handleSubmit}
            >
              {editingUser ? 'Update' : 'Create'}
            </EnhancedButton>
          </DialogActions>
        </Dialog>
      </div>
    </div>
  );
}