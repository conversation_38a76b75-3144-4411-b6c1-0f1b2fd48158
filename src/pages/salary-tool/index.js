// Mui
import { Divider } from '@mui/material';

// Components
import Layout from '../../layouts/module/salary';
import FutureSalary from '../../components/salary-tool/FutureSalary';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  return (
    <div className="container mx-auto flex h-full flex-col p-1 md:p-4">
      <div
        style={{ boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)' }}
        className="flex flex-grow flex-col gap-4 rounded-xl bg-white p-4 text-black dark:bg-gray-600 dark:text-white"
      >
        <FutureSalary />
        <Divider />
      </div>
    </div>
  );
}
