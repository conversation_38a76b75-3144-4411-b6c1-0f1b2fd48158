// Next, React, Tw
import { Fragment, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Tabs, Tab, Switch } from '@mui/material';

// Packages
import moment from 'moment';

// Components
import Layout from '../../layouts/module/finsight';
import SummaryTable from '../../components/finsight/SummaryTable';
import { SearchInput } from '../../components/Shared/CustomInput';

// Others
import { useParamContext } from '../../utils/auth/ParamProvider';
import { SEGMENTS } from '../../utils/finsight';
import { useSimiContext } from '../../utils/simi';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { replaceParam, setParam } = useParamContext();
  const { moduleColorCode } = useSimiContext();
  const { lastUpdatedAtUnix } = useSelector((state) => state.finsight);

  const { query } = useRouter();
  const { view, sortBy } = query;

  // Others

  useEffect(() => {
    if (!view) replaceParam({ view: 'month', sortBy: 'all' });
  }, [view]);

  return (
    <>
      <div className="bg-finsight px-4 py-2 text-white">
        <div className="flex flex-col items-center justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-extrabold">Forecast Performance</p>
            <p className="text-xs">
              Last Updated At : {moment.unix(lastUpdatedAtUnix)?.format('DD-MM-YYYY hh:mm A')}
            </p>
          </div>
          <Tabs
            value={view}
            onChange={(event, newValue) => setParam({ view: newValue })}
            variant="standard"
            centered
            sx={{
              '& .MuiTabs-indicator': {
                backgroundColor: 'white',
              },
              '& .MuiTab-root': {
                color: 'white',
              },
              '& .Mui-selected': {
                color: 'white',
              },
            }}
            className="text-white"
          >
            {['month', 'quarter', 'ytd']?.map((o, i) => (
              <Tab key={i} label={o.toUpperCase()} value={o} />
            ))}
          </Tabs>
        </div>
      </div>
      <div className="container mx-auto flex flex-col gap-2 p-1 md:p-4">
        <div className="flex flex-col gap-4 rounded-md bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
          <div className="flex justify-between">
            <div className="flex items-center">
              <p className="text-sm">By Segments</p>
              <Switch
                checked={sortBy === 'all'}
                sx={{
                  '& .MuiSwitch-switchBase': {
                    color: moduleColorCode,
                  },
                  '& .MuiSwitch-switchBase + .MuiSwitch-track': {
                    backgroundColor: moduleColorCode,
                  },
                  '& .MuiSwitch-switchBase.Mui-checked': {
                    color: moduleColorCode,
                  },
                  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                    backgroundColor: moduleColorCode,
                  },
                }}
                onChange={() => {
                  if (sortBy === 'all') {
                    setParam({ sortBy: 'segment' });
                    return;
                  }
                  setParam({ sortBy: 'all' });
                }}
              />
              <p className="text-sm">All</p>
            </div>
            <SearchInput />
          </div>
          {sortBy === 'all' && <SummaryTable segment="Overall TMG" page="forecast-performance" />}
          {sortBy === 'segment' && (
            <>
              {SEGMENTS?.map((segment) => (
                <SummaryTable segment={segment} page="forecast-performance" />
              ))}
            </>
          )}
        </div>
      </div>
    </>
  );
}
