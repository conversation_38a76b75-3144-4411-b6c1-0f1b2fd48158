// Next, React, Tw
import { Fragment } from 'react';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Divider } from '@mui/material';

// Packages
import moment from 'moment';

// Components
import Layout from '../../layouts/module/finsight';
import UploadExcelButton from '../../components/finsight/UploadExcelButton';
import MonthlyBreakdownTable from '../../components/finsight/MonthlyBreakdownTable';
import { SearchInput } from '../../components/Shared/CustomInput';

// Others
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { SEGMENTS } from '../../utils/finsight';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { lastUpdatedAtUnix, sourcesData } = useSelector((state) => state.finsight);
  const { isAdmin } = useModuleRoleContext();
  const { query } = useRouter();
  const { q } = query;

  // Table
  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return sourcesData;
    }
    return sourcesData.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  return (
    <>
      <div className="bg-finsight px-4 py-2 text-white">
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-extrabold">Monthly Breakdown</p>
            <p className="text-xs">
              Last Updated At : {moment.unix(lastUpdatedAtUnix)?.format('DD-MM-YYYY hh:mm A')}
            </p>
          </div>
        </div>
      </div>
      <div className="container mx-auto flex flex-col gap-2 p-1 md:p-4">
        <div className="flex flex-col gap-4 rounded-md bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
          <div className="flex justify-between">
            <SearchInput />
            {isAdmin && <UploadExcelButton />}
          </div>
          {SEGMENTS?.map((segment) => (
            <>
              <Divider />
              <p className="text-center text-xl font-bold underline">{segment}</p>
              <MonthlyBreakdownTable
                array={filteredTableData?.filter((source) => source?.segment === segment)}
              />
            </>
          ))}
        </div>
      </div>
    </>
  );
}
