// Next, React, Tw
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { twMerge } from 'tailwind-merge';

// Mui
import { Popover } from '@mui/material';
import { KeyboardArrowDown, SwapHoriz } from '@mui/icons-material';

// Packages
import moment from 'moment';

// Components
import Layout from '../../layouts/module/exchange-rate';
import { TextInput } from '../../components/Shared/CustomInput';

// Others
import { setIsLoading } from '../../utils/store/loadingReducer';
import { AUM_ENDPOINT } from '../../utils/aum';
import axios from '../../utils/axios';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars

  const dispatch = useDispatch();
  const [exchageRateData, setExchangeRateData] = useState({});

  const [popOver1Position, setPopOver1Position] = useState(null);
  const [popOver2Position, setPopOver2Position] = useState(null);

  // Form
  const [formData, setFormData] = useState({
    fromCurrency: 'USD',
    fromValue: 1,
    toCurrency: 'MYR',
  });

  // Others
  const rateData = exchageRateData?.data || {};
  const currencyList = ['MYR', ...Object?.keys(rateData)];

  const closeAllPopover = () => {
    setPopOver1Position(null);
    setPopOver2Position(null);
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${AUM_ENDPOINT}/exchange_rate/v1/latest/keyword`);

      setExchangeRateData(response?.data?.data?.[0] || {});
    } catch {
      setExchangeRateData({});
    }

    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="container mx-auto flex h-full flex-col p-4 md:p-8">
      <div
        style={{
          boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.1)',
          background: 'linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%)',
        }}
        className="flex flex-grow flex-col justify-center gap-4 rounded-2xl p-6 text-black transition-all duration-300 dark:bg-gradient-to-br dark:from-gray-700 dark:to-gray-800 dark:text-white"
      >
        <div className="space-y-3 text-center">
          <h1 className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-4xl font-bold text-transparent">
            Exchange Rate
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Rate is taken from{' '}
            <a
              href="https://www.bnm.gov.my/exchange-rates"
              className="text-blue-500 underline transition-colors hover:text-blue-600"
              target="_blank"
              rel="noopener noreferrer"
            >
              Bank Negara Malaysia Official Website
            </a>
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Last Updated: {moment(exchageRateData?.updated_at)?.format('YYYY-MM-DD, hh:mm A')}
          </p>
        </div>

        <div className="mx-auto mt-8 flex w-full max-w-2xl flex-col items-center gap-6 md:flex-row">
          <div className="flex w-full items-center gap-2 md:w-1/2">
            <div className="flex-grow">
              <TextInput
                type="number"
                name="fromValue"
                placeholder="From"
                value={formData?.fromValue}
                onChange={(event) => {
                  const { value } = event.target;
                  setFormData((prev) => ({
                    ...prev,
                    fromValue: value,
                  }));
                }}
                className="w-full rounded-lg border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 dark:border-gray-600 dark:focus:border-blue-400 dark:focus:ring-blue-900"
              />
            </div>
            <button
              type="button"
              className="flex items-center gap-1 rounded-lg border-2 border-gray-200 bg-white px-3 py-2 text-sm transition-all hover:border-blue-500 hover:bg-blue-50 dark:border-gray-600 dark:bg-gray-700 dark:hover:border-blue-400 dark:hover:bg-blue-900/20"
              onClick={(event) =>
                setPopOver1Position(!popOver1Position ? event.currentTarget : null)
              }
            >
              <KeyboardArrowDown
                className="transition-transform duration-200"
                style={{ transform: popOver1Position ? 'rotate(180deg)' : 'rotate(0deg)' }}
              />
              {formData?.fromCurrency}
            </button>
            <Popover
              open={popOver1Position !== null}
              anchorEl={popOver1Position}
              onClose={() => setPopOver1Position(null)}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
              transformOrigin={{ vertical: 'top', horizontal: 'center' }}
            >
              <div className="flex flex-col gap-1 rounded-lg bg-white p-2 shadow-lg dark:bg-gray-700">
                {currencyList?.map((o, i) => (
                  <button
                    key={i}
                    type="button"
                    className={twMerge(
                      'rounded-md px-3 py-2 text-sm transition-all hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-900/30 dark:hover:text-blue-400',
                      formData?.fromCurrency === o &&
                        'bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400'
                    )}
                    onClick={() => {
                      setFormData((prev) => ({ ...prev, fromCurrency: o }));
                      closeAllPopover();
                    }}
                  >
                    {o}
                  </button>
                ))}
              </div>
            </Popover>
          </div>

          <button
            type="button"
            className="rounded-full bg-gradient-to-r from-blue-500 to-purple-500 p-3 text-white shadow-lg transition-all hover:from-blue-600 hover:to-purple-600 hover:shadow-xl dark:from-blue-600 dark:to-purple-600 dark:hover:from-blue-700 dark:hover:to-purple-700"
            onClick={() =>
              setFormData((prev) => ({
                ...prev,
                fromCurrency: prev?.toCurrency,
                toCurrency: prev?.fromCurrency,
              }))
            }
          >
            <SwapHoriz className="text-2xl md:text-3xl" />
          </button>

          <div className="flex w-full items-center gap-2 md:w-1/2">
            <div className="flex-grow">
              <TextInput
                name="toValue"
                placeholder="To"
                value={(() => {
                  const value = formData?.fromValue;
                  let fromValueConvertedToMyr = value;
                  if (formData?.fromCurrency !== 'MYR')
                    fromValueConvertedToMyr = value * (rateData?.[formData?.fromCurrency] || 1);
                  return fromValueConvertedToMyr / (rateData?.[formData?.toCurrency] || 1);
                })()}
                disabled
                className="w-full rounded-lg border-2 border-gray-200 bg-gray-50 dark:border-gray-600 dark:bg-gray-700"
              />
            </div>
            <button
              type="button"
              className="flex items-center gap-1 rounded-lg border-2 border-gray-200 bg-white px-3 py-2 text-sm transition-all hover:border-blue-500 hover:bg-blue-50 dark:border-gray-600 dark:bg-gray-700 dark:hover:border-blue-400 dark:hover:bg-blue-900/20"
              onClick={(event) =>
                setPopOver2Position(!popOver2Position ? event.currentTarget : null)
              }
            >
              <KeyboardArrowDown
                className="transition-transform duration-200"
                style={{ transform: popOver2Position ? 'rotate(180deg)' : 'rotate(0deg)' }}
              />
              {formData?.toCurrency}
            </button>
            <Popover
              open={popOver2Position !== null}
              anchorEl={popOver2Position}
              onClose={() => setPopOver2Position(null)}
              anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
              transformOrigin={{ vertical: 'top', horizontal: 'center' }}
            >
              <div className="flex flex-col gap-1 rounded-lg bg-white p-2 shadow-lg dark:bg-gray-700">
                {currencyList?.map((o, i) => (
                  <button
                    key={i}
                    type="button"
                    className={twMerge(
                      'rounded-md px-3 py-2 text-sm transition-all hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-900/30 dark:hover:text-blue-400',
                      formData?.toCurrency === o &&
                        'bg-blue-50 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400'
                    )}
                    onClick={() => {
                      setFormData((prev) => ({ ...prev, toCurrency: o }));
                      closeAllPopover();
                    }}
                  >
                    {o}
                  </button>
                ))}
              </div>
            </Popover>
          </div>
        </div>
      </div>
    </div>
  );
}
