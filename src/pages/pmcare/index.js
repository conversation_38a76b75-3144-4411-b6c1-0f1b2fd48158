// Next, React, Tw
import { useEffect, useState } from 'react';
import { twMerge } from 'tailwind-merge';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import Link from 'next/link';

// Mui
import { Tooltip } from '@mui/material';
import { Done, Close } from '@mui/icons-material';

// Packages
import * as R from 'ramda';

// Components
import Layout from '../../layouts/module/pmcare';
import { TablePaginationCustom } from '../../components/Shared/table';
import { SelectInput, SearchInput } from '../../components/Shared/CustomInput';

// Others
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useParamContext } from '../../utils/auth/ParamProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const dispatch = useDispatch();
  const { query, replace, asPath } = useRouter();
  const { q, state, city } = query;
  const { page, rowsPerPage } = useSelector((s) => s.simi);

  const { setParam } = useParamContext();

  // Table
  const [tableData, setTableData] = useState([]);

  const getBodyCellStyle = () => 'text-center py-1 text-xs';

  const getTableData = () => {
    let temp = tableData;
    if (state !== 'all') temp = temp?.filter((o) => o?.state === state);
    if (city !== 'all') temp = temp?.filter((o) => o?.city === city);
    if ([undefined, '']?.includes(q)) return temp;
    return temp.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  };

  // Others

  const getUniqueStatesList = () => R.uniq(R.pluck('state', tableData));

  const getUniqueCityList = () => {
    if (state === 'all') return R.uniq(R.pluck('city', tableData));
    return R.uniq(
      R.pluck(
        'city',
        tableData?.filter((o) => o?.state === state)
      )
    );
  };

  const getIcon = (boolean) => {
    if (boolean) return <Done className="text-green-500" />;
    return <Close className="text-red-500" />;
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await fetch(`https://apps.pmcare.my/api/ThirdParty/PmcareTMPanelProviders`, {
        headers: {
          Apikey: 'b89cc281-3c78-427b-8397-64c0d65dc930',
        },
      });
      const data = await response.json();
      if (data?.[0]?.providers) {
        setTableData(data?.[0]?.providers);
      }
    } catch (error) {
      // console.error('Error fetching data:', error);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (!state && !city) replace(`${asPath}?state=all&city=all`);
    fetchData();
  }, []);

  return (
    <div className="container mx-auto p-1 md:p-4">
      <div
        style={{ boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)' }}
        className="flex flex-col gap-4 rounded-xl bg-white p-4 text-black dark:bg-gray-600 dark:text-white"
      >
        <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
          <div className="flex flex-col gap-2">
            <SelectInput
              label="State"
              value={state}
              placeholder="State"
              onChange={(event) => setParam({ state: event.target.value, city: 'all' })}
              options={['all', ...getUniqueStatesList()]}
            />
            <SelectInput
              label="City"
              value={city}
              placeholder="City"
              onChange={(event) => setParam({ city: event.target.value })}
              options={['all', ...getUniqueCityList()]}
            />
          </div>
          <SearchInput />
        </div>
        <div className="overflow-x-auto scrollbar">
          <table className="min-w-full bg-white text-black">
            <thead>
              <tr>
                {[
                  'Panel Code',
                  'Name',
                  'Address',
                  'Contact No.',
                  '24 Hours',
                  'Public Holidays',
                  '',
                ].map((label, i) => (
                  <td key={i} className="bg-pmcare whitespace-nowrap px-4 text-center text-white">
                    {label}
                  </td>
                ))}
              </tr>
            </thead>
            <tbody>
              {getTableData()
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((row, i) => (
                  <tr key={i} className={`${`${i % 2 === 0 ? 'bg-[#f8f8f8]' : 'bg-white'}`}`}>
                    <td className={getBodyCellStyle()}>{row?.providerCode}</td>
                    <td className={twMerge(getBodyCellStyle(), 'max-w-[200px] text-left')}>
                      <Tooltip title={row?.providerName}> {row?.providerName}</Tooltip>
                    </td>
                    <td
                      className={twMerge(
                        getBodyCellStyle(),
                        'max-w-[200px] overflow-hidden whitespace-nowrap md:max-w-[400px]'
                      )}
                    >
                      <Tooltip title={row?.address} clas>
                        {row?.address}
                      </Tooltip>
                    </td>
                    <td className={getBodyCellStyle()}>
                      <Link href={`tel:+6${row?.phoneNo}`} target="_blank">
                        {row?.phoneNo}
                      </Link>
                    </td>
                    <td className={getBodyCellStyle()}>{getIcon(row?.open24Hours)}</td>
                    <td className={getBodyCellStyle()}>{getIcon(row?.openPublicHolidays)}</td>
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
        <TablePaginationCustom count={getTableData().length} />
      </div>
    </div>
  );
}
