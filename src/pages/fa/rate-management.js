// Next, React, Tw
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';

// Packages
import * as yup from 'yup';

// Components
import Layout from '../../layouts/module/fa';
import { TextInput } from '../../components/Shared/CustomInput';

// Others
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { FA_ENDPOINT } from '../../utils/fa';
import { useSnackbar } from '../../components/Shared/snackbar';
import { checkAndReplaceStringWithHyphen } from '../../utils/shared';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();

  const labelKeyTable = [
    { label: 'Cost of Capital', key: 'cost_of_capital' },
    { label: 'Re-Investment', key: 'reinvestment' },
    { label: 'USP', key: 'usp' },
    { label: 'Malaysia Tax', key: 'malaysia_tax' },
    { label: 'US Tax', key: 'united_state_tax' },
    { label: 'HK Tax', key: 'hong_kong_tax' },
    { label: 'SG Tax', key: 'singapore_tax' },
    { label: 'Annual O&M', key: 'annual_om' },
    { label: 'O&M Annual Increment', key: 'om_annual_increment' },
    { label: 'Stamping Fees', key: 'stamping_fees' },
    { label: 'First Year O&M Flat Rate', key: 'first_year_om' },
  ];

  // Table
  const [tableData, setTableData] = useState({});
  const bodyCellStyle = 'text-center py-1 text-sm';

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);

  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) setDialogData(data);
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };

  const schema = yup.object({
    annual_om: yup.number().required('Please provide annual OM.'),
    cost_of_capital: yup.number().required('Please provide cost of capital.'),
    first_year_om: yup.number().required('Please provide first year OM.'),
    malaysia_tax: yup.number().required('Please provide MY tax.'),
    united_state_tax: yup.number().required('Please provide US tax.'),
    hong_kong_tax: yup.number().required('Please provide HK tax.'),
    singapore_tax: yup.number().required('Please provide SG tax.'),
    om_annual_increment: yup.number().required('Please provide OM annual increment.'),
    reinvestment: yup.number().required('Please provide reinvestment.'),
    stamping_fees: yup.number().required('Please provide stamping fees.'),
    usp: yup.number().required('Please provide USP.'),
  });

  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }

      try {
        let response;
        switch (action) {
          case 'put':
            response = await axios.put(`${FA_ENDPOINT}/rate_percentage/${payload?.id}`, payload);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    setDialogData({});
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${FA_ENDPOINT}/rate_percentage/all/keyword`);
      setTableData(response?.data?.data?.[0] || {});
    } catch {
      setTableData({});
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div className="container mx-auto px-4 py-4">
        <div className="overflow-hidden rounded-lg bg-white shadow-sm dark:bg-gray-800">
          {/* Header with title and action button */}
          <div className="flex flex-col items-center justify-between gap-3 border-b border-gray-200 bg-white px-4 py-3 sm:flex-row">
            <div className="flex items-center">
              <h2 className="text-lg font-medium text-gray-800">Rate Management</h2>
            </div>

            <div className="flex items-center gap-0">
              <p className="mr-4 text-sm text-gray-600">
                Updated at: <strong>{tableData?.updated_at}</strong>
              </p>
              <button
                type="button"
                className="bg-primary hover:bg-primary/90 whitespace-nowrap rounded-lg px-5 py-2 text-sm font-medium text-white shadow-sm transition-all duration-200"
                onClick={() => handleClickOpenDialog(true, tableData)}
              >
                Edit Rates
              </button>
            </div>
          </div>

          {/* Table section */}
          <div className="overflow-x-auto bg-white">
            <table className="min-w-full table-fixed border-collapse">
              <thead className="sticky top-0 z-10 bg-gray-50">
                <tr className="border-b border-gray-200">
                  {['Name', 'Rate (%)'].map((label, i) => (
                    <th
                      key={i}
                      scope="col"
                      className="border-b-2 border-gray-300 px-3 py-3 text-left text-sm font-semibold uppercase text-gray-800"
                    >
                      {label}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {labelKeyTable.map((row, i) => (
                  <tr
                    key={i}
                    className="border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-100"
                  >
                    <td className="px-3 py-3 text-sm text-gray-700">{row?.label}</td>
                    <td className="px-3 py-3 text-sm text-gray-700">
                      {checkAndReplaceStringWithHyphen(tableData?.[row?.key])}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <Dialog
        open={dialogOpen}
        onClose={() => handleDialogClose(false)}
        PaperProps={{
          sx: {
            padding: 0,
            borderRadius: '1rem',
            backgroundColor: '#f5f5fc',
            boxShadow: '0 4px 24px rgba(0, 0, 0, 0.1)',
            maxWidth: '550px',
            width: '100%',
          },
        }}
      >
        <div className="overflow-hidden rounded-xl">
          <div className="bg-primary px-6 py-5 text-white">
            <h2 className="text-xl font-medium">Edit Rates</h2>
          </div>
          <div className="bg-white p-6">
            <div className="flex flex-col gap-4">
              {labelKeyTable?.map((o, i) => (
                <div key={i} className="space-y-2">
                  <label htmlFor={o.key} className="block text-sm font-medium text-gray-700">
                    {o?.label} <span className="text-red-500">*</span>
                  </label>
                  <TextInput
                    id={o.key}
                    type="number"
                    name={o?.key}
                    value={dialogData?.[o?.key]}
                    placeholder={o?.label}
                    onChange={handleDialogDataChange}
                    className="focus:ring-primary rounded-lg border border-gray-300 px-4 py-2 text-base shadow-sm focus:border-transparent focus:ring-2"
                  />
                </div>
              ))}
            </div>
          </div>
          <div className="flex items-center justify-end gap-4 border-t border-gray-200 bg-gray-50 px-6 py-5">
            <button
              type="button"
              onClick={() => handleDialogClose(false)}
              className="rounded-lg border border-gray-300 bg-white px-6 py-3 font-medium text-gray-700 transition-colors hover:bg-gray-50"
            >
              Cancel
            </button>
            {editModeDialog && (
              <button
                type="button"
                onClick={() => handleDialogClose('put')}
                className="bg-primary hover:bg-primary/90 rounded-lg px-6 py-3 font-medium text-white shadow-sm transition-colors"
              >
                Save Changes
              </button>
            )}
          </div>
        </div>
      </Dialog>
    </>
  );
}
