// Components
import Layout from '../../layouts/module/fa';
import AttachmentBox from '../../components/Shared/AttachmentBox';

// Others
import { FA_ENDPOINT } from '../../utils/fa';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const { isAdmin } = useModuleRoleContext();

  // Others

  return (
    <div className="container mx-auto px-4 py-4">
      <div className="overflow-hidden rounded-lg bg-white shadow-sm dark:bg-gray-800">
        {/* Header with title */}
        <div className="flex flex-col items-center justify-between gap-3 border-b border-gray-200 bg-white px-4 py-3 sm:flex-row">
          <div className="flex items-center">
            <h2 className="text-lg font-medium text-gray-800">Guidelines</h2>
          </div>
        </div>

        {/* Content section */}
        <div className="p-4">
          <AttachmentBox
            GET_ALL_FILES_ENDPOINT={`${FA_ENDPOINT}/file/guideline/64db23af2b57e5b8a12f8c1d`}
            UPLOAD_CERTAIN_FILE_ENDPOINT={`${FA_ENDPOINT}/file/upload/guideline/64db23af2b57e5b8a12f8c1d`}
            DOWNLOAD_CERTAIN_FILE_ENDPOINT={`${FA_ENDPOINT}/file/download`}
            DELETE_CERTAIN_FILE_ENDPOINT={`${FA_ENDPOINT}/file`}
            DISABLE_UPLOAD={!isAdmin}
          />
        </div>
      </div>
    </div>
  );
}
