// Next, React, Tailwind
import { useEffect, useState } from 'react';
import { twMerge } from 'tailwind-merge';
import { useDispatch } from 'react-redux';

// Auth
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

// Components
import Layout from '../../layouts/module/fa';
import AllProjects from '../../components/fa/AllProjects';
import ReactAnimatedNumber from '../../components/Shared/ReactAnimatedNumber';
import { StatsCard } from '../../components/fa/ui/EnhancedCard';
import { EnhancedButton } from '../../components/fa/ui/EnhancedButton';

// Others
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { FA_ENDPOINT } from '../../utils/fa';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const dispatch = useDispatch();
  const [summaryData, setSummaryData] = useState({});
  const { user } = useAuthContext();
  const { isAdmin } = useModuleRoleContext();

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      // Always fetch all projects
      const response = await axios.get(`${FA_ENDPOINT}/project/all/keyword`);

      if (response?.data?.data) {
        let projectsData = response.data.data;

        // If not admin, filter projects
        if (!isAdmin) {
          // First, filter projects created by the user
          const userCreatedProjects = projectsData.filter(
            (project) => project.created_by_staff_id === user?.staff_id
          );

          // Then, check other projects for user in approvals
          const otherProjects = projectsData.filter(
            (project) => project.created_by_staff_id !== user?.staff_id
          );

          // Check each project's approvals for user's name
          const approvalChecks = await Promise.all(
            otherProjects.map(async (project) => {
              try {
                const approvalResponse = await axios.get(
                  `${FA_ENDPOINT}/approval/project_id/${project.id}`
                );
                const approvals = approvalResponse?.data?.data || [];

                const userInApprovals = approvals.some(
                  (approval) =>
                    approval.status.toLowerCase() === 'pending' &&
                    ['approver', 'endorser', 'reviewer'].includes(approval.type.toLowerCase()) &&
                    approval.user_name_array.includes(user?.name)
                );

                return {
                  project,
                  userInApprovals,
                };
              } catch {
                return { project, userInApprovals: false };
              }
            })
          );

          // Filter projects where user is in approvals
          const projectsWhereUserInApprovals = approvalChecks
            .filter((result) => result.userInApprovals)
            .map((result) => result.project);

          // Combine both sets of projects
          projectsData = [...userCreatedProjects, ...projectsWhereUserInApprovals];
        }

        setSummaryData({
          endorsement: projectsData.filter((o) => o?.status?.toLowerCase()?.includes('endorsement'))
            .length,
          approval: projectsData.filter((o) =>
            o?.status?.toLowerCase()?.includes('pending approver')
          ).length,
          sitting: projectsData.filter((o) => o?.status?.toLowerCase()?.includes('sitting')).length,
          completed: projectsData.filter((o) => o?.status?.toLowerCase()?.includes('completed'))
            .length,
        });
      }
    } catch {
      setSummaryData({});
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [isAdmin, user?.staff_id]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-6">
        {/* Enhanced Header */}
        <div className="mb-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Financial Analysis Dashboard
            </h1>
            <p className="mt-2 text-gray-600 dark:text-gray-300">
              Monitor and manage your financial analysis projects
            </p>
          </div>
        </div>

        {/* Enhanced Stats Cards */}
        <div className="mb-8 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {[
            {
              label: 'Pending Approval',
              key: 'approval',
              variant: 'warning',
              icon: (
                <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              ),
            },
            {
              label: 'Pending Endorsement',
              key: 'endorsement',
              variant: 'primary',
              icon: (
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              ),
            },
            {
              label: 'Pending Sitting',
              key: 'sitting',
              variant: 'default',
              icon: (
                <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              ),
            },
            {
              label: 'Completed',
              key: 'completed',
              variant: 'success',
              icon: (
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              ),
            },
          ]?.map((o, i) => (
            <StatsCard
              key={i}
              title={o.label}
              value={
                <ReactAnimatedNumber
                  value={summaryData[o?.key] || 0}
                  formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                />
              }
              icon={o.icon}
              variant={o.variant}
            />
          ))}
        </div>
      </div>
      <AllProjects />
    </div>
  );
}
