// Next, React, Tw
import { useRouter } from 'next/router';
import { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Components
import Layout from '../../../layouts/module/fa';
import ProjectDetails from '../../../components/fa/ProjectDetails';
import AllScenario from '../../../components/fa/AllScenario';
import Summary from '../../../components/fa/Summary';
import RemarkBox from '../../../components/Shared/RemarkBox';
import HistoryBox from '../../../components/Shared/HistoryBox';
import ScenarioDetails from '../../../components/fa/ScenarioDetails';
import SelectApprovalBox from '../../../components/Shared/Approval/SelectApprovalBox';
import ApprovalStepper from '../../../components/Shared/Approval/ApprovalStepper';
import EnhancedApprovalWorkflow from '../../../components/fa/ui/EnhancedApprovalWorkflow';
import ParallaxHeader from '../../../components/fa/ui/ParallaxHeader';
import FaApproveRejectButtons from '../../../components/fa/FaApproveRejectButtons';
import ActionButtonGroup from '../../../components/fa/ActionButtonGroup';
import { EnhancedButton } from '../../../components/fa/ui/EnhancedButton';
import CollapsibleSection from '../../../components/Shared/CollapsibleSection';
import { Card } from '../../../components/ui/card';
import {
  DocumentIcon,
  CheckCircleIcon,
  CollectionIcon,
  ChartBarIcon,
  ClockIcon,
} from '../../../components/Shared/SectionIcons';

// Others
import { FA_ENDPOINT, useFaContext } from '../../../utils/fa';
import { useModuleRoleContext } from '../../../utils/auth/ModuleRoleProvider';
import { useAuthContext } from '../../../utils/auth/useAuthContext';
import { useSnackbar } from '../../../components/Shared/snackbar';
import axios from '../../../utils/axios';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { fetchAllScenarios } from '../../../utils/store/faReducer';
import { setBreadCrumbsList } from '../../../utils/store/simiReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const dispatch = useDispatch();
  const { query, asPath } = useRouter();
  const { projectId, scenarioId } = query;
  const { handleCreateHistory } = useFaContext();
  const { isAdmin } = useModuleRoleContext();
  const { user } = useAuthContext();
  const { enqueueSnackbar } = useSnackbar();
  const hiddenDivRef = useRef(null);

  const { projectData, allScenarios } = useSelector((state) => state.fa);

  // Helper function to check if project is in draft status (case-insensitive and trimmed)
  const isDraftStatus = useMemo(() => {
    const originalStatus = projectData?.status;
    const normalizedStatus = originalStatus?.toString().toLowerCase().trim();
    
    // Treat empty string as draft status (common in new/draft projects)
    const result = normalizedStatus === 'draft' || normalizedStatus === '';
    
    console.log('Status Check Debug:', {
      originalStatus,
      normalizedStatus,
      expectedStatus: 'draft or empty',
      result,
      comparison: `"${normalizedStatus}" === "draft" || "${normalizedStatus}" === "" = ${result}`
    });
    
    return result;
  }, [projectData?.status]);

  // Helper function to check if user can edit
  const canEditProject = useMemo(() => {
    const result = isDraftStatus && (isAdmin || projectData?.created_by_staff_id === user?.staff_id);
    
    // Debug logging
    console.log('Project Edit Access Check:', {
      projectId,
      status: projectData?.status,
      isDraftStatus,
      isAdmin,
      created_by_staff_id: projectData?.created_by_staff_id,
      current_user_staff_id: user?.staff_id,
      canEditProject: result
    });
    
    return result;
  }, [isDraftStatus, isAdmin, projectData?.created_by_staff_id, user?.staff_id, projectId, projectData?.status]);

  // State for approval save button
  const [approvalSaveState, setApprovalSaveState] = useState({
    hasChanges: false,
    isSaving: false,
    disabled: false,
    onSave: null
  });

  // State for project information save button
  const [projectInfoSaveState, setProjectInfoSaveState] = useState({
    hasChanges: false,
    isSaving: false,
    disabled: false,
    onSave: null
  });


  // State for scenarios save button (for new scenario creation)
  const [scenarioSaveState, setScenarioSaveState] = useState({
    hasChanges: false,
    isSaving: false,
    disabled: false,
    onSave: null
  });

  // Accordion state management - only one section can be open at a time
  const [openSection, setOpenSection] = useState(() => {
    // Try to restore from localStorage on initial load
    if (typeof window !== 'undefined') {
      const savedSection = localStorage.getItem(`fa-project-accordion-${projectId}`);
      return savedSection || null; // null means all sections are closed
    }
    // Default to no section open (all collapsed)
    return null;
  });

  // Parallax scroll state
  const [scrollY, setScrollY] = useState(0);
  const [isScrolled, setIsScrolled] = useState(false);
  const subheaderRef = useRef(null);
  const scrollContainerRef = useRef(null);

  // Handle accordion toggle
  const handleAccordionToggle = (sectionId) => {
    const newOpenSection = openSection === sectionId ? null : sectionId;
    setOpenSection(newOpenSection);
    
    // Save to localStorage
    if (typeof window !== 'undefined') {
      if (newOpenSection) {
        localStorage.setItem(`fa-project-accordion-${projectId}`, newOpenSection);
      } else {
        localStorage.removeItem(`fa-project-accordion-${projectId}`);
      }
    }
  };

  // Parallax scroll handler with smooth interpolation
  const handleScroll = useCallback(() => {
    const scrollContainer = document.getElementById('scroll-container');
    if (!scrollContainer) return;

    const scrollTop = scrollContainer.scrollTop;
    const threshold = 40; // Threshold for when to start the parallax effect - ultra responsive
    const fadeRange = 20; // Range over which the fade occurs for smoother transition

    setScrollY(scrollTop);

    // Smooth transition zone instead of hard cutoff
    if (scrollTop > threshold + fadeRange) {
      setIsScrolled(true);
    } else if (scrollTop < threshold - fadeRange) {
      setIsScrolled(false);
    }
    // Keep current state in the transition zone for smoother effect
  }, []);

  // Setup scroll listener
  useEffect(() => {
    const scrollContainer = document.getElementById('scroll-container');
    if (!scrollContainer) return;

    // Throttle scroll events for performance
    let ticking = false;
    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    scrollContainer.addEventListener('scroll', throttledHandleScroll, { passive: true });

    return () => {
      scrollContainer.removeEventListener('scroll', throttledHandleScroll);
    };
  }, [handleScroll]);

  // Approval save button component
  const ApprovalSaveButton = () => {
    if (!approvalSaveState.hasChanges || !isDraftStatus) return null;

    return (
      <button
        type="button"
        onClick={approvalSaveState.onSave}
        disabled={approvalSaveState.isSaving || !approvalSaveState.hasChanges || approvalSaveState.disabled}
        className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
      >
        {approvalSaveState.isSaving ? 'Saving...' : 'Save Changes'}
      </button>
    );
  };

  // Project information save button component
  const ProjectInfoSaveButton = () => {
    if (!projectInfoSaveState.hasChanges || !canEditProject) return null;

    return (
      <button
        type="button"
        onClick={projectInfoSaveState.onSave}
        disabled={projectInfoSaveState.isSaving || !projectInfoSaveState.hasChanges || projectInfoSaveState.disabled}
        className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
      >
        {projectInfoSaveState.isSaving ? 'Saving...' : 'Save Changes'}
      </button>
    );
  };

  // New state to track if all scenarios have capex, opex, and revenue
  const [allScenarioDetailsFilled, setAllScenarioDetailsFilled] = useState(false);
  const [checkingScenarioDetails, setCheckingScenarioDetails] = useState(false);
  


  // Handle browser back button to ensure it redirects to project page
  useEffect(() => {
    const handlePopState = () => {
      if (scenarioId) {
        window.location.href = `/fa/project/${projectId}`;
      }
    };

    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [projectId, scenarioId]);

  // Clear old section states on page load (cleanup legacy localStorage keys)
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Clear all previous individual section states from localStorage since we're using accordion now
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('section-')) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key));

      // Clear session storage
      sessionStorage.removeItem('lastActiveSection');
    }
  }, [asPath, projectId]); // Re-run when project changes or path changes

  useEffect(() => {
    const checkScenarioDetails = async () => {
      if (!allScenarios || allScenarios.length === 0) {
        setAllScenarioDetailsFilled(false);
        return;
      }
      setCheckingScenarioDetails(true);
      try {
        // For each scenario, check capex, opex, and revenue
        const results = await Promise.all(
          allScenarios.map(async (scenario) => {
            const [capexRes, opexRes, revenueRes] = await Promise.all([
              axios.get(`/fa/capex/scenario_id/${scenario.id}`),
              axios.get(`/fa/opex/scenario_id/${scenario.id}`),
              axios.get(`/fa/revenue/scenario_id/${scenario.id}`),
            ]);
            return (
              Array.isArray(capexRes?.data?.data) && capexRes.data.data.length > 0 &&
              Array.isArray(opexRes?.data?.data) && opexRes.data.data.length > 0 &&
              Array.isArray(revenueRes?.data?.data) && revenueRes.data.data.length > 0
            );
          })
        );
        setAllScenarioDetailsFilled(results.every(Boolean));
      } catch (e) {
        setAllScenarioDetailsFilled(false);
      }
      setCheckingScenarioDetails(false);
    };
    checkScenarioDetails();
  }, [allScenarios]);

  // Others
  const handleRevertProject = async () => {
    dispatch(setIsLoading(true));
    try {
      await axios.put(`${FA_ENDPOINT}/project/${projectId}`, {
        ...projectData,
        version_number: 2,
        status: 'draft',
      });
    } catch {
      /* empty */
    }
    dispatch(setIsLoading(false));
  };

  const handleSubmitApproveProject = async (status) => {
    dispatch(setIsLoading(true));
    try {
      await axios.put(`${FA_ENDPOINT}/project/${projectId}`, {
        ...projectData,
        status,
      });
    } catch {
      /* empty */
    }
    dispatch(setIsLoading(false));
  };





  useEffect(() => {
    if (!projectId) return;
    dispatch(fetchAllScenarios(projectId));
  }, [projectId]);

  // Set breadcrumb with project name
  useEffect(() => {
    if (projectId && projectData?.name) {
      dispatch(
        setBreadCrumbsList([
          {
            linkTo: '/fa',
            label: 'FA',
          },
          {
            linkTo: `/fa/project/${projectId}`,
            label: projectData?.name,
          },
        ])
      );
    }
  }, [projectId, projectData?.name]);

  // Memoize validation data to prevent unnecessary re-renders
  const validationData = useMemo(() => ({
    allScenarios,
    allScenarioDetailsFilled,
    checkingScenarioDetails
  }), [allScenarios, allScenarioDetailsFilled, checkingScenarioDetails]);

  return (
    <div className="relative min-h-full bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
      {/* Parallax Header Component */}
      <ParallaxHeader
        projectData={projectData}
        projectId={projectId}
        isScrolled={isScrolled}
      />

      {!scenarioId && (
        <>
          <div ref={hiddenDivRef} />

          {/* Enhanced Project Header with Parallax Effect */}
          <div className="sticky top-0 z-30 mb-8">
            {/* Main Subheader - Smooth fade out animation */}
            <div
              ref={subheaderRef}
              className="bg-white/95 backdrop-blur-md border-b border-slate-200/60 shadow-sm transition-opacity duration-700 ease-out"
              style={{
                opacity: isScrolled ? 0 : 1,
                pointerEvents: isScrolled ? 'none' : 'auto'
              }}
            >
              <div className="container mx-auto px-6 py-6">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                  {/* Project Title Section */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-8 bg-gradient-to-b from-blue-600 to-blue-700 rounded-full"></div>
                      <div>
                        <h1 className="text-2xl font-bold text-slate-900 leading-tight">
                          {projectData?.name || 'Financial Analysis Project'}
                        </h1>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-sm text-slate-500 font-medium">ID:</span>
                          <code className="text-xs bg-slate-100 text-slate-700 px-2 py-1 rounded font-mono">
                            {projectId}
                          </code>
                          <div className={`ml-2 px-3 py-1 rounded-full text-xs font-semibold ${
                            projectData?.status === 'draft'
                              ? 'bg-amber-100 text-amber-800'
                              : projectData?.status === 'completed'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-blue-100 text-blue-800'
                          }`}>
                            {projectData?.status?.toUpperCase() || 'DRAFT'}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center gap-3">
                    <FaApproveRejectButtons
                        NAME={projectData?.name}
                        GET_ALL_APPROVALS_ENDPOINT={`${FA_ENDPOINT}/approval/project_id/${projectId}`}
                        UPDATE_CERTAIN_APPROVAL_ENDPOINT={`${FA_ENDPOINT}/approval`}
                        REVERT_CALLBACK={async () => {
                          await handleRevertProject();
                          await handleCreateHistory(projectId, 'Project was reverted.');
                          if (window) window.location.reload();
                        }}
                        SUBMIT_CALLBACK={async (status) => {
                          await handleSubmitApproveProject(status);
                          await handleCreateHistory(projectId, 'Project was submitted for approval.');
                          if (window) window.location.reload();
                        }}
                        APPROVE_CALLBACK={async (status) => {
                          await handleSubmitApproveProject(status);
                          await handleCreateHistory(projectId, 'Project was approved.');
                          if (window) window.location.reload();
                        }}
                        documentName={`Project ${projectData?.name}`}
                        // Pass validation data for client-side checking
                        validationData={validationData}
                      />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="container mx-auto px-6 pb-12">
            <div className="space-y-8">
              {/* PRIMARY SECTIONS - Enhanced with better visual hierarchy */}

              {/* Approval Workflow Section - Simplified and Clean */}
              <CollapsibleSection
                id="approval-section"
                title="Approval Workflow"
                icon={<CheckCircleIcon />}
                accordionMode={true}
                isOpen={openSection === 'approval-section'}
                onToggle={handleAccordionToggle}
                rightComponent={<ApprovalSaveButton />}
                className="bg-white rounded-2xl border border-slate-200/60 shadow-sm hover:shadow-md transition-all duration-200"
              >
                <div className="space-y-8 p-8">
                  {/* Enhanced Approval Workflow */}
                  <div className="bg-gradient-to-r from-slate-50 to-blue-50/30 rounded-xl p-6 border border-slate-100">
                    <EnhancedApprovalWorkflow
                      GET_ALL_APPROVALS_ENDPOINT={`${FA_ENDPOINT}/approval/project_id/${projectId}`}
                    />
                  </div>

                  

                  {/* Approver Selection - Always Show for Draft Projects */}
                    <div className="bg-white rounded-xl border border-slate-200 shadow-sm p-6">
                      <div className="flex items-center justify-between mb-6">
                        <div>
                          <h3 className="text-lg font-semibold text-slate-900">Configure Approvers</h3>
                          <p className="text-sm text-slate-600 mt-1">
                            Assign approvers and select reviewers for the approval workflow
                          </p>
                        </div>
                        
                        {/* External Save Button */}
                        {approvalSaveState.hasChanges && (
                          <button
                            type="button"
                            onClick={approvalSaveState.onSave}
                            disabled={approvalSaveState.isSaving || !approvalSaveState.hasChanges || approvalSaveState.disabled}
                            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed whitespace-nowrap rounded-lg px-4 py-2 text-sm font-medium text-white shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                          >
                            {approvalSaveState.isSaving ? 'Saving...' : 'Save Changes'}
                          </button>
                        )}
                      </div>
                      
                      <div className="enhanced-approval-selection">
                        <style>{`
                          /* Enhanced styling for cleaner approver selection */
                          .enhanced-approval-selection :global(.flex.w-full.flex-col) {
                            display: grid !important;
                            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
                            gap: 24px !important;
                            padding: 0 !important;
                          }
                          
                          .enhanced-approval-selection :global(.flex.flex-col.gap-1) {
                            padding: 20px !important;
                            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
                            border: 1px solid #e2e8f0 !important;
                            border-radius: 12px !important;
                            transition: all 0.2s ease !important;
                            min-height: auto !important;
                          }
                          
                          .enhanced-approval-selection :global(.flex.flex-col.gap-1::before) {
                            content: '' !important;
                            position: absolute !important;
                            top: 0 !important;
                            left: 0 !important;
                            right: 0 !important;
                            height: 3px !important;
                            background: linear-gradient(90deg, #3b82f6, #1d4ed8) !important;
                            border-radius: 12px 12px 0 0 !important;
                          }
                          
                          .enhanced-approval-selection :global(.flex.flex-col.gap-1:hover) {
                            border-color: #cbd5e1 !important;
                            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
                          }
                          
                          .enhanced-approval-selection :global(.text-xs.font-medium.text-gray-700) {
                            margin-bottom: 8px !important;
                            font-size: 0.875rem !important;
                            font-weight: 600 !important;
                            color: #475569 !important;
                            text-transform: none !important;
                            letter-spacing: normal !important;
                            padding-left: 0 !important;
                          }
                          
                          .enhanced-approval-selection :global(.text-xs.font-medium.text-gray-700::before) {
                            display: none !important;
                          }
                          
                          .enhanced-approval-selection :global(.MuiOutlinedInput-root) {
                            background: white !important;
                            border-radius: 8px !important;
                            border: 1px solid #d1d5db !important;
                            transition: all 0.2s ease !important;
                          }
                          
                          .enhanced-approval-selection :global(.MuiOutlinedInput-root:hover) {
                            border-color: #9ca3af !important;
                          }
                          
                          .enhanced-approval-selection :global(.MuiOutlinedInput-root.Mui-focused) {
                            border-color: #3b82f6 !important;
                            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
                          }
                          
                          .enhanced-approval-selection :global(.MuiOutlinedInput-notchedOutline) {
                            border: none !important;
                          }
                        `}</style>
                        
                        <SelectApprovalBox
                          key={`approval-${projectId}`}
                          documentIdKey="project_id"
                          documentIdValue={projectId}
                          GET_ALL_APPROVALS_ENDPOINT={`${FA_ENDPOINT}/approval/project_id/${projectId}`}
                          POST_NEW_APPROVAL_ENDPOINT={`${FA_ENDPOINT}/approval`}
                          UPDATE_CERTAIN_APPROVAL_ENDPOINT={`${FA_ENDPOINT}/approval`}
                          onSaveStateChange={setApprovalSaveState}
                          hideInternalSaveButton={true}
                        />
                      </div>
                    </div>
                </div>
              </CollapsibleSection>

              {/* Project Information Section - Enhanced Form Layout */}
              <CollapsibleSection
                id="project-details"
                title="Project Information"
                icon={<DocumentIcon />}
                accordionMode={true}
                isOpen={openSection === 'project-details'}
                onToggle={handleAccordionToggle}
                className="bg-white rounded-2xl border border-slate-200/60 shadow-sm hover:shadow-md transition-all duration-200"
                rightComponent={
                  // Show access info in header if user can't edit
                  !canEditProject ? (
                    <div className="text-sm text-gray-500 px-3 py-1 bg-gray-100 rounded-full">
                      {!isDraftStatus ? 'Read Only' : 'No Edit Access'}
                    </div>
                  ) : null
                }
              >
                <div className="p-8">
                  {/* Access Status Message */}
                  {canEditProject && (
                    <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                          <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </div>
                        <div>
                          <p className="text-sm font-semibold text-green-900">Edit Mode Enabled</p>
                          <p className="text-xs text-green-700">You can edit and save changes to the project information below</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* No Access Message */}
                  {!canEditProject && (
                    <div className="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-amber-100 rounded-lg flex items-center justify-center">
                          <svg className="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                          </svg>
                        </div>
                        <div>
                          <p className="text-sm font-semibold text-amber-900">
                            {!isDraftStatus ? 'Project Locked' : 'No Edit Permission'}
                          </p>
                          <p className="text-xs text-amber-700">
                            {!isDraftStatus 
                              ? `Project information is read-only because the project status is "${projectData?.status}" (not draft)`
                              : 'You can only view project information. Only the project creator or admins can make changes'
                            }
                          </p>
                          {/* Debug info - remove after fixing */}
                          <div className="text-xs text-gray-500 mt-2 font-mono bg-gray-100 p-2 rounded">
                            <div>Debug Info:</div>
                            <div>• original status: "{projectData?.status}" (type: {typeof projectData?.status})</div>
                            <div>• normalized status: "{projectData?.status?.toString().toLowerCase().trim()}"</div>
                            <div>• isDraftStatus: {isDraftStatus ? 'true' : 'false'}</div>
                            <div>• isAdmin: {isAdmin ? 'true' : 'false'}</div>
                            <div>• created_by: {projectData?.created_by_staff_id}</div>
                            <div>• current_user: {user?.staff_id}</div>
                            <div>• canEditProject: {canEditProject ? 'true' : 'false'}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Save Changes Button - Inside Content like Approval */}
                  {canEditProject && projectInfoSaveState.hasChanges && (
                    <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                          <div>
                            <p className="text-sm font-semibold text-blue-900">Unsaved Changes</p>
                            <p className="text-xs text-blue-700">You have unsaved changes in the project information</p>
                          </div>
                        </div>
                        <ProjectInfoSaveButton />
                      </div>
                    </div>
                  )}
                  
                  <ProjectDetails onProjectInfoChange={setProjectInfoSaveState} />
                </div>
              </CollapsibleSection>

              {/* Scenarios Section - Cleaner Table Design */}
              <CollapsibleSection
                id="scenarios-section"
                title="Scenarios"
                icon={<CollectionIcon />}
                accordionMode={true}
                isOpen={openSection === 'scenarios-section'}
                onToggle={handleAccordionToggle}
                className="bg-white rounded-2xl border border-slate-200/60 shadow-sm hover:shadow-md transition-all duration-200"
              >
                <div className="p-8">
                  {/* New Scenario Button - Inside Content */}
                  {projectData?.status === 'draft' && (
                    <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                          </div>
                          <div>
                            <p className="text-sm font-semibold text-blue-900">Scenario Management</p>
                            <p className="text-xs text-blue-700">
                              {projectData?.category === 'single' && allScenarios?.length > 0 
                                ? 'Single category projects can only have one scenario'
                                : 'Create and manage financial scenarios for this project'
                              }
                            </p>
                          </div>
                        </div>
                        {(projectData?.category !== 'single' || allScenarios?.length === 0) && (
                          <button
                            type="button"
                            onClick={() => {
                              const scenarioComponent = document.querySelector('.scenarios-container .add-scenario-button');
                              if (scenarioComponent) {
                                scenarioComponent.click();
                              } else {
                                window.openScenarioDialog && window.openScenarioDialog();
                              }
                            }}
                            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                          >
                            New Scenario
                          </button>
                        )}
                      </div>
                    </div>
                  )}
                  
                  <div className="scenarios-container">
                    <AllScenario />
                  </div>
                </div>
              </CollapsibleSection>

              {/* Financial Summary Section - Full Width with Scenario Sync */}
              <CollapsibleSection
                id="summary-section"
                title="Financial Summary"
                icon={<ChartBarIcon />}
                accordionMode={true}
                isOpen={openSection === 'summary-section'}
                onToggle={handleAccordionToggle}
                className="bg-white rounded-2xl border border-slate-200/60 shadow-sm hover:shadow-md transition-all duration-200"
              >
                <div className="p-8">
                  {allScenarios && allScenarios.length > 0 ? (
                    <div className="space-y-6">
                      {/* Scenario Info Header */}
                      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                              <ChartBarIcon className="w-4 h-4 text-blue-600" />
                            </div>
                            <div>
                              <p className="text-sm font-semibold text-blue-900">Financial Analysis Summary</p>
                              <p className="text-xs text-blue-700">
                                Viewing analysis for {allScenarios.length} scenario{allScenarios.length !== 1 ? 's' : ''}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-4">
                            <div className="text-right">
                              <div className="text-xs text-slate-600 mb-1">Project Status</div>
                              <div className={`inline-flex items-center justify-center text-xs font-medium px-2 py-1 rounded-full min-w-[60px] ${
                                projectData?.status === 'draft' 
                                  ? 'bg-amber-100 text-amber-800 border border-amber-200' 
                                  : 'bg-green-100 text-green-800 border border-green-200'
                              }`}>
                                {projectData?.status?.toUpperCase() || 'DRAFT'}
                              </div>
                            </div>
                            <EnhancedButton
                              variant="primary"
                              onClick={() => window.open(`/fa/project/${projectId}/export`, '_blank')}
                              icon={
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                              }
                            >
                              Export Report
                            </EnhancedButton>
                          </div>
                        </div>
                      </div>

                      {/* Financial Summary Table - Full Width */}
                      <div className="w-full">
                        <Summary showExportButton={false} hideHeader={true} />
                      </div>
                    </div>
                  
                  ) : (
                    <div className="bg-amber-50 rounded-lg p-6 border border-amber-200">
                      <div className="flex items-start gap-4">
                        <div className="w-10 h-10 bg-amber-100 rounded-lg flex items-center justify-center flex-shrink-0">
                          <svg className="w-5 h-5 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5C2.962 18.333 3.924 20 5.464 20z" />
                          </svg>
                        </div>
                        <div className="flex-1">
                          <h4 className="text-base font-semibold text-amber-900 mb-2">No Scenarios Available</h4>
                          <p className="text-sm text-amber-800 mb-4">
                            Create scenarios first to view the financial summary and analysis. Navigate to the Scenarios section above to get started.
                          </p>
                          <button
                            type="button"
                            onClick={() => {
                              // First expand scenarios section
                              setOpenSection('scenarios-section');
                              // Then try to trigger scenario creation
                              setTimeout(() => {
                                const scenarioComponent = document.querySelector('.scenarios-container .add-scenario-button');
                                if (scenarioComponent) {
                                  scenarioComponent.click();
                                } else {
                                  window.openScenarioDialog && window.openScenarioDialog();
                                }
                              }, 300);
                            }}
                            className="text-sm font-medium text-amber-700 hover:text-amber-800 underline decoration-2 underline-offset-2"
                          >
                            Create First Scenario →
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CollapsibleSection>

              {/* History & Remarks Section - Cleaner Timeline */}
              <CollapsibleSection
                id="history-remarks"
                title="History & Remarks"
                icon={<ClockIcon />}
                accordionMode={true}
                isOpen={openSection === 'history-remarks'}
                onToggle={handleAccordionToggle}
                className="bg-white rounded-2xl border border-slate-200/60 shadow-sm hover:shadow-md transition-all duration-200 mb-8"
              >
                <div className="p-8">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                      <div className="flex items-center gap-3 mb-6">
                        <div className="w-8 h-8 bg-slate-100 rounded-lg flex items-center justify-center">
                          <ClockIcon className="w-4 h-4 text-slate-600" />
                        </div>
                        <h3 className="text-lg font-semibold text-slate-900">History</h3>
                      </div>
                      <HistoryBox
                        key={`history-${projectId}`}
                        parentHiddenDivRef={hiddenDivRef}
                        GET_ALL_HISTORIES_ENDPOINT={`${FA_ENDPOINT}/history/project_id/${projectId}`}
                      />
                    </div>
                    
                    <div>
                      <div className="flex items-center gap-3 mb-6">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                          <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                          </svg>
                        </div>
                        <h3 className="text-lg font-semibold text-slate-900">Remarks</h3>
                      </div>
                      <RemarkBox
                        key={`remarks-${projectId}`}
                        parentHiddenDivRef={hiddenDivRef}
                        documentIdKey="project_id"
                        documentIdValue={projectId}
                        GET_ALL_REMARKS_ENDPOINT={`${FA_ENDPOINT}/remark/project_id/${projectId}`}
                        POST_NEW_REMARK_ENDPOINT={`${FA_ENDPOINT}/remark`}
                      />
                    </div>
                  </div>
                </div>
              </CollapsibleSection>
            </div>
          </div>
        </>
      )}
      {scenarioId && <ScenarioDetails />}
    </div>
  );
}
