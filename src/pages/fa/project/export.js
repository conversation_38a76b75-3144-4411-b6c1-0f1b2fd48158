import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useSelector, useDispatch } from 'react-redux';

// Components
import Layout from '../../../layouts/module/fa';
import ProjectDetails from '../../../components/fa/ProjectDetails';
import AllCapex from '../../../components/fa/AllCapex';
import AllOpex from '../../../components/fa/Opex';
import AllRevenue from '../../../components/fa/AllRevenue';
import FinancialAnalysis from '../../../components/fa/FinancialAnalysis';
import Summary from '../../../components/fa/Summary';
import AttachmentBox from '../../../components/Shared/AttachmentBox';

// Others
import { FA_ENDPOINT } from '../../../utils/fa';
import { fetchScenarioData } from '../../../utils/store/faReducer';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import ScreenshotDialog from '../../../components/Shared/ScreenshotDialog';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const router = useRouter();
  const { projectId, scenarioId } = router.query;
  const dispatch = useDispatch();
  const { scenarioData, projectData } = useSelector((state) => state.fa);
  const [capexTotal, setCapexTotal] = useState(null);
  const [opexTotals, setOpexTotals] = useState({
    otc: null,
    otcAmortise: null,
    mrc: null
  });
  const [revenueTotals, setRevenueTotals] = useState({
    otc: null,
    otcAmortise: null,
    mrc: null,
    contractValue: null
  });

  // Fetch data
  const fetchAllData = async () => {
    if (scenarioId) {
      dispatch(setIsLoading(true));
      await dispatch(fetchScenarioData(scenarioId));
      dispatch(setIsLoading(false));
    }
  };

  useEffect(() => {
    fetchAllData();
  }, [scenarioId]);

  // Auto-trigger export when component mounts
  useEffect(() => {
    if (scenarioData?.id && projectData?.name) {
      // Automatically open the export dialog after data is loaded
      const exportButton = document.getElementById('export-pdf-button');
      if (exportButton) {
        setTimeout(() => {
          exportButton.click();
        }, 1000);
      }
    }
  }, [scenarioData, projectData]);

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6 flex flex-col gap-4 rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">
            Export Full Scenario Report: {scenarioData?.name}
          </h1>
          <button
            id="export-pdf-button"
            className="flex items-center gap-1.5 bg-primary hover:bg-primary/90 rounded-lg px-4 py-2 text-sm font-medium text-white shadow-sm transition-all duration-200"
            onClick={() => {
              const dialog = document.querySelector('[role="dialog"]');
              if (dialog) {
                dialog.setAttribute("open", "");
              }
            }}
          >
            <svg
              className="h-4 w-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            Export Full Report
          </button>
        </div>
        <p className="text-gray-600">
          This page generates a comprehensive PDF report containing all sections of this scenario.
        </p>
      </div>

      {/* This will be hidden but needed for the PDF export functionality */}
      <div className="hidden">
        <ScreenshotDialog
          fileName={`FA-${projectData?.name}-${scenarioData?.name}-FullReport.pdf`}
          isLandscape={true}
        >
          <div className="flex w-full flex-col gap-8 p-8">
            {/* Header */}
            <div className="text-center">
              <h1 className="text-2xl font-bold mb-2">Financial Analysis Report</h1>
              <h2 className="text-xl font-semibold mb-2">{projectData?.name}</h2>
              <h3 className="text-lg font-medium">Scenario: {scenarioData?.name}</h3>
              <p className="text-sm">Generated on: {new Date().toLocaleDateString()}</p>
            </div>

            {/* Profile Section */}
            <div className="flex w-full flex-col gap-4">
              <div className="bg-primary w-full text-center text-white py-2 font-bold">
                SCENARIO INFORMATION
              </div>
              <ProjectDetails scenarioId={scenarioData?.id} exportMode={true} />
            </div>

            {/* CAPEX Section */}
            <div className="flex w-full flex-col gap-4">
              <div className="bg-primary w-full text-center text-white py-2 font-bold">
                CAPEX DETAILS
              </div>
              <AllCapex 
                scenarioId={scenarioData?.id} 
                showExportButton={false} 
                exportMode={true}
                onDataLoaded={(capexData) => {
                  const total = capexData?.reduce((prev, curr) => prev + (curr?.amount || 0), 0);
                  setCapexTotal(total);
                }}
              />
            </div>

            {/* OPEX Section */}
            <div className="flex w-full flex-col gap-4">
              <div className="bg-primary w-full text-center text-white py-2 font-bold">
                OPEX DETAILS
              </div>
              <AllOpex 
                scenarioId={scenarioData?.id} 
                showExportButton={false} 
                exportMode={true}
                onDataLoaded={setOpexTotals}
              />
            </div>

            {/* Revenue Section */}
            <div className="flex w-full flex-col gap-4">
              <div className="bg-primary w-full text-center text-white py-2 font-bold">
                REVENUE DETAILS
              </div>
              <AllRevenue 
                scenarioId={scenarioData?.id} 
                showExportButton={false} 
                exportMode={true}
                onDataLoaded={setRevenueTotals}
              />
            </div>

            {/* Financial Analysis Section */}
            <div className="flex w-full flex-col gap-4">
              <div className="bg-primary w-full text-center text-white py-2 font-bold">
                FINANCIAL ANALYSIS
              </div>
              <FinancialAnalysis 
                scenarioId={scenarioData?.id} 
                showExportButton={false} 
                exportMode={true}
              />
            </div>

            {/* Summary Section */}
            <div className="flex w-full flex-col gap-4">
              <div className="bg-primary w-full text-center text-white py-2 font-bold">
                SUMMARY
              </div>
              <Summary 
                scenarioId={scenarioData?.id} 
                showExportButton={false} 
                exportMode={true}
              />
            </div>

            {/* Supporting Documents Section */}
            <div className="flex w-full flex-col gap-4">
              <div className="bg-primary w-full text-center text-white py-2 font-bold">
                SUPPORTING DOCUMENTS
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full bg-white text-black">
                  <thead>
                    <tr>
                      <th className="bg-fa border border-white px-4 py-2 text-left text-xs font-medium text-white">
                        Supporting Documents
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="p-2">
                        <AttachmentBox
                          key={scenarioId}
                          TITLE="Expenses Supporting Documents"
                          GET_ALL_FILES_ENDPOINT={`${FA_ENDPOINT}/file/expense/${scenarioId}`}
                          UPLOAD_CERTAIN_FILE_ENDPOINT={`${FA_ENDPOINT}/file/upload/expense/${scenarioId}`}
                          DOWNLOAD_CERTAIN_FILE_ENDPOINT={`${FA_ENDPOINT}/file/download`}
                          DELETE_CERTAIN_FILE_ENDPOINT={`${FA_ENDPOINT}/file`}
                          DISABLE_UPLOAD={true}
                          exportMode={true}
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            {/* Footer */}
            <div className="text-center mt-8 text-sm text-gray-600">
              <p>© {new Date().getFullYear()} - Generated from SimiDigital Financial Analysis Module</p>
            </div>
          </div>
        </ScreenshotDialog>
      </div>
    </div>
  );
} 