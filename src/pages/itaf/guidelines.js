// Components
import Layout from '../../layouts/module/itaf';
import AttachmentBox from '../../components/Shared/AttachmentBox';

// Others
import { ITAF_ENDPOINT } from '../../utils/itaf';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const { isAdmin } = useModuleRoleContext();

  // Others

  return (
    <div className="container mx-auto p-1 md:p-4">
      <div className="flex flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
        <p className="w-full text-center text-xl font-bold md:text-left">Guidelines</p>
        <AttachmentBox
          GET_ALL_FILES_ENDPOINT={`${ITAF_ENDPOINT}/files/guideline/64db23af2b57e5b8a12f8c1d`}
          UPLOAD_CERTAIN_FILE_ENDPOINT={`${ITAF_ENDPOINT}/files/upload/guideline/64db23af2b57e5b8a12f8c1d`}
          DOWNLOAD_CERTAIN_FILE_ENDPOINT={`${ITAF_ENDPOINT}/files/download`}
          DELETE_CERTAIN_FILE_ENDPOINT={`${ITAF_ENDPOINT}/files`}
          DISABLE_UPLOAD={!isAdmin}
        />
      </div>
    </div>
  );
}
