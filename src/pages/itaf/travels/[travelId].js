// Next, React, Tw
import { useRef } from 'react';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Components
import Layout from '../../../layouts/module/itaf';
import TravelDetails from '../../../components/itaf/TravelDetails';
import RemarkBox from '../../../components/Shared/RemarkBox';
import HistoryBox from '../../../components/Shared/HistoryBox';
import SelectApprovalBox from '../../../components/Shared/Approval/SelectApprovalBox';
import ApprovalStepper from '../../../components/Shared/Approval/ApprovalStepper';
import AllParticipants from '../../../components/itaf/AllParticipants';
import ParticipantDetails from '../../../components/itaf/ParticipantDetails';
import AllFunnels from '../../../components/itaf/AllFunnels';
import AllAgendas from '../../../components/itaf/AllAgendas';
import ExceutiveSummary from '../../../components/itaf/ExecutiveSummary';
import AttachmentBox from '../../../components/Shared/AttachmentBox';
import ActionButtonGroup from '../../../components/itaf/ActionButtonGroup';

// Others
import { ITAF_ENDPOINT } from '../../../utils/itaf';
import { useModuleRoleContext } from '../../../utils/auth/ModuleRoleProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { query } = useRouter();
  const { participantId } = query;
  const { travelData } = useSelector((state) => state.itaf);

  const { isAdmin } = useModuleRoleContext();

  const hiddenDivRef = useRef(null);

  // Others

  return (
    <>
      {!participantId && (
        <div className="relative h-full w-full">
          <div ref={hiddenDivRef} />
          <div className="container mx-auto flex flex-col gap-4  p-1 md:p-4">
            <ApprovalStepper
              key={travelData?.id}
              GET_ALL_APPROVALS_ENDPOINT={`${ITAF_ENDPOINT}/approvals/travel_id/${travelData?.id}`}
            />
            <ActionButtonGroup />
            <div className="flex h-full flex-col gap-4 md:flex-row">
              <div className="w-full md:w-1/2">
                <TravelDetails />
              </div>
              <div className="flex w-full flex-col gap-4 md:w-1/2">
                <ExceutiveSummary />
                <RemarkBox
                  key={travelData?.id}
                  parentHiddenDivRef={hiddenDivRef}
                  documentIdKey="travel_id"
                  documentIdValue={travelData?.id}
                  GET_ALL_REMARKS_ENDPOINT={`${ITAF_ENDPOINT}/remarks/travel_id/${travelData?.id}`}
                  POST_NEW_REMARK_ENDPOINT={`${ITAF_ENDPOINT}/remarks`}
                />
              </div>
            </div>
            <AllFunnels />
            <div className="flex h-full flex-col gap-4 md:flex-row">
              <div className="w-full md:w-1/2">
                <AllParticipants />
              </div>
              <div className="w-full md:w-1/2">
                <AllAgendas />
              </div>
            </div>

            <div className="flex h-full flex-col gap-4 md:flex-row">
              <div className="w-full md:w-1/2">
                <AttachmentBox
                  key={travelData?.id}
                  TITLE="Approval Attachment"
                  GET_ALL_FILES_ENDPOINT={`${ITAF_ENDPOINT}/files/approval/${travelData?.id}`}
                  UPLOAD_CERTAIN_FILE_ENDPOINT={`${ITAF_ENDPOINT}/files/upload/approval/${travelData?.id}`}
                  DOWNLOAD_CERTAIN_FILE_ENDPOINT={`${ITAF_ENDPOINT}/files/download`}
                  DELETE_CERTAIN_FILE_ENDPOINT={`${ITAF_ENDPOINT}/files`}
                  DISABLE_UPLOAD={!isAdmin || travelData?.IS_DISABLED}
                />
              </div>
              <div className="w-full md:w-1/2">
                <AttachmentBox
                  key={travelData?.id}
                  TITLE="Success Stories Attachment"
                  GET_ALL_FILES_ENDPOINT={`${ITAF_ENDPOINT}/files/success-stories/${travelData?.id}`}
                  UPLOAD_CERTAIN_FILE_ENDPOINT={`${ITAF_ENDPOINT}/files/upload/success-stories/${travelData?.id}`}
                  DOWNLOAD_CERTAIN_FILE_ENDPOINT={`${ITAF_ENDPOINT}/files/download`}
                  DELETE_CERTAIN_FILE_ENDPOINT={`${ITAF_ENDPOINT}/files`}
                />
              </div>
            </div>

            <div className="flex h-full flex-col gap-4 md:flex-row">
              <div className="w-full md:w-1/2">
                <HistoryBox
                  key={travelData?.id}
                  parentHiddenDivRef={hiddenDivRef}
                  GET_ALL_HISTORIES_ENDPOINT={`${ITAF_ENDPOINT}/historys/travel_id/${travelData?.id}`}
                />
              </div>
              <div className="w-full md:w-1/2">
                <SelectApprovalBox
                  key={travelData?.id}
                  GET_ALL_APPROVALS_ENDPOINT={`${ITAF_ENDPOINT}/approvals/travel_id/${travelData?.id}`}
                  UPDATE_CERTAIN_APPROVAL_ENDPOINT={`${ITAF_ENDPOINT}/approvals`}
                  disabled={travelData?.IS_DISABLED}
                />
              </div>
            </div>
          </div>
        </div>
      )}
      {participantId && <ParticipantDetails />}
    </>
  );
}
