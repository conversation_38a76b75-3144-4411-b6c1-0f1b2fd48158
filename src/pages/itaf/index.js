// Next, React, Tailwind
import { useEffect, useState } from 'react';
import { twMerge } from 'tailwind-merge';
import { useDispatch } from 'react-redux';

// Components
import Layout from '../../layouts/module/itaf';
import AllTravels from '../../components/itaf/AllTravels';
import ReactAnimatedNumber from '../../components/Shared/ReactAnimatedNumber';

// Others
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { ITAF_ENDPOINT } from '../../utils/itaf';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const dispatch = useDispatch();
  const [summaryData, setSummaryData] = useState({});

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${ITAF_ENDPOINT}/all/keyword`);

      if (response?.data?.data) {
        setSummaryData({
          business: response?.data?.data?.filter((o) =>
            o?.status?.toLowerCase()?.includes('business')
          ).length,
          gm: response?.data?.data?.filter((o) => o?.status?.toLowerCase()?.includes('gm')).length,
          evp: response?.data?.data?.filter((o) => o?.status?.toLowerCase()?.includes('evp'))
            .length,
          completed: response?.data?.data?.filter((o) =>
            o?.status?.toLowerCase()?.includes('completed')
          ).length,
        });
      }
    } catch {
      setSummaryData({});
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div className="container mx-auto p-1 md:p-4">
        <div className="flex w-full flex-col gap-2 md:flex-row">
          {[
            {
              label: 'BUSINESS FINANCE',
              key: 'business',
              style: 'bg-[#c3c3c3] text-black',
            },
            {
              label: 'FINANCE GM',
              key: 'GM',
              style: 'bg-[#ffb783] text-black',
            },
            {
              label: 'EVP',
              key: 'evp',
              style: 'bg-[#3f8ed0] text-white',
            },
            {
              label: 'COMPLETED',
              key: 'completed',
              style: 'bg-[#4ac78e] text-white',
            },
          ]?.map((o, i) => (
            <div
              key={i}
              className={twMerge(
                'flex w-full flex-col  gap-2 rounded-lg px-2 py-2 md:w-1/4',
                o?.style
              )}
            >
              <p>{o?.label}</p>
              <p className="text-right text-2xl font-bold">
                <ReactAnimatedNumber
                  value={summaryData[o?.key]}
                  formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                />
              </p>
            </div>
          ))}
        </div>
      </div>
      <AllTravels />
    </>
  );
}
