// Next, React, Tw
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Packages
import { Dialog, DialogTitle, DialogContent, IconButton } from '@mui/material';
import { Close } from '@mui/icons-material';

// Components
import { TablePaginationCustom } from '../../components/Shared/table';
import Layout from '../../layouts/module/noc';

// Others
import axios from '../../utils/axios';
import { CAPRY_ENDPOINT } from '../../utils/capry';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { checkAndReplaceStringWithHyphen } from '../../utils/shared';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const [selectedRow, setSelectedRow] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const dispatch = useDispatch();
  const { page, rowsPerPage } = useSelector((state) => state.simi);

  // Table
  const [tableData, setTableData] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');

  const filteredTableData = (() => {
    if (!searchQuery) return tableData;

    return tableData.filter((o) =>
      JSON?.stringify(o)?.toLowerCase().includes(searchQuery.toLowerCase())
    );
  })();

  const getBodyCellStyle = () => 'text-center whitespace-nowrap px-2 text-xs';

  // Others
  const getStatusStyle = (status) => {
    if (!status) return 'bg-white';

    switch (status.toLowerCase()) {
      case 'in service':
        return 'bg-[#a9ffb7]';
      case 'pending':
        return 'bg-[#fff1aa]';
      case 'terminate' || 'terminated':
        return 'bg-[#ffcdb7]';
      default:
        return 'bg-white';
    }
  };

  const handleRowClick = (row) => {
    setSelectedRow(row);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedRow(null);
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/buying_management/all/all`);
      const data = response.data.data || [];
      setTableData(Array.isArray(data) ? data : []);
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div className="bg-noc px-4 py-2 text-white">
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-extrabold">List of Buying Management</p>
          </div>
        </div>
      </div>
      <div className="container mx-auto p-1 md:p-4">
        <div className="rounded-xl bg-white text-black dark:bg-gray-600 dark:text-white">
          <div className="mx-auto flex w-full flex-col justify-center gap-6 p-4">
            <div className="flex flex-col gap-2">
              <div className="justify-begin flex flex-col gap-2 md:flex-row">
                <input
                  type="text"
                  placeholder="Search in all columns..."
                  value={searchQuery}
                  onChange={(event) => setSearchQuery(event.target.value)}
                  className="focus:border-noc w-full rounded-md border border-gray-300 px-2 py-1 focus:outline-none"
                />
              </div>
              <div className="my-4">
                <div className="overflow-x-auto scrollbar">
                  <table className="min-w-full bg-white text-black">
                    <thead>
                      <tr>
                        {[
                          'No.',
                          'BM ID',
                          'Circuit ID',
                          'Customer',
                          'Status',
                          'Speed',
                          'Cable System',
                          'SLA/SLG',
                        ].map((label, i) => (
                          <td key={i} className="bg-noc px-2 text-sm text-white">
                            <p className="text-center">{label}</p>
                          </td>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {filteredTableData
                        .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                        .map((row, i) => (
                          <tr
                            key={i}
                            className="cursor-pointer transition duration-150 odd:bg-[#f8f8f8] even:bg-white hover:bg-gray-200"
                            onClick={() => handleRowClick(row)}
                          >
                            <td className={getBodyCellStyle()}>{i + 1 + rowsPerPage * page}</td>
                            <td className={getBodyCellStyle()}>
                              {checkAndReplaceStringWithHyphen(row.name)}
                            </td>
                            <td className={getBodyCellStyle()}>
                              {checkAndReplaceStringWithHyphen(row.nova_circuit_id)}
                            </td>
                            <td className={getBodyCellStyle()}>
                              {checkAndReplaceStringWithHyphen(row.customer_name)}
                            </td>
                            <td
                              className={`${getBodyCellStyle()} ${getStatusStyle(row.service_status)}`}
                            >
                              {checkAndReplaceStringWithHyphen(row.service_status)}
                            </td>
                            <td className={getBodyCellStyle()}>
                              {checkAndReplaceStringWithHyphen(row.speed)}
                            </td>
                            <td className={getBodyCellStyle()}>
                              {checkAndReplaceStringWithHyphen(row.cable_system)}
                            </td>
                            <td className={getBodyCellStyle()}>
                              {checkAndReplaceStringWithHyphen(row.sla_slg)}
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                </div>
                <TablePaginationCustom count={filteredTableData?.length} />
                <div className="text-center text-xs italic text-gray-500">
                  Click on any row to view more details
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle className="bg-noc flex items-center justify-between text-white">
          <p>Buying Management Details</p>
          <IconButton onClick={handleCloseDialog}>
            <Close className="text-white" />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          {selectedRow && (
            <div className="py-4">
              {[
                { label: 'ID', key: 'id' },
                { label: 'BM ID', key: 'name' },
                { label: 'Buying Type', key: 'buying_type' },
                { label: 'Cable System', key: 'cable_system' },
                { label: 'Contract Owner Email', key: 'contract_owner_email' },
                { label: 'Opportunity ID', key: 'opportunity_id' },
                { label: 'Partner', key: 'partner' },
                { label: 'Customer Name', key: 'customer_name' },
                { label: 'Project ID', key: 'project_id' },
                { label: 'Product', key: 'product' },
                { label: 'TMRO Project Reference', key: 'tmro_project_reference' },
                { label: 'Supplier Service No', key: 'supplier_service_no' },
                { label: 'TM Service No', key: 'tm_service_no' },
                { label: 'Service Status', key: 'service_status' },
                { label: 'Nova Circuit ID', key: 'nova_circuit_id' },
                { label: 'Speed', key: 'speed' },
                { label: 'SLA/SLG', key: 'sla_slg' },
                { label: 'Order Submission Date', key: 'order_submission_date' },
                { label: 'Opportunity Owner', key: 'opportunity_owner' },
                { label: 'Service Termination Date', key: 'service_termination_date' },
                { label: 'Outpayment Start Date', key: 'outpayment_start_date' },
                { label: 'Outpayment End Date', key: 'outpayment_end_date' },
              ]?.map((o, i) => (
                <div key={i} className="grid grid-cols-2 gap-4 border-b py-2">
                  <div className="font-semibold">{o.label}</div>
                  <div>{checkAndReplaceStringWithHyphen(selectedRow?.[o?.key])}</div>
                </div>
              ))}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
