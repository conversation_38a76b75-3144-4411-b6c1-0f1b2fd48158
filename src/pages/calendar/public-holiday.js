// Next, React, Tw
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { Edit } from '@mui/icons-material';

// Packages
import * as yup from 'yup';
import moment from 'moment';

// Components
import Layout from '../../layouts/module/calendar';
import { TablePaginationCustom } from '../../components/Shared/table';
import { AutoCompleteTextInput, DateInput, SearchInput } from '../../components/Shared/CustomInput';

// Others
import axios from '../../utils/axios';
import { useSnackbar } from '../../components/Shared/snackbar';
import { AUM_ENDPOINT } from '../../utils/aum';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { toUpperCaseFirstLetter } from '../../utils/shared';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { q } = query;

  // Table
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(20);
  const [tableData, setTableData] = useState([]);

  const getBodyCellStyle = () => 'text-center py-1 text-xs';

  const onChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const getTableData = () => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  };

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);

  const schema = yup.object({
    year: yup.number().required('Please provide year.'),
    date: yup.number().required('Please provide date.'),
    occasion: yup.string().required('Please provide occasion.'),
  });

  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }

    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
        return;
      }
      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${AUM_ENDPOINT}/date/v1`, payload);
            break;
          case 'put':
            response = await axios.put(`${AUM_ENDPOINT}/date/v1/${payload.id}`);
            break;
          case 'delete':
            response = await axios.delete(`${AUM_ENDPOINT}/date/v1/${payload.id}`);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
      } catch (error) {
        // console.log(error);
      }
    }

    setDialogData({});
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${AUM_ENDPOINT}/date/v1/all/keyword`);
      if (response.data.data) {
        setTableData(response.data.data);
      }
    } catch (error) {
      // console.error('Error fetching data:', error);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div className="container mx-auto p-1 md:p-4">
        <div
          style={{ boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)' }}
          className="flex flex-col gap-4 rounded-xl bg-white p-4 text-black dark:bg-gray-600 dark:text-white"
        >
          <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
            <SearchInput />
            <button
              type="button"
              className="bg-calendar cta-btn"
              onClick={() => handleClickOpenDialog(false)}
            >
              New Date
            </button>
          </div>
          <div className="overflow-x-auto scrollbar">
            <table className="min-w-full bg-white text-black">
              <thead>
                <tr>
                  {['Year', 'Date', 'Occasion', ''].map((label, i) => (
                    <td
                      key={i}
                      className="bg-calendar whitespace-nowrap px-4 text-center text-white"
                    >
                      {label}
                    </td>
                  ))}
                </tr>
              </thead>
              <tbody>
                {getTableData()
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((row, i) => (
                    <tr key={i} className={`${`${i % 2 === 0 ? 'bg-[#f8f8f8]' : 'bg-white'}`}`}>
                      <td className={getBodyCellStyle()}>{row?.year}</td>
                      <td className={getBodyCellStyle()}>
                        {moment.unix(row?.date).format('YYYY-MM-DD')}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {toUpperCaseFirstLetter(row?.occasion)}
                      </td>
                      <td className=" text-center">
                        <div className="flex gap-2">
                          <button
                            type="button"
                            className=""
                            onClick={() => handleClickOpenDialog(true, row)}
                          >
                            <Edit className="text-calendar" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
          <TablePaginationCustom
            count={tableData.length}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={onChangePage}
          />
        </div>
      </div>
      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle sx={{ backgroundColor: '#536eef' }}>
          {!editModeDialog && <p className="text-white">Add New Date</p>}
          {editModeDialog && <p className="text-white">Edit Date</p>}
        </DialogTitle>
        <DialogContent>
          <div className="flex flex-col gap-4 p-4">
            <DateInput
              views={['month', 'day']}
              value={moment().format('YYYY-MM-DD')}
              placeholder="Date"
              returnedFormat="YYYY-MM-DD"
              onChange={(target) =>
                setDialogData((prev) => ({
                  ...prev,
                  year: Number(moment(target?.target?.value, 'YYYY-MM-DD')?.year()),
                  date: Number(moment(target?.target?.value, 'YYYY-MM-DD')?.unix()),
                }))
              }
            />
            <AutoCompleteTextInput
              name="occasion"
              value={dialogData?.occasion}
              placeholder="Occasion"
              options={['salary']}
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between">
            <div>
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('delete')}
                  className="bg-[#ff6d6d] p-2 text-white"
                >
                  Delete
                </button>
              )}
            </div>
            <div className="flex gap-2">
              <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
                Close
              </button>

              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-admin p-2 text-white"
                >
                  Save
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-admin p-2 text-white"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
}
