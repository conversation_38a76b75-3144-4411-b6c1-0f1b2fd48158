// Next, React, Tw
import Image from 'next/image';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';
import Link from 'next/link';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions, Divider, Tooltip } from '@mui/material';

// Packages
import moment from 'moment';
import { json2csv } from 'json-2-csv';
import * as yup from 'yup';
import { useSnackbar } from 'notistack';

// Components
import Layout from '../../layouts/module/hsba';
import { HSBA_ENDPOINT, getLogo, useHsbaContext } from '../../utils/hsba';
import { DateInput, TextInput, SelectInput } from '../../components/Shared/CustomInput';
import ReactAnimatedNumber from '../../components/Shared/ReactAnimatedNumber';

// Others
import axios from '../../utils/axios';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { sortArrayOfObjectsByCertainKeyAlphabetically } from '../../utils/shared';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { useAuthContext } from '../../utils/auth/useAuthContext';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { query } = useRouter();
  const { setParam, replaceParam } = useParamContext();
  const { month } = query;
  const { isAdmin } = useModuleRoleContext();
  const { user } = useAuthContext();
  const dispatch = useDispatch();
  const { triggerEngine } = useHsbaContext();
  const { enqueueSnackbar } = useSnackbar();

  const [interRegionData, setInterRegionData] = useState([]);

  // Dialog
  const schema = yup.object().shape({
    month: yup.string().required('Month is required')?.default(moment().format('YYYY-MM')),
    customer: yup.string().required('Customer is required'),
    data: yup.number().required('Data is required'),
    voice: yup.number().required('Voice is required'),
    iptv: yup.number().required('IPTV is required'),
  });
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  // Others
  const bodyCellStyle = 'text-center text-black whitespace-nowrap text-xs';

  const handleDownloadButtonClick = async (interRegionId, customerName) => {
    dispatch(setIsLoading(true));
    let response;
    try {
      response = await axios.get(`${HSBA_ENDPOINT}/interRegion/id/${interRegionId}`);
    } catch {
      /* empty */
    }
    dispatch(setIsLoading(false));

    let temp = [];
    for (let i = 0; i < response.data.length; i += 1) {
      temp = [...temp, ...response?.data[i]?.data];
    }
    const blob = new Blob([(await json2csv(temp))?.replaceAll('undefined', '')], {
      type: 'text/csv',
    });
    const a = document.createElement('a');
    a.download = `Inter-Region-${customerName?.split(' ')[0]}-${moment().format('YYYY-MM')}.csv`;
    a.href = window.URL.createObjectURL(blob);
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${HSBA_ENDPOINT}/interRegion/month/${month}`);
      setInterRegionData(sortArrayOfObjectsByCertainKeyAlphabetically(response.data, 'customer'));
    } catch {
      setInterRegionData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (!month) {
      replaceParam({ month: moment().subtract(1, 'months').format('YYYY-MM') });
      return;
    }
    fetchData();
  }, [month]);

  return (
    <>
      <div
        style={{
          background: 'linear-gradient(to right bottom, #7957d5, #485fc7)',
          color: 'white',
          padding: '15px',
        }}
      >
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-bold">Inter-Region</p>
          </div>
          {user?.isSuperAdmin && (
            <button
              type="button"
              className="cta-btn bg-green-500"
              onClick={() => setDialogOpen(true)}
            >
              Generate Data
            </button>
          )}
        </div>
      </div>
      <div className="container flex flex-col gap-4 p-4">
        <div className="flex flex-row-reverse items-center justify-between">
          <div className="flex items-center gap-2">
            <Link
              href="/hsba/inter-region-calculation-formula.png"
              target="_blank"
              className="whitespace-nowrap text-xs text-blue-500 hover:underline"
            >
              Calculation Formula
            </Link>
            <DateInput
              views={['year', 'month']}
              value={month}
              placeholder="Month"
              returnedFormat="YYYY-MM"
              onChange={(event) => setParam({ month: event.target.value })}
            />
            {isAdmin && (
              <Tooltip title="Depot Data OneDrive Storage">
                <Link
                  href="https://tm365-my.sharepoint.com/:f:/g/personal/kamil_fauzi_tm_com_my/ErYuHbhUbqFNkOkTkb47sB4BskAe-gJeUMGnThdxGaohGw?e=r6rx8p"
                  target="_blank"
                >
                  <Image src="/assets/icons/onedrive.webp" alt="OneDrive" width={30} height={30} />
                </Link>
              </Tooltip>
            )}
          </div>
        </div>

        <div
          style={{
            boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)',
          }}
          className=" rounded-xl bg-white p-4"
        >
          <div className="flex flex-col">
            {interRegionData.map((data, index) => (
              <div key={index} className="mb-8 flex flex-col gap-4">
                <div className="flex flex-col items-center justify-between gap-4 md:flex-row md:gap-0">
                  <div className="flex flex-col items-center gap-5 md:flex-row">
                    <Image
                      src={getLogo(data.customer)}
                      alt="Customer Logo"
                      width={70}
                      height={70}
                    />

                    <div className="flex items-center gap-1">
                      <p className="text-lg font-bold text-black">RM</p>{' '}
                      <ReactAnimatedNumber
                        value={data.total_charge}
                        style={{
                          fontSize: '1.17rem',
                          fontWeight: 'bold',
                          color: 'black',
                        }}
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <p className="text-xs font-semibold">
                      Last Updated at : {moment(data.updatedAt).format('YYYY-MM-DD, hh:mm A')}
                    </p>
                    <button
                      type="button"
                      onClick={() => handleDownloadButtonClick(String(data._id), data.customer)}
                    >
                      <Image
                        src="/assets/icons/csv-logo.svg"
                        alt="CSV Icon"
                        width={25}
                        height={25}
                      />
                    </button>
                  </div>
                </div>

                <div className="overflow-x-auto scrollbar">
                  <table className="min-w-full">
                    <thead>
                      <tr>
                        {[
                          'Type',
                          'Service Gateway Bandwidth / Mbps',
                          'Nationwide Bandwidth / Mbps',
                          'Contention Ratio',
                        ].map((label, i) => (
                          <td
                            key={i}
                            className="bg-hsba whitespace-nowrap py-1 text-center text-sm text-white"
                          >
                            {label}
                          </td>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {data.details.map((row, i) => (
                        <tr key={i}>
                          <td className={bodyCellStyle}>{row.type}</td>
                          <td className={bodyCellStyle}>
                            {row?.serviceGateway?.toLocaleString('en-US')}
                          </td>
                          <td className={bodyCellStyle}>
                            {row?.bandwidth?.toLocaleString('en-US')}
                          </td>
                          <td className={bodyCellStyle}>
                            {`${row?.ratio === null ? '-' : row?.ratio}`}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                {index !== interRegionData.length - 1 && <Divider />}
              </div>
            ))}
          </div>
        </div>
      </div>

      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)}>
        <form
          onSubmit={async (event) => {
            event.preventDefault();
            let payload;
            try {
              payload = await schema.validate(dialogData, { abortEarly: false });
            } catch (error) {
              enqueueSnackbar(error.errors, {
                variant: 'error',
              });
              return;
            }
            await triggerEngine('inter_region', payload);
          }}
        >
          <DialogTitle className="bg-hsba text-white">Generate Inter-Region Data</DialogTitle>
          <DialogContent>
            <div className="flex w-[400px] flex-col gap-2 px-2 py-4">
              <DateInput
                views={['month']}
                name="month"
                value={dialogData?.month}
                placeholder="Month"
                returnedFormat="YYYY-MM"
                onChange={handleDialogDataChange}
              />
              <SelectInput
                name="customer"
                value={dialogData?.customer}
                placeholder="Customer"
                options={['celcom', 'digi', 'maxis', 'measat', 'umobile', 'redtone', 'viewqwest']}
                onChange={handleDialogDataChange}
              />
              <TextInput
                name="data"
                value={dialogData?.data}
                placeholder="Data Mbps"
                onChange={handleDialogDataChange}
              />
              <TextInput
                name="voice"
                value={dialogData?.voice}
                placeholder="Voice / Mbps"
                onChange={handleDialogDataChange}
              />
              <TextInput
                name="iptv"
                value={dialogData?.iptv}
                placeholder="IPTV / Mbps"
                onChange={handleDialogDataChange}
              />
            </div>
          </DialogContent>
          <DialogActions>
            <div className="flex justify-end gap-4">
              <button
                type="button"
                onClick={() => setDialogOpen(false)}
                className="cta-btn text-black"
              >
                Cancel
              </button>
              <button type="submit" className="bg-hsba cta-btn">
                Save
              </button>
            </div>
          </DialogActions>
        </form>
      </Dialog>
    </>
  );
}
