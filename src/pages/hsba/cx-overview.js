// Next, React, Tw
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';

// Packages
import * as R from 'ramda';
import moment from 'moment';

// Components
import Order from '../../components/hsba/ReturnOrder';
import DemandListRequest from '../../components/hsba/DemandListRequest';
import { useSnackbar } from '../../components/Shared/snackbar';
import Layout from '../../layouts/module/hsba';
import { TextInput, SelectInput, DateInput } from '../../components/Shared/CustomInput';
import ReactAnimatedNumber from '../../components/Shared/ReactAnimatedNumber';

// Others
import { HSBA_ENDPOINT } from '../../utils/hsba';
import { NOTIFICATION_ENDPOINT } from '../../utils/notification';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useParamContext } from '../../utils/auth/ParamProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const { isLoading } = useSelector((state) => state.loading);
  const { query } = useRouter();
  const { date, customer } = query;
  const { setParam, replaceParam } = useParamContext();

  const [returnOrders, setReturnOrders] = useState([]);
  const [demandLists, setDemandLists] = useState([]);

  const [transferRequest, setTransferRequest] = useState([
    {
      meet: 0,
      jeopardy: 0,
      breached: 0,
    },
  ]);

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const DIALOG_DATA = {
    customer: null,
    meet: 0,
    jeopardy: 0,
    breached: 0,
  };
  const [dialogData, setDialogData] = useState(DIALOG_DATA);
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }
    setDialogOpen(true);
  };
  const handleDialogClose = async (action) => {
    if (action) {
      try {
        if (Object.values(dialogData).includes(null)) {
          enqueueSnackbar('Please fill in all field.', {
            variant: 'error',
          });
          return;
        }
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${HSBA_ENDPOINT}/transferRequest`, {
              date,
              customer: dialogData?.customer,
              meet: Number(dialogData?.meet),
              jeopardy: Number(dialogData?.jeopardy),
              breached: Number(dialogData?.breached),
            });
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = 'Success';
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch (error) {
        // console.log(error);
      }
    }

    setDialogData(DIALOG_DATA);
    setDialogOpen(false);
    fetchData();
  };

  // Others
  const month = moment(date, 'YYYY-MM-DD').format('YYYY-MM');

  const filteredOrders = returnOrders.filter((order) => order.isReturnedAt(month));

  const getTransferRequest = () => {
    if (customer === 'all') {
      const sumByKey = (key) => R.pipe(R.pluck(key), R.sum);
      return [
        {
          meet: sumByKey('meet')(transferRequest),
          jeopardy: sumByKey('jeopardy')(transferRequest),
          breached: sumByKey('breached')(transferRequest),
        },
      ];
    }
    return transferRequest?.filter((o) => o?.customer === customer);
  };
  const transferRequests = getTransferRequest();

  const getDemandList = () => {
    if (customer === 'all') {
      return demandLists;
    }
    return demandLists.filter((o) => o?.CUSTOMER?.includes(customer));
  };
  const demandList = getDemandList();
  const getReturnOrders = () => {
    if (customer === 'all') {
      return filteredOrders;
    }
    return filteredOrders?.filter((o) => o?.ACCOUNT_NAME?.includes(customer));
  };
  const returnOrders_ = getReturnOrders();

  const getFilterList = () => {
    if (returnOrders?.length === 0 || demandLists?.length === 0) {
      return [];
    }
    const temp = R.uniq(R.pluck('CUSTOMER', demandLists));
    let temp2 = R.uniq(R.pluck('ACCOUNT_NAME', returnOrders));
    temp2 = temp?.map((o) => o?.split(' ')[0]);
    return Array.from(new Set(temp2.concat(temp)));
  };
  const filterList = getFilterList();

  const sendEmail = async () => {
    const recipientList = ['<EMAIL>', '<EMAIL>'];
    dispatch(setIsLoading(true));
    try {
      for (let i = 0; i < recipientList?.length; i += 1) {
        // eslint-disable-next-line
        await axios.post(`${NOTIFICATION_ENDPOINT}/send_email`, {
          body: 'string',
          html_body: `<!DOCTYPE html>\n<html lang="en">\n<head>\n<meta charset="UTF-8">\n<meta name="viewport" content="width=device-width, initial-scale=1.0">\n<title>Your Page Title</title>\n</head>\n<body>\n<div style="box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5); border-radius: 0.75rem; background-color: #fff;" class="">\n  <div style="display: flex; flex-direction: column; align-items: left; gap: 0.75rem; padding-left: 2rem; padding-right: 2rem;" class="">\n    <p style="font-size: 1.25rem; font-weight: bold;" class="">Transfer Request</p>\n    <div style="display: flex; width: 100%; flex-direction: row; align-items: center; gap: 12px;" class="">\n      <div style="width: 100px; border-radius: 0.375rem; background-color: #00b04f; color: #fff; padding: 0.5rem 1rem; display: flex; flex-direction: column; align-items: center; gap: 0.5rem;" class="">\n        <p style="font-size: 0.875rem; font-weight: bold;" class="">Meet</p>\n        <p style="font-size: 2rem;">${
            transferRequests[0]?.meet
          }</p>\n      </div>\n      <div style="width: 100px; border-radius: 0.375rem; background-color: #ffe699; color: #000; padding: 0.5rem 1rem; display: flex; flex-direction: column; align-items: center; gap: 0.5rem;" class="">\n        <p style="font-size: 0.875rem; font-weight: bold;" class="">Jeopardy</p>\n        <p style="font-size: 2rem;">${
            transferRequests[0]?.jeopardy
          }</p>\n      </div>\n      <div style="width: 100px; border-radius: 0.375rem; background-color: #ff0000; color: #fff; padding: 0.5rem 1rem; display: flex; flex-direction: column; align-items: center; gap: 0.5rem;" class="">\n        <p style="font-size: 0.875rem; font-weight: bold;" class="">Breached</p>\n        <p style="font-size: 2rem;">${
            transferRequests[0]?.breached
          }</p>\n      </div>\n    </div>\n    <p style="font-size: 0.75rem;"><i>**TR Daily: Status Approved</i></p>\n    <p style="font-size: 1.25rem; font-weight: bold;">Demand List</p>\n    <div style="display: flex; width: 100%; flex-direction: row; align-items: center; gap: 12px;" class="">\n      <div style="width: 100px; border-radius: 0.375rem; background-color: #00b04f; color: #fff; padding: 0.5rem 1rem; display: flex; flex-direction: column; align-items: center; gap: 0.5rem;" class="">\n        <p style="font-size: 0.875rem; font-weight: bold;" class="">Meet</p>\n        <p style="font-size: 2rem;">${
            demandList.filter((request) => request.isMeet()).length
          }</p>\n      </div>\n      <div style="width: 100px; border-radius: 0.375rem; background-color: #ffe699; color: #000; padding: 0.5rem 1rem; display: flex; flex-direction: column; align-items: center; gap: 0.5rem;" class="">\n        <p style="font-size: 0.875rem; font-weight: bold;" class="">Jeopardy</p>\n        <p style="font-size: 2rem;">${
            demandList.filter((request) => request.isInJeopardy()).length
          }</p>\n      </div>\n      <div style="width: 100px; border-radius: 0.375rem; background-color: #ff0000; color: #fff; padding: 0.5rem 1rem; display: flex; flex-direction: column; align-items: center; gap: 0.5rem;" class="">\n        <p style="font-size: 0.875rem; font-weight: bold;" class="">Breached</p>\n        <p style="font-size: 2rem;">${
            demandList.filter((request) => request.isBreached()).length
          }</p>\n      </div>\n    </div>\n    <p style="font-size: 1.25rem; font-weight: bold;">Return Order</p>\n    <div style="display: flex; width: 100%; flex-direction: row; align-items: center; gap: 12px;" class="">\n      <div style="width: 100px; border-radius: 0.375rem; background-color: #00b04f; color: #fff; padding: 0.5rem 1rem; display: flex; flex-direction: column; align-items: center; gap: 0.5rem;" class="">\n        <p style="font-size: 0.875rem; font-weight: bold;" class="">Meet</p>\n        <p style="font-size: 2rem;">${
            returnOrders_.filter(
              (order) =>
                order.isMonitored(month) &&
                !order.isBreached(month) &&
                !order.isInJeopardy(month) &&
                !order.isToCancel(month)
            ).length
          }</p>\n      </div>\n      <div style="width: 100px; border-radius: 0.375rem; background-color: #ffe699; color: #000; padding: 0.5rem 1rem; display: flex; flex-direction: column; align-items: center; gap: 0.5rem;" class="">\n        <p style="font-size: 0.875rem; font-weight: bold;" class="">Jeopardy</p>\n        <p style="font-size: 2rem;">${
            returnOrders_.filter((order) => order.isInJeopardy(month) && !order.isToCancel(month))
              .length
          }</p>\n      </div>\n      <div style="width: 100px; border-radius: 0.375rem; background-color: #ff0000; color: #fff; padding: 0.5rem 1rem; display: flex; flex-direction: column; align-items: center; gap: 0.5rem;" class="">\n        <p style="font-size: 0.875rem; font-weight: bold;" class="">Breached</p>\n        <p style="font-size: 2rem;">${
            returnOrders_.filter((order) => order.isBreached(month) && order.isMonitored(month))
              .length
          }</p>\n      </div> </div>\n  <p style="font-size: 0.75rem;"><i>**Exclusion : 3rd party land owner / PBT approval - Related to capex budget
                      issue (zerox)</i></p>  \n  </div>\n</div>\n</body>\n</html>`,
          recipient: recipientList[i],
          sender: '<EMAIL>',
          subject: `Daily Performance Report Card for ${customer} ${date}`,
        });
      }
      enqueueSnackbar('Success', {
        variant: 'success',
        anchorOrigin: {
          vertical: 'top',
          horizontal: 'center',
        },
      });
    } catch {
      enqueueSnackbar('Something went wrong', {
        variant: 'error',
      });
    }
    dispatch(setIsLoading(false));
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${HSBA_ENDPOINT}/returnOrder/${date}`);
      setReturnOrders(response.data[0].orders.map((order) => new Order(order)));
    } catch {
      /* empty */
    }

    try {
      const response = await axios.get(
        `${HSBA_ENDPOINT}/demand/${moment().format('YYYY-MM')}/${date}`
      );
      setDemandLists(
        response.data[0].requests.map((request) => new DemandListRequest(request, []))
      );
    } catch {
      /* empty */
    }

    try {
      const response = await axios.get(`${HSBA_ENDPOINT}/transferRequest/${date}`);
      if (response?.data?.length > 0) {
        setTransferRequest(response?.data);
      } else {
        setTransferRequest([]);
      }
      // Test
    } catch {
      setTransferRequest([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (!date) {
      replaceParam({ date: moment().format('YYYY-MM-DD'), customer: 'all' });
      return;
    }
    fetchData();
  }, [date]);

  return (
    <>
      {!isLoading && (
        <>
          <div
            style={{
              background: 'linear-gradient(to right bottom, #485fc7, #7957d5)',
              color: 'white',
              padding: '15px',
            }}
            className="w-full"
          >
            <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
              <div className="flex flex-col gap-1">
                <p className="text-lg font-bold">CX Daily Overview</p>
                <p className="text-xs">As of : {date}</p>
              </div>
            </div>
          </div>
          <div className="container mx-auto p-1 text-black dark:text-white md:p-4">
            <div className="flex w-full flex-col items-center gap-2 p-4 md:flex-row md:justify-between">
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-2">
                  <DateInput
                    value={date}
                    placeholder="Date"
                    returnedFormat="YYYY-MM-DD"
                    onChange={(event) => {
                      setParam({ date: event?.target?.value });
                    }}
                  />
                </div>
                <SelectInput
                  value={customer}
                  placeholder="Customer"
                  options={['all', ...filterList]}
                  onChange={(event) => {
                    setParam({ customer: event.target.value });
                  }}
                />
              </div>
              <div className="flex items-center gap-2">
                {customer !== 'all' && (
                  <>
                    <button
                      type="button"
                      onClick={() => sendEmail()}
                      className="rounded-lg bg-green-500 px-4 py-2 font-semibold text-white"
                    >
                      Send E-mail
                    </button>
                    <button
                      type="button"
                      onClick={() => handleClickOpenDialog(true, transferRequests[0])}
                      className="rounded-lg bg-green-500 px-4 py-2 font-semibold text-white"
                    >
                      Edit TR
                    </button>
                  </>
                )}
              </div>
            </div>
            <div
              style={{ boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)' }}
              className=" rounded-xl bg-white dark:bg-gray-600"
            >
              <div className="flex flex-col items-center gap-2 px-8 py-4 md:items-start">
                <p className="text-xl font-bold">Transfer Request</p>
                <div className="flex w-full flex-col items-center gap-4 md:flex-row">
                  <div className="flex w-[100px] flex-col items-center gap-1 rounded-lg bg-[#00b04f] px-4 py-2 text-white">
                    <p className="text-sm font-semibold">Meet</p>
                    <p className="text-2xl">
                      <ReactAnimatedNumber
                        value={transferRequests[0]?.meet}
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                    </p>
                  </div>
                  <div className="flex w-[100px] flex-col items-center gap-1 rounded-lg bg-[#ffe699] px-4 py-2 text-black">
                    <p className="text-sm font-semibold">Jeopardy</p>
                    <p className="text-2xl">
                      <ReactAnimatedNumber
                        value={transferRequests[0]?.jeopardy}
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                    </p>
                  </div>
                  <div className="flex w-[100px] flex-col items-center gap-1 rounded-lg bg-[#ff0000] px-4 py-2 text-white">
                    <p className="text-sm font-semibold">Breached</p>
                    <p className="text-2xl">
                      <ReactAnimatedNumber
                        value={transferRequests[0]?.breached}
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                    </p>
                  </div>
                </div>
                <p className="text-xs">
                  <i>**TR Daily : Status Approved</i>
                </p>
                <p className="text-xl font-bold">Demand List</p>
                <div className="flex w-full flex-col items-center gap-4 md:flex-row">
                  <div className="flex w-[100px] flex-col items-center gap-1 rounded-lg bg-[#00b04f] px-4 py-2 text-white">
                    <p className="text-sm font-semibold">Meet</p>
                    <p className="text-2xl">
                      <ReactAnimatedNumber
                        value={demandList.filter((request) => request.isMeet()).length}
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                    </p>
                  </div>
                  <div className="flex w-[100px] flex-col items-center gap-1 rounded-lg bg-[#ffe699] px-4 py-2 text-black">
                    <p className="text-sm font-semibold">Jeopardy</p>
                    <p className="text-2xl">
                      <ReactAnimatedNumber
                        value={demandList.filter((request) => request.isInJeopardy()).length}
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                    </p>
                  </div>
                  <div className="flex w-[100px] flex-col items-center gap-1 rounded-lg bg-[#ff0000] px-4 py-2 text-white">
                    <p className="text-sm font-semibold">Breached</p>
                    <p className="text-2xl">
                      <ReactAnimatedNumber
                        value={demandList.filter((request) => request.isBreached()).length}
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                    </p>
                  </div>
                </div>
                <p className="text-xl font-bold">Return Order</p>
                <div className="flex w-full flex-col items-center gap-4 md:flex-row">
                  <div className="flex w-[100px] flex-col items-center gap-1 rounded-lg bg-[#00b04f] px-4 py-2 text-white">
                    <p className="text-sm font-semibold">Meet</p>
                    <p className="text-2xl">
                      <ReactAnimatedNumber
                        value={
                          returnOrders_.filter(
                            (order) =>
                              order.isMonitored(month) &&
                              !order.isBreached(month) &&
                              !order.isInJeopardy(month) &&
                              !order.isToCancel(month)
                          ).length
                        }
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                    </p>
                  </div>
                  <div className="flex w-[100px] flex-col items-center gap-1 rounded-lg bg-[#ffe699] px-4 py-2 text-black">
                    <p className="text-sm font-semibold">Jeopardy</p>
                    <p className="text-2xl">
                      <ReactAnimatedNumber
                        value={
                          returnOrders_.filter(
                            (order) => order.isInJeopardy(month) && !order.isToCancel(month)
                          ).length
                        }
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                    </p>
                  </div>
                  <div className="flex w-[100px] flex-col items-center gap-1 rounded-lg bg-[#ff0000] px-4 py-2 text-white">
                    <p className="text-sm font-semibold">Breached</p>
                    <p className="text-2xl">
                      <ReactAnimatedNumber
                        value={
                          returnOrders_.filter(
                            (order) => order.isBreached(month) && order.isMonitored(month)
                          ).length
                        }
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      )}

      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle>Transfer Request</DialogTitle>
        <DialogContent>
          <div className="flex flex-col gap-2 p-2">
            <SelectInput
              name="customer"
              value={dialogData?.customer}
              placeholder="Customer"
              options={filterList?.map((o) => ({ label: o, value: o }))}
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="meet"
              value={dialogData?.meet}
              placeholder="Meet"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="jeopardy"
              value={dialogData?.jeopardy}
              placeholder="Jeopardy"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="breached"
              value={dialogData?.breached}
              placeholder="Breached"
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex justify-end gap-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
              Cancel
            </button>
            <button
              type="button"
              onClick={() => handleDialogClose('post')}
              className="bg-green-500 p-2 text-white"
            >
              Save
            </button>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
}
