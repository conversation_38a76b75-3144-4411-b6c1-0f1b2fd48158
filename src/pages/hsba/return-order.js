// Next, React, Tw
import Image from 'next/image';
import { twMerge } from 'tailwind-merge';
import { useState, useEffect, Fragment } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Mui
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  IconButton,
} from '@mui/material';
import { Close } from '@mui/icons-material';

// Packages
import * as R from 'ramda';
import moment from 'moment';

// Components
import { TablePaginationCustom } from '../../components/Shared/table';
import Layout from '../../layouts/module/hsba';
import Order from '../../components/hsba/ReturnOrder';
import ApexChart from '../../components/Shared/ReactApexcharts';
import { SelectInput, DateInput } from '../../components/Shared/CustomInput';
import ExportExcelButton from '../../components/Shared/ExportExcelButton';
import TringgerEngineButton from '../../components/hsba/TringgerEngineButton';

// Others
import axios from '../../utils/axios';
import { HSBA_ENDPOINT, getLogo } from '../../utils/hsba';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const dispatch = useDispatch();
  const { isLoading } = useSelector((state) => state.loading);
  const { isAdmin } = useModuleRoleContext();
  const [asOf, setAsOf] = useState('');
  const [orders, setOrders] = useState([]);
  const [filterKeyword, setFilterKeyword] = useState('');
  const [returnedMonth, setReturnedMonth] = useState(moment().format('YYYY-MM'));
  const [showOrders, setShowOrders] = useState([]);
  const [showOrderTitle, setShowOrderTitle] = useState('');
  const [sourceDate, setSourceDate] = useState('');
  const [dateSourceDate, setDateSourceDate] = useState(moment().format('YYYY-MM-DD'));
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(20);

  // Table
  const headCellStyle =
    'text-center bg-hsba text-white border border-[#999999] text-sm font-semibold whitespace-nowrap px-1';

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);

  // Others

  const customerWithCount = R.countBy(R.toUpper)(
    R.sort((a, b) => a.localeCompare(b), R.pipe(R.pluck('ACCOUNT_NAME'))(orders))
  );

  const showThisOrders = (orderList, group, title) => {
    setShowOrders(orderList);
    setShowOrderTitle(`${group} - ${title}`);
    setDialogOpen(true);
  };

  const percentage = (value, total) => {
    if (total === 0) return '-';
    return `${((value / total) * 100).toFixed(1)}%`;
  };

  const activityDateDisplay = (temp_time) => {
    if (!moment(temp_time).isValid()) return '';

    return moment(temp_time).format('YYYY-MM-DD');
  };

  const filteredOrdersAndMonth = (() => {
    if (returnedMonth === '') {
      return orders;
    }

    return orders.filter((order) => order.isReturnedAt(returnedMonth));
  })();

  const filteredOrders = (() => {
    if (filterKeyword === '' || filteredOrdersAndMonth.length === 0) {
      return filteredOrdersAndMonth;
    }

    return filteredOrdersAndMonth.filter((order) =>
      order.ACCOUNT_NAME.toLowerCase().includes(filterKeyword.toLowerCase())
    );
  })();

  const pendingResolvedOrders = filteredOrders.filter(
    (order) =>
      order.isActive(returnedMonth) &&
      order.isMonitored(returnedMonth) &&
      !order.isToCancel(returnedMonth)
  );

  const pendingResolvedOrdersNotMonitored = filteredOrders.filter(
    (order) => !order.isMonitored(returnedMonth)
  );

  const toCancelOrders = filteredOrders.filter((order) => order.isToCancel(returnedMonth));

  const exceptionOrders = filteredOrders.filter(
    (order) =>
      (order.isMissingMir(returnedMonth) || order.isMissingReturnDate(returnedMonth)) &&
      !order.isToCancel(returnedMonth)
  );

  const inJeopardyOrders = filteredOrders.filter(
    (order) => order.isInJeopardy(returnedMonth) && !order.isToCancel(returnedMonth)
  );

  const breachedOrders = filteredOrders.filter(
    (order) => order.isBreached(returnedMonth) && order.isMonitored(returnedMonth)
  );
  // const breachedOrdersNoMirDate = breachedOrders
  //   ?.map((o) => ({
  //     ...o,
  //     SETS: o.SETS.filter((p) => p?.MIR_DATE === ''),
  //   }))
  //   ?.filter((o) => o?.SETS?.length > 0);

  // const breachedOrdersWithMirDate = breachedOrders
  //   ?.map((o) => ({
  //     ...o,
  //     SETS: o.SETS.filter((p) => p?.MIR_DATE !== ''),
  //   }))
  //   ?.filter((o) => o?.SETS?.length > 0);

  const notBreachedOrders = filteredOrders.filter(
    (order) =>
      order.isMonitored(returnedMonth) &&
      !order.isBreached(returnedMonth) &&
      !order.isInJeopardy(returnedMonth) &&
      !order.isToCancel(returnedMonth)
  );

  const ordersWithMultipleReturned = filteredOrders.filter((order) => order.SETS.length > 1);

  const chartOptions = {
    responsive: [
      {
        breakpoint: 1023,
        options: {
          plotOptions: {
            bar: {
              horizontal: false,
            },
          },
          legend: {
            show: true,
            position: 'top',
            floating: false,
            width: 250,
          },
          yaxis: {
            floating: true,
            labels: {
              show: true,
            },
          },
        },
      },
    ],
    grid: {
      show: true,
    },
    chart: {
      toolbar: { show: false },
      stacked: true,
      events: {
        dataPointSelection: (event, chartContext, config) => {
          const { seriesIndex, dataPointIndex } = config;
          let data;
          let title;
          if (dataPointIndex === 1) {
            title = 'Not Monitored';
            data = pendingResolvedOrdersNotMonitored;
          } else {
            switch (seriesIndex) {
              case 1:
                title = 'Meet';
                data = notBreachedOrders;
                break;
              case 2:
                title = 'Jeopardy';
                data = inJeopardyOrders;
                break;
              case 3:
                title = 'Breached';
                data = breachedOrders;
                break;
              case 4:
                title = 'To-Cancel';
                data = toCancelOrders;
                break;
              default:
                break;
            }
          }
          showThisOrders(data, 'All', title);
        },
        click: () => {
          showThisOrders(
            [
              ...notBreachedOrders,
              ...inJeopardyOrders,
              ...breachedOrders,
              ...toCancelOrders,
              ...pendingResolvedOrdersNotMonitored,
            ],
            'All',
            'Due to TM and Customer'
          );
        },
      },
    },
    plotOptions: {
      bar: {
        horizontal: true,
      },
    },
    stroke: {
      width: 1,
      colors: ['#fff'],
    },
    xaxis: {
      categories: ['Due to TM', 'Due to Customer'],
      labels: {
        show: true,
      },
    },
    yaxis: {
      title: {
        text: undefined,
      },
      labels: {
        show: true,
      },
    },
    tooltip: {
      y: {
        formatter: (val) => `${val} orders`,
      },
    },
    fill: {
      opacity: 1,
    },
    legend: {
      show: true,
      position: 'right',
      floating: false,
    },
  };
  const filteredChartSeries = (() => {
    if (isLoading) return [];

    return [
      {
        name: 'Not monitored',
        data: [0, pendingResolvedOrdersNotMonitored.length],
      },
      {
        name: 'Meet',
        data: [notBreachedOrders.length],
      },
      {
        name: 'Jeopardy',
        data: [inJeopardyOrders.length],
      },
      {
        name: 'Breached',
        data: [breachedOrders.length],
      },
      {
        name: 'To-Cancel',
        data: [toCancelOrders.length],
      },
    ];
  })();

  const orderByReturnReason = (() => {
    const getFirstReturnReason = R.pathOr('Unknown', ['FIRST_SET', 'RETURN_REASON']);

    const isMonitored = R.indexBy(R.prop('RETURN_REASON'), [
      {
        RETURN_REASON: 'Due to TM - Contractor',
        monitored: true,
        metric: '15 Calendar Days',
        alert: '10 Calendar Days',
      },
      {
        RETURN_REASON: 'Due to TM - Infra',
        monitored: true,
        metric: '15 Calendar Days',
        alert: '10 Calendar Days',
      },
      {
        RETURN_REASON: 'Due to TM - Operation',
        monitored: true,
        metric: '3 Business Days',
        alert: '2 Business Days',
      },
      {
        RETURN_REASON: 'Due to TM - Order',
        monitored: true,
        metric: '3 Business Days',
        alert: '2 Business Days',
      },
      {
        RETURN_REASON: 'Due to TM - System',
        monitored: true,
        metric: '15 Calendar Days',
        alert: '10 Calendar Days',
      },
      {
        RETURN_REASON: 'Due to TM - Technical',
        monitored: true,
        metric: '5 Business Days',
        alert: '3 Business Days',
      },
    ]);

    return R.pipe(
      R.groupBy(getFirstReturnReason),
      R.mapObjIndexed((order_temp, index) => ({
        RETURN_REASON: index,
        MONITORED: (isMonitored[index] || { monitored: false }).monitored,
        METRIC: (isMonitored[index] || { metric: 'Not monitored' }).metric,
        ALERT: (isMonitored[index] || { alert: 'Not monitored' }).alert,
        TOTAL: order_temp.length,
        PENDING_RESOLVED: order_temp.filter(
          (order) => order.isActive(returnedMonth) && !order.isToCancel(returnedMonth)
        ),
        PENDING_RESOLVED_MEET: order_temp.filter(
          (order) =>
            order.isActive(returnedMonth) &&
            !order.isBreached(returnedMonth) &&
            !order.isInJeopardy(returnedMonth) &&
            !order.isToCancel(returnedMonth)
        ),
        PENDING_RESOLVED_JEOPARDY: order_temp.filter(
          (order) =>
            order.isActive(returnedMonth) &&
            !order.isBreached(returnedMonth) &&
            order.isInJeopardy(returnedMonth) &&
            !order.isToCancel(returnedMonth)
        ),
        PENDING_RESOLVED_BREACHED: order_temp.filter(
          (order) =>
            order.isActive(returnedMonth) &&
            order.isBreached(returnedMonth) &&
            !order.isToCancel(returnedMonth)
        ),
        RESOLVED: order_temp.filter(
          (order) => !order.isActive(returnedMonth) && !order.isToCancel(returnedMonth)
        ),
        RESOLVED_MEET: order_temp.filter(
          (order) =>
            !order.isActive(returnedMonth) &&
            !order.isBreached(returnedMonth) &&
            !order.isInJeopardy(returnedMonth) &&
            !order.isToCancel(returnedMonth)
        ),
        RESOLVED_JEOPARDY: order_temp.filter(
          (order) =>
            !order.isActive(returnedMonth) &&
            !order.isBreached(returnedMonth) &&
            order.isInJeopardy(returnedMonth) &&
            !order.isToCancel(returnedMonth)
        ),
        RESOLVED_BREACHED: order_temp.filter(
          (order) =>
            !order.isActive(returnedMonth) &&
            order.isBreached(returnedMonth) &&
            !order.isToCancel(returnedMonth)
        ),
        TO_CANCEL: order_temp.filter((order) => order.isToCancel(returnedMonth)),
        ALL: order_temp,
      })),
      R.values(),
      R.sortWith([R.descend(R.prop('TOTAL')), R.ascend(R.prop('RETURN_REASON'))])
    )(filteredOrders);
  })();

  const returnedByMonthWithDate = (() => {
    const months = {};

    const repeat = (n) => (f) => (x) => {
      if (n > 0) return repeat(n - 1)(f)(f(x));
      return x;
    };

    const times = (n) => (f) =>
      repeat(n)((i) => {
        f(i);
        return i + 1;
      })(0);

    times(12)((month) => {
      const date = moment().subtract(month, 'month');
      months[date.format('YYYY-MM')] = date.format('MMMM YYYY');
    });

    return months;
  })();

  const getVerdictCellStyle = (text) => {
    if (text.toLowerCase().includes('met')) {
      return { backgroundColor: '#00d1b2', color: 'white' };
    }

    if (text.toLowerCase().includes('missed')) {
      return { backgroundColor: '#ff3860', color: 'white' };
    }

    if (text.toLowerCase().includes('within')) {
      return { backgroundColor: '#4983df', color: 'white' };
    }

    if (text.toLowerCase().includes('threshold')) {
      return { backgroundColor: '#ffdd57' };
    }

    if (text.toLowerCase().includes('missing')) {
      return { backgroundColor: 'white', color: '#ff3860' };
    }

    if (text.toLowerCase().includes('monitored')) {
      return { backgroundColor: '#363636', color: 'white' };
    }

    return {};
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));

    try {
      const response = await axios.get(`${HSBA_ENDPOINT}/returnOrder/${dateSourceDate}`);
      setOrders(response.data[0].orders.map((order) => new Order(order)));
      setSourceDate(response.data[0].message_date);
      setAsOf(moment(response.data[0].updatedAt).format('YYYY-MM-DD, hh:mm A'));
    } catch {
      setOrders([]);
      setSourceDate('');
    }
    dispatch(setIsLoading(false));
    if (dateSourceDate === '')
      setDateSourceDate(moment(sourceDate, 'YYYY/MM/DD hh:mm A').format('YYYY-MM-DD'));
  };

  useEffect(() => {
    fetchData();
  }, [dateSourceDate]);

  return (
    <>
      <div
        style={{
          background: 'linear-gradient(to right bottom, #485fc7, #7957d5)',
          color: 'white',
          padding: '15px',
        }}
      >
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-bold">Return Order</p>
            <p className="text-xs">As of : {asOf}</p>
          </div>
          {isAdmin && <TringgerEngineButton process="return_order" payload={{}} />}
        </div>
      </div>
      <div className="bg-[#f5f5f5] text-black dark:bg-gray-800 dark:text-white">
        <div className="container flex flex-col justify-between gap-4 ">
          <div className="mx-auto my-4 flex w-full flex-col gap-4">
            <div className="flex flex-col items-center justify-between gap-4 md:flex-row md:gap-0">
              <div className="flex flex-col justify-center gap-4">
                <div className="flex items-center gap-4">
                  <DateInput
                    value={dateSourceDate}
                    placeholder="As of"
                    returnedFormat="YYYY-MM-DD"
                    onChange={(event) => {
                      setDateSourceDate(event?.target?.value);
                    }}
                  />
                </div>
                <div className="flex items-center gap-4">
                  <div className="w-[200px]">
                    <SelectInput
                      value={returnedMonth}
                      placeholder="Returned At"
                      options={Object.entries(returnedByMonthWithDate).map(([month, monthStr]) => ({
                        label: monthStr,
                        value: month,
                      }))}
                      onChange={(event) => setReturnedMonth(event.target.value)}
                    />
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-end gap-4">
                <button
                  type="button"
                  className="flex flex-col items-center"
                  onClick={() =>
                    showThisOrders(pendingResolvedOrders, 'All', 'Pending Resolved (TM)')
                  }
                >
                  <p className="w-full rounded-t-[4px] bg-[#ff6600] p-4 text-center text-sm text-white">
                    On-going (SLA&apos;d)
                  </p>
                  <p className="w-full rounded-b-[4px] bg-[#fff9f5] p-4 text-center text-[#5569d0]">
                    {pendingResolvedOrders?.length}
                  </p>
                </button>

                <button
                  type="button"
                  className="flex flex-col items-center"
                  onClick={() =>
                    showThisOrders(
                      pendingResolvedOrdersNotMonitored,
                      'All',
                      'Pending Resolved (Customer)'
                    )
                  }
                >
                  <p className="w-full rounded-t-[4px] bg-[#3e8ed0] p-4 text-center text-sm text-white">
                    On-going (Others)
                  </p>
                  <p className="w-full rounded-b-[4px] bg-[#f7fafd] p-4 text-center text-[#5569d0]">
                    {pendingResolvedOrdersNotMonitored.length}
                  </p>
                </button>
              </div>
            </div>

            <Divider />

            <div className="flex items-center gap-4">
              <button
                type="button"
                className="flex flex-col items-center justify-center rounded-md bg-white p-[16px] text-black"
                onClick={() =>
                  showThisOrders(ordersWithMultipleReturned, 'All', 'Multiple returned')
                }
              >
                <p className="text-sm">Showing</p>
                <p className="text-lg font-bold">
                  {filteredOrders.length !== orders.length && (
                    <span>{filteredOrders.length} of&nbsp;</span>
                  )}
                  <span>{orders.length}</span>
                </p>
              </button>

              <div className="flex flex-grow flex-col items-center justify-around rounded-[4px] bg-white p-4 md:flex-row">
                {[
                  {
                    title: 'Jeopardy',
                    numberColor: 'text-[#ffe08a]',
                    data: inJeopardyOrders,
                  },
                  {
                    title: 'System Error',
                    numberColor: 'text-[#ff6600]',
                    data: exceptionOrders,
                  },
                  {
                    title: 'Breached',
                    numberColor: 'text-[#f14668]',
                    data: breachedOrders,
                  },
                  // {
                  //   title: 'Breached - In progress',
                  //   numberColor: 'text-[#f14668]',
                  //   data: breachedOrdersNoMirDate,
                  // },
                  // {
                  //   title: 'Breached - Resolved',
                  //   numberColor: 'text-[#b60afa]',
                  //   data: breachedOrdersWithMirDate,
                  // },
                  {
                    title: 'To-Cancel',
                    numberColor: 'text-[#363636]',
                    data: toCancelOrders,
                  },
                ].map((o, i) => (
                  <button
                    key={i}
                    type="button"
                    className="flex flex-col items-center justify-center text-black"
                    onClick={() => showThisOrders(o?.data, 'All', o?.title)}
                  >
                    <p className="text-sm">{o?.title}</p>
                    <p className={twMerge('text-xl font-bold', o?.numberColor)}>
                      {o?.data?.length}
                    </p>
                  </button>
                ))}
              </div>
            </div>

            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="text-md">Filter by Customer :</div>
                <div className="w-[200px]">
                  <SelectInput
                    value={filterKeyword}
                    options={[
                      {
                        label: 'All',
                        value: '',
                      },
                      ...Object.entries(customerWithCount).map(([customer, counter]) => ({
                        label: `${customer.split(' ')[0]} (${counter})`,
                        value: customer.split(' ')[0],
                      })),
                    ]}
                    onChange={(event) => setFilterKeyword(event.target.value)}
                  />
                </div>
              </div>
            </div>

            <div className="rounded-[4px] bg-white p-4 text-black">
              <ApexChart
                key={JSON.stringify(filteredChartSeries)}
                className="box"
                type="bar"
                height={200}
                options={chartOptions}
                series={filteredChartSeries}
                style={{
                  cursor: 'pointer',
                }}
              />
            </div>

            <div className="flex flex-col justify-center gap-4 md:flex-row md:flex-wrap">
              {orderByReturnReason.map((reason, index) => (
                <div key={index} className="w-full rounded-[4px] bg-white md:w-[49%]">
                  <div
                    className={`flex items-center justify-between ${
                      reason.MONITORED ? 'bg-[#3e8ed0]' : 'bg-[#363636]'
                    }  p-[10px] text-white`}
                  >
                    <p className="text-xl font-bold">{reason.RETURN_REASON}</p>
                    <button
                      type="button"
                      className="flex cursor-pointer items-center justify-center gap-2"
                      onClick={() => showThisOrders(reason.ALL, reason.RETURN_REASON, 'All')}
                    >
                      <p
                        className="text-sm"
                        style={{
                          backgroundColor: '#ffe08a',
                          color: 'black',
                          padding: '8px',
                          borderRadius: '8px',
                        }}
                      >
                        {reason.TOTAL}
                      </p>
                      <p
                        className="text-sm"
                        style={{
                          backgroundColor: '#f5f5f5',
                          color: 'black',
                          padding: '8px',
                          borderRadius: '8px',
                        }}
                      >
                        {percentage(reason.TOTAL, filteredOrders.length)}
                      </p>
                    </button>
                  </div>
                  <div className="overflow-x-auto scrollbar">
                    <table className="min-w-full">
                      <thead>
                        <tr>
                          <td className="px-4 text-center text-sm">Status</td>
                          <td className="px-4 text-center text-sm text-[#48c78e]">Meet</td>
                          <td className="px-4 text-center text-sm text-[#f14699] ">Breached</td>
                          <td className="px-4 text-center text-sm text-[#ffe08a]">Jeopardy</td>
                          <td>Total</td>
                        </tr>
                      </thead>
                      <tbody className="text-black">
                        {reason.MONITORED ? (
                          <tr>
                            <td className="px-4 text-sm">Pending Resolved</td>
                            {/* eslint-disable-next-line */}
                            <td
                              className="cursor-pointer px-4 text-center text-sm"
                              title={percentage(
                                reason.PENDING_RESOLVED_MEET.length,
                                reason.PENDING_RESOLVED.length
                              )}
                              onClick={() =>
                                showThisOrders(
                                  reason.PENDING_RESOLVED_MEET,
                                  reason.RETURN_REASON,
                                  'Meet'
                                )
                              }
                            >
                              <p className="text-sm">{reason.PENDING_RESOLVED_MEET.length}</p>
                              <p className="text-sm">
                                {percentage(
                                  reason.PENDING_RESOLVED_MEET.length,
                                  reason.PENDING_RESOLVED.length
                                )}
                              </p>
                            </td>
                            {/* eslint-disable-next-line */}
                            <td
                              className="cursor-pointer px-4 text-center text-sm"
                              title={percentage(
                                reason.PENDING_RESOLVED_BREACHED.length,
                                reason.PENDING_RESOLVED.length
                              )}
                              onClick={() =>
                                showThisOrders(
                                  reason.PENDING_RESOLVED_BREACHED,
                                  reason.RETURN_REASON,
                                  'Breached'
                                )
                              }
                            >
                              <p className="text-sm">{reason.PENDING_RESOLVED_BREACHED.length}</p>
                              <p className="text-sm">
                                {percentage(
                                  reason.PENDING_RESOLVED_BREACHED.length,
                                  reason.PENDING_RESOLVED.length
                                )}
                              </p>
                            </td>
                            {/* eslint-disable-next-line */}
                            <td
                              className="cursor-pointer px-4 text-center text-sm"
                              title={percentage(
                                reason.PENDING_RESOLVED_JEOPARDY.length,
                                reason.PENDING_RESOLVED.length
                              )}
                              onClick={() =>
                                showThisOrders(
                                  reason.PENDING_RESOLVED_JEOPARDY,
                                  reason.RETURN_REASON,
                                  'In Jeopardy'
                                )
                              }
                            >
                              <p className="text-sm">{reason.PENDING_RESOLVED_JEOPARDY.length}</p>
                              <p className="text-sm">
                                {percentage(
                                  reason.PENDING_RESOLVED_JEOPARDY.length,
                                  reason.PENDING_RESOLVED.length
                                )}
                              </p>
                            </td>
                            {/* eslint-disable-next-line */}
                            <td
                              className="cursor-pointer px-4 text-center text-sm"
                              onClick={() =>
                                showThisOrders(reason.PENDING_RESOLVED, reason.RETURN_REASON, 'All')
                              }
                            >
                              <p className="text-sm">{reason.PENDING_RESOLVED.length}</p>
                            </td>
                          </tr>
                        ) : (
                          <tr>
                            <td className="px-4 text-sm">
                              Pending Resolved
                              <p className="text-xs">&nbsp;</p>
                            </td>
                            <td className="px-4 text-center text-sm text-gray-400" colSpan={3}>
                              Not monitored
                            </td>
                            {/* eslint-disable-next-line */}
                            <td
                              className="cursor-pointer px-4 text-center text-sm"
                              onClick={() =>
                                showThisOrders(reason.PENDING_RESOLVED, reason.RETURN_REASON, 'All')
                              }
                            >
                              <p className="text-xs">{reason.PENDING_RESOLVED.length}</p>
                            </td>
                          </tr>
                        )}
                        {reason.MONITORED ? (
                          <tr>
                            <td className="px-4 text-sm">Resolved</td>
                            {/* eslint-disable-next-line */}
                            <td
                              className="cursor-pointer px-4 text-center text-sm"
                              title={percentage(
                                reason.RESOLVED_MEET.length,
                                reason.RESOLVED.length
                              )}
                              onClick={() =>
                                showThisOrders(reason.RESOLVED_MEET, reason.RETURN_REASON, 'Meet')
                              }
                            >
                              <p className="text-xs">{reason.RESOLVED_MEET.length}</p>
                              <p className="text-xs">
                                {percentage(reason.RESOLVED_MEET.length, reason.RESOLVED.length)}
                              </p>
                            </td>
                            {/* eslint-disable-next-line */}
                            <td
                              className="cursor-pointer text-center text-sm"
                              title={percentage(
                                reason.RESOLVED_BREACHED.length,
                                reason.RESOLVED.length
                              )}
                              onClick={() =>
                                showThisOrders(
                                  reason.RESOLVED_BREACHED,
                                  reason.RETURN_REASON,
                                  'Breached'
                                )
                              }
                            >
                              <p className="text-xs">{reason.RESOLVED_BREACHED.length}</p>
                              <p className="text-xs">
                                {percentage(
                                  reason.RESOLVED_BREACHED.length,
                                  reason.RESOLVED.length
                                )}
                              </p>
                            </td>
                            {/* eslint-disable-next-line */}
                            <td
                              className="cursor-pointer text-center text-sm"
                              title={percentage(
                                reason.RESOLVED_JEOPARDY.length,
                                reason.RESOLVED.length
                              )}
                              onClick={() =>
                                showThisOrders(
                                  reason.RESOLVED_JEOPARDY,
                                  reason.RETURN_REASON,
                                  'In Jeopardy'
                                )
                              }
                            >
                              <p className="text-xs">{reason.RESOLVED_JEOPARDY.length}</p>
                              <p className="text-xs">
                                {percentage(
                                  reason.RESOLVED_JEOPARDY.length,
                                  reason.RESOLVED.length
                                )}
                              </p>
                            </td>
                            {/* eslint-disable-next-line */}
                            <td
                              className="cursor-pointer text-center text-sm"
                              onClick={() =>
                                showThisOrders(reason.RESOLVED, reason.RETURN_REASON, 'All')
                              }
                            >
                              <p className="text-xs">{reason.RESOLVED.length}</p>
                            </td>
                          </tr>
                        ) : (
                          <tr>
                            <td className="px-4 text-sm">
                              Resolved
                              <p className="text-xs">&nbsp;</p>
                            </td>
                            <td className="px-4 text-center text-sm text-gray-400" colSpan={3}>
                              Not monitored
                            </td>
                            {/* eslint-disable-next-line */}
                            <td
                              className="cursor-pointer px-4 text-center text-sm"
                              onClick={() =>
                                showThisOrders(
                                  reason.RESOLVED,
                                  reason.RETURN_REASON,
                                  'All (Resolved)'
                                )
                              }
                            >
                              <p className="text-xs">{reason.RESOLVED.length}</p>
                            </td>
                          </tr>
                        )}
                        <tr>
                          <td className="px-4 text-sm">
                            Suggest to Cancel
                            <p className="text-xs">&nbsp;</p>
                          </td>
                          <td className="px-4 text-sm" colSpan={3} />
                          {/* eslint-disable-next-line */}
                          <td
                            className="cursor-pointer px-4 text-center text-sm"
                            onClick={() =>
                              showThisOrders(
                                reason.TO_CANCEL,
                                reason.RETURN_REASON,
                                'Suggest to Cancel'
                              )
                            }
                          >
                            <p className="text-xs">{reason.TO_CANCEL.length}</p>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <Dialog open={dialogOpen} fullWidth maxWidth="xl">
            <DialogTitle>
              {showOrderTitle}
              <IconButton
                onClick={() => setDialogOpen(false)}
                sx={{
                  position: 'absolute',
                  right: 8,
                  top: 8,
                  color: (theme) => theme.palette.grey[500],
                }}
              >
                <Close />
              </IconButton>
            </DialogTitle>
            <DialogContent sx={{ padding: '16px' }}>
              <div className="flex flex-col gap-4">
                <div className="overflow-x-auto scrollbar">
                  <table className="min-w-full">
                    <thead>
                      <tr>
                        <td className={`${headCellStyle} w-[108px]`} rowSpan={2}>
                          Order #
                        </td>
                        <td className={`${headCellStyle}`} rowSpan={2}>
                          Customer
                        </td>
                        <td className={`${headCellStyle}`} rowSpan={2}>
                          Zone
                        </td>
                        <td className={`${headCellStyle} `} rowSpan={2}>
                          Exchange
                        </td>
                        <td className={`${headCellStyle}`} colSpan={6}>
                          Returned Activity
                        </td>
                      </tr>
                      <tr>
                        <td className={`${headCellStyle}`}>Return Reason</td>
                        <td className={`${headCellStyle}`}>Return Code</td>
                        <td className={`${headCellStyle} w-[108px]`}>Returned At</td>
                        <td className={`${headCellStyle} w-[88px]`}>Target At</td>
                        <td className={`${headCellStyle} w-[108px]`}>Resolved At</td>
                        <td className={`${headCellStyle}`}>Verdict</td>
                      </tr>
                    </thead>
                    <tbody className="text-xs">
                      {showOrders
                        .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                        .map((order, index) => (
                          <Fragment key={index}>
                            <tr>
                              <td
                                rowSpan={order.SETS.length + 1}
                                style={{
                                  border: '1px solid #999999',
                                  backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                  whiteSpace: 'nowrap',
                                }}
                              >
                                {order.ORDER_NUM}
                              </td>
                              <td
                                rowSpan={order.SETS.length + 1}
                                style={{
                                  border: '1px solid #999999',
                                  backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                }}
                              >
                                <div className="flex justify-center">
                                  <Image
                                    src={getLogo(order.ACCOUNT_NAME)}
                                    alt="Customer Logo"
                                    width={40}
                                    height={40}
                                  />
                                </div>
                              </td>
                              <td
                                rowSpan={order.SETS.length + 1}
                                style={{
                                  border: '1px solid #999999',
                                  backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                  whiteSpace: 'nowrap',
                                }}
                              >
                                {order.ZONE.replace('ZONE', '')}
                              </td>
                              <td
                                rowSpan={order.SETS.length + 1}
                                style={{
                                  border: '1px solid #999999',
                                  backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                  textAlign: 'center',
                                }}
                              >
                                {order.EXCHANGE}
                              </td>
                            </tr>
                            {order.SETS.map((set, i) => (
                              <tr key={`${index}_${i}`}>
                                <td
                                  style={{
                                    border: '1px solid #999999',
                                    backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                  }}
                                >
                                  <span>{set.RETURN_REASON.replace('Due to', '')}</span>
                                  &nbsp;
                                  {set.RETURN_REASON_ORI !== set.RETURN_REASON && (
                                    <span>
                                      <font-awesome-icon icon="question-circle" />
                                    </span>
                                  )}
                                </td>
                                <td
                                  style={{
                                    border: '1px solid #999999',
                                    backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                  }}
                                >
                                  {set.RETURN_CODE}
                                </td>
                                <td
                                  style={{
                                    border: '1px solid #999999',
                                    backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                    textAlign: 'center',
                                    whiteSpace: 'nowrap',
                                  }}
                                >
                                  {activityDateDisplay(set.RETURN_DATE)}
                                  &nbsp;
                                  {set.RETURN_COMMENTS && (
                                    <span>
                                      <font-awesome-icon icon="comment-alt" />
                                    </span>
                                  )}
                                </td>
                                <td
                                  style={{
                                    border: '1px solid #999999',
                                    backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                    textAlign: 'center',
                                    whiteSpace: 'nowrap',
                                  }}
                                >
                                  {activityDateDisplay(set.SLA_TARGET_AT)}
                                </td>
                                {set.MIR_STATUS ? (
                                  <td
                                    style={{
                                      border: '1px solid #999999',
                                      backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                      color: `${
                                        activityDateDisplay(set.MIR_DATE) === ''
                                          ? '#209cee'
                                          : undefined
                                      }`,
                                      textAlign: 'center',
                                    }}
                                  >
                                    {activityDateDisplay(set.MIR_DATE) || 'In Progress'}
                                    &nbsp;
                                    {set.MIR_COMMENTS && (
                                      <span>
                                        <font-awesome-icon
                                          icon="comment-alt"
                                          className="has-text-dark"
                                        />
                                      </span>
                                    )}
                                  </td>
                                ) : (
                                  <td>Activity Missing</td>
                                )}
                                <td
                                  style={{
                                    border: '1px solid #999999',
                                    textAlign: 'middle',
                                    whiteSpace: 'nowrap',
                                    ...getVerdictCellStyle(set.VERDICT),
                                  }}
                                >
                                  {set.VERDICT}
                                </td>
                              </tr>
                            ))}
                          </Fragment>
                        ))}
                    </tbody>
                  </table>
                </div>

                <TablePaginationCustom
                  count={showOrders.length}
                  page={page}
                  rowsPerPage={rowsPerPage}
                  onPageChange={(event, newPage) => setPage(newPage)}
                />
              </div>
            </DialogContent>
            <DialogActions>
              <div className="flex w-full items-center gap-2 p-4">
                <ExportExcelButton
                  key={showOrders?.length}
                  filename={`HSBA-ReturnOrder-${showOrderTitle.substring(0, 30)}-${moment().format(
                    'YYYY-MM-DD'
                  )}.xlsx`}
                  data={showOrders
                    .map((order) =>
                      order.SETS.map((set) => ({
                        ...{
                          ORDER_NUM: order.ORDER_NUM,
                          ACCOUNT_NAME: order.ACCOUNT_NAME,
                          CREATED_DATE: order.CREATED_DATE,
                          ORDER_TYPE: order.ORDER_TYPE,
                          ORDER_STATUS: order.ORDER_STATUS,
                          PRODUCT_NAME: order.PRODUCT_NAME,
                          CENTRAL: order.CENTRAL,
                          PTT: order.PTT,
                          ZONE: order.ZONE,
                          EXCHANGE: order.EXCHANGE,
                        },
                        ...set,
                      }))
                    )
                    .flat()}
                />
              </div>
            </DialogActions>
          </Dialog>
        </div>
      </div>
    </>
  );
}
