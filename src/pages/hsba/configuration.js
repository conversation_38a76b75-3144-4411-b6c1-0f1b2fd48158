// Next, React, Tw
import { useRouter } from 'next/router';
import { useEffect } from 'react';

// Mui
import { Tabs, Tab } from '@mui/material';

// Components
import Layout from '../../layouts/module/hsba';
import CustomerConfiguration from '../../components/hsba/CustomerConfiguration';
import ProductConfiguration from '../../components/hsba/ProductConfiguration';
import InterRegionRateConfiguration from '../../components/hsba/InterRegionRateConfiguration';
import CableReportRateConfiguration from '../../components/hsba/CableReportRateConfiguration';

// Others
import { useParamContext } from '../../utils/auth/ParamProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const router = useRouter();
  const { setParam } = useParamContext();
  const { tab } = router.query;

  useEffect(() => {
    if (!tab) router?.replace('/hsba/configuration?tab=0');
  }, []);

  return (
    <>
      <div
        style={{
          background: 'linear-gradient(to right bottom, #7957d5, #485fc7)',
          color: 'white',
          padding: '15px',
        }}
      >
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-bold">Configuration</p>
          </div>
        </div>
      </div>
      <div className="container mx-auto my-4 flex items-center">
        <Tabs
          value={tab}
          onChange={(event, newValue) => setParam({ tab: newValue })}
          centered
          className="text-black dark:text-white"
        >
          <Tab label="Customer" value="0" />
          <Tab label="Product" value="1" />
          <Tab label="Inter Region Rate" value="2" />
          <Tab label="Cable Report Rate" value="3" />
        </Tabs>
      </div>
      {tab === '0' && <CustomerConfiguration />}
      {tab === '1' && <ProductConfiguration />}
      {tab === '2' && <InterRegionRateConfiguration />}
      {tab === '3' && <CableReportRateConfiguration />}
    </>
  );
}
