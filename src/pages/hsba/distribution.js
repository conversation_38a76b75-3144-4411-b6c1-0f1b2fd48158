// Next, React, Tw
import { twMerge } from 'tailwind-merge';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Mui
import { Switch, Select, MenuItem, FormControl } from '@mui/material';

// Packages
import * as R from 'ramda';
import moment from 'moment';

// Components
import Layout from '../../layouts/module/hsba';
import DistributionPanel from '../../components/hsba/DistributionPanel';
import { DateInput } from '../../components/Shared/CustomInput';
import TringgerEngineButton from '../../components/hsba/TringgerEngineButton';

// Others
import { HSBA_ENDPOINT } from '../../utils/hsba';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useAuthContext } from '../../utils/auth/useAuthContext';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const dispatch = useDispatch();
  const { user } = useAuthContext();
  const [showBreakdown, setShowBreakdown] = useState(false);
  const [lowerSpeed, setLowerSpeed] = useState({ speed: 30, label: 'Lower' });
  const [upperSpeed, setUpperSpeed] = useState({ speed: 100, label: 'Upper' });
  const [dataSet, setDataSet] = useState([]);
  const [selectedCustomer, setSelectedCustomer] = useState('All');
  const [asOf, setAsOf] = useState('');
  const [selectedDate, setSelectedDate] = useState(moment().format('YYYY-MM-DD'));

  // Table
  const headerCellStyle = 'bg-white font-semibold text-sm text-center px-1';

  // Others

  const filteredDataSet = (() => {
    const sortByCount = R.sortWith([R.descend(R.pipe(R.pluck('count'), R.sum()))]);

    const temp = R.pipe(R.values(), R.flatten())(dataSet);

    if (selectedCustomer !== 'All')
      return R.pipe(
        R.filter(R.propEq('customer', selectedCustomer)),
        R.groupBy(R.prop('state')),
        R.values(),
        sortByCount
      )(temp);

    return R.pipe(R.groupBy(R.prop('state')), R.values(), sortByCount)(temp);
  })();

  const totalNationwide = R.pipe(
    R.values(),
    R.head(),
    R.values(),
    R.pluck('count'),
    R.sum()
  )(filteredDataSet);

  const customers = R.pipe(R.values(), R.flatten(), R.pluck('customer'), R.uniq())(dataSet);

  const customerSummary = R.pipe(
    R.prop('NATIONWIDE'),
    R.values(),
    R.groupBy(R.prop('customer')),
    R.map(R.pipe(R.pluck('count'), R.sum())),
    R.mapObjIndexed((o, key) => ({
      name: key,
      count: o,
    })),
    R.values(),
    R.sort(R.descend(R.prop('count')))
  )(dataSet);

  const totalSummary = R.pipe(R.prop('NATIONWIDE'), R.values(), R.pluck('count'), R.sum())(dataSet);

  const fetchData = async () => {
    dispatch(setIsLoading(true));

    try {
      const response = await axios.get(`${HSBA_ENDPOINT}/activeByState/${selectedDate}`);
      setDataSet(response.data[0].data);
      setAsOf(moment(response.data[0].updatedAt).format('YYYY-MM-DD, hh:mm A'));
    } catch {
      setDataSet([]);
      setAsOf('');
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [selectedDate]);

  return (
    <>
      <div
        style={{
          background: 'linear-gradient(to right bottom, #485fc7, #7957d5)',
          color: 'white',
          padding: '15px',
        }}
      >
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-bold">Speed Distribution (Active Subscriber)</p>
            <p className="text-xs">As of : {asOf}</p>
          </div>
          {user?.isSuperAdmin && <TringgerEngineButton process="distribution" payload={{}} />}
        </div>
      </div>
      <div className="bg-[#f5f5f5] text-black dark:bg-gray-800 dark:text-white">
        <div className="container flex flex-col justify-between gap-4 ">
          <div className="mx-auto flex w-full flex-col items-center justify-around gap-4 pt-6 md:flex-row">
            {customerSummary.map((customer, index) => (
              <div
                key={index}
                className="md:w-1/7 flex w-4/5 flex-col items-center justify-center rounded-lg bg-white p-2 text-black shadow-xl"
              >
                <p className="text-xs">{customer.name.split(' ')[0]}</p>
                <p className="text-l font-bold">{customer.count.toLocaleString()}</p>
              </div>
            ))}
            <div className="md:w-1/7 flex w-4/5 flex-col items-center justify-center rounded-lg bg-[#363636] p-2 text-white shadow-xl">
              <p className="text-xs">Total</p>
              <p className="text-l font-bold">{totalSummary.toLocaleString()}</p>
            </div>
          </div>
          <div className="mx-auto flex flex-col items-center justify-between gap-4 pt-1 md:flex-row md:gap-0">
            <div className="flex items-center justify-between gap-10">
              <div className="flex flex-col items-center justify-center">
                <p className="text-sm font-bold">Lower Speed (Mbps)</p>
                <div className="flex justify-center">
                  <button
                    type="button"
                    className="rounded-l-full bg-[#ff6600] px-2 py-1 font-bold text-white"
                    onClick={() => setLowerSpeed({ speed: lowerSpeed.speed - 5, label: 'Lower' })}
                  >
                    -
                  </button>
                  <input
                    className="bg-white text-center text-black"
                    type="number"
                    step="5"
                    size="small"
                    value={lowerSpeed.speed}
                    min="5"
                    max={upperSpeed.speed}
                    onChange={(event) => {
                      setLowerSpeed({ speed: event.target.value, label: 'Lower' });
                    }}
                  />
                  <button
                    type="button"
                    className="rounded-r-full bg-[#ff6600] px-2 py-1 font-bold text-white"
                    onClick={() => setLowerSpeed({ speed: lowerSpeed.speed + 5, label: 'Lower' })}
                  >
                    +
                  </button>
                </div>
              </div>
              <div className="flex flex-col items-center justify-center">
                <p className="text-sm font-bold">Upper Speed (Mbps)</p>
                <div className="flex justify-center">
                  <button
                    type="button"
                    className="rounded-l-full bg-[#ff6600] px-2 py-1 font-bold text-white"
                    onClick={() => setUpperSpeed({ speed: upperSpeed.speed - 5, label: 'Upper' })}
                  >
                    -
                  </button>
                  <input
                    className="bg-white text-center text-black"
                    type="number"
                    step="5"
                    size="small"
                    value={upperSpeed.speed}
                    min={lowerSpeed.speed}
                    max="1000"
                    onChange={(event) => {
                      setUpperSpeed({ speed: event.target.value, label: 'Upper' });
                    }}
                  />
                  <button
                    type="button"
                    className="rounded-r-full bg-[#ff6600] px-2 py-1 font-bold text-white"
                    onClick={() => setUpperSpeed({ speed: upperSpeed.speed + 5, label: 'Upper' })}
                  >
                    +
                  </button>
                </div>
              </div>
            </div>

            <div className="mx-8 md:flex-row">
              <p className="text-sm font-bold">Filter By</p>
              <FormControl size="small">
                <Select
                  value={selectedCustomer}
                  onChange={(event) => {
                    setSelectedCustomer(event.target.value);
                  }}
                  sx={{
                    width: '175px',
                    backgroundColor: 'white',
                  }}
                >
                  <MenuItem value="All">
                    <p className="text-xs">All</p>
                  </MenuItem>
                  {customers.map((customer) => (
                    <MenuItem key={customer} value={customer}>
                      <p className="text-xs">{customer.replace('(WSE LABUAN)', '')}</p>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </div>
            <div className="mx-4">
              <DateInput
                value={selectedDate}
                placeholder="As of"
                returnedFormat="YYYY-MM-DD"
                onChange={(event) => {
                  setSelectedDate(event?.target?.value);
                }}
              />
            </div>
            <div className="mx-4">
              <p className="text-sm font-bold">Breakdown</p>

              <Switch
                checked={showBreakdown}
                onChange={(event) => setShowBreakdown(event.target.checked)}
              />
            </div>
          </div>
          <div className="overflow-x-auto scrollbar">
            <table className="min-w-full text-black">
              <thead>
                <tr>
                  <td rowSpan={2} className={headerCellStyle}>
                    <p>State</p>
                  </td>
                  <td rowSpan={2} className={twMerge(headerCellStyle, 'bg-[#363636] text-white')}>
                    <p>Total</p>
                  </td>
                  <td colSpan={4} className={twMerge(headerCellStyle, 'bg-[#3e8ed0] text-white')}>
                    <p>Residential</p>
                  </td>
                  <td colSpan={4} className={twMerge(headerCellStyle, 'bg-[#ffe08a] ')}>
                    <p>Business</p>
                  </td>
                </tr>
                <tr>
                  <td className={twMerge(headerCellStyle, 'bg-white')}>
                    <p>Lower</p>{' '}
                    <div className="rounded-[7px] bg-[#7957d5] p-[3px] text-white">
                      &lt;= {lowerSpeed.speed}Mbps
                    </div>
                  </td>
                  <td className={headerCellStyle}>
                    <p>Middle</p>
                  </td>
                  <td className={headerCellStyle}>
                    <p>Upper</p>{' '}
                    <div className="rounded-[7px] bg-[#ff6600] p-[3px] text-white">
                      &gt;= {upperSpeed.speed}Mbps
                    </div>
                  </td>
                  <td className={twMerge(headerCellStyle, 'bg-[#363636] text-white')}>
                    <p>Total</p>
                  </td>
                  <td className={headerCellStyle}>
                    <p>Lower</p>{' '}
                    <div className="rounded-[7px] bg-[#7957d5] p-[3px] text-white">
                      &lt;= {lowerSpeed.speed}Mbps
                    </div>
                  </td>
                  <td className={headerCellStyle}>
                    <p>Middle</p>
                  </td>
                  <td className={headerCellStyle}>
                    <p>Upper</p>{' '}
                    <div className="rounded-[7px] bg-[#ff6600] p-[3px] text-white">
                      &gt;= {upperSpeed.speed}Mbps
                    </div>
                  </td>
                  <td className={twMerge(headerCellStyle, 'bg-[#363636] text-white')}>
                    <p>Total</p>
                  </td>
                </tr>
              </thead>
              {filteredDataSet.length > 0 &&
                filteredDataSet.map((stateGroup, state) => (
                  <DistributionPanel
                    key={state}
                    bandwidths={stateGroup}
                    lowerSpeed={lowerSpeed.speed}
                    upperSpeed={upperSpeed.speed}
                    showBreakdown={showBreakdown}
                    totalNationwide={totalNationwide}
                  />
                ))}
            </table>
          </div>
        </div>
      </div>
    </>
  );
}
