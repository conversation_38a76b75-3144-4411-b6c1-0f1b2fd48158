// Next, React, Tw
import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Components
import Layout from '../../layouts/module/hsba';
import EmptyPageWithLogoAndTmSong from '../../components/Shared/EmptyPageWithLogoAndTmSong';

// Others
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { replace } = useRouter();
  const { isAdmin } = useModuleRoleContext();
  const { userSubModules } = useSelector((state) => state.aum);

  useEffect(() => {
    if (isAdmin) {
      replace('/hsba/management-view');
      return;
    }
    if (userSubModules?.length === 0) return;
    if (userSubModules?.includes('management-view')) {
      replace('/hsba/management-view');
      return;
    }
    replace(`/hsba/${userSubModules?.[0]}`);
  }, [isAdmin, userSubModules?.length]);
  return <EmptyPageWithLogoAndTmSong />;
}
