// Next, React, Tw
import Image from 'next/image';
import { twMerge } from 'tailwind-merge';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';

// Mui
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  Tabs,
  Tab,
} from '@mui/material';

// Packages
import moment from 'moment';
import { json2csv } from 'json-2-csv';

// Components
import Layout from '../../layouts/module/hsba';
import { HSBA_ENDPOINT, getLogo } from '../../utils/hsba';
import { TextInput, SelectInput, DateInput } from '../../components/Shared/CustomInput';
import ReactAnimatedNumber from '../../components/Shared/ReactAnimatedNumber';
import UserPopup from '../../components/Shared/UserPopup';

// Others
import axios from '../../utils/axios';
import { useSnackbar } from '../../components/Shared/snackbar';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { toUpperCaseFirstLetter } from '../../utils/shared';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { user } = useAuthContext();
  const { query } = useRouter();
  const { enqueueSnackbar } = useSnackbar();
  const { setParam, replaceParam } = useParamContext();
  const { month } = query;
  const dispatch = useDispatch();

  const [interRegionData, setInterRegionData] = useState([]);
  const [interRegionRequestData, setInterRegionRequestData] = useState([]);
  const [tab, setTab] = useState(0);

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [, setEditModeDialog] = useState(false);
  const DIALOG_DATA = {
    month: moment().subtract(1, 'months').format('YYYY-MM'),
    customer: null,
    data: null,
    voice: null,
    iptv: null,
    last_created_by: user?.staff_id,
    status: 'pending',
  };
  const [dialogData, setDialogData] = useState(DIALOG_DATA);
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  const handleDialogClose = async (action) => {
    if (action) {
      dialogData.data = Number(dialogData.data);
      dialogData.voice = Number(dialogData.voice);
      dialogData.iptv = Number(dialogData.iptv);

      try {
        const response = await axios.post(
          `${HSBA_ENDPOINT}/interRegion/request/${dialogData.month}`,
          dialogData
        );
        if (response.data.status === 'success') {
          enqueueSnackbar('Created', {
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'center',
            },
          });
        }
      } catch (error) {
        // console.log(error);

        enqueueSnackbar('Failed', {
          variant: 'error',
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
      }
    }
    setDialogData(DIALOG_DATA);
    setDialogOpen(false);
    fetchData2();
  };

  // Others

  const getHeaderCellStyle = () =>
    'whitespace-nowrap bg-[#408dd0] py-1 text-center text-sm text-white';
  const getBodyCellStyle = () => 'text-center text-black whitespace-nowrap text-xs';

  const handleDownloadButtonClick = async (interRegionId, customerName) => {
    dispatch(setIsLoading(true));
    let response;
    try {
      response = await axios.get(`${HSBA_ENDPOINT}/interRegion/id/${interRegionId}`);
    } catch (error) {
      // console.log(error);
    }
    dispatch(setIsLoading(false));

    let temp = [];
    for (let i = 0; i < response.data.length; i += 1) {
      temp = [...temp, ...response?.data[i]?.data];
    }
    const blob = new Blob([(await json2csv(temp))?.replaceAll('undefined', '')], {
      type: 'text/csv',
    });
    const a = document.createElement('a');
    a.download = `Inter-Region-${customerName?.split(' ')[0]}-${moment().format('YYYY-MM')}.csv`;
    a.href = window.URL.createObjectURL(blob);
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${HSBA_ENDPOINT}/interRegion/month/${month}`);
      setInterRegionData(response.data);
    } catch (error) {
      // console.log(error);
    }
    dispatch(setIsLoading(false));
  };

  const fetchData2 = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${HSBA_ENDPOINT}/interRegion/request/${month}`);
      setInterRegionRequestData(response?.data);
    } catch (error) {
      // console.log(error);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (!month) {
      replaceParam({ month: moment().subtract(1, 'months').format('YYYY-MM') });
      return;
    }
    if (tab === 0) {
      fetchData();
      return;
    }
    fetchData2();
  }, [tab, month]);

  return (
    <>
      <div
        style={{
          background: 'linear-gradient(to right bottom, #7957d5, #485fc7)',
          color: 'white',
          padding: '15px',
        }}
      >
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-bold">Inter-Region</p>
          </div>
        </div>
      </div>
      <div className="container flex flex-col gap-4 p-4">
        <div className="flex flex-row-reverse items-center justify-between">
          <div className="w-[250px]">
            <DateInput
              views={['year', 'month']}
              value={month}
              placeholder="Month"
              returnedFormat="YYYY-MM"
              onChange={(event) => setParam({ month: event.target.value })}
            />
          </div>
          {user?.isSuperAdmin && (
            <Tabs
              value={tab}
              onChange={(event, newValue) => setTab(newValue)}
              centered
              className="text-black dark:text-white"
            >
              <Tab label="Report" value={0} />
              <Tab label="Request Logs" value={1} />
            </Tabs>
          )}
        </div>

        <div className={`${tab === 0 ? 'block' : 'hidden'}`}>
          <div
            style={{
              boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)',
            }}
            className=" rounded-xl bg-white p-4"
          >
            <div className="flex flex-col">
              {interRegionData.map((data, index) => (
                <div key={index} className="mb-8 flex flex-col gap-4">
                  <div className="flex flex-col items-center justify-between gap-4 md:flex-row md:gap-0">
                    <div className="flex flex-col items-center gap-5 md:flex-row">
                      <Image
                        src={getLogo(data.customer)}
                        alt="Customer Logo"
                        width={70}
                        height={70}
                      />

                      <div className="flex items-center gap-1">
                        <p className="text-lg font-bold text-black">RM</p>{' '}
                        <ReactAnimatedNumber
                          value={data.total_charge}
                          style={{
                            fontSize: '1.17rem',
                            fontWeight: 'bold',
                            color: 'black',
                          }}
                          formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                        />
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <p className="text-xs font-semibold">
                        Last Updated at : {moment(data.updatedAt).format('YYYY-MM-DD, hh:mm A')}
                      </p>
                      <button
                        type="button"
                        onClick={() => handleDownloadButtonClick(String(data._id), data.customer)}
                      >
                        <Image
                          src="/assets/icons/csv-logo.svg"
                          alt="CSV Icon"
                          width={25}
                          height={25}
                        />
                      </button>
                    </div>
                  </div>

                  <div className="overflow-x-auto scrollbar">
                    <table className="min-w-full">
                      <thead>
                        <tr>
                          {[
                            'Type',
                            'Service Gateway Bandwidth / Mbps',
                            'Nationwide Bandwidth / Mbps',
                            'Contention Ratio',
                          ].map((label, i) => (
                            <td key={i} className={getHeaderCellStyle()}>
                              {label}
                            </td>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {data.details.map((row, i) => (
                          <tr key={i}>
                            <td className={getBodyCellStyle()}>{row.type}</td>
                            <td className={getBodyCellStyle()}>
                              {row?.serviceGateway?.toLocaleString('en-US')}
                            </td>
                            <td className={getBodyCellStyle()}>
                              {row?.bandwidth?.toLocaleString('en-US')}
                            </td>
                            <td className={getBodyCellStyle()}>
                              {`${row?.ratio === null ? '-' : row?.ratio}`}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  {index !== interRegionData.length - 1 && <Divider />}
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className={`${tab === 1 ? 'block' : 'hidden'}`}>
          <div className="my-4 flex flex-col items-center justify-end gap-4 md:flex-row md:gap-0">
            <button
              type="button"
              className="rounded-[4px] bg-green-600 px-4 py-2 text-xs font-semibold text-white"
              onClick={() => handleClickOpenDialog(false)}
            >
              Generate New
            </button>
          </div>

          <div
            style={{ boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)' }}
            className="rounded-xl bg-white p-4"
          >
            <div className="flex flex-col">
              <div className="overflow-x-auto scrollbar">
                <table className="min-w-full">
                  <thead>
                    <tr>
                      {['Customer', 'Data', 'Voice', 'IPTV', 'Last Created By', 'Status'].map(
                        (label, i) => (
                          <td key={i} className={getHeaderCellStyle()}>
                            {label}
                          </td>
                        )
                      )}
                    </tr>
                  </thead>
                  <tbody>
                    {interRegionRequestData.map((row, i) => (
                      <tr key={i}>
                        <td className={getBodyCellStyle()}>{row?.customer}</td>
                        <td className={getBodyCellStyle()}>{row?.data?.toLocaleString('en-US')}</td>
                        <td className={getBodyCellStyle()}>
                          {row?.voice?.toLocaleString('en-US')}
                        </td>
                        <td className={getBodyCellStyle()}>{row?.iptv?.toLocaleString('en-US')}</td>
                        <td className={twMerge(getBodyCellStyle(), 'cursor-pointer')}>
                          <UserPopup
                            label={row.last_created_by.toUpperCase()}
                            staff_id={row.last_created_by}
                          />
                        </td>
                        <td className={getBodyCellStyle()}>{toUpperCaseFirstLetter(row.status)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle>Generate New Report</DialogTitle>
        <DialogContent>
          <div className="flex flex-col gap-4 p-2">
            <SelectInput
              name="customer"
              value={dialogData?.customer}
              placeholder="Customer"
              options={[
                'MAXIS BROADBAND SDN. BHD.',
                'CELCOM MOBILE SDN. BHD.',
                'OHANA COMMUNICATIONS SDN BHD',
                'DIGI TELECOMMUNICATIONS SDN. BHD.',
                'REDTONE TELECOMMUNICATIONS SDN BHD (WSE LABUAN)',
                'MEASAT BROADCAST NETWORK SYSTEMS SDN BHD',
              ]}
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="data"
              value={dialogData?.data}
              placeholder="Data / Mbps"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="voice"
              value={dialogData?.voice}
              placeholder="Voice / Mbps"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="iptv"
              value={dialogData?.iptv}
              placeholder="IPTV / Mbps"
              onChange={handleDialogDataChange}
            />
            <div className="flex w-full items-center justify-between gap-2">
              <DateInput
                views={['month']}
                name="month"
                value={dialogData?.month}
                placeholder="Month"
                returnedFormat="YYYY-MM"
                onChange={handleDialogDataChange}
              />
            </div>
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex justify-end gap-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
              Cancel
            </button>
            <button
              type="button"
              onClick={() => handleDialogClose(true)}
              className="rounded-[4px] bg-green-500 p-2 font-semibold text-white"
            >
              Submit
            </button>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
}
