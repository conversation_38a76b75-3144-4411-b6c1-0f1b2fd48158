// Next, React, Tw
import { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/router';
import { twMerge } from 'tailwind-merge';
import { useDispatch } from 'react-redux';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions, Divider } from '@mui/material';

// Packages
import moment from 'moment';
import * as yup from 'yup';

// Components
import { useSnackbar } from '../../components/Shared/snackbar';
import Layout from '../../layouts/module/hsba';
import { TablePaginationCustom } from '../../components/Shared/table';
import ExportExcelButton from '../../components/Shared/ExportExcelButton';
import ReactAnimatedNumber from '../../components/Shared/ReactAnimatedNumber';
import {
  TextInput,
  SelectInput,
  DateInput,
  SearchInput,
} from '../../components/Shared/CustomInput';

// Others
import { HSBA_ENDPOINT } from '../../utils/hsba';
import axios from '../../utils/axios';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { checkAndReplaceNumberWithZero } from '../../utils/shared';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { enqueueSnackbar } = useSnackbar();
  const { replace, query } = useRouter();
  const { q, date, selectedCustomer } = query;
  const { setParam } = useParamContext();
  const dispatch = useDispatch();

  const TODAY_DATE_IN_UNIX = moment().startOf('day').unix();
  const [tableDataFrom, setTableDataFrom] = useState(TODAY_DATE_IN_UNIX);
  const [tableDataTo, setTableDataTo] = useState(TODAY_DATE_IN_UNIX);
  const [customerList, setCustomerList] = useState([]);
  const [btuStockUtilizationData, setBtuStockUtilizationData] = useState({});
  const [btuStockUtilizationByCustomerData, setBtuStockUtilizationByCustomerData] = useState([]);

  // Table
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(20);
  const [tableData, setTableData] = useState([]);

  const bodyCellStyle = 'text-center py-1 text-xs';

  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  })();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialog2Open, setDialog2Open] = useState(false);
  const MESSAGE = 'Please provide';
  const schema = yup.object({
    date: yup?.number().required(`${MESSAGE} date`),
    btu_en_route: yup
      ?.number()
      .required(`${MESSAGE} btu en route`)
      .transform((value) => Number(value)),
    btu_contract: yup
      ?.number()
      .required(`${MESSAGE} btu contract`)
      .transform((value) => Number(value)),
    btu_released: yup
      ?.number()
      .required(`${MESSAGE} btu released`)
      .transform((value) => Number(value)),
  });
  const schema2 = yup.object({
    date: yup?.number().required(`${MESSAGE} date`),
    customer: yup?.string().required(`${MESSAGE} customer`),
    btu_utilized: yup
      ?.number()
      .required(`${MESSAGE} btu utilized`)
      .transform((value) => Number(value)),
  });
  const [dialogData, setDialogData] = useState({});
  const [dialog2Data, setDialog2Data] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value?.trim(),
    }));
  };
  const handleDialogData2Change = (event) => {
    const { name, value } = event.target;
    setDialog2Data((prevValues) => ({
      ...prevValues,
      [name]: value?.trim(),
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }
    setDialogOpen(true);
  };
  const handleClickOpenDialog2 = (editMode, data) => {
    if (editMode) {
      setDialog2Data(data);
    }
    setDialog2Open(true);
  };
  const handleDialogClose = async (action) => {
    if (action) {
      let payload = { ...dialogData, date };
      try {
        payload = await schema.validate(payload, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }
      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${HSBA_ENDPOINT}/btuStockUtilization`, payload);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = 'Success';
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        /* empty */
      }
    }

    setDialogData({});
    setDialogOpen(false);
    fetchData();
  };

  const handleDialog2Close = async (action) => {
    if (action) {
      let payload = { ...dialog2Data, customer: selectedCustomer, date };
      try {
        payload = await schema2.validate(payload, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
        return;
      }
      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${HSBA_ENDPOINT}/btuStockUtilizationByCustomer`, payload);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = 'Success';
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        /* empty */
      }
    }

    setDialog2Data({});
    setDialog2Open(false);
    fetchData();
  };

  // Others
  const filteredBtuStockUtilizationByCustomerData = (() => {
    if (selectedCustomer === 'all') {
      return btuStockUtilizationByCustomerData;
    }
    return btuStockUtilizationByCustomerData.filter((o) => o?.customer === selectedCustomer);
  })();

  const utilizedNumber = filteredBtuStockUtilizationByCustomerData?.reduce(
    (accumulator, o) => accumulator + o.btu_utilized,
    0
  );

  const memoizedComponent = useMemo(
    () => (
      <div className="flex flex-col items-center gap-2 px-8 py-4 md:items-start">
        <div className="flex w-full justify-center gap-2">
          <div className="flex w-[150px] flex-col items-center gap-2 rounded-xl bg-green-500 px-4 py-2 text-white shadow-dashboardCard dark:shadow-dashboardCardDark md:w-[200px]">
            <p className="text-4xl">
              <ReactAnimatedNumber value={checkAndReplaceNumberWithZero(utilizedNumber)} />
            </p>
            <p className="text-xs">Utilized</p>
          </div>
        </div>
        <div className="flex w-full justify-center gap-2">
          <div className="flex w-[150px] flex-col items-center gap-2 rounded-xl bg-orange-500 px-4 py-2 text-white shadow-dashboardCard dark:shadow-dashboardCardDark md:w-[200px]">
            <p className="text-2xl md:text-4xl">
              <ReactAnimatedNumber
                value={checkAndReplaceNumberWithZero(
                  btuStockUtilizationData?.btu_released - utilizedNumber
                )}
              />
            </p>
            <p className="text-xs">Utilized Balance</p>
          </div>
          <div className="flex w-[150px] flex-col items-center gap-2 rounded-xl bg-orange-500 px-4 py-2 text-white shadow-dashboardCard dark:shadow-dashboardCardDark md:w-[200px]">
            <p className="text-2xl md:text-4xl">
              <ReactAnimatedNumber
                value={checkAndReplaceNumberWithZero(btuStockUtilizationData?.btu_en_route)}
              />
            </p>
            <p className="text-xs">BTU en route</p>
          </div>
        </div>
        <div className="flex w-full justify-center gap-2">
          <div className="flex w-[150px] flex-col items-center gap-2 rounded-xl bg-blue-900 px-4 py-2 text-white shadow-dashboardCard dark:shadow-dashboardCardDark md:w-[200px]">
            <p className="text-2xl md:text-4xl">
              <ReactAnimatedNumber
                value={checkAndReplaceNumberWithZero(btuStockUtilizationData?.btu_contract)}
              />
            </p>
            <p className="text-xs">
              Total BTU <br /> (Contract)
            </p>
          </div>
          <div className="flex w-[150px] flex-col items-center gap-2 rounded-xl bg-blue-900 px-4 py-2 text-white shadow-dashboardCard dark:shadow-dashboardCardDark md:w-[200px]">
            <p className="text-2xl md:text-4xl">
              <ReactAnimatedNumber
                value={checkAndReplaceNumberWithZero(btuStockUtilizationData?.btu_released)}
              />
            </p>
            <p className="text-xs">
              Total BTU <br /> (Released)
            </p>
          </div>
          <div className="flex w-[150px] flex-col items-center gap-2 rounded-xl bg-blue-900 px-4 py-2 text-white shadow-dashboardCard dark:shadow-dashboardCardDark md:w-[200px]">
            <p className="text-2xl md:text-4xl">
              <ReactAnimatedNumber
                value={checkAndReplaceNumberWithZero(
                  btuStockUtilizationData?.btu_contract - btuStockUtilizationData?.btu_released
                )}
              />
            </p>
            <p className="text-xs">
              Total BTU <br /> (Balance)
            </p>
          </div>
        </div>
      </div>
    ),
    [JSON?.stringify(utilizedNumber), JSON?.stringify(btuStockUtilizationData)]
  );

  const fetchCustomerList = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${HSBA_ENDPOINT}/customer`);
      if (response?.status === 200) {
        setCustomerList(response?.data);
      }
    } catch (error) {
      //   console.log(error);
    }
    dispatch(setIsLoading(false));
  };

  const fetchTableData = async () => {
    if (Number(tableDataFrom) > Number(tableDataTo)) {
      enqueueSnackbar('Invalid Date Input', {
        variant: 'error',
        anchorOrigin: {
          vertical: 'top',
          horizontal: 'center',
        },
      });
      return;
    }
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(
        `${HSBA_ENDPOINT}/btuStockUtilizationByCustomer?from=${tableDataFrom}&to=${tableDataTo}`
      );
      if (response?.status === 200) {
        setTableData(response?.data);
      }
    } catch {
      /* empty */
    }
    dispatch(setIsLoading(false));
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${HSBA_ENDPOINT}/btuStockUtilization/${date}`);

      if (response?.status === 200) {
        if (date === 'latest') {
          setParam({ date: response?.data[0]?.date });
        } else {
          setBtuStockUtilizationData(response?.data[0]);
        }
      }
    } catch (error) {
      // console.log(error);
    }
    try {
      const response2 = await axios.get(
        `${HSBA_ENDPOINT}/btuStockUtilizationByCustomer?from=${date}&to=${date}`
      );
      if (response2?.status === 200) {
        setBtuStockUtilizationByCustomerData(response2?.data);
      }
    } catch (error) {
      // console.log(error);
    }
    dispatch(setIsLoading(false));
  };

  const handleQueryUndefined = () => {
    replace(`/hsba/btu-stock-utilization?date=latest&selectedCustomer=all`);
  };

  useEffect(() => {
    if (!date && !selectedCustomer) {
      handleQueryUndefined();
      return;
    }
    fetchData();
    fetchCustomerList();
    fetchTableData();
  }, [date]);

  return (
    <>
      <div
        style={{
          background: 'linear-gradient(to right bottom, #485fc7, #7957d5)',
          color: 'white',
          padding: '15px',
        }}
        className="w-full"
      >
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-bold">BTU Stock Utilization</p>
          </div>
        </div>
      </div>
      <div className="container mx-auto p-1 text-black dark:text-white md:p-4">
        <div className="flex w-full flex-col items-center gap-2 p-4 md:flex-row md:justify-between">
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-2">
              <div className="w-[175px]">
                <DateInput
                  value={Number(date)}
                  placeholder="Date"
                  returnedFormat="unix"
                  onChange={(event) => {
                    setParam({
                      date: event?.target?.value,
                    });
                  }}
                />
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-[175px]">
                <SelectInput
                  value={selectedCustomer}
                  placeholder="Customer"
                  options={[
                    'all',
                    ...customerList
                      ?.filter(
                        (o) =>
                          !['Webe', 'CELCOM MOBILE SDN BHD', 'CELCOMDIGI BERHAD'].includes(o?.name)
                      )
                      .map((o, i) => o?.name),
                  ]}
                  onChange={(event) => {
                    setParam({ selectedCustomer: event.target.value });
                  }}
                />
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {selectedCustomer !== 'all' && (
              <button
                type="button"
                onClick={() =>
                  handleClickOpenDialog2(true, filteredBtuStockUtilizationByCustomerData[0])
                }
                className="cta-btn bg-hsba"
              >
                Edit Customer Utilization
              </button>
            )}

            <button
              type="button"
              onClick={() => handleClickOpenDialog(true, btuStockUtilizationData)}
              className="cta-btn bg-hsba"
            >
              Edit BTU Details
            </button>
          </div>
        </div>
        <div
          style={{ boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)' }}
          className=" flex flex-col gap-4 rounded-xl bg-white px-4 dark:bg-gray-600"
        >
          {memoizedComponent}
          <Divider flexItem />
          <div className="flex flex-col items-center justify-between gap-2 md:flex-row">
            <div className="flex flex-col items-center gap-2 md:flex-row">
              <DateInput
                views={['year', 'month', 'day']}
                value={tableDataFrom}
                returnedFormat="unix"
                placeholder="From"
                onChange={(event) => {
                  setTableDataFrom(event?.target?.value);
                }}
              />
              <DateInput
                views={['year', 'month', 'day']}
                value={tableDataTo}
                returnedFormat="unix"
                placeholder="To"
                onChange={(event) => {
                  setTableDataTo(event?.target?.value);
                }}
              />

              <button type="button" onClick={() => fetchTableData()} className="cta-btn bg-hsba">
                Search
              </button>
            </div>
            <div className="flex items-center gap-2">
              <SearchInput />
              <ExportExcelButton
                key={JSON?.stringify(filteredTableData)}
                data={filteredTableData}
                filename="export.csv"
              />
            </div>
          </div>

          <div className="overflow-x-auto scrollbar">
            <table className="min-w-full bg-white text-black">
              <thead>
                <tr>
                  {['No.', 'Date', 'Customer', 'BTU Utilized', 'Updated At'].map((label, i) => (
                    <td key={i} className="bg-hsba whitespace-nowrap px-4 text-center text-white">
                      {label}
                    </td>
                  ))}
                </tr>
              </thead>
              <tbody>
                {filteredTableData.map((row, i) => (
                  <tr key={i} className={`${`${i % 2 === 0 ? 'bg-[#f8f8f8]' : 'bg-white'}`}`}>
                    <td className={bodyCellStyle}>{i + 1 + rowsPerPage * page}</td>
                    <td className={bodyCellStyle}>{moment.unix(row?.date).format('YYYY-MM-DD')}</td>
                    <td className={bodyCellStyle}>{row?.customer}</td>
                    <td className={bodyCellStyle}>{row?.btu_utilized}</td>
                    <td className={bodyCellStyle}>
                      {moment(row?.updatedAt)?.format('YYYY-MM-DD, hh:mm A')}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <TablePaginationCustom
            count={tableData.length}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={(event, newPage) => {
              setPage(newPage);
            }}
          />
        </div>
      </div>

      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle>Overall BTU Utilization</DialogTitle>
        <DialogContent>
          <div className="flex w-full flex-col gap-2 p-2 md:w-[400px]">
            <TextInput
              name="btu_en_route"
              value={dialogData?.btu_en_route}
              placeholder="Total BTU En Route"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="btu_contract"
              value={dialogData?.btu_contract}
              placeholder="Total BTU (Contract)"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="btu_released"
              value={dialogData?.btu_released}
              placeholder="Total BTU (Released)"
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex justify-end gap-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
              Cancel
            </button>
            <button
              type="button"
              onClick={() => handleDialogClose('post')}
              className="bg-green-500 p-2 text-white"
            >
              Save
            </button>
          </div>
        </DialogActions>
      </Dialog>

      <Dialog open={dialog2Open} onClose={() => handleDialog2Close(false)}>
        <DialogTitle>{selectedCustomer} Utilization</DialogTitle>
        <DialogContent>
          <div className="flex flex-col gap-2 p-2">
            <div className="relative h-10 w-full min-w-[400px]">
              <input
                className={twMerge(
                  `peer h-full w-full rounded-lg border  bg-transparent px-3 py-2.5 text-xs outline outline-0 transition-all placeholder-shown:border focus:border-2 focus:border-[#ff7b7b] focus:border-t-transparent focus:outline-0 disabled:border-2 disabled:border-t-transparent disabled:outline-0`,
                  `${dialog2Data?.btu_utilized !== undefined ? 'border-t-transparent' : ''}`
                )}
                name="btu_utilized"
                value={dialog2Data?.btu_utilized}
                onChange={handleDialogData2Change}
                placeholder=" "
                autoComplete="off"
              />
              <p
                className={`before:content[' '] after:content[' '] pointer-events-none absolute -top-1.5 left-0 flex h-full w-full select-none text-xs font-normal leading-tight transition-all before:pointer-events-none before:mr-1 before:mt-[6.5px] before:box-border before:block before:h-1.5 before:w-2.5 before:rounded-tl-md before:border-l before:border-t before:transition-all after:pointer-events-none after:ml-1 after:mt-[6.5px] after:box-border after:block after:h-1.5 after:w-2.5 after:flex-grow after:rounded-tr-md after:border-r after:border-t after:transition-all peer-placeholder-shown:text-sm peer-placeholder-shown:leading-[3.75] peer-placeholder-shown:before:border-transparent peer-placeholder-shown:after:border-transparent peer-focus:text-[11px] peer-focus:leading-tight peer-focus:text-[#ff7b7b] peer-focus:before:border-l-2 peer-focus:before:border-t-2 peer-focus:before:border-[#ff7b7b] peer-focus:after:border-r-2 peer-focus:after:border-t-2 peer-focus:after:border-[#ff7b7b]`}
              >
                Total Utilization
              </p>
            </div>
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex justify-end gap-4">
            <button type="button" onClick={() => handleDialog2Close(false)} className="p-2">
              Cancel
            </button>
            <button
              type="button"
              onClick={() => handleDialog2Close('post')}
              className="bg-green-500 p-2 text-white"
            >
              Save
            </button>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
}
