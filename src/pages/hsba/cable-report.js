// Next, React, Tw
import Image from 'next/image';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';

// Packages
import * as R from 'ramda';
import moment from 'moment';

// Components
import Layout from '../../layouts/module/hsba';
import ExportExcelButton from '../../components/Shared/ExportExcelButton';
import { getLogo, HSBA_ENDPOINT } from '../../utils/hsba';
import ReactAnimatedNumber from '../../components/Shared/ReactAnimatedNumber';
import { DateInput } from '../../components/Shared/CustomInput';
import TringgerEngineButton from '../../components/hsba/TringgerEngineButton';

// Others
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { useAuthContext } from '../../utils/auth/useAuthContext';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { month } = query;
  const { replaceParam, setParam } = useParamContext();
  const { user } = useAuthContext();

  const [asOf, setAsOf] = useState('');
  const [cardsData, setCardsData] = useState([]);
  const [csvData, setCsvData] = useState([]);

  // Others
  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${HSBA_ENDPOINT}/cableReport/${month}`);

      setCardsData(
        R.pipe(
          R.toPairs,
          R.map(([key, value]) => ({ ...value, name: key }))
        )(response.data[0].summary)
      );
      setCsvData(response.data[0].data);
      setAsOf(moment(response.data[0].updatedAt).format('YYYY-MM-DD, hh:mm A'));
    } catch (error) {
      setCsvData([]);
      setAsOf('');
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (!month) {
      replaceParam({ month: moment().format('YYYY-MM') });
      return;
    }
    fetchData();
  }, [month]);

  return (
    <>
      <div
        style={{
          background: 'linear-gradient(to right bottom, #7957d5, #485fc7)',
          color: 'white',
          padding: '15px',
        }}
      >
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-bold">Cable Report</p>
            <p className="text-xs">As of : {asOf}</p>
          </div>
          {user?.isSuperAdmin && <TringgerEngineButton process="cable_report" payload={{}} />}
        </div>
      </div>
      <div className="container flex flex-col gap-4 p-4">
        <div className=" text-black dark:text-white ">
          <div className="flex items-center justify-between">
            <div className="w-[250px]">
              <DateInput
                views={['month']}
                value={month}
                placeholder="Month"
                returnedFormat="YYYY-MM"
                onChange={(event) => setParam({ month: event.target.value })}
              />
            </div>
            {csvData?.length > 0 && (
              <ExportExcelButton data={csvData} filename={`cable-report-${month}.csv`} />
            )}
          </div>

          <div className="my-8 flex flex-col justify-center gap-2 md:flex-row md:flex-wrap">
            {cardsData.map((data, index) => (
              <div className="flex w-full flex-col justify-end gap-4 rounded-[4px] bg-white p-4 text-black shadow-lg hover:cursor-pointer md:w-[24%]">
                <div className="flex justify-center md:justify-start">
                  <Image src={getLogo(data.name)} alt="Company Logo" width={60} height={60} />
                </div>
                <div className="flex justify-center gap-2">
                  <p className="text-[1.37rem] font-bold text-black">RM</p>
                  <ReactAnimatedNumber
                    value={data.totalCharges}
                    style={{
                      fontSize: '1.37rem',
                      fontWeight: 'bold',
                      color: 'black',
                    }}
                    formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                  />
                </div>
                <p className="flex items-center justify-center whitespace-nowrap text-xs md:justify-end">
                  More than standard length :&nbsp;<strong>{data.moreThanStandardLength}</strong>
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
}
