// Next, React, Tw
import Image from 'next/image';
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Mui
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Divider,
  IconButton,
  Fragment,
  Checkbox,
} from '@mui/material';
import { Close } from '@mui/icons-material';

// Packages
import * as R from 'ramda';
import moment from 'moment';
import { json2csv } from 'json-2-csv';

// Components
import Layout from '../../layouts/module/hsba';
import { TablePaginationCustom } from '../../components/Shared/table';
import DemandListRequest from '../../components/hsba/DemandListRequest';
import ApexChart from '../../components/Shared/ReactApexcharts';
import { DateInput } from '../../components/Shared/CustomInput';

// Others
import axios from '../../utils/axios';
import { getL<PERSON>, HSBA_ENDPOINT } from '../../utils/hsba';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { setParam } = useParamContext();
  const { query, asPath, replace } = useRouter();
  const { dataAsOfDate } = query;
  const dispatch = useDispatch();

  const [requests, setRequests] = useState([]);
  const [sourceDate, setSourceDate] = useState(moment().format('YYYY-MM-DD, hh:mm A'));
  const [filterKeyword] = useState('');
  const [filterCustomer, setFilterCustomer] = useState('');
  const [showTheseRequests, setShowTheseRequests] = useState([]);
  const [showTheseRequestsTitle, setShowTheseRequestsTitle] = useState('');
  const [exportWithFormattedAddress, setExportWithFormattedAddress] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(20);
  const [createdMonth, setCreatedMonth] = useState(moment()?.format('YYYY-MM'));
  const [performanceData, setPerformanceData] = useState([]);

  // Table
  const headCellStyle =
    'text-center bg-hsba text-white border border-[#999999] text-sm font-semibold whitespace-nowrap px-1';

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);

  // Others

  const requestStatusMapper = (group) => ({
    ...group,
    length: R.length(group),
    status: R.head(group).REQUEST_STATUS,
    requests: group,
  });

  const renameStatus = (status) => {
    if (status.toLowerCase().includes('alternative')) return 'No Alternative Port';

    if (status.toLowerCase().includes('serviceable')) return 'Not Serviceable';

    return status;
  };

  const openModal = (list, title) => {
    setShowTheseRequests(list);
    setShowTheseRequestsTitle(title);
    setDialogOpen(true);
  };

  const diffForHumans = (time, base = moment()) => {
    if (!moment(time).isValid()) return null;

    return moment(time).from(base);
  };

  const dateDisplay = (time) => {
    if (!moment(time).isValid()) return '';

    return moment(time).format('YYYY-MM-DD h:mm A');
  };

  const statusTagClass = (text) => {
    if (text.toLowerCase().includes('proceed order')) {
      return 'bg-[#23d160]';
    }

    if (text.toLowerCase().includes('port full')) {
      return 'bg-[#209cee]';
    }

    if (text.toLowerCase().includes('recreate')) {
      return 'bg-[#ffdd57]';
    }

    if (text.toLowerCase().includes('serviceable')) {
      return 'bg-[#ff3860]';
    }

    if (text.toLowerCase().includes('open')) {
      return 'bg-[#00d1b2]';
    }

    if (text.toLowerCase().includes('progress')) {
      return 'bg-[#3273dc]';
    }

    if (text.toLowerCase().includes('reject')) {
      return 'bg-[#363636]';
    }

    return '';
  };

  const verdictClass = (text) => {
    const lowerText = text.toLowerCase();

    if (lowerText.includes('met')) {
      return { backgroundColor: '#23d160', color: 'white' };
    }

    if (lowerText.includes('missed')) {
      return { backgroundColor: '#ff3860', color: 'white' };
    }

    if (lowerText.includes('within')) {
      return { backgroundColor: '#3273dc', color: 'white' };
    }

    if (lowerText.includes('threshold')) {
      return { backgroundColor: '#ffdd57', color: 'black' };
    }

    if (lowerText.includes('missing')) {
      return { backgroundColor: 'white', color: '#ff3860' };
    }

    if (lowerText.includes('monitored')) {
      return { backgroundColor: '#363636', color: 'white' };
    }

    return {};
  };

  const exportList = async (mode = 'simple') => {
    const addressCombiner = (address) =>
      R.pipe(R.omit(['Latitude', 'Longitude', 'NEID']), R.values(), R.join(', '))(address);

    const selectedColumnsOnly = R.map(
      R.pick([
        'REQUEST_ID',
        'CATEGORY',
        'ORBIT_ID',
        'SERVICE_ADDRESS',
        'REQUEST_STATUS',
        'CREATED_AT',
        'CREATED_BY',
        'AGING',
        'SUB_AGING',
        'REJECT_REMARK',
        'ORBIT_REMARK',
        'PROJECT_RFSI_DATE',
      ])
    );

    const keysRenamer = R.curry((keysMap, obj) =>
      R.reduce((acc, key) => R.assoc(keysMap[key] || key, obj[key], acc), {}, R.keys(obj))
    );

    const renameKeys = R.map(
      keysRenamer({
        REQUEST_ID: 'REFERENCE_ID',
        ORBIT_ID: 'TICKET_ID',
        REQUEST_STATUS: 'STATUS',
        ORBIT_REMARK: 'AND_REMARK',
      })
    );

    const removeSystemColumns = R.map(R.omit(['_index', 'ADDRESS']));

    const flattenAddress = (formattedAddress) => (request) => {
      if (formattedAddress) {
        return {
          ...request,
          ...{
            UnitLotNumber: request.ADDRESS.UnitLotNumber,
            Floor: request.ADDRESS.Floor,
            BuildingName: request.ADDRESS.BuildingName,
            StreetType: request.ADDRESS.StreetType,
            StreetName: request.ADDRESS.StreetName,
            Section: request.ADDRESS.Section,
            City: request.ADDRESS.City,
            Postcode: request.ADDRESS.Postcode,
            State: request.ADDRESS.State,
            Latitude: request.ADDRESS.Latitude,
            Longitude: request.ADDRESS.Longitude,
            NEID: request.ADDRESS.NEID,
          },
        };
      }
      // combined address
      return {
        ...request,
        SERVICE_ADDRESS: addressCombiner(request.ADDRESS),
      };
    };
    let dataPlaceholder;
    if (mode === 'simple') {
      dataPlaceholder = R.pipe(
        R.map(flattenAddress(false)),
        selectedColumnsOnly,
        renameKeys,
        removeSystemColumns
      )(showTheseRequests);
    } else {
      dataPlaceholder = R.pipe(
        R.map(flattenAddress(exportWithFormattedAddress)),
        removeSystemColumns
      )(showTheseRequests);
    }

    const blob = new Blob([(await json2csv(dataPlaceholder))?.replaceAll('undefined', '')], {
      type: 'text/csv',
    });
    const a = document.createElement('a');
    a.download = `HSBA-DemandList-${showTheseRequestsTitle}-${moment().format('YYYY-MM-DD')}.csv`;
    a.href = window.URL.createObjectURL(blob);
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const asOf = moment(sourceDate, 'YYYY/MM/DD hh:mm').format('YYYY-MM-DD, hh:mm A');

  const performanceChartSeries = [
    {
      name: 'Total',
      data: R.pluck('total')(performanceData),
    },
    {
      name: 'Meet',
      data: R.pluck('meet')(performanceData),
    },
    {
      name: 'Breached',
      data: R.pluck('breached')(performanceData),
    },
  ];

  const performanceChartOptions = {
    chart: { toolbar: { show: false } },
    colors: ['#008FFB', '#00E396', '#FF0000'],
    dataLabels: {
      enabled: false,
    },
    stroke: {
      curve: 'smooth',
    },
    legend: {
      position: 'top',
      horizontalAlign: 'center',
    },
    xaxis: {
      type: 'category',
      categories: performanceData.map(({ month }) => moment(month, 'YYYY-MM').format('MMMM YYYY')),
    },
  };

  const customerWithCount = R.countBy(R.toUpper)(
    R.sort((a, b) => a.localeCompare(b), R.pipe(R.pluck('CUSTOMER'))(requests))
  );

  const modalList = showTheseRequests;

  const filteredRequests = (() => {
    const filterMode = filterCustomer === '' ? filterKeyword : `"CUSTOMER":"${filterCustomer}`;

    return requests.filter((request) => request.contains(filterMode));
  })();

  const filteredByMonth = filteredRequests.filter((request) => request.isCreatedAt(createdMonth));

  const totalRequests = filteredByMonth;
  const openRequests = filteredByMonth.filter((request) => request.isOpen());
  const inProgressRequests = filteredByMonth.filter((request) => request.isInProgress());
  const respondedRequests = filteredByMonth.filter((request) => request.isResponded());
  const jeopardyRequests = filteredByMonth.filter((request) => request.isInJeopardy());
  const breachedRequests = filteredByMonth.filter((request) => request.isBreached());
  const meetRequests = filteredByMonth.filter((request) => request.isMeet());
  const excludedRequests = filteredByMonth.filter((request) => request.isExcluded());
  const addressNotFoundRequests = filteredByMonth.filter((request) =>
    request.isUnderCategory('Address Not Found')
  );
  const portFullRequests = filteredByMonth.filter((request) =>
    request.isUnderCategory('Port Full')
  );

  const rfsiMeetRequests = filteredByMonth.filter((request) => request.isRfsiMeet());
  const rfsiJeopardyRequests = filteredByMonth.filter((request) => request.isRfsiJeopardy());
  const rfsiBreachedRequests = filteredByMonth.filter((request) => request.isRfsiBreached());

  const meetGroupByStatus = R.pipe(
    R.groupBy(R.prop('REQUEST_STATUS')),
    R.map(requestStatusMapper),
    R.values(),
    R.sortBy((status) => status.length)
  )(meetRequests);

  const breachedGroupByStatus = R.pipe(
    R.groupBy(R.prop('REQUEST_STATUS')),
    R.map(requestStatusMapper),
    R.values,
    R.sortBy((status) => status.length)
  )(breachedRequests);

  const excludedGroupByStatus = R.pipe(
    R.groupBy(R.prop('REQUEST_STATUS')),
    R.map(requestStatusMapper),
    R.values,
    R.sortBy((status) => status.length)
  )(excludedRequests);

  const monthList = R.pipe(
    R.indexBy(R.prop('month')),
    R.map((month) => moment(month.month, 'YYYY-MM').format('MMMM YYYY'))
  )(performanceData);

  const fetchData = async () => {
    if ([undefined, '']?.includes(dataAsOfDate)) {
      replace(`${asPath}?dataAsOfDate=${moment()?.format('YYYY-MM-DD')}`);
      return;
    }
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${HSBA_ENDPOINT}/demand/${createdMonth}/${dataAsOfDate}`);
      setRequests(response.data[0].requests.map((request) => new DemandListRequest(request, [])));
      setSourceDate(moment(response.data[0].updatedAt));
    } catch {
      /* empty */
    }
    dispatch(setIsLoading(false));
  };

  const fetchPerformanceData = async () => {
    try {
      const response = await axios.get(`${HSBA_ENDPOINT}/demandPerformance`);
      setPerformanceData(response.data.data);
    } catch {
      /* empty */
    }
  };

  useEffect(() => {
    fetchData();
  }, [createdMonth, dataAsOfDate]);

  useEffect(() => {
    fetchPerformanceData();
  }, []);

  return (
    <>
      <div
        style={{
          background: 'linear-gradient(to right bottom, #485fc7, #7957d5)',
          color: 'white',
          padding: '15px',
        }}
      >
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-bold">Demand List</p>
            <p className="text-xs">As of : {asOf}</p>
          </div>
        </div>
      </div>
      <div className="bg-[#f5f5f5] text-black dark:bg-gray-800 dark:text-white">
        <div className="container flex flex-col justify-between gap-4">
          <div className="mx-auto my-4 flex w-full flex-col gap-4">
            <div className="flex flex-col gap-4 md:flex-row md:gap-0">
              <div className="w-full md:w-1/2">
                <div className="flex flex-col items-center gap-4">
                  <div className="flex flex-col items-center gap-2 md:flex-row">
                    <DateInput
                      value={dataAsOfDate}
                      placeholder="As of"
                      returnedFormat="YYYY-MM-DD"
                      onChange={(event) => {
                        setParam({ dataAsOfDate: event?.target?.value });
                      }}
                    />
                  </div>
                  <div className="flex flex-col items-center gap-2 md:flex-row">
                    <p className="text-md font-semibold">Requests created in:</p>
                    <select
                      className="peer h-full w-[200px] rounded-lg border  bg-transparent bg-white px-3 py-2.5 text-xs text-black outline outline-0 transition-all placeholder-shown:border focus:border-2 focus:border-[#ff7b7b] focus:outline-0 disabled:border-2 disabled:border-t-transparent disabled:outline-0"
                      value={createdMonth}
                      onChange={(event) => {
                        setCreatedMonth(event.target.value);
                      }}
                    >
                      {Object.entries(monthList).map(([monthStr, month]) => (
                        <option value={monthStr} key={monthStr}>
                          {month}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="flex w-full justify-center gap-2">
                    <div className="flex">
                      <p className="rounded-l-[2px] bg-[#363636] p-2 text-xs font-semibold text-white">
                        Address Not Found
                      </p>
                      <p className="rounded-r-[2px] bg-[#ff6600] p-2 text-xs font-semibold text-white">
                        {addressNotFoundRequests.length}
                      </p>
                    </div>
                    <div className="flex">
                      <p className="rounded-l-[2px] bg-[#363636] p-2 text-xs font-semibold text-white">
                        Port Full
                      </p>
                      <p className="rounded-r-[2px] bg-[#7957d5] p-2 text-xs font-semibold text-white">
                        {portFullRequests.length}
                      </p>
                    </div>
                    <div className="flex">
                      <p className="rounded-l-[2px] bg-[#363636] p-2 text-xs font-semibold text-white">
                        As Of
                      </p>
                      <p className="rounded-r-[2px] bg-[#3e8ed0] p-2 text-xs font-semibold text-white">
                        {moment(sourceDate, 'YYYY/MM/DD hh:mm').fromNow()}
                      </p>
                    </div>
                  </div>
                  <div className="flex w-full gap-2">
                    {[
                      {
                        title: 'Open',
                        dialogTitle: 'Open Request (Pending Action by CSM)',
                        data: openRequests,
                      },
                      {
                        title: 'In Progress',
                        dialogTitle: 'Pending Updates from Orbit',
                        data: inProgressRequests,
                      },
                      {
                        title: 'Responded',
                        dialogTitle: 'Responded Request',
                        data: respondedRequests,
                      },
                    ]?.map((o, i) => (
                      <button
                        type="button"
                        className="flex w-1/3 flex-col items-center justify-center rounded-[4px] bg-white p-4 text-center text-black shadow-md"
                        onClick={() => openModal(o?.data, o?.dialogTitle)}
                      >
                        <p className="text-xl font-semibold">{o?.data.length}</p>
                        <p className="text-lg font-semibold">{o?.title}</p>
                      </button>
                    ))}
                  </div>
                  <div className="flex w-full flex-col justify-center gap-2 md:flex-row">
                    <div className="flex items-center gap-1">
                      <div className="">Filter by Customer</div>
                      <div>
                        <select
                          className="peer h-full w-[200px] rounded-lg border  bg-transparent bg-white px-3 py-2.5 text-xs text-black outline outline-0 transition-all placeholder-shown:border focus:border-2 focus:border-[#ff7b7b] focus:outline-0 disabled:border-2 disabled:border-t-transparent disabled:outline-0"
                          value={filterCustomer}
                          onChange={(event) => setFilterCustomer(event.target.value)}
                        >
                          <option value="">All</option>
                          {Object.entries(customerWithCount).map(([customer, counter]) => (
                            <option value={customer} key={customer}>
                              {customer} ({counter})
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex w-full flex-col justify-center gap-2 px-2 text-xl font-semibold md:w-1/2">
                <div className="flex flex-wrap">
                  {[
                    {
                      title: 'Jeopardy',
                      dialogTitle: 'Request in Jeopardy (Less than 3 days left)',
                      customeStyle: 'bg-[#ffe08a] text-black',
                      data: jeopardyRequests,
                    },
                    {
                      title: 'Total',
                      dialogTitle: 'All Request',
                      customeStyle: 'bg-[#363636] text-white',
                      data: totalRequests,
                    },
                    {
                      title: 'Meet',
                      dialogTitle: 'Request that met SLA',
                      customeStyle: 'bg-[#48c78e] text-white',
                      data: meetRequests,
                    },
                    {
                      title: 'Breached',
                      dialogTitle: 'Request that breached SLA',
                      customeStyle: 'bg-[#f14668] text-white',
                      data: breachedRequests,
                    },
                  ]?.map((o, i) => (
                    <button
                      type="button"
                      key={i}
                      className={`h-28 w-1/2 ${o?.customeStyle}`}
                      onClick={() => openModal(o?.data, o?.dialogTitle)}
                    >
                      <p>{o?.data.length}</p>
                      <p>
                        {o?.title}
                        {i === 1 && (
                          <>
                            {' '}
                            {excludedRequests.length > 0 && (
                              <span>with {excludedRequests.length} exclusion</span>
                            )}
                          </>
                        )}
                      </p>
                    </button>
                  ))}
                </div>
              </div>
            </div>

            <Divider>Performance Summary</Divider>

            <div className="bg-white p-4 text-black">
              <ApexChart
                type="area"
                height={350}
                options={performanceChartOptions}
                series={performanceChartSeries}
              />
            </div>

            <Divider>Details Breakdown</Divider>

            <div className="flex items-center justify-center">
              <div className="flex w-1/4 flex-col items-center justify-between gap-4 rounded-[4px] bg-[#48c78e] p-4 text-white md:flex-row md:gap-0">
                <h6>Meet</h6>
                <p className="rounded-[4px] bg-[#0a0a0a] p-4 text-white">{meetRequests.length}</p>
              </div>
              <div className="flex w-3/4 flex-wrap items-center justify-center gap-2">
                {meetGroupByStatus.length ? (
                  <>
                    {meetGroupByStatus.map((grouped, index) => (
                      <button
                        type="button"
                        key={index}
                        className="flex flex-col items-center justify-center hover:cursor-pointer"
                        onClick={() => openModal(grouped.requests, `${grouped.status} (Meet)`)}
                      >
                        <p className="p-1 text-xs font-semibold">{renameStatus(grouped.status)}</p>
                        <p className="text-sm font-semibold">{grouped.length}</p>
                      </button>
                    ))}
                  </>
                ) : (
                  <p className="text-sm font-semibold">Woo-hoo, no breached request</p>
                )}
              </div>
            </div>

            <div className="flex items-center justify-center">
              <div className="flex w-1/4 flex-col items-center justify-between gap-4 rounded-[4px] bg-[#f14668] p-4 text-white md:flex-row md:gap-0">
                <h6>Breached</h6>
                <p className="rounded-[4px] bg-[#0a0a0a] p-4 text-white">
                  {breachedRequests.length}
                </p>
              </div>
              <div className="flex w-3/4 flex-wrap items-center justify-center gap-2">
                {breachedGroupByStatus.length ? (
                  <>
                    {breachedGroupByStatus.map((grouped, index) => (
                      <button
                        type="button"
                        key={index}
                        className="flex flex-col items-center justify-center hover:cursor-pointer"
                        onClick={() => openModal(grouped.requests, `${grouped.status} (Breached)`)}
                      >
                        <p className="p-1 text-xs font-semibold">{renameStatus(grouped.status)}</p>
                        <p className="text-sm font-semibold">{grouped.length}</p>
                      </button>
                    ))}
                  </>
                ) : (
                  <p className="text-sm font-semibold">Woo-hoo, no breached request</p>
                )}
              </div>
            </div>

            <div className="flex items-center justify-center">
              <div className="flex w-1/4 flex-col items-center justify-between gap-4 rounded-[4px] bg-[#7957d5] p-4 text-white md:flex-row md:gap-0">
                <h6>Exclusion</h6>
                <p className="rounded-[4px] bg-[#0a0a0a] p-4 text-white">
                  {excludedRequests.length}
                </p>
              </div>
              <div className="flex w-3/4 flex-wrap items-center justify-center">
                {excludedGroupByStatus.length ? (
                  <>
                    {excludedGroupByStatus.map((grouped, index) => (
                      <button
                        type="button"
                        key={index}
                        className="flex flex-col items-center justify-center hover:cursor-pointer"
                        onClick={() => openModal(grouped.requests, `${grouped.status} (Exclusion)`)}
                      >
                        <p className="p-1 text-xs font-semibold">{renameStatus(grouped.status)}</p>
                        <p className="text-sm font-semibold">{grouped.length}</p>
                      </button>
                    ))}
                  </>
                ) : (
                  <p className="text-sm font-semibold">No Exclusion</p>
                )}
              </div>
            </div>

            <Divider>RFSI Breakdown</Divider>

            <div className="flex w-full flex-col items-center justify-around gap-2 md:flex-row md:gap-0">
              {[
                {
                  title: 'Meet',
                  customStyle: 'bg-[#70ad47] text-white',
                  data: rfsiMeetRequests,
                },
                {
                  title: 'Jeopardy',
                  customStyle: 'bg-[#ffff00] text-black',
                  data: rfsiJeopardyRequests,
                },
                {
                  title: 'Breached',
                  customStyle: 'bg-[#ff0000] text-white',
                  data: rfsiBreachedRequests,
                },
              ]?.map((o, i) => (
                <button
                  type="button"
                  key={i}
                  className={`flex w-full cursor-pointer flex-col items-center gap-4 rounded-lg p-4 text-center font-semibold md:w-1/5 ${o?.customStyle}`}
                  onClick={() => openModal(o?.data, `RFSI (${o?.title})`)}
                >
                  <h6>{o?.title}</h6>
                  <p className="w-20 rounded-[4px] bg-[#0a0a0a] p-4 text-white">{o?.data.length}</p>
                </button>
              ))}
            </div>
          </div>
          <Dialog open={dialogOpen} fullWidth maxWidth="xl">
            <DialogTitle>
              {showTheseRequestsTitle}
              <IconButton
                onClick={() => setDialogOpen(false)}
                sx={{
                  position: 'absolute',
                  right: 8,
                  top: 8,
                  color: (theme) => theme.palette.grey[500],
                }}
              >
                <Close />
              </IconButton>
            </DialogTitle>
            <DialogContent sx={{ padding: '16px' }}>
              <div className="overflow-x-auto scrollbar">
                <table className="min-w-full text-xs">
                  <thead>
                    <tr>
                      <td className={`${headCellStyle} w-[120px]`}>Request #</td>
                      <td className={`${headCellStyle} w-[36px]`}>Customer</td>
                      <td className={`${headCellStyle} w-[36px]`}>Type</td>
                      <td className={`${headCellStyle} w-[75px]`}>Created At</td>
                      <td className={`${headCellStyle} w-[75px]`}>Target At</td>
                      <td className={`${headCellStyle} w-[95px]`}>Responded At</td>
                      <td className={`${headCellStyle} min-w-[400px]`}>Orbit</td>
                      <td className={`${headCellStyle} w-[200px]`}>Verdict</td>
                    </tr>
                  </thead>
                  <tbody>
                    {modalList.length === 0 ? (
                      <tr>
                        <td
                          colSpan={7}
                          align="center"
                          className="even"
                          style={{
                            height: '150px',
                            verticalAlign: 'middle',
                            border: '1px solid #999999',
                            backgroundColor: '#fafafa',
                          }}
                        >
                          <p>No matching requests found. Did you enable any filter?</p>
                        </td>
                      </tr>
                    ) : (
                      modalList
                        .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                        .map((request, index) => (
                          <>
                            <tr key={index}>
                              <td
                                rowSpan={2}
                                align="center"
                                style={{
                                  verticalAlign: 'middle',
                                  border: '1px solid #999999',
                                  backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                }}
                              >
                                <div className="flex flex-col items-center gap-2">
                                  <div className="flex items-center text-white">
                                    <p className="rounded-l-lg bg-[#0a0a0a] p-2">Aging</p>
                                    <p className="rounded-r-lg bg-[#363636] p-2">{request.AGING}</p>
                                  </div>
                                  <div>{request.REQUEST_ID}</div>
                                  <div
                                    className={`${statusTagClass(
                                      request.REQUEST_STATUS
                                    )} rounded-lg p-2 text-white`}
                                  >
                                    {renameStatus(request.REQUEST_STATUS)}
                                  </div>
                                </div>
                              </td>
                              {/* eslint-disable */}
                              <td
                                rowSpan={2}
                                align="center"
                                style={{
                                  verticalAlign: 'middle',
                                  fontWeight: 'bold',
                                  border: '1px solid #999999',
                                  backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                }}
                              >
                                <div className="flex justify-center">
                                  <Image
                                    src={getLogo(request.CUSTOMER)}
                                    alt="Customer Logo"
                                    width={50}
                                    height={50}
                                  />
                                </div>
                              </td>
                              <td
                                rowSpan={2}
                                style={{
                                  verticalAlign: 'middle',
                                  border: '1px solid #999999',
                                  backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                }}
                              >
                                <div className="flex justify-center"></div>
                                {request.CATEGORY === 'Address Not Found' && (
                                  <Image
                                    src={'/hsba/addressNotFound.png'}
                                    alt="Address Not Found Icon"
                                    width={30}
                                    height={30}
                                  />
                                )}
                                {request.CATEGORY === 'Port Full' && (
                                  <Image
                                    src={'/hsba/portFull.png'}
                                    alt="Port Full Icon"
                                    width={30}
                                    height={30}
                                  />
                                )}
                              </td>
                              <td
                                rowSpan={2}
                                align="center"
                                style={{
                                  verticalAlign: 'middle',
                                  border: '1px solid #999999',
                                  backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                }}
                                title={diffForHumans(request.CREATED_AT)}
                              >
                                {dateDisplay(request.CREATED_AT)}
                              </td>
                              <td
                                rowSpan={2}
                                align="center"
                                style={{
                                  verticalAlign: 'middle',
                                  border: '1px solid #999999',
                                  backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                }}
                                title={diffForHumans(request.SLA_TARGET_AT)}
                              >
                                {dateDisplay(request.SLA_TARGET_AT)}
                              </td>
                              {request.REQUEST_STATUS === 'Rejected' ? (
                                <td
                                  rowSpan={2}
                                  align="center"
                                  style={{
                                    verticalAlign: 'middle',
                                    border: '1px solid #999999',
                                    backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                  }}
                                  title={diffForHumans(request.SYS_UPDATED_AT)}
                                >
                                  {dateDisplay(request.SYS_UPDATED_AT) || 'Not yet'}
                                </td>
                              ) : (
                                <td
                                  rowSpan={2}
                                  align="center"
                                  style={{
                                    verticalAlign: 'middle',
                                    border: '1px solid #999999',
                                    backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                  }}
                                  title={diffForHumans(request.UPDATED_AT)}
                                >
                                  {dateDisplay(request.UPDATED_AT) || 'Not yet'}
                                </td>
                              )}
                              <td
                                style={{
                                  border: '1px solid #999999',
                                  backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                }}
                              >
                                <>
                                  {request.REQUEST_STATUS === 'Rejected' ? (
                                    <div>
                                      <p>{request.REJECT_REMARK}</p>
                                      <p>{request.UPDATED_BY}</p>
                                    </div>
                                  ) : request.ORBIT_ID ? (
                                    <div className="flex flex-col">
                                      <div className="flex items-center justify-between">
                                        <div>
                                          <strong>#</strong> {request.ORBIT_ID}
                                        </div>
                                        {request.SUB_AGING >= 0 && (
                                          <div className="flex text-white">
                                            <p className="rounded-l-lg bg-[#0a0a0a] p-2">
                                              Orbit Aging
                                            </p>
                                            <p className="rounded-r-lg bg-[#363636] p-2">
                                              {request.SUB_AGING}
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                      <div>
                                        <p>
                                          <strong>Status:</strong>{' '}
                                          {request.ORBIT_STATUS || 'In Progress'}
                                        </p>
                                      </div>
                                      {request.ORBIT_ACTION && (
                                        <div>
                                          <p>
                                            <strong>Action</strong>
                                          </p>
                                          <p>{request.ORBIT_ACTION}</p>
                                        </div>
                                      )}
                                      {request.ORBIT_REMARK && (
                                        <div>
                                          <p>
                                            <strong>Remark</strong>
                                          </p>
                                          <p>{request.ORBIT_REMARK}</p>
                                        </div>
                                      )}
                                    </div>
                                  ) : (
                                    <p>Ticket not yet created</p>
                                  )}
                                </>
                              </td>
                              <td
                                style={{
                                  width: '200px',
                                  verticalAlign: 'middle',
                                  border: '1px solid #999999',
                                  backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                  ...verdictClass(request.VERDICT),
                                }}
                              >
                                {request.VERDICT}
                              </td>
                            </tr>
                            <tr key={`address_${index}`}>
                              <td
                                style={{
                                  border: '1px solid #999999',
                                  backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                }}
                              >
                                <p>
                                  {request.ADDRESS?.UnitLotNumber && (
                                    <span>{request.ADDRESS?.UnitLotNumber},&nbsp;</span>
                                  )}
                                  {request.ADDRESS?.Floor && (
                                    <span>{request.ADDRESS?.Floor},&nbsp;</span>
                                  )}
                                  {request.ADDRESS?.BuildingNumber && (
                                    <span>{request.ADDRESS?.BuildingNumber},&nbsp;</span>
                                  )}
                                  {request.ADDRESS?.StreetType && (
                                    <span>{request.ADDRESS?.StreetType},&nbsp;</span>
                                  )}
                                  {request.ADDRESS?.StreetName && (
                                    <span>{request.ADDRESS?.StreetName},&nbsp;</span>
                                  )}
                                  {request.ADDRESS?.Section && (
                                    <span>{request.ADDRESS?.Section},&nbsp;</span>
                                  )}
                                  {request.ADDRESS?.Postcode && (
                                    <span>{request.ADDRESS?.Postcode},&nbsp;</span>
                                  )}
                                  {request.ADDRESS?.City && (
                                    <span>{request.ADDRESS?.City},&nbsp;</span>
                                  )}
                                  {request.ADDRESS?.State && <span>{request.ADDRESS?.State}</span>}
                                </p>
                                {request.ADDRESS?.Latitude && (
                                  <p className="pt-2">
                                    {request.ADDRESS?.Latitude}, {request.ADDRESS?.Longitude}
                                  </p>
                                )}
                                {!request.ADDRESS?.Latitude &&
                                  request.CATEGORY === 'Address Not Found' && (
                                    <Box>No coordinates provided</Box>
                                  )}
                              </td>
                              <td
                                style={{
                                  verticalAlign: 'middle',
                                  border: '1px solid #999999',
                                  backgroundColor: `${index % 2 === 0 ? '#fafafa' : 'white'}`,
                                }}
                              >
                                {request.EXCHANGE && <div title="Exchange">{request.EXCHANGE}</div>}
                                {request.EXCHANGE && <div title="Zone">{request.ZONE}</div>}
                                {request.EXCHANGE && <div title="PTT">{request.PTT}</div>}
                              </td>
                            </tr>
                          </>
                        ))
                    )}
                  </tbody>
                </table>
              </div>
              <TablePaginationCustom
                count={modalList.length}
                page={page}
                rowsPerPage={rowsPerPage}
                onPageChange={(event, newPage) => {
                  setPage(newPage);
                }}
              />
            </DialogContent>
            <DialogActions>
              <div className="flex w-full flex-wrap items-center gap-2 p-4">
                <button
                  type="button"
                  className="cta-btn bg-[#48c78e]"
                  onClick={() => exportList()}
                >{`Export (Simplified)`}</button>
                <button
                  type="button"
                  className="cta-btn bg-[#3e8ed0]"
                  onClick={() => exportList('operations')}
                >{`Export (Detailed)`}</button>
                <div className="flex items-center gap-1">
                  <Checkbox
                    size="big"
                    checked={exportWithFormattedAddress}
                    onChange={(event) => setExportWithFormattedAddress(event.target.checked)}
                  />
                  <p className="text-lg font-semibold">With formatted address</p>
                </div>
              </div>
            </DialogActions>
          </Dialog>
        </div>
      </div>
    </>
  );
}
