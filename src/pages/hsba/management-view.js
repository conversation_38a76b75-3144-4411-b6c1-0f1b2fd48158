// Next, React, Tw
import { twMerge } from 'tailwind-merge';
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Mui
import { Divider } from '@mui/material';
import { KeyboardDoubleArrowLeft, KeyboardDoubleArrowRight } from '@mui/icons-material';

// Packages
import * as R from 'ramda';
import moment from 'moment';

// Components
import YearPerf from '../../components/hsba/YearPerf';
import Layout from '../../layouts/module/hsba';
import ReactAnimatedNumber from '../../components/Shared/ReactAnimatedNumber';
import { SelectInput } from '../../components/Shared/CustomInput';

// Others
import { HSBA_ENDPOINT } from '../../utils/hsba';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const dispatch = useDispatch();
  const { isLoading } = useSelector((state) => state.loading);

  const [currentMonth, setCurrentMonth] = useState(
    moment().subtract(moment().format('DD') === '01' ? 1 : 0, 'month')
  );
  const [allRecords, setAllRecords] = useState(false);
  const [customer, setCustomer] = useState('All');
  const [averageScope, setAverageScope] = useState(12);
  const [packageShown, setPackageShown] = useState('residential');
  const [asOf, setAsOf] = useState('');

  // Others
  const reportYear = currentMonth.format('YYYY');

  const records = allRecords[reportYear] || {};

  const customers = (() => {
    if (!records[currentMonth.format('YYYY-MM')]?.breakdown) {
      return [];
    }
    return R.keys(records[currentMonth.format('YYYY-MM')]?.breakdown);
  })();

  const prevMonth = currentMonth.clone().subtract(1, 'month');

  const currentReport = (() => {
    if (customer === 'All') {
      return (
        records[currentMonth.format('YYYY-MM')] || {
          new_install: 0,
          termination: 0,
          active: 0,
        }
      );
    }

    if (!(records[currentMonth.format('YYYY-MM')] || {}).breakdown) {
      return {
        new_install: 0,
        termination: 0,
        active: 0,
      };
    }

    return (
      (records[currentMonth.format('YYYY-MM')] || {}).breakdown[customer] || {
        new_install: 0,
        termination: 0,
        active: 0,
      }
    );
  })();

  const prevReport = (() => {
    if (customer === 'All') {
      return (
        records[prevMonth.format('YYYY-MM')] || {
          new_install: 0,
          termination: 0,
          active: 0,
        }
      );
    }
    return R.find((temp) => temp.date === prevMonth.format('YYYYMM'))([]) || {};
  })();

  const currentNet = currentReport.new_install - currentReport.termination;

  const prevNet = prevReport.new_install - prevReport.termination;

  const residentialCurrentNet =
    currentReport.residentialNewInstall - currentReport.residentialTermination;

  const businessCurrentNet = currentReport.businessNewInstall - currentReport.businessTermination;

  const residentialPrevNet = prevReport.residentialNewInstall - prevReport.residentialTermination;

  const businessPrevNet = prevReport.businessNewInstall - prevReport.businessTermination;

  const dataSet = R.pipe(R.map(R.pipe(R.prop('breakdown'), R.values())))(records);

  const filteredDataSet = (() => {
    if (customer === 'All') return dataSet;
    return R.map(R.filter(R.propEq('customer', customer)))(dataSet);
  })();

  const calcGrowth = (a, b) => {
    if (!b) {
      return null;
    }
    return (((a - b) / b) * 100).toFixed(2);
  };

  const currentTile = (() => {
    if (packageShown === 'residential') {
      return {
        newInstall: currentReport.residentialNewInstall,
        newInstallGrowth: calcGrowth(
          currentReport.residentialNewInstall,
          prevReport.residentialNewInstall
        ),
        termination: currentReport.residentialTermination,
        terminationGrowth: calcGrowth(
          currentReport.residentialTermination,
          prevReport.residentialTermination
        ),
        net: residentialCurrentNet,
        netGrowth: calcGrowth(residentialCurrentNet, residentialPrevNet),
      };
    }

    return {
      newInstall: currentReport.businessNewInstall,
      newInstallGrowth: calcGrowth(currentReport.businessNewInstall, prevReport.businessNewInstall),
      termination: currentReport.businessTermination,
      terminationGrowth: calcGrowth(
        currentReport.businessTermination,
        prevReport.businessTermination
      ),
      net: businessCurrentNet,
      netGrowth: calcGrowth(businessCurrentNet, businessPrevNet),
    };
  })();

  const getAverageNew = (key) => {
    if (averageScope === 12)
      return Math.round(
        R.pipe(
          R.map(R.pipe(R.pluck(key), R.sum())),
          R.values(),
          R.takeLast(averageScope),
          R.mean()
        )(filteredDataSet)
      );

    return Math.round(
      R.pipe(
        R.map(R.pipe(R.pluck(key), R.sum())),
        R.values(),
        R.takeLast(averageScope),
        R.init(),
        R.mean()
      )(filteredDataSet)
    );
  };

  const handleSwipe = (dir) => {
    if (dir === 'left') {
      setCurrentMonth(currentMonth.clone().add(1, 'month'));
    }

    if (dir === 'right') {
      setCurrentMonth(currentMonth.clone().add(-1, 'month'));
    }
  };

  const growth = (value) => {
    if (!value) {
      return '';
    }
    if (value > 0) {
      return `Up ${value}%`;
    }
    return `Down ${-value}%`;
  };

  const buttonStyle = `border-r border-hsba px-2 font-bold text-hsba hover:bg-hsba hover:text-white`;

  const changeMonthTab = (
    <>
      <button
        type="button"
        className={twMerge('hover:text-hsba flex items-center justify-center text-sm')}
        onClick={() => handleSwipe('right')}
      >
        <KeyboardDoubleArrowLeft />
        <p>Previous</p>
      </button>

      <Divider orientation="vertical" flexItem />

      <button
        type="button"
        className={twMerge('hover:text-hsba flex items-center justify-center text-sm')}
        onClick={() => handleSwipe('left')}
      >
        <p>Next</p>
        <KeyboardDoubleArrowRight />
      </button>
    </>
  );

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${HSBA_ENDPOINT}/btuPerformance/${reportYear}`);
      setAllRecords({
        ...allRecords,
        [reportYear]: R.indexBy(R.prop('month'), response.data),
      });
      setAsOf(
        moment(response.data[response.data.length - 1]?.updatedAt).format('YYYY-MM-DD, hh:mm A')
      );
    } catch {
      /* empty */
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [reportYear]);

  return (
    <>
      {!isLoading && (
        <>
          <div
            style={{
              background: 'linear-gradient(to right bottom, #485fc7, #7957d5)',
              color: 'white',
              padding: '15px',
            }}
            className="w-full"
          >
            <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
              <div className="flex flex-col gap-1">
                <p className="text-lg font-bold">BTU Port Performance</p>
                <p className="text-xs">As of : {asOf}</p>
              </div>
            </div>
          </div>
          <div className="container mx-auto p-1 md:p-4">
            <div
              style={{ boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)' }}
              className="rounded-xl bg-white text-black shadow-dashboardCard dark:bg-gray-600 dark:text-white dark:shadow-dashboardCardDark"
            >
              <div className="flex flex-col gap-8 p-4 md:flex-row">
                <div className="flex w-full flex-col gap-2 md:w-1/2">
                  <div className="flex items-center justify-between">
                    <p className="text-lg font-bold">
                      {currentMonth.format('MMMM YYYY').toUpperCase()}
                    </p>
                    <div className="w-[150px]">
                      <SelectInput
                        value={customer}
                        options={[
                          { label: 'All', value: 'All' },
                          ...customers.map((c) => ({ label: c.split(' ')[0], value: c })),
                        ]}
                        onChange={(event) => setCustomer(event.target.value)}
                      />
                    </div>
                  </div>

                  <div className="flex flex-grow flex-col items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="#ff822f"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <ellipse cx="12" cy="5" rx="9" ry="3" />
                      <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3" />
                      <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5" />
                    </svg>
                    <p className="text-md font-bold">Year-to-Date Active</p>
                    <p className="text-3xl font-bold">
                      <ReactAnimatedNumber
                        value={currentReport.active}
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                    </p>
                    <p className="text-xs">
                      {growth(calcGrowth(currentReport.active, prevReport.active))}&nbsp;
                    </p>
                  </div>
                  <div className="flex flex-grow items-center justify-between">
                    <div className="flex flex-grow flex-col items-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="18"
                        height="18"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="#58b878"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M3 3v18h18" />
                        <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3" />
                      </svg>
                      <p className="text-xs font-bold">New install</p>
                      <ReactAnimatedNumber
                        value={currentReport.new_install}
                        style={{
                          fontSize: 18,
                        }}
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                      <p className="text-xs">
                        {growth(calcGrowth(currentReport.new_install, prevReport.new_install))}
                        &nbsp;
                      </p>
                    </div>

                    <div className="flex flex-grow flex-col items-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="18"
                        height="18"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="#f14668"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M3 3v18h18" />
                        <path d="M18.7 14.3L15 10.5l-2.7 2.7L7 8" />
                      </svg>
                      <p className="text-xs font-bold">Termination</p>
                      <ReactAnimatedNumber
                        value={currentReport.termination}
                        style={{
                          fontSize: 18,
                        }}
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                      <p className="text-xs">
                        {growth(calcGrowth(currentReport.termination, prevReport.termination))}
                        &nbsp;
                      </p>
                    </div>

                    <div className="flex flex-grow flex-col items-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="18"
                        height="18"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="#4fa0d6"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M12 22a10 10 0 1 0 0-20 10 10 0 0 0 0 20z" />
                        <path d="M19 6l-7 6V2.5" />
                      </svg>
                      <p className="text-xs font-bold">Net. Total</p>
                      <ReactAnimatedNumber
                        value={currentNet}
                        style={{
                          fontSize: 18,
                        }}
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                      <p className="text-xs">{growth(calcGrowth(currentNet, prevNet))}&nbsp;</p>
                    </div>
                  </div>

                  <Divider />

                  <div className="flex flex-grow items-center justify-center">
                    <button
                      type="button"
                      onClick={() => setPackageShown('residential')}
                      className={twMerge(
                        buttonStyle,
                        'rounded-l-lg',
                        `${packageShown === 'residential' && 'bg-hsba text-white'}`
                      )}
                    >
                      Residential
                    </button>
                    <button
                      type="button"
                      onClick={() => setPackageShown('business')}
                      className={twMerge(
                        buttonStyle,
                        'rounded-r-lg border-r-0',
                        `${packageShown === 'business' && 'bg-hsba text-white'}`
                      )}
                    >
                      Business
                    </button>
                  </div>

                  <div className="flex flex-grow items-center justify-between">
                    <div className="flex flex-grow flex-col items-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="18"
                        height="18"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="#58b878"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M3 3v18h18" />
                        <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3" />
                      </svg>
                      <p className="text-xs font-bold">New install</p>
                      <ReactAnimatedNumber
                        value={currentTile.newInstall}
                        style={{
                          fontSize: 18,
                        }}
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                      <p className="text-xs">{growth(currentTile.newInstallGrowth)}&nbsp;</p>
                    </div>

                    <div className="flex flex-grow flex-col items-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="18"
                        height="18"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="#f14668"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M3 3v18h18" />
                        <path d="M18.7 14.3L15 10.5l-2.7 2.7L7 8" />
                      </svg>
                      <p className="text-xs font-bold">Termination</p>
                      <ReactAnimatedNumber
                        value={currentTile.termination}
                        style={{
                          fontSize: 18,
                        }}
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                      <p className="text-xs">{growth(currentTile.terminationGrowth)}&nbsp;</p>
                    </div>

                    <div className="flex flex-grow flex-col items-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="18"
                        height="18"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="#4fa0d6"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M12 22a10 10 0 1 0 0-20 10 10 0 0 0 0 20z" />
                        <path d="M19 6l-7 6V2.5" />
                      </svg>
                      <p className="text-xs font-bold">Net. Total</p>
                      <ReactAnimatedNumber
                        value={currentTile.net}
                        style={{
                          fontSize: 18,
                        }}
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                      <p className="text-xs">{growth(currentTile.netGrowth)}&nbsp;</p>
                    </div>
                  </div>

                  <div className="block md:hidden">
                    <Divider />
                  </div>

                  <div className=" flex flex-grow items-center justify-around md:hidden">
                    {changeMonthTab}
                  </div>

                  <div className="block md:hidden">
                    <Divider />
                  </div>
                </div>
                <div className="flex w-full flex-col gap-2 md:w-1/2">
                  <div className="rounded-md bg-white p-4 text-black">
                    {Object?.keys(records)?.length > 0 && (
                      <YearPerf data={records} customer={customer} />
                    )}
                  </div>

                  <div className="flex flex-grow flex-col items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="#7de39f"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M12 20v-6M6 20V10M18 20V4" />
                    </svg>
                    <p className="text-md font-bold">Average</p>
                  </div>

                  <div className="flex flex-grow items-center justify-center">
                    <button
                      type="button"
                      onClick={() => setAverageScope(12)}
                      className={twMerge(
                        buttonStyle,
                        'rounded-l-lg',
                        `${averageScope === 12 && 'bg-hsba text-white'}`
                      )}
                    >
                      Year-to-Date
                    </button>
                    <button
                      type="button"
                      onClick={() => setAverageScope(7)}
                      className={twMerge(
                        buttonStyle,
                        '',
                        `${averageScope === 7 && 'bg-hsba text-white'}`
                      )}
                    >
                      Last 6 Months
                    </button>
                    <button
                      type="button"
                      onClick={() => setAverageScope(4)}
                      className={twMerge(
                        buttonStyle,
                        'rounded-r-lg border-r-0',
                        `${averageScope === 4 && 'bg-hsba text-white'}`
                      )}
                    >
                      Last 3 Months
                    </button>
                  </div>

                  <div className="flex flex-grow items-center justify-between">
                    <div className="flex flex-grow flex-col items-center">
                      <p className="text-xs font-bold">New Install</p>
                      <ReactAnimatedNumber
                        value={getAverageNew('new_install')}
                        style={{
                          fontSize: 18,
                        }}
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                    </div>

                    <div className="flex flex-grow flex-col items-center">
                      <p className="text-xs font-bold">Termination</p>
                      <ReactAnimatedNumber
                        value={getAverageNew('termination')}
                        style={{
                          fontSize: 18,
                        }}
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                    </div>

                    <div className="flex flex-grow flex-col items-center">
                      <p className="text-xs font-bold">Active BTU</p>
                      <ReactAnimatedNumber
                        value={getAverageNew('active')}
                        style={{
                          fontSize: 18,
                        }}
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="hidden md:block">
                <Divider />
              </div>
              <div className="mx-auto hidden w-1/2 flex-col gap-2 md:flex">
                <div className="flex flex-grow items-center justify-around">{changeMonthTab}</div>

                <Divider />
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
}
