// Next, React, Tw
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Packages
import * as R from 'ramda';

// Components
import Layout from '../../layouts/module/metric';
import ProductAccessManagement from '../../components/metric/ProductAccessManagement';

// Others
import axios from '../../utils/axios';
import { METRIC_ENDPOINT } from '../../utils/metric';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const dispatch = useDispatch();

  const [productList, setProductList] = useState([]);

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${METRIC_ENDPOINT}/product/all/keyword`);
      if (response?.data?.data) {
        setProductList(R.pluck('product_abbreviation', response?.data?.data));
      }
    } catch {
      setProductList([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      {productList?.length === 0 ? (
        <div className="flex h-full w-full items-center justify-center bg-white">
          <p>No product!</p>
        </div>
      ) : (
        <ProductAccessManagement PRODUCTS={productList} module="metric" />
      )}
    </>
  );
}
