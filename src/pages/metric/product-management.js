// Next, react, Tw
import { twMerge } from 'tailwind-merge';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { MoreVert } from '@mui/icons-material';

// Packages
import * as yup from 'yup';

// Components
import Layout from '../../layouts/module/metric';
import { TablePaginationCustom } from '../../components/Shared/table';
import { METRIC_ENDPOINT } from '../../utils/metric';
import { TextInput, SelectInput } from '../../components/Shared/CustomInput';

// Others
import axios from '../../utils/axios';
import { useSnackbar } from '../../components/Shared/snackbar';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { query } = useRouter();
  const { setParam } = useParamContext();
  const { enqueueSnackbar } = useSnackbar();
  const { filteredDivisionId } = query;
  const dispatch = useDispatch();

  const [divisionList, setDivisionList] = useState([]);

  // Table
  const [tableData, setTableData] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(20);

  const bodyCellStyle = 'text-center whitespace-nowrap px-1 text-xs';

  const filteredTableData = (() => {
    if (filteredDivisionId === undefined || filteredDivisionId === 'All') {
      return tableData;
    }
    return tableData?.filter((o) => o?.division_id === filteredDivisionId);
  })();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const schema = yup.object({
    division_id: yup.string().required('Please choose a division.'),
    product_name: yup.string().required('Please provide a product name.'),
    product_abbreviation: yup.string().required('Please provide an abbreviation.'),
  });
  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  const handleDialogClose = async (action) => {
    if (action) {
      try {
        await schema.validate(dialogData, { abortEarly: true });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }

      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${METRIC_ENDPOINT}/product`, dialogData);
            break;
          case 'put':
            response = await axios.put(`${METRIC_ENDPOINT}/product/${dialogData?.id}`, dialogData);
            break;
          case 'delete':
            response = await axios.delete(`${METRIC_ENDPOINT}/product/${dialogData?.id}`);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    setDialogData({});
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${METRIC_ENDPOINT}/product/all/keyword`);
      if (response?.data?.data) {
        setTableData(response?.data?.data);
      }
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  const fetchData2 = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${METRIC_ENDPOINT}/division/all/keyword`);
      if (response?.data?.status === 'success' && response?.data?.data !== null) {
        setDivisionList(response?.data?.data);
      }
    } catch {
      setDivisionList([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
    fetchData2();
  }, []);

  return (
    <>
      <div className="bg-gradient-to-r from-[#d43801] to-[#ff8f14] p-[15px] text-white">
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-bold">Product Management</p>
          </div>
        </div>
      </div>

      <div className="container mx-auto p-1 md:p-4">
        <div
          style={{ boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)' }}
          className="flex flex-col gap-2 rounded-xl bg-white p-4 dark:bg-gray-600 dark:text-white"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-[200px]">
                <SelectInput
                  value={filteredDivisionId}
                  placeholder="Filter by"
                  options={[
                    {
                      label: 'All',
                      value: 'All',
                    },
                    ...divisionList.map((o, i) => ({
                      label: o?.division_name,
                      value: o?.id,
                    })),
                  ]}
                  onChange={(event) => setParam({ filteredDivisionId: event.target.value })}
                />
              </div>
            </div>

            <button
              type="button"
              className="rounded-[4px] bg-green-500 px-2 py-2 text-white"
              onClick={() => handleClickOpenDialog(false)}
            >
              New Product
            </button>
          </div>
          <div className="my-4 overflow-x-auto scrollbar">
            <table className="min-w-full">
              <thead>
                <tr>
                  {['No.', 'Name', 'Abbreviation', ''].map((label, i) => (
                    <td
                      key={i}
                      className="bg-[#408dd0] text-center text-sm font-semibold text-white"
                    >
                      {label}
                    </td>
                  ))}
                </tr>
              </thead>
              <tbody>
                {filteredTableData
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((row, i) => (
                    <tr key={i}>
                      <td className={bodyCellStyle}>{i + 1 + rowsPerPage * page}</td>
                      <td className={bodyCellStyle}>{row?.product_name}</td>
                      <td className={bodyCellStyle}>{row?.product_abbreviation}</td>
                      {/* eslint-disable-next-line */}
                      <td className={twMerge(bodyCellStyle, 'cursor-pointer')}>
                        <MoreVert onClick={() => handleClickOpenDialog(true, row)} />
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
          <TablePaginationCustom
            count={tableData.length}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={(event, newPage) => {
              setPage(newPage);
            }}
          />
        </div>
      </div>

      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle>{!editModeDialog ? 'New' : 'Edit'} Product</DialogTitle>
        <DialogContent>
          <div className="flex flex-col gap-4 p-2">
            <SelectInput
              name="division_id"
              value={dialogData?.division_id}
              placeholder="Division"
              options={divisionList?.map((o) => ({
                label: o?.division_name,
                value: o?.id,
              }))}
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="product_name"
              value={dialogData?.product_name}
              placeholder="Name"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="product_abbreviation"
              value={dialogData?.product_abbreviation}
              placeholder="Abbreviation"
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
              Cancel
            </button>
            <div className="flex gap-2">
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('delete')}
                  className="rounded-[4px] bg-red-500 p-2 font-semibold text-white"
                >
                  Delete
                </button>
              )}
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="rounded-[4px] bg-green-500 p-2 font-semibold text-white"
                >
                  Save
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="rounded-[4px] bg-green-500 p-2 font-semibold text-white"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
}
