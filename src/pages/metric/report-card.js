// Next, React, Tw
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';

// Mui
import { FormControlLabel, Switch, Divider, useMediaQuery } from '@mui/material';

// Packages
import moment from 'moment';

// Components
import Layout from '../../layouts/module/metric';
import PhysicalSection from '../../components/metric/PhysicalSection';
import RevenueSection from '../../components/metric/RevenueSection';
import { SelectInput, DateInput } from '../../components/Shared/CustomInput';

// Others
import axios from '../../utils/axios';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { METRIC_ENDPOINT } from '../../utils/metric';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { query } = useRouter();
  const { setParam } = useParamContext();
  const { divisionId, productId, year, month } = query;
  const isSmallScreen = useMediaQuery('(max-width:768px)');
  const dispatch = useDispatch();

  const [divisionDetails, setDivisionDetails] = useState({});
  const [productList, setProductList] = useState([]);

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${METRIC_ENDPOINT}/product/division_id/${divisionId}`);
      if (response?.data?.status === 'success' && response?.data?.data !== null) {
        setProductList(response?.data?.data);
        setParam({
          productId: response?.data?.data[0]?.id,
        });
      }
    } catch {
      setProductList([]);
    }
    dispatch(setIsLoading(false));
  };

  const fetchData2 = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${METRIC_ENDPOINT}/division/id/${divisionId}`);
      setDivisionDetails(response?.data?.data?.[0] || {});
    } catch (error) {
      setDivisionDetails({});
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
    fetchData2();
  }, [divisionId]);

  return (
    <>
      <div className="bg-gradient-to-r from-[#d43801] to-[#ff8f14] p-[15px] text-white">
        <div className="flex flex-col items-center gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-bold">
              Report Card - {divisionDetails?.division_name} Division
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto p-1 md:p-4">
        <div
          style={{ boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)' }}
          className="flex flex-col gap-4 rounded-xl bg-white p-4 dark:bg-gray-600 dark:text-white"
        >
          <div className="flex flex-col items-center gap-2 md:flex-row">
            <div className="w-[150px]">
              <SelectInput
                value={productId}
                placeholder="Filter by"
                options={productList.map((o, i) => ({
                  label: o?.product_abbreviation,
                  value: o?.id,
                }))}
                onChange={(event) => setParam({ productId: event.target.value })}
              />
            </div>
            <FormControlLabel
              control={
                <Switch
                  checked={year === 'all'}
                  onChange={() => {
                    if (year === 'all') {
                      setParam({ year: moment()?.format('YYYY'), month: 'all', week: undefined });
                      return;
                    }
                    setParam({ year: 'all' });
                  }}
                />
              }
              label="Lifetime"
            />
            {year !== 'all' && (
              <div className="w-[150px]">
                <DateInput
                  views={['year']}
                  value={year}
                  placeholder="Year"
                  returnedFormat="YYYY"
                  onChange={(event) => {
                    setParam({ year: event?.target?.value });
                  }}
                />
              </div>
            )}
            {year !== 'all' && (
              <FormControlLabel
                control={
                  <Switch
                    checked={month === 'all'}
                    onChange={() => {
                      if (month === 'all') {
                        setParam({ month: Number(moment()?.format('MM')) });
                        return;
                      }
                      setParam({ month: 'all' });
                    }}
                  />
                }
                label="Entire Year"
              />
            )}

            {year !== 'all' && month !== 'all' && (
              <div className="w-[150px]">
                <DateInput
                  views={['month']}
                  value={month}
                  placeholder="Month"
                  returnedFormat="MM"
                  onChange={(event) => {
                    setParam({ month: event?.target?.value });
                  }}
                />
              </div>
            )}
          </div>

          <Divider />
          <div className="flex flex-col gap-4 md:flex-row md:gap-0">
            <div className="w-full md:w-1/2">
              <RevenueSection />
            </div>
            <Divider orientation={`${isSmallScreen ? '' : 'vertical'}`} flexItem />
            <div className="w-full md:w-1/2">
              <PhysicalSection />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
