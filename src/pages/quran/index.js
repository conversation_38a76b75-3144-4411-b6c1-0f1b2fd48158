// Next.js and React
import Image from 'next/image';
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useTheme } from 'next-themes';

// MUI Components
import { Dialog, IconButton, Tooltip, Slide, Fade, Paper } from '@mui/material';
import {
  KeyboardDoubleArrowLeft,
  KeyboardDoubleArrowRight,
  Close,
  MenuBook,
  FormatListNumbered,
  Bookmark,
  BookmarkBorder,
  Search,
} from '@mui/icons-material';

// Third-party packages
import axios from 'axios';
import { useSwipeable } from 'react-swipeable';
import { twMerge } from 'tailwind-merge';

// Components
import Layout from '../../layouts/module/quran';

// Utilities
import { useParamContext } from '../../utils/auth/ParamProvider';
import { localStorageAvailable } from '../../utils/shared';
import { setGoToCertainPageDialogOpen } from '../../utils/store/quranReducer';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const { query, replace } = useRouter();
  const { page } = query;
  const { setParam } = useParamContext();
  const storageAvailable = localStorageAvailable();
  const dispatch = useDispatch();
  const { goToCertainPageDialogOpen } = useSelector((state) => state.quran);
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  // Others
  const [imageData, setImageData] = useState(null);
  const [suraList, setSuraList] = useState([]);
  const [bookmarks, setBookmarks] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');

  const isCurrentlyAtFirstPage = page === '1';
  const isCurrentlyAtLastPage = page === '604';
  const isBookmarked = bookmarks.includes(page);

  const goToPage = (pageNumber) => {
    setParam({ page: pageNumber });
    localStorage.setItem('lastViewedQuranPage', pageNumber);
  };

  const handlePageChange = (nextOrPrevious) => {
    if (nextOrPrevious === 'previous' && isCurrentlyAtFirstPage) return;
    if (nextOrPrevious === 'next' && isCurrentlyAtLastPage) return;

    let temp = 1;
    if (nextOrPrevious === 'previous') {
      temp = -1;
    }
    const newPageNumber = Number(page) + temp;
    goToPage(newPageNumber);
  };

  const toggleBookmark = () => {
    const newBookmarks = isBookmarked ? bookmarks.filter((b) => b !== page) : [...bookmarks, page];

    setBookmarks(newBookmarks);
    if (storageAvailable) {
      localStorage.setItem('quranBookmarks', JSON.stringify(newBookmarks));
    }
  };

  const handlers = useSwipeable({
    onSwiped: (eventData) => {
      if (eventData.dir === 'Left') {
        handlePageChange('previous');
      }
      if (eventData.dir === 'Right') {
        handlePageChange('next');
      }
    },
  });

  const filteredSuraList = searchQuery
    ? suraList.filter(
        (sura) =>
          sura.name_simple.toLowerCase().includes(searchQuery.toLowerCase()) ||
          sura.translated_name.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : suraList;

  const fetchListOfQuranSura = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`https://api.quran.com/api/v4/chapters`);
      if (response?.status === 200) {
        setSuraList(response?.data?.chapters);
      }
    } catch (error) {
      // console.log(error);
    }
    dispatch(setIsLoading(false));
  };

  const fetchQuranPage = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await fetch(`/quran-images/${page}.png`, { responseType: 'arraybuffer' });
      if (response?.status === 200) {
        const data = await response.arrayBuffer();
        const image = `data:image/png;base64,${Buffer.from(data, 'binary').toString('base64')}`;
        setImageData(image);
      }
    } catch {
      setImageData(null);
    } finally {
      dispatch(setIsLoading(false));
    }
  };

  useEffect(() => {
    fetchListOfQuranSura();

    // Load bookmarks from localStorage
    if (storageAvailable) {
      const savedBookmarks = localStorage.getItem('quranBookmarks');
      if (savedBookmarks) {
        setBookmarks(JSON.parse(savedBookmarks));
      }
    }
  }, []);

  useEffect(() => {
    if (page) {
      dispatch(setGoToCertainPageDialogOpen(false));
      fetchQuranPage();
    }
  }, [page]);

  useEffect(() => {
    if (storageAvailable && localStorage.getItem('lastViewedQuranPage')) {
      replace(`/quran?page=${localStorage.getItem('lastViewedQuranPage')}`);
      return;
    }
    replace('/quran?page=1');
    localStorage.setItem('lastViewedQuranPage', '1');
  }, []);

  return (
    <div className="container mx-auto h-full p-0 md:p-4">
      {/* Top toolbar */}
      <div className="mb-2 flex items-center justify-between p-2 md:p-0">
        <h1 className="text-lg font-medium text-gray-800 dark:text-white">
          Al-Quran <span className="text-sm text-gray-500 dark:text-gray-200">Page {page}</span>
        </h1>

        <div className="flex space-x-2">
          <Tooltip title={isBookmarked ? 'Remove bookmark' : 'Add bookmark'}>
            <IconButton
              onClick={toggleBookmark}
              className="text-amber-600 dark:text-amber-400"
              size="small"
            >
              {isBookmarked ? <Bookmark /> : <BookmarkBorder />}
            </IconButton>
          </Tooltip>
        </div>
      </div>

      {/* Main content area */}
      <Paper
        elevation={0}
        className="mb-4 overflow-hidden rounded-xl border border-gray-100 bg-white dark:border-gray-700 dark:bg-gray-800"
      >
        <div className="flex items-center justify-between px-4 py-3">
          <Tooltip title="Next page">
            <IconButton
              disabled={isCurrentlyAtLastPage}
              onClick={() => handlePageChange('next')}
              className="text-gray-700 dark:text-gray-300"
              size="medium"
            >
              <KeyboardDoubleArrowLeft />
            </IconButton>
          </Tooltip>
          <div className="flex space-x-2">
            <Tooltip title="Go to page">
              <IconButton
                onClick={() => dispatch(setGoToCertainPageDialogOpen(true))}
                className="text-gray-700 dark:text-gray-300"
                size="medium"
              >
                <MenuBook />
              </IconButton>
            </Tooltip>
          </div>
          <Tooltip title="Previous page">
            <IconButton
              disabled={isCurrentlyAtFirstPage}
              onClick={() => handlePageChange('previous')}
              className="text-gray-700 dark:text-gray-300"
              size="medium"
            >
              <KeyboardDoubleArrowRight />
            </IconButton>
          </Tooltip>
        </div>

        {imageData && (
          <div
            {...handlers}
            className="flex justify-center overflow-hidden bg-white p-2 transition-all duration-200 dark:bg-gray-800"
          >
            <Fade in timeout={300}>
              <div className="relative">
                <Image
                  src={imageData}
                  alt="Quran Page"
                  width={1024}
                  height={1656}
                  className={twMerge(
                    'rounded-lg transition-all duration-200 md:w-[calc(100vh_-_350px)]',
                    isDark
                      ? 'brightness-180 contrast-150 drop-shadow-lg hue-rotate-15 invert'
                      : 'drop-shadow-sm'
                  )}
                  priority
                />
              </div>
            </Fade>
          </div>
        )}

        <div className="border-t border-gray-100 px-4 py-3 text-center text-sm text-gray-500 dark:border-gray-700 dark:text-gray-400">
          Swipe or use arrow buttons to navigate
        </div>
      </Paper>

      {/* Enhanced Navigation Dialog */}
      <Dialog
        fullScreen
        open={goToCertainPageDialogOpen}
        onClose={() => dispatch(setGoToCertainPageDialogOpen(false))}
        TransitionComponent={Slide}
        TransitionProps={{ direction: 'up' }}
      >
        <div className="flex h-full flex-col overflow-hidden">
          <div className="flex items-center justify-between border-b border-gray-200 bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
            <h2 className="text-xl font-medium text-gray-800 dark:text-gray-100">Navigate Quran</h2>

            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search
                  className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"
                  fontSize="small"
                />
                <input
                  type="text"
                  placeholder="Search surah..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-64 rounded-full border border-gray-200 bg-gray-50 py-2 pl-10 pr-4 text-sm outline-none transition-all focus:border-blue-300 focus:ring-2 focus:ring-blue-100 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 dark:focus:border-blue-500 dark:focus:ring-blue-900"
                />
              </div>

              <IconButton
                onClick={() => dispatch(setGoToCertainPageDialogOpen(false))}
                className="rounded-full text-gray-500 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
              >
                <Close />
              </IconButton>
            </div>
          </div>

          <div className="flex flex-grow flex-col overflow-auto bg-gray-50 dark:bg-gray-900">
            {/* Bookmarks section */}
            {bookmarks.length > 0 && (
              <div className="p-4">
                <h3 className="mb-3 text-lg font-medium text-gray-800 dark:text-gray-200">
                  Your Bookmarks
                </h3>
                <div className="flex flex-wrap gap-2">
                  {bookmarks.map((bookmarkedPage) => (
                    <button
                      key={`bookmark-${bookmarkedPage}`}
                      type="button"
                      onClick={() => goToPage(bookmarkedPage)}
                      className="flex items-center rounded-lg border border-amber-200 bg-amber-50 px-3 py-2 text-amber-700 transition-colors hover:bg-amber-100 dark:border-amber-800 dark:bg-amber-900/30 dark:text-amber-300 dark:hover:bg-amber-800/50"
                    >
                      <Bookmark fontSize="small" className="mr-1" />
                      Page {bookmarkedPage}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Juz Selection */}
            <div className="p-4">
              <h3 className="mb-3 text-lg font-medium text-gray-800 dark:text-gray-200">
                <FormatListNumbered className="-mt-1 mr-2 inline text-blue-600 dark:text-blue-400" />
                Juz Selection
              </h3>
              <div className="grid grid-cols-3 gap-2 sm:grid-cols-5 md:grid-cols-6 lg:grid-cols-10">
                {Array.from({ length: 30 }, (_, i) => i + 1)?.map((o) => (
                  <button
                    key={o}
                    type="button"
                    onClick={() => {
                      if (o === 1) {
                        goToPage(1);
                        return;
                      }
                      goToPage((o - 1) * 20 + 2);
                    }}
                    className="rounded-lg border border-blue-100 bg-blue-50 p-2 text-center text-blue-700 transition-all hover:border-blue-200 hover:bg-blue-100 hover:shadow-sm dark:border-blue-800 dark:bg-blue-900/30 dark:text-blue-300 dark:hover:border-blue-700 dark:hover:bg-blue-800/50"
                  >
                    <span className="block text-lg font-medium">{o}</span>
                    <span className="text-xs text-blue-500 dark:text-blue-400">Juz</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Surah Selection */}
            <div className="flex-grow px-4 pb-8">
              <h3 className="mb-3 text-lg font-medium text-gray-800 dark:text-gray-200">
                <MenuBook className="-mt-1 mr-2 inline text-purple-600 dark:text-purple-400" />
                Surah Selection
              </h3>
              <div className="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
                {filteredSuraList?.map((o, i) => (
                  <button
                    key={i}
                    type="button"
                    onClick={() => goToPage(o?.pages[0])}
                    className="group flex items-center rounded-lg border border-gray-100 bg-white p-4 text-left transition-all hover:border-purple-200 hover:bg-purple-50/50 hover:shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:hover:border-purple-700 dark:hover:bg-purple-900/30"
                  >
                    <div className="mr-4 flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-br from-purple-500 to-blue-500 text-white">
                      <span className="font-medium">{o?.id}</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 group-hover:text-purple-700 dark:text-gray-100 dark:group-hover:text-purple-300">
                        {o?.name_simple}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {o?.translated_name?.name} • {o?.verses_count} verses
                      </p>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </Dialog>
    </div>
  );
}
