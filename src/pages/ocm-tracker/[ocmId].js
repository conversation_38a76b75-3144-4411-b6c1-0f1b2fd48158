// Next, React, Tw
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Packages
import moment from 'moment';

// Components
import Layout from '../../layouts/module/ocm-tracker';
import PapersListTable from '../../components/ocm-tracker/PapersListTable';

// Others
import { useSimiContext } from '../../utils/simi';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { fetchPapersData, fetchOcmsData } from '../../utils/store/ocmTrackerReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and
  const { query } = useRouter();
  const { ocmId } = query;
  const { getCertainStaffInfoFromStaffId } = useSimiContext();
  const dispatch = useDispatch();

  const [ocmData, setOcmData] = useState(null);
  const { ocmsList } = useSelector((state) => state.ocmTracker);

  // Others;

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    await dispatch(fetchOcmsData(`/ocm_id/${ocmId}`));
    dispatch(setIsLoading(false));
  };

  const fetchPapers = async () => {
    dispatch(setIsLoading(true));
    await fetchPapersData(`/ocm_id/${ocmId}`);
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (!ocmId) return;
    if (ocmsList?.length === 0) {
      fetchData();
      return;
    }
    setOcmData(ocmsList?.find((o) => o?.ocm_id === ocmId));
  }, [ocmId, ocmsList?.length]);

  useEffect(() => {
    if (!ocmId) return;
    fetchPapers();
  }, [ocmId]);

  return (
    <>
      <div className="bg-ocm-tracker px-6 py-4 text-white shadow-lg">
        <div className="flex flex-col justify-between gap-4 md:flex-row md:items-center md:gap-0">
          <div className="flex w-full items-center justify-between ">
            <p className="text-lg font-extrabold">{ocmData?.ocm_id}</p>
          </div>
        </div>
      </div>

      <div className="container mx-auto p-4 md:p-6">
        <div className="grid grid-cols-1 gap-6 ">
          <div className="rounded-md bg-white p-6 shadow-dashboardCard transition-shadow hover:shadow-lg dark:bg-gray-800 dark:text-white dark:shadow-dashboardCardDark">
            <h2 className="mb-4 border-b border-gray-200 pb-2 font-RobotoMedium text-xl font-bold dark:border-gray-700">
              OCM Information
            </h2>

            <div className="grid grid-cols-1 gap-x-4 md:grid-cols-3">
              {[
                {
                  label: 'Location',
                  value: ocmData?.location,
                },
                {
                  label: 'Time',
                  value: `${moment.unix(ocmData?.time_from)?.format('DD/MMM/YYYY hh:mm A')} - ${moment.unix(ocmData?.time_to)?.format('DD/MMM/YYYY hh:mm A')}`,
                },
                {
                  label: 'Created By',
                  value: getCertainStaffInfoFromStaffId(ocmData?.created_by_staff_id, 'name'),
                },
              ]?.map((o) => (
                <div className="mb-4">
                  <h3 className="text-xs font-bold uppercase text-gray-500 dark:text-gray-300">
                    {o?.label}
                  </h3>
                  <p className="mt-1 text-sm dark:text-white">{o?.value}</p>
                </div>
              ))}
            </div>
          </div>

          <div className="col-span-1 space-y-4 rounded-md bg-white p-6 shadow-dashboardCard transition-shadow hover:shadow-lg dark:bg-gray-800 dark:text-white dark:shadow-dashboardCardDark ">
            <p className="mb-4 border-b border-gray-200 pb-2 font-RobotoMedium text-xl font-bold dark:border-gray-700">
              Papers
            </p>
            <div className="flex w-full flex-col">
              <PapersListTable fetchData={fetchPapers} />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
