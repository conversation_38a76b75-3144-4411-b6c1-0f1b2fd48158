// Components
import Layout from '../../layouts/module/ocm-tracker';
import AdminDashboard from '../../components/ocm-tracker/AdminDashboard';
import UserDashboard from '../../components/ocm-tracker/UserDashboard';

// Others
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { isAdmin } = useModuleRoleContext();

  // Others

  return (
    <>
      <div className="bg-ocm-tracker px-4 py-2 text-white">
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-extrabold">Dashboard</p>
          </div>
        </div>
      </div>
      {isAdmin && <AdminDashboard />}
      {!isAdmin && <UserDashboard />}
    </>
  );
}
