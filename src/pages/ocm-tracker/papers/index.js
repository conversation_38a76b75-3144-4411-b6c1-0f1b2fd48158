// Next, React, Tw
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Components
import Layout from '../../../layouts/module/ocm-tracker';
import PapersListTable from '../../../components/ocm-tracker/PapersListTable';

// Others
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { fetchPapersData } from '../../../utils/store/ocmTrackerReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { q } = query;

  const { papersList } = useSelector((state) => state.ocmTracker);

  // Table

  const filteredTableData = (() => {
    const temp = papersList;
    if ([undefined, '']?.includes(q)) {
      return temp;
    }
    return temp.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    await dispatch(fetchPapersData(`/all/keyword`));
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div className="bg-ocm-tracker px-4 py-2 text-white">
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-extrabold">Papers List</p>
          </div>
        </div>
      </div>
      <PapersListTable
        key={filteredTableData?.length}
        tableData={filteredTableData}
        fetchData={fetchData}
      />
    </>
  );
}
