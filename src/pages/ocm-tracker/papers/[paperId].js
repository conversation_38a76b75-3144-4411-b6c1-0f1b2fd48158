// Next, React, Tw
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import { twMerge } from 'tailwind-merge';

// Mui
import { <PERSON>u, Stepper, Step, StepLabel, Tooltip } from '@mui/material';
import { Upload, FileDownload, Delete } from '@mui/icons-material';

// Packages
import { useSnackbar } from 'notistack';
import moment from 'moment';

// Components
import Layout from '../../../layouts/module/ocm-tracker';
import WarnBeforeActionPopupButton from '../../../components/Shared/WarnBeforeActionPopupButton';
import RemarkBox from '../../../components/Shared/RemarkBox';

// Others
import { useSimiContext } from '../../../utils/simi';
import {
  getStatusColor,
  OCM_TRACKER_ENDPOINT,
  STATUSES,
  useOcmTrackerContext,
  STRATEGY_TEAM_EMAILS,
} from '../../../utils/ocm-tracker';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import axios from '../../../utils/axios';
import { checkAndReplaceStringWithHyphen } from '../../../utils/shared';
import { fetchPapersData } from '../../../utils/store/ocmTrackerReducer';
import { useModuleRoleContext } from '../../../utils/auth/ModuleRoleProvider';
import { useAuthContext } from '../../../utils/auth/useAuthContext';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and
  const { query, asPath } = useRouter();
  const { paperId } = query;
  const { getCertainStaffInfoFromStaffId, moduleColorCode, handleSendEmail } = useSimiContext();
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const { paperSchema } = useOcmTrackerContext();
  const [anchorEl, setAnchorEl] = useState(null);
  const { isAdmin } = useModuleRoleContext();
  const { user } = useAuthContext();

  const [originalPaperData, setOriginalPaperData] = useState(null);
  const [paperData, setPaperData] = useState(null);
  const { papersList } = useSelector((state) => state.ocmTracker);
  const [ocmsList, setOcmsList] = useState([]);
  const [uploadedAttachmentsList, setUploadedAttachmentsList] = useState([]);

  // Table
  const bodyCellStyle = 'text-center py-1 text-sm';

  // Form
  const handleFormSubmit = async () => {
    let payload;
    try {
      payload = await paperSchema.validate(paperData, { abortEarly: false });
    } catch (error) {
      enqueueSnackbar(error.errors, {
        variant: 'error',
      });
      return;
    }

    try {
      const response = await axios.put(`${OCM_TRACKER_ENDPOINT}/paper/${payload?.id}`, payload);
      let statusVariant;
      let message;
      if (response.data.status === 'success') {
        statusVariant = 'success';
        message = response.data.data;
        if (paperData?.status !== originalPaperData?.status) {
          await handleSendEmail(
            [getCertainStaffInfoFromStaffId(paperData?.created_by_staff_id, 'email')],
            [user?.email],
            `${paperData?.paper_id}`,
            `                    
              <p>${paperData?.paper_id}'s status has been updated.</p>\n
              <p>Kindly, please click this <a href="${process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT}${asPath}">link</a> to review it.</p>
            `
          );
        }
      } else {
        statusVariant = 'error';
        message = 'Failed';
      }
      enqueueSnackbar(message, {
        variant: statusVariant,
      });
    } catch {
      enqueueSnackbar('Failed', {
        variant: 'error',
      });
    }
    fetchData();
  };

  // Others;

  const mandatoryAttachmentsList = (() => {
    const attachmentsList = ['Signed OCM Deck (PDF/PPT)', 'OCM Deck (PPT)'];
    if (paperData?.type === 'matters arising')
      ['FA Endorsement']?.map((o) => attachmentsList.push(o));
    if (paperData?.type === 'strategy') {
      if (['new', 'update']?.includes(paperData?.category))
        ['FA Endorsement']?.map((o) => attachmentsList.push(o));
    }
    if (paperData?.type === 'customer') {
      ['Internal Credit Rating']?.map((o) => attachmentsList.push(o));
      if (
        ['new', 'existing']?.includes(paperData?.category) &&
        !paperData?.title?.toLowerCase()?.includes('change')
      ) {
        ['Basis / Experian Report', 'BG Exemption Form']?.map((o) => attachmentsList.push(o));
        if (['Division Malaysia Carrier Sales']?.includes(user?.division))
          ['License (MCS customer - ASP/NSP/NFP)', 'e-STK (For MCS)']?.map((o) =>
            attachmentsList.push(o)
          );
      }
    }
    return attachmentsList?.map((o) => {
      const temp = uploadedAttachmentsList?.find((p) => p?.type === o);
      return {
        ...(temp || {}),
        type: o,
      };
    });
  })();

  const optionalAttachmentsList = uploadedAttachmentsList?.filter((o) => o?.type === 'other');

  // Attachments
  const handleActionButtonClick = async (action, data, event) => {
    dispatch(setIsLoading(true));
    if (action) {
      try {
        let response;
        switch (action) {
          case 'upload':
            response = await axios.post(
              `${OCM_TRACKER_ENDPOINT}/files/upload/${paperId}/${encodeURIComponent(data?.type)}`,
              { file: event.target.files[0] },
              {
                headers: {
                  'Content-Type': 'multipart/form-data',
                },
              }
            );
            await handleSendEmail(
              STRATEGY_TEAM_EMAILS,
              [user?.email],
              `${paperData?.paper_id}`,
              `                    
              <p>${paperData?.paper_id} has new attachment.</p>\n
              <p>Kindly, please click this <a href="${process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT}${asPath}">link</a> to review it.</p>
            `
            );
            break;
          case 'download':
            response = await axios.get(`${OCM_TRACKER_ENDPOINT}/files/download/${data?.id}`, {
              responseType: 'blob',
            });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(response?.data);
            link.download = data?.file_name;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(link.href);
            break;
          case 'delete':
            response = await axios.delete(`${OCM_TRACKER_ENDPOINT}/files/${data?.id}`);
            break;
          default:
            break;
        }
        enqueueSnackbar('Success');
      } catch {
        enqueueSnackbar('Something went wrong', {
          variant: 'error',
        });
      }
    }
    dispatch(setIsLoading(false));
    fetchAttachments();
  };

  const fetchAttachments = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${OCM_TRACKER_ENDPOINT}/files/${paperId}`);
      const temp =
        response?.data?.data?.map((o) => ({ ...o, type: decodeURIComponent(o?.type) })) || [];
      setUploadedAttachmentsList(temp);
    } catch {
      setUploadedAttachmentsList([]);
    }
    dispatch(setIsLoading(false));
  };

  const fetchOcms = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${OCM_TRACKER_ENDPOINT}/ocm/upcoming/keyword`);
      const temp = response?.data?.data || [];
      temp.reverse();
      setOcmsList(temp);
    } catch {
      setOcmsList([]);
    }
    dispatch(setIsLoading(false));
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    await dispatch(fetchPapersData(`/paper_id/${paperId}`));
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [paperId]);

  useEffect(() => {
    if (!paperId) return;
    if (papersList?.length === 0) {
      fetchData();
      return;
    }
    const temp = papersList?.find((o) => o?.paper_id === paperId) || null;
    setOriginalPaperData(temp);
    setPaperData(temp);
  }, [paperId, papersList?.length]);

  useEffect(() => {
    if (!paperId) return;
    fetchAttachments();
  }, [paperId]);

  useEffect(() => {
    fetchOcms();
  }, []);

  useEffect(() => {
    if (!paperData?.id) return;
    if (JSON?.stringify(paperData) !== JSON?.stringify(originalPaperData)) handleFormSubmit();
  }, [JSON?.stringify(paperData)]);

  return (
    <>
      <div className="bg-ocm-tracker px-6 py-4 text-white shadow-lg">
        <div className="flex flex-col justify-between gap-4 md:flex-row md:items-center md:gap-0">
          <div className="flex w-full items-center justify-between ">
            <p className="text-lg font-extrabold">{paperData?.paper_id}</p>
            <p
              className={twMerge(
                'ml-4 rounded-full px-3 py-1 text-xs font-bold',
                getStatusColor(paperData?.status)
              )}
            >
              {paperData?.status?.toUpperCase()}
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto flex w-full flex-col gap-4 p-4 md:p-6">
        <div className="w-full overflow-x-scroll md:overflow-x-auto">
          <Stepper
            activeStep={(() => STATUSES?.findIndex((o) => o === paperData?.status))()}
            alternativeLabel
          >
            {STATUSES.map((o, i) => (
              <Step
                key={i}
                sx={{
                  '& .MuiStepLabel-root .Mui-completed': {
                    color: moduleColorCode,
                  },
                  '& .MuiStepLabel-root .Mui-active': {
                    color: moduleColorCode,
                  },
                }}
              >
                <StepLabel>
                  <p className="whitespace-nowrap text-xs font-semibold text-black">
                    {o?.toUpperCase()}
                  </p>
                </StepLabel>
              </Step>
            ))}
          </Stepper>
        </div>
        <div className="flex w-full items-center justify-end gap-2">
          {isAdmin && (
            <>
              {paperData?.status !== 'rejected' && (
                <WarnBeforeActionPopupButton
                  onApprove={() => setPaperData({ ...paperData, status: 'rejected' })}
                >
                  <p className="cta-btn bg-red-500">Reject</p>
                </WarnBeforeActionPopupButton>
              )}

              <button
                type="button"
                className="bg-ocm-tracker cta-btn"
                onClick={(event) => setAnchorEl(event.currentTarget)}
              >
                Assign to OCM
              </button>
              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={() => setAnchorEl(null)}
                className="w-full"
              >
                <div className="flex w-full flex-col">
                  {ocmsList?.map((o) => (
                    <WarnBeforeActionPopupButton
                      positiveSentiment
                      onApprove={() => {
                        setPaperData({ ...paperData, ocm_id: o?.ocm_id, status: 'assigned' });
                        setAnchorEl(null);
                      }}
                    >
                      <p className="w-full px-4 text-xs hover:bg-gray-500 hover:text-white">
                        {o?.ocm_id} | {moment.unix(o?.time_from).format('DD/MMM/YYYY')}
                      </p>
                    </WarnBeforeActionPopupButton>
                  ))}
                </div>
              </Menu>
            </>
          )}
        </div>
        <div className="grid grid-cols-3 gap-6 ">
          <div className="col-span-2 rounded-md bg-white p-6 shadow-dashboardCard transition-shadow hover:shadow-lg dark:bg-gray-800 dark:text-white dark:shadow-dashboardCardDark">
            <p className="mb-4 border-b border-gray-200 pb-2 font-RobotoMedium text-xl font-bold dark:border-gray-700">
              Paper Information
            </p>

            <div className="grid grid-cols-1 gap-x-4 md:grid-cols-3">
              <div className="col-span-3 mb-4">
                <h3 className="text-xs font-bold uppercase text-gray-500 dark:text-gray-300">
                  Title
                </h3>
                <p className="mt-1 text-base dark:text-white">{paperData?.title}</p>
              </div>
              {[
                {
                  label: 'Type',
                  value: paperData?.type?.toUpperCase(),
                },
                {
                  label: 'Category',
                  value: paperData?.category?.toUpperCase(),
                },
                {
                  label: 'Created By',
                  value: getCertainStaffInfoFromStaffId(paperData?.created_by_staff_id, 'name'),
                },
                {
                  label: 'Division',
                  value: getCertainStaffInfoFromStaffId(paperData?.created_by_staff_id, 'division'),
                },
              ]?.map((o) => (
                <div className="mb-4">
                  <h3 className="text-xs font-bold uppercase text-gray-500 dark:text-gray-300">
                    {o?.label}
                  </h3>
                  <p className="mt-1 text-sm dark:text-white">{o?.value}</p>
                </div>
              ))}
            </div>
            {paperData?.ocm_id !== '' && (
              <>
                <p className="mb-4 border-b border-gray-200 pb-2 font-RobotoMedium text-xl font-bold dark:border-gray-700">
                  OCM Information
                </p>
                <div className="grid grid-cols-1 gap-x-4 md:grid-cols-3">
                  {[
                    {
                      label: 'Location',
                      value: ocmsList?.find((o) => o?.ocm_id === paperData?.ocm_id)?.location,
                    },
                    {
                      label: 'Date',
                      value: moment
                        ?.unix(ocmsList?.find((o) => o?.ocm_id === paperData?.ocm_id)?.time_from)
                        ?.format('DD/MMM/YYYY'),
                    },
                  ]?.map((o) => (
                    <div className="mb-4">
                      <h3 className="text-xs font-bold uppercase text-gray-500 dark:text-gray-300">
                        {o?.label}
                      </h3>
                      <p className="mt-1 text-sm dark:text-white">{o?.value}</p>
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
          <div className="col-span-1 rounded-md bg-white p-1 shadow-dashboardCard transition-shadow hover:shadow-lg dark:bg-gray-800 dark:text-white dark:shadow-dashboardCardDark">
            <RemarkBox
              key={paperData?.id}
              documentIdKey="paper_id"
              documentIdValue={paperData?.id}
              GET_ALL_REMARKS_ENDPOINT={`${OCM_TRACKER_ENDPOINT}/remarks/paper_id/${paperData?.id}`}
              POST_NEW_REMARK_ENDPOINT={`${OCM_TRACKER_ENDPOINT}/remarks`}
              CALLBACK_UPON_REMARK={() => {
                if (!isAdmin) return;
                setPaperData({ ...paperData, status: 'queried' });
              }}
            />
          </div>

          <div className="col-span-3 rounded-md bg-white p-6 shadow-dashboardCard transition-shadow hover:shadow-lg dark:bg-gray-800 dark:text-white dark:shadow-dashboardCardDark ">
            <p className="mb-4 border-b border-gray-200 pb-2 font-RobotoMedium text-xl font-bold dark:border-gray-700">
              Mandatory Attachments
            </p>
            <div className="flex w-full flex-col">
              <div className="overflow-x-auto scrollbar">
                <table className="min-w-full bg-white text-black">
                  <thead>
                    <tr>
                      {['No.', 'Type', 'File Name', ''].map((label, i) => (
                        <td
                          key={i}
                          className="bg-ocm-tracker whitespace-nowrap px-4 text-center text-white"
                        >
                          {label}
                        </td>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {mandatoryAttachmentsList?.map((row, i) => (
                      <tr
                        key={i}
                        className={twMerge(
                          'odd:bg-white even:bg-[#f8f8f8]',
                          !row?.file_name && 'bg-red-500'
                        )}
                      >
                        <td className={bodyCellStyle}>{i + 1}</td>
                        <td className={bodyCellStyle}>{row?.type}</td>
                        <td className={bodyCellStyle}>
                          {checkAndReplaceStringWithHyphen(row?.file_name)}
                        </td>
                        <td
                          className={twMerge(
                            bodyCellStyle,
                            'flex w-full items-center justify-center gap-2'
                          )}
                        >
                          {!row?.file_name && (
                            <>
                              <Tooltip title="Upload New">
                                <WarnBeforeActionPopupButton
                                  positiveSentiment
                                  onApprove={() =>
                                    document
                                      .getElementById(`ocm-tracker-paper-attachments-${i}`)
                                      .click()
                                  }
                                >
                                  <Upload className="text-ocm-tracker" />
                                </WarnBeforeActionPopupButton>
                              </Tooltip>
                              <input
                                id={`ocm-tracker-paper-attachments-${i}`}
                                type="file"
                                className="hidden"
                                onChange={(event) => handleActionButtonClick('upload', row, event)}
                              />
                            </>
                          )}

                          {row?.file_name && (
                            <>
                              <Tooltip title="Download">
                                <button
                                  type="button"
                                  onClick={(event) =>
                                    handleActionButtonClick('download', row, event)
                                  }
                                >
                                  <FileDownload className="text-green-500" />
                                </button>
                              </Tooltip>
                              <Tooltip title="Delete">
                                <WarnBeforeActionPopupButton
                                  onApprove={(event) =>
                                    handleActionButtonClick('delete', row, event)
                                  }
                                >
                                  <Delete className="text-red-500" />
                                </WarnBeforeActionPopupButton>
                              </Tooltip>
                            </>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
            <div className="flex w-full justify-between pt-16">
              <p className="mb-4 flex border-b border-gray-200 pb-2 font-RobotoMedium text-xl font-bold dark:border-gray-700">
                Optional Attachments
              </p>
              <button
                type="button"
                className="bg-ocm-tracker cta-btn"
                onClick={() =>
                  document.getElementById(`ocm-tracker-paper-attachments-other`).click()
                }
              >
                Upload New
              </button>
              <input
                id="ocm-tracker-paper-attachments-other"
                type="file"
                className="hidden"
                onChange={(event) => handleActionButtonClick('upload', { type: 'other' }, event)}
              />
            </div>

            <div className="flex w-full flex-col">
              <div className="overflow-x-auto scrollbar">
                <table className="min-w-full bg-white text-black">
                  <thead>
                    <tr>
                      {['No.', 'File Name', ''].map((label, i) => (
                        <td
                          key={i}
                          className="bg-ocm-tracker whitespace-nowrap px-4 text-center text-white"
                        >
                          {label}
                        </td>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {optionalAttachmentsList?.map((row, i) => (
                      <tr key={i} className="odd:bg-white even:bg-[#f8f8f8] ">
                        <td className={bodyCellStyle}>{i + 1}</td>
                        <td className={bodyCellStyle}>
                          {checkAndReplaceStringWithHyphen(row?.file_name)}
                        </td>
                        <td
                          className={twMerge(
                            bodyCellStyle,
                            'flex w-full items-center justify-center gap-2'
                          )}
                        >
                          {row?.file_name && (
                            <>
                              <Tooltip title="Download">
                                <button
                                  type="button"
                                  onClick={(event) =>
                                    handleActionButtonClick('download', row, event)
                                  }
                                >
                                  <FileDownload className="text-green-500" />
                                </button>
                              </Tooltip>
                              <Tooltip title="Delete">
                                <WarnBeforeActionPopupButton
                                  onApprove={(event) =>
                                    handleActionButtonClick('delete', row, event)
                                  }
                                >
                                  <Delete className="text-red-500" />
                                </WarnBeforeActionPopupButton>
                              </Tooltip>
                            </>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
