// Next, React, Tw
import { useRouter } from 'next/router';
import { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import dynamic from 'next/dynamic';

// Mui
import { Stepper, Step, StepLabel, Tooltip } from '@mui/material';
import { Info } from '@mui/icons-material';

// Packages
import * as yup from 'yup';
import * as R from 'ramda';

// Components
import Layout from '../../layouts/module/support';
import { TextInput, SelectInput, AutoCompleteTextInput } from '../../components/Shared/CustomInput';
import AttachmentBox from '../../components/Shared/AttachmentBox';
import HistoryBox from '../../components/Shared/HistoryBox';
import RemarkBox from '../../components/Shared/RemarkBox';
import { useSnackbar } from '../../components/Shared/snackbar';
import SelectRemoveStaffListContainer from '../../components/Shared/SelectRemoveStaffListContainer';

// Others
import { RM_ENDPOINT, useSupportContext } from '../../utils/support';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { getColorCode, getModuleFromPath } from '../../utils/shared';
import { useSimiContext } from '../../utils/simi';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const HtmlEditor = dynamic(() => import('../../components/Shared/HtmlEditor'), {
    ssr: false,
  });
  const { enqueueSnackbar } = useSnackbar();
  const { query, asPath } = useRouter();
  const module = getModuleFromPath(asPath);
  const { ticketId } = query;
  const dispatch = useDispatch();
  const hiddenDivRef = useRef(null);
  const { allStaffs } = useSelector((state) => state.aum);
  const { user } = useAuthContext();
  const { handleCreateHistory } = useSupportContext();
  const { handleSendEmail, getCertainStaffInfoFromStaffId } = useSimiContext();

  const STATUSES = ['pending', 'queued', 'ongoing', 'completed', 'escalated', 'rejected'];

  const [originalTicketData, setOriginalTicketData] = useState({});
  const [ticketData, setTicketData] = useState({});

  // Form
  const schema = yup.object({
    module: yup.string().required(`Please provide module`)?.default(module),
    summary: yup.string().required(`Please provide title`),
    description: yup.string().required(`Please provide description`),
    requestor_staff_id: yup.string().required(`Please provide requestor`).default(user?.staff_id),
    assignee_staff_id: yup.string().required(`Please provide assignee`),
  });

  const handleFormDataChange = (event) => {
    let name, value;

    // Handle both event objects and direct values from AutoCompleteTextInput
    if (event && typeof event === 'object' && event.target) {
      // Standard event object from regular inputs
      ({ name, value } = event.target);
    } else if (event && typeof event === 'object' && event.name !== undefined) {
      // Direct object with name and value properties
      ({ name, value } = event);
    } else {
      // Handle cases where event might be undefined or malformed
      console.error('Invalid event object passed to handleFormDataChange:', event);
      return;
    }

    setTicketData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const handleDialogClose = async (action) => {
    if (!action) {
      fetchData();
      return;
    }

    let payload;
    try {
      payload = await schema.validate(ticketData, { abortEarly: false });
    } catch (error) {
      const errorMessage = error.errors ? error.errors.join(', ') : 'Validation failed';
      enqueueSnackbar(errorMessage, {
        variant: 'error',
      });
      return;
    }

    try {
      let response;
      switch (action) {
        case 'put':
          response = await axios.put(`${RM_ENDPOINT}/${payload?.id}`, payload);
          break;
        case 'delete':
          response = await axios.delete(`${RM_ENDPOINT}/${ticketData?.id}`);
          break;
        default:
          break;
      }
      let statusVariant;
      let message;
      if (response.data.status === 'success') {
        statusVariant = 'success';
        message = response.data.data;
        switch (action) {
          case 'put':
            await handleCreateHistory(
              ticketData?.id,
              `Ticket status updated to ${payload?.status}.`
            );
            const assigneeEmail = allStaffs?.find(
              (o) => o?.staff_id === payload?.assignee_staff_id
            )?.email;
            const requestorEmail = allStaffs?.find(
              (o) => o?.staff_id === payload?.requestor_staff_id
            )?.email;
            const spectatorsEmail = (payload?.spectators_staff_id || [])
              ?.map((o) => allStaffs?.find((p) => p?.staff_id === o)?.email)
              ?.filter(Boolean); // Remove undefined emails
            // Only send email if we have valid email addresses
            const primaryEmail = originalTicketData?.assignee_staff_id !== payload?.assignee_staff_id
              ? assigneeEmail
              : requestorEmail;
            const ccEmails = [
              originalTicketData?.assignee_staff_id !== payload?.assignee_staff_id
                ? requestorEmail
                : assigneeEmail,
              ...spectatorsEmail,
            ].filter(Boolean); // Remove undefined emails

            if (primaryEmail) {
              await handleSendEmail(
                [primaryEmail],
                ccEmails,
                ticketData?.ticket_id,
                `
                    <p>${ticketData?.ticket_id} has been updated.</p>\n
                    <p>Kindly, please click this <a href="${process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT}${asPath}">link</a> to review it.</p>
                `,
                enqueueSnackbar
              );
            }
            if (window) window?.location?.reload();
            break;
          default:
            break;
        }
      } else {
        statusVariant = 'error';
        message = 'Failed';
      }
      enqueueSnackbar(message, {
        variant: statusVariant,
      });
    } catch (error) {
      console.error('Error in handleDialogClose:', error);
      enqueueSnackbar('An error occurred while processing the request', {
        variant: 'error',
      });
    }

    fetchData();
  };

  // Others

  const fetchData = async () => {
    if (!ticketId) return;

    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${RM_ENDPOINT}/id/${ticketId}`);
      if (response?.data?.data?.[0]) {
        setOriginalTicketData(response?.data?.data?.[0]);
        setTicketData(response?.data?.data?.[0]);
      } else {
        // Handle case where no data is returned
        setOriginalTicketData({});
        setTicketData({});
      }
    } catch (error) {
      console.error('Error fetching ticket data:', error);
      setOriginalTicketData({});
      setTicketData({});
      enqueueSnackbar('Failed to fetch ticket data', { variant: 'error' });
    }

    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [ticketId]); // fetchData is stable since it only depends on ticketId

  return (
    <>
      <div ref={hiddenDivRef} />
      <div className="bg-support px-4 py-2 text-white">
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-center text-lg font-extrabold md:text-left">
              {ticketData?.ticket_id}
            </p>
          </div>
        </div>
      </div>
      <div className="container mx-auto flex flex-col gap-4 p-1 md:p-4">
        <div className="w-full overflow-x-scroll md:overflow-x-auto">
          <Stepper
            activeStep={STATUSES?.findIndex((o) => o === originalTicketData?.status) ?? 0}
            alternativeLabel
          >
            {[
              ...STATUSES?.slice(0, 3),
              STATUSES?.slice(3)
                ?.reduce((prev, curr) => `${prev}/${curr}`, '')
                ?.slice(1),
            ].map((o, i) => (
              <Step
                key={i}
                sx={{
                  '& .MuiStepLabel-root .Mui-completed': {
                    color: getColorCode(module),
                  },
                  '& .MuiStepLabel-root .Mui-active': {
                    color: getColorCode(module),
                  },
                }}
              >
                <StepLabel>
                  <p className="text-xs font-semibold text-black">{o?.toUpperCase()}</p>
                </StepLabel>
              </Step>
            ))}
          </Stepper>
        </div>
        <div className="flex w-full flex-col gap-4 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
          <div className="mx-auto flex w-full flex-col gap-4 md:w-1/2">
            <TextInput value={ticketData?.module || ''} placeholder="Module" disabled />
            <TextInput
              name="summary"
              value={ticketData?.summary || ''}
              placeholder="Summary"
              onChange={handleFormDataChange}
              showRedAsteric
            />
            <HtmlEditor
              name="description"
              value={ticketData?.description || ''}
              placeholder="Description"
              onChange={(event) => {
                // HtmlEditor might pass different event formats, handle gracefully
                if (typeof event === 'string') {
                  handleFormDataChange({ name: 'description', value: event });
                } else {
                  handleFormDataChange(event);
                }
              }}
              disabled
            />
            <div className="w-full rounded-md border border-[#e5e7eb] bg-white px-2">
              <div className="flex justify-between">
                <p className="text-xs">Spectators</p>
                <Tooltip title="Select those will also be notified about this ticket">
                  <Info className="cursor-pointer text-xs" />
                </Tooltip>
              </div>

              <SelectRemoveStaffListContainer
                key={ticketId}
                INITIAL_STAFF_LIST={ticketData?.spectators_staff_id}
                KEY="staff_id"
                CALLBACK_FUNCTION={(selectedStaffList) => {
                  setTicketData({
                    ...ticketData,
                    spectators_staff_id: R.pluck('staff_id')(selectedStaffList),
                  });
                }}
              />
            </div>
            {user?.isSuperAdmin && (
              <>
                <p className="text-center text-sm font-semibold">Superadmin Area</p>
                <SelectInput
                  name="status"
                  value={ticketData?.status || ''}
                  placeholder="Status"
                  options={STATUSES}
                  onChange={handleFormDataChange}
                  showRedAsteric
                />
                {/* Only render AutoCompleteTextInput when allStaffs data is available */}
                {allStaffs && allStaffs.length > 0 && (
                  <AutoCompleteTextInput
                    name="assignee_staff_id"
                    value={ticketData?.assignee_staff_id || ''}
                    placeholder="Assignee"
                    options={allStaffs.map((o) => ({ value: o?.staff_id, label: o?.name }))}
                    onChange={(value) => handleFormDataChange({ name: 'assignee_staff_id', value })}
                  />
                )}
              </>
            )}
            {JSON?.stringify(ticketData) !== JSON?.stringify(originalTicketData) && (
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-support cta-btn"
                >
                  Save
                </button>
              </div>
            )}
          </div>
        </div>
        {/* Only render these components when ticketData.id is available */}
        {ticketData?.id && (
          <div className="flex flex-col gap-4 md:flex-row">
            <div className="flex w-full flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white md:w-1/3">
              <AttachmentBox
                key={ticketData?.id}
                TITLE="Attachment"
                GET_ALL_FILES_ENDPOINT={`${RM_ENDPOINT}/files/ticket/${ticketData?.id}`}
                UPLOAD_CERTAIN_FILE_ENDPOINT={`${RM_ENDPOINT}/files/upload/ticket/${ticketData?.id}`}
                DOWNLOAD_CERTAIN_FILE_ENDPOINT={`${RM_ENDPOINT}/files/download`}
                DELETE_CERTAIN_FILE_ENDPOINT={`${RM_ENDPOINT}/files`}
              />
            </div>
            <div className="flex w-full flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white md:w-1/3">
              <HistoryBox
                key={ticketData?.id}
                parentHiddenDivRef={hiddenDivRef}
                GET_ALL_HISTORIES_ENDPOINT={`${RM_ENDPOINT}/histories/ticket_id/${ticketData?.id}`}
              />
            </div>

            <div className="flex w-full flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white md:w-1/3">
              <RemarkBox
                key={ticketData?.id}
                parentHiddenDivRef={hiddenDivRef}
                documentIdKey="ticket_id"
                documentIdValue={ticketData?.id}
                GET_ALL_REMARKS_ENDPOINT={`${RM_ENDPOINT}/remarks/ticket_id/${ticketData?.id}`}
                POST_NEW_REMARK_ENDPOINT={`${RM_ENDPOINT}/remarks`}
              />
            </div>
          </div>
        )}
      </div>
    </>
  );
}
