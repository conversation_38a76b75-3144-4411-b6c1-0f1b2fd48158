// Next, React, Tw
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { twMerge } from 'tailwind-merge';
import { useDispatch, useSelector } from 'react-redux';
import dynamic from 'next/dynamic';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { Edit, Visibility } from '@mui/icons-material';

// Packages
import * as yup from 'yup';
import * as R from 'ramda';

// Components
import { TablePaginationCustom } from '../../components/Shared/table';
import { useSnackbar } from '../../components/Shared/snackbar';
import Layout from '../../layouts/module/support';
import {
  TextInput,
  SelectInput,
  SearchInput,
  BinarySwitchInput,
} from '../../components/Shared/CustomInput';
import UserPopup from '../../components/Shared/UserPopup';
import ReactAnimatedNumber from '../../components/Shared/ReactAnimatedNumber';
import WarnBeforeActionPopupButton from '../../components/Shared/WarnBeforeActionPopupButton';

// Others
import { RM_ENDPOINT, useSupportContext } from '../../utils/support';
import axios from '../../utils/axios';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { toUpperCaseFirstLetter, ALL_MODULES } from '../../utils/shared';
import { useSimiContext } from '../../utils/simi';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useParamContext } from '../../utils/auth/ParamProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { query, push } = useRouter();
  const { q, module, status, assigneeStaffId, dialogOpen } = query;
  const { user } = useAuthContext();
  const HtmlEditor = dynamic(() => import('../../components/Shared/HtmlEditor'), {
    ssr: false,
  });

  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const { setParam, replaceParam } = useParamContext();
  const { page, rowsPerPage } = useSelector((state) => state.simi);
  const { allStaffs } = useSelector((state) => state.aum);

  const { handleCreateHistory } = useSupportContext();
  const { handleSendEmail } = useSimiContext();

  // Table
  const [tableData, setTableData] = useState([]);
  const filteredTableData = (() => {
    let temp = tableData;
    if (status !== 'all') {
      if (status === 'incomplete')
        temp = tableData.filter((o) => !['completed', 'rejected', 'escalated']?.includes(o.status));
      else temp = tableData.filter((o) => o?.status === status);
    }
    if (assigneeStaffId !== 'all')
      temp = temp.filter((o) => o.assignee_staff_id === assigneeStaffId);
    if ([undefined, '']?.includes(q)) return temp;
    return temp.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  })();

  const bodyCellStyle = 'text-center whitespace-nowrap py-1 text-xs ';

  // Dialog
  const [editModeDialog, setEditModeDialog] = useState(false);
  const schema = yup.object({
    module: yup.string().required(`Please provide module`)?.default(module),
    summary: yup.string().required(`Please provide title`),
    description: yup.string().required(`Please provide description`),
    requestor_staff_id: yup.string().required(`Please provide requestor`).default(user?.staff_id),
  });
  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }
    setEditModeDialog(editMode);
    replaceParam({ dialogOpen: 'true' });
  };

  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }

      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${RM_ENDPOINT}`, payload);
            break;
          case 'delete':
            response = await axios.delete(`${RM_ENDPOINT}/${dialogData?.id}`);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
          switch (action) {
            case 'post':
              const ticketId = response?.data?.data?.split(' ')?.[2];
              await handleCreateHistory(ticketId, 'Ticket is created.');
              await handleSendEmail(
                ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                [allStaffs?.find((o) => o?.staff_id === payload?.requestor_staff_id)?.email],
                'New Ticket Created',
                `
                  <p>New Ticket Created.</p>\n
                  <p>Kindly, please click this <a href="${process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT}/support/${ticketId}">link</a> to review it.</p>\n
                `
              );
              push(`/support/${ticketId}`);
              return;
            default:
              break;
          }
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        /* empty */
      }
    }
    setDialogData({});
    replaceParam({ dialogOpen: undefined });
    fetchData();
  };

  // Others

  const uniqueStatuses = R.uniq(R.pluck('status', tableData));

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    let temp = [];
    let temp2 = [];
    try {
      let param = 'all/keyword';
      if (!user?.isSuperAdmin) param = `requestor_staff_id/${user?.staff_id}`;
      const response = await axios.get(`${RM_ENDPOINT}/${param}`);
      response?.data?.data?.reverse();
      temp = response?.data?.data || [];
    } catch {
      temp = [];
    }

    if (!user?.isSuperAdmin) {
      try {
        const response = await axios.get(`${RM_ENDPOINT}/spectators_staff_id/${user?.staff_id}`);
        response?.data?.data?.reverse();
        temp2 = response?.data?.data || [];
      } catch {
        temp2 = [];
      }
    }

    setTableData([...temp, ...temp2]);

    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (!status) replaceParam({ status: 'incomplete', assigneeStaffId: 'all' });
  }, [status, assigneeStaffId]);

  return (
    <>
      <div className="bg-support px-4 py-2 text-white">
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-extrabold">My Ticket</p>
          </div>
        </div>
      </div>
      <div className="container mx-auto p-1 md:p-4">
        {user?.isSuperAdmin && (
          <div className="mx-auto my-8 flex w-full flex-col items-center justify-between gap-8 md:w-1/2 md:flex-row">
            {[
              {
                assignee_name: 'Rahman',
                assignee_staff_id: 's54458',
              },
              {
                assignee_name: 'Azim',
                assignee_staff_id: 'tm34264',
              },
            ]?.map((o, i) => (
              <div
                key={i}
                className={twMerge(
                  'bg-support flex w-full flex-col gap-2 rounded-md p-2 text-center text-white md:w-1/3',
                  o?.assignee_staff_id === assigneeStaffId && 'brightness-150'
                )}
              >
                <button
                  type="button"
                  onClick={() =>
                    setParam({
                      assigneeStaffId:
                        assigneeStaffId === o?.assignee_staff_id ? 'all' : o?.assignee_staff_id,
                    })
                  }
                >
                  <p className="text-xs font-bold">{o?.assignee_name}</p>
                  <p className="text-4xl">
                    <ReactAnimatedNumber
                      value={
                        tableData?.filter((p) => p?.assignee_staff_id === o?.assignee_staff_id)
                          ?.length
                      }
                      formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                    />
                  </p>
                </button>
              </div>
            ))}
          </div>
        )}

        <div className="flex flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
          <div className="flex flex-col items-center justify-center gap-4 md:flex-row md:justify-between md:gap-0">
            <div className="flex flex-col items-center gap-2 md:flex-row">
              <div className="w-full md:w-[200px]">
                <SelectInput
                  value={status}
                  placeholder="Filter Status"
                  options={['all', ...uniqueStatuses]}
                  onChange={(event) => setParam({ status: event?.target?.value })}
                />
              </div>
              <SearchInput />
              <div className="w-full md:w-[100px]">
                <BinarySwitchInput
                  value={status === 'incomplete'}
                  placeholder="Incomplete"
                  onChange={(event) =>
                    setParam({ status: event.target.value ? 'incomplete' : 'all' })
                  }
                  showRedAsteric={false}
                />
              </div>
            </div>
            <button
              type="button"
              className="bg-support cta-btn"
              onClick={() => handleClickOpenDialog(false)}
            >
              New Ticket
            </button>
          </div>
          <div className="mt-4 overflow-x-auto scrollbar">
            <table className="min-w-full">
              <thead>
                <tr>
                  {[
                    'No.',
                    'Module',
                    'Summary',
                    'Requestor',
                    'Assignee',
                    'Status',
                    'Created At',
                    '',
                  ].map((label, i) => (
                    <td
                      key={i}
                      className="bg-support whitespace-nowrap py-1 text-center text-sm text-white"
                    >
                      {label}
                    </td>
                  ))}
                  {user?.isSuperAdmin && (
                    <td className="bg-support whitespace-nowrap py-1 text-center text-sm text-white">
                      Action
                    </td>
                  )}
                </tr>
              </thead>
              <tbody>
                {filteredTableData
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((o, i) => (
                    <tr key={i} className={`odd:bg-white even:bg-[#f8f8f8] `}>
                      <td className={bodyCellStyle}>{i + 1 + rowsPerPage * page}</td>
                      <td className={bodyCellStyle}>{o?.module.toUpperCase()}</td>
                      <td
                        className={twMerge(
                          bodyCellStyle,
                          'max-w-[400px] overflow-hidden overflow-ellipsis text-left'
                        )}
                      >
                        {o?.summary}
                      </td>
                      <td className={bodyCellStyle}>
                        <UserPopup
                          label={o?.requestor_staff_id.toUpperCase()}
                          staff_id={o?.requestor_staff_id}
                        />
                      </td>
                      <td className={bodyCellStyle}>
                        <UserPopup
                          label={o?.assignee_staff_id.toUpperCase()}
                          staff_id={o?.assignee_staff_id}
                        />
                      </td>
                      <td className={bodyCellStyle}>{toUpperCaseFirstLetter(o?.status)}</td>
                      <td className={bodyCellStyle}>{o?.created_at}</td>
                      <td className={twMerge(bodyCellStyle, 'flex justify-center gap-2 px-2')}>
                        <button type="button" onClick={() => handleClickOpenDialog(true, o)}>
                          <Edit className="text-support" />
                        </button>
                        <button type="button" onClick={() => push(`/support/${o?.id}`)}>
                          <Visibility className="text-support" />
                        </button>
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
          <TablePaginationCustom count={filteredTableData.length} />
        </div>
      </div>

      <Dialog fullScreen open={dialogOpen === 'true'} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-support">
          <p className="text-center text-white">{!editModeDialog ? 'New' : 'Edit'} Ticket</p>
        </DialogTitle>
        <DialogContent>
          <div className="mx-auto flex w-2/3 flex-col gap-2 px-2 py-4">
            <SelectInput
              value={module}
              placeholder="Module"
              options={R.pluck('module')(ALL_MODULES).map((o, i) => o)}
              onChange={(event) => setParam({ module: event?.target?.value })}
              disabled={editModeDialog && !user?.isSuperAdmin}
            />
            <TextInput
              name="summary"
              value={dialogData?.summary}
              placeholder="Summary"
              onChange={handleDialogDataChange}
              disabled={editModeDialog && !user?.isSuperAdmin}
            />
            <HtmlEditor
              name="description"
              value={dialogData?.description}
              placeholder="Description"
              onChange={handleDialogDataChange}
              disabled={editModeDialog && !user?.isSuperAdmin}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full items-center justify-between">
            <div className="flex gap-2">
              <button type="button" onClick={() => handleDialogClose(false)} className="">
                Close
              </button>
              {editModeDialog && (
                <WarnBeforeActionPopupButton onApprove={() => handleDialogClose('delete')} />
              )}
            </div>
            <div className="flex gap-2">
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-support cta-btn"
                >
                  Create
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-support cta-btn"
                >
                  Save
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
}
