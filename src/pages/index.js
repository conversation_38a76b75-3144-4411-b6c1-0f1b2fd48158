// Next, React, Tw
import { useEffect } from 'react';
import { useRouter } from 'next/router';

// Packages
import { m } from 'framer-motion';

export default function Page() {
  // Standard and Vars
  const { push } = useRouter();

  // Others
  useEffect(() => {
    const redirect = async () => {
      await new Promise((r) => setTimeout(r, 1000));
      push('/dashboard');
    };
    redirect();
  }, []);

  return (
    <div className="flex min-h-screen w-full items-center justify-center bg-white dark:bg-gray-800">
      <m.img
        src="/assets/logo/New-SIMI-Logo.svg"
        alt="SIMI Logo"
        width={100}
        height={100}
        loading="eager"
        className=" rounded-md dark:bg-white dark:shadow-dashboardCardDark"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      />
    </div>
  );
}
