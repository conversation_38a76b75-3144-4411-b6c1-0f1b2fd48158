// Next, React, Tw
import Link from 'next/link';
import Image from 'next/image';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Mui
import { useMediaQuery } from '@mui/material';

// Components
import { getEmail, getPhone, getDesignation } from '../../utils/dashboard';
import Layout from '../../layouts/dashboard';
import QRBox from '../../components/Shared/QRBox';

// Others
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { AUM_ENDPOINT } from '../../utils/aum';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const { user } = useAuthContext();
  const isSmallScreen = useMediaQuery('(max-width: 768px)');
  const dispatch = useDispatch();

  const [userData, setUserData] = useState({});

  // Others

  const getNickName = () => {
    if (userData?.nick_name !== '') {
      return `NICKNAME:${userData?.nick_name}\n`;
    }
    return '';
  };

  const getCompany = () => {
    if (userData?.use_alt_company) {
      return userData?.alt_company;
    }
    return 'Telekom Malaysia Berhad';
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${AUM_ENDPOINT}/user/v1/${user?.staff_id}`);
      setUserData(response.data.data[response.data.data.length - 1]);
    } catch (error) {
      // console.error('Error fetching data:', error);
    }

    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (!user?.staff_id) return;
    fetchData();
  }, [user?.staff_id]);

  return (
    <div className="container relative h-full p-1 md:p-20">
      <div className="relative flex h-full w-full flex-col-reverse bg-white shadow-dashboardCard dark:shadow-dashboardCardDark md:flex-row">
        <div className="flex h-1/2 w-full flex-col justify-end md:h-auto md:w-1/2">
          <div className="mt-0 flex w-full flex-grow items-center justify-center gap-2 pl-4 pr-2 md:mt-8 md:gap-4">
            {userData?.profile_picture !== '' && (
              <img
                src={userData?.profile_picture}
                alt="Profile"
                width={200}
                height={200}
                className="h-[150px] w-[150px] md:h-[200px] md:w-[200px]"
              />
            )}
            <div className="flex flex-col justify-center gap-[0.1rem]">
              <p className="font-HkGrotesk text-xl font-extrabold text-[#1800e6] md:text-3xl lg:text-4xl">{`HELLO I'M`}</p>
              <p className="lg:5xl font-HkGrotesk text-2xl font-extrabold text-[#ff7a00] md:text-4xl">
                {userData?.nick_name !== ''
                  ? userData?.nick_name?.toUpperCase()
                  : userData?.name.split(' ')[0]?.toUpperCase()}
              </p>
              <p className="lg:text-md font-RobotoMedium text-xs font-medium text-black md:text-sm">
                {userData?.name}
              </p>
              <p className="lg:text-md font-RobotoRegular text-xs text-gray-500 md:text-sm">
                {getDesignation(userData)}
              </p>
              {getPhone(userData) !== '' && (
                <p className="lg:text-md font-RobotoRegular text-xs text-gray-500 md:text-sm">
                  {getPhone(userData)}
                </p>
              )}
              <p className="lg:text-md font-RobotoRegular text-xs text-gray-500 md:text-sm">
                {getEmail(userData)}
              </p>
              <p className="lg:text-md font-RobotoRegular text-xs text-gray-500 md:text-sm">
                tmglobal.com.my
              </p>
            </div>
          </div>

          <div className="flex w-full items-end justify-between">
            <Image
              src="/dashboard/digital-card-bottom.svg"
              alt="Bottom Left Icon"
              width={200}
              height={50}
              loading="eager"
              priority
              className=" w-1/3"
            />
            <Image
              src="/login/tmglobal with tagline.webp"
              alt="Bottom Right Icon"
              width={200}
              height={50}
              loading="eager"
              priority
              className="mb-3 mr-3  w-1/3 md:mb-6"
            />
          </div>
        </div>
        <div className="relative flex h-1/2 w-full justify-center bg-[#16018C] md:h-auto md:w-1/2">
          <div className="flex h-full flex-col items-center justify-center gap-1 p-4  md:justify-center md:p-16">
            <QRBox
              value={`BEGIN:VCARD\nVERSION:3.0\nN:;${userData?.name?.trim()}\n${getNickName()}ORG:${getCompany()}\nTEL;CELL:${getPhone(
                userData
              )}\nEMAIL;WORK;INTERNET:${getEmail(userData)}\nEND:VCARD`}
              size={Number(`${!isSmallScreen ? 300 : 225}`)}
            />
            <p className="font-HkGrotesk text-lg font-extrabold text-white md:text-2xl">{`LET'S GET IN TOUCH`}</p>
          </div>
        </div>
        <Image
          src="/dashboard/digital-card-middle.svg"
          alt="Middle Icon for Small Screen"
          width={75}
          height={75}
          loading="eager"
          priority
          className="absolute right-0 top-1/2 -translate-x-0 -translate-y-1/2 transform md:right-1/2 md:top-0 md:translate-x-1/2 md:translate-y-0"
        />
      </div>
      <Link
        href={`/dashboard/view-user/${userData?.staff_id}?dialogOpen=true`}
        className="absolute right-0 top-0 m-4 hidden text-xs font-semibold text-white hover:underline md:block md:text-sm md:text-blue-500 md:dark:text-white"
      >
        Edit
      </Link>
    </div>
  );
}
