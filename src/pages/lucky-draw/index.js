// Next, React, Tw
import { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Mui
import { Tooltip, Drawer, Switch, Dialog, DialogTitle, DialogContent, DialogActions, Button } from '@mui/material';
import { Settings } from '@mui/icons-material';

// Packages
import { m, AnimatePresence } from 'framer-motion';
import * as rando from 'random-number-in-range';

// Components
import Layout from '../../layouts/module/lucky-draw';
import ImagePicker from '../../components/Shared/ImagePicker';
import UploadCsvButton from '../../components/Shared/UploadCsv';

// Others
import { localStorageAvailable } from '../../utils/shared';
import { setNameList } from '../../utils/store/luckyDrawReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const storageAvailable = localStorageAvailable();
  const dispatch = useDispatch();

  const [bg, setBg] = useState(null);
  const [title, setTitle] = useState('LUCKY DRAW');
  const audioRef = useRef();
  const [settingsDrawerIsOpen, setSettingsDrawerIsOpen] = useState(false);
  const [intervalId, setIntervalId] = useState(null);
  const { nameList } = useSelector((state) => state?.luckyDraw);
  const [namePosInNameList, setNamePosInNameList] = useState(0);
  const [playMusic, setPlayMusic] = useState(true);
  const [isDrawing, setIsDrawing] = useState(false);
  const [isInitial, setIsInitial] = useState(true);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Others

  const getBg = () =>
    bg || '/events/TM-Global-Raya-Aidilfitri-2025/poster.png';

  const handleDrawButtonClick = () => {
    let nameListLength = nameList?.length;
    if (isInitial) {
      setIsInitial(false);
    } else {
      const temp = [...nameList];
      temp.splice(namePosInNameList, 1);
      dispatch(setNameList(temp));
      if (storageAvailable) localStorage.setItem('luckyDrawNameList', JSON?.stringify(temp));
      nameListLength -= 1;
      setNamePosInNameList(() => 0);
    }

    // Disable all other button
    setIsDrawing(true);

    // Play Audio
    if (playMusic) {
      audioRef.current.currentTime = 0;
      audioRef.current.play();
    }

    // Role the Name
    const id = setInterval(() => {
      setNamePosInNameList((prev) => {
        if (prev === nameListLength - 1) {
          return 0;
        }
        return prev + 1;
      });
    }, 100);
    setIntervalId(id);

    // Stop Rolling

    setTimeout(() => {
      setNamePosInNameList(rando(0, nameListLength));
      clearInterval(id);
      setIsDrawing(false);
    }, 3000);
  };

  useEffect(() => {
    if (storageAvailable && localStorage.getItem('luckyDrawBg')) {
      setBg(localStorage.getItem('luckyDrawBg'));
    }
  }, []);

  useEffect(
    () => () => {
      clearInterval(intervalId);
    },
    [intervalId]
  );

  useEffect(() => {
    if (nameList?.length === 0 && storageAvailable && localStorage.getItem('luckyDrawNameList')) {
      dispatch(setNameList(JSON.parse(localStorage.getItem('luckyDrawNameList'))));
    }
  }, []);

  return (
    <>
      {/* eslint-disable-next-line */}
      <audio ref={audioRef} controls className="hidden">
        <source src="/lucky-draw/drum-roll-please-6386.mp3" type="audio/mpeg" />
      </audio>
      <div
        className="flex h-full w-full items-center justify-center bg-cover bg-center"
        style={{ backgroundImage: `url(${getBg()})` }}
      >
        <div className="container mx-auto flex w-full flex-col items-center gap-3 rounded-md py-8 px-4 mt-[28rem]">
          <div className="bg-black bg-opacity-30 px-8 py-4 rounded-xl">
            <p className="text-center text-4xl font-extrabold text-white outline-2 outline-[#ffbf20] [text-shadow:_2px_2px_4px_rgb(0_0_0_/_100%)]">
              {title?.toUpperCase()}
            </p>
          </div>
          
          <div className="flex w-4/5 justify-center rounded-xl bg-white shadow-lg p-6 text-lg md:text-xl lg:text-2xl xl:text-3xl">
            {isInitial ? (
              <div className="flex flex-col items-center gap-2">
                <p className="text-gray-800 font-bold">Ready to start the lucky draw!</p>
                <p className="text-sm text-gray-500">
                  {nameList.length < 2 
                    ? `Need at least 2 participants. Current count: ${nameList.length}` 
                    : `${nameList.length} participants ready`}
                </p>
              </div>
            ) : (
              <AnimatePresence>
                <m.p
                  key={namePosInNameList}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="w-full overflow-hidden overflow-ellipsis whitespace-nowrap text-center font-bold"
                >
                  {nameList[namePosInNameList]}
                </m.p>
              </AnimatePresence>
            )}
          </div>
          
          <div className="flex flex-col items-center gap-4">
            <button
              type="button"
              className="rounded-xl bg-[#ffbf20] p-4 px-8 text-lg font-bold text-white shadow-lg transform transition-transform hover:scale-105 disabled:opacity-50 disabled:hover:scale-100"
              disabled={isDrawing || nameList.length < 2}
              onClick={() => handleDrawButtonClick()}
            >
              {isDrawing ? 'DRAWING...' : 'DRAW'}
            </button>
            
            {nameList.length < 2 && (
              <div className="bg-white bg-opacity-80 rounded-lg p-2 text-sm text-red-500">
                Please add at least 2 participants in settings
              </div>
            )}
          </div>
        </div>
        <div className="absolute right-0 top-0 m-4 flex items-center gap-4">
          <Tooltip title="Settings">
            <button
              type="button"
              className="bg-white bg-opacity-20 hover:bg-opacity-40 rounded-full p-2 transition-all"
              disabled={isDrawing}
              onClick={() => setSettingsDrawerIsOpen(true)}
              aria-label="Open settings"
            >
              <Settings className="text-white" fontSize="medium" />
            </button>
          </Tooltip>
        </div>
      </div>
      <Drawer
        anchor="right"
        open={settingsDrawerIsOpen}
        onClose={() => setSettingsDrawerIsOpen(false)}
      >
        <div className="flex h-full w-screen flex-col gap-4 overflow-y-auto bg-[#e20035] p-4 md:w-[400px] ">
          <p className="text-lg font-bold text-white md:text-2xl">Settings</p>
          <div className="rounded-md bg-white p-3">
            <label htmlFor="lucky-draw-title" className="block text-sm font-medium text-gray-700 mb-1">
              Draw Title
            </label>
            <input
              id="lucky-draw-title"
              value={title}
              onChange={(event) => setTitle(event?.target?.value)}
              placeholder="Enter title for the lucky draw"
              className="w-full rounded-md border border-gray-300 p-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="rounded-md bg-white p-3">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Background Image
            </label>
            <div className="flex justify-center border border-gray-200 rounded p-2">
              <ImagePicker
                value={bg}
                onChange={(event) => {
                  setBg(event?.target?.value);
                  if (storageAvailable) localStorage.setItem('luckyDrawBg', event?.target?.value);
                }}
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">Select an image to use as background for the lucky draw</p>
          </div>

          <div className="flex w-full flex-col gap-4 rounded-md bg-white p-4">
            <div className="flex w-full items-center justify-between">
              <p className="font-medium text-gray-700">Participant Names</p>
              <div className="flex gap-2">
                <button
                  type="button"
                  className="bg-red-500 text-white rounded px-3 py-1 text-xs hover:bg-red-600"
                  onClick={() => setIsDeleteDialogOpen(true)}
                  disabled={nameList.length === 0 || isDrawing}
                >
                  Delete All
                </button>
                <UploadCsvButton
                  headerIsAtRow={0}
                  onlyAccept={['xlsx']}
                  onUpload={(data) => {
                    // Handle various Excel sheet names - try 'Lucky Draw' first, fall back to first sheet
                    const sheetData = data?.['Lucky Draw'] || Object.values(data || {})[0] || [];
                    
                    // Safely process the data with null checks
                    const temp = sheetData
                      .filter(item => item && typeof item === 'object' && item.Name)
                      .map(item => {
                        // Combine System ID and Name if both exist
                        if (item['System ID'] && item.Name) {
                          return `${item['System ID']} - ${item.Name}`;
                        }
                        // Fallback to just Name
                        return item.Name;
                      });
                      
                    // Only update if we have valid data
                    if (temp && temp.length > 0) {
                      dispatch(setNameList(temp));
                    }
                  }}
                />
              </div>
            </div>
            
            <div className="mt-2 border border-gray-200 rounded-md h-64 overflow-y-auto">
              {nameList && nameList.length > 0 ? (
                <div className="p-2">
                  {nameList.map((name, index) => (
                    <div key={index} className="flex items-center justify-between py-1 px-2 hover:bg-gray-100 rounded">
                      <span className="text-sm text-gray-800">{index + 1}. {name}</span>
                      <button 
                        type="button"
                        className="text-red-500 text-xs hover:text-red-700"
                        onClick={() => {
                          const updatedList = [...nameList];
                          updatedList.splice(index, 1);
                          dispatch(setNameList(updatedList));
                          if (storageAvailable) localStorage.setItem('luckyDrawNameList', JSON.stringify(updatedList));
                        }}
                        disabled={isDrawing}
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  No participants added yet. Upload CSV or add manually.
                </div>
              )}
            </div>
            
            <div className="mt-3 flex items-center gap-2">
              <input
                type="text"
                className="flex-1 border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Add a participant name"
                id="new-participant"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && e.target.value.trim()) {
                    const newList = [...nameList, e.target.value.trim()];
                    dispatch(setNameList(newList));
                    if (storageAvailable) localStorage.setItem('luckyDrawNameList', JSON.stringify(newList));
                    e.target.value = '';
                  }
                }}
              />
              <button
                type="button"
                className="bg-green-500 text-white rounded px-3 py-2 text-sm hover:bg-green-600"
                onClick={() => {
                  const input = document.getElementById('new-participant');
                  if (input.value.trim()) {
                    const newList = [...nameList, input.value.trim()];
                    dispatch(setNameList(newList));
                    if (storageAvailable) localStorage.setItem('luckyDrawNameList', JSON.stringify(newList));
                    input.value = '';
                  }
                }}
              >
                Add
              </button>
            </div>
          </div>
          <div className="rounded-md bg-white p-3">
            <div className="flex w-full justify-between items-center">
              <label htmlFor="drum-roll-switch" className="text-sm font-medium text-gray-700">
                Play Drum Roll Sound
                <p className="text-xs text-gray-500">Sound effect during the draw</p>
              </label>
              <Switch
                id="drum-roll-switch"
                onChange={(event) => setPlayMusic(event?.target?.checked)}
                checked={playMusic}
                color="primary"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <button
              type="button"
              className="flex-1 text-md rounded-xl bg-gray-500 p-3 font-bold text-white md:text-lg hover:bg-gray-600"
              onClick={() => setSettingsDrawerIsOpen(false)}
            >
              Cancel
            </button>
            <button
              type="button"
              className="flex-1 text-md rounded-xl bg-[#ffbf20] p-3 font-bold text-white md:text-lg hover:bg-[#e5ac1c]"
              onClick={() => {
                localStorage.setItem('luckyDrawNameList', JSON?.stringify(nameList));
                setSettingsDrawerIsOpen(false);
              }}
            >
              Save & Close
            </button>
          </div>
        </div>
      </Drawer>

      {/* Confirmation Dialog */}
      <Dialog
        open={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
      >
        <DialogTitle>
          <p className="text-lg font-bold">Confirm Delete All</p>
        </DialogTitle>
        <DialogContent>
          <p className="py-2">Are you sure you want to delete all {nameList.length} participants? This action cannot be undone.</p>
        </DialogContent>
        <DialogActions className="p-4">
          <Button 
            onClick={() => setIsDeleteDialogOpen(false)}
            variant="outlined"
            color="primary"
          >
            Cancel
          </Button>
          <Button 
            onClick={() => {
              dispatch(setNameList([]));
              if (storageAvailable) localStorage.setItem('luckyDrawNameList', JSON.stringify([]));
              setIsDeleteDialogOpen(false);
            }}
            variant="contained"
            color="error"
            autoFocus
          >
            Delete All
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
