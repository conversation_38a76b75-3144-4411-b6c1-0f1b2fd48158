// Next, React, Tw
import { Fragment, useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { Edit } from '@mui/icons-material';

// Packages
import * as yup from 'yup';

// Components
import Layout from '../../layouts/module/solid';
import { TablePaginationCustom } from '../../components/Shared/table';
import UploadBulkPopClsButton from '../../components/solid/UploadBulkPopClsButton';
import { TextInput, SelectInput, SearchInput } from '../../components/Shared/CustomInput';

// Others
import axios from '../../utils/axios';
import { CAPRY_ENDPOINT } from '../../utils/capry';
import { useSnackbar } from '../../components/Shared/snackbar';
import { checkAndReplaceStringWithHyphen, toUpperCaseFirstLetter } from '../../utils/shared';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuthContext();
  const { isAdmin } = useModuleRoleContext();
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { q } = query;

  const [countryList, setCountryList] = useState([]);

  // Table
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(20);
  const [tableData, setTableData] = useState([]);

  const bodyCellStyle = 'text-center py-1 text-xs';

  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) return tableData;
    return tableData.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) setDialogData(data);
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };

  const schema = yup.object({
    country: yup.string().required('Please provide country.'),
    address: yup.string().required('Please provide address.'),
    type: yup.string().required('Please provide type.'),
  });

  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }

      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${CAPRY_ENDPOINT}/solid/pop_cls`, payload);
            break;
          case 'put':
            response = await axios.put(`${CAPRY_ENDPOINT}/solid/pop_cls/${payload?.id}`, payload);
            break;
          case 'delete':
            response = await axios.delete(`${CAPRY_ENDPOINT}/solid/pop_cls/${payload?.id}`);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    fetchData();
    setDialogData({});
    setDialogOpen(false);
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/solid/pop_cls/all/keyword`);
      if (response?.data?.data) {
        setTableData(response?.data?.data);
      } else {
        setTableData([]);
      }
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  const fetchAllCountries = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await fetch(`/capry/country-list.json`);
      if (response?.status === 200) {
        const data = await response.json();
        setCountryList(data?.ref_country_codes);
      }
    } catch {
      setCountryList([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
    fetchAllCountries();
  }, []);
  return (
    <>
      <div className="container mx-auto  p-1 md:p-4">
        <div
          style={{ boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)' }}
          className="flex flex-col gap-8 rounded-xl bg-white p-4 dark:bg-gray-600 dark:text-white"
        >
          <div className="flex flex-col gap-8">
            <div className="flex items-center justify-between">
              <SearchInput />
              <div className="flex gap-2">
                {user?.isSuperAdmin && <UploadBulkPopClsButton />}
                {isAdmin && (
                  <button
                    type="button"
                    className=" bg-solid rounded-md p-1 text-sm text-white"
                    onClick={() => handleClickOpenDialog(false)}
                  >
                    New PoP/CLS
                  </button>
                )}
              </div>
            </div>
            <div className="overflow-x-auto scrollbar">
              <table className="min-w-full bg-white text-black">
                <thead>
                  <tr>
                    {['No.', 'Type', 'Country', 'Address', ''].map((label, i) => (
                      <td
                        key={i}
                        className="bg-solid whitespace-nowrap px-4 text-center text-white"
                      >
                        {label}
                      </td>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {filteredTableData
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((row, i) => (
                      <tr
                        key={i}
                        className="border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-200"
                      >
                        <td className={bodyCellStyle}>{i + 1 + rowsPerPage * page}</td>
                        <td className={bodyCellStyle}>
                          {checkAndReplaceStringWithHyphen(toUpperCaseFirstLetter(row?.type))}
                        </td>
                        <td className={bodyCellStyle}>
                          {checkAndReplaceStringWithHyphen(row?.country)}
                        </td>
                        <td className={bodyCellStyle}>
                          {checkAndReplaceStringWithHyphen(row?.address)}
                        </td>
                        <td className={bodyCellStyle}>
                          {!isAdmin && (
                            <div className="flex gap-2">
                              <button
                                type="button"
                                className=""
                                onClick={() => handleClickOpenDialog(true, row)}
                              >
                                <Edit className="text-capry" />
                              </button>
                            </div>
                          )}
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
            <TablePaginationCustom
              count={filteredTableData.length}
              page={page}
              rowsPerPage={rowsPerPage}
              onPageChange={(e, nextPage) => setPage(nextPage)}
            />
          </div>
        </div>
      </div>
      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-capry text-center text-white">
          {!editModeDialog ? 'Add New' : 'Edit'} Pop/CLS
        </DialogTitle>
        <DialogContent>
          <div className="my-8 flex w-full flex-col gap-1 p-2 md:w-[400px]">
            <SelectInput
              name="region"
              value={dialogData?.region}
              placeholder="Country"
              options={countryList.map((o, i) => o?.country)}
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="address"
              value={dialogData?.address}
              placeholder="Address"
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
              Cancel
            </button>
            <div className="flex gap-2">
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-capry rounded-[4px] p-2 font-semibold text-white"
                >
                  Save
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('delete')}
                  className="rounded-[4px] bg-red-500 p-2 font-semibold text-white"
                >
                  Delete
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-capry rounded-[4px] p-2 font-semibold text-white"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
}
