// Next, React, Tw
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';

// Components
import Layout from '../../../layouts/module/solid';
import { SearchInput } from '../../../components/Shared/CustomInput';
import UserPopup from '../../../components/Shared/UserPopup';
import ExportExcelButton from '../../../components/Shared/ExportExcelButton';

// Others
import axios from '../../../utils/axios';
import { CAPRY_ENDPOINT } from '../../../utils/capry';
import { setIsLoading } from '../../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const { push, query } = useRouter();
  const { q } = query;
  const dispatch = useDispatch();

  // Table
  const [tableData, setTableData] = useState([]);

  const bodyCellStyle = 'text-center py-1 text-xs';

  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/solid/requirement/all/keyword`);
      setTableData(response?.data?.data || []);
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="container mx-auto  p-1 md:p-4">
      <div
        style={{ boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)' }}
        className="flex flex-col gap-8 rounded-xl bg-white p-4 dark:bg-gray-600 dark:text-white"
      >
        <div className="flex items-center justify-between">
          <SearchInput />
          <button
            type="button"
            className=" bg-solid rounded-md p-1 text-sm text-white"
            onClick={() => push('/solid/requirement/new')}
          >
            Cross Check
          </button>
        </div>
        <div className="flex w-full justify-end">
          <ExportExcelButton
            key={filteredTableData?.length}
            data={filteredTableData}
            filename="export.csv"
          />
        </div>
        <div className="-mt-6 overflow-x-auto scrollbar">
          <table className="min-w-full bg-white text-black">
            <thead>
              <tr>
                {[
                  'Requirement ID',
                  'Customer',
                  'Bandwidth (x100 Gbps)',
                  'Created by',
                  'Created Date',
                ].map((label, i) => (
                  <td
                    key={i}
                    className="bg-solid whitespace-nowrap px-4 text-center text-sm text-white"
                  >
                    {label}
                  </td>
                ))}
              </tr>
            </thead>
            <tbody>
              {filteredTableData.map((row, i) => (
                <tr
                  key={i}
                  className="cursor-pointer border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-200"
                >
                  <td
                    className={bodyCellStyle}
                    onClick={() => push(`/solid/requirement/${row?.requirement_id}`)}
                  >
                    {row?.requirement_id}
                  </td>
                  <td
                    className={bodyCellStyle}
                    onClick={() => push(`/solid/requirement/${row?.requirement_id}`)}
                  >
                    {row?.account_name}
                  </td>
                  <td
                    className={bodyCellStyle}
                    onClick={() => push(`/solid/requirement/${row?.requirement_id}`)}
                  >
                    {row?.bandwidth_times_100_gbps}
                  </td>
                  <td className={bodyCellStyle}>
                    <UserPopup
                      label={row?.created_by_staff_id?.toUpperCase()}
                      staff_id={row?.created_by_staff_id}
                    />
                  </td>
                  <td
                    className={bodyCellStyle}
                    onClick={() => push(`/solid/requirement/${row?.requirement_id}`)}
                  >
                    {row?.created_date}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
