// Next, React, Tw
import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Divider } from '@mui/material';

// Packages
import * as yup from 'yup';

// Components
import Layout from '../../../layouts/module/solid';
import QuotationTable from '../../../components/solid/QuotationTable';
import QuotationFromSfaTable from '../../../components/solid/QuotationFromSfaTable';
import {
  TextInput,
  SelectInput,
  AutoCompleteTextInput,
} from '../../../components/Shared/CustomInput';
import CheckExistingRequirementDialog from '../../../components/solid/CheckExistingRequirementDialog';

// Others
import axios from '../../../utils/axios';
import { CAPRY_ENDPOINT } from '../../../utils/capry';
import { useSnackbar } from '../../../components/Shared/snackbar';
import { useAuthContext } from '../../../utils/auth/useAuthContext';
import { setRequirementData } from '../../../utils/store/capryReducer';
import { useModuleRoleContext } from '../../../utils/auth/ModuleRoleProvider';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import {
  checkAndReplaceStringWithHyphen,
  ALL_TM_CUSTOMER_ACCOUNT_ENDPOINT,
  TM_GLOBAL_REGIONS,
} from '../../../utils/shared';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { query, push } = useRouter();
  const { requirementId } = query;
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuthContext();
  const dispatch = useDispatch();
  const { requirementData } = useSelector((state) => state?.capry);
  const { isAdmin } = useModuleRoleContext();
  const IS_DISABLED = !isAdmin;

  const [popClslist, setPopClslist] = useState([]);
  const [sfaData, setSfaData] = useState(null);
  const [cableList, setCableList] = useState([]);
  const [countryList, setCountryList] = useState([]);
  const [accountList, setAccountList] = useState([]);

  // Table
  const bodyCellStyle = 'text-center py-1 text-xs';
  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);

  // Form
  const handleFormDataChange = (event) => {
    const { name, value } = event.target;
    dispatch(setRequirementData({ ...requirementData, [name]: value }));
  };
  const requirementSchema = yup.object({
    a_end_country: yup.string().required('Please provide A-End Country.'),
    a_end_location: yup.string(),
    a_end_pop_cls: yup.string().required('Please provide A-End Pop/CLS.'),
    account_name: yup.string().required('Please provide customer name.'),
    bandwidth_times_100_gbps: yup
      .number()
      .required('Please provide bandwidth.')
      .transform((value) => Number(value)),
    created_by_staff_id: yup
      .string()
      .required('Please provide created by staff ID.')
      ?.default(user?.staff_id),
    preferred_cable_name: yup.string().required('Please provide preferred cable name.'),
    region: yup.string(),
    requirement_id: yup.string().required('Please provide SFA ID.')?.default(requirementId),
    term: yup
      .string()
      .oneOf(['lease', 'iru'], 'Term must be either "lease" or "iru"')
      .required('Please provide month.'),
    term_period_years: yup
      .number()
      .required('Please provide term period in years.')
      .transform((value) => Number(value)),
    b_end_country: yup.string().required('Please provide B-End Country.'),
    z_end_location: yup.string(),
    z_end_pop_cls: yup.string().required('Please provide Z-End Pop/CLS.'),
  });

  const handleFormSubmit = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await requirementSchema.validate(requirementData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }
      dispatch(setIsLoading(true));
      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${CAPRY_ENDPOINT}/solid/requirement`, payload);
            if (response?.data?.status === 'success') {
              const localRequirementId = response.data.data?.split(' ')[2];
              push(`/solid/requirement/${localRequirementId}`);
            }
            break;
          case 'put':
            response = await axios.put(
              `${CAPRY_ENDPOINT}/solid/requirement/${requirementId}`,
              payload
            );
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
      dispatch(setIsLoading(false));
    }
    fetchData();
  };

  // Others

  const aEndPopClsList = (() => {
    if (!requirementData?.a_end_country) return [];
    return popClslist.filter((o) => o?.country === requirementData?.a_end_country);
  })();

  const bEndPopClsList = (() => {
    if (!requirementData?.b_end_country) return [];
    return popClslist.filter((o) => o?.country === requirementData?.b_end_country);
  })();

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(
        `${CAPRY_ENDPOINT}/solid/requirement/requirement_id/${requirementId}`
      );

      if (response?.data?.data) {
        dispatch(setRequirementData(response?.data?.data?.[0]));
      }
    } catch {
      /* empty */
    }
    dispatch(setIsLoading(false));
  };

  const fetchAllPopCls = async () => {
    dispatch(setIsLoading(true));

    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/solid/pop_cls/all/keyword`);
      setPopClslist(response?.data?.data || []);
    } catch {
      setPopClslist([]);
    }
    dispatch(setIsLoading(false));
  };

  const fetchSfaData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.post(
        `${CAPRY_ENDPOINT}/order_opportunity/opportunity_id/${requirementData?.salesforce_id}`
      );
      setSfaData(response?.data?.data?.[0] || null);
    } catch {
      setSfaData(null);
    }
    dispatch(setIsLoading(false));
  };

  const fetchAllAccounts = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${ALL_TM_CUSTOMER_ACCOUNT_ENDPOINT}`);
      if (response?.data?.data) {
        setAccountList(response?.data?.data?.map((o) => o?.name));
      }
    } catch {
      setAccountList([]);
    }
    dispatch(setIsLoading(false));
  };

  const fetchAllCableSystems = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await fetch(`/capry/cable-all.json`);
      if (response?.status === 200) {
        const data = await response.json();
        setCableList(data);
      }
    } catch {
      setCableList([]);
    }
    dispatch(setIsLoading(false));
  };

  const fetchCountryList = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await fetch(`/capry/country-list.json`);
      if (response?.status === 200) {
        const data = await response.json();
        setCountryList(data?.ref_country_codes);
      }
    } catch {
      setCountryList([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (requirementId === 'new') setDialogOpen(true);
  }, [requirementId]);

  useEffect(() => {
    if (requirementId !== 'new') {
      fetchData();
    }
  }, [requirementId]);

  useEffect(() => {
    if (requirementData?.salesforce_id) {
      fetchSfaData();
    }
  }, [requirementData?.salesforce_id]);

  useEffect(() => {
    fetchAllAccounts();
    fetchAllCableSystems();
    fetchCountryList();
    fetchAllPopCls();
  }, []);

  return (
    <>
      <div className="container mx-auto  p-1 md:p-4">
        <div
          style={{ boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)' }}
          className="flex flex-col gap-8 rounded-xl bg-white p-4 dark:bg-gray-600 dark:text-white"
        >
          <p className="text-xl font-bold">
            {requirementId === 'new' && 'New Requirement'}
            {requirementId !== 'new' && `Requirement - ${requirementId}`}
          </p>
          <Divider className="text-sm">Data from SIMI</Divider>
          <div className="flex w-full gap-2">
            <div className="flex w-1/2 flex-col gap-2">
              <TextInput
                name="salesforce_id"
                value={requirementData?.salesforce_id}
                placeholder="SFA ID"
                onChange={handleFormDataChange}
                disabled={IS_DISABLED}
                showRedAsteric={false}
              />
              <TextInput
                name="task_rms_id"
                value={requirementData?.task_rms_id}
                placeholder="Task RMS ID"
                onChange={handleFormDataChange}
                disabled={IS_DISABLED}
                showRedAsteric={false}
              />
              <SelectInput
                name="region"
                value={requirementData?.region}
                placeholder="Region"
                options={TM_GLOBAL_REGIONS}
                onChange={handleFormDataChange}
                disabled={IS_DISABLED}
                showRedAsteric={false}
              />
              <AutoCompleteTextInput
                name="account_name"
                value={requirementData?.account_name}
                placeholder="Customer"
                options={accountList}
                onChange={(event) =>
                  dispatch(
                    setRequirementData({ ...requirementData, account_name: event.target.value })
                  )
                }
                disabled={IS_DISABLED}
              />

              <SelectInput
                name="preferred_cable_name"
                value={requirementData?.preferred_cable_name}
                options={cableList.map((o, i) => o?.name)}
                placeholder="Preferred Cable"
                onChange={handleFormDataChange}
                disabled={IS_DISABLED}
              />
              <SelectInput
                name="a_end_country"
                value={requirementData?.a_end_country}
                placeholder="A-End Country"
                options={countryList?.map((o, i) => o?.country)}
                onChange={handleFormDataChange}
                disabled={IS_DISABLED}
              />
              <AutoCompleteTextInput
                name="a_end_pop_cls"
                value={requirementData?.a_end_pop_cls}
                placeholder="A-End PoP/CLS"
                options={aEndPopClsList?.map((o) => ({
                  label: o?.address,
                  value: o?.address,
                }))}
                onChange={(event) =>
                  dispatch(
                    setRequirementData({ ...requirementData, a_end_pop_cls: event.target.value })
                  )
                }
                disabled={IS_DISABLED}
              />
              <TextInput
                name="a_end_location"
                value={requirementData?.a_end_location}
                placeholder="A-End Location"
                onChange={handleFormDataChange}
                disabled={IS_DISABLED}
                showRedAsteric={false}
              />
              <SelectInput
                name="b_end_country"
                value={requirementData?.b_end_country}
                placeholder="B-End Country"
                options={countryList?.map((o, i) => o?.country)}
                onChange={handleFormDataChange}
                disabled={IS_DISABLED}
              />
              <AutoCompleteTextInput
                name="z_end_pop_cls"
                value={requirementData?.z_end_pop_cls}
                placeholder="B-End PoP/CLS"
                options={bEndPopClsList?.map((o) => ({
                  label: o?.address,
                  value: o?.address,
                }))}
                onChange={(event) =>
                  dispatch(
                    setRequirementData({ ...requirementData, z_end_pop_cls: event.target.value })
                  )
                }
                disabled={IS_DISABLED}
              />
              <TextInput
                name="z_end_location"
                value={requirementData?.z_end_location}
                placeholder="B-End Location"
                onChange={handleFormDataChange}
                disabled={IS_DISABLED}
                showRedAsteric={false}
              />
            </div>
            <div className="flex w-1/2 flex-col gap-2">
              <SelectInput
                name="term"
                value={requirementData?.term}
                placeholder="Term"
                options={['lease', 'iru']}
                onChange={handleFormDataChange}
                disabled={IS_DISABLED}
              />
              <TextInput
                name="term_period_years"
                value={requirementData?.term_period_years}
                placeholder="Term Period (Years)"
                onChange={handleFormDataChange}
                disabled={IS_DISABLED}
              />
              <TextInput
                name="bandwidth_times_100_gbps"
                value={requirementData?.bandwidth_times_100_gbps}
                placeholder="Bandwidth (x100 Gbps)"
                onChange={handleFormDataChange}
                disabled={IS_DISABLED}
              />
            </div>
          </div>
          <div className="flex justify-end">
            {requirementId === 'new' && (
              <button
                type="button"
                className=" bg-solid rounded-md p-1 text-white"
                onClick={() => handleFormSubmit('post')}
              >
                Save
              </button>
            )}
            {requirementId !== 'new' && isAdmin && (
              <button
                type="button"
                className=" bg-solid rounded-md p-1 text-white"
                onClick={() => handleFormSubmit('put')}
              >
                Edit
              </button>
            )}
          </div>

          {requirementId !== 'new' && (
            <>
              <Divider className="text-sm">Data from SFA</Divider>
              <div className="mx-auto w-1/2">
                <div className="overflow-x-auto scrollbar">
                  <table className="min-w-full bg-white text-black">
                    <tbody>
                      {[
                        {
                          label: 'SFA ID',
                          key: 'opportunity_id',
                        },
                        { label: 'Description', key: 'description' },
                        { label: 'Customer', key: 'customer_name' },
                        { label: 'Sales PIC', key: 'owner' },
                        { label: 'PO Date', key: 'po_date' },
                        { label: 'Sales Type', key: 'type' },
                        { label: 'Status', key: 'status' },
                      ]?.map((o, i) => (
                        <tr
                          key={i}
                          className="border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8]"
                        >
                          <td className={bodyCellStyle}>{o?.label}</td>
                          <td className={bodyCellStyle}>:</td>
                          <td className={bodyCellStyle}>
                            {checkAndReplaceStringWithHyphen(sfaData?.[o?.key])}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
              <Divider className="text-sm">Quotations from SIMI</Divider>
              <QuotationTable requirementId={requirementId} />
              <Divider className="text-sm">Quotations from SFA</Divider>
              <QuotationFromSfaTable taskRmsId={requirementData?.task_rms_id} />
            </>
          )}
        </div>
      </div>
      <CheckExistingRequirementDialog dialogOpen={dialogOpen} setDialogOpen={setDialogOpen} />
    </>
  );
}
