// Components
import Layout from '../../layouts/module/solid';
import QuotationTable from '../../components/solid/QuotationTable';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  return (
    <div className="container mx-auto  p-1 md:p-4">
      <div
        style={{ boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)' }}
        className="flex flex-col gap-8 rounded-xl bg-white p-4 dark:bg-gray-600 dark:text-white"
      >
        <div className="flex flex-col gap-8">
          <QuotationTable requirementId="all" />
        </div>
      </div>
    </div>
  );
}
