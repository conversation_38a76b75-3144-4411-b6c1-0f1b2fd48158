// Next, React, Tw
import { useRouter } from 'next/router';
import { twMerge } from 'tailwind-merge';
import { useDispatch } from 'react-redux';
import Image from 'next/image';

// Components
import Layout from '../../layouts/module/solid';

// Others
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { CAPRY_ENDPOINT } from '../../utils/capry';
import { useSnackbar } from '../../components/Shared/snackbar';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const dispatch = useDispatch();
  const { push } = useRouter();
  const { enqueueSnackbar } = useSnackbar();

  const fetchQuotationData = async (quoteId) => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/solid/quote/id/${quoteId}`);

      if (response?.data?.data?.[0]) {
        const temp = response?.data?.data?.[0];
        push(`/solid/requirement/${temp?.requirement_id}?quoteId=${temp?.id}&dialogOpen=true`);
      }
    } catch {
      enqueueSnackbar('No data found', { variant: 'error' });
    }
    dispatch(setIsLoading(false));
  };

  return (
    <div className="flex h-full w-full flex-col items-center justify-center gap-2 bg-white dark:bg-gray-800">
      <form
        className="flex w-1/4 flex-col items-center gap-2"
        onSubmit={async (event) => {
          event?.preventDefault();
          const formData = new FormData(event.target);
          const quoteId = formData.get('emailCode')?.trim();
          await fetchQuotationData(quoteId);
        }}
      >
        <Image src="/assets/moduleLogo/solid.webp" alt="Logo" width={100} height={100} />
        <div className="relative h-10 w-full">
          <input
            className={twMerge(
              `peer h-full w-full rounded-lg border bg-transparent  bg-white px-3 py-2.5 text-xs outline outline-0 transition-all placeholder-shown:border focus:border-2 focus:border-[#ff7b7b] focus:border-t-transparent focus:outline-0 disabled:border-2 disabled:border-t-transparent disabled:outline-0`
            )}
            name="emailCode"
            placeholder=" "
            autoComplete="off"
          />
          <p
            className={`before:content[' '] after:content[' '] pointer-events-none absolute -top-1.5 left-0 flex h-full w-full select-none text-xs font-normal leading-tight transition-all before:pointer-events-none before:mr-1 before:mt-[6.5px] before:box-border before:block before:h-1.5 before:w-2.5 before:rounded-tl-md before:border-l before:border-t before:transition-all after:pointer-events-none after:ml-1 after:mt-[6.5px] after:box-border after:block after:h-1.5 after:w-2.5 after:flex-grow after:rounded-tr-md after:border-r after:border-t after:transition-all peer-placeholder-shown:text-sm peer-placeholder-shown:leading-[3.75] peer-placeholder-shown:before:border-transparent peer-placeholder-shown:after:border-transparent peer-focus:text-[11px] peer-focus:leading-tight peer-focus:text-[#ff7b7b] peer-focus:before:border-l-2 peer-focus:before:border-t-2 peer-focus:before:border-[#ff7b7b] peer-focus:after:border-r-2 peer-focus:after:border-t-2 peer-focus:after:border-[#ff7b7b]`}
          >
            E-Mail Code
            <span className="font-semibold text-red-500">&nbsp;*</span>
          </p>
        </div>
        <button type="submit" className="bg-solid w-full rounded-md p-1 text-white">
          Submit
        </button>
      </form>
    </div>
  );
}
