// Next, React, Tw
import { useEffect } from 'react';
import { useRouter } from 'next/router';

// Mui
import { Tabs, Tab } from '@mui/material';

// Components
import Layout from '../../../layouts/module/capry';
import AllReservationView from '../../../components/capry/offnet/AllReservationView';

// Others
import { useParamContext } from '../../../utils/auth/ParamProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const router = useRouter();
  const { replace, asPath } = useRouter();
  const { category } = router.query;
  const { setParam } = useParamContext();

  // Others

  useEffect(() => {
    if (!category) replace(`${asPath}?category=offnet`);
  }, [category]);

  return (
    <div className="container mx-auto  p-1 md:p-4">
      <div className="flex flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
        <div className="flex items-center justify-between">
          <Tabs
            value={category}
            onChange={(event, newValue) => setParam({ category: newValue })}
            centered
            sx={{
              '& .MuiTabs-indicator': {
                backgroundColor: '#ff6602',
              },
            }}
            className="text-black dark:text-white"
          >
            <Tab label="OFFNET" value="offnet" />
            <Tab label="ONNET" value="onnet" />
          </Tabs>
        </div>

        {category === 'offnet' && <AllReservationView />}
        {category === 'onnet' && (
          <div className="flex h-full w-full items-center justify-center">
            <p>Coming Soon!</p>
          </div>
        )}
      </div>
    </div>
  );
}
