// Next, React, Tw
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';

// Mui
import { Divider } from '@mui/material';

// Redux
import { setReservationData } from '../../../utils/store/capryReducer';

// Components
import Layout from '../../../layouts/module/capry';
import ReservationStepper from '../../../components/capry/offnet/ReservationStepper';
import ReservationDetails from '../../../components/capry/offnet/ReservationDetails';
import NewReservation from '../../../components/capry/offnet/NewReservation';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const { query } = useRouter();
  const { reservationId } = query;
  const dispatch = useDispatch();
  const { reservationData } = useSelector((state) => state.capry);
  
  // Reset reservation data when creating a new reservation
  useEffect(() => {
    if (reservationId === 'new') {
      dispatch(setReservationData({}));
    }
  }, [reservationId, dispatch]);

  return (
    <div className="container mx-auto flex flex-col gap-4  p-1 md:px-4 md:py-8">
      <Divider className="font-semibold">Activation Steps</Divider>
      <div className="mt-8 w-full overflow-x-scroll md:overflow-x-auto">
        <ReservationStepper />
      </div>
      {reservationId === 'new' && !reservationData?.salesforce_id ? (
        <NewReservation />
      ) : (
        <ReservationDetails />
      )}
    </div>
  );
}
