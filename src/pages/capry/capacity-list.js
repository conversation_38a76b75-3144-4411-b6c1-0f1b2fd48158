// Next, React, Tw
import { useEffect } from 'react';
import { useRouter } from 'next/router';

// Components
import Layout from '../../layouts/module/capry';
import AllPathsView from '../../components/capry/offnet/AllPathsView';

// Others
import { useParamContext } from '../../utils/auth/ParamProvider';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const { query } = useRouter();
  const { category } = query;
  const { setParam, replaceParam } = useParamContext();
  const { isAdmin } = useModuleRoleContext();

  // Others

  useEffect(() => {
    if (!category) replaceParam({ category: 'offnet' });
  }, [category]);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header Section */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col gap-4">
            {/* Page Title */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
                  Capacity Management
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  View and manage network capacity across all paths and connections
                </p>
              </div>
            </div>

            {/* Tab Navigation */}
            <div className="flex items-center">
              <nav className="flex space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                {[
                  { label: 'OFFNET', value: 'offnet' },
                  { label: 'ONNET', value: 'onnet' },
                ].map((tab) => (
                  <button
                    key={tab.value}
                    onClick={() => setParam({ category: tab.value, cableId: '', bearerId: '' })}
                    className={`px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
                      category === tab.value
                        ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                        : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          {category === 'offnet' && <AllPathsView />}
          {category === 'onnet' && (
            <div className="flex h-64 w-full items-center justify-center">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 text-gray-400">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                  </svg>
                </div>
                <p className="text-gray-500 dark:text-gray-400 text-lg font-medium">Coming Soon!</p>
                <p className="text-gray-400 dark:text-gray-500 text-sm mt-1">ONNET capacity management will be available soon</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
