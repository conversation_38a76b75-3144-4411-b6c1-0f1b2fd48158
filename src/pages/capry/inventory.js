// Next, React, Tw
import Link from 'next/link';
import { useEffect } from 'react';
import { useRouter } from 'next/router';

// Mui
import { Tabs, Tab } from '@mui/material';

// Components
import Layout from '../../layouts/module/capry';
import CableSystemView from '../../components/capry/offnet/CableSystemView';
import BearerView from '../../components/capry/offnet/BearerView';
import UploadBulkButton from '../../components/capry/offnet/UploadBulkOffnetInventoriesButton';
import PathDetails from '../../components/capry/offnet/PathDetails';

// Others
import { useParamContext } from '../../utils/auth/ParamProvider';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const { query } = useRouter();
  const { category, cableId, pathId } = query;
  const { setParam, replaceParam } = useParamContext();
  const { isAdmin } = useModuleRoleContext();

  // Others

  useEffect(() => {
    if (!category) replaceParam({ category: 'offnet' });
  }, [category]);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header Section */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col gap-4">
            {/* Page Title */}
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
                  Inventory Management
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  Manage your network capacity and infrastructure
                </p>
              </div>

              {/* Admin Actions */}
              {isAdmin && !cableId && (
                <div className="flex items-center gap-3">
                  <Link
                    href="/capry/template.csv"
                    target="_blank"
                    className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    CSV Template
                  </Link>
                  <Link
                    href="/capry/bulk-upload-guide.md"
                    target="_blank"
                    className="inline-flex items-center px-3 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Upload Guide
                  </Link>
                  <UploadBulkButton />
                </div>
              )}
            </div>

            {/* Tab Navigation */}
            {!cableId && (
              <div className="flex items-center">
                <nav className="flex space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                  {[
                    { label: 'OFFNET', value: 'offnet' },
                    { label: 'ONNET', value: 'onnet' },
                  ].map((tab) => (
                    <button
                      key={tab.value}
                      onClick={() => setParam({ category: tab.value, cableId: '', bearerId: '' })}
                      className={`px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
                        category === tab.value
                          ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                          : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                      }`}
                    >
                      {tab.label}
                    </button>
                  ))}
                </nav>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          {category === 'offnet' && (
            <>
              {!cableId && <CableSystemView />}
              {cableId && !pathId && <BearerView />}
              {cableId && pathId && <PathDetails pathId={pathId} />}
            </>
          )}
          {category === 'onnet' && (
            <div className="flex flex-col items-center justify-center py-16">
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">ONNET Coming Soon</h3>
                <p className="text-gray-500 dark:text-gray-400">This feature is currently under development.</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
