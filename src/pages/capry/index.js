// Next, React, Tw
import dynamic from 'next/dynamic';
import { useState, useEffect, useMemo } from 'react';
import { twMerge } from 'tailwind-merge';
import { useDispatch } from 'react-redux';

// Components
import Layout from '../../layouts/module/capry';
import ReactAnimatedNumber from '../../components/Shared/ReactAnimatedNumber';
import OnMapDiv from '../../components/capry/OnMapDiv';

// Others
import axios from '../../utils/axios';
import { CAPRY_ENDPOINT } from '../../utils/capry';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const dispatch = useDispatch();

  const OpenStreetMap = dynamic(() => import('../../components/capry/Map'), {
    ssr: false,
  });

  const [offnetSummary, setOffnetSummary] = useState({});

  // Others

  const memoizedComponent = useMemo(
    () => (
      <div className="flex flex-col gap-2 px-8 md:flex-row">
        <div className="flex w-full flex-col rounded-md bg-gray-500 p-2 text-white md:w-1/2 ">
          <p className="w-full text-center text-3xl font-bold">ONNET</p>
          <div className="flex flex-col md:flex-row">
            {[
              {
                label: 'Total Capacity',
                suffix: 'GB',
                value: 'N/A',
                hexCode: 'bg-[#e4ffe7]',
              },
              {
                label: 'Total Activated',
                suffix: 'GB',
                value: 'N/A',
                hexCode: 'bg-[#f6e5e5]',
              },
              { label: 'Total Available', suffix: 'GB', value: 'N/A', hexCode: 'bg-[#e3f5ff]' },
              { label: 'Utilization %', suffix: '%', value: 'N/A', hexCode: 'bg-[#e5ecf6]' },
            ].map((o, i) => (
              <div key={i} className="w-full p-2 md:w-1/4">
                <div
                  className={twMerge(
                    'flex w-full flex-col rounded-md bg-[#e4ffe7] p-2 text-black',
                    o?.hexCode
                  )}
                >
                  <p className="text-xs">{o?.label}</p>
                  <p className="text-right text-xl font-bold">
                    {/* <ReactAnimatedNumber
                      value={o?.value}
                      formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                    /> */}
                    {o?.value}
                    {/* <span className="text-sm">{o?.suffix}</span> */}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="bg-capry flex w-full flex-col rounded-md p-2 text-white md:w-1/2 ">
          <p className="w-full text-center text-3xl font-bold">OFFNET</p>
          <div className="flex flex-col md:flex-row">
            {[
              {
                label: 'Total Capacity',
                suffix: 'GB',
                value: offnetSummary?.total_capacity,
                hexCode: 'bg-[#e4ffe7]',
              },
              {
                label: 'Total Activated',
                suffix: 'GB',
                value: offnetSummary?.total_activated,
                hexCode: 'bg-[#f6e5e5]',
              },
              {
                label: 'Total Available',
                suffix: 'GB',
                value: offnetSummary?.total_available,
                hexCode: 'bg-[#e3f5ff]',
              },
              {
                label: 'Utilization %',
                suffix: '%',
                value: offnetSummary?.utilization_percentage,
                hexCode: 'bg-[#e5ecf6]',
              },
            ].map((o, i) => (
              <div key={i} className="w-full p-2 md:w-1/4">
                <div
                  className={twMerge('flex w-full flex-col rounded-md p-2 text-black', o?.hexCode)}
                >
                  <p className="text-xs">{o?.label}</p>
                  <p className="text-right text-xl font-bold">
                    <ReactAnimatedNumber
                      value={o?.value}
                      formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                    />{' '}
                    <span className="text-sm">{o?.suffix}</span>
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    ),
    [JSON?.stringify(offnetSummary)]
  );

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/offnet/path/all/keyword`);

      if (response?.data?.data) {
        let temp = response?.data?.data?.filter((o) => !['draft']?.includes(o?.status));
        const totalCapacity = temp?.reduce(
          (accumulator, currentValue) => accumulator + currentValue.total_capacity,
          0
        );
        temp = temp?.filter((o) => !['provisioning', 'activated']?.includes(o?.status));
        const totalAvailable = temp?.reduce(
          (accumulator, currentValue) => accumulator + currentValue.total_capacity,
          0
        );
        const totalActivated = totalCapacity - totalAvailable;
        const utilizationPercentage = (totalActivated / totalCapacity) * 100;
        setOffnetSummary({
          total_capacity: totalCapacity,
          total_available: totalAvailable,
          total_activated: totalActivated,
          utilization_percentage: utilizationPercentage,
        });
      }
    } catch {
      setOffnetSummary({});
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="container mx-auto p-1 md:p-4">
      <div className="flex flex-col gap-4">
        {memoizedComponent}
        <div className="flex justify-center md:hidden">
          <OnMapDiv />
        </div>

        <OpenStreetMap />
      </div>
    </div>
  );
}
