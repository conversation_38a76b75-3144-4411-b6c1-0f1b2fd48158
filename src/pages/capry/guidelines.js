// Components
import Layout from '../../layouts/module/capry';
import AttachmentBox from '../../components/Shared/AttachmentBox';

// Others
import { CAPRY_ENDPOINT } from '../../utils/capry';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const { isAdmin } = useModuleRoleContext();

  // Others

  return (
    <div className="container mx-auto p-1 md:p-4">
      <div className="flex flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
        <p className="w-full text-center text-xl font-bold md:text-left">Guidelines</p>
        <AttachmentBox
          GET_ALL_FILES_ENDPOINT={`${CAPRY_ENDPOINT}/file/guideline/64db23af2b57e5b8a12f8c1d`}
          UPLOAD_CERTAIN_FILE_ENDPOINT={`${CAPRY_ENDPOINT}/file/upload/guideline/64db23af2b57e5b8a12f8c1d`}
          DOWNLOAD_CERTAIN_FILE_ENDPOINT={`${CAPRY_ENDPOINT}/file/download`}
          DELETE_CERTAIN_FILE_ENDPOINT={`${CAPRY_ENDPOINT}/file`}
          DISABLE_UPLOAD={!isAdmin}
        />
      </div>
    </div>
  );
}
