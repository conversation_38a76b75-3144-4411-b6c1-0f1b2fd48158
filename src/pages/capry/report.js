// Next, React, Tw
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { twMerge } from 'tailwind-merge';
import { useRouter } from 'next/router';

// Mui
import { Switch, Tabs, Tab } from '@mui/material';

// Packages
import moment from 'moment';

// Components
import Layout from '../../layouts/module/capry';
import ReactApexcharts from '../../components/Shared/ReactApexcharts';
import ReactAnimatedNumber from '../../components/Shared/ReactAnimatedNumber';
import { DateInput } from '../../components/Shared/CustomInput';

// Others
import { setIsLoading } from '../../utils/store/loadingReducer';
import axios from '../../utils/axios';
import { CAPRY_ENDPOINT, getStatusBg, statusAndColorMapping } from '../../utils/capry';
import {
  toUpperCaseFirstLetter,
  sortArrayOfObjectsByCertainKeyAlphabetically,
  sortArrayOfObjectsByCertainKeyNumerically,
} from '../../utils/shared';
import { useSimiContext } from '../../utils/simi';
import { useParamContext } from '../../utils/auth/ParamProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ------------------------------------------------- ---------------------

export default function Page() {
  // Standard and Vars
  const dispatch = useDispatch();
  const { push, query } = useRouter();
  const { handoverYear, type, sortBy } = query;
  const { setParam, replaceParam } = useParamContext();
  const { moduleColorCode } = useSimiContext();

  const [allCableSystems, setAllCableSystems] = useState([]);
  const [allPaths, setAllPaths] = useState([]);

  // Others

  const allPathsWithCableData = allPaths.map((o) => ({
    ...o,
    ...allCableSystems?.find((p) => p?.id === o?.cable_id),
  }));

  const filteredAllPaths = (() => {
    const paths = allPathsWithCableData.filter((o) => o?.type === type);
    return handoverYear === 'all'
      ? paths
      : paths.filter((o) => moment(o?.handover_date)?.year() === Number(handoverYear));
  })();

  const filteredAllCableSystems = (() => {
    const systems = allCableSystems.filter((o) => o?.type === type);
    const paths = filteredAllPaths;
    systems.forEach((system) => {
      const temp = paths.filter((path) => path?.cable_id === system?.id);
      system.path_list = temp;
      system.cable_total_capacity = temp?.reduce(
        (prev, curr) => prev + (curr?.total_capacity || 0),
        0
      );
    });

    if (sortBy === 'name') return sortArrayOfObjectsByCertainKeyAlphabetically(systems, 'name');
    return sortArrayOfObjectsByCertainKeyNumerically(systems, 'cable_total_capacity', false);
  })();

  const getSummaryData = () => {
    const statuses = ['activated', 'reserved', 'provisioning', 'available', 'draft', 'booked'];

    const summary = statuses.map((status) => ({
      label: status,
      value: filteredAllPaths
        ?.filter((p) => p?.status === status)
        ?.reduce((prev, curr) => prev + (curr?.total_capacity || 0), 0),
    }));

    summary.push({
      label: 'total',
      value: filteredAllPaths
        ?.filter((p) => p?.status !== 'draft')
        ?.reduce((prev, curr) => prev + (curr?.total_capacity || 0), 0),
    });

    return summary;
  };

  // Chart
  const options = (() => {
    const cableSystems = filteredAllCableSystems;
    return {
      chart: {
        stacked: true,
        toolbar: {
          show: false,
        },
        events: {
          dataPointSelection: (_, __, { ___, dataPointIndex, ____ }) =>
            push(`/capry/inventory?category=offnet&cableId=${cableSystems?.[dataPointIndex]?.id}`),
        },
      },
      responsive: [
        {
          breakpoint: 480,
          options: {
            legend: {
              position: 'bottom',
              offsetX: -10,
              offsetY: 0,
            },
          },
        },
      ],
      dataLabels: {
        enabled: true,
        formatter: (val, { seriesIndex, dataPointIndex, w }) =>
          seriesIndex === series.length - 1 ? val : '',
        offsetY: -10,
        style: {
          fontSize: '12px',
          colors: ['#000'],
        },
      },
      xaxis: {
        type: 'category',
        categories: cableSystems?.map((o) => o?.name),
      },
      yaxis: {
        title: {
          text: 'Gbps',
        },
      },
      legend: {
        position: 'bottom',
      },
      fill: {
        opacity: 1,
      },
      colors: [
        statusAndColorMapping.activated,
        statusAndColorMapping.reserved,
        statusAndColorMapping.provisioning,
        statusAndColorMapping.available,
        statusAndColorMapping.draft,
        statusAndColorMapping.booked,
        statusAndColorMapping.total,
      ],
    };
  })();

  const series = (() => {
    const cableSystems = filteredAllCableSystems;
    const statuses = ['activated', 'reserved', 'provisioning', 'available', 'draft', 'booked'];

    const seriesData = statuses.map((status) => ({
      name: status.charAt(0).toUpperCase() + status.slice(1),
      type: 'bar',
      data: cableSystems.map((o) =>
        o?.path_list
          ?.filter((p) => p?.status === status)
          ?.reduce((prev, curr) => prev + (curr?.total_capacity || 0), 0)
      ),
    }));

    const totalData = {
      name: 'Total',
      type: 'scatter',
      data: cableSystems.map((o) =>
        o?.path_list?.reduce((prev, curr) => prev + (curr?.total_capacity || 0), 0)
      ),
    };

    return [...seriesData, totalData];
  })();

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/offnet/cable/all/keyword`);
      if (response?.data?.data) {
        setAllCableSystems(response?.data?.data?.map((o) => ({ ...o, path_list: [] })));
      }
    } catch {
      setAllCableSystems([]);
    }
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/offnet/path/all/keyword`);
      setAllPaths(response?.data?.data || []);
    } catch (error) {
      setAllPaths([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (!handoverYear || !type || !sortBy)
      replaceParam({ handoverYear: 'all', type: 'submarine', sortBy: 'name' });
  }, [handoverYear, type]);

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="container mx-auto  p-1 md:p-4">
      <div className="flex flex-col gap-8 overflow-x-auto rounded-xl bg-white p-4 shadow-md scrollbar dark:bg-gray-600 dark:text-white">
        <Tabs
          value={type}
          onChange={(event, newValue) => setParam({ type: newValue, handoverYear: 'all' })}
          centered
          sx={{
            '& .MuiTabs-indicator': {
              backgroundColor: '#ff6602',
            },
          }}
          className="text-black dark:text-white"
        >
          {[
            {
              label: 'Submarine',
              value: 'submarine',
            },
            {
              label: 'Backhaul',
              value: 'backhaul',
            },
          ]?.map((o, i) => (
            <Tab key={i} label={o.label} value={o.value} />
          ))}
        </Tabs>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Switch
              checked={handoverYear === 'all'}
              sx={{
                '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                  backgroundColor: moduleColorCode,
                },
              }}
              onChange={() => {
                if (handoverYear === 'all') {
                  setParam({ handoverYear: moment()?.format('YYYY') });
                  return;
                }
                setParam({ handoverYear: 'all' });
              }}
            />
            <p className="text-sm">All</p>
            {handoverYear !== 'all' && (
              <div className="w-[150px] pl-2">
                <DateInput
                  views={['year']}
                  value={handoverYear}
                  placeholder="Handover Year"
                  returnedFormat="YYYY"
                  onChange={(event) => setParam({ handoverYear: event?.target?.value })}
                />
              </div>
            )}
          </div>
          <div className="flex items-center">
            <p className="text-sm">Name</p>
            <Switch
              checked={sortBy === 'capacity'}
              sx={{
                '& .MuiSwitch-switchBase': {
                  color: moduleColorCode,
                },
                '& .MuiSwitch-switchBase + .MuiSwitch-track': {
                  backgroundColor: moduleColorCode,
                },
                '& .MuiSwitch-switchBase.Mui-checked': {
                  color: moduleColorCode,
                },
                '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                  backgroundColor: moduleColorCode,
                },
              }}
              onChange={() => {
                if (sortBy === 'capacity') {
                  setParam({ sortBy: 'name' });
                  return;
                }
                setParam({ sortBy: 'capacity' });
              }}
            />
            <p className="text-sm">Capacity</p>
          </div>
        </div>

        <div className="flex flex-col md:flex-row">
          {getSummaryData()?.map((o, i) => (
            <div key={i} className="md:w-1/7 w-full p-2">
              <div
                className={twMerge(
                  'flex w-full flex-col rounded-md p-2 text-white',
                  getStatusBg(o?.label)
                )}
              >
                <p className="text-sm">{toUpperCaseFirstLetter(o?.label)}</p>
                <p className="text-right text-2xl font-bold">
                  <ReactAnimatedNumber
                    value={o?.value}
                    formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                  />{' '}
                  <span className="text-sm">Gbps</span>
                </p>
              </div>
            </div>
          ))}
        </div>
        <div className="rounded-lg bg-white">
          <ReactApexcharts
            key={filteredAllCableSystems?.length}
            options={options}
            series={series}
            height={500}
          />
        </div>
      </div>
    </div>
  );
}
