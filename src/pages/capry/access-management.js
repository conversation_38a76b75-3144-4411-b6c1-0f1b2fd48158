// layouts
import Layout from '../../layouts/module/capry';
// components
import AccessManagementTemplate from '../../components/Shared/userAccessManagement';

// ----------------------------------------------------------------------

AccessManagement.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function AccessManagement() {
  return <AccessManagementTemplate roleArray={['pending', 'user', 'reserver', 'admin']} />;
}
