// Next, React, Tw
import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSelector } from 'react-redux';
import { twMerge } from 'tailwind-merge';

// Packages
import * as R from 'ramda';
import moment from 'moment';

// Components
import Layout from '../../layouts/module/sdp';
import { TablePaginationCustom } from '../../components/Shared/table';
import ReactApexcharts from '../../components/Shared/ReactApexcharts';
import SortButton from '../../components/Shared/SortButton';

// Others
import { useParamContext } from '../../utils/auth/ParamProvider';
import {
  SelectInput,
  TextInput,
  SearchInput,
  SelectMultipleInput,
} from '../../components/Shared/CustomInput';
import {
  checkAndReplaceStringWithHyphen,
  checkAndReplaceNumberWithZero,
  getColorCode,
  sortArrayOfObjectsByCertainKeyAlphabetically,
  sortArrayOfObjectsByCertainKeyNumerically,
} from '../../utils/shared';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { asPath, query } = useRouter();
  const module = asPath?.split('?')?.[0]?.split('/')[1];
  const {
    q,
    unit,
    orderTypeArray,
    orderStatus,
    customerName,
    productType,
    agingFrom,
    agingTo,
    year,
    month,
    currency,
    sortedByKey,
    sortInAscendingOrder,
  } = query;
  const { page, rowsPerPage } = useSelector((state) => state.simi);
  const { ordersDataUpdatedAt, ordersData } = useSelector((state) => state.sdp);
  const { setParam, replaceParam } = useParamContext();

  // Table
  const getHeaderCellStyle = () => 'bg-sdp whitespace-nowrap px-4 text-center text-sm text-white';
  const getBodyCellStyle = () =>
    'text-center py-1 whitespace-nowrap text-[11px] max-w-[100px] overflow-hidden whitespace-nowrap';

  // Others

  const getUnits = () => R.uniq(R.pluck('unit', ordersData));
  const getOrderTypes = () => R.uniq(R.pluck('order_type', ordersData));
  const getOrderStatuses = () => R.uniq(R.pluck('order_status', ordersData));
  const getCustomerNames = () => R.uniq(R.pluck('customer_name', ordersData));
  const getProductTypes = () => R.uniq(R.pluck('product_type', ordersData));
  const getYears = () => R.uniq(R.pluck('year', ordersData))?.sort((a, b) => a - b);
  const getMonths = () => [
    'jan',
    'feb',
    'mar',
    'apr',
    'may',
    'jun',
    'jul',
    'aug',
    'sep',
    'oct',
    'nov',
    'dec',
  ];
  const getCurrencies = () => ['myr', 'sgd', 'usd'];

  const getFilteredOrders = () => {
    let temp = ordersData?.filter(
      (o) =>
        o?.year === year &&
        o?.aging_from_rfs_date_to_today <= Number(agingTo) &&
        o?.aging_from_rfs_date_to_today >= Number(agingFrom)
    );

    if (month !== 'all') {
      temp = temp?.filter((o) => o?.month === month);
    }
    if (unit !== 'all') {
      temp = temp?.filter((o) => o?.unit === unit);
    }
    if (orderStatus !== 'all') {
      temp = temp?.filter((o) => o?.order_status === orderStatus);
    }
    if (customerName !== 'all') {
      temp = temp?.filter((o) => o?.customer_name === customerName);
    }
    if (productType !== 'all') {
      temp = temp?.filter((o) => o?.product_type === productType);
    }
    if (![undefined, '']?.includes(q)) {
      temp = temp.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
    }
    const getOrderTypeArray = () => {
      if (!orderTypeArray) return [];
      return JSON?.parse(orderTypeArray);
    };
    if (!getOrderTypeArray()?.includes('all')) {
      temp = temp?.filter((o) => getOrderTypeArray()?.includes(o?.order_type));
    }
    if (sortedByKey && sortInAscendingOrder) {
      if (Number.isNaN(temp?.[0]?.[sortedByKey])) {
        temp = sortArrayOfObjectsByCertainKeyAlphabetically(
          temp,
          sortedByKey,
          sortInAscendingOrder === 'true'
        );
      } else {
        temp = sortArrayOfObjectsByCertainKeyNumerically(
          temp,
          sortedByKey,
          sortInAscendingOrder === 'true'
        );
      }
    }
    return temp;
  };

  const mrcGraphSeriesData = getMonths()?.map((o) =>
    checkAndReplaceNumberWithZero(
      getFilteredOrders()
        ?.filter((p) => p?.month === o)
        ?.reduce((prev, curr) => prev + Number(curr?.mrc_total_net_price), 0) / 1000000,
      0
    )
  );

  useEffect(() => {
    replaceParam({
      unit: 'all',
      orderTypeArray: '["all"]',
      orderStatus: 'all',
      customerName: 'all',
      orderNumber: 'all',
      productType: 'all',
      agingFrom: '-365',
      agingTo: '365',
      year: moment()?.format('YYYY'),
      month: 'all',
      currency: 'all',
    });
  }, []);

  return (
    <>
      <div className="bg-sdp px-4 py-2 text-white">
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-extrabold">Order Details</p>
            <p className="text-xs">As of : {ordersDataUpdatedAt}</p>
          </div>
        </div>
      </div>

      <div className="container mx-auto p-1 md:p-4">
        <div className="flex flex-col gap-2 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
          <div className="flex flex-col gap-2 md:flex-row">
            <div className="w-full md:w-1/5">
              <SelectInput
                value={unit}
                placeholder="Unit"
                options={['all', ...getUnits()]}
                onChange={(event) => setParam({ unit: event.target.value })}
              />
            </div>
            <div className="w-full md:w-1/5">
              <SelectMultipleInput
                value={orderTypeArray}
                placeholder="Order Type"
                defaultLabel="All"
                options={[...getOrderTypes()]}
                onChange={(e) => setParam({ orderTypeArray: e.target.value })}
              />
            </div>
            <div className="w-full md:w-1/5">
              <SelectInput
                value={orderStatus}
                placeholder="Order Status"
                options={['all', ...getOrderStatuses()]}
                onChange={(event) => setParam({ orderStatus: event.target.value })}
              />
            </div>
            <div className="w-full md:w-1/5">
              <SelectInput
                value={customerName}
                placeholder="Customer Name"
                options={['all', ...getCustomerNames()]}
                onChange={(event) => setParam({ customerName: event.target.value })}
              />
            </div>
            <div className="w-full md:w-1/5">
              <SelectInput
                value={productType}
                placeholder="Product Type"
                options={['all', ...getProductTypes()]}
                onChange={(event) => setParam({ productType: event.target.value })}
              />
            </div>
          </div>
          <div className="flex flex-col gap-2 md:flex-row">
            <div className="w-full md:w-1/5">
              <SelectInput
                value={currency}
                placeholder="Currency"
                options={['all', ...getCurrencies()]}
                onChange={(event) => setParam({ currency: event.target.value })}
              />
            </div>
            <div className="w-full md:w-1/5">
              <SelectInput
                value={year}
                placeholder="Year"
                options={[...getYears()]}
                onChange={(event) => setParam({ year: event.target.value })}
              />
            </div>
            <div className="w-full md:w-1/5">
              <SelectInput
                value={month}
                placeholder="Month"
                options={['all', ...getMonths()]}
                onChange={(event) => setParam({ month: event.target.value })}
              />
            </div>
            <div className="w-full md:w-1/5">
              <TextInput
                value={agingFrom}
                placeholder="Aging From"
                onChange={(event) => setParam({ agingFrom: event.target.value })}
              />
            </div>
            <div className="w-full md:w-1/5">
              <TextInput
                value={agingTo}
                placeholder="Aging To"
                onChange={(event) => setParam({ agingTo: event.target.value })}
              />
            </div>
          </div>
          <div className="flex w-full flex-col gap-4 md:flex-row">
            <div className="flex w-full flex-col md:w-1/2">
              <p className="text-sdp text-md text-center  font-extrabold dark:text-white">
                TOTAL ORDER BY MONTH
              </p>
              <div className="rounded-md bg-white dark:shadow-dashboardCardDark">
                <ReactApexcharts
                  options={{
                    chart: {
                      type: 'bar',
                      toolbar: {
                        show: false,
                      },
                    },
                    xaxis: {
                      categories: getMonths()?.map((o) => o?.toUpperCase()),
                    },
                    plotOptions: {
                      bar: {
                        horizontal: false,
                        columnWidth: '50%',
                      },
                    },
                    dataLabels: {
                      enabled: true,
                    },
                    colors: [getColorCode(module)],
                  }}
                  series={[
                    {
                      data: getMonths()?.map(
                        (o) => getFilteredOrders()?.filter((p) => p?.month === o)?.length
                      ),
                    },
                  ]}
                  type="bar"
                />
              </div>
            </div>
            <div className="flex w-full flex-col md:w-1/2">
              <p className="text-sdp text-md text-center  font-extrabold dark:text-white">
                TOTAL MRC BY MONTH
              </p>
              <div className="rounded-md bg-white dark:shadow-dashboardCardDark">
                <ReactApexcharts
                  options={{
                    chart: {
                      type: 'bar',
                      toolbar: {
                        show: false,
                      },
                    },
                    xaxis: {
                      categories: getMonths()?.map((o) => o?.toUpperCase()),
                    },
                    plotOptions: {
                      bar: {
                        horizontal: false,
                        columnWidth: '50%',
                      },
                    },
                    dataLabels: {
                      enabled: true,
                    },
                    colors: [getColorCode(module)],
                    yaxis: {
                      labels: {
                        formatter: (value) => `${checkAndReplaceNumberWithZero(value)}M`,
                      },
                    },
                    title: {
                      text: `Annual Cumulative: ${mrcGraphSeriesData.reduce((prev, curr) => prev + Number(curr), 0)}M`,
                      align: 'center',
                      style: {
                        fontSize: '12px',
                        fontWeight: 'bold',
                      },
                    },
                  }}
                  series={[
                    {
                      data: mrcGraphSeriesData,
                    },
                  ]}
                  type="bar"
                />
              </div>
            </div>
          </div>
          <SearchInput />
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white text-black">
              <thead>
                <tr>
                  <td className={getHeaderCellStyle()}>Customer Name</td>
                  <td className={getHeaderCellStyle()}>Unit</td>
                  <td className={getHeaderCellStyle()}>Order Number</td>
                  <td className={getHeaderCellStyle()}>Order Type</td>
                  <td className={getHeaderCellStyle()}>Order Status</td>
                  <td className={getHeaderCellStyle()}>RFS Date</td>
                  <td className={getHeaderCellStyle()}>
                    Aging <SortButton objectKey="aging_from_rfs_date_to_today" />
                  </td>
                  <td className={getHeaderCellStyle()}>Currency</td>
                  <td className={getHeaderCellStyle()}>
                    NRC Total Price <SortButton objectKey="nrc_total_net_price" />
                  </td>
                  <td className={getHeaderCellStyle()}>
                    MRC Total Price <SortButton objectKey="mrc_total_net_price" />
                  </td>
                  <td className={getHeaderCellStyle()}>Product Type</td>
                  <td className={getHeaderCellStyle()}>Service ID</td>
                </tr>
              </thead>
              <tbody>
                {getFilteredOrders()
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((row, i) => (
                    <tr key={i} className={twMerge('border-b border-[#fcfcfd]')}>
                      <td
                        className={twMerge(
                          getBodyCellStyle(),
                          'min-w-[250px] max-w-[250px] text-left'
                        )}
                      >
                        {checkAndReplaceStringWithHyphen(row?.customer_name)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.unit)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.order_num)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.order_type)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.order_status)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.rfs_date)}
                      </td>
                      <td
                        className={twMerge(
                          getBodyCellStyle(),
                          (() => {
                            if (row?.order_status?.toLowerCase() === 'completed') return '';
                            if (row?.aging_from_rfs_date_to_today < 1)
                              return 'bg-red-500 text-white';
                            if (row?.aging_from_rfs_date_to_today < 20)
                              return ' bg-yellow-500 text-white';
                            return '';
                          })()
                        )}
                      >
                        {checkAndReplaceStringWithHyphen(
                          row?.order_status?.toLowerCase() === 'completed'
                            ? ''
                            : row?.aging_from_rfs_date_to_today
                        )}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.currency)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceNumberWithZero(row?.nrc_total_net_price)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceNumberWithZero(row?.mrc_total_net_price)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.product_type)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.service_id)}
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
          <TablePaginationCustom count={getFilteredOrders().length} />
        </div>
      </div>
    </>
  );
}
