// Next, React, Tw
import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import { useEffect } from 'react';

// Packages
import * as R from 'ramda';
import moment from 'moment';

// Components
import Layout from '../../layouts/module/sdp';
import ReactAnimatedNumber from '../../components/Shared/ReactAnimatedNumber';
import { SelectInput, SelectMultipleInput } from '../../components/Shared/CustomInput';
import ReactApexcharts from '../../components/Shared/ReactApexcharts';
import TringgerEngineButton from '../../components/hsba/TringgerEngineButton';

// Others
import { useParamContext } from '../../utils/auth/ParamProvider';
import { getColorCode } from '../../utils/shared';
import { useAuthContext } from '../../utils/auth/useAuthContext';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const { query, asPath } = useRouter();
  const module = asPath?.split('?')?.[0]?.split('/')[1];
  const { month, year, unit, orderTypeArray, orderStatus, customerName, productType } = query;
  const { ordersDataUpdatedAt, ordersData } = useSelector((state) => state.sdp);
  const { setParam, replaceParam } = useParamContext();
  const { user } = useAuthContext();

  // Others

  const units = R.uniq(R.pluck('unit', ordersData));
  const orderTypes = R.uniq(R.pluck('order_type', ordersData));
  const orderStatuses = R.uniq(R.pluck('order_status', ordersData));
  const customerNames = R.uniq(R.pluck('customer_name', ordersData));
  const productTypes = R.uniq(R.pluck('product_type', ordersData));
  const years = R.uniq(R.pluck('year', ordersData))?.sort((a, b) => a - b);
  const months = [
    'jan',
    'feb',
    'mar',
    'apr',
    'may',
    'jun',
    'jul',
    'aug',
    'sep',
    'oct',
    'nov',
    'dec',
  ];

  const getFilteredOrders = () => {
    let temp = ordersData?.filter((o) => o?.year === year);

    if (month !== 'all') {
      temp = temp?.filter((o) => o?.month === month);
    }
    if (unit !== 'all') {
      temp = temp?.filter((o) => o?.unit === unit);
    }
    if (orderStatus !== 'all') {
      temp = temp?.filter((o) => o?.order_status === orderStatus);
    }
    if (customerName !== 'all') {
      temp = temp?.filter((o) => o?.customer_name === customerName);
    }
    if (productType !== 'all') {
      temp = temp?.filter((o) => o?.product_type === productType);
    }
    const getOrderTypeArray = () => {
      if (!orderTypeArray) return [];
      return JSON?.parse(orderTypeArray);
    };
    if (!getOrderTypeArray()?.includes('all')) {
      temp = temp?.filter((o) => getOrderTypeArray()?.includes(o?.order_type));
    }
    return temp;
  };

  const getInProgressOrders = () => {
    let temp = getFilteredOrders();
    temp = temp?.filter((o) => !['completed']?.includes(o?.order_status?.toLowerCase()));
    return temp;
  };

  const getCompletedOrders = () => {
    let temp = getFilteredOrders();
    temp = temp?.filter((o) => ['completed']?.includes(o?.order_status?.toLowerCase()));
    return temp;
  };

  useEffect(() => {
    replaceParam({
      year: moment()?.format('YYYY'),
      month: 'all',
      unit: 'all',
      orderTypeArray: '["all"]',
      orderStatus: 'all',
      customerName: 'all',
      productType: 'all',
    });
  }, []);

  return (
    <>
      <div className="bg-sdp px-4 py-2 text-white">
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-extrabold">Overall Performance</p>
            <p className="text-xs">As of : {ordersDataUpdatedAt}</p>
          </div>
          {user?.isSuperAdmin && (
            <TringgerEngineButton process="service_performance" payload={{}} />
          )}
        </div>
      </div>
      <div className="container mx-auto p-1 md:p-4">
        <div className="flex flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
          <div className="flex w-full flex-col gap-4 md:flex-row">
            <div className="flex w-full flex-col gap-2 md:w-[20%]">
              <SelectInput
                value={year}
                placeholder="Year"
                defaultLabel="All"
                options={[...years]}
                onChange={(e) => setParam({ year: e.target.value })}
              />
              <SelectInput
                value={month}
                placeholder="Month"
                defaultLabel="All"
                options={['all', ...months]}
                onChange={(e) => setParam({ month: e.target.value })}
              />
              <SelectInput
                value={unit}
                placeholder="Unit"
                defaultLabel="All"
                options={['all', ...units]}
                onChange={(e) => setParam({ unit: e.target.value })}
              />
              <SelectMultipleInput
                value={orderTypeArray}
                placeholder="Order Type"
                defaultLabel="All"
                options={[...orderTypes]}
                onChange={(e) => setParam({ orderTypeArray: e.target.value })}
              />
              <SelectInput
                value={orderStatus}
                placeholder="Order Status"
                defaultLabel="All"
                options={['all', ...orderStatuses]}
                onChange={(e) => setParam({ orderStatus: e.target.value })}
              />
              <SelectInput
                value={customerName}
                placeholder="Customer Name"
                defaultLabel="All"
                options={['all', ...customerNames]}
                onChange={(e) => setParam({ customerName: e.target.value })}
              />
              <SelectInput
                value={productType}
                placeholder="Product Type"
                defaultLabel="All"
                options={['all', ...productTypes]}
                onChange={(e) => setParam({ productType: e.target.value })}
              />
            </div>
            <div className="flex w-full flex-col gap-2 md:w-[20%]">
              <div className="flex h-1/2 w-full flex-col gap-2 p-2">
                <p className="text-sdp text-md font-extrabold  dark:text-white">TOTAL ORDER</p>
                <p className="text-xs ">IN PROGRESS & COMPLETED</p>
                <p className="text-center text-5xl font-bold">
                  <ReactAnimatedNumber
                    value={getFilteredOrders()?.length}
                    formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                  />
                </p>
              </div>
              <div className="flex h-1/2 w-full flex-col gap-2 p-2">
                <p className="text-sdp text-md font-extrabold dark:text-white">TOTAL ORDER</p>
                <p className="text-xs  ">IN PROGRESS</p>
                <p className="text-center text-5xl font-bold">
                  <ReactAnimatedNumber
                    value={getInProgressOrders()?.length}
                    formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                  />
                </p>
              </div>
            </div>
            <div className="flex w-full flex-col gap-2 md:w-[30%]">
              <p className="text-sdp text-md text-center  font-extrabold dark:text-white">
                ORDER STATUS
              </p>
              <div className="rounded-md bg-white dark:shadow-dashboardCardDark">
                <ReactApexcharts
                  options={{
                    chart: {
                      type: 'donut',
                    },
                    labels: orderStatuses,
                    legend: {
                      position: 'bottom',
                      verticalAlign: 'middle',
                    },
                    dataLabels: {
                      enabled: true,
                      formatter: (_, { seriesIndex, w }) => w.config.series[seriesIndex],
                    },
                    colors: [
                      '#00a915',
                      '#ffff00',
                      '#ff6400',
                      '#ff0000',
                      '#a20000',
                      '#ff00c9',
                      '#0044e1',
                    ],
                  }}
                  series={orderStatuses?.map(
                    (o) => getFilteredOrders()?.filter((p) => p?.order_status === o)?.length
                  )}
                  type="donut"
                />
              </div>
            </div>
            <div className="flex w-full flex-col gap-2 md:w-[30%]">
              <p className="text-sdp text-md text-center  font-extrabold dark:text-white">
                IN PROGRESS ORDER
              </p>
              <div className="rounded-md bg-white dark:shadow-dashboardCardDark">
                <ReactApexcharts
                  options={{
                    chart: {
                      type: 'pie',
                    },
                    labels: units,
                    legend: {
                      position: 'right',
                      verticalAlign: 'middle',
                    },
                    dataLabels: {
                      enabled: true,
                      formatter: (_, { seriesIndex, w }) => w.config.series[seriesIndex],
                    },
                    colors: ['#0044e1', '#00a915', '#ff6400', '#ff00c9'],
                  }}
                  series={units?.map(
                    (o) => getInProgressOrders()?.filter((p) => p?.unit === o)?.length
                  )}
                  type="pie"
                />
              </div>
            </div>
          </div>
          <div className="flex w-full flex-col gap-4 md:flex-row">
            <div className="flex w-full flex-col gap-2 md:w-1/2">
              <p className="text-sdp text-md text-center  font-extrabold dark:text-white">
                COMPLETED ORDER BY MONTH
              </p>
              <div className="rounded-md bg-white dark:shadow-dashboardCardDark">
                <ReactApexcharts
                  options={{
                    chart: {
                      type: 'bar',
                      toolbar: {
                        show: false,
                      },
                    },
                    xaxis: {
                      categories: months?.map((o) => o?.toUpperCase()),
                    },
                    plotOptions: {
                      bar: {
                        horizontal: false,
                        columnWidth: '50%',
                      },
                    },
                    dataLabels: {
                      enabled: true,
                    },
                    colors: [getColorCode(module)],
                  }}
                  series={[
                    {
                      data: months?.map(
                        (o) => getCompletedOrders()?.filter((p) => p?.month === o)?.length
                      ),
                    },
                  ]}
                  type="bar"
                />
              </div>
            </div>
            <div className="flex w-full flex-col gap-2 md:w-1/2">
              <p className="text-sdp text-md text-center  font-extrabold dark:text-white">
                IN PROGRESS ORDER BY MONTH
              </p>
              <div className="rounded-md bg-white dark:shadow-dashboardCardDark">
                <ReactApexcharts
                  options={{
                    chart: {
                      type: 'bar',
                      toolbar: {
                        show: false,
                      },
                    },
                    xaxis: {
                      categories: months?.map((o) => o?.toUpperCase()),
                    },
                    plotOptions: {
                      bar: {
                        horizontal: false,
                        columnWidth: '50%',
                      },
                    },
                    dataLabels: {
                      enabled: true,
                    },
                    legend: {
                      position: 'top',
                      verticalAlign: 'middle',
                    },
                    colors: ['#0044e1', '#00a915', '#ff6400', '#ff00c9'],
                  }}
                  series={units?.map((o) => ({
                    name: o,
                    data: months?.map(
                      (m) =>
                        getInProgressOrders()?.filter((p) => p?.unit === o && p?.month === m)
                          ?.length
                    ),
                  }))}
                  type="bar"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
