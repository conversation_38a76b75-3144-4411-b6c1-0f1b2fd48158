// Next, React, Tw
import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSelector } from 'react-redux';
import { twMerge } from 'tailwind-merge';

// Packages
import * as R from 'ramda';

// Components
import Layout from '../../layouts/module/sdp';
import { TablePaginationCustom } from '../../components/Shared/table';
import ExportExcelButton from '../../components/Shared/ExportExcelButton';
import SortButton from '../../components/Shared/SortButton';

// Others
import { useParamContext } from '../../utils/auth/ParamProvider';
import { SelectInput, SearchInput } from '../../components/Shared/CustomInput';
import {
  checkAndReplaceStringWithHyphen,
  checkAndReplaceNumberWithZero,
  sortArrayOfObjectsByCertainKeyAlphabetically,
  sortArrayOfObjectsByCertainKeyNumerically,
} from '../../utils/shared';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { query } = useRouter();
  const { q, unit, sortedByKey, sortInAscendingOrder } = query;
  const { page, rowsPerPage } = useSelector((state) => state.simi);
  const { ordersDataUpdatedAt, ordersData } = useSelector((state) => state.sdp);
  const { setParam, replaceParam } = useParamContext();

  // Table
  const getHeaderCellStyle = () => 'bg-sdp whitespace-nowrap px-4 text-center text-sm text-white';
  const getBodyCellStyle = () =>
    'text-center py-1 whitespace-nowrap text-[11px] max-w-[100px] overflow-hidden whitespace-nowrap';

  // Others

  const getUnits = () => R.uniq(R.pluck('unit', ordersData));

  const getFilteredOrders = () => {
    let temp = ordersData?.filter((o) => o?.rfs_date === '');

    if (unit !== 'all') {
      temp = temp?.filter((o) => o?.unit === unit);
    }
    if (![undefined, '']?.includes(q)) {
      temp = temp.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
    }
    if (sortedByKey && sortInAscendingOrder) {
      if (Number.isNaN(temp?.[0]?.[sortedByKey])) {
        temp = sortArrayOfObjectsByCertainKeyAlphabetically(
          temp,
          sortedByKey,
          sortInAscendingOrder === 'true'
        );
      } else {
        temp = sortArrayOfObjectsByCertainKeyNumerically(
          temp,
          sortedByKey,
          sortInAscendingOrder === 'true'
        );
      }
    }
    return temp;
  };

  useEffect(() => {
    replaceParam({
      unit: 'all',
    });
  }, []);

  return (
    <>
      <div className="bg-sdp px-4 py-2 text-white">
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-extrabold">No RFS Date Orders</p>
            <p className="text-xs">As of : {ordersDataUpdatedAt}</p>
          </div>
        </div>
      </div>

      <div className="container mx-auto p-1 md:p-4">
        <div className="flex flex-col gap-2 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
          <div className="flex gap-2">
            <div className="w-full md:w-1/5">
              <SelectInput
                value={unit}
                placeholder="Unit"
                options={['all', ...getUnits()]}
                onChange={(event) => setParam({ unit: event.target.value })}
              />
            </div>
          </div>
          <div className="flex flex-col items-center justify-between md:flex-row">
            <SearchInput />
            <ExportExcelButton key={getFilteredOrders()?.length} data={getFilteredOrders()} />
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white text-black">
              <thead>
                <tr>
                  <td className={getHeaderCellStyle()}>Customer Name</td>
                  <td className={getHeaderCellStyle()}>Unit</td>
                  <td className={getHeaderCellStyle()}>Order Number</td>
                  <td className={getHeaderCellStyle()}>Order Type</td>
                  <td className={getHeaderCellStyle()}>Order Status</td>
                  <td className={getHeaderCellStyle()}>RFS Date</td>
                  <td className={getHeaderCellStyle()}>
                    Aging
                    <SortButton objectKey="aging_from_rfs_date_to_today" />
                  </td>
                  <td className={getHeaderCellStyle()}>Currency</td>
                  <td className={getHeaderCellStyle()}>
                    NRC Total Price <SortButton objectKey="nrc_total_net_price" />
                  </td>
                  <td className={getHeaderCellStyle()}>
                    MRC Total Price <SortButton objectKey="mrc_total_net_price" />
                  </td>
                  <td className={getHeaderCellStyle()}>Product Type</td>
                  <td className={getHeaderCellStyle()}>Service ID</td>
                </tr>
              </thead>
              <tbody>
                {getFilteredOrders()
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((row, i) => (
                    <tr
                      key={i}
                      className="border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-200"
                    >
                      <td
                        className={twMerge(
                          getBodyCellStyle(),
                          'min-w-[250px] max-w-[250px] text-left'
                        )}
                      >
                        {checkAndReplaceStringWithHyphen(row?.customer_name)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.unit)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.order_num)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.order_type)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.order_status)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.rfs_date)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.aging_from_rfs_date_to_today)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.currency)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceNumberWithZero(row?.nrc_total_net_price)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceNumberWithZero(row?.mrc_total_net_price)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.product_type)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.service_id)}
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
          <TablePaginationCustom count={getFilteredOrders().length} />
        </div>
      </div>
    </>
  );
}
