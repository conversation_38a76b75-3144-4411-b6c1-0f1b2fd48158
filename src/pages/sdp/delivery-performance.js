// Next, React, Tw
import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { twMerge } from 'tailwind-merge';

// Packages
import * as R from 'ramda';
import moment from 'moment';

// Components
import Layout from '../../layouts/module/sdp';
import ReactAnimatedNumber from '../../components/Shared/ReactAnimatedNumber';
import { SelectInput, SearchInput, SelectMultipleInput } from '../../components/Shared/CustomInput';
import ReactApexcharts from '../../components/Shared/ReactApexcharts';
import { TablePaginationCustom } from '../../components/Shared/table';
import SortButton from '../../components/Shared/SortButton';

// Others
import {
  checkAndReplaceStringWithHyphen,
  sortArrayOfObjectsByCertainKeyAlphabetically,
  sortArrayOfObjectsByCertainKeyNumerically,
} from '../../utils/shared';
import { useParamContext } from '../../utils/auth/ParamProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const { query } = useRouter();
  const {
    q,
    month,
    year,
    unit,
    orderTypeArray,
    orderStatus,
    customerName,
    productType,
    sortedByKey,
    sortInAscendingOrder,
  } = query;
  const { ordersDataUpdatedAt, ordersData } = useSelector((state) => state.sdp);
  const { page, rowsPerPage } = useSelector((state) => state.simi);
  const { setParam, replaceParam } = useParamContext();

  // Table
  const getHeaderCellStyle = () => 'bg-sdp whitespace-nowrap px-4 text-center text-sm text-white';
  const getBodyCellStyle = () =>
    'text-center py-1 whitespace-nowrap text-[11px] max-w-[200px] overflow-hidden whitespace-nowrap';

  // Others

  const getStatusCompletion = () => ['Beyond Target RFS', 'Within Target RFS'];
  const getUnits = () => R.uniq(R.pluck('unit', ordersData));
  const getOrderTypes = () => R.uniq(R.pluck('order_type', ordersData));
  const getOrderStatuses = () => R.uniq(R.pluck('order_status', ordersData));
  const getCustomerNames = () => R.uniq(R.pluck('customer_name', ordersData));
  const getYears = () => R.uniq(R.pluck('year', ordersData))?.sort((a, b) => a - b);
  const getMonths = () => [
    'jan',
    'feb',
    'mar',
    'apr',
    'may',
    'jun',
    'jul',
    'aug',
    'sep',
    'oct',
    'nov',
    'dec',
  ];

  const getFilteredOrders = () => {
    let temp = ordersData?.filter((o) => o?.year === year);

    if (month !== 'all') {
      temp = temp?.filter((o) => o?.month === month);
    }
    if (unit !== 'all') {
      temp = temp?.filter((o) => o?.unit === unit);
    }
    if (orderStatus !== 'all') {
      temp = temp?.filter((o) => o?.order_status === orderStatus);
    }
    if (customerName !== 'all') {
      temp = temp?.filter((o) => o?.customer_name === customerName);
    }
    if (productType !== 'all') {
      temp = temp?.filter((o) => o?.product_type === productType);
    }
    if (![undefined, '']?.includes(q)) {
      temp = temp.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
    }

    const getOrderTypeArray = () => {
      if (!orderTypeArray) return [];
      return JSON?.parse(orderTypeArray);
    };
    if (!getOrderTypeArray()?.includes('all')) {
      temp = temp?.filter((o) => getOrderTypeArray()?.includes(o?.order_type));
    }

    if (sortedByKey && sortInAscendingOrder) {
      if (Number.isNaN(temp?.[0]?.[sortedByKey])) {
        temp = sortArrayOfObjectsByCertainKeyAlphabetically(
          temp,
          sortedByKey,
          sortInAscendingOrder === 'true'
        );
      } else {
        temp = sortArrayOfObjectsByCertainKeyNumerically(
          temp,
          sortedByKey,
          sortInAscendingOrder === 'true'
        );
      }
    }

    return temp;
  };

  useEffect(() => {
    replaceParam({
      year: moment()?.format('YYYY'),
      month: 'all',
      unit: 'all',
      orderTypeArray: '["all"]',
      orderStatus: 'all',
      customerName: 'all',
      productType: 'all',
    });
  }, []);

  return (
    <>
      <div className="bg-sdp px-4 py-2 text-white">
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-extrabold">Delivery Performance</p>
            <p className="text-xs">As of : {ordersDataUpdatedAt}</p>
          </div>
        </div>
      </div>
      <div className="container mx-auto p-1 md:p-4">
        <div className="flex flex-col gap-2 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
          <div className="flex w-full flex-col gap-4 md:flex-row">
            <div className="flex w-full flex-col gap-2 md:w-[20%]">
              <SelectInput
                value={year}
                placeholder="Year"
                defaultLabel="All"
                options={[...getYears()]}
                onChange={(e) => setParam({ year: e.target.value })}
              />
              <SelectInput
                value={month}
                placeholder="Month"
                defaultLabel="All"
                options={['all', ...getMonths()]}
                onChange={(e) => setParam({ month: e.target.value })}
              />
              <SelectInput
                value={unit}
                placeholder="Unit"
                defaultLabel="All"
                options={['all', ...getUnits()]}
                onChange={(e) => setParam({ unit: e.target.value })}
              />
              <SelectMultipleInput
                value={orderTypeArray}
                placeholder="Order Type"
                defaultLabel="All"
                options={[...getOrderTypes()]}
                onChange={(e) => setParam({ orderTypeArray: e.target.value })}
              />
              <SelectInput
                value={orderStatus}
                placeholder="Order Status"
                defaultLabel="All"
                options={['all', ...getOrderStatuses()]}
                onChange={(e) => setParam({ orderStatus: e.target.value })}
              />
              <SelectInput
                value={customerName}
                placeholder="Customer Name"
                defaultLabel="All"
                options={['all', ...getCustomerNames()]}
                onChange={(e) => setParam({ customerName: e.target.value })}
              />
            </div>
            <div className="flex w-full flex-col gap-2 md:w-[30%]">
              <p className="text-sdp text-md text-center  font-extrabold dark:text-white">
                OVERALL PERFORMANCE
              </p>
              <div className="rounded-md bg-white dark:shadow-dashboardCardDark">
                <ReactApexcharts
                  options={{
                    chart: {
                      type: 'pie',
                    },
                    labels: getStatusCompletion(),
                    legend: {
                      position: 'bottom',
                      verticalAlign: 'middle',
                    },
                    colors: ['#ff0000', '#35ff03'],
                  }}
                  series={getStatusCompletion()?.map(
                    (o, i) =>
                      getFilteredOrders()?.filter((p) => Number(p?.within_rfs_target_date) === i)
                        ?.length
                  )}
                  type="pie"
                />
              </div>
            </div>
            <div className="flex w-full flex-col md:w-[20%]">
              {getUnits()?.map((o, i) => (
                <div key={i} className="flex h-1/4 w-full flex-col p-1">
                  <p className="text-sdp text-sm font-extrabold  dark:text-white">{o}</p>
                  <p className="text-center text-3xl font-bold">
                    <ReactAnimatedNumber
                      value={getFilteredOrders()?.filter((p) => p?.unit === o)?.length}
                      formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                    />
                  </p>
                </div>
              ))}
            </div>
            <div className="flex w-full flex-col gap-2 md:w-[30%]">
              <p className="text-sdp text-md text-center  font-extrabold dark:text-white">
                PERFORMANCE BY UNIT
              </p>
              <div className="rounded-md bg-white dark:shadow-dashboardCardDark">
                <ReactApexcharts
                  options={{
                    chart: {
                      type: 'bar',
                      toolbar: {
                        show: false,
                      },
                    },
                    xaxis: {
                      categories: getUnits(),
                    },
                    plotOptions: {
                      bar: {
                        horizontal: false,
                        columnWidth: '50%',
                      },
                    },
                    dataLabels: {
                      enabled: true,
                    },
                    legend: {
                      position: 'top',
                      verticalAlign: 'middle',
                    },
                    colors: ['#ff0000', '#35ff03'],
                  }}
                  series={getStatusCompletion()?.map((o, i) => ({
                    name: o,
                    data: getUnits()?.map(
                      (m) =>
                        getFilteredOrders()?.filter(
                          (p) => p?.unit === m && Number(p?.within_rfs_target_date) === i
                        )?.length
                    ),
                  }))}
                  type="bar"
                />
              </div>
            </div>
          </div>
          <SearchInput />
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white text-black">
              <thead>
                <tr>
                  <td className={getHeaderCellStyle()}>Customer Name</td>
                  <td className={getHeaderCellStyle()}>Unit</td>
                  <td className={getHeaderCellStyle()}>Order Number</td>
                  <td className={getHeaderCellStyle()}>Created Date</td>
                  <td className={getHeaderCellStyle()}>RFS Target</td>
                  <td className={getHeaderCellStyle()}>RFS Date</td>
                  <td className={getHeaderCellStyle()}>Status Completion</td>
                  <td className={getHeaderCellStyle()}>
                    Completion Day Gap
                    <SortButton objectKey="completion_day_gap" />
                  </td>
                  <td className={getHeaderCellStyle()}>
                    Aging
                    <SortButton objectKey="aging_from_created_date_to_today" />
                  </td>
                  <td className={getHeaderCellStyle()}>Order Type</td>
                  <td className={getHeaderCellStyle()}>Order Status</td>
                  <td className={getHeaderCellStyle()}>Product Type</td>
                </tr>
              </thead>
              <tbody>
                {getFilteredOrders()
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((row, i) => (
                    <tr
                      key={i}
                      className="border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-200"
                    >
                      <td
                        className={twMerge(
                          getBodyCellStyle(),
                          'min-w-[250px] max-w-[250px] text-left'
                        )}
                      >
                        {checkAndReplaceStringWithHyphen(row?.customer_name)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.unit)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.order_num)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.order_created_date)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.rfs_target_date)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.rfs_date)}
                      </td>
                      <td
                        className={twMerge(
                          getBodyCellStyle(),
                          row?.within_rfs_target_date ? 'text-green-500' : 'text-red-500'
                        )}
                      >
                        {row?.within_rfs_target_date ? 'Within Target RFS' : 'Beyond Target RFS'}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(
                          row?.order_status?.toLowerCase() === 'completed'
                            ? row?.completion_day_gap
                            : ''
                        )}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(
                          row?.order_status?.toLowerCase() === 'completed'
                            ? ''
                            : row?.aging_from_created_date_to_today
                        )}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.order_type)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.order_status)}
                      </td>
                      <td className={getBodyCellStyle()}>
                        {checkAndReplaceStringWithHyphen(row?.product_type)}
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
          <TablePaginationCustom count={getFilteredOrders().length} />
        </div>
      </div>
    </>
  );
}
