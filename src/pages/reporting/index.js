// Next, React, Tw
import React, { useState, useEffect } from 'react';
// cspell:disable-next-line
/* eslint-disable-next-line import/no-extraneous-dependencies */
import Highcharts from 'highcharts';
// cspell:disable-next-line
/* eslint-disable-next-line import/no-extraneous-dependencies */
import HighchartsReact from 'highcharts-react-official';

// Mui
import { Dialog } from '@mui/material';

// Packages
import DocViewer, { DocViewerRenderers } from '@cyntler/react-doc-viewer';
import '@cyntler/react-doc-viewer/dist/index.css';

// Components
import Layout from '../../layouts/module/reporting';
import { SelectInput } from '../../components/Shared/CustomInput';

// Others
import { getColorCode } from '../../utils/shared';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { useAuthContext } from '../../utils/auth/useAuthContext';

// Data
import performanceDataJSON from '../../data/performanceData.json';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Performance tracking data with month column and subproducts
  const performanceData = performanceDataJSON;

  // State for selected product and month filters
  const { query, setParam } = useParamContext();
  const { isAuthenticated } = useAuthContext();
  const [selectedProduct, setSelectedProduct] = useState('All Products');
  const [selectedMonth, setSelectedMonth] = useState('All Months');
  const [showSubproducts, setShowSubproducts] = useState(false);
  const [selectedSubproduct, setSelectedSubproduct] = useState('All Subproducts');
  const [documentUrl, setDocumentUrl] = useState(null);
  const [downloadStatus, setDownloadStatus] = useState('');
  const [isDetailedReportsExpanded, setIsDetailedReportsExpanded] = useState(false);

  // Generate product options for the select input
  const productOptions = [
    { value: 'All Products', label: 'All Products' },
    ...performanceData
      .filter((item) => item.product !== 'TOTAL OVERALL')
      .map((item) => item.product)
      .filter((value, index, self) => self.indexOf(value) === index)
      .map((product) => ({ value: product, label: product })),
  ];

  // Generate month options for the select input
  const monthOptions = [
    { value: 'All Months', label: 'All Months' },
    ...performanceData
      .map((item) => item.month)
      .filter((value, index, self) => self.indexOf(value) === index)
      .map((month) => ({ value: month, label: month })),
  ];

  // Generate subproduct options for the select input based on selected product
  const getSubproductOptions = () => {
    // Start with All Subproducts option
    const options = [{ value: 'All Subproducts', label: 'All Subproducts' }];
    
    // If a specific product is selected, get its subproducts
    if (selectedProduct !== 'All Products') {
      const subproducts = performanceData
        .filter(item => item.product === selectedProduct)
        .flatMap(item => item.subproducts || [])
        .map(subItem => subItem.product)
        .filter((value, index, self) => self.indexOf(value) === index);
      
      // Add each unique subproduct as an option
      subproducts.forEach(subproduct => {
        options.push({ value: subproduct, label: subproduct });
      });
    } else {
      // If all products selected, get all unique subproducts
      const allSubproducts = performanceData
        .flatMap(item => item.subproducts || [])
        .map(subItem => subItem.product)
        .filter((value, index, self) => self.indexOf(value) === index);
      
      // Add each unique subproduct as an option
      allSubproducts.forEach(subproduct => {
        options.push({ value: subproduct, label: subproduct });
      });
    }
    
    return options;
  };

  const subproductOptions = getSubproductOptions();

  // Filter main products data based on selected filters
  const filteredData = performanceData.filter(
    (item) => 
      (selectedProduct === 'All Products' || item.product === selectedProduct) &&
      (selectedMonth === 'All Months' || item.month === selectedMonth)
  );

  // Prepare both main products and filtered subproducts if showing subproducts
  const getFilteredDataWithSubproducts = () => {
    if (!showSubproducts || selectedSubproduct === 'All Subproducts') {
      return filteredData;
    }

    // Filter main product data to only those with matching subproducts
    return filteredData.filter(item => 
      item.subproducts && item.subproducts.some(sub => sub.product === selectedSubproduct)
    );
  };

  const finalFilteredData = getFilteredDataWithSubproducts();

  // Reset subproduct selection when product changes
  useEffect(() => {
    setSelectedSubproduct('All Subproducts');
  }, [selectedProduct]);

  // Function to format numbers with commas and decimal points
  const formatNumber = (num) => {
    if (num === undefined || num === null) return '-';
    return num.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  // Function to format percentages
  const formatPercent = (num) => {
    if (num === undefined || num === null) return '-';
    return `${num.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}%`;
  };

  // Function to determine the text color based on value
  const getTextColorClass = (value) => {
    if (value === undefined || value === null) return '';
    return value < 0 ? 'text-red-600 font-semibold' : '';
  };

  // Available documents mapping
  const availableDocuments = [
    {
      name: 'TMG Product Marketing Revenue Monthly Report - January 2025.pptx',
      month: 'Jan',
      displayName: 'January 2025 Revenue Report',
      path: '/xu8CxJ2VjGvFiEGCR88e5EtehkLhzf/reporting/docs/TMG Product Marketing Revenue Monthly Report - January 2025.pptx',
      type: 'powerpoint'
    },
    {
      name: 'TMG Product Marketing Revenue Monthly Report - February 2025.pptx',
      month: 'Feb',
      displayName: 'February 2025 Revenue Report',
      path: '/xu8CxJ2VjGvFiEGCR88e5EtehkLhzf/reporting/docs/TMG Product Marketing Revenue Monthly Report - February 2025.pptx',
      type: 'powerpoint'
    },
    {
      name: 'TMG Product Marketing Revenue Monthly Report - March 2025.pptx',
      month: 'Mar',
      displayName: 'March 2025 Revenue Report',
      path: '/xu8CxJ2VjGvFiEGCR88e5EtehkLhzf/reporting/docs/TMG Product Marketing Revenue Monthly Report - March 2025.pptx',
      type: 'powerpoint'
    },
    {
      name: 'Physical-Jan.zip',
      month: 'Jan',
      displayName: 'January 2025 Physical Documents',
      path: '/xu8CxJ2VjGvFiEGCR88e5EtehkLhzf/reporting/docs/Physical-Jan.zip',
      type: 'zip'
    },
    {
      name: 'Physical-Feb.zip',
      month: 'Feb',
      displayName: 'February 2025 Physical Documents',
      path: '/xu8CxJ2VjGvFiEGCR88e5EtehkLhzf/reporting/docs/Physical-Feb.zip',
      type: 'zip'
    },
    {
      name: 'Physical-Mar.zip',
      month: 'Mar',
      displayName: 'March 2025 Physical Documents',
      path: '/xu8CxJ2VjGvFiEGCR88e5EtehkLhzf/reporting/docs/Physical-Mar.zip',
      type: 'zip'
    }
  ];

  // Get relevant documents based on selected month
  const getRelevantDocuments = () => {
    if (selectedMonth === 'All Months') {
      return availableDocuments;
    }
    return availableDocuments.filter(doc => doc.month === selectedMonth);
  };

  // Handle document viewing - uses Google Docs viewer for PowerPoint files, direct download for ZIP files
  const handleDocumentView = async (docItem) => {
    try {
      // For ZIP files, directly download instead of trying to view
      if (docItem.type === 'zip') {
        setDownloadStatus(`📦 ZIP files cannot be viewed online. Starting download of ${docItem.displayName}...`);
        await handleDocumentDownload(docItem);
        return;
      }

      // Get the full URL for the document
      const fullUrl = `${window.location.origin}${docItem.path}`;

      // Use Google Docs viewer for PowerPoint files
      const googleViewerUrl = `https://docs.google.com/gview?url=${encodeURIComponent(fullUrl)}&embedded=true`;

      // Open in new tab with Google Docs viewer
      const newWindow = window.open(googleViewerUrl, '_blank');

      // Check if popup was blocked
      if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
        // Popup was blocked, show message
        setDownloadStatus(`⚠️ Popup blocked. Please allow popups or use the Download button.`);
        setTimeout(() => setDownloadStatus(''), 5000);
      } else {
        setDownloadStatus(`📖 Opening ${docItem.displayName} in Google Docs viewer...`);
        setTimeout(() => setDownloadStatus(''), 3000);
      }
    } catch (error) {
      console.error('Error opening document:', error);
      setDownloadStatus(`❌ Could not open viewer. Please try the Download button.`);
      setTimeout(() => setDownloadStatus(''), 5000);
    }
  };

  // Handle document download with Chrome-specific compatibility
  const handleDocumentDownload = async (docItem) => {
    try {
      setDownloadStatus(`Starting download of ${docItem.displayName}...`);

      // Detect Chrome browser
      const isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);

      if (isChrome) {
        // Chrome-specific method: Use window.open with download attribute
        const link = document.createElement('a');
        link.href = docItem.path;
        link.download = docItem.name;
        link.target = '_blank';
        link.rel = 'noopener noreferrer';
        link.style.display = 'none';

        // For Chrome, we need to add the link to DOM and trigger click
        document.body.appendChild(link);

        // Use setTimeout to ensure DOM is updated
        setTimeout(() => {
          link.click();
          document.body.removeChild(link);
        }, 100);

        setDownloadStatus(`✅ ${docItem.displayName} download initiated! Check Chrome's download bar at the bottom.`);
      } else {
        // Method 1: Try fetch and blob approach for other browsers
        const response = await fetch(docItem.path);
        if (response.ok) {
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = docItem.name;
          link.style.display = 'none';

          // Add to DOM, click, and remove
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // Clean up the blob URL
          window.URL.revokeObjectURL(url);

          setDownloadStatus(`✅ ${docItem.displayName} download started! Check your Downloads folder.`);
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      }

      // Clear status after 5 seconds
      setTimeout(() => setDownloadStatus(''), 5000);
    } catch (error) {
      console.error('Error downloading document:', error);
      setDownloadStatus(`⚠️ Primary download failed. Trying alternative method...`);

      // Method 2: Fall back to window.open approach
      try {
        // Open in new tab - Chrome will handle as download
        window.open(docItem.path, '_blank');
        setDownloadStatus(`✅ ${docItem.displayName} opened in new tab. Chrome should download it automatically.`);

        setTimeout(() => setDownloadStatus(''), 5000);
      } catch (fallbackError) {
        console.error('Fallback download failed:', fallbackError);
        setDownloadStatus(`❌ Download failed. Please try right-clicking the View button and selecting "Save link as..."`);

        setTimeout(() => setDownloadStatus(''), 8000);
      }
    }
  };

  // Prepare data and options for Highcharts
  const getChartOptions = () => {
    // For Total Overall chart when All Products is selected
    if (selectedProduct === 'All Products') {
      const totalOverallData = performanceData.filter(item => item.product === 'TOTAL OVERALL');
      if (totalOverallData.length) {
        // Filter by month if month is selected
        const filteredTotalData = selectedMonth === 'All Months' 
          ? totalOverallData 
          : totalOverallData.filter(item => item.month === selectedMonth);
        
        if (filteredTotalData.length === 0) return null;
        
        const months = filteredTotalData.map(item => item.month);
        const actualData = filteredTotalData.map(item => item.actual);
        const targetData = filteredTotalData.map(item => item.target);
        
        // Calculate variance data
        const varianceData = actualData.map((actual, idx) => +(actual - targetData[idx]).toFixed(2));
        
        return generateChartConfig(months, actualData, targetData, varianceData, 'TOTAL OVERALL PERFORMANCE');
      }
      return null;
    }

    // For subproduct chart (existing code)
    if (showSubproducts && selectedSubproduct !== 'All Subproducts') {
      // Find all instances of the selected subproduct across months
      const allSubproductData = [];
      
      // Get all filtered products
      const productEntries = performanceData.filter(
        (item) => item.product === selectedProduct &&
        (selectedMonth === 'All Months' || item.month === selectedMonth)
      );
      
      // Extract the subproduct data
      productEntries.forEach(entry => {
        const subproduct = entry.subproducts?.find(sub => sub.product === selectedSubproduct);
        if (subproduct) {
          allSubproductData.push({
            ...subproduct,
            parentMonth: entry.month
          });
        }
      });
      
      if (allSubproductData.length === 0) return null;
      
      // Sort by month to ensure consistent order
      allSubproductData.sort((a, b) => {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        return months.indexOf(a.month) - months.indexOf(b.month);
      });
      
      const months = allSubproductData.map(item => item.month);
      const actualData = allSubproductData.map(item => item.actual);
      const targetData = allSubproductData.map(item => item.target);
      
      // Calculate variance data
      const varianceData = actualData.map((actual, idx) => +(actual - targetData[idx]).toFixed(2));
      
      // Return chart config for subproduct
      return generateChartConfig(months, actualData, targetData, varianceData, selectedSubproduct);
    }
    
    // For product chart (original functionality)
    const productData = performanceData.filter((item) => item.product === selectedProduct);
    if (!productData.length) return null;

    const months = productData.map(item => item.month);
    const actualData = productData.map(item => item.actual);
    const targetData = productData.map(item => item.target);
    
    // Calculate variance data
    const varianceData = actualData.map((actual, idx) => +(actual - targetData[idx]).toFixed(2));

    // Return chart config for product
    return generateChartConfig(months, actualData, targetData, varianceData, selectedProduct);
  };

  // Helper function to generate chart configuration
  const generateChartConfig = (months, actualData, targetData, varianceData, title) => {
    return {
      chart: {
        height: 300,
        marginRight: 100, // Add space for second y-axis
        spacingTop: 20,
        spacingBottom: 20,
        backgroundColor: '#FAFAFA', // Light background like Notion
        borderRadius: 8,
        style: {
          fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, sans-serif',
        },
      },
      credits: {
        enabled: false, // Remove Highcharts watermark
      },
      title: {
        text: title, // Display product or subproduct name as title
        align: 'left',
        style: {
          fontSize: '14px',
          fontWeight: 'bold'
        }
      },
      xAxis: {
        categories: months,
        lineColor: '#EAEAEA',
        tickColor: '#EAEAEA',
        labels: {
          style: {
            color: '#505050',
            fontSize: '12px',
          },
        },
        gridLineWidth: 0,
      },
      yAxis: [
        {
          // Primary yAxis for Target and Actual
          title: {
            text: 'Target & Actual (RM mil)',
            style: {
              color: '#505050',
              fontSize: '12px',
              fontWeight: 'normal',
            },
          },
          min: 0, // Ensure axis starts at 0
          gridLineColor: '#EAEAEA',
          gridLineDashStyle: 'Dash',
          labels: {
            style: {
              color: '#505050',
              fontSize: '11px',
            },
          },
        },
        {
          // Secondary yAxis for Variance
          title: {
            text: 'Variance (RM mil)',
            style: {
              color: '#E67E22', // Softer orange for variance
              fontSize: '12px',
              fontWeight: 'normal',
            },
          },
          opposite: true,
          gridLineWidth: 0,
          labels: {
            style: {
              color: '#E67E22', // Softer orange for variance
              fontSize: '11px',
            },
          },
        },
      ],
      legend: {
        align: 'center',
        verticalAlign: 'top',
        margin: 25,
        itemStyle: {
          fontWeight: 'normal',
        },
      },
      tooltip: {
        shared: true,
        useHTML: true,
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderWidth: 0,
        borderRadius: 8,
        shadow: true,
        style: {
          fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, sans-serif',
          fontSize: '12px',
          padding: '12px',
        },
        headerFormat:
          '<div style="font-size:12px; font-weight:bold; margin-bottom:5px">{point.key}</div><table style="border-collapse:collapse; width:100%">',
        pointFormat:
          '<tr><td style="color:{series.color}; padding:2px 8px 2px 0">{series.name}: </td>' +
          '<td style="text-align:right; padding:2px 0"><b>{point.y:.2f} RM mil</b></td></tr>',
        footerFormat: '</table>',
        valueDecimals: 2,
      },
      plotOptions: {
        column: {
          grouping: true,
          pointPadding: 0.15,
          borderRadius: 3,
          borderWidth: 0,
          opacity: 0.9,
        },
        spline: {
          lineWidth: 2,
          marker: {
            enabled: true,
            radius: 4,
            symbol: 'circle',
          },
        },
      },
      series: [
        {
          name: 'Target',
          type: 'column',
          color: '#637CBF', // Muted blue, more notion-like
          data: targetData,
          yAxis: 0,
        },
        {
          name: 'Actual',
          type: 'column',
          color: '#EA6A6B', // Muted pink/red, more notion-like
          data: actualData,
          yAxis: 0,
        },
        {
          name: 'Variance (A-T)',
          type: 'spline',
          color: '#E67E22', // Softer orange
          data: varianceData,
          yAxis: 1,
        },
      ],
    };
  };

  const chartOptions = getChartOptions();

  // Get summary metrics for product or subproduct
  const getSummaryMetrics = () => {
    // For Total Overall summary when All Products is selected
    if (selectedProduct === 'All Products') {
      const totalOverallData = performanceData.filter(item => item.product === 'TOTAL OVERALL');
      if (totalOverallData.length) {
        // Filter by month if month is selected
        const filteredTotalData = selectedMonth === 'All Months' 
          ? totalOverallData 
          : totalOverallData.filter(item => item.month === selectedMonth);
        
        if (filteredTotalData.length === 0) return null;
        
        let totalActual = 0;
        let totalTarget = 0;
        let fullYearTarget = 0;
        let funnelRealistic = 0;
        let ytdActual = 0;
        let ytdTarget = 0;
        
        filteredTotalData.forEach((item, idx) => {
          // Use the last item for ytd values and full year target to get the most recent data
          if (idx === filteredTotalData.length - 1) {
            ytdActual = item.ytdActual;
            ytdTarget = item.ytdTarget;
            fullYearTarget = item.fullYearTarget;
            funnelRealistic = item.funnelRealistic;
          }
          totalActual += item.actual;
          totalTarget += item.target;
        });
        
        const variance = +(totalActual - totalTarget).toFixed(2);
        const percentVariance = totalTarget !== 0 ? +((variance / totalTarget) * 100).toFixed(2) : 0;
        const surplusGap = +(funnelRealistic - fullYearTarget).toFixed(2);
        
        return {
          title: 'TOTAL OVERALL PERFORMANCE',
          fullYearTarget,
          totalActual,
          totalTarget,
          ytdActual,
          ytdTarget,
          variance,
          percentVariance,
          funnelRealistic,
          surplusGap
        };
      }
    }
    
    // For subproduct summary
    if (showSubproducts && selectedSubproduct !== 'All Subproducts') {
      let totalActual = 0;
      let totalTarget = 0;
      let fullYearTarget = 0;
      let funnelRealistic = 0;
      let ytdActual = 0;
      let ytdTarget = 0;
      let count = 0;
      let latestSubproduct = null;
      
      // Gather all instances of the selected subproduct
      performanceData.forEach(product => {
        product.subproducts?.forEach(sub => {
          if (sub.product === selectedSubproduct) {
            if (selectedMonth === 'All Months' || product.month === selectedMonth) {
              // Keep track of the latest subproduct data for ytd values
              if (!latestSubproduct || 
                  (product.month && latestSubproduct.parentMonth && 
                   product.month.localeCompare(latestSubproduct.parentMonth) > 0)) {
                latestSubproduct = { ...sub, parentMonth: product.month };
              }
              
              totalActual += sub.actual;
              totalTarget += sub.target;
              count++;
            }
          }
        });
      });
      
      if (count > 0 && latestSubproduct) {
        ytdActual = latestSubproduct.ytdActual;
        ytdTarget = latestSubproduct.ytdTarget;
        fullYearTarget = latestSubproduct.fullYearTarget;
        funnelRealistic = latestSubproduct.funnelRealistic;
        
        const variance = +(totalActual - totalTarget).toFixed(2);
        const percentVariance = totalTarget !== 0 ? +((variance / totalTarget) * 100).toFixed(2) : 0;
        const surplusGap = +(funnelRealistic - fullYearTarget).toFixed(2);
        
        return {
          title: selectedSubproduct,
          fullYearTarget,
          totalActual,
          totalTarget,
          ytdActual,
          ytdTarget,
          variance,
          percentVariance,
          funnelRealistic,
          surplusGap
        };
      }
    }
    
    // For product summary (or if no subproduct selected)
    if (selectedProduct !== 'All Products') {
      let totalActual = 0;
      let totalTarget = 0;
      let fullYearTarget = 0;
      let funnelRealistic = 0;
      let ytdActual = 0;
      let ytdTarget = 0;
      let count = 0;
      let latestProduct = null;
      
      // Filter and sum product data
      const filteredProducts = performanceData.filter(
        item => item.product === selectedProduct && 
        (selectedMonth === 'All Months' || item.month === selectedMonth)
      );
      
      filteredProducts.forEach(product => {
        // Keep track of the latest product data for ytd values
        if (!latestProduct || 
            (product.month && latestProduct.month && 
             product.month.localeCompare(latestProduct.month) > 0)) {
          latestProduct = product;
        }
        
        totalActual += product.actual;
        totalTarget += product.target;
        count++;
      });
      
      if (count > 0 && latestProduct) {
        ytdActual = latestProduct.ytdActual;
        ytdTarget = latestProduct.ytdTarget;
        fullYearTarget = latestProduct.fullYearTarget;
        funnelRealistic = latestProduct.funnelRealistic;
        
        const variance = +(totalActual - totalTarget).toFixed(2);
        const percentVariance = totalTarget !== 0 ? +((variance / totalTarget) * 100).toFixed(2) : 0;
        const surplusGap = +(funnelRealistic - fullYearTarget).toFixed(2);
        
        return {
          title: selectedProduct,
          fullYearTarget,
          totalActual,
          totalTarget,
          ytdActual,
          ytdTarget,
          variance,
          percentVariance,
          funnelRealistic,
          surplusGap
        };
      }
    }
    
    return null;
  };
  
  const summaryMetrics = getSummaryMetrics();

  return (
    <>
      <div
        className="bg-reporting px-4 py-2 text-white"
        style={{ backgroundColor: getColorCode('reporting') }}
      >
        <div className="flex flex-col gap-1">
          <p className="text-lg font-extrabold">PERFORMANCE TRACKING</p>
          <p className="text-sm">Track and analyze performance metrics.</p>
        </div>
      </div>

      <div className="container mx-auto p-4">
        <div className="mb-6">
          <h2 className="mb-4 text-xl font-bold">Performance Summary</h2>

          <div className="mb-4 flex flex-col space-y-4 md:flex-row md:space-x-4 md:space-y-0">
            <div className="w-full md:w-1/5">
              <SelectInput
                label="Filter by Product"
                options={productOptions}
                value={selectedProduct}
                onChange={(e) => setSelectedProduct(e.target.value)}
              />
            </div>
            <div className="w-full md:w-1/5">
              <SelectInput
                label="Filter by Month"
                options={monthOptions}
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(e.target.value)}
              />
            </div>
            <div className="w-full flex items-end md:w-1/5">
              <label className="inline-flex items-center cursor-pointer">
                <input 
                  type="checkbox" 
                  className="sr-only peer"
                  checked={showSubproducts}
                  onChange={() => {
                    setShowSubproducts(!showSubproducts);
                    if (!showSubproducts) {
                      setSelectedSubproduct('All Subproducts');
                    }
                  }}
                />
                <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                <span className="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">Show Subproducts</span>
              </label>
            </div>
            {showSubproducts && (
              <div className="w-full md:w-1/5">
                <SelectInput
                  label="Filter by Subproduct"
                  options={subproductOptions}
                  value={selectedSubproduct}
                  onChange={(e) => setSelectedSubproduct(e.target.value)}
                />
              </div>
            )}
          </div>

          {summaryMetrics && (
            <div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-3">
              <div className="rounded-lg bg-blue-50 p-4 shadow-sm">
                <div className="mb-1 text-sm font-semibold text-gray-500">Full Year Target</div>
                <div className="text-xl font-bold">{formatNumber(summaryMetrics.fullYearTarget)} <span className="text-sm">RM mil</span></div>
              </div>
              <div className="rounded-lg bg-green-50 p-4 shadow-sm">
                <div className="mb-1 text-sm font-semibold text-gray-500">YTD Actual</div>
                <div className="text-xl font-bold">{formatNumber(summaryMetrics.ytdActual)} <span className="text-sm">RM mil</span></div>
              </div>
              <div className="rounded-lg bg-orange-50 p-4 shadow-sm">
                <div className="mb-1 text-sm font-semibold text-gray-500">YTD Target</div>
                <div className="text-xl font-bold">{formatNumber(summaryMetrics.ytdTarget)} <span className="text-sm">RM mil</span></div>
              </div>
            </div>
          )}

          {chartOptions && (
            <div className="mb-6 rounded border p-4 shadow-md">
              <HighchartsReact
                highcharts={Highcharts}
                options={chartOptions}
                containerProps={{ style: { height: '300px' } }}
              />
            </div>
          )}

          {/* Detailed Reports Section */}
          {getRelevantDocuments().length > 0 && (
            <div className="mb-6 rounded-lg border border-gray-200 bg-white shadow-sm">
              <div className="p-6 pb-0">
                <div className="mb-4 flex items-center justify-between">
                  <button
                    type="button"
                    onClick={() => setIsDetailedReportsExpanded(!isDetailedReportsExpanded)}
                    className="flex items-center space-x-2 text-lg font-semibold text-gray-900 hover:text-gray-700 focus:outline-none"
                  >
                    <h3>Detailed Reports</h3>
                    <svg
                      className={`h-5 w-5 transform transition-transform duration-200 ${isDetailedReportsExpanded ? 'rotate-180' : ''}`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                  <div className="flex items-center text-sm text-gray-500">
                    <svg className="mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    {selectedMonth === 'All Months' ? `${getRelevantDocuments().length} reports` : `${getRelevantDocuments().length} reports for ${selectedMonth}`}
                  </div>
                </div>
              </div>

              {/* Collapsible Content */}
              {isDetailedReportsExpanded && (
                <div className="px-6 pb-6">
                  {/* Download Status */}
                  {downloadStatus && (
                    <div className="mb-4 rounded-md bg-blue-50 border border-blue-200 p-3">
                      <div className="flex items-center">
                        <svg className="mr-2 h-4 w-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <span className="text-sm text-blue-700">{downloadStatus}</span>
                      </div>
                    </div>
                  )}

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                {getRelevantDocuments().map((document, index) => (
                  <div key={index} className="rounded-lg border border-gray-200 bg-gray-50 p-4 transition-all hover:bg-gray-100">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center mb-2">
                          {document.type === 'zip' ? (
                            <svg className="mr-2 h-5 w-5 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                            </svg>
                          ) : (
                            <svg className="mr-2 h-5 w-5 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                            </svg>
                          )}
                          <span className="text-sm font-medium text-gray-900">{document.displayName}</span>
                        </div>
                        <p className="text-xs text-gray-500 mb-3">
                          {document.type === 'zip' ? 'ZIP Archive' : 'PowerPoint Presentation'} • {document.month} 2025
                        </p>

                        <div className="flex space-x-2">
                          {document.type === 'zip' ? (
                            // For ZIP files, only show download button since they can't be viewed online
                            <button
                              type="button"
                              onClick={() => handleDocumentDownload(document)}
                              className="inline-flex items-center rounded-md bg-purple-600 px-3 py-1.5 text-xs font-medium text-white shadow-sm hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-1"
                            >
                              <svg className="mr-1 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                              Download ZIP
                            </button>
                          ) : (
                            // For PowerPoint files, show both view and download buttons
                            <>
                              <button
                                type="button"
                                onClick={() => handleDocumentView(document)}
                                className="inline-flex items-center rounded-md bg-blue-600 px-3 py-1.5 text-xs font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                              >
                                <svg className="mr-1 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                                View
                              </button>
                              <button
                                type="button"
                                onClick={() => handleDocumentDownload(document)}
                                className="inline-flex items-center rounded-md bg-gray-600 px-3 py-1.5 text-xs font-medium text-white shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1"
                              >
                                <svg className="mr-1 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                Download
                              </button>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-4 rounded-md bg-blue-50 p-3">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <div className="text-sm text-blue-700 space-y-1">
                      {selectedMonth === 'All Months' ? (
                        <p><strong>Tip:</strong> Filter by a specific month to see only relevant reports for that period.</p>
                      ) : (
                        <p><strong>Filtered:</strong> Showing reports for {selectedMonth}. Select "All Months" to see all reports.</p>
                      )}
                      <p><strong>Document Types:</strong> PowerPoint files can be viewed online or downloaded. ZIP archives contain physical documents and can only be downloaded.</p>
                      <p><strong>Document Access:</strong> Use <strong>View</strong> for secure preview or <strong>Download</strong> to save files.</p>
                    </div>
                  </div>
                </div>
              </div>
                </div>
              )}
            </div>
          )}

          <div className="overflow-x-auto rounded-lg shadow">
            <table className="min-w-full divide-y divide-gray-200">
              <thead>
                <tr className="bg-gray-50">
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Product
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    FY Target
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Month
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Actual
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Target
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    YTD Actual
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    YTD Target
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Variance
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    % YTD
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Funnel
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500"
                  >
                    Surplus/Gap
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {(() => {
                  // Group data by product for merging cells
                  const groupedData = {};
                  finalFilteredData.forEach(row => {
                    if (!groupedData[row.product]) {
                      groupedData[row.product] = [];
                    }
                    groupedData[row.product].push(row);
                  });

                  // Render rows with merged cells for same products
                  return Object.entries(groupedData).flatMap(([product, rows]) => {
                    // First render the main product rows
                    const mainRows = rows.map((row, idx) => (
                      <tr key={`${product}-${row.month}`} className={row.bgColor}>
                        {idx === 0 ? (
                          <td 
                            className="whitespace-nowrap px-6 py-4 text-sm font-semibold text-gray-900"
                            rowSpan={rows.length}
                          >
                            {row.product}
                          </td>
                        ) : null}
                        {idx === 0 ? (
                          <td 
                            className="whitespace-nowrap px-6 py-4 text-right text-sm text-gray-700"
                            rowSpan={rows.length}
                          >
                            {formatNumber(rows[rows.length - 1].fullYearTarget)}
                          </td>
                        ) : null}
                        <td className="whitespace-nowrap px-6 py-4 text-center text-sm text-gray-700">
                          {row.month}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-right text-sm text-gray-700">
                          {formatNumber(row.actual)}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-right text-sm text-gray-700">
                          {formatNumber(row.target)}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-right text-sm text-gray-700">
                          {formatNumber(row.ytdActual)}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-right text-sm text-gray-700">
                          {formatNumber(row.ytdTarget)}
                        </td>
                        <td
                          className={`whitespace-nowrap px-6 py-4 text-right text-sm text-gray-700 ${getTextColorClass(row.variance)}`}
                        >
                          {formatNumber(row.variance)}
                        </td>
                        <td
                          className={`whitespace-nowrap px-6 py-4 text-right text-sm text-gray-700 ${getTextColorClass(row.percentYtd)}`}
                        >
                          {formatPercent(row.percentYtd)}
                        </td>
                        <td className="whitespace-nowrap px-6 py-4 text-right text-sm text-gray-700">
                          {formatNumber(row.funnelRealistic)}
                        </td>
                        <td
                          className={`whitespace-nowrap px-6 py-4 text-right text-sm text-gray-700 ${getTextColorClass(row.surplusGap)}`}
                        >
                          {formatNumber(row.surplusGap)}
                        </td>
                      </tr>
                    ));

                    // Then render the subproduct rows if they exist and the toggle is on
                    if (showSubproducts) {
                      // Collect all subproducts from all months for this product
                      const allSubproducts = [];
                      rows.forEach(row => {
                        if (row.subproducts && row.subproducts.length > 0) {
                          row.subproducts.forEach(subRow => {
                            // Only include subproducts that match the filter if one is selected
                            if (selectedSubproduct === 'All Subproducts' || subRow.product === selectedSubproduct) {
                              allSubproducts.push({
                                ...subRow,
                                parentMonth: row.month
                              });
                            }
                          });
                        }
                      });

                      // Group subproducts by name
                      const groupedSubproducts = {};
                      allSubproducts.forEach(subRow => {
                        if (!groupedSubproducts[subRow.product]) {
                          groupedSubproducts[subRow.product] = [];
                        }
                        groupedSubproducts[subRow.product].push(subRow);
                      });

                      // Render merged subproduct rows
                      const subproductRows = Object.entries(groupedSubproducts).flatMap(([subproductName, subRows]) => {
                        return subRows.map((subRow, idx) => (
                          <tr key={`${product}-${subRow.product}-${subRow.month}`} className="bg-gray-50">
                            {idx === 0 ? (
                              <td 
                                className="whitespace-nowrap pl-10 py-4 text-sm italic text-gray-700"
                                rowSpan={subRows.length}
                              >
                                {subRow.product}
                              </td>
                            ) : null}
                            {idx === 0 ? (
                              <td 
                                className="whitespace-nowrap px-6 py-4 text-right text-sm text-gray-700"
                                rowSpan={subRows.length}
                              >
                                {formatNumber(subRows[subRows.length - 1].fullYearTarget)}
                              </td>
                            ) : null}
                            <td className="whitespace-nowrap px-6 py-4 text-center text-sm text-gray-700">
                              {subRow.month}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-right text-sm text-gray-700">
                              {formatNumber(subRow.actual)}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-right text-sm text-gray-700">
                              {formatNumber(subRow.target)}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-right text-sm text-gray-700">
                              {formatNumber(subRow.ytdActual)}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-right text-sm text-gray-700">
                              {formatNumber(subRow.ytdTarget)}
                            </td>
                            <td
                              className={`whitespace-nowrap px-6 py-4 text-right text-sm text-gray-700 ${getTextColorClass(subRow.variance)}`}
                            >
                              {formatNumber(subRow.variance)}
                            </td>
                            <td
                              className={`whitespace-nowrap px-6 py-4 text-right text-sm text-gray-700 ${getTextColorClass(subRow.percentYtd)}`}
                            >
                              {formatPercent(subRow.percentYtd)}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-right text-sm text-gray-700">
                              {formatNumber(subRow.funnelRealistic)}
                            </td>
                            <td
                              className={`whitespace-nowrap px-6 py-4 text-right text-sm text-gray-700 ${getTextColorClass(subRow.surplusGap)}`}
                            >
                              {formatNumber(subRow.surplusGap)}
                            </td>
                          </tr>
                        ));
                      });

                      return [...mainRows, ...subproductRows];
                    }

                    return mainRows;
                  });
                })()}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Document Viewer Dialog */}
      <Dialog
        open={query?.viewDocumentDialogOpen === 'true'}
        fullScreen
        onClose={() => setParam({ viewDocumentDialogOpen: 'false' })}
      >
        <DocViewer
          documents={[
            {
              uri: documentUrl,
            },
          ]}
          config={{
            header: {
              disableHeader: true,
              disableFileName: true,
              retainURLParams: false,
            },
            pdfVerticalScrollByDefault: true,
          }}
          pluginRenderers={DocViewerRenderers}
        />
      </Dialog>
    </>
  );
}
