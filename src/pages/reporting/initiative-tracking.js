// Next, React, Tw
import React, { useState, useEffect } from 'react';

// Components
import Layout from '../../layouts/module/reporting';
import { Select } from '../../components/Shared/Form';
import ProjectCard from '../../components/reporting/ProjectCard';

// Others
import { getColorCode } from '../../utils/shared';
import { METRIC_ENDPOINT } from '../../utils/metric';
import existingInitiativesData from '../../data/initiatives-data-fixed.json';
import newInitiativesData from '../../data/new-initiatives.json';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  const [projectData, setProjectData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Filter state
  const [selectedInitiative, setSelectedInitiative] = useState('');

  // Get unique values for filter
  const initiatives = ['', ...new Set(projectData.map((project) => project.initiative))].sort();

  // Apply filters
  useEffect(() => {
    let filtered = [...projectData];

    if (selectedInitiative) {
      filtered = filtered.filter((project) => project.initiative === selectedInitiative);
    }

    setFilteredData(filtered);
  }, [projectData, selectedInitiative]);

  useEffect(() => {
    // For testing purposes, directly use the local data
    console.log('Using local initiatives data for testing');
    
    // Combine existing initiatives with new initiatives from spreadsheet
    const combinedInitiatives = [...existingInitiativesData, ...newInitiativesData];
    
    setProjectData(combinedInitiatives);
    setLoading(false);
    
    // Uncomment the below code when ready to use the API
    /*
    const fetchProjects = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${METRIC_ENDPOINT}/reporting/timeline`);
        const result = await response.json();

        if (result.success) {
          setProjectData(result.data.projects || []);
        } else {
          // If API fails, use our local initiatives data
          console.log('API call failed, using local initiatives data');
          const combinedInitiatives = [...existingInitiativesData, ...newInitiativesData];
          setProjectData(combinedInitiatives);
        }
      } catch (err) {
        console.error(`Error fetching projects: ${err.message}`);
        // On error, fall back to local initiatives data
        const combinedInitiatives = [...existingInitiativesData, ...newInitiativesData];
        setProjectData(combinedInitiatives);
      } finally {
        setLoading(false);
      }
    };
    
    fetchProjects();
    */
  }, []);

  // Function to determine background color based on date status
  // Currently not used but kept for potential future use
  // const getBackgroundColor = (date) => {
  //   if (!date || date === '-' || date === 'Date?') return '';
  //   return date === 'Date?' ? 'bg-yellow-50' : 'bg-green-50';
  // };

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-lg font-semibold">Loading project data...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-lg font-semibold text-red-500">{error}</div>
      </div>
    );
  }

  // Style definitions are now directly applied to elements
  // Common cell styling is applied in the component JSX

  return (
    <>
      <div
        className="bg-reporting px-4 py-2 text-white"
        style={{ backgroundColor: getColorCode('reporting') }}
      >
        <div className="flex flex-col gap-1">
          <p className="text-lg font-extrabold">INITIATIVE TRACKING</p>
          <p className="text-sm">Track and analyze project completion timelines.</p>
        </div>
      </div>

      <div className="container mx-auto p-4">
        {/* Filter Section */}
        <div className="mb-6 flex items-center space-x-2">
          <div className="w-72">
            <Select
              label="Filter by Initiative"
              value={selectedInitiative}
              onChange={(e) => setSelectedInitiative(e.target.value)}
            >
              <option value="">All Initiatives</option>
              {initiatives.map((initiative) => (
                <option key={initiative} value={initiative}>
                  {initiative}
                </option>
              ))}
            </Select>
          </div>
          {selectedInitiative && (
            <button
              type="button"
              onClick={() => setSelectedInitiative('')}
              className="mt-6 flex h-10 items-center rounded-md bg-gray-100 px-3 text-sm text-gray-600 hover:bg-gray-200"
            >
              Clear Filter
            </button>
          )}
        </div>

        {filteredData.map((project) => (
          <ProjectCard key={project.id} project={project} />
        ))}
      </div>
    </>
  );
}
