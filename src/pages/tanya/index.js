// Next, React, <PERSON>l<PERSON>
import { useEffect } from 'react';

// Layouts
import Layout from '../../layouts/module/tanya';

// Others
import { GAISHA_ENDPOINT } from '../../utils/gaisha';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Others

  const sendMessageToGaisha = (payload) => {
    const gaishaIframe = document.getElementById('gaisha');
    if (gaishaIframe?.contentWindow && GAISHA_ENDPOINT) {
      try {
        // Extract origin from the full URL
        const targetOrigin = new URL(GAISHA_ENDPOINT).origin;
        gaishaIframe.contentWindow.postMessage(payload, targetOrigin);
      } catch (error) {
        console.log('PostMessage error:', error);
        // Fallback to wildcard (less secure but functional)
        gaishaIframe.contentWindow.postMessage(payload, '*');
      }
    }
  };

  const handleGaishaLoading = (event) => {
    if (!event?.data?.gaishaLoaded) return;
    const localStorageKeys = ['accessToken', 'theme'];
    for (let i = 0; i < localStorageKeys.length; i += 1) {
      sendMessageToGaisha({
        key: localStorageKeys[i],
        value: localStorage?.getItem(localStorageKeys[i]),
      });
    }
  };

  const handleStorageChange = (event) => {
    if (event?.key && ['accessToken', 'theme']?.includes(event?.key) && event?.newValue)
      sendMessageToGaisha({
        key: event.key,
        value: event.newValue,
      });
  };

  useEffect(() => {
    window.addEventListener('message', handleGaishaLoading, false);
    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', () => {});
      window.removeEventListener('message', () => {});
    };
  }, []);

  return (
    <iframe
      id="gaisha"
      width="100%"
      height="100%"
      src={`${GAISHA_ENDPOINT}?showToolbar=false`}
      title="TANYA"
      className="absolute left-0 top-0 h-full w-full"
    />
  );
}
