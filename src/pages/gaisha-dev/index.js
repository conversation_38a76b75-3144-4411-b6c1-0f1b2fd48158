// Next, React, Tailwind
import { useRef, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSelector } from 'react-redux';

// Packages
import PropTypes from 'prop-types';

import Header from '../../layouts/Header';
import LoadingScreen from '../../components/Shared/loading-screen';

// Others
import { GAISHA_DEV_ENDPOINT } from '../../utils/gaisha';

// ----------------------------------------------------------------------

Page.propTypes = {
  children: PropTypes.node,
};

// ----------------------------------------------------------------------

export default function Page({ children }) {
  // Standard
  const { query } = useRouter();
  const { showGaisha } = query;
  const { isLoading } = useSelector((state) => state.loading);

  // Others
  const pageContainerRef = useRef();
  const hideHeaderAndSideMenu = () => pageContainerRef.current.requestFullscreen();

  const sendMessageToGaisha = (payload) => {
    const gaishaIframe = document.getElementById('gaisha');
    if (gaishaIframe?.contentWindow && GAISHA_DEV_ENDPOINT) {
      try {
        // Extract origin from the full URL
        const targetOrigin = new URL(GAISHA_DEV_ENDPOINT).origin;
        gaishaIframe.contentWindow.postMessage(payload, targetOrigin);
      } catch (error) {
        console.log('PostMessage error:', error);
        // Fallback to wildcard (less secure but functional)
        gaishaIframe.contentWindow.postMessage(payload, '*');
      }
    }
  };

  const handleGaishaLoading = (event) => {
    if (!event?.data?.gaishaLoaded) {
      return;
    }
    const localStorageKeys = ['accessToken', 'theme'];
    for (let i = 0; i < localStorageKeys.length; i += 1) {
      sendMessageToGaisha({
        key: localStorageKeys[i],
        value: localStorage?.getItem(localStorageKeys[i]),
      });
    }
  };

  const handleStorageChange = (event) => {
    if (event?.key && ['accessToken', 'theme']?.includes(event?.key) && event?.newValue) {
      sendMessageToGaisha({
        key: event.key,
        value: event.newValue,
      });
    }
  };

  useEffect(() => {
    window.addEventListener('message', handleGaishaLoading, false);
    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', () => {});
      window.removeEventListener('message', () => {});
    };
  }, [showGaisha]);

  return (
    <div className="flex h-full w-full flex-col">
      <div className="w-full">
        <Header hideHeaderAndSideMenu={hideHeaderAndSideMenu} />
      </div>
      <div
        ref={pageContainerRef}
        className="relative w-full flex-grow bg-[#f5f5f5] dark:bg-gray-800"
      >
        <div className="absolute left-0 top-0 h-full w-[100%] overflow-y-auto scrollbar scrollbar-none">
          {children}
        </div>
        <div className="relative h-full w-full bg-white dark:bg-gray-800">
          <iframe
            id="gaisha"
            width="100%"
            height="100%"
            src={`${GAISHA_DEV_ENDPOINT}?showToolbar=false`}
            title="TANYA-DEV"
            className="absolute left-0 top-0 h-full w-full"
          />
        </div>
      </div>
      {isLoading && <LoadingScreen />}
    </div>
  );
}
