// Next, React, Tw
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useState, useEffect, useRef } from 'react';
import { useTheme } from 'next-themes';
import { useDispatch, useSelector } from 'react-redux';
import { twMerge } from 'tailwind-merge';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions, Divider, Tooltip } from '@mui/material';
import { Info } from '@mui/icons-material';

// Packages
import * as R from 'ramda';
import moment from 'moment';
import encodeUrl from 'encodeurl';
import Joyride from 'react-joyride';

// Components
import Layout from '../../layouts/dashboard';
import GaishaButton from '../../components/dashboard/GaishaButton';
import ReactAnimatedNumber from '../../components/Shared/ReactAnimatedNumber';
import { SearchInput, BinarySwitchInput } from '../../components/Shared/CustomInput';
import AdminModal from '../../components/Shared/AdminModal';

// Others
import axios from '../../utils/axios';
import { useSnackbar } from '../../components/Shared/snackbar';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { AUM_ENDPOINT } from '../../utils/aum';
import {
  localStorageAvailable,
  REQUIRE_PERMISSION_MODULES,
  NO_PERMISSION_MODULES,
  SUPERADMIN_ONLY_MODULES,
} from '../../utils/shared';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { fetchUserModules } from '../../utils/store/aumReducer';
import { useParamContext } from '../../utils/auth/ParamProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { user, initialize } = useAuthContext();
  const { push, query } = useRouter();
  const { q, sortByCategory } = query;
  const { enqueueSnackbar } = useSnackbar();
  const { theme, setTheme } = useTheme();
  const storageAvailable = localStorageAvailable();
  const dispatch = useDispatch();
  const { setParam } = useParamContext();
  const canvasRef = useRef(null);
  const [isMobile, setIsMobile] = useState(false);

  const { userModules } = useSelector((state) => state.aum);

  const [dateData, setDateData] = useState([]);
  const [selectedModule, setSelectedModule] = useState(null);
  const [joyrideRun, setJoyrideRun] = useState(false);
  const [adminModalOpen, setAdminModalOpen] = useState(false);

  const steps = [
    {
      target: '.joyride-dashboard-step-1',
      content:
        "This is Single Interface Multiple Information, SIMI. It's a system that encompasses many small systems.",
      disableBeacon: true,
    },
    {
      target: '.joyride-dashboard-step-2',
      content: 'The colored cards are the modules you have access to.',
    },
    {
      target: '.joyride-dashboard-step-3',
      content: 'The greyed cards are the modules you do not have access to.',
    },
    {
      target: '.joyride-dashboard-step-4',
      content: 'Click on any greyed card to request access.',
    },
    {
      target: '.joyride-dashboard-step-5',
      content: 'View your profile here.',
    },
    {
      target: '.joyride-dashboard-step-6',
      content: 'Toggle dark mode here.',
    },
    {
      target: '.joyride-dashboard-step-7',
      content: "Access TANYA, TM Global's chatbot here.",
    },
    {
      target: '.joyride-dashboard-step-8',
      content: 'Download SIMI App here.',
    },
    {
      target: '.joyride-dashboard-step-9',
      content: 'Create / file new ticket for any support request here.',
    },
    {
      target: '.joyride-dashboard-step-10',
      content: 'Enjoy :-)',
    },
  ];

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleDialogClose = async (approved) => {
    if (!approved) {
      setDialogOpen(false);
      return;
    }
    dispatch(setIsLoading(true));
    try {
      const response = await axios.post(`${AUM_ENDPOINT}/user/v1/module/${user?.staff_id}`, {
        module: selectedModule,
        role: 'pending',
        tag: '',
      });

      if (response.data.status === 'success') {
        enqueueSnackbar(`${response.data.data}`);
        initialize();
        dispatch(fetchUserModules(user?.staff_id));
      }
    } catch (error) {
      enqueueSnackbar(error?.message);
    }
    dispatch(setIsLoading(false));
    setDialogOpen(false);
  };

  // Check if device is mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768); // Standard breakpoint for medium screens
    };
    
    // Initial check
    checkIfMobile();
    
    // Add event listener to check on resize
    window.addEventListener('resize', checkIfMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // Canvas Background Effect
  useEffect(() => {
    // Skip canvas animation if on mobile
    if (isMobile || !canvasRef.current) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    let animationFrameId;
    
    // Set canvas dimensions to match viewport
    const handleResize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    
    window.addEventListener('resize', handleResize);
    handleResize();
    
    // Particle system
    const particles = [];
    const particleCount = 50;
    
    // Define particle colors
    const particleColors = theme === 'dark' 
      ? ['rgba(0, 15, 100, 0.4)', 'rgba(255, 128, 0, 0.4)'] 
      : ['rgba(0, 30, 150, 0.3)', 'rgba(255, 100, 0, 0.3)'];
    
    class Particle {
      constructor() {
        this.x = Math.random() * canvas.width;
        this.y = Math.random() * canvas.height;
        this.size = Math.random() * 3 + 1;
        this.speedX = Math.random() * 2 - 1;
        this.speedY = Math.random() * 2 - 1;
        this.color = particleColors[Math.floor(Math.random() * particleColors.length)];
      }
      
      update() {
        this.x += this.speedX;
        this.y += this.speedY;
        
        if (this.x > canvas.width || this.x < 0) {
          this.speedX = -this.speedX;
        }
        
        if (this.y > canvas.height || this.y < 0) {
          this.speedY = -this.speedY;
        }
      }
      
      draw() {
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
      }
    }
    
    const init = () => {
      for (let i = 0; i < particleCount; i++) {
        particles.push(new Particle());
      }
    };
    
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      for (let i = 0; i < particles.length; i++) {
        particles[i].update();
        particles[i].draw();
      }
      
      // Draw connecting lines between particles
      for (let a = 0; a < particles.length; a++) {
        for (let b = a; b < particles.length; b++) {
          const dx = particles[a].x - particles[b].x;
          const dy = particles[a].y - particles[b].y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < 100) {
            ctx.beginPath();
            // Line color based on particle colors
            ctx.strokeStyle = theme === 'dark' 
              ? 'rgba(0, 30, 120, 0.1)' 
              : 'rgba(255, 120, 0, 0.1)';
            ctx.lineWidth = 1;
            ctx.moveTo(particles[a].x, particles[a].y);
            ctx.lineTo(particles[b].x, particles[b].y);
            ctx.stroke();
          }
        }
      }
      
      animationFrameId = requestAnimationFrame(animate);
    };
    
    init();
    animate();
    
    return () => {
      window.removeEventListener('resize', handleResize);
      cancelAnimationFrame(animationFrameId);
    };
  }, [theme, isMobile]);

  // Others

  const EXCLUDED_MODULES = ['gaisha', 'payme-app'];
  const userModules_ = (() => {
    const temp = userModules?.map((o) => o.module);
    return temp?.filter((o) => !EXCLUDED_MODULES?.includes(o));
  })();

  const getModuleCards = (shownInDashboard) => {
    let sortedModules;
    if (shownInDashboard)
      sortedModules = userModules_?.map(
        (o) => REQUIRE_PERMISSION_MODULES?.find((m) => m.module === o) || {}
      );
    else
      sortedModules = REQUIRE_PERMISSION_MODULES?.filter((o) => !userModules_?.includes(o?.module));

    sortedModules = [
      ...sortedModules,
      ...(shownInDashboard ? NO_PERMISSION_MODULES : []),
      ...(shownInDashboard && user?.isSuperAdmin ? SUPERADMIN_ONLY_MODULES : []),
    ];

    // Filter out empty objects or undefined modules
    sortedModules = sortedModules.filter(module => module && module.module);

    if (![undefined, '']?.includes(q))
      sortedModules = sortedModules.filter((o) =>
        JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase())
      );

    const getCard = (temp, i) => {
      // Special Notion-like card for System Administration
      if (temp?.module === 'admin' && temp?.notionStyle) {
        return (
          <div
            key={i}
            className={twMerge(
              'group col-span-1',
              shownInDashboard
                ? 'joyride-dashboard-step-2'
                : 'joyride-dashboard-step-3 joyride-dashboard-step-4',
              isMobile ? 'col-span-2' : 'md:col-span-2' // Make admin card wider
            )}
          >
            {isMobile ? (
              // Mobile Notion-style layout
              <div className="flex flex-col">
                <button
                  type="button"
                  className="relative w-full cursor-pointer rounded-[12px] bg-gradient-to-br from-slate-50 to-white border border-slate-200/60 shadow-sm hover:shadow-md transition-all duration-300 ease-out hover:transform hover:scale-[1.02] dark:from-slate-800/50 dark:to-slate-700/50 dark:border-slate-600/50 dark:shadow-slate-900/20 p-4"
                  onClick={() => {
                    if (shownInDashboard && user?.isSuperAdmin) {
                      setAdminModalOpen(true);
                    }
                  }}
                >
                  <div className="flex items-center gap-4">
                    {/* Icon */}
                    <div className="flex-shrink-0 w-12 h-12 rounded-[8px] bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-lg">
                      <svg
                        className="w-6 h-6 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                      </svg>
                    </div>
                    
                    {/* Content */}
                    <div className="flex-1 text-left">
                      <h3 className="text-sm font-semibold text-slate-900 dark:text-slate-100 mb-1 leading-tight">
                        {temp?.title}
                      </h3>
                      <p className="text-xs text-slate-600 dark:text-slate-400 leading-relaxed">
                        {temp?.description}
                      </p>
                    </div>
                    
                    {/* Arrow indicator */}
                    <div className="flex-shrink-0 opacity-60 group-hover:opacity-100 transition-opacity">
                      <svg
                        className="w-4 h-4 text-slate-400 dark:text-slate-300"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </div>
                  </div>
                  
                  {/* Subtle hover overlay */}
                  <div className="absolute inset-0 rounded-[12px] bg-gradient-to-r from-indigo-500/0 via-purple-500/0 to-indigo-500/0 group-hover:from-indigo-500/5 group-hover:via-purple-500/5 group-hover:to-indigo-500/5 transition-all duration-300" />
                </button>
              </div>
            ) : (
              // Desktop Notion-style layout
              <button
                type="button"
                className="relative w-full cursor-pointer rounded-[12px] bg-gradient-to-br from-slate-50 to-white border border-slate-200/60 shadow-sm hover:shadow-lg transition-all duration-300 ease-out hover:transform hover:scale-[1.02] dark:from-slate-800/50 dark:to-slate-700/50 dark:border-slate-600/50 dark:shadow-slate-900/20 group"
                onClick={() => {
                  if (shownInDashboard && user?.isSuperAdmin) {
                    setAdminModalOpen(true);
                  }
                }}
              >
                <div className="p-6">
                  <div className="flex items-start gap-4">
                    {/* Icon */}
                    <div className="flex-shrink-0 w-14 h-14 rounded-[10px] bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                      <svg
                        className="w-7 h-7 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                      </svg>
                    </div>
                    
                    {/* Content */}
                    <div className="flex-1 text-left">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-base font-semibold text-slate-900 dark:text-slate-100 leading-tight">
                          {temp?.title}
                        </h3>
                        <span className="px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-700 dark:bg-indigo-900/30 dark:text-indigo-300">
                          Admin Only
                        </span>
                      </div>
                      <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed mb-3">
                        {temp?.description}
                      </p>
                      
                      {/* Feature pills */}
                      <div className="flex flex-wrap gap-2">
                        <span className="px-2 py-1 rounded-md text-xs bg-slate-100 text-slate-600 dark:bg-slate-700 dark:text-slate-300">
                          User Management
                        </span>
                        <span className="px-2 py-1 rounded-md text-xs bg-slate-100 text-slate-600 dark:bg-slate-700 dark:text-slate-300">
                          System Config
                        </span>
                        <span className="px-2 py-1 rounded-md text-xs bg-slate-100 text-slate-600 dark:bg-slate-700 dark:text-slate-300">
                          Module Control
                        </span>
                      </div>
                    </div>
                    
                    {/* Arrow indicator */}
                    <div className="flex-shrink-0 opacity-40 group-hover:opacity-70 group-hover:translate-x-1 transition-all duration-300">
                      <svg
                        className="w-5 h-5 text-slate-400 dark:text-slate-300"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
                
                {/* Subtle hover overlay with gradient */}
                <div className="absolute inset-0 rounded-[12px] bg-gradient-to-r from-indigo-500/0 via-purple-500/0 to-indigo-500/0 group-hover:from-indigo-500/5 group-hover:via-purple-500/5 group-hover:to-indigo-500/5 transition-all duration-300" />
                
                {/* Subtle border glow on hover */}
                <div className="absolute inset-0 rounded-[12px] ring-1 ring-transparent group-hover:ring-indigo-200/50 dark:group-hover:ring-indigo-500/20 transition-all duration-300" />
              </button>
            )}
          </div>
        );
      }

      // Regular card design for other modules
      return (
        <div
          key={i}
          className={twMerge(
            'group col-span-1',
            shownInDashboard
              ? 'joyride-dashboard-step-2'
              : 'joyride-dashboard-step-3 joyride-dashboard-step-4'
          )}
        >
          {isMobile ? (
            // Mobile layout - title outside card
            <div className="flex flex-col items-center">
              <button
                type="button"
                className={`text-primary hover:bg-primary relative w-16 h-16 cursor-pointer rounded-[8px] bg-white shadow-lg transition-all ease-in-out hover:text-white dark:bg-gray-700 dark:text-white dark:shadow-dashboardCardDark hover:transform hover:scale-105 ${
                  !shownInDashboard ? 'grayscale' : ''
                }`}
                onClick={() => {
                  if (shownInDashboard) {
                    // Special handling for admin module - open modal instead of redirecting
                    if (temp?.module === 'admin' && user?.isSuperAdmin) {
                      setAdminModalOpen(true);
                      return;
                    }
                    push(`/${temp?.module}`);
                    return;
                  }
                  setSelectedModule(temp?.module);
                  setDialogOpen(true);
                }}
              >
                <div className="flex h-full w-full items-center justify-center">
                  <Image
                    src={`/assets/moduleLogo/${temp?.module}.webp`}
                    alt={temp?.title}
                    width={48}
                    height={48}
                    className="h-auto w-9/12"
                    loading="eager"
                    priority
                  />
                </div>
                {/* Card shine effect overlay */}
                <div className="absolute inset-0 rounded-[8px] bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-20 transition-opacity dark:via-gray-500" />
              </button>
              <p className="mt-2 text-xs font-medium text-center dark:text-white line-clamp-2 px-1">
                {temp?.title}
              </p>
            </div>
          ) : (
            // Desktop card layout - remains the same
            <button
              type="button"
              className={`text-primary hover:bg-primary relative w-full cursor-pointer rounded-[8px] bg-white shadow-lg transition-all ease-in-out hover:text-white dark:bg-gray-700 dark:text-white dark:shadow-dashboardCardDark hover:transform hover:scale-105 ${
                !shownInDashboard ? 'grayscale' : ''
              }`}
              onClick={() => {
                if (shownInDashboard) {
                  // Special handling for admin module - open modal instead of redirecting
                  if (temp?.module === 'admin' && user?.isSuperAdmin) {
                    setAdminModalOpen(true);
                    return;
                  }
                  push(`/${temp?.module}`);
                  return;
                }
                setSelectedModule(temp?.module);
                setDialogOpen(true);
              }}
            >
              <div className="rounded-[8px] overflow-hidden border border-gray-100 dark:border-gray-600">
                <div className="flex items-center gap-1">
                  <div className="h-[4rem] w-[20%] rounded-l-[8px] overflow-hidden flex items-center justify-center bg-white">
                    <Image
                      src={`/assets/moduleLogo/${temp?.module}.webp`}
                      alt="Card Image"
                      width={1004}
                      height={1004}
                      className="h-auto w-full"
                      loading="eager"
                      priority
                    />
                  </div>
                  <div className="flex w-3/4 flex-col gap-1 overflow-hidden p-2 md:w-full">
                    <p className="text-sm font-bold dark:text-white">{temp?.title}</p>
                    <p className="overflow-hidden overflow-ellipsis whitespace-nowrap text-xs transition-all duration-300 ease-out group-hover:overflow-visible group-hover:whitespace-normal dark:text-gray-300">
                      {temp?.description}
                    </p>
                  </div>
                </div>
              </div>
              {/* Card shine effect overlay */}
              <div className="absolute inset-0 rounded-[8px] bg-gradient-to-r from-transparent via-white to-transparent opacity-0 group-hover:opacity-20 transition-opacity dark:via-gray-500" />
            </button>
          )}
        </div>
      );
    };

    if (sortedModules?.length === 0)
      return <div className="w-full text-center text-sm dark:text-white">No module to access.</div>;

    const categories = R.uniq(R.pluck('category', sortedModules));

    return (
      <div className={`grid gap-4 ${isMobile ? 'grid-cols-4' : 'grid-cols-1 md:grid-cols-4'}`}>
        {sortByCategory !== 'true' ? (
          sortedModules?.map((module, i) => getCard(module, i))
        ) : (
          <>
            {categories?.map((category) => (
              <>
                <p
                  className={twMerge(
                    'text-primary pt-4 text-sm font-bold dark:text-white',
                    isMobile ? 'col-span-4' : 'col-span-1 md:col-span-4',
                    !shownInDashboard && 'grayscale'
                  )}
                >
                  {category}
                </p>
                {sortedModules
                  ?.filter((o) => o.category === category)
                  ?.map((module, i) => getCard(module, i))}
              </>
            ))}
          </>
        )}
      </div>
    );
  };

  const fetchDate = async () => {
    try {
      const response = await axios.get(`${AUM_ENDPOINT}/date/v1/all/keyword`);
      if (response?.data?.data) {
        const currentDate = moment();
        const data = response?.data?.data?.map((o) => ({
          ...o,
          momentDate: moment.unix(o?.date),
        }));
        const nextDate = data
          .filter((o) => o.momentDate.isAfter(currentDate))
          .sort((a, b) => a.momentDate - b.momentDate)[0];
        const filteredData = nextDate ? [nextDate] : [];
        setDateData(filteredData);
      }
    } catch {
      /* empty */
    }
  };

  useEffect(() => {
    if (!user) return;
    dispatch(fetchUserModules(user?.staff_id));
    fetchDate();
  }, [user]);

  useEffect(() => {
    if (!user) return;
    if (
      storageAvailable &&
      localStorage.getItem('isFirstTimeLogin') &&
      localStorage.getItem('isFirstTimeLogin') === 'false'
    )
      return;
    localStorage.setItem('isFirstTimeLogin', 'false');
    setTheme('light');
    setJoyrideRun(true);
  }, [user]);

  return (
    <>
      <Joyride
        steps={steps}
        run={joyrideRun}
        continuous
        showProgress
        showSkipButton
        callback={({ status }) => {
          if (['finished', 'skipped']?.includes(status)) setJoyrideRun(false);
        }}
        styles={{
          options: {
            primaryColor: '#180092',
          },
          tooltip: {
            fontSize: '14px',
          },
          buttonNext: {
            fontSize: '11px',
          },
          buttonBack: {
            fontSize: '11px',
          },
          buttonSkip: {
            fontSize: '11px',
          },
        }}
      />
      {!isMobile && (
        <canvas 
          ref={canvasRef} 
          className="fixed inset-0 pointer-events-none z-0"
        />
      )}
      <div className="joyride-dashboard-step-1 joyride-dashboard-step-10 container relative flex h-full flex-col justify-between gap-8 p-4 md:px-16 md:py-8 z-10">
        {!isMobile && (
          <button
            type="button"
            className="absolute right-1 top-1 text-xs text-blue-500"
            onClick={() => setJoyrideRun(true)}
          >
            <Tooltip title="Guide">
              <Info />
            </Tooltip>
          </button>
        )}
        <div className="w-full">
          <div className="rounded-lg bg-white p-4 text-black shadow-sm dark:bg-gray-700 dark:text-white dark:border dark:border-gray-600 w-full relative overflow-hidden">
            {!isMobile && (
              <Image 
                src="/assets/logo/abstract.jpg"
                alt="Abstract background"
                fill
                className="absolute inset-0 object-cover opacity-20 dark:opacity-20"
                priority
              />
            )}
            <div className="flex items-center justify-between w-full relative z-10">
              <div className="text-primary dark:text-white">
                <h2 className="text-lg font-bold">Hi, {user?.name || user?.staff_id || 'User'}</h2>
                <p className="text-sm opacity-80">Welcome to SIMI Dashboard</p>
              </div>
              {!isMobile && (
                <div className="flex flex-col items-center justify-center md:flex-row">
                  <Link
                    href={`https://outlook.office.com/calendar/action/compose?allday=true&path=%2Fcalendar%2Faction%2Fcompose&rru=addevent&startdt=${encodeUrl(
                      moment.unix(dateData[0]?.date).format('YYYY-MM-DD')
                    )}&subject=${encodeUrl(dateData[0]?.occasion?.toUpperCase())}`}
                    target="_blank"
                    className="text-primary group flex items-center gap-4 rounded-lg dark:text-white"
                  >
                    <div className="flex flex-col">
                      <p className="text-xs font-bold ">{dateData[0]?.occasion?.toUpperCase()}</p>
                      <p className="text-xs font-bold ">
                        {moment.unix(dateData[0]?.date)?.format('DD MMM, YYYY')}
                      </p>
                    </div>
                    <div className="bg-secondary group-hover:bg-primary flex flex-col rounded-full p-4 text-center text-2xl font-bold text-white shadow-dashboardCard dark:shadow-dashboardCardDark">
                      <ReactAnimatedNumber
                        value={moment.unix(dateData[0]?.date).diff(moment(), 'days') + 1}
                        formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                      />
                      <p className="text-xs gap-8">Days</p>
                    </div>
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex flex-grow flex-col justify-center relative z-10 w-full -mt-4">
          <div className="rounded-lg bg-white p-4 text-black shadow-sm dark:bg-gray-700 dark:text-white dark:border dark:border-gray-600 w-full">
            <div className="mb-4">
              <SearchInput 
                onSortClick={() => setParam({ sortByCategory: sortByCategory === 'true' ? undefined : 'true' })}
                sortActive={sortByCategory === 'true'}
              />
            </div>
            <div className="joyride-dashboard-step-2 relative">
              {getModuleCards(true)}
            </div>
          </div>
          
          <Divider>
            <p className="text-xs dark:text-gray-300">No Access Modules</p>
          </Divider>
          
          <div className="rounded-lg bg-white p-4 text-black shadow-sm dark:bg-gray-700 dark:text-white dark:border dark:border-gray-600 w-full">
            {getModuleCards(false)}
          </div>
        </div>

        <div className="w-full">
          <div className="rounded-lg bg-white p-4 text-black shadow-sm dark:bg-gray-700 dark:text-white dark:border dark:border-gray-600 w-full">
            <div className="flex flex-col-reverse items-center gap-6 md:flex-row md:justify-between">
              {/* Mobile footer */}
              {isMobile ? (
                <div className="flex w-full flex-col items-center">
                  <div className="flex w-full justify-center items-center mb-2 relative">
                    <div className="absolute left-0">
                      <button
                        type="button"
                        className="flex items-center gap-2"
                        onClick={() =>
                          window && window.open(`${process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT}/simi-apps/#`)
                        }
                      >
                        <Image src="/dashboard/app-store.webp" alt="Logo" width={20} height={20} />
                        <Image src="/dashboard/play-store.webp" alt="Logo" width={20} height={20} />
                      </button>
                    </div>
                    
                    <Link href="https://tmglobal.com.my/" target="_blank">
                      <Image
                        src={
                          theme === 'light'
                            ? '/assets/logo/TM Global Logo_Full Color 110px.svg'
                            : '/assets/logo/TM Global Logo_Rev White 110px.svg'
                        }
                        alt="Logo"
                        width={40}
                        height={40}
                        style={{ width: "auto", height: "auto" }}
                      />
                    </Link>
                  </div>
                  <p className="text-primary font-sans text-xs font-semibold dark:text-white mb-2">
                    Powered by TM Global
                  </p>
                </div>
              ) : (
                /* Desktop footer */
                <>
                  <button
                    type="button"
                    className="joyride-dashboard-step-8 flex items-center gap-2"
                    onClick={() =>
                      window && window.open(`${process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT}/simi-apps/#`)
                    }
                  >
                    <Image src="/dashboard/app-store.webp" alt="Logo" width={25} height={25} />
                    <Image src="/dashboard/play-store.webp" alt="Logo" width={25} height={25} />
                  </button>
                  <div className="flex items-center gap-2">
                    <p className="text-primary font-sans text-xs font-semibold dark:text-white">
                      Powered by
                    </p>
                    <Link href="https://tmglobal.com.my/" target="_blank">
                      <Image
                        src={
                          theme === 'light'
                            ? '/assets/logo/TM Global Logo_Full Color 110px.svg'
                            : '/assets/logo/TM Global Logo_Rev White 110px.svg'
                        }
                        alt="Logo"
                        width={30}
                        height={30}
                        className="-mt-1"
                        style={{ width: "auto", height: "auto", maxHeight: "20px", maxWidth: "80px" }}
                      />
                    </Link>
                  </div>
                </>
              )}
              
              {/* Gaisha button appears on both mobile and desktop */}
              <div className={`${isMobile ? 'mt-2' : ''} fixed bottom-4 right-4 mr-4 z-50`}>
                <GaishaButton disabled={!userModules?.some((module) => module.module === 'gaisha')} />
              </div>
            </div>
          </div>
        </div>
      </div>

      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-primary text-white">{`You are not authorized to access the ${selectedModule?.toUpperCase()} module.`}</DialogTitle>

        <DialogContent>
          <div className="mt-4 flex w-full flex-col items-center gap-4">
            Would you like to request for access?
          </div>
        </DialogContent>

        <DialogActions className="flex w-full justify-between">
          <button
            type="button"
            className="cta-btn text-black"
            onClick={() => handleDialogClose(false)}
          >
            No
          </button>
          <button
            type="button"
            className="bg-secondary cta-btn"
            onClick={() => handleDialogClose(true)}
          >
            Yes
          </button>
        </DialogActions>
      </Dialog>

      {/* Admin Modal */}
      <AdminModal 
        open={adminModalOpen} 
        onClose={() => setAdminModalOpen(false)} 
      />
    </>
  );
}
