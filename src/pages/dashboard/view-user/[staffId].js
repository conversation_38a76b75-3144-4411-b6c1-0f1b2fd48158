// Next, React and Tw
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';

// Mui
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Switch,
  Divider,
  Tooltip,
} from '@mui/material';
import { Edit, Info } from '@mui/icons-material';

// Packages
import * as R from 'ramda';

// Components
import Layout from '../../../layouts/dashboard';
import ProfilePicture from '../../../components/Shared/User/ProfilePicture';
import ProfileDetails from '../../../components/Shared/User/ProfileDetails';
import { TextInput } from '../../../components/Shared/CustomInput';

// Others
import axios from '../../../utils/axios';
import { useAuthContext } from '../../../utils/auth/useAuthContext';
import { useSnackbar } from '../../../components/Shared/snackbar';
import { useParamContext } from '../../../utils/auth/ParamProvider';
import { AUM_ENDPOINT } from '../../../utils/aum';
import { setIsLoading } from '../../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { query, push } = useRouter();
  const { user } = useAuthContext();
  const { enqueueSnackbar } = useSnackbar();
  const { dialogOpen, staffId } = query;
  const { setParam } = useParamContext();
  const dispatch = useDispatch();

  const [userId, setUserId] = useState(null);
  const [userObject, setUserObject] = useState({});
  const [recognizedEmailDomains, setRecognizedEmailDomains] = useState([]);

  // Table
  const [tableData, setTableData] = useState([]);
  const getBodyCellStyle = () => 'text-center whitespace-nowrap py-1 text-sm';

  // Dialog
  const [editModeDialog, setEditModeDialog] = useState(false);
  const DIALOG_DATA = {
    profile_picture: null,
    nick_name: null,
    alt_email: null,
    alt_phone: null,
    weight: null,
    height: null,
    use_alt_email: false,
    use_alt_phone: false,
  };
  const [dialogData, setDialogData] = useState(DIALOG_DATA);
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }
    setEditModeDialog(editMode);
    setParam({ dialogOpen: 'true' });
  };
  const handleDialogClose = async (action) => {
    if (action) {
      try {
        if (dialogData?.alt_email !== '') {
          const domain =
            dialogData?.alt_email.split('@')[dialogData.alt_email.split('@').length - 1];
          if (!recognizedEmailDomains.includes(domain?.toLowerCase())) {
            enqueueSnackbar('Please use a valid TM recognized E-mail.', {
              variant: 'error',
              anchorOrigin: {
                vertical: 'top',
                horizontal: 'center',
              },
            });
            return;
          }
        }
        dispatch(setIsLoading(true));
        const response = await axios.put(`${AUM_ENDPOINT}/profile/v1/${userId}`, dialogData);
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
      } catch (error) {
        // console.log(error);
      }
    }
    dispatch(setIsLoading(true));
    setParam({ dialogOpen: 'false' });
    fetchData();
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${AUM_ENDPOINT}/user/v1/${staffId}`);
      setUserObject(response.data.data[response.data.data.length - 1]);
      const response2 = await axios.get(`${AUM_ENDPOINT}/user/v1/module/all/${staffId}`);
      setUserId(response2.data.data[0]?.user_id);
      let temp = response2.data.data[0]?.modules;
      temp = temp.filter((o) => o?.module !== '');
      setTableData(temp);
    } catch (error) {
      // console.error('Error fetching data:', error);
    }
    try {
      const response = await axios.get(`${AUM_ENDPOINT}/domain/v1/all`);
      if (response.data.status === 'success' && response.data.data !== null) {
        setRecognizedEmailDomains(R.pluck('domain_name', response.data.data));
      }
    } catch (error) {
      // console.error('Error fetching data:', error);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (user?.isSuperAdmin) return;

    if (staffId !== user?.staff_id) push('/dashboard');
  }, []);

  useEffect(() => {
    fetchData();
  }, [staffId]);

  useEffect(() => {
    if (Object?.keys(userObject)?.length > 0) setDialogData(userObject);
  }, [Object?.keys(userObject)?.length]);

  return (
    <>
      <div className="container mx-auto flex min-h-full flex-col justify-center p-2 md:p-16">
        <div
          style={{ boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)' }}
          className="flex flex-col gap-4 rounded-2xl bg-white p-4"
        >
          <div className="flex flex-col gap-8 p-4 md:p-8">
            <div className="flex w-full flex-col gap-4 md:flex-row md:gap-0">
              <div className="flex h-auto w-full flex-col items-center justify-center gap-2 md:w-1/2">
                <ProfilePicture userObject={userObject} userId={userId} fetchData={fetchData} />
                {(staffId === user?.staff_id || user?.isSuperAdmin) && (
                  <button type="button" onClick={() => handleClickOpenDialog(true, userObject)}>
                    <div className="hover:text-primary flex items-center gap-1">
                      <p className=" hover:underline">Edit Profile</p>
                      <Edit className="-mt-2 h-[15px] md:h-[20px]" />
                    </div>
                  </button>
                )}
              </div>
              <div className="flex w-full flex-col gap-3 md:w-1/2">
                <ProfileDetails userObject={userObject} />
              </div>
            </div>

            {tableData?.length > 0 && (
              <div className="mt-4 overflow-x-auto scrollbar">
                <table className="min-w-full">
                  <thead>
                    <tr>
                      {['No', 'Module', 'Role'].map((label, index) => (
                        <td key={index} className="bg-primary text-center font-bold text-white">
                          {label}
                        </td>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {tableData.map((row, i) => (
                      <tr key={i} className="border-b border-gray-400">
                        <td className={getBodyCellStyle()}>{i + 1}</td>
                        <td className={getBodyCellStyle()}>{row?.module?.toUpperCase()}</td>
                        <td className={getBodyCellStyle()}>
                          {row.role.charAt(0).toUpperCase() + row.role.slice(1)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>

      <Dialog open={dialogOpen === 'true'} onClose={() => handleDialogClose(false)}>
        <DialogTitle sx={{ backgroundColor: '#5e7ce0', color: 'white' }}>Edit Profile</DialogTitle>
        <DialogContent className="scrollbar-none">
          <div className="flex w-full flex-col gap-4 px-2 py-8 md:w-[400px]">
            <div className="flex items-center gap-2">
              <TextInput
                name="nick_name"
                value={dialogData?.nick_name}
                placeholder="Nickname"
                onChange={handleDialogDataChange}
              />
              <Tooltip title="Digital card will prioritize showing your nickname over the first word in your full name.">
                <Info className="cursor-pointer" />
              </Tooltip>
            </div>
            <TextInput
              name="alt_designation"
              value={dialogData?.alt_designation}
              placeholder="Alternate Designation"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="alt_company"
              value={dialogData?.alt_company}
              placeholder="Alternate Company"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="alt_email"
              value={dialogData?.alt_email}
              placeholder="Alternate E-mail"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="alt_phone"
              value={dialogData?.alt_phone}
              placeholder="Alternate Phone No."
              onChange={handleDialogDataChange}
            />
            <Divider className="my-4" />
            <div className="flex items-center justify-between">
              <p className="text-xs">Use Alt Designation :</p>
              <div className="flex items-center gap-2">
                <Switch
                  name="use_alt_designation"
                  onChange={(event) => {
                    if (dialogData?.alt_designation === '') {
                      enqueueSnackbar(
                        'Please put any alternate designation to set alternate designation as default.',
                        {
                          variant: 'error',
                          anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'center',
                          },
                        }
                      );
                      return;
                    }
                    const { name, checked } = event.target;
                    setDialogData((prevValues) => ({
                      ...prevValues,
                      [name]: checked,
                    }));
                  }}
                  checked={dialogData.use_alt_designation}
                />
                <Tooltip title="Ticking this will force digital card to show your alternate designation">
                  <Info className="cursor-pointer" />
                </Tooltip>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <p className="text-xs">Use Alt Company :</p>
              <div className="flex items-center gap-2">
                <Switch
                  name="use_alt_company"
                  onChange={(event) => {
                    if (dialogData?.alt_company === '') {
                      enqueueSnackbar(
                        'Please put any alternate company to set alternate company as default.',
                        {
                          variant: 'error',
                          anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'center',
                          },
                        }
                      );
                      return;
                    }
                    const { name, checked } = event.target;
                    setDialogData((prevValues) => ({
                      ...prevValues,
                      [name]: checked,
                    }));
                  }}
                  checked={dialogData.use_alt_company}
                />
                <Tooltip title="Ticking this will force digital card to show your alternate company">
                  <Info className="cursor-pointer" />
                </Tooltip>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <p className="text-xs">Use Alt Email :</p>
              <div className="flex items-center gap-2">
                <Switch
                  name="use_alt_email"
                  onChange={(event) => {
                    if (dialogData?.alt_email === '') {
                      enqueueSnackbar(
                        'Please put any alternate e-mail to set alternate e-mail as default.',
                        {
                          variant: 'error',
                          anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'center',
                          },
                        }
                      );
                      return;
                    }
                    const { name, checked } = event.target;
                    setDialogData((prevValues) => ({
                      ...prevValues,
                      [name]: checked,
                    }));
                  }}
                  checked={dialogData.use_alt_email}
                />
                <Tooltip title="Ticking this will force digital card to show your alternate e-mail">
                  <Info className="cursor-pointer" />
                </Tooltip>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <p className="text-xs">Use Alt Phone No. :</p>
              <div className="flex items-center gap-2">
                <Switch
                  name="use_alt_phone"
                  onChange={(event) => {
                    if (dialogData?.alt_phone === '') {
                      enqueueSnackbar(
                        'Please put any alternate phone no. to set alternate phone no. as default.',
                        {
                          variant: 'error',
                          anchorOrigin: {
                            vertical: 'top',
                            horizontal: 'center',
                          },
                        }
                      );
                      return;
                    }
                    const { name, checked } = event.target;
                    setDialogData((prevValues) => ({
                      ...prevValues,
                      [name]: checked,
                    }));
                  }}
                  checked={dialogData.use_alt_phone}
                />
                <Tooltip title="Ticking this will force digital card to show your alternate phone no.">
                  <Info className="cursor-pointer" />
                </Tooltip>
              </div>
            </div>
            {user?.isSuperAdmin && (
              <>
                <Divider className="my-4" />
                <p className="w-full text-center font-semibold">Superadmin View</p>
                <TextInput
                  name="division"
                  value={dialogData?.division}
                  placeholder="Division"
                  onChange={handleDialogDataChange}
                />
                <TextInput
                  name="cost_center"
                  value={dialogData?.cost_center}
                  placeholder="Cost Center"
                  onChange={handleDialogDataChange}
                />
                <TextInput
                  name="email"
                  value={dialogData?.email}
                  placeholder="E-mail"
                  onChange={handleDialogDataChange}
                />
                <TextInput
                  name="position"
                  value={dialogData?.position}
                  placeholder="Position"
                  onChange={handleDialogDataChange}
                />
                <TextInput
                  name="supervisor"
                  value={dialogData?.supervisor}
                  placeholder="Supervisor"
                  onChange={handleDialogDataChange}
                />
                <TextInput
                  name="unit"
                  value={dialogData?.unit}
                  placeholder="Unit"
                  onChange={handleDialogDataChange}
                />
              </>
            )}
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-end">
            <div className="flex gap-2">
              <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
                Cancel
              </button>
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-[#5e7ce0] p-2 text-white"
                >
                  Save
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-[#5e7ce0] p-2 text-white"
                >
                  Save
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
}
