// Next, React, Tw
import Image from 'next/image';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Mui
import { Notifications } from '@mui/icons-material';

// Packages
import moment from 'moment';

// Components
import Layout from '../../layouts/dashboard';

// Others
import { useAuthContext } from '../../utils/auth/useAuthContext';
import axios from '../../utils/axios';
import { AUM_ENDPOINT } from '../../utils/aum';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { user } = useAuthContext();
  const dispatch = useDispatch();

  const CURRENT_TIME = moment();
  const [notificationData, setNotificationData] = useState([]);

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${AUM_ENDPOINT}/notification/v1/${user?.staff_id}`);

      if (response.data.status === 'success' && response.data.data !== null) {
        setNotificationData(response.data.data);
      }
    } catch (error) {
      console.log(error);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="container mx-auto h-full p-1 md:p-4">
      <div className="flex h-full flex-col gap-4  p-4">
        <p className="flex items-center gap-2 text-2xl font-bold">
          Notifications <Notifications />
        </p>
        <div className="w-full flex-grow overflow-y-auto scrollbar scrollbar-none">
          <div className="mx-auto flex w-full flex-col gap-2 ">
            {notificationData.map((o, i) => (
              <div className="flex w-full items-center justify-between bg-white p-4">
                <div className="flex flex-col gap-2">
                  <p className="text-sm font-semibold">{o?.message}</p>
                  <p className="text-xs">
                    {CURRENT_TIME.diff(moment(o?.created_at), 'minutes')} mins ago
                  </p>
                </div>
                <Image src="/assets/logo/New-SIMI-Logo.svg" alt="Logo" width={40} height={40} />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
