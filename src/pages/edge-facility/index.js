// Next, React, Tw
import dynamic from 'next/dynamic';
import Image from 'next/image';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';

// Components
import Layout from '../../layouts/module/ef';
import MapTableComponent from '../../components/ef/MapTable';
import ReactAnimatedNumber from '../../components/Shared/ReactAnimatedNumber';

// Others
import axios from '../../utils/axios';
import { EF_ENDPOINT } from '../../utils/ef';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const router = useRouter();
  const dispatch = useDispatch();

  const OpenStreetMap = dynamic(() => import('../../components/ef/Map'), {
    ssr: false,
  });

  const [cardsData, setCardsData] = useState([]);

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${EF_ENDPOINT}/summary/dashboard`);
      setCardsData(response.data.data);
    } catch (error) {
      // console.log(error);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div className="h-32 w-full rounded-b-[0px] bg-[#536eef] px-4 py-8">
        <div className="flex flex-col gap-4">
          <p className="text-lg font-bold text-white">Dashboard</p>
        </div>
      </div>
      <div className="container -mt-16">
        <div className="mx-auto my-4 flex w-full flex-col justify-center gap-6 p-2">
          <div className="flex flex-col justify-between gap-4 md:flex-row">
            {[
              {
                title: 'Registered Sites',
                key: 'registered_site',
                icon: '/assets/icons/carbon_building.svg',
              },
              {
                title: 'Total Rack',
                key: 'total_available_rack',
                icon: '/assets/icons/clarity_rack-server-line.svg',
              },
              {
                title: 'Total Available Port',
                key: 'total_available_port',
                icon: '/assets/icons/fluent_usb-port-20-regular.svg',
              },
              {
                title: 'No of Customer',
                key: 'total_customer',
                icon: '/assets/icons/formkit_people.svg',
              },
            ].map((card, index) => (
              <button
                type="button"
                className={`flex w-full flex-col gap-2 rounded-[4px] bg-white p-2 shadow-md md:w-1/4 ${
                  card.title === 'Registered Sites'
                    ? 'cursor-pointer hover:bg-blue-100'
                    : 'cursor-default'
                }`}
                onClick={
                  card.title === 'Registered Sites'
                    ? () => router.push('/edge-facility/sites')
                    : null
                }
              >
                <div>
                  <p className="text-md font-bold">{card.title}</p>
                </div>
                <div className="flex w-full items-end justify-between gap-2 text-3xl font-bold">
                  <ReactAnimatedNumber
                    value={cardsData[`${card.key}`]}
                    formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                  />
                  <Image src={card.icon} alt="Card Image" width={60} height={60} />
                </div>
              </button>
            ))}
          </div>

          <div className="flex flex-col items-center gap-4 rounded-[4px] bg-white p-4 px-4 shadow-lg">
            <p className="text-xl font-bold">EDGE FACILITIES SITES</p>

            <OpenStreetMap />

            <div className="w-full overflow-x-auto scrollbar md:hidden">
              <MapTableComponent />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
