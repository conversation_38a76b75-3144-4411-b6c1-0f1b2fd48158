// Next, React, Tw
import Image from 'next/image';
import { useState, useEffect, useRef, useMemo } from 'react';
import { useRouter } from 'next/router';
import { twMerge } from 'tailwind-merge';
import { useDispatch } from 'react-redux';

// Components
import Layout from '../../../layouts/module/ef';
import SiteTableDetailsComponent from '../../../components/ef/siteTableDetails';
import RackTableDetailsComponent from '../../../components/ef/rackTableDetails';
import PatchPanelTableDetailsComponent from '../../../components/ef/patchPanelTableDetails';
import PortTableDetailsComponent from '../../../components/ef/portTableDetails';
import MmrTableDetailsComponent from '../../../components/ef/MmrTableDetails';
import ReactAnimatedNumber from '../../../components/Shared/ReactAnimatedNumber';

// Others
import axios from '../../../utils/axios';
import { EF_ENDPOINT } from '../../../utils/ef';
import { useParamContext } from '../../../utils/auth/ParamProvider';
import { setIsLoading } from '../../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const router = useRouter();
  const hiddenDivRef = useRef(null);
  const { setParam } = useParamContext();
  const dispatch = useDispatch();

  const { component, siteId, rackId } = router.query;
  const [siteName, setSiteName] = useState(null);
  const [cards, setCards] = useState([]);
  const [rackData, setRackData] = useState([]);

  const colorMap = {
    active: 'bg-[#eb343d]',
    inactive: 'bg-[#0e9c3f]',
    reserved: 'bg-[#ff7a03]',
  };

  // Others
  const getComponentBreadCrumbs = () => {
    const getButton = (text, to) => (
      <button type="button" onClick={() => setParam({ component: to })}>
        <p className="text-lg font-semibold text-black hover:underline">{text}</p>
      </button>
    );

    switch (component) {
      case undefined:
        return <div className="flex gap-2">{getButton('Site Overview', 0)}</div>;
      case '0':
        return <div className="flex gap-2">{getButton('Site Overview', 0)}</div>;
      case '1':
        return (
          <div className="flex gap-2">
            {getButton('Site Overview', 0)}/{getButton('Rack', 1)}
          </div>
        );
      case '2':
        return (
          <div className="flex gap-2">
            {getButton('Site Overview', 0)}/{getButton('Rack', 1)}/{getButton('Patch Panel', 2)}
          </div>
        );
      case '3':
        return (
          <div className="flex gap-2">
            {getButton('Site Overview', 0)}/{getButton('Rack', 1)}/{getButton('Patch Panel', 2)}/
            {getButton('Port', 3)}
          </div>
        );
      case '11':
        return (
          <div className="flex gap-2">
            {getButton('Site Overview', 0)}/{getButton('MMR Patch Panel', 1)}
          </div>
        );
      case '12':
        return (
          <div className="flex gap-2">
            {getButton('Site Overview', 0)}/{getButton('MMR Patch Panel', 1)}/{getButton('Port', 3)}
          </div>
        );
      default:
        return '';
    }
  };

  const getCellRack = (cell, i) => (
    <button
      type="button"
      className={twMerge(
        `${
          i === 0 && 'border-l-4'
        } h-[50px] w-[70px] border-y-4 border-r-4 border-black text-white hover:brightness-150  ${
          rackId === cell?.id ? 'brightness-150' : ''
        }`,
        colorMap[cell?.rack_status]
      )}
      onClick={() => setParam({ rackId: cell?.id, patchPanelId: undefined, component: 1 })}
    >
      <div className="flex h-full w-full items-center justify-center whitespace-nowrap text-sm">
        {cell?.rack_name}
      </div>
    </button>
  );

  const memoizedComponent = useMemo(
    () => (
      <div className="container -mt-10 rounded-[4px]">
        <div className="flex flex-col justify-between gap-4 md:flex-row">
          {cards.map((card, index) => (
            // eslint-disable-next-line
            <div
              className={`flex w-full flex-col gap-2 rounded-[4px] bg-white p-2 shadow-md md:w-1/4 `}
            >
              <div>
                <p className="text-md font-bold">{card.title}</p>
              </div>
              <div className="flex items-end justify-between gap-2 text-3xl font-bold">
                <div className="flex gap-2">
                  <ReactAnimatedNumber
                    value={card?.data}
                    formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                  />
                  {(index === 2 || index === 3) && <p>kW</p>}
                </div>
                <Image src={card.icon} alt="Card Icon" width={60} height={60} />
              </div>
            </div>
          ))}
        </div>
      </div>
    ),
    [JSON?.stringify(cards)]
  );

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${EF_ENDPOINT}/site/id/${siteId}`);
      if (response.data.status === 'success') {
        setSiteName(response.data.data[0].site);
        setCards([
          {
            title: 'Available Rack',
            data: response.data.data[0].total_rack_available,
            icon: '/assets/icons/carbon_building.svg',
          },
          {
            title: 'Active Rack',
            data: response.data.data[0].total_rack_active,
            icon: '/assets/icons/clarity_rack-server-line.svg',
          },
          {
            title: 'Active Power',
            data: response.data.data[0].total_power_active / 1000,
            icon: '/assets/icons/subway_power.svg',
          },
          {
            title: 'Available Power',
            data: response.data.data[0].total_power_available / 1000,
            icon: '/assets/icons/formkit_people.svg',
          },
        ]);
      }
      const response2 = await axios.get(`${EF_ENDPOINT}/rack/site_id/${siteId}`);
      if (response2.data.status === 'success') {
        const temp = response2?.data?.data.filter((o) => o?.rack_name !== '-');
        setRackData(temp);
      }
    } catch (error) {
      // console.log(error);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
    hiddenDivRef.current?.scrollIntoView();
  }, []);

  return (
    <>
      <div ref={hiddenDivRef} />
      <div className="flex h-32 w-full justify-between bg-[#536eef] px-4 py-8 text-white">
        <div className="flex flex-col gap-4">
          <p className="text-lg font-bold">Floor Layout - {siteName}</p>
        </div>
      </div>
      {memoizedComponent}
      <div className="container my-4 w-[92%] rounded-[4px] bg-white shadow-lg">
        <div className="flex flex-col-reverse items-center justify-center gap-2 md:flex-row">
          <div className="w-full overflow-x-auto md:w-auto">
            <div className="relative h-[400px] w-[1000px]">
              <Image
                src="/ef/floor_layout.webp"
                alt="Floor Layout Image"
                width={1000}
                height={400}
                className="absolute left-0 top-0 z-0"
                onClick={() =>
                  setParam({ rackId: undefined, patchPanelId: undefined, component: 0 })
                }
              />
              <div className="absolute left-1/2 top-[100px] z-10 -translate-x-1/2">
                <div className="flex">
                  {rackData?.slice(0, 8).map((cell, i) => getCellRack(cell, i))}
                </div>
              </div>
              <div className="absolute left-1/2 top-[240px] z-10 -translate-x-1/2">
                <div className="flex">
                  {rackData?.slice(8).map((cell, i) => getCellRack(cell, i))}
                </div>
              </div>
              <div className="absolute left-[75px] top-[120px] z-10">
                <button
                  type="button"
                  className={`flex h-[180px] w-[90px] flex-col items-center gap-4 border-4 border-black p-4 hover:bg-[#f0a665] hover:text-white ${
                    component === '11' ? 'bg-[#ff7a03] text-white' : ''
                  }`}
                  onClick={() =>
                    setParam({ rackId: undefined, patchPanelId: undefined, component: 11 })
                  }
                >
                  <div className="flex h-full flex-col justify-center">
                    <p>MMR</p>
                  </div>
                </button>
              </div>
              <div className="absolute bottom-0 left-1/2 z-10 flex -translate-x-1/2 -translate-y-[40px] transform items-center gap-2 text-[13px] text-black">
                <div className={`h-[10px] w-[10px] ${colorMap?.active}`} />
                <p>Occupied</p>
                <div className={`h-[10px] w-[10px] ${colorMap?.inactive}`} />
                <p>Vacant</p>
                <div className={`h-[10px] w-[10px] ${colorMap?.reserved}`} />
                <p>Reserved</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container my-4 flex w-[92%] flex-col gap-4 rounded-[4px] bg-white p-4 shadow-lg">
        {getComponentBreadCrumbs()}
        {['0', '', undefined]?.includes(component) && <SiteTableDetailsComponent />}
        {component === '1' && <RackTableDetailsComponent />}
        {component === '2' && <PatchPanelTableDetailsComponent />}
        {component === '3' && <PortTableDetailsComponent />}
        {component === '11' && <MmrTableDetailsComponent />}
        {component === '12' && <PortTableDetailsComponent />}
      </div>
    </>
  );
}
