// Next, React, Tw
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { AddCircle, Edit } from '@mui/icons-material';
import { twMerge } from 'tailwind-merge';

// Components
import Layout from '../../../layouts/module/ef';
import { useSnackbar } from '../../../components/Shared/snackbar';
import ExportExcelButton from '../../../components/Shared/ExportExcelButton';
import { SearchInput } from '../../../components/Shared/CustomInput';

// Others
import axios from '../../../utils/axios';
import { EF_ENDPOINT } from '../../../utils/ef';
import { setIsLoading } from '../../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const { push, query } = useRouter();
  const { q } = query;
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();

  // Table
  const [tableData, setTableData] = useState([]);
  const [page] = useState(0);
  const [rowsPerPage] = useState(10);
  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  })();
  const bodyCellStyle = 'text-center py-1 px-2 text-sm cursor-pointer';

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const INITIAL_DIALOG_DATA = {
    state: null,
    site: null,
    code: null,
    address: null,
    rfs: null,
    network_room: null,
    connector_type: null,
    latitude: null,
    longitude: null,
    site_status: 'active',
  };

  const [dialogData, setDialogData] = useState(INITIAL_DIALOG_DATA);
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }

    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  const handleDialogClose = async (action) => {
    let response;
    if (action) {
      try {
        if (Object.values(dialogData).includes(null)) {
          enqueueSnackbar('Please fill in all field.', {
            variant: 'error',
          });
          return;
        }

        switch (action) {
          case 'post':
            response = await axios.post(`${EF_ENDPOINT}/site`, dialogData);
            break;
          case 'put':
            response = await axios.put(`${EF_ENDPOINT}/site/${dialogData.id}`, dialogData);
            break;
          case 'delete':
            response = await axios.delete(`${EF_ENDPOINT}/site/${dialogData.id}`);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch (error) {
        // console.log(error);
        const errorMessage =
          error.response?.data?.message || error.message || 'An unexpected error occurred';
        enqueueSnackbar(errorMessage, {
          variant: 'error',
        });
      }
    }

    if (response?.data?.status === 'success' && action === 'post') {
      const siteId = response?.data?.data?.split(' ')[2];
      for (let i = 1; i < 16 + 1; i += 1) {
        let rackSize;
        if (i <= 4) {
          rackSize = '800 mm x 1200 mm x 484 mm';
        } else if (i <= 8) {
          rackSize = '600 mm x 1200 mm x 484 mm';
        } else if (i <= 12) {
          rackSize = '800 mm x 1200 mm x 484 mm';
        } else if (i <= 16) {
          rackSize = '600 mm x 1200 mm x 484 mm';
        }

        try {
          // eslint-disable-next-line
          await axios.post(`${EF_ENDPOINT}/rack/${siteId}`, {
            customer_name: '',
            rack_name: `${dialogData?.code}-R${i.toString().padStart(2, '0')}`,
            rack_size: rackSize,
            rack_status: 'inactive',
            rack_type: 'customer',
            total_power: 0,
          });
        } catch (error) {
          // console.log(error?.meesage);
        }
      }
    }

    setDialogData(INITIAL_DIALOG_DATA);
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${EF_ENDPOINT}/site/all/keyword`);
      if (response?.data?.status === 'success') {
        const temp = response.data.data.sort((a, b) => a.site.localeCompare(b.site));
        setTableData(temp);
      }
    } catch (error) {
      // console.log(error);
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div className="flex h-36 w-full items-center justify-between rounded-b-[0px] bg-[#536eef] px-4 py-8 text-white">
        <div className="flex flex-col">
          <p className="text-xl font-bold">Sites</p>
          <p className="text-sm">List of Edge Facilities registered sites</p>
        </div>
      </div>

      <div className="container my-4 -mt-8 flex flex-col rounded-[4px] bg-white p-4 shadow-lg">
        <div className="flex w-full flex-col items-center justify-between gap-2 md:flex-row">
          <SearchInput />
          <div className="flex items-center gap-2">
            <button type="button" onClick={() => handleClickOpenDialog(false)}>
              <div className="cta-btn flex items-center gap-2 bg-green-500 ">
                <AddCircle className="h-4 w-4" />
                <p className="font-semibold">Add New Site</p>
              </div>
            </button>
            <ExportExcelButton data={tableData} filename="site-details.csv" />
          </div>
        </div>

        <div className="mt-4 overflow-x-auto scrollbar">
          <table className="min-w-full">
            <thead>
              <tr>
                {[
                  'No.',
                  'State',
                  'Edge Facilities Sites',
                  'Address',
                  'RFS',
                  'EF FDF In Network Room',
                  'Connector Type',
                  '',
                ].map((label, index) => (
                  <td
                    key={index}
                    className="bg-ef whitespace-nowrap px-4 text-center text-sm text-white"
                  >
                    {label}
                  </td>
                ))}
              </tr>
            </thead>
            <tbody>
              {filteredTableData
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((row, i) => (
                  <tr
                    key={`${i}-${row?.id}`}
                    className="cursor-pointer odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-200"
                    onClick={() => push(`/edge-facility/sites/${row?.id}`)}
                  >
                    <td className={twMerge(bodyCellStyle, 'text-xs')}>
                      {i + 1 + rowsPerPage * page}
                    </td>
                    <td className={twMerge(bodyCellStyle, 'text-left text-xs')}>{row?.state}</td>
                    <td className={twMerge(bodyCellStyle, 'w-[220px] text-left text-xs')}>
                      {row?.site}
                    </td>
                    <td className={twMerge(bodyCellStyle, 'text-left text-xs')}>{row?.address}</td>
                    <td className={twMerge(bodyCellStyle, 'text-xs')}>{row?.rfs}</td>
                    <td className={twMerge(bodyCellStyle, 'text-left text-xs')}>
                      {row?.network_room}
                    </td>
                    <td className={twMerge(bodyCellStyle, 'text-xs')}>{row?.connector_type}</td>
                    <td className=" text-center">
                      <button
                        type="button"
                        className="px-1"
                        onClick={(event) => {
                          event.stopPropagation();
                          handleClickOpenDialog(true, row);
                        }}
                      >
                        <Edit className="text-ef" />
                      </button>
                    </td>
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
      </div>

      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle sx={{ backgroundColor: '#536eef' }}>
          {!editModeDialog && <p className="text-white">Add New Site</p>}
          {editModeDialog && <p className="text-white">Edit Site</p>}
        </DialogTitle>
        <DialogContent>
          <div className="flex flex-col gap-4 p-2">
            <p>State :</p>
            <input
              name="state"
              value={dialogData?.state}
              onChange={handleDialogDataChange}
              className="w-[400px] rounded-lg border border-slate-300 p-2"
            />
            <p>Edge Facilities Sites :</p>
            <input
              name="site"
              value={dialogData?.site}
              onChange={handleDialogDataChange}
              className="w-[400px] rounded-lg border border-slate-300 p-2"
            />
            {!editModeDialog && (
              <>
                <p>Edge Facilities Code :</p>
                <input
                  name="code"
                  value={dialogData?.code}
                  onChange={handleDialogDataChange}
                  className="w-[400px] rounded-lg border border-slate-300 p-2"
                />
              </>
            )}

            <p>Address :</p>
            <textarea
              name="address"
              rows={3}
              value={dialogData?.address}
              onChange={handleDialogDataChange}
              className="w-[400px] rounded-lg border border-slate-300 p-2"
            />
            <p>RFS :</p>
            <input
              name="rfs"
              value={dialogData?.rfs}
              onChange={handleDialogDataChange}
              className="w-[400px] rounded-lg border border-slate-300 p-2"
            />
            <p>EF FDF in Network Room :</p>
            <input
              name="network_room"
              value={dialogData?.network_room}
              onChange={handleDialogDataChange}
              className="w-[400px] rounded-lg border border-slate-300 p-2"
            />
            <p>Connector Type :</p>
            <input
              name="connector_type"
              value={dialogData?.connector_type}
              onChange={handleDialogDataChange}
              className="w-[400px] rounded-lg border border-slate-300 p-2"
            />
            <p>Latitude :</p>
            <input
              name="latitude"
              value={dialogData?.latitude}
              onChange={handleDialogDataChange}
              className="w-[400px] rounded-lg border border-slate-300 p-2"
            />
            <p>Longitude :</p>
            <input
              name="longitude"
              value={dialogData?.longitude}
              onChange={handleDialogDataChange}
              className="w-[400px] rounded-lg border border-slate-300 p-2"
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between">
            <div>
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('delete')}
                  className="bg-[#ff6d6d] p-2 text-white"
                >
                  Delete
                </button>
              )}
            </div>
            <div className="flex gap-2">
              <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
                Close
              </button>

              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-[#efefef] p-2 text-black"
                >
                  Save
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-[#efefef] p-2 text-black"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
}
