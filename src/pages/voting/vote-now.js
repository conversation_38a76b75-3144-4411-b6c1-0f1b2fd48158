// Next, React, Tw
import Image from 'next/image';
import { useRouter } from 'next/router';
import { useEffect, useState, useRef } from 'react';
import { twMerge } from 'tailwind-merge';

// Mui
import { Radio, useMediaQuery, Divider } from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';

// Packages
import * as yup from 'yup';
import ProgressBar from '@ramonak/react-progress-bar';

// Components
import Layout from '../../layouts/module/voting';
import QRBox from '../../components/Shared/QRBox';

// Others
import axios from '../../utils/axios';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { useSnackbar } from '../../components/Shared/snackbar';
import { VOTING_ENDPOINT } from '../../utils/voting';
import { getColorCode } from '../../utils/shared';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { user } = useAuthContext();
  const isSmallScreen = useMediaQuery('(max-width: 768px)');
  const { enqueueSnackbar } = useSnackbar();
  const { query, asPath } = useRouter();
  const { votingid } = query;
  const module = asPath?.split('?')?.[0]?.split('/');
  const audioRef = useRef(null);
  const showResultsTimerInput = useRef(null);
  const intervalRef = useRef(null);

  const [userVoted, setUserVoted] = useState(false);
  const [votingData, setVotingData] = useState({});
  const [candidateList, setCandidateList] = useState([]);
  const [showResults, setShowResults] = useState(false);

  // Form
  const schema = yup.object({
    candidate_id: yup.string().required('Please select a candidate.'),
    voter_id: yup.string().required('Please provide voter id.'),
  });
  const [formData, setFormData] = useState({});

  const handleFormSubmit = async (action) => {
    if (action) {
      let payload;
      try {
        payload = { ...formData, voter_id: user?.staff_id };
        payload = await schema.validate(payload, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }
      try {
        let response;
        switch (action) {
          case 'put':
            response = await axios.put(`${VOTING_ENDPOINT}/voter/candidate`, payload);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch (error) {
        enqueueSnackbar(error?.errors, {
          variant: 'error',
        });
      }
    }
    setUserVoted(true);
    setFormData({});
    fetchCandidatesData();
  };

  // Others

  const votingIsOpen = votingData?.status === 'open';

  const filteredSeries = [...candidateList]?.sort((a, b) => b.total_vote - a.total_vote);
  const maxVotes = filteredSeries?.[0]?.total_vote || 10;

  const qRSource = `${process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT}${asPath}`;

  const changeVotingStatus = async (status) => {
    try {
      await axios.put(`${VOTING_ENDPOINT}/voting/${votingid}`, {
        ...votingData,
        status,
      });
    } catch {
      enqueueSnackbar('Failed', {
        variant: 'error',
      });
    }
  };

  const handleBlur = async (event) => {
    const value = Number(event.target.value);
    if (Number.isNaN(value) || value <= 0) return;

    await changeVotingStatus('open');
    await fetchVotingData();
    setShowResults(false);

    audioRef.current.currentTime = 0;
    audioRef.current.play();

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    let countdown = value;
    showResultsTimerInput.current.value = countdown;

    intervalRef.current = setInterval(async () => {
      countdown -= 1;
      showResultsTimerInput.current.value = countdown;

      if (countdown === 0) {
        await changeVotingStatus('closed');
        clearInterval(intervalRef.current);
        setShowResults(true);
        audioRef.current.pause();
      }
    }, 1000);
  };

  const resultsPart = (
    <div className="flex w-full items-center justify-end gap-2">
      <div className="relative flex-grow rounded-lg bg-white p-2">
        <div className="flex w-full flex-col gap-2 p-1">
          {filteredSeries?.map((o, i) => (
            <div key={`${i}-${o?.total_vote}`} className="flex w-full items-center gap-2">
              <img
                src={o?.candidate_picture}
                alt="Event"
                height={75}
                width={75}
                className="rounded-full"
              />
              <div className="flex-grow">
                <ProgressBar
                  height="45px"
                  bgColor={getColorCode(module)}
                  completed={String((o?.total_vote / maxVotes) * 100)}
                  customLabel={`${o?.candidate_name} - ${o?.total_vote}`}
                />
              </div>
            </div>
          ))}
        </div>

        {!showResults && (
          <div className="absolute left-0 top-0 h-full w-full rounded-lg bg-white opacity-[0.97]" />
        )}
      </div>
      <div className="flex flex-col items-center gap-2">
        <input
          ref={showResultsTimerInput}
          type="number"
          onBlur={handleBlur}
          className="w-[100px] rounded-md p-1 text-center text-4xl text-black focus:outline-none"
        />
        <button type="button" onClick={() => setShowResults((prev) => !prev)}>
          {!showResults && <Visibility className="text-white" />}
          {showResults && <VisibilityOff className="text-white" />}
        </button>
      </div>
    </div>
  );

  const fetchVotingData = async () => {
    try {
      const response = await axios.get(`${VOTING_ENDPOINT}/voting/id/${votingid}`);
      if (response.data.status === 'success' && response.data.data !== null) {
        setVotingData(response.data.data[0]);
      }
    } catch {
      setVotingData({});
    }
  };

  const fetchCandidatesData = async () => {
    try {
      const response = await axios.get(`${VOTING_ENDPOINT}/candidate/voting_id/${votingid}`);
      if (response?.data?.data) {
        response.data.data = response?.data?.data?.map((o) => {
          const temp = o;
          if (!o?.total_vote) {
            temp.total_vote = 0;
          }
          return temp;
        });
        setCandidateList(response?.data?.data);
      }
    } catch {
      setCandidateList([]);
    }
  };

  // eslint-disable-next-line
  useEffect(() => {
    fetchVotingData();
    fetchCandidatesData();
    if (!isSmallScreen) {
      const timer = setInterval(async () => {
        fetchCandidatesData();
      }, 1000);
      return () => {
        clearInterval(timer);
      };
    }
  }, [votingid]);

  useEffect(() => () => clearInterval(intervalRef.current), []);

  return (
    <div className="flex w-full flex-col bg-[url(/events/lrp-2024/bg.png)] bg-cover bg-center bg-no-repeat md:h-full ">
      {/* eslint-disable-next-line jsx-a11y/media-has-caption */}
      <audio
        ref={audioRef}
        controls
        onEnded={() => {
          audioRef.current.currentTime = 0;
          audioRef.current.play();
        }}
        className="hidden"
      >
        <source
          src="/voting/suspense-cartoon-sneaky-animation-music-248098.mp3"
          type="audio/mpeg"
        />
      </audio>
      <div className="container mx-auto flex h-full flex-col gap-2 px-2 py-4 text-white md:justify-end">
        <p className="text-center text-3xl font-bold text-white underline">
          {votingData?.voting_name?.toUpperCase()}
        </p>
        {votingIsOpen && !userVoted && (
          <div className="flex flex-col justify-end gap-8 md:h-1/2 md:pt-10">
            <div className="mx-auto flex w-full flex-col justify-between  gap-4  md:w-3/4 md:flex-row">
              {candidateList?.map((o) => (
                <button
                  key={o?.id}
                  type="button"
                  className={twMerge(
                    'flex flex-col items-center justify-end gap-2 rounded-md p-1',
                    formData?.candidate_id === o?.id && 'border-2 border-white'
                  )}
                  onClick={() =>
                    setFormData((prev) => ({
                      ...prev,
                      candidate_id: o?.id,
                    }))
                  }
                >
                  <img
                    src={o?.candidate_picture}
                    alt="Event"
                    className="h-[175px] w-[175px] rounded-md"
                  />
                  <p className="text-lg font-semibold">{o?.candidate_name}</p>
                  <Radio
                    checked={formData?.candidate_id === o?.id}
                    value={formData?.candidate_id === o?.id}
                    size="small"
                    sx={{
                      color: getColorCode(module),
                      '&.Mui-checked': {
                        color: getColorCode(module),
                      },
                    }}
                  />
                </button>
              ))}
            </div>
            <div className="flex w-full flex-grow justify-center">
              <button
                type="button"
                disabled={!formData?.candidate_id || userVoted}
                className="h-[40px] rounded-lg bg-[#00e3f6] px-4 py-2 text-[#3c50e0] disabled:bg-gray-500 disabled:text-white"
                onClick={() => handleFormSubmit('put')}
              >
                Vote!
              </button>
            </div>
          </div>
        )}
        {votingIsOpen && userVoted && (
          <div className="md:t-0 -mt-32 flex h-screen w-full items-center justify-center gap-2">
            <Image src="/voting/ballot.svg" width={175} height={175} />

            <p className="text-center text-xl font-bold">Voted, Thank You!</p>
          </div>
        )}
        {!votingIsOpen && (
          <div className="md:t-0 -mt-32 flex h-screen  w-full items-center justify-center gap-2">
            <Image src="/voting/ballot.svg" width={175} height={175} />

            <p className="text-center text-xl font-bold">Not yet!</p>
          </div>
        )}
        <div className="hidden h-1/2 items-center gap-2 border-t border-gray-500 md:flex">
          <QRBox
            value={qRSource}
            size={Number(`${!isSmallScreen ? 280 : 230}`)}
            showDownloadButton
          />
          <Divider sx={{ backgroundColor: 'white' }} orientation="vertical" />
          <div className="flex-grow">{resultsPart}</div>
        </div>
      </div>
    </div>
  );
}
