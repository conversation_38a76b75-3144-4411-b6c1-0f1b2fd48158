// Next, React, Tw
import Image from 'next/image';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

// Components
import Layout from '../../layouts/module/voting';

// Others
import axios from '../../utils/axios';
import { VOTING_ENDPOINT } from '../../utils/voting';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { push, query } = useRouter();
  const { eventId } = query;
  const dispatch = useDispatch();

  const [cardsData, setCardsData] = useState([]);

  // Others

  const getCardsdata = () => {
    if (!eventId) return cardsData;
    return cardsData?.filter((o) => o?.event_id === eventId);
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${VOTING_ENDPOINT}/voting/all/keyword`);
      if (response.data.data !== null) {
        response.data.data = response?.data?.data?.filter((o) => o?.status === 'open');
        setCardsData(response?.data?.data);
      }
    } catch (error) {
      // console.error('Error fetching data:', error);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="flex min-h-screen w-full flex-col bg-[url(/events/lrp-2024/bg.png)] bg-cover bg-fixed bg-center bg-no-repeat p-8">
      {cardsData?.length === 0 && (
        <div className="container mx-auto flex min-h-[80vh] w-full items-center justify-center text-white">
          <p className="text-xl">There is no open voting. Chill...</p>
        </div>
      )}
      {cardsData?.length > 0 && (
        <div className="container mx-auto grid grid-cols-1 gap-4 p-8  md:grid-cols-4">
          {getCardsdata()?.map((o, i) => (
            <button
              key={`${i}-${o?.id}`}
              type="button"
              className="col-span-1 flex flex-col gap-2 rounded-lg bg-white p-4"
              onClick={() =>
                push(
                  `/voting/${o.candidate_source === 'manual' ? 'vote-now' : 'vote-now-all'}?votingid=${o?.id}`
                )
              }
            >
              <p className="w-full bg-white text-center text-lg font-semibold">{o?.voting_name}</p>
              <div className="flex gap-2 bg-[#fefefe]">
                <Image src="/voting/ballot.svg" width={80} height={80} />
                <div className="flex flex-col justify-center gap-2">
                  <p className="text-[#848ca0]">No. of Candidates</p>
                  <p className="text-center text-xl font-bold">{o?.total_candidate}</p>
                </div>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
