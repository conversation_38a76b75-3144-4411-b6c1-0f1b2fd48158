// Next, React, Tw
import { useRouter } from 'next/router';
import { useEffect, useState, Fragment } from 'react';
import { twMerge } from 'tailwind-merge';
import { useDispatch, useSelector } from 'react-redux';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions, Divider } from '@mui/material';
import { Edit } from '@mui/icons-material';

// Packages
import * as yup from 'yup';

// Components
import Layout from '../../layouts/module/voting';
import { TablePaginationCustom } from '../../components/Shared/table';
import UploadCandidatesButton from '../../components/voting/UploadCandidatesButton';
import { useSnackbar } from '../../components/Shared/snackbar';
import { TextInput, SelectInput, SearchInput } from '../../components/Shared/CustomInput';
import ImagePicker from '../../components/Shared/ImagePicker';

// Others
import axios from '../../utils/axios';
import { toUpperCaseFirstLetter } from '../../utils/shared';
import { VOTING_ENDPOINT } from '../../utils/voting';
import { EVENT_ENDPOINT } from '../../utils/event';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { push, query } = useRouter();
  const { q } = query;
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const { allStaffs } = useSelector((state) => state.aum);

  const [eventData, setEventData] = useState([]);

  // Table
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(20);
  const [tableData, setTableData] = useState([]);
  const getBodyCellStyle = () => 'text-center py-1 text-xs';

  const onChangePage = (event, newPage) => {
    setPage(newPage);
  };
  const getTableData = () => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  };

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const schema = yup.object({
    event_id: yup.string(),
    voting_name: yup.string().required('Please provide name.'),
    total_candidate: yup
      .number()
      .transform((value) => Number(value))
      .when('candidate_source', {
        is: 'manual',
        then: (s) => s.required('Please provide total candidate.'),
        otherwise: (s) => s.optional(),
      }),
    status: yup.string().required('Please provide status.').default('draft'),
    candidate_source: yup.string().required('Please provide candidate source.'),
  });
  const [dialogData, setDialogData] = useState({});
  const [candidateList, setCandidateList] = useState([]);
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = async (editMode, data) => {
    if (editMode) {
      let response;
      setDialogData(data);
      try {
        response = await axios.get(`${VOTING_ENDPOINT}/candidate/voting_id/${data?.id}`);
        if (response.data.status === 'success' && response.data.data !== null) {
          setCandidateList(response?.data?.data);
        }
      } catch (error) {
        // console.error('Error fetching data:', error);
      }
    }

    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
        return;
      }
      dispatch(setIsLoading(true));
      try {
        let response;
        switch (action) {
          case 'post':
            // If source is event user list, fetch attendees first
            if (dialogData?.candidate_source === 'event_user_list' && dialogData?.event_id) {
              try {
                const attendeesResponse = await axios.get(
                  `${EVENT_ENDPOINT}/attendees/event_id/${dialogData.event_id}`
                );
                if (
                  attendeesResponse?.data?.status === 'success' &&
                  attendeesResponse?.data?.data
                ) {
                  const attendees = attendeesResponse?.data?.data?.map((o) => ({
                    ...o,
                    ...allStaffs?.find((e) => e?.staff_id === o?.staff_id),
                  }));

                  payload.total_candidate = attendees.length;

                  // Create voting
                  response = await axios.post(`${VOTING_ENDPOINT}/voting`, payload);
                  if (response?.data?.status === 'success') {
                    const votingId = response?.data?.data?.split(' ')[3];

                    // Create candidates from attendees
                    await Promise.all(
                      attendees.map((attendee) =>
                        axios.post(`${VOTING_ENDPOINT}/candidate`, {
                          candidate_name: attendee?.name || attendee?.staff_name || '',
                          candidate_staff_id: attendee?.staff_id || '',
                          voting_id: votingId,
                        })
                      )
                    );
                  }
                } else {
                  throw new Error('Failed to fetch event attendees');
                }
              } catch (error) {
                enqueueSnackbar('Failed to fetch event attendees', { variant: 'error' });
                dispatch(setIsLoading(false));
                return;
              }
            } else {
              // Handle CSV upload or manual input
              response = await axios.post(`${VOTING_ENDPOINT}/voting`, payload);
              if (response?.data?.status === 'success') {
                const votingId = response?.data?.data?.split(' ')[3];
                
                if (dialogData?.candidate_source === 'csv_upload') {
                  // Create candidates from CSV data
                  await Promise.all(
                    candidateList.map((candidate) =>
                      axios.post(`${VOTING_ENDPOINT}/candidate`, {
                        ...candidate,
                        voting_id: votingId,
                      })
                    )
                  );
                } else {
                  // Original manual input flow
                  for (let i = 0; i < payload?.total_candidate; i += 1) {
                    // eslint-disable-next-line
                    await axios.post(`${VOTING_ENDPOINT}/candidate`, {
                      candidate_name: '',
                      candidate_staff_id: '',
                      voting_id: votingId,
                    });
                  }
                }
              }
            }
            break;
          case 'put':
            response = await axios.put(`${VOTING_ENDPOINT}/voting/${dialogData.id}`, payload);
            if (response?.data?.status === 'success') {
              for (let i = 0; i < candidateList?.length; i += 1) {
                await axios.put(
                  `${VOTING_ENDPOINT}/candidate/${candidateList[i]?.id}`,
                  candidateList[i]
                );
              }
            }
            break;
          case 'delete':
            response = await axios.delete(`${VOTING_ENDPOINT}/voting/${dialogData.id}`);
            if (response?.data?.status === 'success') {
              for (let i = 0; i < candidateList?.length; i += 1) {
                await axios.delete(`${VOTING_ENDPOINT}/candidate/${candidateList[i]?.id}`);
              }
            }
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
      } catch (error) {
        // console.log(error);
      }
      dispatch(setIsLoading(false));
    }

    setDialogData({});
    setCandidateList([]);
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const getStatusStyle = (status) => {
    if (status.toLowerCase().includes('draft')) {
      return 'bg-[#fff1aa]';
    }
    if (status.toLowerCase().includes('closed')) {
      return 'bg-[#a9ffb7]';
    }
    if (status.toLowerCase().includes('open')) {
      return 'bg-[#ffcdb7]';
    }
    return 'bg-white';
  };

  const handleClickRow = (row) => {
    console.log(row);
    const path = row.candidate_source === 'manual' ? 'vote-now' : 'vote-now-all';
    push(`/voting/${path}?votingid=${row?.id}`);
  };

  const fetchEventData = async () => {
    try {
      const response = await fetch(`${EVENT_ENDPOINT}/latest/keyword`);
      const data = await response.json();
      if (data?.data) {
        setEventData(data?.data);
      }
    } catch {
      setEventData([]);
    }
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${VOTING_ENDPOINT}/voting/all/keyword`);
      if (response.data.data !== null) {
        response?.data?.data?.reverse();
        setTableData(response.data.data);
      }
    } catch (error) {
      // console.error('Error fetching data:', error);
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
    fetchEventData();
  }, []);
  return (
    <>
      <div className="flex h-full w-full flex-col bg-[url(/events/lrp-2024/bg.png)] bg-cover bg-center bg-no-repeat p-8">
        <div className="container mx-auto flex flex-col gap-4 rounded-md bg-white px-8 py-4 text-black">
          <div className="flex w-full flex-col gap-2 md:flex-row md:justify-between">
            <SearchInput />

            <button
              type="button"
              className="bg-voting rounded-md px-2 py-1 text-white"
              onClick={() => handleClickOpenDialog(false)}
            >
              New Voting
            </button>
          </div>
          <div className="overflow-x-auto scrollbar">
            <table className="min-w-full bg-white text-black">
              <thead>
                <tr>
                  {['No.', 'Voting Name', 'Total Candidate', 'Status', ''].map((label, i) => (
                    <td key={i} className="bg-voting whitespace-nowrap px-4 text-center text-white">
                      {label}
                    </td>
                  ))}
                </tr>
              </thead>
              <tbody>
                {getTableData().map((row, i) => (
                  <tr
                    key={i}
                    className={`${`${
                      i % 2 === 0 ? 'bg-[#f8f8f8]' : 'bg-white'
                    }`} cursor-pointer hover:bg-gray-300`}
                  >
                    <td className={getBodyCellStyle()} onClick={() => handleClickRow(row)}>
                      {i + 1 + rowsPerPage * page}
                    </td>
                    <td className={getBodyCellStyle()} onClick={() => handleClickRow(row)}>
                      {toUpperCaseFirstLetter(row?.voting_name)}
                    </td>
                    <td className={getBodyCellStyle()} onClick={() => handleClickRow(row)}>
                      {row?.total_candidate}
                    </td>
                    <td
                      className={twMerge(getBodyCellStyle(), 'flex justify-center')}
                      onClick={() => handleClickRow(row)}
                    >
                      <p
                        className={twMerge(
                          getStatusStyle(row?.status),
                          'w-[100px] rounded-lg px-2 py-1 font-semibold'
                        )}
                      >
                        {row?.status?.toUpperCase()}
                      </p>
                    </td>
                    <td className=" text-center">
                      <div className="flex gap-2">
                        <button
                          type="button"
                          className=""
                          onClick={() => handleClickOpenDialog(true, row)}
                        >
                          <Edit className="text-voting" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <TablePaginationCustom
            count={tableData.length}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={onChangePage}
          />
        </div>
      </div>
      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-voting text-center">
          {!editModeDialog && <p className="text-white">Add New Voting</p>}
          {editModeDialog && <p className="text-white">Edit Voting</p>}
        </DialogTitle>
        <DialogContent>
          <div className="flex w-full flex-col gap-4 px-2 py-8 md:w-[400px]">
            {!editModeDialog && (
              <>
                <Divider>
                  <p className="text-lg font-semibold">Event</p>
                </Divider>
                <SelectInput
                  name="event_id"
                  value={dialogData?.event_id}
                  placeholder="Select Event"
                  options={eventData?.map((o) => ({ label: o?.name, value: o?.event_id }))}
                  onChange={handleDialogDataChange}
                  showRedAsteric={false}
                />
              </>
            )}
            <SelectInput
              name="candidate_source"
              value={dialogData?.candidate_source}
              placeholder="Choose a source for the candidate"
              options={[
                { label: 'User List from Event', value: 'event_user_list' },
                { label: 'Input Manually', value: 'manual' },
                { label: 'Upload CSV File', value: 'csv_upload' },
              ]}
              onChange={handleDialogDataChange}
              showRedAsteric={false}
            />
            <Divider>
              <p className="text-lg font-semibold">Details</p>
            </Divider>
            <TextInput
              name="voting_name"
              value={dialogData?.voting_name}
              placeholder="Name"
              onChange={handleDialogDataChange}
            />
            {editModeDialog && (
              <SelectInput
                name="status"
                value={dialogData?.status}
                placeholder="Status"
                options={[
                  { label: 'Draft', value: 'draft' },
                  { label: 'Open', value: 'open' },
                  { label: 'Closed', value: 'closed' },
                ]}
                onChange={handleDialogDataChange}
              />
            )}
            {dialogData?.candidate_source === 'csv_upload' && (
              <div className="flex flex-col gap-2">
                <UploadCandidatesButton
                  onCandidatesUploaded={(candidates) => {
                    setCandidateList(candidates);
                    setDialogData((prev) => ({
                      ...prev,
                      total_candidate: candidates.length,
                    }));
                  }}
                />
              </div>
            )}
            {dialogData?.candidate_source === 'manual' && (
              <>
                {!editModeDialog && (
                  <TextInput
                    name="total_candidate"
                    value={dialogData?.total_candidate}
                    placeholder="Total Candidate"
                    onChange={(event) => {
                      setDialogData((prevValues) => ({
                        ...prevValues,
                        total_candidate: Number(event?.target?.value),
                      }));
                    }}
                  />
                )}
                {candidateList?.length > 0 && (
                  <Divider>
                    <p className="text-lg font-semibold">Candidates</p>
                  </Divider>
                )}
                {candidateList?.map((o, i) => (
                  <div key={i} className="flex flex-col items-center gap-1">
                    <ImagePicker
                      name="candidate_picture"
                      value={o?.candidate_picture}
                      onChange={(event) => {
                        setCandidateList((prev) => {
                          const temp = [...prev];
                          temp[i] = {
                            ...temp[i],
                            candidate_picture: event?.target?.value,
                          };
                          return temp;
                        });
                      }}
                    />
                    <TextInput
                      name="candidate_name"
                      value={o?.candidate_name}
                      placeholder="Candidate Name"
                      onChange={(event) => {
                        setCandidateList((prev) => {
                          const temp = [...prev];
                          temp[i] = {
                            ...temp[i],
                            candidate_name: event?.target?.value,
                          };
                          return temp;
                        });
                      }}
                    />
                  </div>
                ))}
              </>
            )}
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between">
            <div>
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('delete')}
                  className="bg-red-500 p-2 text-white"
                >
                  Delete
                </button>
              )}
            </div>
            <div className="flex gap-2">
              <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
                Close
              </button>

              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-voting p-2 text-white"
                >
                  Save
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-voting p-2 text-white"
                >
                  Save
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
}
