// Next, React, Tw
import Image from 'next/image';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

// Mui
import { Autocomplete, TextField, useMediaQuery, Divider, TablePagination } from '@mui/material';

// Packages
import * as yup from 'yup';

// Components
import Layout from '../../layouts/module/voting';
import QRBox from '../../components/Shared/QRBox';

// Others
import axios from '../../utils/axios';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { useSnackbar } from '../../components/Shared/snackbar';
import { VOTING_ENDPOINT } from '../../utils/voting';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { user } = useAuthContext();
  const { isAdmin } = useModuleRoleContext();
  const isSmallScreen = useMediaQuery('(max-width: 768px)');
  const { enqueueSnackbar } = useSnackbar();
  const { query, asPath } = useRouter();
  const { votingid } = query;

  const [userVoted, setUserVoted] = useState(false);
  const [votingData, setVotingData] = useState({});
  const [candidateList, setCandidateList] = useState([]);
  const [tableCandidateList, setTableCandidateList] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);

  // Form
  const schema = yup.object({
    candidate_id: yup.string().required('Please select a candidate.'),
    voter_id: yup.string().required('Please provide voter id.'),
  });
  const [formData, setFormData] = useState({});

  const handleFormSubmit = async (action) => {
    if (action) {
      let payload;
      try {
        payload = { ...formData, voter_id: user?.staff_id };
        payload = await schema.validate(payload, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
        return;
      }
      try {
        let response;
        switch (action) {
          case 'put':
            response = await axios.put(`${VOTING_ENDPOINT}/voter/candidate`, payload);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
      } catch (error) {
        enqueueSnackbar(error?.errors, {
          variant: 'error',
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
      }
    }
    setUserVoted(true);
    setFormData({});
    fetchCandidatesData();
  };

  // Others
  const votingOpen = () => votingData?.status === 'open';
  const getSeries = () => [...tableCandidateList]?.sort((a, b) => b.total_vote - a.total_vote);
  const getQRSource = () => `${process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT}${asPath}`;

  const getResultPart = () => (
    <div className="w-full overflow-x-auto rounded-xl bg-white p-4 shadow-xl md:p-6">
      <div className="overflow-hidden rounded-lg border-2 border-gray-200">
        <table className="w-full min-w-[500px] table-fixed">
          <thead className="bg-[#3c50e0] text-white">
            <tr>
              <th className="w-1/4 border-b-2 border-[#2c3ed0] px-6 py-4 text-left text-base font-bold uppercase tracking-wider">
                Staff ID
              </th>
              <th className="w-1/2 border-b-2 border-[#2c3ed0] px-6 py-4 text-left text-base font-bold uppercase tracking-wider">
                Name
              </th>
              <th className="w-1/4 border-b-2 border-[#2c3ed0] px-6 py-4 text-right text-base font-bold uppercase tracking-wider">
                Vote Count
              </th>
            </tr>
          </thead>
          <tbody className="divide-y-2 divide-gray-200">
            {getSeries()
              ?.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((candidate, index) => (
                <tr
                  key={candidate.id}
                  className={`${
                    index % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                  } transition-colors hover:bg-blue-50`}
                >
                  <td className="truncate px-6 py-5 text-base text-gray-900">
                    {candidate.candidate_staff_id}
                  </td>
                  <td className="truncate px-6 py-5 text-base text-gray-900">
                    {candidate.candidate_name}
                  </td>
                  <td className="px-6 py-5 text-right text-base font-bold text-gray-900">
                    {candidate.total_vote}
                  </td>
                </tr>
              ))}
          </tbody>
        </table>
      </div>
      <TablePagination
        component="div"
        count={getSeries()?.length || 0}
        page={page}
        onPageChange={(event, newPage) => setPage(newPage)}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={(event) => {
          setRowsPerPage(parseInt(event.target.value, 10));
          setPage(0);
        }}
        rowsPerPageOptions={[5, 10, 25]}
        className="border-t-2 border-gray-200 bg-gray-50"
      />
    </div>
  );

  const fetchCandidatesData = async () => {
    try {
      const response = await axios.get(`${VOTING_ENDPOINT}/candidate/voting_id/${votingid}`);
      if (response?.data?.data) {
        const allCandidates = response.data.data.map((o) => ({
          ...o,
          total_vote: o?.total_vote || 0,
        }));

        const filteredCandidates = allCandidates.filter(
          (candidate) => candidate.candidate_staff_id !== user.staff_id
        );

        setTableCandidateList(
          allCandidates.sort((a, b) => a.candidate_name.localeCompare(b.candidate_name))
        );
        setCandidateList(
          filteredCandidates.sort((a, b) => a.candidate_name.localeCompare(b.candidate_name))
        );
      }
    } catch {
      setCandidateList([]);
      setTableCandidateList([]);
    }
  };

  const fetchVotingData = async () => {
    try {
      const response = await axios.get(`${VOTING_ENDPOINT}/voting/id/${votingid}`);
      if (response.data.status === 'success' && response.data.data !== null) {
        const data = response.data.data[0];
        setVotingData(data);
      }
    } catch {
      setVotingData({});
    }
  };

  // eslint-disable-next-line
  useEffect(() => {
    fetchVotingData();
    fetchCandidatesData();
    if (!isSmallScreen) {
      const timer = setInterval(async () => {
        fetchCandidatesData();
      }, 1000);
      return () => {
        clearInterval(timer);
      };
    }
  }, [votingid]);

  return (
    <div className="relative flex h-screen w-full flex-col overflow-hidden bg-[url(/events/lrp-2024/bg.png)] bg-cover bg-fixed bg-center bg-no-repeat">
      <div className="container mx-auto flex h-full min-h-screen flex-col justify-center gap-4 px-4 py-6 text-white md:justify-start md:px-6 lg:px-8">
        <p className="text-center text-2xl font-bold text-white underline md:text-3xl lg:text-4xl">
          {votingData?.voting_name?.toUpperCase()}
        </p>
        {votingOpen() && !userVoted && (
          <div className="mt-4 flex flex-col justify-center gap-6 md:mt-8 md:gap-8">
            <div className="mx-auto w-full max-w-2xl rounded-lg bg-white p-3 shadow-lg md:p-4">
              <Autocomplete
                options={candidateList}
                getOptionLabel={(option) => option.candidate_name || ''}
                renderOption={(props, option) => (
                  <li {...props} key={option.id}>
                    <div className="flex items-center gap-4">
                      <div>
                        <p className="font-semibold text-black">{option.candidate_name}</p>
                        <p className="text-sm text-gray-600">{option.candidate_staff_id}</p>
                      </div>
                    </div>
                  </li>
                )}
                value={candidateList.find((c) => c.id === formData.candidate_id) || null}
                onChange={(event, newValue) => {
                  setFormData((prev) => ({
                    ...prev,
                    candidate_id: newValue?.id || '',
                  }));
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Search by candidate name"
                    placeholder="Start typing..."
                    fullWidth
                  />
                )}
                isOptionEqualToValue={(option, value) => option.id === value.id}
              />
            </div>
            <div className="flex w-full flex-grow justify-center">
              <button
                type="button"
                disabled={!formData?.candidate_id || userVoted}
                className="h-[40px] rounded-lg bg-[#00e3f6] px-4 py-2 text-[#3c50e0] disabled:bg-gray-500 disabled:text-white"
                onClick={() => handleFormSubmit('put')}
              >
                Vote!
              </button>
            </div>
          </div>
        )}
        {votingOpen() && userVoted && (
          <div className="flex min-h-[50vh] w-full flex-col items-center justify-center gap-4 md:flex-row">
            <Image src="/voting/ballot.svg" width={175} height={175} alt="Vote successful" />
            <p className="text-center text-xl font-bold">Voted, Thank You!</p>
          </div>
        )}
        {!votingOpen() && (
          <div className="flex min-h-[50vh] w-full flex-col items-center justify-center gap-4 md:flex-row">
            <Image src="/voting/ballot.svg" width={175} height={175} alt="Voting not yet open" />
            <p className="text-center text-xl font-bold">Not yet!</p>
          </div>
        )}
        {isAdmin && (
          <div className="mt-8 hidden flex-col items-center gap-4 border-gray-500 pt-4 md:flex md:flex-row">
            <div className="w-full md:w-auto">
              <QRBox
                value={getQRSource()}
                size={Number(`${!isSmallScreen ? 250 : 200}`)}
                showDownloadButton
              />
            </div>
            <Divider
              sx={{ backgroundColor: 'white' }}
              orientation={isSmallScreen ? 'horizontal' : 'vertical'}
              flexItem
            />
            <div className="w-full flex-grow">{getResultPart()}</div>
          </div>
        )}
      </div>
    </div>
  );
}
