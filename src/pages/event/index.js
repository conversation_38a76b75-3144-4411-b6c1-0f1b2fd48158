// Next, React, Tw
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';
import { twMerge } from 'tailwind-merge';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions, useMediaQuery } from '@mui/material';
import { Edit } from '@mui/icons-material';

// Packages
import * as yup from 'yup';
import moment from 'moment';
import randomColor from 'randomcolor';

// Components
import Layout from '../../layouts/module/event';
import {
  SearchInput,
  SelectInput,
  TextInput,
  TextAreaInput,
  DateInput,
  BinarySwitchInput,
} from '../../components/Shared/CustomInput';
import ImagePicker from '../../components/Shared/ImagePicker';
import { TablePaginationCustom } from '../../components/Shared/table';
import WarnBeforeActionPopupButton from '../../components/Shared/WarnBeforeActionPopupButton';

// Others
import { useSnackbar } from '../../components/Shared/snackbar';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { EVENT_ENDPOINT } from '../../utils/event';
import { checkAndReplaceStringWithHyphen } from '../../utils/shared';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { useAuthContext } from '../../utils/auth/useAuthContext';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { query, push } = useRouter();
  const { q } = query;
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const { isAdmin } = useModuleRoleContext();
  const isSmallScreen = useMediaQuery('(max-width: 600px)');
  const { user } = useAuthContext();

  // Table
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(20);
  const [tableData, setTableData] = useState([]);
  const bodyCellStyle = 'text-center py-1 text-sm px-2';
  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);

  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) setDialogData(data);
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };

  const schema = yup.object({
    created_by_staff_id: yup
      .string()
      .required('Please provide created by.')
      ?.default(user?.staff_id),
    description: yup.string(),
    end_date: yup.number(),
    google_map_link: yup.string(),
    location: yup.string().required('Please provide location.'),
    name: yup.string().required('Please provide name.'),
    qr_is_dynamic: yup.boolean().required('Please provide QR is dynamic.')?.default(false),
    start_date: yup.number(),
    status: yup.string().required('Please provide status.')?.default('active'),
    waze_link: yup.string(),
  });

  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }

      if (!payload?.end_date) payload.end_date = payload.start_date;

      dispatch(setIsLoading(true));
      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${EVENT_ENDPOINT}`, payload);
            break;
          case 'put':
            response = await axios.put(`${EVENT_ENDPOINT}/${payload?.id}`, payload);
            break;
          case 'delete':
            response = await axios.delete(`${EVENT_ENDPOINT}/${payload?.event_id}`);
            break;
          default:
            break;
        }

        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = 'Success';
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    dispatch(setIsLoading(false));
    setDialogData({});
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const getReadableDate = (date) => moment.unix(date).format('DD/MM/YYYY');

  const handleClickRow = (id) => {
    if (isAdmin) {
      push(`/event/${id}`);
      return;
    }
    push(`/event/view-event/${id}`);
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      let endpoint = '/latest/keyword';
      if (isAdmin) endpoint = '/all/keyword';
      const response = await axios.get(`${EVENT_ENDPOINT}${endpoint}`);

      if (response?.data?.data) {
        response.data.data.reverse();

        setTableData(response?.data?.data);
      }
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [isAdmin]);

  return (
    <>
      {!isSmallScreen && (
        <>
          <div className="container mx-auto p-1 md:p-4">
            <div className="flex flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
              <div className="flex items-center justify-between">
                <p>Events List</p>
                <div className="flex gap-2">
                  <SearchInput />
                  {isAdmin && (
                    <button
                      type="button"
                      className="bg-fa rounded-lg px-2 py-1 text-white"
                      onClick={() => handleClickOpenDialog(false)}
                    >
                      NEW EVENT
                    </button>
                  )}
                </div>
              </div>
              <div className="overflow-x-auto scrollbar">
                <table className="min-w-full bg-white text-black">
                  <thead>
                    <tr>
                      {[
                        'No.',
                        'ID',
                        'Name',
                        'Description',
                        'Status',
                        'Location',
                        'Google Map Link',
                        'Waze Link',
                        'Start Date',
                        'End Date',
                        '',
                      ].map((label, i) => (
                        <td key={i} className="bg-fa whitespace-nowrap px-4 text-center text-white">
                          {label}
                        </td>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {filteredTableData
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((row, i) => (
                        <tr
                          key={i}
                          className="cursor-pointer border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-200"
                        >
                          <td
                            className={bodyCellStyle}
                            onClick={() => handleClickRow(row?.event_id)}
                          >
                            {i + 1}
                          </td>
                          <td
                            className={twMerge(bodyCellStyle, 'whitespace-nowrap')}
                            onClick={() => handleClickRow(row?.event_id)}
                          >
                            {checkAndReplaceStringWithHyphen(row?.event_id)}
                          </td>
                          <td
                            className={twMerge(bodyCellStyle, 'whitespace-nowrap')}
                            onClick={() => handleClickRow(row?.event_id)}
                          >
                            {checkAndReplaceStringWithHyphen(row?.name)}
                          </td>
                          <td
                            className={bodyCellStyle}
                            onClick={() => handleClickRow(row?.event_id)}
                          >
                            {checkAndReplaceStringWithHyphen(row?.description)}
                          </td>
                          <td
                            className={bodyCellStyle}
                            onClick={() => handleClickRow(row?.event_id)}
                          >
                            {checkAndReplaceStringWithHyphen(row?.status?.toUpperCase())}
                          </td>
                          <td
                            className={twMerge(bodyCellStyle, 'whitespace-nowrap')}
                            onClick={() => handleClickRow(row?.event_id)}
                          >
                            {checkAndReplaceStringWithHyphen(row?.location)}
                          </td>
                          <td
                            className={twMerge(
                              bodyCellStyle,
                              'max-w-[200px] overflow-hidden whitespace-nowrap'
                            )}
                            onClick={() => handleClickRow(row?.event_id)}
                          >
                            {checkAndReplaceStringWithHyphen(row?.google_map_link)}
                          </td>
                          <td
                            className={twMerge(
                              bodyCellStyle,
                              'max-w-[200px] overflow-hidden whitespace-nowrap'
                            )}
                            onClick={() => handleClickRow(row?.event_id)}
                          >
                            {checkAndReplaceStringWithHyphen(row?.waze_link)}
                          </td>
                          <td
                            className={bodyCellStyle}
                            onClick={() => handleClickRow(row?.event_id)}
                          >
                            {checkAndReplaceStringWithHyphen(getReadableDate(row?.start_date))}
                          </td>
                          <td
                            className={bodyCellStyle}
                            onClick={() => handleClickRow(row?.event_id)}
                          >
                            {checkAndReplaceStringWithHyphen(getReadableDate(row?.end_date))}
                          </td>
                          <td className=" text-center">
                            {isAdmin && (
                              <button
                                type="button"
                                onClick={() => handleClickOpenDialog(true, row)}
                              >
                                <Edit className="text-fa" />
                              </button>
                            )}
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
              <TablePaginationCustom
                count={filteredTableData.length}
                page={page}
                rowsPerPage={rowsPerPage}
                onPageChange={(e, p) => setPage(p)}
              />
            </div>
          </div>

          <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
            <DialogTitle className="bg-fa text-center text-white">New Event</DialogTitle>
            <DialogContent>
              <div className="my-8 flex w-full flex-col gap-4 p-2 md:w-[400px]">
                <div className="flex justify-center">
                  <ImagePicker
                    name="image"
                    value={dialogData?.image}
                    onChange={handleDialogDataChange}
                  />
                </div>

                <TextInput
                  name="name"
                  value={dialogData?.name}
                  placeholder="Name"
                  onChange={handleDialogDataChange}
                />
                <TextAreaInput
                  name="description"
                  value={dialogData?.description}
                  placeholder="Description"
                  onChange={handleDialogDataChange}
                  showRedAsteric={false}
                />
                <TextInput
                  name="location"
                  value={dialogData?.location}
                  placeholder="Location"
                  onChange={handleDialogDataChange}
                />
                <TextInput
                  name="google_map_link"
                  value={dialogData?.google_map_link}
                  placeholder="Google Map Link"
                  onChange={handleDialogDataChange}
                  showRedAsteric={false}
                />
                <TextInput
                  name="waze_link"
                  value={dialogData?.waze_link}
                  placeholder="Waze Link"
                  onChange={handleDialogDataChange}
                  showRedAsteric={false}
                />
                <DateInput
                  views={['day']}
                  name="start_date"
                  value={dialogData?.start_date}
                  placeholder="Start Date"
                  returnedFormat="unix"
                  onChange={handleDialogDataChange}
                />
                <DateInput
                  views={['day']}
                  name="end_date"
                  value={dialogData?.end_date}
                  placeholder="End Date"
                  returnedFormat="unix"
                  onChange={handleDialogDataChange}
                />
                <BinarySwitchInput
                  name="qr_is_dynamic"
                  value={dialogData?.qr_is_dynamic}
                  placeholder="Dynamic QR"
                  onChange={handleDialogDataChange}
                  showRedAsteric
                />
                {editModeDialog && (
                  <SelectInput
                    name="status"
                    value={dialogData?.status}
                    placeholder="Status"
                    options={['active', 'cancelled']}
                    onChange={handleDialogDataChange}
                  />
                )}
              </div>
            </DialogContent>
            <DialogActions>
              <div className="flex w-full justify-between gap-4">
                <div className="flex items-center gap-2">
                  <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
                    Cancel
                  </button>
                  {editModeDialog && (
                    <WarnBeforeActionPopupButton
                      onApprove={() => handleDialogClose('delete')}
                      onCancel={() => {}}
                    />
                  )}
                </div>

                <div className="flex items-center gap-2">
                  {!editModeDialog && (
                    <button
                      type="button"
                      onClick={() => handleDialogClose('post')}
                      className="bg-fa cta-btn"
                    >
                      Create
                    </button>
                  )}
                  {editModeDialog && (
                    <button
                      type="button"
                      onClick={() => handleDialogClose('put')}
                      className="bg-fa cta-btn"
                    >
                      Edit
                    </button>
                  )}
                </div>
              </div>
            </DialogActions>
          </Dialog>
        </>
      )}
      {isSmallScreen && (
        <div className="flex h-full w-full flex-col gap-1">
          <div className="w-full bg-white p-1 text-center">Events List</div>
          <div className="container mx-auto flex-grow bg-white p-2">
            <div className="flex h-full w-full flex-col gap-2 rounded-md ">
              {tableData?.map((row, i) => (
                <Link
                  key={i}
                  href={`/event/view-event/${row?.event_id}`}
                  className="flex flex-col gap-1 rounded-md p-2"
                  style={{
                    backgroundColor: randomColor({
                      luminosity: 'light',
                    }),
                  }}
                >
                  <p className="text-lg font-semibold">{row?.name}</p>
                  <p className="">{row?.location}</p>
                  <p className="">
                    {getReadableDate(row?.start_date)} - {getReadableDate(row?.end_date)}
                  </p>
                </Link>
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  );
}
