// Next, React, Tw
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Packages
import moment from 'moment';

// Compoents
import Layout from '../../../layouts/module/event';

// Others
import { useSnackbar } from '../../../components/Shared/snackbar';
import axios from '../../../utils/axios';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { EVENT_ENDPOINT } from '../../../utils/event';
import { useAuthContext } from '../../../utils/auth/useAuthContext';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const dispatch = useDispatch();
  const { query, replace } = useRouter();
  const { eventId, expiredAt } = query;
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuthContext();

  // Dialog

  const [dialogData, setDialogData] = useState({});

  const handleUserAttended = async () => {
    try {
      const response = await axios.put(`${EVENT_ENDPOINT}/attendees/${dialogData?.id}`, {
        ...dialogData,
        attendance_time:
          dialogData?.attendance_time === 0 ? moment()?.unix() : dialogData?.attendance_time,
      });

      let statusVariant;
      let message;
      if ([201, 204]?.includes(response?.status)) {
        statusVariant = 'success';
        message = 'Success';
      } else {
        statusVariant = 'error';
        message = 'Failed';
      }
      enqueueSnackbar(message, {
        variant: statusVariant,
      });
    } catch {
      enqueueSnackbar('Failed', {
        variant: 'error',
      });
    }
    replace(`/event/view-event/${eventId}`);
  };

  // Others

  const fetchAttendeeData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(
        `${EVENT_ENDPOINT}/attendees/checkin/${eventId}/${user?.staff_id}`
      );

      if (response?.data?.data) {
        setDialogData(response?.data?.data?.[0]);
      }
    } catch {
      /* empty */
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (!eventId) return;
    fetchAttendeeData();
  }, [eventId]);

  useEffect(() => {
    if (!expiredAt) return;

    if (!dialogData?.id) return;

    if (moment?.unix() > moment?.unix(Number(expiredAt))?.add(2, 'minutes')?.unix()) return;

    if (dialogData?.rsvp !== 'yes') {
      enqueueSnackbar('Please meet event PIC.', {
        variant: 'error',
      });
      return;
    }

    handleUserAttended();
  }, [dialogData?.id]);

  if (expiredAt && moment?.unix() > Number(expiredAt))
    return <div className="flex h-full items-center justify-center">QR expired.</div>;

  if (!dialogData?.id)
    return (
      <div className="flex h-full items-center justify-center">
        You are not invited to this event :-(
      </div>
    );

  return <div className="flex h-full items-center justify-center">Redirecting...</div>;
}
