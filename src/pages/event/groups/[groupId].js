// Next, React, Tw
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Image from 'next/image';
import { twMerge } from 'tailwind-merge';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions, Divider } from '@mui/material';
import { Edit, Delete } from '@mui/icons-material';

// Compoents
import Layout from '../../../layouts/module/event';
import FilterRoleButtonGroup from '../../../components/Shared/FilterRoleButtonGroup';
import { TablePaginationCustom } from '../../../components/Shared/table';
import { SearchInput, AutoCompleteTextInput } from '../../../components/Shared/CustomInput';

// Others
import { useSnackbar } from '../../../components/Shared/snackbar';
import axios from '../../../utils/axios';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { EVENT_ENDPOINT } from '../../../utils/event';
import { useParamContext } from '../../../utils/auth/ParamProvider';
import { checkAndReplaceStringWithHyphen, getFilteredStaffData } from '../../../utils/shared';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { dataRole, groupId, q } = query;
  const { replaceParam } = useParamContext();
  const { enqueueSnackbar } = useSnackbar();
  const { allStaffs } = useSelector((state) => state.aum);

  const [groupData, setGroupData] = useState({});

  // Table
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(20);
  const [tableData, setTableData] = useState([]);
  const bodyCellStyle = 'text-center py-1 text-sm whitespace-nowrap px-2';
  const filteredTableData = (() => {
    const temp = getFilteredStaffData(tableData, dataRole, 'position');
    if ([undefined, '']?.includes(q)) {
      return temp;
    }
    return temp.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const handleClickOpenDialog = (editMode, data) => {
    setDialogOpen(true);
  };

  const handleDialogClose = async (action) => {
    try {
      await axios.put(`${EVENT_ENDPOINT}/groups/${groupId}`, {
        ...groupData,
        staff_id_list: tableData?.map((o) => o?.staff_id),
      });

      enqueueSnackbar('Success', {});
    } catch {
      enqueueSnackbar('Failed', {
        variant: 'error',
      });
    }
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${EVENT_ENDPOINT}/groups/id/${groupId}`);

      if (response?.data?.data) {
        setTableData(
          [...response?.data?.data?.[0]?.staff_id_list]?.map((o) => ({
            ...allStaffs?.find((e) => e?.staff_id === o),
          }))
        );
        delete response?.data?.data?.[0]?.staff_id_list;

        setGroupData(response?.data?.data?.[0]);
      }
    } catch (error) {
      // console.log(error);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (!dataRole) replaceParam({ dataRole: 'all' });
  }, [dataRole]);

  useEffect(() => {
    if (!groupId) return;

    fetchData();
  }, [groupId, allStaffs?.length]);

  return (
    <>
      <div className="container mx-auto p-1 md:p-4">
        <div className="flex flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
          <p>{groupData?.name}</p>
          <Divider />

          <div className="flex items-center justify-between">
            <FilterRoleButtonGroup />
            <div className="flex items-center gap-2">
              <button
                type="button"
                className="flex items-center gap-1 rounded-lg bg-[#ceff8f] px-3 py-2 text-sm"
                onClick={() => handleClickOpenDialog(false)}
              >
                <Image
                  src="/event/ant-design_user-add-outlined.svg"
                  width={20}
                  height={20}
                  alt="Icon"
                />
                Add User
              </button>
              <SearchInput />
            </div>
          </div>

          <div className="overflow-x-auto ">
            <table className="min-w-full bg-white text-black">
              <thead>
                <tr>
                  {['No.', 'Name', 'Division', 'E-Mail', ''].map((label, i) => (
                    <td key={i} className="bg-fa whitespace-nowrap px-4 text-center text-white">
                      {label}
                    </td>
                  ))}
                </tr>
              </thead>
              <tbody>
                {filteredTableData
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((row, i) => (
                    <tr
                      key={i}
                      className="cursor-pointer border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-200"
                    >
                      <td className={bodyCellStyle}>{i + 1}</td>
                      <td className={twMerge(bodyCellStyle, 'text-left')}>
                        {checkAndReplaceStringWithHyphen(row?.name)}
                      </td>
                      <td className={bodyCellStyle}>
                        {checkAndReplaceStringWithHyphen(row?.division)}
                      </td>
                      <td className={bodyCellStyle}>
                        {checkAndReplaceStringWithHyphen(row?.email)}
                      </td>
                      <td className=" text-center">
                        <button type="button" onClick={() => handleClickOpenDialog(true, row)}>
                          <Edit className="text-fa" />
                        </button>
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
          <TablePaginationCustom
            count={filteredTableData.length}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={(e, p) => setPage(p)}
          />
        </div>
      </div>
      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-fa text-center text-white">Add User</DialogTitle>
        <DialogContent>
          <div className="my-8 flex min-h-[400px] w-full flex-col gap-4 p-2 md:w-[400px]">
            <AutoCompleteTextInput
              placeholder="Type a Name"
              options={allStaffs?.map((o) => o?.name)}
              onClick={(event) =>
                setTableData((prev) => [
                  ...prev,
                  allStaffs?.find((o) => o?.name === event?.target?.value),
                ])
              }
              showRedAsteric={false}
            />
            <Divider />
            {tableData
              ?.map((o) => o?.name)
              ?.map((o, i) => (
                <div key={i} className="flex items-center justify-between">
                  <p>{o}</p>
                  <button
                    type="button"
                    onClick={() =>
                      setTableData((prev) => {
                        const temp = [...prev];
                        temp.splice(i, 1);
                        return temp;
                      })
                    }
                  >
                    <Delete className="w-[15px]" />
                  </button>
                </div>
              ))}
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <button type="button" onClick={() => setDialogOpen(false)} className="p-2">
              Cancel
            </button>
            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => handleDialogClose()}
                className="bg-fa rounded-[4px] p-2 font-semibold text-white"
              >
                Save
              </button>
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
}
