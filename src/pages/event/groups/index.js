// Next, React, Tw
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { Edit } from '@mui/icons-material';

// Packages
import * as yup from 'yup';

// Components
import Layout from '../../../layouts/module/event';
import { SearchInput, TextInput, TextAreaInput } from '../../../components/Shared/CustomInput';
import { TablePaginationCustom } from '../../../components/Shared/table';
import UserPopup from '../../../components/Shared/UserPopup';

// Others
import { useSnackbar } from '../../../components/Shared/snackbar';
import axios from '../../../utils/axios';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { EVENT_ENDPOINT } from '../../../utils/event';
import { checkAndReplaceStringWithHyphen } from '../../../utils/shared';
import { useAuthContext } from '../../../utils/auth/useAuthContext';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { query, push } = useRouter();
  const { q } = query;
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuthContext();

  // Table
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(20);
  const [tableData, setTableData] = useState([]);
  const bodyCellStyle = 'text-center py-1 text-sm';
  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);

  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) setDialogData(data);
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };

  const schema = yup.object({
    created_by: yup.string()?.default(user?.staff_id),
    description: yup.string(),
    name: yup.string().required('Please provide name.'),
    staff_id_list: yup.array().of(yup.string().required('Please provide a staff ID.'))?.default([]),
  });

  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
        return;
      }

      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${EVENT_ENDPOINT}/groups`, payload);
            break;
          case 'put':
            response = await axios.put(`${EVENT_ENDPOINT}/groups/${payload?.id}`, payload);
            break;
          case 'delete':
            response = await axios.delete(`${EVENT_ENDPOINT}/groups/${payload?.id}`, payload);
            break;
          default:
            break;
        }

        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = 'Success';
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    setDialogData({});
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const handleClickRow = (id) => push(`/event/groups/${id}`);

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${EVENT_ENDPOINT}/groups/all/keyword`);

      if (response?.data?.data) {
        response.data.data.reverse();

        setTableData(response?.data?.data || []);
      }
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div className="container mx-auto p-1 md:p-4">
        <div className="flex flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
          <div className="flex items-center justify-between">
            <p>Groups List</p>
            <div className="flex gap-2">
              <SearchInput />
              <button
                type="button"
                className="bg-fa rounded-lg px-2 py-1 text-white"
                onClick={() => handleClickOpenDialog(false)}
              >
                NEW GROUP
              </button>
            </div>
          </div>
          <div className="overflow-x-auto scrollbar">
            <table className="min-w-full bg-white text-black">
              <thead>
                <tr>
                  {[
                    'No.',
                    'Name',
                    'Description',
                    'Total Users',
                    'Created By',
                    'Created Date',
                    '',
                  ].map((label, i) => (
                    <td key={i} className="bg-fa whitespace-nowrap px-4 text-center text-white">
                      {label}
                    </td>
                  ))}
                </tr>
              </thead>
              <tbody>
                {filteredTableData
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((row, i) => (
                    <tr
                      key={i}
                      className="cursor-pointer border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-200"
                    >
                      <td className={bodyCellStyle} onClick={() => handleClickRow(row?.id)}>
                        {i + 1}
                      </td>
                      <td className={bodyCellStyle} onClick={() => handleClickRow(row?.id)}>
                        {checkAndReplaceStringWithHyphen(row?.name)}
                      </td>
                      <td className={bodyCellStyle} onClick={() => handleClickRow(row?.id)}>
                        {checkAndReplaceStringWithHyphen(row?.description)}
                      </td>
                      <td className={bodyCellStyle} onClick={() => handleClickRow(row?.id)}>
                        {checkAndReplaceStringWithHyphen(row?.staff_id_list?.length)}
                      </td>

                      <td className={bodyCellStyle}>
                        <UserPopup
                          label={row?.created_by?.toUpperCase()}
                          staff_id={row?.created_by}
                        />
                      </td>
                      <td className={bodyCellStyle} onClick={() => handleClickRow(row?.id)}>
                        {checkAndReplaceStringWithHyphen(row?.created_at)}
                      </td>
                      <td className=" text-center">
                        <button type="button" onClick={() => handleClickOpenDialog(true, row)}>
                          <Edit className="text-fa" />
                        </button>
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
          <TablePaginationCustom
            count={filteredTableData.length}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={(e, p) => setPage(p)}
          />
        </div>
      </div>

      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-fa text-center text-white">
          {!editModeDialog ? 'New' : 'Edit'} Event
        </DialogTitle>
        <DialogContent>
          <div className="my-8 flex w-full flex-col gap-4 p-2 md:w-[400px]">
            <TextInput
              name="name"
              value={dialogData?.name}
              placeholder="Name"
              onChange={handleDialogDataChange}
            />
            <TextAreaInput
              name="description"
              value={dialogData?.description}
              placeholder="Description"
              onChange={handleDialogDataChange}
              showRedAsteric={false}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
              Cancel
            </button>
            <div className="flex gap-2">
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-fa rounded-[4px] p-2 font-semibold text-white"
                >
                  Create
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('delete')}
                  className="rounded-[4px] bg-red-500 p-2 font-semibold text-white"
                >
                  Delete
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-fa rounded-[4px] p-2 font-semibold text-white"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
}
