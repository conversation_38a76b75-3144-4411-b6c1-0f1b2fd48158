// Next, React, Tw
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Packages
import * as yup from 'yup';
import moment from 'moment';

// Compoents
import Layout from '../../../layouts/module/event';
import {
  TextInput,
  SelectInput,
  TextAreaInput,
  BinarySwitchInput,
} from '../../../components/Shared/CustomInput';
import AttachmentBox from '../../../components/Shared/AttachmentBox';

// Others
import { useSnackbar } from '../../../components/Shared/snackbar';
import axios from '../../../utils/axios';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { EVENT_ENDPOINT } from '../../../utils/event';
import { useAuthContext } from '../../../utils/auth/useAuthContext';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const dispatch = useDispatch();
  const { query, replace } = useRouter();
  const { eventId } = query;
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuthContext();

  const [eventData, setEventData] = useState({});

  // Dialog 2

  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const schema2 = yup.object({
    attendance: yup.boolean().default(false),
    car_pool: yup.boolean().default(false),
    emergency_contact: yup.string().required('Please provide emergency contact.'),
    event_id: yup.string().required('Please provide event ID.')?.default(eventId),
    healthy: yup.boolean().required('Please provide you are well.'),
    plate_no: yup.string(),
    remarks_health: yup.string(),
    remarks_rsvp: yup.string(),
    room_info: yup.string(),
    rsvp: yup.string().required('Please RSVP.'),
    sex: yup.string().required('Please provide gender.'),
    size_tshirt: yup.string(),
    staff_id: yup.string().required('Please provide staff_id.'),
    team_info: yup.string(),
  });

  const handleDialog2Close = async (action) => {
    if (action) {
      let payload;
      if (action !== 'delete') {
        try {
          payload = await schema2.validate(dialogData, { abortEarly: false });
        } catch (error) {
          enqueueSnackbar(error.errors, {
            variant: 'error',
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'center',
            },
          });
          return;
        }
      }

      try {
        let response;
        switch (action) {
          case 'put':
            response = await axios.put(`${EVENT_ENDPOINT}/attendees/${payload?.id}`, payload);
            break;
          default:
            break;
        }

        let statusVariant;
        let message;
        if ([201, 204]?.includes(response?.status)) {
          statusVariant = 'success';
          message = 'Success';
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    replace('/event');
  };

  // Others

  const eventStarted = moment().unix() >= eventData?.start_date;

  const fetchAttendeeData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(
        `${EVENT_ENDPOINT}/attendees/checkin/${eventId}/${user?.staff_id}`
      );

      if (response?.data?.data) {
        setDialogData(response?.data?.data?.[0]);
      }
    } catch (error) {
      // console.log(error);
    }
    dispatch(setIsLoading(false));
  };

  const fetchEventData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${EVENT_ENDPOINT}/event_id/${eventId}`);

      if (response?.data?.data) {
        setEventData(response?.data?.data?.[0]);
      }
    } catch {
      /* empty */
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (!eventId) return;
    fetchAttendeeData();
    fetchEventData();
  }, [eventId]);

  if (!dialogData?.id)
    return (
      <div className="flex h-full items-center justify-center">
        You are not invited to this event :-(
      </div>
    );

  return (
    <div className="container mx-auto p-1 md:p-4">
      <div className="flex flex-col gap-4 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
        <p className="text-center text-lg font-bold">{eventData?.name}</p>
        {eventData?.image !== '' && (
          <div className="mb-8 flex justify-center">
            <img
              src={eventData?.image}
              alt="Attached"
              width={200}
              height={200}
              className="z-0 w-full md:w-1/3"
            />
          </div>
        )}

        <div className="mx-auto flex w-full flex-col gap-4 text-black md:w-1/3">
          {eventStarted && (
            <>
              {dialogData?.attendance_time === 0 && (
                <div className="relative flex flex-col items-center gap-1 rounded-md bg-[#ff7272] px-4 py-2">
                  <p className="text-lg font-semibold">Opps!</p>
                  <p className="text-center text-xs text-white">
                    Please scan the event QR code to register.
                  </p>
                  <Image
                    src="/event/bx_error.svg"
                    width={45}
                    height={45}
                    alt="Icon"
                    className="absolute left-0 top-1/2 -translate-y-1/2"
                  />
                </div>
              )}
              {dialogData?.attendance_time !== 0 && (
                <>
                  <div className="relative flex flex-col items-center gap-2 rounded-md bg-[#b9ffd9] px-4 py-2">
                    <p className="text-lg font-bold">Congratzzz!</p>
                    <p className="text-center text-xs">You made it to the event.</p>
                    <Image
                      src="/event/gg_check-o.svg"
                      width={45}
                      height={45}
                      alt="Icon"
                      className="absolute left-0 top-1/2 -translate-y-1/2 px-1"
                    />
                  </div>
                  <div className="relative flex flex-col items-center gap-1 rounded-md bg-[#b9f1ff] px-4 py-2">
                    <p className="text-xs">GROUP</p>
                    <p className="text-center text-lg font-bold">{dialogData?.team_info}</p>
                    {/* <p className="mt-4 text-xs">ACCOMODATION</p>
                    <p className="text-center text-lg font-bold">
                      Canopy {dialogData?.canopy_info} | Room {dialogData?.room_info}
                    </p> */}
                    <Image
                      src="/event/info.png"
                      width={45}
                      height={45}
                      alt="Icon"
                      className="absolute left-0 top-1/2 -translate-y-1/2 px-1.5"
                    />
                  </div>
                </>
              )}
            </>
          )}
          <div className="flex items-center justify-center gap-2">
            {eventData?.google_map_link !== '' && (
              <Link href={eventData?.google_map_link || '/'} target="__blank">
                <Image src="/event/google-maps.png" width={30} height={30} alt="Google Map" />
              </Link>
            )}
            {eventData?.waze_link !== '' && (
              <Link href={eventData?.waze_link || '/'} target="__blank">
                <Image src="/event/waze.png" width={30} height={30} alt="Waze" />
              </Link>
            )}
          </div>
          <AttachmentBox
            key={eventData?.id}
            GET_ALL_FILES_ENDPOINT={`${EVENT_ENDPOINT}/files/event/${eventData?.id}`}
            UPLOAD_CERTAIN_FILE_ENDPOINT={`${EVENT_ENDPOINT}/files/upload/event/${eventData?.id}`}
            DOWNLOAD_CERTAIN_FILE_ENDPOINT={`${EVENT_ENDPOINT}/files/download`}
            DELETE_CERTAIN_FILE_ENDPOINT={`${EVENT_ENDPOINT}/files`}
            DISABLE_UPLOAD
          />
          {!eventStarted && (
            <>
              <SelectInput
                name="rsvp"
                value={dialogData?.rsvp}
                placeholder="RSVP Status"
                defaultLabel="-"
                options={['pending', 'yes', 'no']}
                onChange={handleDialogDataChange}
              />
              <SelectInput
                name="sex"
                value={dialogData?.sex}
                placeholder="Gender"
                options={['male', 'female']}
                onChange={handleDialogDataChange}
              />
              <TextAreaInput
                name="remarks_rsvp"
                value={dialogData?.remarks_rsvp}
                placeholder="RSVP Remark"
                onChange={handleDialogDataChange}
                showRedAsteric={false}
              />
              <BinarySwitchInput
                name="healthy"
                value={dialogData?.healthy}
                placeholder="Healthy"
                onChange={handleDialogDataChange}
              />
              <TextAreaInput
                name="remarks_health"
                value={dialogData?.remarks_health}
                placeholder="Health Remark"
                onChange={handleDialogDataChange}
                showRedAsteric={false}
              />
              <TextInput
                name="emergency_contact"
                value={dialogData?.emergency_contact}
                placeholder="Emergency Contact"
                onChange={handleDialogDataChange}
              />
              {/*
              <SelectInput
                name="size_tshirt"
                value={dialogData?.size_tshirt}
                placeholder="T-Shirt Size"
                options={['3xs', '2xs', 'xs', 's', 'm', 'l', 'xl', '2xl', '3xl', '4xl', '5xl']}
                onChange={handleDialogDataChange}
              />
              <BinarySwitchInput
                name="car_pool"
                value={dialogData?.car_pool}
                placeholder="Will Carpool"
                onChange={handleDialogDataChange}
              />
              */}
              <TextInput
                name="plate_no"
                value={dialogData?.plate_no}
                placeholder="Plate No."
                onChange={handleDialogDataChange}
                showRedAsteric={false}
              />
              <div className="flex w-full justify-end gap-4">
                <button
                  type="button"
                  onClick={() => handleDialog2Close('put')}
                  className="bg-fa rounded-[4px] p-2 font-semibold text-white"
                >
                  Save
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
