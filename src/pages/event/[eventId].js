// Next, React, Tw
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Image from 'next/image';
import { twMerge } from 'tailwind-merge';

// Mui
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  useMediaQuery,
} from '@mui/material';
import { Delete, Link, Male, Female } from '@mui/icons-material';

// Packages
import * as yup from 'yup';
import moment from 'moment';

// Compoents
import Layout from '../../layouts/module/event';
import ReactAnimatedNumber from '../../components/Shared/ReactAnimatedNumber';
import FilterRoleButtonGroup from '../../components/Shared/FilterRoleButtonGroup';
import { TablePaginationCustom } from '../../components/Shared/table';
import {
  SearchInput,
  AutoCompleteTextInput,
  TextInput,
  SelectInput,
  TextAreaInput,
  BinarySwitchInput,
} from '../../components/Shared/CustomInput';
import AttachmentBox from '../../components/Shared/AttachmentBox';
import ExportExcelButton from '../../components/Shared/ExportExcelButton';
import UploadBulkButton from '../../components/event/UploadBulkButton';
import QRBox from '../../components/Shared/QRBox';
import WarnBeforeActionPopupButton from '../../components/Shared/WarnBeforeActionPopupButton';

// Others
import { useSnackbar } from '../../components/Shared/snackbar';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { EVENT_ENDPOINT } from '../../utils/event';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { checkAndReplaceStringWithHyphen, getFilteredStaffData } from '../../utils/shared';
import { setNameList } from '../../utils/store/luckyDrawReducer';
import { useAuthContext } from '../../utils/auth/useAuthContext';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const dispatch = useDispatch();
  const { query, push } = useRouter();
  const { dataRole, eventId, q, showAbsentAttendees } = query;
  const { replaceParam, setParam } = useParamContext();
  const { enqueueSnackbar } = useSnackbar();
  const { allStaffs } = useSelector((state) => state.aum);
  const isSmallScreen = useMediaQuery('(max-width: 768px)');
  const { isAdmin } = useModuleRoleContext();
  const { user } = useAuthContext();

  const [newAttendeeList, setNewAttendeeList] = useState([]);
  const [eventData, setEventData] = useState({});
  const [summaryData, setSummaryData] = useState([]);
  const [groupsData, setGroupsData] = useState([]);
  const [expiryUnixTimestamp, setExpiryUnixTimestamp] = useState(null);

  // Table
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(20);
  const [tableData, setTableData] = useState([]);
  const bodyCellStyle = 'text-center py-1 text-sm whitespace-nowrap px-2';
  const filteredTableData = (() => {
    let temp = getFilteredStaffData(tableData, dataRole, 'position');
    if (showAbsentAttendees === 'true') {
      temp = temp?.filter((o) => o?.rsvp === 'yes' && o?.attendance_time === 0);
    }
    if ([undefined, '']?.includes(q)) {
      return temp;
    }
    return temp.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const handleClickOpenDialog = (editMode, data) => {
    setDialogOpen(true);
  };

  const handleDialogClose = async (action) => {
    try {
      for (let i = 0; i < newAttendeeList.length; i += 1) {
        if (tableData?.find((o) => o?.name === newAttendeeList[i])) continue;
        await axios.post(`${EVENT_ENDPOINT}/attendees`, {
          staff_id: allStaffs?.find((o) => o?.name === newAttendeeList[i])?.staff_id,
          event_id: eventId,
          rsvp: 'pending',
        });
      }
      enqueueSnackbar('Success');
    } catch {
      enqueueSnackbar('Failed', {
        variant: 'error',
      });
    }
    setNewAttendeeList([]);
    setDialogOpen(false);
    fetchData();
  };

  // Dialog 2
  const [dialog2Open, setDialog2Open] = useState(false);

  const [dialog2Data, setDialog2Data] = useState({});
  const handleDialog2DataChange = (event) => {
    const { name, value } = event.target;
    setDialog2Data((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog2 = (editMode, data) => {
    if (editMode) setDialog2Data(data);
    setDialog2Open(true);
  };

  const schema2 = yup.object({
    attendance_time: yup.number().default(0),
    car_pool: yup.boolean().default(false),
    emergency_contact: yup.string(),
    event_id: yup.string().required('Please provide event ID.')?.default(eventId),
    healthy: yup.boolean(),
    plate_no: yup.string(),
    remarks_health: yup.string(),
    remarks_rsvp: yup.string(),
    room_info: yup.string(),
    rsvp: yup.string().required('Please RSVP.').default('pending'),
    sex: yup.string(),
    size_tshirt: yup.string(),
    staff_id: yup.string().required('Please provide staff_id.'),
    team_info: yup.string(),
  });

  const handleDialog2Close = async (action) => {
    if (action) {
      let payload;
      if (action !== 'delete') {
        try {
          payload = await schema2.validate(dialog2Data, { abortEarly: false });
        } catch (error) {
          enqueueSnackbar(error.errors, {
            variant: 'error',
          });
          return;
        }
      }

      try {
        let response;
        switch (action) {
          case 'put':
            response = await axios.put(`${EVENT_ENDPOINT}/attendees/${payload?.id}`, payload);
            break;
          case 'delete':
            response = await axios.delete(`${EVENT_ENDPOINT}/attendees/${dialog2Data?.id}`);
            break;
          default:
            break;
        }

        let statusVariant;
        let message;
        if ([201, 204]?.includes(response?.status)) {
          statusVariant = 'success';
          message = 'Success';
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    setDialog2Data({});
    setDialog2Open(false);
    fetchData();
  };

  // Dialog 3
  const [dialog3Open, setDialog3Open] = useState(false);

  // Dialog 4
  const [dialog4Open, setDialog4Open] = useState(false);

  // Others

  const getStyledText = (temp) => {
    if (temp === 'pending')
      return (
        <span className="flex w-[100px] justify-center rounded-sm bg-orange-500 px-2 py-1  text-white">
          Pending
        </span>
      );
    if ([true, 'yes']?.includes(temp))
      return (
        <span className="flex w-[100px] justify-center rounded-sm bg-green-500 px-2 py-1  text-white">
          Yes
        </span>
      );
    return (
      <span className="flex w-[100px] justify-center rounded-sm bg-red-500 px-2 py-1  text-white">
        No
      </span>
    );
  };

  const eventStarted = () => moment().unix() >= eventData?.start_date;

  const getLink = () =>
    `${
      process?.env?.NODE_ENV === 'development'
        ? 'localhost:3000'
        : process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT
    }/event/${
      eventData?.qr_is_dynamic && !eventStarted()
        ? `view-event/${eventId}`
        : `check-in/${eventId}?expiredAt=${expiryUnixTimestamp}`
    }`;

  const fetchEventData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${EVENT_ENDPOINT}/event_id/${eventId}`);

      if (response?.data?.data) {
        setEventData(response?.data?.data?.[0]);
      }
    } catch (error) {
      // console.log(error);
    }
    dispatch(setIsLoading(false));
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${EVENT_ENDPOINT}/attendees/event_id/${eventId}`);

      if (response?.data?.data) {
        response.data.data = response?.data?.data?.map((o) => ({
          ...o,
          ...allStaffs?.find((e) => e?.staff_id === o?.staff_id),
        }));
        setSummaryData([
          {
            title: 'Total Participant',
            value: response?.data?.data?.length,
            bgColor: 'bg-[#e4ffe7]',
            iconName: 'bi_people',
          },
          {
            title: 'Total RSVP Respond',
            value: response?.data?.data?.filter((o) => o?.rsvp !== 'pending')?.length,
            bgColor: 'bg-[#f6e5e5]',
            iconName: 'mdi_calendar-rsvp',
          },
          {
            title: `Total RSVP Accepted`,
            value: response?.data?.data?.filter((o) => o?.rsvp === 'yes')?.length,
            bgColor: 'bg-[#e3f5ff]',
            iconName: 'iconamoon_shield-yes',
          },
          {
            title: 'Total Attendance',
            value: response?.data?.data?.filter((o) => o?.attendance_time !== 0)?.length,
            bgColor: 'bg-[#e5ecf6]',
            iconName: 'carbon_report-data',
          },
        ]);
        setTableData(response?.data?.data);
      } else {
        setTableData([]);
      }
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  const fetchGroups = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${EVENT_ENDPOINT}/groups/all/keyword`);

      if (response?.data?.data) {
        setGroupsData(response?.data?.data);
      } else {
        setGroupsData([]);
      }
    } catch {
      setGroupsData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (!dataRole) replaceParam({ dataRole: 'all', showAbsentAttendees: 'false' });
  }, [dataRole]);

  useEffect(() => {
    if (!eventId) return;

    fetchEventData();
    fetchData();
    fetchGroups();
  }, [eventId, allStaffs?.length]);

  useEffect(() => {
    let timer;
    const EXPIRED_IN_MINUTES = eventData?.qr_is_dynamic ? 5 : 525600;
    if (dialog3Open) {
      const generateExpiryUnixTimestamp = () => {
        setExpiryUnixTimestamp(moment().add(EXPIRED_IN_MINUTES, 'minutes').unix());
      };
      generateExpiryUnixTimestamp();
      timer = setInterval(
        () => {
          generateExpiryUnixTimestamp();
        },
        EXPIRED_IN_MINUTES * 60 * 1000
      );
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [dialog3Open]);

  return (
    <>
      <div className="container mx-auto p-1 md:p-4">
        <div className="flex flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
          <p>{eventData?.name}</p>
          <Divider />
          <div className="flex items-center gap-2">
            {summaryData?.map((o, i) => (
              <div
                key={i}
                className={twMerge('flex w-1/4 flex-col gap-2 rounded-md px-4 py-2', o?.bgColor)}
              >
                <p>{o?.title}</p>
                <div className="flex items-center justify-between">
                  <p>
                    <ReactAnimatedNumber
                      value={o?.value}
                      formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                    />
                  </p>
                  <Image src={`/event/${o?.iconName}.svg`} alt="Icon" width={45} height={45} />
                </div>
              </div>
            ))}
          </div>
          <div className="flex items-end justify-end">
            <div className="flex items-center gap-2">
              <button
                type="button"
                className="rounded-lg bg-[#e57cff] p-1"
                onClick={() => {
                  push(`/voting?eventId=${eventData?.event_id}`);
                }}
              >
                <Image src="/event/mdi_vote.svg" width={25} height={25} alt="Icon" />
              </button>
              <button
                type="button"
                className="rounded-lg bg-[#fec702] p-1"
                onClick={() => {
                  dispatch(
                    setNameList(
                      tableData?.filter((o) => o?.attendance_time !== 0)?.map((o) => o?.name)
                    )
                  );
                  push('/lucky-draw');
                }}
              >
                <Image src="/event/mdi_prize.svg" width={25} height={25} alt="Icon" />
              </button>
              <button
                type="button"
                className="rounded-lg bg-[#0170ff] p-1"
                onClick={() => setDialog3Open(true)}
              >
                <Image src="/event/bx_qr.svg" width={25} height={25} alt="Icon" />
              </button>
              <button
                type="button"
                className="rounded-lg bg-purple-700 p-1"
                onClick={async () => {
                  await navigator.clipboard.writeText(getLink());
                  enqueueSnackbar(`Copied link to clipboard.`, {
                    variant: 'success',
                    anchorOrigin: {
                      vertical: 'top',
                      horizontal: 'center',
                    },
                  });
                }}
              >
                <Link className="h-[25px] w-[25px] text-white" />
              </button>
            </div>
          </div>
          <div className="flex items-center justify-end">
            <div className="flex items-center gap-2">
              {user?.isSuperAdmin && <UploadBulkButton type="update" attendeeArray={tableData} />}
              {user?.isSuperAdmin && <UploadBulkButton type="new" attendeeArray={tableData} />}
              <button
                type="button"
                className="cta-btn flex items-center gap-1  bg-[#ceff8f] text-black"
                onClick={() => handleClickOpenDialog(false)}
              >
                <Image
                  src="/event/ant-design_user-add-outlined.svg"
                  width={20}
                  height={20}
                  alt="Icon"
                />
                Add Attendee
              </button>
              <button
                type="button"
                className="cta-btn flex items-center gap-1  bg-[#fec702] text-black"
                onClick={() => setDialog4Open(true)}
              >
                <Image src="/event/material-symbols_upload.svg" width={20} height={20} alt="Icon" />
                Upload Attachments
              </button>
              <ExportExcelButton
                key={filteredTableData?.length}
                headers={[
                  { label: 'Event ID', key: 'event_id' },
                  {
                    label: 'Staff ID',
                    key: 'staff_id',
                  },
                  {
                    label: 'Name',
                    key: 'name',
                  },
                  {
                    label: 'Division',
                    key: 'division',
                  },
                  {
                    label: 'E-Mail',
                    key: 'email',
                  },
                  {
                    label: 'Gender',
                    key: 'sex',
                  },
                  {
                    label: 'RSVP',
                    key: 'rsvp',
                  },
                  {
                    label: 'RSVP Remarks',
                    key: 'remarks_rsvp',
                  },
                  {
                    label: 'Healthy',
                    key: 'healthy',
                  },
                  {
                    label: 'Health Remarks',
                    key: 'remarks_health',
                  },
                  // {
                  //   label: 'Emergency Contact',
                  //   key: 'emergency_contact',
                  // },
                  // {
                  //   label: 'T-Shirt Size',
                  //   key: 'size_tshirt',
                  // },
                  // {
                  //   label: 'Carpool',
                  //   key: 'car_pool',
                  // },
                  {
                    label: 'Plate No.',
                    key: 'plate_no',
                  },
                  // {
                  //   label: 'Room Info',
                  //   key: 'room_info',
                  // },
                  {
                    label: 'Team Info',
                    key: 'team_info',
                  },
                  {
                    label: 'Attendance Time',
                    key: 'attendance_time',
                  },
                ]}
                data={filteredTableData}
              />
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <FilterRoleButtonGroup />
              <BinarySwitchInput
                value={showAbsentAttendees === 'true'}
                placeholder="Absent"
                onChange={(event) => setParam({ showAbsentAttendees: event.target.value })}
                showRedAsteric={false}
              />
            </div>

            <SearchInput />
          </div>

          <div className="overflow-x-auto ">
            <table className="min-w-full bg-white text-black">
              <thead>
                <tr>
                  {[
                    'No.',
                    'Name',
                    'Division',
                    'E-Mail',
                    'Gender',
                    'RSVP',
                    'RSVP Remarks',
                    'Healthy',
                    'Health Remarks',
                    'Emergency Contact',
                    // 'T-Shirt Size',
                    // 'Carpool',
                    'Plate No.',
                    // 'Room Info',
                    'Team Info',
                    'Attendance Time',
                  ].map((label, i) => (
                    <td key={i} className="bg-fa whitespace-nowrap px-4 text-center text-white">
                      {label}
                    </td>
                  ))}
                </tr>
              </thead>
              <tbody>
                {filteredTableData
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((row, i) => (
                    <tr
                      key={i}
                      className="cursor-pointer border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-200"
                      onClick={() => handleClickOpenDialog2(true, row)}
                    >
                      <td className={bodyCellStyle}>{i + 1 + rowsPerPage * page}</td>
                      <td className={twMerge(bodyCellStyle, 'text-left')}>
                        {checkAndReplaceStringWithHyphen(row?.name)}
                      </td>
                      <td className={bodyCellStyle}>
                        {checkAndReplaceStringWithHyphen(row?.division)}
                      </td>
                      <td className={bodyCellStyle}>
                        {checkAndReplaceStringWithHyphen(row?.email)}
                      </td>
                      <td className={bodyCellStyle}>
                        {/* eslint-disable-next-line */}
                        {row?.sex === 'male' ? (
                          <Male className="text-blue-500" />
                        ) : row?.sex === 'female' ? (
                          <Female className="text-pink-500" />
                        ) : (
                          '-'
                        )}
                      </td>
                      <td className={bodyCellStyle}>{getStyledText(row?.rsvp)}</td>
                      <td className={bodyCellStyle}>
                        {checkAndReplaceStringWithHyphen(row?.remarks_rsvp)}
                      </td>
                      <td className={bodyCellStyle}>{getStyledText(row?.healthy)}</td>
                      <td className={bodyCellStyle}>
                        {checkAndReplaceStringWithHyphen(row?.remarks_health)}
                      </td>
                      <td className={bodyCellStyle}>
                        {checkAndReplaceStringWithHyphen(row?.emergency_contact)}
                      </td>
                      {/* <td className={bodyCellStyle}>
                        {checkAndReplaceStringWithHyphen(row?.size_tshirt.toUpperCase())}
                      </td>
                      <td className={bodyCellStyle}>{getStyledText(row?.car_pool)}</td> */}
                      <td className={bodyCellStyle}>
                        {checkAndReplaceStringWithHyphen(row?.plate_no)}
                      </td>
                      {/* <td className={bodyCellStyle}>
                        {checkAndReplaceStringWithHyphen(row?.room_info)}
                      </td> */}
                      <td className={bodyCellStyle}>
                        {checkAndReplaceStringWithHyphen(row?.team_info)}
                      </td>
                      <td className={bodyCellStyle}>
                        {checkAndReplaceStringWithHyphen(
                          row?.attendance_time !== 0
                            ? moment.unix(row?.attendance_time).format('DD/MM/YYYY, hh:mm A')
                            : '-'
                        )}
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
          <TablePaginationCustom
            count={filteredTableData.length}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={(e, p) => setPage(p)}
          />
        </div>
      </div>
      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-fa text-center text-white">New Attendee</DialogTitle>
        <DialogContent>
          <div className="my-8 flex min-h-[400px] w-full flex-col gap-4 p-2 md:w-[400px]">
            <SelectInput
              placeholder="Add Group"
              options={groupsData?.map((o) => o?.name)}
              onChange={(event) =>
                setNewAttendeeList((prev) => [
                  ...prev,
                  ...groupsData
                    ?.find((o) => o?.name === event?.target?.value)
                    ?.staff_id_list?.map((o) => allStaffs?.find((i) => i?.staff_id === o)?.name),
                ])
              }
            />
            <p className="text-center">or</p>
            <AutoCompleteTextInput
              placeholder="Type a Name"
              options={allStaffs?.map((o) => o?.name)}
              onClick={(event) => setNewAttendeeList((prev) => [...prev, event?.target?.value])}
              showRedAsteric={false}
            />
            <Divider />
            {newAttendeeList?.map((o, i) => (
              <div key={i} className="flex items-center justify-between">
                <p>{o}</p>
                <button
                  type="button"
                  onClick={() =>
                    setNewAttendeeList((prev) => {
                      const temp = [...prev];
                      temp.splice(i, 1);
                      return temp;
                    })
                  }
                >
                  <Delete className="w-[15px]" />
                </button>
              </div>
            ))}
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <button type="button" onClick={() => setDialogOpen(false)} className="p-2">
              Cancel
            </button>
            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => handleDialogClose()}
                className="bg-fa rounded-[4px] p-2 font-semibold text-white"
              >
                Add
              </button>
            </div>
          </div>
        </DialogActions>
      </Dialog>

      <Dialog open={dialog2Open} onClose={() => handleDialog2Close(false)}>
        <DialogTitle className="bg-fa text-center text-white">New Event</DialogTitle>
        <DialogContent>
          <div className="my-8 flex w-full flex-col gap-4 p-2 md:w-[400px]">
            {user?.isSuperAdmin && (
              <TextInput
                name="staff_id"
                value={dialog2Data?.staff_id}
                placeholder="Staff ID"
                onChange={handleDialog2DataChange}
              />
            )}

            <SelectInput
              name="sex"
              value={dialog2Data?.sex}
              placeholder="Gender"
              options={['male', 'female']}
              onChange={handleDialog2DataChange}
              showRedAsteric={false}
            />
            <SelectInput
              name="rsvp"
              value={dialog2Data?.rsvp}
              placeholder="RSVP Status"
              defaultLabel="-"
              options={['pending', 'yes', 'no']}
              onChange={handleDialog2DataChange}
            />
            <TextAreaInput
              name="remarks_rsvp"
              value={dialog2Data?.remarks_rsvp}
              placeholder="RSVP Remark"
              onChange={handleDialog2DataChange}
              showRedAsteric={false}
            />
            <BinarySwitchInput
              name="healthy"
              value={dialog2Data?.healthy}
              placeholder="Healthy"
              onChange={handleDialog2DataChange}
              showRedAsteric={false}
            />
            <TextAreaInput
              name="remarks_health"
              value={dialog2Data?.remarks_health}
              placeholder="Health Remark"
              onChange={handleDialog2DataChange}
              showRedAsteric={false}
            />
            <TextInput
              name="emergency_contact"
              value={dialog2Data?.emergency_contact}
              placeholder="Emergency Contact"
              onChange={handleDialog2DataChange}
              showRedAsteric={false}
            />
            <SelectInput
              name="size_tshirt"
              value={dialog2Data?.size_tshirt}
              placeholder="T-Shirt Size"
              options={['3xs', '2xs', 'xs', 's', 'm', 'l', 'xl', '2xl', '3xl', '4xl', '5xl']}
              onChange={handleDialog2DataChange}
              showRedAsteric={false}
            />
            <BinarySwitchInput
              name="car_pool"
              value={dialog2Data?.car_pool}
              placeholder="Will Carpool"
              onChange={handleDialog2DataChange}
              showRedAsteric={false}
            />
            <TextInput
              name="plate_no"
              value={dialog2Data?.plate_no}
              placeholder="Plate No."
              onChange={handleDialog2DataChange}
              showRedAsteric={false}
            />
            <TextInput
              name="room_info"
              value={dialog2Data?.room_info}
              placeholder="Room Info"
              onChange={handleDialog2DataChange}
              showRedAsteric={false}
            />
            <TextInput
              name="team_info"
              value={dialog2Data?.team_info}
              placeholder="Team Info"
              onChange={handleDialog2DataChange}
              showRedAsteric={false}
            />

            {isAdmin && (
              <BinarySwitchInput
                name="attendance_time"
                value={dialog2Data?.attendance_time}
                placeholder="Attendance"
                onChange={(event) => {
                  const { name, value } = event.target;
                  setDialog2Data((prevValues) => ({
                    ...prevValues,
                    [name]: value ? moment()?.unix() : 0,
                  }));
                }}
              />
            )}
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <div className="flex gap-2">
              <button type="button" onClick={() => handleDialog2Close(false)} className="p-2">
                Cancel
              </button>
              <WarnBeforeActionPopupButton onApprove={() => handleDialog2Close('delete')} />
            </div>
            <button
              type="button"
              onClick={() => handleDialog2Close('put')}
              className="bg-fa  cta-btn"
            >
              Edit
            </button>
          </div>
        </DialogActions>
      </Dialog>

      <Dialog open={dialog3Open} onClose={() => setDialog3Open(false)}>
        <DialogTitle className="bg-fa text-center text-white">Scan QR</DialogTitle>
        <DialogContent>
          <div className="my-8 flex min-h-[400px] w-full flex-col gap-4 p-2 md:w-[400px]">
            <QRBox
              key={expiryUnixTimestamp}
              value={getLink()}
              size={Number(`${!isSmallScreen ? 300 : 225}`)}
              showDownloadButton={!eventData?.qr_is_dynamic}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-end gap-4">
            <div className="flex gap-2">
              <button type="button" onClick={() => setDialog3Open(false)} className="bg-fa cta-btn">
                Close
              </button>
            </div>
          </div>
        </DialogActions>
      </Dialog>

      <Dialog open={dialog4Open} onClose={() => setDialog4Open(false)}>
        <DialogContent>
          <div className="my-8 flex w-full flex-col gap-4 p-2 md:w-[500px]">
            <div className="m-auto w-full bg-white p-1">
              <AttachmentBox
                key={eventData?.id}
                GET_ALL_FILES_ENDPOINT={`${EVENT_ENDPOINT}/files/event/${eventData?.id}`}
                UPLOAD_CERTAIN_FILE_ENDPOINT={`${EVENT_ENDPOINT}/files/upload/event/${eventData?.id}`}
                DOWNLOAD_CERTAIN_FILE_ENDPOINT={`${EVENT_ENDPOINT}/files/download`}
                DELETE_CERTAIN_FILE_ENDPOINT={`${EVENT_ENDPOINT}/files`}
                DISABLE_UPLOAD={!isAdmin}
              />
            </div>
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-end gap-4">
            <div className="flex gap-2">
              <button type="button" onClick={() => setDialog4Open(false)} className="bg-fa cta-btn">
                Close
              </button>
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
}
