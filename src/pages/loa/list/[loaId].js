// Next, React, Tw
import { useRouter } from 'next/router';
import { useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';

// Mui
import { Divider, Tooltip } from '@mui/material';

// Packages
import moment from 'moment';

// Components
import Layout from '../../../layouts/module/loa';
import ApprovalSelectInput from '../../../components/loa/ApprovalSelectInput';
import ApprovalStepper from '../../../components/Shared/Approval/ApprovalStepper';
import RemarkBox from '../../../components/Shared/RemarkBox';
import AttachmentBox from '../../../components/Shared/AttachmentBox';
import HistoryBox from '../../../components/Shared/HistoryBox';
import ApproveRejectButtonGroup from '../../../components/Shared/Approval/ApproveRejectButtonGroup';
import LoaActionSection from '../../../components/loa/LoaActionSection';
import { TextInput, SelectInput, TextAreaInput } from '../../../components/Shared/CustomInput';
import FetchUsdToMyrExchangeRateButton from '../../../components/Shared/FetchUsdToMyrExchangeRateButton';
import ScreenshotDialog from '../../../components/Shared/ScreenshotDialog';

// Others
import { LOA_ENDPOINT, loaIsEditable, useLoaContext } from '../../../utils/loa';
import axios from '../../../utils/axios';
import { setLoaData, setLoaTcvData } from '../../../utils/store/loaReducer';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { useParamContext } from '../../../utils/auth/ParamProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { loaData, loaTcvData } = useSelector((state) => state.loa);
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { loaId } = query;
  const hiddenDivRef = useRef(null);
  const { setParam } = useParamContext();

  const { handleCreateHistory } = useLoaContext();

  // Form
  const handleformDataChange = (event, nameInput, valueInput) => {
    let name;
    let value;
    if (nameInput === undefined && valueInput === undefined) {
      // eslint-disable-next-line
      name = event.target.name;
      // eslint-disable-next-line
      value = event.target.value;
    } else {
      name = nameInput;
      value = valueInput;
    }
    dispatch(setLoaData({ ...loaData, [name]: value }));
  };
  const handleTcvFormDataChange = (event, nameInput, valueInput) => {
    if (!nameInput && !valueInput) {
      const { name, value } = event.target;
      dispatch(setLoaTcvData({ ...loaTcvData, [name]: value }));
      return;
    }
    const name = nameInput;
    const value = valueInput;

    let tcvInRm;
    if (name === 'tcv') {
      tcvInRm = value * loaTcvData.currency_exchange;
    } else {
      tcvInRm = loaTcvData.tcv * loaTcvData.currency_exchange;
    }
    dispatch(
      setLoaTcvData({ ...loaTcvData, [name]: value, tcv_in_rm: String(tcvInRm.toFixed(2)) })
    );
  };

  // Others

  const handleRevertProject = async () => {
    dispatch(setIsLoading(true));
    try {
      await axios.put(`${LOA_ENDPOINT}/loa/${loaId}`, {
        ...loaData,
        status: 'draft',
      });
    } catch {
      /* empty */
    }
    dispatch(setIsLoading(false));
  };

  const handleSubmitApproveProject = async (status) => {
    dispatch(setIsLoading(true));
    try {
      await axios.put(`${LOA_ENDPOINT}/loa/${loaId}`, {
        ...loaData,
        status,
      });
    } catch {
      /* empty */
    }
    dispatch(setIsLoading(false));
  };

  const dataPart = (
    <>
      <div className="flex w-full flex-col gap-3 p-1 md:w-1/2">
        <TextInput
          name="supplier"
          value={loaData?.supplier}
          placeholder="Supplier (International)"
          onChange={handleformDataChange}
          disabled={!loaIsEditable(loaData?.status)}
        />
        <TextInput
          name="customer"
          value={loaData?.customer}
          placeholder="Customer (TM Global)"
          onChange={handleformDataChange}
          disabled={!loaIsEditable(loaData?.status)}
        />
        <TextAreaInput
          name="service_description"
          value={loaData?.service_description}
          placeholder="Service Description"
          onChange={handleformDataChange}
          disabled={!loaIsEditable(loaData?.status)}
        />
        <TextAreaInput
          name="justification"
          value={loaData?.justification}
          placeholder="Justification"
          onChange={handleformDataChange}
          disabled={!loaIsEditable(loaData?.status)}
        />
        <SelectInput
          name="sourcing_method"
          value={loaData?.sourcing_method}
          placeholder="Sourcing Method"
          options={[
            'Customer Purchase via Sole/Single Sourcing',
            'Customer Purchase via Quotation',
            'Reciprocal Business',
            'Internal Business Arrangement',
          ]}
          onChange={handleformDataChange}
          disabled={!loaIsEditable(loaData?.status)}
        />
        <SelectInput
          name="type_approval"
          value={loaData?.type_approval}
          placeholder="Approval Type"
          options={['OCM', 'BOC', 'PER', 'JPP1', 'JPP2', 'BTC', 'TM BOD']}
          onChange={handleformDataChange}
          disabled={!loaIsEditable(loaData?.status)}
        />
        <TextAreaInput
          name="approval"
          value={loaData?.approval}
          placeholder="Approval"
          onChange={handleformDataChange}
          disabled={!loaIsEditable(loaData?.status)}
        />
        <TextInput
          name="margin"
          value={loaData?.margin}
          placeholder="Margin"
          onChange={handleformDataChange}
          disabled={!loaIsEditable(loaData?.status)}
        />
        <SelectInput
          name="loa"
          value={loaData?.loa}
          placeholder="LOA"
          options={['AGM', 'GM', 'VP', 'EVP', 'NIL']}
          onChange={handleformDataChange}
          disabled={!loaIsEditable(loaData?.status)}
        />
      </div>
      <div className="flex w-full flex-col gap-3 p-1 md:w-1/2">
        <div className="flex w-full flex-col gap-3 rounded-lg border border-gray-300 p-2">
          <div className="flex justify-center">
            <p className="text-sm">Total Contract Value (TCV)</p>
          </div>
          <SelectInput
            name="currency"
            value={loaTcvData?.currency}
            placeholder="Currency"
            options={['MYR', 'USD']}
            onChange={handleTcvFormDataChange}
            disabled={!loaIsEditable(loaData?.status)}
          />
          <TextInput
            name="otc"
            value={loaTcvData?.otc}
            placeholder={`OTC ${loaTcvData?.currency !== '' ? `(${loaTcvData?.currency})` : ''}`}
            onChange={handleTcvFormDataChange}
            disabled={!loaIsEditable(loaData?.status)}
          />
          <TextInput
            name="mrc"
            value={loaTcvData?.mrc}
            placeholder={`MRC ${loaTcvData?.currency !== '' ? `(${loaTcvData?.currency})` : ''}`}
            onChange={handleTcvFormDataChange}
            disabled={!loaIsEditable(loaData?.status)}
          />
          <TextInput
            name="om"
            value={loaTcvData?.om}
            placeholder={`O&M ${loaTcvData?.currency !== '' ? `(${loaTcvData?.currency})` : ''}`}
            onChange={handleTcvFormDataChange}
            disabled={!loaIsEditable(loaData?.status)}
          />
          <TextInput
            name="total_om"
            value={loaTcvData?.total_om}
            placeholder="Total O&M"
            onChange={handleTcvFormDataChange}
            disabled={!loaIsEditable(loaData?.status)}
          />
          <TextInput
            name="term"
            value={loaTcvData?.term}
            placeholder="TCV Term (Month)"
            onChange={handleTcvFormDataChange}
            disabled={!loaIsEditable(loaData?.status)}
          />
          <TextInput
            name="tcv"
            value={loaTcvData?.tcv}
            placeholder="TCV (USD)"
            onChange={handleTcvFormDataChange}
            disabled={!loaIsEditable(loaData?.status)}
          />

          <div className="flex w-full justify-between">
            <Tooltip title={`As of ${loaTcvData?.currency_exchange_date}`} placement="bottom-start">
              <TextInput
                name="currency_exchange"
                value={loaTcvData?.currency_exchange}
                placeholder="Currency Exchange"
                onChange={handleTcvFormDataChange}
                disabled
              />
            </Tooltip>

            {loaIsEditable(loaData?.status) && (
              <FetchUsdToMyrExchangeRateButton
                disabled={loaTcvData?.currency !== 'USD'}
                onChange={(data) => {
                  dispatch(
                    setLoaTcvData({
                      ...loaTcvData,
                      currency_exchange: String(data),
                      currency_exchange_date: moment().format('YYYY / MM / DD, hh:mm A'),
                      tcv_in_rm: String(loaTcvData.tcv * data),
                    })
                  );
                }}
              />
            )}
          </div>
          <TextInput
            name="tcv_in_rm"
            value={loaTcvData?.tcv_in_rm}
            placeholder="TCV (RM)"
            onChange={handleTcvFormDataChange}
            disabled={!loaIsEditable(loaData?.status)}
          />
        </div>
        {loaIsEditable(loaData?.status) && (
          <div className="flex w-full flex-col gap-3 rounded-lg border border-gray-300 p-2">
            <div className="flex justify-center">
              <p className="text-sm">Escalation</p>
            </div>
            <ApprovalSelectInput />
          </div>
        )}
      </div>
    </>
  );

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${LOA_ENDPOINT}/id/${loaId}`);
      if (response.data.status === 'success') {
        dispatch(setLoaData(response.data.data[0]));
        dispatch(setLoaTcvData(response.data.data[0]?.total_contract_value));
      } else {
        dispatch(setLoaData({}));
        dispatch(setLoaTcvData({}));
      }
    } catch {
      dispatch(setLoaData({}));
      dispatch(setLoaTcvData({}));
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [loaId]);

  return (
    <>
      <div ref={hiddenDivRef} />
      <div className="p-1 md:p-4">
        <div className="container flex flex-col gap-4">
          <div className="flex w-full justify-end">
            <button
              type="button"
              className="cta-btn bg-loa"
              onClick={() => setParam({ screenshotDialogOpen: true })}
            >
              Export PDF
            </button>
          </div>
          <div className="flex w-full flex-col gap-8 rounded-md bg-white p-4 shadow-lg">
            <div className="flex items-center justify-between">
              <div className="flex flex-col">
                <p className="text-lg font-bold">{loaData?.loa_id_name}</p>
              </div>
              <ApproveRejectButtonGroup
                NAME={loaData?.name}
                GET_ALL_APPROVALS_ENDPOINT={`${LOA_ENDPOINT}/approvals/loa_id/${loaId}`}
                UPDATE_CERTAIN_APPROVAL_ENDPOINT={`${LOA_ENDPOINT}/approval`}
                REVERT_CALLBACK={async () => {
                  await handleRevertProject();
                  await handleCreateHistory(loaId, 'Project was reverted.');
                  if (window) window.location.reload();
                }}
                SUBMIT_CALLBACK={async (status) => {
                  await handleSubmitApproveProject(status);
                  await handleCreateHistory(loaId, 'Project was submitted for approval.');
                  if (window) window.location.reload();
                }}
                APPROVE_CALLBACK={async (status) => {
                  await handleSubmitApproveProject(status);
                  await handleCreateHistory(loaId, 'Project was approved.');
                  if (window) window.location.reload();
                }}
                documentName={`${loaData?.name}`}
              />
            </div>
            <Divider />

            <ApprovalStepper
              GET_ALL_APPROVALS_ENDPOINT={`${LOA_ENDPOINT}/approvals/loa_id/${loaId}`}
            />

            <div className="flex w-full flex-col gap-1 md:flex-row">{dataPart}</div>

            <div className="flex w-full gap-1  p-1 ">
              <div className="w-1/3">
                <AttachmentBox
                  key={loaId}
                  GET_ALL_FILES_ENDPOINT={`${LOA_ENDPOINT}/files/loa/${loaId}`}
                  UPLOAD_CERTAIN_FILE_ENDPOINT={`${LOA_ENDPOINT}/files/upload/loa/${loaId}`}
                  DOWNLOAD_CERTAIN_FILE_ENDPOINT={`${LOA_ENDPOINT}/files/download`}
                  DELETE_CERTAIN_FILE_ENDPOINT={`${LOA_ENDPOINT}/files`}
                />
              </div>
              <div className="w-1/3">
                <HistoryBox
                  key={loaId}
                  parentHiddenDivRef={hiddenDivRef}
                  GET_ALL_HISTORIES_ENDPOINT={`${LOA_ENDPOINT}/historys/loa_id/${loaId}`}
                />
              </div>
              <div className="w-1/3">
                <RemarkBox
                  key={loaId}
                  parentHiddenDivRef={hiddenDivRef}
                  documentIdKey="loa_id"
                  documentIdValue={loaId}
                  GET_ALL_REMARKS_ENDPOINT={`${LOA_ENDPOINT}/remarks/loa_id/${loaId}`}
                  POST_NEW_REMARK_ENDPOINT={`${LOA_ENDPOINT}/remarks`}
                />
              </div>
            </div>
            <LoaActionSection fetchData={fetchData} />
          </div>
        </div>
      </div>
      <ScreenshotDialog fileName={`${loaData?.loa_id_name}.pdf`} isLandscape>
        <div className="flex w-full flex-col gap-4 p-4">
          <div className="bg-loa w-full text-center text-white">
            {loaData?.loa_id_name?.toUpperCase()}
          </div>
          <div className="flex w-full flex-row gap-1">{dataPart}</div>
          <div className="bg-loa w-full text-center text-white">REMARKS and HISTORIES</div>
          <div className="flex w-full gap-1  p-1 ">
            <div className="w-1/2">
              <HistoryBox
                key={loaId}
                parentHiddenDivRef={hiddenDivRef}
                GET_ALL_HISTORIES_ENDPOINT={`${LOA_ENDPOINT}/historys/loa_id/${loaId}`}
                isExporting
              />
            </div>
            <div className="w-1/2">
              <RemarkBox
                key={loaId}
                parentHiddenDivRef={hiddenDivRef}
                documentIdKey="loa_id"
                documentIdValue={loaId}
                GET_ALL_REMARKS_ENDPOINT={`${LOA_ENDPOINT}/remarks/loa_id/${loaId}`}
                POST_NEW_REMARK_ENDPOINT={`${LOA_ENDPOINT}/remarks`}
                isExporting
              />
            </div>
          </div>
        </div>
      </ScreenshotDialog>
    </>
  );
}
