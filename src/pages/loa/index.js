// Next, React, Tw
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Mui
import { Article, RateReview, DirectionsWalk, Snowshoeing } from '@mui/icons-material';

// Components
import Layout from '../../layouts/module/loa';
import LoaTable from '../../components/loa/LoaTable';
import PendingLoaTable from '../../components/loa/PendingLoaTable';
import ReactAnimatedNumber from '../../components/Shared/ReactAnimatedNumber';

// Others
import axios from '../../utils/axios';
import { LOA_ENDPOINT } from '../../utils/loa';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const dispatch = useDispatch();
  const { isAdmin } = useModuleRoleContext();

  const [cardsData, setCardsData] = useState([]);

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${LOA_ENDPOINT}/approval/summary/keyword`);
      if (response?.data?.data) {
        setCardsData([
          {
            title: 'LOA Is Empty',
            number: response.data.data?.total_empty,
            icon: <Snowshoeing className="h-[40px] w-[40px] text-[#D6AE01]" />,
          },
          {
            title: 'LOA In Draft',
            number: response.data.data?.total_draft,
            icon: <DirectionsWalk className="h-[40px] w-[40px] text-[#20214F]" />,
          },
          {
            title: 'LOA Pending Approval',
            number: response.data.data?.total_pending,
            icon: <RateReview className="h-[40px] w-[40px] text-[#7FB5B5]" />,
          },
          {
            title: 'LOA Approved',
            number: response.data.data?.total_approved,
            icon: <Article className="h-[40px] w-[40px] text-[#A12312]" />,
          },
        ]);
      }
    } catch {
      setCardsData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="p-4">
      <div className="container flex flex-col gap-4">
        {isAdmin && (
          <div className="flex w-full flex-col gap-4 md:flex-row">
            {cardsData.map((card, index) => (
              <div
                key={index}
                className="flex w-full flex-col gap-2 rounded-[4px] bg-white p-2 shadow-md md:w-1/4"
              >
                <div>
                  <p className="text-sm font-bold">{card.title}</p>
                </div>
                <div className="flex justify-between gap-2 text-3xl font-bold">
                  <ReactAnimatedNumber
                    value={card.number}
                    formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                  />
                  {card.icon}
                </div>
              </div>
            ))}
          </div>
        )}
        <div className="flex w-full flex-col gap-4 rounded-[4px] bg-white p-4 shadow-lg">
          <PendingLoaTable />
        </div>
        {isAdmin && (
          <div className="flex w-full flex-col gap-4 rounded-[4px] bg-white p-4 shadow-lg">
            <LoaTable />
          </div>
        )}
      </div>
    </div>
  );
}
