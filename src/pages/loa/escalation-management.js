// Next, React, Tw
import { twMerge } from 'tailwind-merge';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Dialog, DialogTitle, DialogActions, Stepper, Step, Divider } from '@mui/material';
import { Edit } from '@mui/icons-material';

// Components
import Layout from '../../layouts/module/loa';
import { TablePaginationCustom } from '../../components/Shared/table';
import NewEscalationManagementRow from '../../components/loa/NewEscalationManagementRow';
import ApprovalStep from '../../components/loa/ApprovalStep';
import { SearchInput } from '../../components/Shared/CustomInput';

// Others
import { LOA_ENDPOINT } from '../../utils/loa';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useSnackbar } from '../../components/Shared/snackbar';
import { getColorCode, getModuleFromPath } from '../../utils/shared';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const { query, asPath } = useRouter();
  const module = getModuleFromPath(asPath);
  const { q } = query;

  const ESCALATION_LIST = [
    {
      approval_type: '',
      user_name: '',
    },
  ];
  const [escalationList, setEscalationList] = useState(ESCALATION_LIST);

  // Table
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(10);
  const [tableData, setTableData] = useState([]);
  const onChangePage = (event, newPage) => {
    setPage(newPage);
  };
  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  })();

  const bodyCellStyle = 'text-center whitespace-nowrap py-1 px-4 text-xs';

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const [dialogData, setDialogData] = useState({});
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
      setEscalationList(data?.escalation_user);
    }
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  const handleDialogClose = async (action) => {
    if (action) {
      for (let i = 0; i < escalationList?.length; i += 1) {
        if (Object.values(escalationList[i]).includes('')) {
          enqueueSnackbar('Please fill in all field.', {
            variant: 'error',
          });
          return;
        }
      }
      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${LOA_ENDPOINT}/escalation`, {
              escalation_user: escalationList,
            });
            break;
          case 'put':
            response = await axios.put(`${LOA_ENDPOINT}/escalation/${dialogData.id}`, {
              ...dialogData,
              escalation_user: escalationList,
            });
            break;
          case 'delete':
            response = await axios.delete(`${LOA_ENDPOINT}/escalation/${dialogData.id}`);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        /* empty */
      }
    }

    setDialogData({});
    setEscalationList(ESCALATION_LIST);
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${LOA_ENDPOINT}/escalation/all/keyword`);
      if (response?.data?.data) {
        response.data.data.reverse();
        setTableData(response.data.data);
      }
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div className=" p-4">
        <div className="container flex flex-col gap-4">
          <div className="flex w-full flex-col gap-4 rounded-xl bg-white p-4 shadow-lg">
            <div className="flex flex-col items-center justify-between gap-2 md:flex-row">
              <div className="flex flex-col">
                <p className="text-sm font-bold">All</p>
                <p className="text-xs font-semibold">Configure escalation flow on LOA approval.</p>
              </div>
              <div className="flex gap-2">
                <SearchInput />
                <button
                  type="button"
                  className="cta-btn bg-loa"
                  onClick={() => handleClickOpenDialog(false)}
                >
                  New Flow
                </button>
              </div>
            </div>
            <Divider />
            <div className="overflow-x-auto scrollbar">
              <table className="min-w-full">
                <thead>
                  <tr>
                    <td className={`${bodyCellStyle} bg-loa text-sm font-semibold text-white`}>
                      No.
                    </td>
                    <td className={`${bodyCellStyle} bg-loa text-sm font-semibold text-white`}>
                      Escalation ID
                    </td>
                    <td className={`${bodyCellStyle} bg-loa text-sm font-semibold text-white`}>
                      Escalation
                    </td>
                    <td className={`${bodyCellStyle} bg-loa text-sm font-semibold text-white`} />
                  </tr>
                </thead>
                <tbody>
                  {filteredTableData
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((row, i) => (
                      <tr className={`${i % 2 === 0 ? 'bg-[#f8f8f8]' : 'bg-white'}`}>
                        <td className={bodyCellStyle}>{i + 1 + rowsPerPage * page}</td>
                        <td className={bodyCellStyle}>{row?.escalation_type}</td>
                        <td className={twMerge(bodyCellStyle, 'w-full p-2')}>
                          <Stepper activeStep={-1} alternativeLabel>
                            {row?.escalation_user.map((approval, j) => (
                              <Step
                                key={j}
                                sx={{
                                  '& .Mui-disabled .MuiStepIcon-root': {
                                    color: getColorCode(module),
                                  },
                                }}
                              >
                                <ApprovalStep approval={approval} />
                              </Step>
                            ))}
                          </Stepper>
                        </td>
                        <td className={bodyCellStyle}>
                          <button type="button" onClick={() => handleClickOpenDialog(true, row)}>
                            <Edit className="text-loa" />
                          </button>
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
            <TablePaginationCustom
              count={tableData.length}
              page={page}
              rowsPerPage={rowsPerPage}
              onPageChange={onChangePage}
            />
          </div>
        </div>
      </div>

      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-loa text-white">
          <p className="text-center">
            {!editModeDialog && 'Add New Escalation'}
            {editModeDialog && 'Edit Escalation'}
          </p>
        </DialogTitle>
        <div className="flex min-h-[300px] flex-col gap-2 overflow-y-hidden px-2 py-4">
          <NewEscalationManagementRow
            escalationList={escalationList}
            setEscalationList={setEscalationList}
          />
        </div>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
              Cancel
            </button>
            <div className="flex gap-2">
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('delete')}
                  className="cta-btn bg-red-500"
                >
                  Delete
                </button>
              )}
              {!editModeDialog && escalationList?.length !== 0 && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-loa cta-btn"
                >
                  Save
                </button>
              )}
              {editModeDialog && escalationList?.length !== 0 && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-loa cta-btn"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
}
