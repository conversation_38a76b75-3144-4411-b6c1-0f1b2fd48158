// Next, React, Tw
import { Fragment, useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';
import { twMerge } from 'tailwind-merge';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';

// Packages
import moment from 'moment';

// Components
import Layout from '../../layouts/module/oasys';
import AllTasks from '../../components/oasys/AllTasks';
import { SelectInput, TextAreaInput, DateInput } from '../../components/Shared/CustomInput';
import WarnBeforeActionPopupButton from '../../components/Shared/WarnBeforeActionPopupButton';

// Others
import { useOasysContext, OASYS_ENDPOINT, getJobStatusStyle } from '../../utils/oasys';
import { useSnackbar } from '../../components/Shared/snackbar';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { setOriginalJobData } from '../../utils/store/oasysReducer';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { useSimiContext } from '../../utils/simi';
import { useAuthContext } from '../../utils/auth/useAuthContext';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const { jobScema, handleCreateHistory } = useOasysContext();
  const { replace, query, asPath } = useRouter();
  const { jobId } = query;
  const { userSubModules } = useSelector((state) => state.aum);
  const { originalJobData, role } = useSelector((state) => state.oasys);
  const { isAdmin } = useModuleRoleContext();
  const { handleSendEmail, getCertainStaffInfoFromStaffId } = useSimiContext();
  const { user } = useAuthContext();

  const [allProjects, setAllProjects] = useState([]);
  const [jobData, setJobData] = useState({});

  // Form
  const handleFormDataChange = (event) => {
    const { name, value } = event.target;
    setJobData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleFormSubmit = async (action) => {
    let payload;

    if (action === 'put') {
      try {
        payload = await jobScema.validate(jobData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }
    }

    dispatch(setIsLoading(true));
    try {
      switch (action) {
        case 'put':
          await axios.put(`${OASYS_ENDPOINT}/${payload?.id}`, payload);
          break;
        case 'delete':
          await axios.delete(`${OASYS_ENDPOINT}/${jobData?.id}`);
          break;
        default:
          break;
      }
      enqueueSnackbar('Success', {
        variant: 'success',
      });
      switch (action) {
        case 'put':
          if (originalJobData?.status !== jobData?.status) {
            await handleCreateHistory(
              jobData?.id,
              `Status changed to ${jobData?.status?.toUpperCase()}`
            );
            if (window) window?.location?.reload();
          }
          break;
        case 'delete':
          replace('/oasys');
          break;
        default:
          break;
      }
    } catch {
      enqueueSnackbar('Error', {
        variant: 'error',
      });
    }
    dispatch(setIsLoading(false));
    fetchData();
  };

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleDialogClose = async (action) => {
    if (action) {
      if (dialogData?.status === 'open')
        setJobData((prev) => ({
          ...prev,
          status: dialogData?.status,
          status_remarks: '',
          on_hold_end_date: 0,
        }));
      else
        setJobData((prev) => ({
          ...prev,
          status: dialogData?.status,
          status_remarks: dialogData?.status_remarks,
          on_hold_end_date: dialogData?.on_hold_end_date,
        }));
      setDialogData((prev) => ({}));
    }
    if (dialogData?.status === 'rejected') {
      await handleSendEmail(
        [getCertainStaffInfoFromStaffId(originalJobData?.created_by_staff_id, 'email')],
        [user?.email],
        `${originalJobData?.job_id}`,
        `                    
          <p>${originalJobData?.job_id} has been rejected.</p>\n
          <p>Kindly, please click this <a href="${process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT}${asPath}">link</a> to review it.</p>
                `
      );
    }
    setDialogOpen(false);
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${OASYS_ENDPOINT}/job_id/${jobId}`);
      const temp = response?.data?.data?.[0] || { data: [] };
      temp.data = undefined;
      dispatch(setOriginalJobData(temp));
      setJobData(temp);
    } catch {
      dispatch(setOriginalJobData(null));
      setJobData(null);
    }
    dispatch(setIsLoading(false));
  };

  const fetchAllProjects = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${OASYS_ENDPOINT}/projects/all/keyword`);
      const temp = response?.data?.data || [];
      setAllProjects(temp);
    } catch {
      setAllProjects([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchAllProjects();
  }, []);

  useEffect(() => {
    if (!jobId) return;
    fetchData();
  }, [jobId]);

  useEffect(() => {
    if (!jobData?.id) return;
    if (JSON?.stringify(jobData) !== JSON?.stringify(originalJobData)) handleFormSubmit('put');
  }, [JSON?.stringify(jobData)]);

  return (
    <>
      <div className="bg-oasys px-4 py-2 text-white">
        <div className="flex flex-col items-center justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-extrabold">{originalJobData?.job_id}</p>
            <p className="text-xs">
              Created by :{' '}
              {getCertainStaffInfoFromStaffId(originalJobData?.created_by_staff_id, 'name')}
            </p>
            <p className="text-xs">
              Last Modified at :{' '}
              {moment?.unix(originalJobData?.modified_at)?.format('YYYY-MM-DD hh:mm A')}
            </p>
          </div>
          <p
            className={twMerge(
              getJobStatusStyle(originalJobData?.status),
              'rounded-md px-1 shadow-dashboardCardDark'
            )}
          >
            {originalJobData?.status?.toUpperCase()}
          </p>
        </div>
      </div>
      <div className="container mx-auto flex flex-col gap-2 p-1 dark:bg-gray-600 dark:text-white md:p-4">
        <div className="my-8 flex flex-col justify-between gap-2 md:flex-row">
          <div className="flex w-full items-center justify-between">
            <div className="flex flex-col gap-2">
              {['executive']?.includes(role) && (
                <div className="w-full md:w-[450px]">
                  <SelectInput
                    name="project_id"
                    value={jobData?.project_id}
                    placeholder="Project"
                    defaultLabel="-"
                    options={[
                      { label: '-', value: '000000000000000000000000' },
                      ...allProjects?.map((o) => ({ label: o?.name, value: o?.id })),
                    ]}
                    onChange={handleFormDataChange}
                    showRedAsteric={false}
                    disabled={!isAdmin && !userSubModules?.includes('executive')}
                  />
                </div>
              )}
              {jobData?.status_remarks !== '' && (
                <div className="w-full md:w-[450px]">
                  <TextAreaInput
                    name="status_remarks"
                    value={jobData?.status_remarks}
                    placeholder="Status Remarks"
                    onChange={handleFormDataChange}
                    showRedAsteric={false}
                    disabled
                  />
                </div>
              )}
            </div>
            {originalJobData?.status !== 'completed' && (
              <div className="flex items-center gap-2">
                {['executive']?.includes(role) && (
                  <>
                    {[
                      {
                        label: 'Put On Hold',
                        value: 'on hold',
                      },
                      {
                        label: 'Reject',
                        value: 'rejected',
                      },
                      {
                        label: 'Re-Open',
                        value: 'open',
                      },
                    ]?.map((o, i) =>
                      originalJobData?.status !== o?.value ? (
                        <button
                          type="button"
                          key={i}
                          className={twMerge(getJobStatusStyle(o?.value), 'cta-btn')}
                          onClick={() => {
                            setDialogData((prev) => ({ ...prev, status: o?.value }));
                            setDialogOpen(true);
                          }}
                        >
                          {o?.label}
                        </button>
                      ) : (
                        ''
                      )
                    )}
                  </>
                )}
                <WarnBeforeActionPopupButton
                  onCancel={() => {}}
                  onApprove={() => handleFormSubmit('delete')}
                />
              </div>
            )}
          </div>
        </div>
      </div>
      <div className="container mx-auto p-1 md:p-4">
        <AllTasks endpoint={`job_id/${jobId}`} />
      </div>
      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-oasys text-center text-white">
          {dialogData?.status === 'on hold' && 'Put Job On Hold'}
          {dialogData?.status === 'rejected' && 'Reject Job'}
        </DialogTitle>
        <DialogContent>
          <div className="my-8 flex w-full flex-col gap-4 p-2 md:w-[400px]">
            {dialogData?.status === 'on hold' && (
              <DateInput
                views={['year', 'month', 'day']}
                name="on_hold_end_date"
                value={dialogData?.on_hold_end_date}
                placeholder="On Hold Until"
                returnedFormat="unix"
                onChange={handleDialogDataChange}
              />
            )}
            <TextAreaInput
              name="status_remarks"
              value={dialogData?.status_remarks}
              placeholder="Remarks"
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <div className="flex gap-2">
              <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
                Cancel
              </button>
            </div>

            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => handleDialogClose('put')}
                className="bg-oasys cta-btn"
              >
                Update
              </button>
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
}
