// Next, React, Tw
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';
import Link from 'next/link';

// Components
import Layout from '../../layouts/module/oasys';
import FilterStatusButtonsGroup from '../../components/oasys/FilterStatusButtonsGroup';
import SummaryCards from '../../components/oasys/SummaryCards';
import AllJobsTable from '../../components/oasys/AllJobsTable';

// Others
import { OASYS_ENDPOINT } from '../../utils/oasys';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { setJobsTableData, setRole } from '../../utils/store/oasysReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { status } = query;
  const { user } = useAuthContext();

  const { replaceParam } = useParamContext();

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${OASYS_ENDPOINT}/created_by_staff_id/${user?.staff_id}`);
      const temp = response?.data?.data || [];
      temp.reverse();
      dispatch(setJobsTableData(temp));
    } catch {
      dispatch(setJobsTableData([]));
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [user?.staff_id]);

  useEffect(() => {
    if (status) return;
    replaceParam({ status: 'all' });
  }, [status]);

  useEffect(() => {
    dispatch(setRole('sales'));
  }, []);

  return (
    <>
      <div className="bg-oasys px-4 py-2 text-white">
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-extrabold">Dashboard</p>
          </div>
        </div>
      </div>
      <SummaryCards type="job" />
      <div className="container mx-auto flex gap-2 p-1 md:p-4">
        <div className="flex-grow">
          {' '}
          <div className="flex items-center justify-between gap-2 p-1 dark:text-white md:p-4">
            <FilterStatusButtonsGroup type="job" />
            <Link href="/oasys/new" className="cta-btn bg-oasys whitespace-nowrap">
              New Job
            </Link>
          </div>
          <div className="p-1 md:p-4">
            <div className="flex flex-col gap-2 rounded-md bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
              <AllJobsTable />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
