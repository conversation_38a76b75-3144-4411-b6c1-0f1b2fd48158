// Next, React, Tw
import { useRouter } from 'next/router';

// Components
import Layout from '../../../layouts/module/oasys';
import { UpdateTask } from '../../../components/oasys/Task';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { query } = useRouter();
  const { taskId } = query;

  return (
    <>
      <div className="bg-oasys px-4 py-2 text-white">
        <div className="flex flex-col items-center justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-extrabold">My Tasks</p>
          </div>
        </div>
      </div>
      <div className="container mx-auto p-1 md:p-4">
        <div className="flex flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
          <UpdateTask key={taskId} taskId={taskId} disabled={false} />
        </div>
      </div>
    </>
  );
}
