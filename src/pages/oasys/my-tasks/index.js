// Components
import Layout from '../../../layouts/module/oasys';
import AllTasks from '../../../components/oasys/AllTasks';

// Others
import { useAuthContext } from '../../../utils/auth/useAuthContext';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { user } = useAuthContext();

  return (
    <>
      <div className="bg-oasys px-4 py-2 text-white">
        <div className="flex flex-col items-center justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-extrabold">My Tasks</p>
          </div>
        </div>
      </div>
      <div className="container mx-auto p-1 md:p-4">
        <AllTasks endpoint={`processor_staff_id/${user?.staff_id}`} />
      </div>
    </>
  );
}
