// Next, React, Tw
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import { twMerge } from 'tailwind-merge';

// Mui
import { Visibility } from '@mui/icons-material';

// Packages
import moment from 'moment-business-days';

// Components
import Layout from '../../layouts/module/oasys';
import { TablePaginationCustom } from '../../components/Shared/table';
import { SearchInput } from '../../components/Shared/CustomInput';
import SummaryCards from '../../components/oasys/SummaryCards';
import FilterStatusButtonsGroup from '../../components/oasys/FilterStatusButtonsGroup';
import ExportExcelButton from '../../components/Shared/ExportExcelButton';
import FilterTableDataByDateRange from '../../components/Shared/FilterTableDataByDateRange';

// Others
import {
  OASYS_ENDPOINT,
  getTaskStatusStyle,
  useOasysContext,
  TASKS_TABLE_HEADERS_FOR_EXPORT,
} from '../../utils/oasys';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { setTasksTableData, setRole } from '../../utils/store/oasysReducer';
import { checkAndReplaceStringWithHyphen } from '../../utils/shared';
import { useSimiContext } from '../../utils/simi';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const dispatch = useDispatch();
  const { query, push } = useRouter();
  const { status } = query;
  const { user } = useAuthContext();
  const { page, rowsPerPage } = useSelector((state) => state.simi);
  const { filteredTasksTableData } = useOasysContext();
  const { getCertainStaffInfoFromStaffId } = useSimiContext();
  const { replaceParam } = useParamContext();

  // Table
  const bodyCellStyle = 'text-center py-1 text-sm';

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(
        `${OASYS_ENDPOINT}/tasks/processor_staff_id/${user?.staff_id}`
      );
      let temp = response?.data?.data || [];
      temp = temp?.map((o) => ({
        ...o,
        aging: moment().startOf('day').diff(moment.unix(o?.created_at).startOf('day'), 'days'),
      }));
      temp.reverse();
      dispatch(setTasksTableData(temp));
    } catch {
      dispatch(setTasksTableData([]));
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [user?.staff_id]);

  useEffect(() => {
    if (status) return;
    replaceParam({ status: 'all' });
  }, [status]);

  useEffect(() => {
    dispatch(setRole('processor'));
  }, []);

  return (
    <>
      <div className="bg-oasys px-4 py-2 text-white">
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-extrabold">Dashboard</p>
          </div>
        </div>
      </div>
      <SummaryCards type="task" />
      <div className="container mx-auto flex justify-between gap-2 p-1 md:p-4">
        <FilterStatusButtonsGroup type="task" />
        <button
          type="button"
          className="cta-btn bg-oasys whitespace-nowrap"
          onClick={() => push('/oasys/my-tasks')}
        >
          Start Working On Pending Tasks
        </button>
      </div>
      <div className="container mx-auto flex gap-2 p-1 md:p-4">
        <div className="flex-grow">
          <div className="flex flex-col gap-2 rounded-md bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
            <div className="flex w-full justify-between">
              <div className="flex gap-2">
                <FilterTableDataByDateRange />
                <SearchInput />
              </div>
              <ExportExcelButton
                data={filteredTasksTableData?.map((o) => ({
                  ...o,
                  ...o?.data,
                }))}
                headers={TASKS_TABLE_HEADERS_FOR_EXPORT}
              />
            </div>
            <div className="flex w-full flex-col">
              <div className="overflow-x-auto scrollbar">
                <table className="min-w-full bg-white text-black">
                  <thead>
                    <tr>
                      {[
                        'No.',
                        'Job ID',
                        'Task ID',
                        'Customer',
                        'Quote',
                        'Site',
                        'Order No.',
                        'Service ID',
                        'Status',
                        'Aging',
                        'Processor',
                        '',
                      ].map((label, i) => (
                        <td
                          key={i}
                          className="bg-oasys whitespace-nowrap px-4 text-center text-white"
                        >
                          {label}
                        </td>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {filteredTasksTableData
                      ?.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      ?.map((row, i) => (
                        <tr key={i} className="odd:bg-white even:bg-[#f8f8f8] ">
                          <td className={bodyCellStyle}>{i + 1}</td>
                          <td className={bodyCellStyle}>
                            {checkAndReplaceStringWithHyphen(row?.job_id)}
                          </td>
                          <td className={bodyCellStyle}>
                            {checkAndReplaceStringWithHyphen(row?.task_id)}
                          </td>
                          <td className={bodyCellStyle}>
                            {checkAndReplaceStringWithHyphen(row?.data?.['Customer Name'])}
                          </td>
                          <td className={bodyCellStyle}>
                            {checkAndReplaceStringWithHyphen(row?.data?.['Quote Name'])}
                          </td>
                          <td className={bodyCellStyle}>
                            {checkAndReplaceStringWithHyphen(row?.data?.['Site ID/Site Name'])}
                          </td>
                          <td className={bodyCellStyle}>
                            {checkAndReplaceStringWithHyphen(row?.data?.order_no)}
                          </td>
                          <td className={bodyCellStyle}>
                            {checkAndReplaceStringWithHyphen(row?.data?.service_id)}
                          </td>
                          <td className={twMerge(bodyCellStyle, getTaskStatusStyle(row?.status))}>
                            {checkAndReplaceStringWithHyphen(row?.status?.toUpperCase())}
                          </td>
                          <td
                            className={twMerge(
                              bodyCellStyle,
                              ['blocked', 'completed']?.includes(row?.status) &&
                                row?.aging > 3 &&
                                'bg-red-500'
                            )}
                          >
                            {checkAndReplaceStringWithHyphen(
                              ['blocked', 'completed']?.includes(row?.status) ? '-' : row?.aging
                            )}
                          </td>
                          <td className={bodyCellStyle}>
                            {checkAndReplaceStringWithHyphen(
                              getCertainStaffInfoFromStaffId(row?.processor_staff_id, 'name')
                            )}
                          </td>
                          <td className={bodyCellStyle}>
                            <button
                              type="button"
                              onClick={() => push(`/oasys/my-tasks/${row?.id}`)}
                            >
                              <Visibility className="text-oasys" />
                            </button>
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
              <TablePaginationCustom count={filteredTasksTableData.length} />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
