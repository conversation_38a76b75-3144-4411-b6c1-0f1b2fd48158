// Next, React, Tw
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Packages
import * as R from 'ramda';

// Components
import Layout from '../../layouts/module/oasys';
import { TablePaginationCustom } from '../../components/Shared/table';
import RoleManagementCell from '../../components/oasys/RoleManagementCell';
import { SelectInput, SearchInput } from '../../components/Shared/CustomInput';

// Others
import axios from '../../utils/axios';
import { AUM_ENDPOINT } from '../../utils/aum';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useParamContext } from '../../utils/auth/ParamProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standards and Vars
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { q, division } = query;
  const { page, rowsPerPage } = useSelector((state) => state.simi);
  const { replaceParam, setParam } = useParamContext();

  // Table
  const [tableData, setTableData] = useState([]);

  const filteredTableData = (() => {
    let temp = tableData;
    if (division !== 'all') {
      temp = temp.filter((o) => o?.division === division);
    }
    if ([undefined, '']?.includes(q)) {
      return temp;
    }
    return temp.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  })();

  const headerCellStyle =
    'whitespace-nowrap bg-oasys px-4 text-center text-white text-sm font-semibold';

  // Others

  const divisionList = R.uniq(R.pluck('division', filteredTableData));

  const fetchData = async () => {
    dispatch(setIsLoading(true));

    try {
      const response = await axios.get(`${AUM_ENDPOINT}/user/v1/module/oasys/all`);
      const temp = response?.data?.data || [];

      temp.reverse();
      setTableData(temp);
    } catch {
      setTableData([]);
    }

    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (!division) {
      replaceParam({ division: 'all' });
      return;
    }
    fetchData();
  }, [division]);

  return (
    <div className="container mx-auto p-1 md:p-4">
      <div
        style={{ boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)' }}
        className="flex flex-col gap-4 rounded-xl bg-white p-4 dark:bg-gray-600 dark:text-white"
      >
        <p className="w-full  text-center text-3xl font-bold md:text-left">
          Product Access Management
        </p>
        <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
          <div className="flex flex-col items-center gap-2 md:flex-row">
            <div className="w-[200px]">
              <SelectInput
                value={division}
                placeholder="Filter by Division"
                options={['all', ...divisionList.filter((o, i) => o !== '')]}
                onChange={(event) => setParam({ division: event.target.value })}
              />
            </div>
          </div>

          <div className="flex items-center justify-center gap-4">
            <SearchInput />
          </div>
        </div>

        <div className="my-4 overflow-x-auto scrollbar">
          <table className="min-w-full bg-white text-black">
            <thead>
              <tr>
                <td rowSpan={2} className={headerCellStyle}>
                  No.
                </td>
                <td rowSpan={2} className={headerCellStyle}>
                  Name
                </td>
                <td rowSpan={2} className={headerCellStyle}>
                  Staff ID
                </td>
                <td colSpan={3} className={headerCellStyle}>
                  Roles
                </td>
              </tr>
              <tr>
                <td className={headerCellStyle}>Name</td>
                <td className={headerCellStyle}>Status</td>
                <td className={headerCellStyle}>Action</td>
              </tr>
            </thead>
            <tbody>
              {filteredTableData
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((row, i) => (
                  <RoleManagementCell
                    key={row?.user_id}
                    rowData={row}
                    i={i}
                    rowsPerPage={rowsPerPage}
                    page={page}
                  />
                ))}
            </tbody>
          </table>
        </div>
        <TablePaginationCustom count={filteredTableData?.length} />
      </div>
    </div>
  );
}
