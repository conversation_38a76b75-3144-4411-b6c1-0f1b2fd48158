// Next, React, Tw
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Tabs, Tab } from '@mui/material';

// Components
import Layout from '../../layouts/module/oasys';
import AllProjects from '../../components/oasys/AllProjects';
import FilterStatusButtonsGroup from '../../components/oasys/FilterStatusButtonsGroup';
import SummaryCards from '../../components/oasys/SummaryCards';
import AllJobsTable from '../../components/oasys/AllJobsTable';
import AllProcessorsTable from '../../components/oasys/AllProcessorsTable';
import AllTasks from '../../components/oasys/AllTasks';

// Others
import { OASYS_ENDPOINT } from '../../utils/oasys';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { setJobsTableData, setRole } from '../../utils/store/oasysReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { status, mode } = query;

  const { replaceParam, setParam } = useParamContext();

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${OASYS_ENDPOINT}/all/keyword`);
      const temp = response?.data?.data || [];
      temp.reverse();
      dispatch(setJobsTableData(temp));
    } catch {
      dispatch(setJobsTableData([]));
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (status) return;
    replaceParam({ status: 'all', mode: 'job', projectId: 'all' });
  }, [status]);

  useEffect(() => {
    dispatch(setRole('executive'));
  }, []);

  return (
    <>
      <div className="bg-oasys px-4 py-2 text-white">
        <div className="flex flex-col items-center justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-extrabold">Dashboard</p>
          </div>
          <div className="flex flex-col">
            <Tabs
              value={mode}
              onChange={(event, newValue) => setParam({ mode: newValue })}
              variant="standard"
              centered
              sx={{
                '& .MuiTabs-indicator': {
                  backgroundColor: 'white',
                },
                '& .MuiTab-root': {
                  color: 'white',
                },
                '& .Mui-selected': {
                  color: 'white',
                },
              }}
              className="text-white"
            >
              {['job', 'tasks distribution', 'blocked tasks', 'unassigned tasks']?.map((o, i) => (
                <Tab key={i} label={o.toUpperCase()} value={o} />
              ))}
            </Tabs>
          </div>
        </div>
      </div>
      {mode === 'job' && (
        <>
          <SummaryCards type="job" />
          <div className="container mx-auto flex flex-col gap-2 p-1 md:flex-row md:p-4">
            <div className="h-full overflow-y-auto p-1 md:p-4">
              <AllProjects />
            </div>
            <div className="flex-grow">
              {' '}
              <div className="flex flex-col items-center justify-between gap-2 p-1 dark:text-white md:flex-row md:p-4">
                <FilterStatusButtonsGroup type="job" />
              </div>
              <div className="p-1 md:p-4">
                <div className="flex flex-col gap-2 rounded-md bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
                  <AllJobsTable />
                </div>
              </div>
            </div>
          </div>
        </>
      )}
      {mode !== 'job' && (
        <div className="container mx-auto p-1 md:p-4">
          {mode === 'tasks distribution' && <AllProcessorsTable />}
          {mode === 'blocked tasks' && <AllTasks endpoint="status/blocked" />}
          {mode === 'unassigned tasks' && <AllTasks endpoint="status/pending" />}
        </div>
      )}
    </>
  );
}
