// Next, React, Tw
import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Components
import Layout from '../../layouts/module/oasys';
import EmptyPageWithLogoAndTmSong from '../../components/Shared/EmptyPageWithLogoAndTmSong';

// Others
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { replace } = useRouter();
  const { isAdmin } = useModuleRoleContext();
  const { userSubModules } = useSelector((state) => state.aum);

  useEffect(() => {
    if (isAdmin) {
      replace('/oasys/executive');
      return;
    }
    if (userSubModules?.includes('sales')) {
      replace('/oasys/sales');
      return;
    }
    if (userSubModules?.includes('executive')) {
      replace('/oasys/executive');
      return;
    }
    if (userSubModules?.includes('processor')) {
      replace('/oasys/processor');
    }
  }, [isAdmin, userSubModules?.length]);

  return <EmptyPageWithLogoAndTmSong />;
}
