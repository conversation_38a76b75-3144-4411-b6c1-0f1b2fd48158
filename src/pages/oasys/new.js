// Next, React, Tw
import { Fragment, useState } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';
import Link from 'next/link';

// Mui
import { Tooltip, Divider } from '@mui/material';
import { Remove } from '@mui/icons-material';

// Packages
import moment from 'moment';

// Components
import Layout from '../../layouts/module/oasys';
import { NewTask } from '../../components/oasys/Task';
import UploadCsvButton from '../../components/Shared/UploadCsv';
import WarnBeforeActionPopupButton from '../../components/Shared/WarnBeforeActionPopupButton';

// Others
import {
  JOB_TYPE_KEY_LABEL_MAP,
  PROCESSOR_UPDATES_KEY_LABEL_MAP,
  useOasysContext,
  OASYS_ENDPOINT,
} from '../../utils/oasys';
import { useSnackbar } from '../../components/Shared/snackbar';
import { setIsLoading } from '../../utils/store/loadingReducer';
import axios from '../../utils/axios';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const [dataArray, setDataArray] = useState([]);
  const { push } = useRouter();
  const { jobScema, newTaskSchema } = useOasysContext();

  // Form

  const handleCreateTasks = async (job_id, data) => {
    for (let i = 0; i < data?.length; i += 1) {
      let payload;
      try {
        payload = await newTaskSchema.validate(
          {
            job_id,
            task_id: `TASK-${moment().format('YYYYMMDD-HHmmss')}-${i + 1}`,
            task_type: data?.[i]?.taskType,
            data: { ...data?.[i], taskType: undefined },
          },
          { abortEarly: false }
        );
        enqueueSnackbar('Success', {
          variant: 'success',
        });
      } catch {
        enqueueSnackbar('Error', {
          variant: 'error',
        });
      }

      const file = payload?.file;
      let response;
      dispatch(setIsLoading(true));
      try {
        response = await axios.post(`${OASYS_ENDPOINT}/tasks`, payload);
      } catch {
        enqueueSnackbar('Error', {
          variant: 'error',
        });
      }

      if (file) {
        try {
          await axios.post(
            `${OASYS_ENDPOINT}/files/upload/tasks/${response?.data?.data?.id}`,
            { file },
            {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
            }
          );
        } catch {
          enqueueSnackbar('Error', {
            variant: 'error',
          });
        }
      }

      dispatch(setIsLoading(false));
    }
  };

  const handleFormSubmit = async () => {
    if (dataArray?.length === 0) {
      enqueueSnackbar('Please select at least one job', {
        variant: 'error',
      });
      return;
    }

    const data = dataArray?.map((o) => ({
      ...PROCESSOR_UPDATES_KEY_LABEL_MAP?.reduce(
        (prev, curr) => ({ ...prev, [curr?.key]: '' }),
        {}
      ),
      ...(o?.taskType?.toLowerCase()?.includes('terminat')
        ? { rft_date: '' }
        : { rft_date: undefined }),
      ...o,
    }));

    let payload;
    try {
      payload = await jobScema.validate({ counters: data?.length }, { abortEarly: false });
      enqueueSnackbar('Success', {
        variant: 'success',
      });
    } catch {
      enqueueSnackbar('Error', {
        variant: 'error',
      });
    }

    dispatch(setIsLoading(true));
    let response;
    try {
      response = await axios.post(`${OASYS_ENDPOINT}/`, payload);
      if ([201]?.includes(response?.status)) {
        enqueueSnackbar('Success', {
          variant: 'success',
        });
        if (response?.data?.data?.job_id) {
          setDataArray([]);
          await handleCreateTasks(response?.data?.data?.job_id, data);
          push(`/oasys/${response?.data?.data?.job_id}`);
        }
      }
    } catch {
      enqueueSnackbar('Error', {
        variant: 'error',
      });
    }
    dispatch(setIsLoading(false));
  };

  return (
    <>
      <div className="bg-oasys px-4 py-2 text-white">
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-extrabold">New Job</p>
          </div>
        </div>
      </div>
      <div className="mx-4 mt-8 flex items-center justify-end">
        <div className="flex items-center gap-2">
          <Link
            href="/oasys/template.xlsx"
            target="__blank"
            className="text-sm text-blue-500 hover:underline"
          >
            Template
          </Link>
          <UploadCsvButton
            onlyAccept={['xlsx']}
            onUpload={(data) => {
              let temp = [];
              Object?.keys(data)?.map((o) =>
                data?.[o]?.map((p) => temp.push({ taskType: o, ...p }))
              );
              temp = temp?.filter((o) => o?.taskType !== '_MCC-printable');
              temp = temp?.map((o) =>
                Object.fromEntries(
                  Object.entries(o).map(([key, val]) =>
                    key?.toLowerCase()?.includes('date')
                      ? [key, moment('1899-12-30').add(val, 'days').format('YYYY-MM-DD')]
                      : [key, String(val)]
                  )
                )
              );
              setDataArray(temp);
            }}
            headerIsAtRow={1}
          />
        </div>
        <div className="flex items-center justify-end">
          <button
            type="button"
            className="cta-btn bg-oasys"
            onClick={async () => {
              await handleFormSubmit();
            }}
          >
            Submit
          </button>
        </div>
      </div>
      <div className="container mx-auto p-1 md:p-4">
        <div className="flex flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
          {dataArray?.map((o, i) => (
            <Fragment key={i}>
              <div className="flex w-full flex-col gap-2">
                <div className="flex items-center justify-end">
                  <Tooltip title="Remove this job">
                    <WarnBeforeActionPopupButton
                      onCancel={() => {}}
                      onApprove={() =>
                        setDataArray((prev) => prev.filter((_, index) => index !== i))
                      }
                    >
                      <Remove className="bg-oasys rounded-md text-white" />
                    </WarnBeforeActionPopupButton>
                  </Tooltip>
                </div>
                <NewTask
                  taskType={o?.taskType}
                  data={o}
                  onChange={(event) =>
                    setDataArray((prev) => {
                      const newData = [...prev];
                      newData[i] = { ...newData[i], [event.target.name]: event.target.value };
                      return newData;
                    })
                  }
                  disabled={false}
                />
                <div />
              </div>
              <Divider />
            </Fragment>
          ))}
          <div className="flex flex-col gap-2">
            <p className="text-md text-center font-semibold">Add Job ++</p>
            <div className="flex flex-wrap items-center justify-center gap-4">
              {Object?.keys(JOB_TYPE_KEY_LABEL_MAP)?.map((o, i) => (
                <button
                  key={i}
                  type="button"
                  className="cta-btn bg-oasys flex w-1/5 justify-center overflow-x-hidden whitespace-nowrap px-1"
                  onClick={() =>
                    setDataArray((prev) => [
                      ...prev,
                      {
                        taskType: o,
                        ...JOB_TYPE_KEY_LABEL_MAP[o]?.reduce(
                          (a, b) => ({ ...a, [b?.key]: '' }),
                          {}
                        ),
                      },
                    ])
                  }
                >
                  {o}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
