import { useState, useEffect } from 'react';
import { Box, Typography } from '@mui/material';

import Layout from '../../layouts/module/scorecard';
import DashboardOverview from '../../components/scorecard/dashboard/DashboardOverview';
import { fetchDashboardData } from '../../utils/scorecard';

Page.getLayout = (page) => <Layout>{page}</Layout>;

export default function Page() {
  // Router not currently used but might be needed for navigation later
  // const router = useRouter();
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [updatedAt, setUpdatedAt] = useState(new Date().toLocaleString());

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        const data = await fetchDashboardData();
        setDashboardData(data);
        setError(null);
        setUpdatedAt(new Date().toLocaleString());
      } catch (err) {
        setError('Failed to load dashboard data');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  const renderContent = () => {
    if (error) {
      return (
        <Box className="p-4 text-red-500">
          <Typography color="error">{error}</Typography>
        </Box>
      );
    }

    if (loading) {
      return (
        <Box className="p-4">
          <Typography>Loading dashboard data...</Typography>
        </Box>
      );
    }

    return <DashboardOverview data={dashboardData} />;
  };

  return (
    <>
      <div className="bg-sdp px-4 py-2 text-white">
        <div className="flex flex-col justify-between gap-4 md:flex-row md:gap-0">
          <div className="flex flex-col gap-1">
            <p className="text-lg font-extrabold">Partner Scorecard</p>
            <p className="text-xs">As of : {updatedAt}</p>
          </div>
        </div>
      </div>
      <div className="container mx-auto p-1 md:p-4">
        <div className="flex flex-col gap-8 rounded-xl bg-white p-4 shadow-dashboardCard dark:bg-gray-600 dark:text-white dark:shadow-dashboardCardDark">
          {renderContent()}
        </div>
      </div>
    </>
  );
}
