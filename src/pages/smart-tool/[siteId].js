/* eslint-disable no-nested-ternary */
// Next, React, tW
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';

// Mui
import {
  Grid,
  Typography,
  Paper,
  Button,
  CircularProgress,
  Alert,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Stack,
  Divider,
  Container,
  useTheme,
  useMediaQuery,
  Drawer, // Import Drawer
  List, // Import List
  ListItem, // Import ListItem
  ListItemButton, // Import ListItemButton
  ListItemText, // Import ListItemText
  Toolbar, // Import Toolbar for spacing in Drawer
  AppBar, // Import AppBar for Drawer title
} from '@mui/material';
import {
  ArrowBack,
  Edit,
  Download,
  CloudUpload,
  Delete,
  Save,
  Cancel,
  Add,
} from '@mui/icons-material';

// Packages
import { useSnackbar } from 'notistack';

// Components
import Layout from '../../layouts/module/smart-tool';
import ExportOptions from '../../components/smart-tool/ExportOptions';
import EditableField from '../../components/smart-tool/EditableField';

// Others
import { SMART_TOOL_ENDPOINT } from '../../utils/smart-tool';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useAuthContext } from '../../utils/auth/useAuthContext'; // Import useAuthContext

// ----------------------------------------------------------------------

// Helper function to format date string
const formatDateTime = (dateString) => {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return '-';
    }
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
    const year = date.getFullYear();
    let hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
    const hoursStr = String(hours).padStart(2, '0');

    return `${day}-${month}-${year} ${hoursStr}:${minutes} ${ampm}`;
  } catch (e) {
    console.error("Error formatting date:", dateString, e);
    return '-'; // Return fallback on error
  }
};

// Formats YYYY-MM-DD or other parsable date string to DD-MM-YYYY
const formatDateToDDMMYYYY = (dateString) => {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    // Adjust for potential timezone offset if the input is just YYYY-MM-DD
    const utcDate = new Date(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate());
    if (isNaN(utcDate.getTime())) return '-';
    const day = String(utcDate.getDate()).padStart(2, '0');
    const month = String(utcDate.getMonth() + 1).padStart(2, '0'); // Month is 0-indexed
    const year = utcDate.getFullYear();
    return `${day}-${month}-${year}`;
  } catch (e) {
    console.error("Error formatting date:", dateString, e);
    return '-';
  }
};

// Formats HH:mm or HH:mm:ss string to hh:mm AM/PM
const formatTimeToAMPM = (timeString) => {
  if (!timeString || typeof timeString !== 'string') return '-';
  try {
    const parts = timeString.split(':');
    if (parts.length < 2) return '-'; // Basic validation
    let hours = parseInt(parts[0], 10);
    const minutes = String(parts[1]).padStart(2, '0');
    if (isNaN(hours) || parseInt(parts[1], 10) > 59) return '-'; // More validation

    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
    const hoursStr = String(hours).padStart(2, '0');

    return `${hoursStr}:${minutes} ${ampm}`;
  } catch (e) {
    console.error("Error formatting time:", timeString, e);
    return '-';
  }
};

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  const ATTACHMENT_TYPES = [
    'Site Map (google map)',
    'Site overview (location)',
    'Room photo',
    'Fiber route',
    'Internal Wiring',
    'Space photo',
    'Power Breaker photo',
    'Other',
  ];

  // Standard and Vars
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const { query, push } = useRouter();
  const { siteId } = query;
  const { user } = useAuthContext(); // Get user from context
  const [siteDetails, setSiteDetails] = useState(null);
  const [error, setError] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState({});
  const [saveLoading, setSaveLoading] = useState(false);

  // Findings State
  const [findingsEditMode, setFindingsEditMode] = useState(false);
  const [findings, setFindings] = useState([]);
  const [findingsLoading, setFindingsLoading] = useState(false);
  const [findingsError, setFindingsError] = useState(null);
  const [openFindingDialog, setOpenFindingDialog] = useState(false);
  const [currentFinding, setCurrentFinding] = useState(null);
  const [findingSaveLoading, setFindingSaveLoading] = useState(false);

  // Delete State
  const [openDeleteConfirmDialog, setOpenDeleteConfirmDialog] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // Attachment State
  const [selectedAttachmentType, setSelectedAttachmentType] = useState('');
  const [customAttachmentType, setCustomAttachmentType] = useState('');
  const [selectedFinding, setSelectedFinding] = useState('');
  const [attachmentCaption, setAttachmentCaption] = useState('');
  const [attachments, setAttachments] = useState([]);
  const [attachmentLoading, setAttachmentLoading] = useState(false);
  const [openDeleteAttachmentDialog, setOpenDeleteAttachmentDialog] = useState(false);
  const [attachmentToDelete, setAttachmentToDelete] = useState(null);
  const [openEditAttachmentDialog, setOpenEditAttachmentDialog] = useState(false);
  const [attachmentToEdit, setAttachmentToEdit] = useState(null);

  // Mobile Drawer State
  const [workgroupDrawerOpen, setWorkgroupDrawerOpen] = useState(false);
  const [attachmentTypeDrawerOpen, setAttachmentTypeDrawerOpen] = useState(false);
  const [findingUploadDrawerOpen, setFindingUploadDrawerOpen] = useState(false);
  const [findingEditDrawerOpen, setFindingEditDrawerOpen] = useState(false);

  const theme = useTheme(); // Access theme
  const isMobile = useMediaQuery(theme.breakpoints.down('sm')); // Check for mobile

  // Helper for minimalist TextField styling
  const minimalistTextFieldSx = (theme) => ({
    '& .MuiInputBase-input': {
      color: theme.palette.text.primary, // Use theme text color
    },
    '& .MuiOutlinedInput-root': {
      backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[800] : theme.palette.grey[100], // Subtle background
      '& fieldset': {
        borderColor: theme.palette.divider, // Use theme divider color for border
        borderWidth: '1px', // Ensure border is visible but thin
      },
      '&:hover fieldset': {
        borderColor: theme.palette.mode === 'dark' ? theme.palette.grey[600] : theme.palette.grey[400],
      },
      '&.Mui-focused fieldset': {
        borderColor: theme.palette.primary.main, // Highlight with primary color on focus
        borderWidth: '1px',
      },
      '&.Mui-disabled': {
        backgroundColor: theme.palette.action.disabledBackground,
        '& fieldset': {
          borderColor: theme.palette.action.disabled,
        },
      },
    },
    '& .MuiInputLabel-root': {
      color: theme.palette.text.secondary, // Slightly muted label color
      '&.Mui-focused': {
        color: theme.palette.primary.main, // Highlight label on focus
      },
    },
  });

  // Helper for minimalist Select styling
  const minimalistSelectSx = (theme) => ({
    backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[800] : theme.palette.grey[100],
    '& .MuiSelect-select': {
      color: theme.palette.text.primary,
    },
    '& .MuiOutlinedInput-notchedOutline': {
      borderColor: theme.palette.divider,
      borderWidth: '1px',
    },
    '&:hover .MuiOutlinedInput-notchedOutline': {
      borderColor: theme.palette.mode === 'dark' ? theme.palette.grey[600] : theme.palette.grey[400],
    },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      borderColor: theme.palette.primary.main,
      borderWidth: '1px',
    },
    '.MuiSvgIcon-root ': { // Style dropdown icon
      color: theme.palette.text.secondary,
    },
  });

  // Helper for minimalist Table styling
  const minimalistTableSx = (theme) => ({
    '& .MuiTableCell-head': {
      fontWeight: 600,
      color: theme.palette.text.secondary,
      backgroundColor: theme.palette.mode === 'dark' ? 'transparent' : theme.palette.grey[50], // Very light header background
      borderBottom: `1px solid ${theme.palette.divider}`, // Only bottom border for header cells
      padding: '8px 16px', // Adjust padding
    },
    '& .MuiTableCell-body': {
      borderBottom: `1px solid ${theme.palette.divider}`, // Light border between rows
      padding: '12px 16px', // Adjust padding
    },
    '& .MuiTableRow-root:last-child .MuiTableCell-body': {
      borderBottom: 'none', // No border for the last row
    },
    '& .MuiTableBody-root .MuiTableRow-root:hover': {
      backgroundColor: theme.palette.action.hover, // Subtle hover effect
    },
  });

  // Attachment Handlers
  const handleAttachmentTypeChange = (e) => {
    const value = e.target.value;
    setSelectedAttachmentType(value);
    
    // Reset custom type if not selecting 'Other'
    if (value !== 'Other') {
      setCustomAttachmentType('');
    }
  };

  const handleDownloadAttachment = async (attachmentId) => {
    try {
      const response = await axios.get(`${SMART_TOOL_ENDPOINT}/files/download/${attachmentId}`, {
        responseType: 'blob',
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      // Use the filename from Content-Disposition header if available, otherwise use a default name
      const contentDisposition = response.headers['content-disposition'];
      const fileName = contentDisposition
        ? contentDisposition.split('filename=')[1].replace(/"/g, '')
        : `attachment-${attachmentId}`;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      enqueueSnackbar('File downloaded successfully');
    } catch (err) {
      enqueueSnackbar(err.message || 'An error occurred while downloading file', {
        variant: 'error',
      });
    }
  };

  const handleFindingChange = (e) => {
    setSelectedFinding(e.target.value);
  };

  const handleCaptionChange = (e) => {
    setAttachmentCaption(e.target.value);
  };

  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (file && selectedAttachmentType && siteId) {
      // Validate that custom type is provided when 'Other' is selected
      if (selectedAttachmentType === 'Other' && !customAttachmentType) {
        enqueueSnackbar('Please specify the attachment type', {
          variant: 'error',
        });
        return;
      }

      // Validate file type if a finding is selected
      if (selectedFinding) {
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedTypes.includes(file.type)) {
          enqueueSnackbar('Only JPG, JPEG, and PNG files are allowed for finding attachments', {
            variant: 'error',
          });
          return;
        }
      }

      setAttachmentLoading(true);
      try {
        // Create form data
        const fileFormData = new FormData();
        fileFormData.append('file', file);

        // Add caption if provided
        if (attachmentCaption) {
          fileFormData.append('caption', attachmentCaption);
        }

        // Determine attachment type to use (custom or predefined)
        const attachmentType = selectedAttachmentType === 'Other' && customAttachmentType 
          ? customAttachmentType 
          : selectedAttachmentType;

        // Construct the URL based on whether a finding is selected
        let uploadUrl = `${SMART_TOOL_ENDPOINT}/files/upload/${attachmentType}/${siteId}`;
        if (selectedFinding) {
          uploadUrl += `?finding_id=${selectedFinding}`;
        }

        const response = await axios.post(uploadUrl, fileFormData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });

        if (response.data.success) {
          fetchAttachments();
          enqueueSnackbar('File uploaded successfully');
          setSelectedAttachmentType('');
          setSelectedFinding('');
          setAttachmentCaption('');
          setCustomAttachmentType('');
        } else {
          throw new Error(response.data.message || 'Failed to upload file');
        }
      } catch (err) {
        enqueueSnackbar(err.message || 'An error occurred while uploading file', {
          variant: 'error',
        });
      } finally {
        setAttachmentLoading(false);
      }
    }
  };

  const handleDeleteAttachmentClick = (attachment) => {
    setAttachmentToDelete(attachment);
    setOpenDeleteAttachmentDialog(true);
  };

  const handleEditAttachmentClick = (attachment) => {
    setAttachmentToEdit({
      ...attachment,
      finding_id: attachment.finding_id || '',
      caption: attachment.caption || '',
    });
    setOpenEditAttachmentDialog(true);
  };

  const handleSaveAttachmentEdit = async () => {
    if (!attachmentToEdit) {
      setOpenEditAttachmentDialog(false);
      return;
    }

    try {
      const response = await axios.put(`${SMART_TOOL_ENDPOINT}/files/${attachmentToEdit.id}`, {
        finding_id: attachmentToEdit.finding_id || null,
        caption: attachmentToEdit.caption || '',
      });

      if (response.data.success) {
        // Update in local state
        setAttachments((prev) =>
          prev.map((attachment) =>
            attachment.id === attachmentToEdit.id
              ? {
                  ...attachment,
                  finding_id: attachmentToEdit.finding_id || null,
                  caption: attachmentToEdit.caption || '',
                }
              : attachment
          )
        );

        enqueueSnackbar('Attachment updated successfully');
      } else {
        throw new Error(response.data.message || 'Failed to update attachment');
      }
    } catch (err) {
      enqueueSnackbar(err.message || 'An error occurred while updating attachment', {
        variant: 'error',
      });
    } finally {
      setOpenEditAttachmentDialog(false);
      setAttachmentToEdit(null);
    }
  };

  const handleAttachmentEditChange = (e) => {
    const { name, value } = e.target;
    setAttachmentToEdit((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleConfirmDeleteAttachment = async () => {
    if (!attachmentToDelete) {
      setOpenDeleteAttachmentDialog(false);
      return;
    }

    try {
      const response = await axios.delete(`${SMART_TOOL_ENDPOINT}/files/${attachmentToDelete.id}`);

      if (response.data.success) {
        // Remove from local state
        setAttachments((prev) =>
          prev.filter((attachment) => attachment.id !== attachmentToDelete.id)
        );

        enqueueSnackbar('File deleted successfully');
      } else {
        throw new Error(response.data.message || 'Failed to delete file');
      }
    } catch (err) {
      enqueueSnackbar(err.message || 'An error occurred while deleting file', {
        variant: 'error',
      });
    } finally {
      setOpenDeleteAttachmentDialog(false);
      setAttachmentToDelete(null);
    }
  };

  // Fetch attachments from API
  const fetchAttachments = async () => {
    if (!siteId) return;

    setAttachmentLoading(true);
    try {
      const response = await axios.get(`${SMART_TOOL_ENDPOINT}/files/${siteId}`);

      if (response.data.success) {
        const attachmentsData = response.data.data || [];

        // For image attachments associated with findings, fetch the base64 data
        const processedAttachments = await Promise.all(
          attachmentsData.map(async (attachment) => {
            // Only process image attachments
            const isImage =
              attachment.file_name?.toLowerCase().endsWith('.jpg') ||
              attachment.file_name?.toLowerCase().endsWith('.jpeg') ||
              attachment.file_name?.toLowerCase().endsWith('.png');

            // Only fetch base64 data for images associated with findings
            if (isImage && attachment.finding_id) {
              try {
                // Fetch the image data
                const imageResponse = await axios.get(
                  `${SMART_TOOL_ENDPOINT}/files/download/${attachment.id}`,
                  {
                    responseType: 'blob',
                  }
                );

                // Convert blob to base64
                const blob = imageResponse.data;
                const reader = new FileReader();

                // Use a promise to handle the async FileReader
                const base64Data = await new Promise((resolve) => {
                  reader.onloadend = () => {
                    // Extract the base64 data part (remove the data:image/xxx;base64, prefix)
                    const base64String = reader.result.split(',')[1];
                    resolve(base64String);
                  };
                  reader.readAsDataURL(blob);
                });

                // Return attachment with base64 data
                return {
                  ...attachment,
                  base64Data,
                };
              } catch (imageErr) {
                console.error(
                  `Error fetching image data for attachment ${attachment.id}:`,
                  imageErr
                );
                return attachment; // Return original attachment if image fetch fails
              }
            }

            // Return original attachment for non-images or non-finding attachments
            return attachment;
          })
        );

        setAttachments(processedAttachments);
      } else {
        throw new Error(response.data.message || 'Failed to fetch attachments');
      }
    } catch (err) {
      console.error('Error fetching attachments:', err);
      if (attachments.length > 0) {
        enqueueSnackbar(err.message || 'An error occurred while fetching attachments', {
          variant: 'error',
        });
      }
    } finally {
      setAttachmentLoading(false);
    }
  };

  // Delete Handlers
  const handleDeleteClick = () => {
    setOpenDeleteConfirmDialog(true);
  };

  const handleConfirmDelete = async () => {
    if (!siteId) {
      enqueueSnackbar('Site ID is required to delete site', {
        variant: 'error',
      });
      setOpenDeleteConfirmDialog(false);
      return;
    }

    setDeleteLoading(true);
    try {
      const response = await axios.delete(`${SMART_TOOL_ENDPOINT}/sites/${siteId}`);

      if (response.data.success) {
        enqueueSnackbar('Site deleted successfully');
        push(`/smart-tool/`);
      } else {
        enqueueSnackbar(response.data.message || 'Failed to delete site', {
          variant: 'error',
        });
      }
    } catch (err) {
      enqueueSnackbar(err.response?.data?.message || 'An error occurred while deleting site', {
        variant: 'error',
      });
    } finally {
      setDeleteLoading(false);
      setOpenDeleteConfirmDialog(false);
    }
  };

  // Back Handler
  const handleBack = () => push('/smart-tool/');

  // Edit Handlers
  const handleEditClick = () => {
    setFormData({
      ...siteDetails,
      representatives: siteDetails.representatives ? [...siteDetails.representatives] : [],
      checklists: siteDetails.checklists ? [...siteDetails.checklists] : [],
      actions: siteDetails.actions ? [...siteDetails.actions] : [],
    });
    setEditMode(!editMode);
  };

  // Findings Edit Handler
  const handleFindingsEditClick = () => {
    setFindingsEditMode(!findingsEditMode);
  };

  const handleCancelEdit = () => {
    setEditMode(false);
    setFormData({});
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleRepresentativeChange = (index, field, value) => {
    setFormData((prev) => {
      const updatedRepresentatives = [...(prev.representatives || [])];
      updatedRepresentatives[index] = {
        ...updatedRepresentatives[index],
        [field]: value,
      };
      return {
        ...prev,
        representatives: updatedRepresentatives,
      };
    });
  };

  const handleAddRepresentative = () => {
    setFormData((prev) => ({
      ...prev,
      representatives: [...(prev.representatives || []), { name: '', unit: '', contact_no: '' }],
    }));
  };

  const handleRemoveRepresentative = (index) => {
    setFormData((prev) => {
      const updatedRepresentatives = [...(prev.representatives || [])];
      updatedRepresentatives.splice(index, 1);
      return {
        ...prev,
        representatives: updatedRepresentatives,
      };
    });
  };

  const handleActionChange = (index, field, value) => {
    setFormData((prev) => {
      const updatedActions = [...(prev.actions || [])];
      updatedActions[index] = {
        ...updatedActions[index],
        [field]: value,
      };
      return {
        ...prev,
        actions: updatedActions,
      };
    });
  };

  const handleAddAction = () => {
    setFormData((prev) => ({
      ...prev,
      actions: [...(prev.actions || []), { action: '', responsibility: '' }],
    }));
  };

  const handleRemoveAction = (index) => {
    setFormData((prev) => {
      const updatedActions = [...(prev.actions || [])];
      updatedActions.splice(index, 1);
      return {
        ...prev,
        actions: updatedActions,
      };
    });
  };

  const handleChecklistChange = (index, field, value) => {
    setFormData((prev) => {
      const updatedChecklists = [...(prev.checklists || [])];
      updatedChecklists[index] = {
        ...updatedChecklists[index],
        [field]: value,
      };
      return {
        ...prev,
        checklists: updatedChecklists,
      };
    });
  };

  const handleAddChecklist = () => {
    setFormData((prev) => ({
      ...prev,
      checklists: [...(prev.checklists || []), { requirement: '', status: '' }],
    }));
  };

  const handleRemoveChecklist = (index) => {
    setFormData((prev) => {
      const updatedChecklists = [...(prev.checklists || [])];
      updatedChecklists.splice(index, 1);
      return {
        ...prev,
        checklists: updatedChecklists,
      };
    });
  };

  // Findings Dialog Handlers
  const handleOpenAddFindingDialog = () => {
    setCurrentFinding({ description: '' });
    setOpenFindingDialog(true);
  };

  const handleOpenEditFindingDialog = (finding) => {
    setCurrentFinding({ ...finding });
    setOpenFindingDialog(true);
  };

  const handleCloseFindingDialog = () => {
    setOpenFindingDialog(false);
    setCurrentFinding(null);
  };

  const handleFindingInputChange = (e) => {
    const { name, value } = e.target;
    setCurrentFinding((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Findings API Handlers
  const fetchFindings = async () => {
    if (!siteId) return;

    setFindingsLoading(true);
    try {
      // Try to get findings from the dedicated API
      try {
        const response = await axios.get(`${SMART_TOOL_ENDPOINT}/findings?site_id=${siteId}`);

        if (response.data.success) {
          // Sort findings by created_at in ascending order
          const sortedFindings = [...(response.data.data || [])].sort((a, b) => {
            // Handle missing dates (place them at the beginning)
            if (!a.created_at) return -1;
            if (!b.created_at) return 1;
            // Compare dates in ascending order
            return new Date(a.created_at) - new Date(b.created_at);
          });
          setFindings(sortedFindings);
          setFindingsError(null);
          return;
        }
      } catch (apiErr) {
        console.warn('Dedicated findings API not available, falling back to site details:', apiErr);
        // Continue to fallback
      }

      // Fallback: Use findings from site details if available
      if (siteDetails && siteDetails.findings) {
        let siteFindings = [];

        if (Array.isArray(siteDetails.findings)) {
          siteFindings = [...siteDetails.findings];
        } else if (typeof siteDetails.findings === 'string' && siteDetails.findings.trim() !== '') {
          // Convert string to a single finding object
          siteFindings = [{ description: siteDetails.findings }];
        }

        // Sort findings by created_at in ascending order
        const sortedFindings = siteFindings.sort((a, b) => {
          // Handle missing dates (place them at the beginning)
          if (!a.created_at) return -1;
          if (!b.created_at) return 1;
          // Compare dates in ascending order
          return new Date(a.created_at) - new Date(b.created_at);
        });

        setFindings(sortedFindings);
        setFindingsError(null);
      } else {
        // No findings in site details either
        setFindings([]);
        setFindingsError(null);
      }
    } catch (err) {
      console.error('Error processing findings:', err);
      setFindings([]);
      setFindingsError(null); // Don't show error initially
    } finally {
      setFindingsLoading(false);
    }
  };

  const handleSaveFinding = async () => {
    if (!currentFinding || !siteId) return;

    setFindingSaveLoading(true);
    try {
      // First try to use the dedicated API
      try {
        let response;

        if (currentFinding.id) {
          // Update existing finding
          response = await axios.put(`${SMART_TOOL_ENDPOINT}/findings/${currentFinding.id}`, {
            ...currentFinding,
            site_id: siteId,
            user_full_name: user?.name, // Add user name for update
          });
        } else {
          // Create new finding
          response = await axios.post(`${SMART_TOOL_ENDPOINT}/findings/${siteId}`, {
            ...currentFinding,
            user_full_name: user?.name, // Add user name for create
          });
        }

        if (response.data.success) {
          enqueueSnackbar(`Finding ${currentFinding.id ? 'updated' : 'added'} successfully`);
          fetchFindings(); // Refresh findings list
          handleCloseFindingDialog();
          return;
        }
      } catch (apiErr) {
        console.warn('Dedicated findings API not available for saving:', apiErr);
        // Continue to fallback
      }

      // Fallback: Update findings in site details
      const updatedFindings = [...findings];

      if (currentFinding.id) {
        // Update existing finding
        const index = updatedFindings.findIndex((f) => f.id === currentFinding.id);
        if (index !== -1) {
          updatedFindings[index] = { ...currentFinding };
        }
      } else {
        // Add new finding with a temporary ID
        updatedFindings.push({
          ...currentFinding,
          id: `temp-${Date.now()}`,
        });
      }

      // Update site details with new findings
      const response = await axios.put(`${SMART_TOOL_ENDPOINT}/sites/${siteId}`, {
        ...siteDetails,
        findings: updatedFindings,
      });

      if (response.data.success) {
        setFindings(updatedFindings);
        setSiteDetails((prev) => ({
          ...prev,
          findings: updatedFindings,
        }));
        enqueueSnackbar(`Finding ${currentFinding.id ? 'updated' : 'added'} successfully`);
        handleCloseFindingDialog();
      } else {
        throw new Error(
          response.data.message || `Failed to ${currentFinding.id ? 'update' : 'add'} finding`
        );
      }
    } catch (err) {
      enqueueSnackbar(
        err.message ||
          `An error occurred while ${currentFinding.id ? 'updating' : 'adding'} finding`,
        { variant: 'error' }
      );
    } finally {
      setFindingSaveLoading(false);
    }
  };

  const handleDeleteFinding = async (findingId) => {
    if (!findingId) return;

    try {
      // First try to use the dedicated API
      try {
        const response = await axios.delete(`${SMART_TOOL_ENDPOINT}/findings/${findingId}`);

        if (response.data.success) {
          enqueueSnackbar('Finding deleted successfully');
          fetchFindings(); // Refresh findings list
          return;
        }
      } catch (apiErr) {
        console.warn('Dedicated findings API not available for deletion:', apiErr);
        // Continue to fallback
      }

      // Fallback: Update findings in site details
      const updatedFindings = findings.filter((f) => f.id !== findingId);

      // Update site details with new findings
      const response = await axios.put(`${SMART_TOOL_ENDPOINT}/sites/${siteId}`, {
        ...siteDetails,
        findings: updatedFindings,
      });

      if (response.data.success) {
        setFindings(updatedFindings);
        setSiteDetails((prev) => ({
          ...prev,
          findings: updatedFindings,
        }));
        enqueueSnackbar('Finding deleted successfully');
      } else {
        throw new Error(response.data.message || 'Failed to delete finding');
      }
    } catch (err) {
      enqueueSnackbar(err.message || 'An error occurred while deleting finding', {
        variant: 'error',
      });
    }
  };

  const handleSave = async () => {
    setSaveLoading(true);
    try {
      // Add user_full_name to the payload
      const payload = {
        ...formData,
        user_full_name: user?.name,
      };
      const response = await axios.put(`${SMART_TOOL_ENDPOINT}/sites/${siteId}`, payload);

      if (response.data.success) {
        // Re-fetch data after successful save
        await fetchSiteDetails();
        enqueueSnackbar('Site details updated successfully');
        setEditMode(false);
        // Reset formData only after successful save and fetch
        setFormData({});
      } else {
        const errorObj = response.data.error || {};
        const errorMessage =
          errorObj.message || response.data.message || 'Failed to update site details';
        const errorCode = errorObj.code || '';

        if (errorCode === 'DUPLICATE_SPHERE_ID') {
          enqueueSnackbar(`Error: ${errorMessage}. Please use a different Sphere ID.`, {
            variant: 'error',
          });
        } else {
          enqueueSnackbar(`Error: ${errorMessage}`, {
            variant: 'error',
          });
        }
      }
    } catch (err) {
      if (err.error) {
        const errorObj = err.error;
        const errorMessage = errorObj.message || 'An error occurred';
        const errorCode = errorObj.code || '';

        if (errorCode === 'DUPLICATE_SPHERE_ID') {
          enqueueSnackbar(`Error: ${errorMessage}. Please use a different Sphere ID.`, {
            variant: 'error',
          });
        } else {
          enqueueSnackbar(`Error: ${errorMessage}`, {
            variant: 'error',
          });
        }
      } else if (err.response?.data?.error) {
        const errorObj = err.response.data.error;
        const errorMessage = errorObj.message || 'An error occurred';
        const errorCode = errorObj.code || '';

        if (errorCode === 'DUPLICATE_SPHERE_ID') {
          enqueueSnackbar(`Error: ${errorMessage}. Please use a different Sphere ID.`, {
            variant: 'error',
          });
        } else {
          enqueueSnackbar(`Error: ${errorMessage}`, {
            variant: 'error',
          });
        }
      } else if (err.response?.data?.success === false) {
        const errorMessage = err.response.data.message || 'An error occurred';
        enqueueSnackbar(`Error: ${errorMessage}`, {
          variant: 'error',
        });
      } else {
        enqueueSnackbar(
          err.response?.data?.message || 'An error occurred while updating site details',
          { variant: 'error' }
        );
      }
    } finally {
      setSaveLoading(false);
    }
  };

  // --- Define fetchSiteDetails as a standalone function ---
  const fetchSiteDetails = async () => {
    if (!siteId) return;

    dispatch(setIsLoading(true));
    setError(null); // Reset error before fetching
    try {
      const response = await axios.get(`${SMART_TOOL_ENDPOINT}/sites/${siteId}`);

      if (response.data.success) {
        const siteData = response.data.data.site || response.data.data;
        setSiteDetails(siteData);
        // Don't reset formData here, let handleSave handle it
      } else {
        throw new Error(response.data.message || 'Failed to fetch site details');
      }
    } catch (err) {
      setError(err.message || 'An error occurred while fetching site details');
    } finally {
      dispatch(setIsLoading(false));
    }
  };

  // Fetch Site Details, Attachments, and Findings on initial load and siteId change
  useEffect(() => {
    fetchSiteDetails(); // Call the standalone fetch function

    if (siteId) {
      fetchAttachments();
    }
  }, [siteId]); // Keep dependency array as is

  // Fetch findings after site details are loaded
  useEffect(() => {
    if (siteDetails && siteId) {
      fetchFindings();
    }
  }, [siteDetails, siteId]);

  // --- Define Content Variables ---
  let representativesContent = null;
  if (siteDetails) { // Ensure siteDetails exists before accessing representatives
    if (editMode) {
      if (isMobile) {
        // Mobile Edit Mode: Stacked View
        representativesContent = (
          <Stack spacing={2}>
            {formData.representatives && formData.representatives.length > 0 ? (
              formData.representatives.map((rep, index) => (
                <Paper key={index} variant="outlined" sx={{ p: 2 }}>
                  <Stack spacing={1.5}>
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                      <Typography variant="subtitle2">Representative {index + 1}</Typography>
                      <IconButton
                        size="small"
                        onClick={() => handleRemoveRepresentative(index)}
                        color="error"
                      >
                        <Delete fontSize="small" />
                      </IconButton>
                    </Stack>
                    <TextField
                      label="Name"
                      fullWidth
                      size="small"
                      value={rep.name || ''}
                      onChange={(e) => handleRepresentativeChange(index, 'name', e.target.value)}
                      variant="outlined"
                      sx={minimalistTextFieldSx(theme)}
                    />
                    <TextField
                      label="Unit/Company"
                      fullWidth
                      size="small"
                      value={rep.unit || ''}
                      onChange={(e) => handleRepresentativeChange(index, 'unit', e.target.value)}
                      variant="outlined"
                      sx={minimalistTextFieldSx(theme)}
                    />
                    <TextField
                      label="Contact No"
                      fullWidth
                      size="small"
                      value={rep.contact_no || ''}
                      onChange={(e) => handleRepresentativeChange(index, 'contact_no', e.target.value)}
                      variant="outlined"
                      sx={minimalistTextFieldSx(theme)}
                    />
                  </Stack>
                </Paper>
              ))
            ) : (
              <Typography variant="body2" color="text.secondary" align="center" sx={{ py: 3 }}>
                No representatives added yet.
              </Typography>
            )}
            <Button
              variant="outlined"
              size="small"
              startIcon={<Add />}
              onClick={handleAddRepresentative}
              sx={{ alignSelf: 'flex-start' }} // Align button left
            >
              Add Representative
            </Button>
          </Stack>
        );
      } else {
         // Desktop Edit Mode: Table View
        representativesContent = (
          <Stack spacing={2}> {/* Use Stack for vertical spacing */}
            {formData.representatives && formData.representatives.length > 0 ? (
              <TableContainer
                component={Paper}
                variant="outlined" // Consistent outlined style
                sx={{ ...minimalistTableSx(theme), overflowX: 'auto' }} // Apply minimalist style and ensure horizontal scroll on small screens
              >
                <Table size="small" aria-label="representatives table">
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ width: '5%' }}>No.</TableCell> {/* Set widths if needed */}
                      <TableCell sx={{ width: '30%' }}>Name</TableCell>
                      <TableCell sx={{ width: '25%' }}>Unit/Company</TableCell>
                      <TableCell sx={{ width: '30%' }}>Contact No</TableCell>
                      <TableCell sx={{ width: '10%', textAlign: 'right' }}>Actions</TableCell> {/* Align actions */}
                    </TableRow>
                  </TableHead>
                  <TableBody>{/* Ensure no leading/trailing whitespace within TableBody */}
                    {formData.representatives.map((rep, index) => (
                      <TableRow key={index} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                        <TableCell>{index + 1}</TableCell>
                        <TableCell>
                          <TextField
                            fullWidth
                            size="small"
                            value={rep.name || ''}
                            onChange={(e) => handleRepresentativeChange(index, 'name', e.target.value)}
                            variant="outlined" // Use outlined for consistency within table
                            sx={minimalistTextFieldSx(theme)} // Apply minimalist style
                          />
                        </TableCell>
                        <TableCell>
                          <TextField
                            fullWidth
                            size="small"
                            value={rep.unit || ''}
                            onChange={(e) => handleRepresentativeChange(index, 'unit', e.target.value)}
                            variant="outlined"
                            sx={minimalistTextFieldSx(theme)}
                          />
                        </TableCell>
                        <TableCell>
                          <TextField
                            fullWidth
                            size="small"
                            value={rep.contact_no || ''}
                            onChange={(e) => handleRepresentativeChange(index, 'contact_no', e.target.value)}
                            variant="outlined"
                            sx={minimalistTextFieldSx(theme)}
                          />
                        </TableCell>
                        <TableCell align="right"> {/* Align actions */}
                          <IconButton
                            size="small"
                            onClick={() => handleRemoveRepresentative(index)}
                            color="error" // Use color directly
                          >
                            <Delete fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}{/* Ensure no leading/trailing whitespace within TableBody */}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Typography variant="body2" color="text.secondary" align="center" sx={{ py: 3 }}>
                No representatives added
              </Typography>
            )}
            <Button
              variant="outlined"
              size="small"
              startIcon={<Add />} // Use Add icon
              onClick={handleAddRepresentative}
              sx={{ alignSelf: 'flex-start' }} // Align button left
            >
              Add Representative
            </Button>
          </Stack>
        );
      }
    } else {
      // View Mode - conditional based on device
      if (isMobile) {
        // Mobile View Mode: Stacked View
        representativesContent = (
          <Stack spacing={2}>
            {siteDetails.representatives && siteDetails.representatives.length > 0 ? (
              siteDetails.representatives.map((rep, index) => (
                <Paper key={index} variant="outlined" sx={{ p: 2 }}>
                  <Stack spacing={1}>
                    <Typography variant="subtitle2">Representative {index + 1}</Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="caption" color="text.secondary">Name:</Typography>
                      <Typography variant="body2">{rep.name || '-'}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="caption" color="text.secondary">Unit:</Typography>
                      <Typography variant="body2">{rep.unit || '-'}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="caption" color="text.secondary">Contact No:</Typography>
                      <Typography variant="body2">{rep.contact_no || '-'}</Typography>
                    </Box>
                  </Stack>
                </Paper>
              ))
            ) : (
              <Typography variant="body2" color="text.secondary" align="center" sx={{ py: 3 }}>
                No representatives listed.
              </Typography>
            )}
          </Stack>
        );
      } else {
        // Desktop View Mode: Table View
        representativesContent = siteDetails.representatives && siteDetails.representatives.length > 0 ? (
          <TableContainer component={Paper} variant="outlined" sx={{ ...minimalistTableSx(theme), overflowX: 'auto' }}>
            <Table size="small" aria-label="representatives table">
              <TableHead>
                <TableRow>
                  <TableCell sx={{ width: '5%' }}>No.</TableCell>
                  <TableCell sx={{ width: '35%' }}>Name</TableCell>
                  <TableCell sx={{ width: '30%' }}>Unit</TableCell>
                  <TableCell sx={{ width: '30%' }}>Contact No</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>{/* Ensure no leading/trailing whitespace within TableBody */}
                {siteDetails.representatives.map((rep, index) => (
                  <TableRow key={index} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>{rep.name}</TableCell>
                    <TableCell>{rep.unit}</TableCell>
                    <TableCell>{rep.contact_no}</TableCell>
                  </TableRow>
                ))}{/* Ensure no leading/trailing whitespace within TableBody */}
              </TableBody>
            </Table>
          </TableContainer>
        ) : (
          <Typography variant="body2" color="text.secondary"> {/* Lighter text for empty state */}
            No representatives listed.
          </Typography>
        );
      }
    }
  }
  // --- End Content Variables ---

  // --- Define Checklist Content Variable ---
  let checklistsContent = null;
  if (siteDetails) { // Ensure siteDetails exists
    if (editMode) {
      if (isMobile) {
        // Mobile Edit Mode: Stacked View
        checklistsContent = (
          <Stack spacing={2}>
            {formData.checklists && formData.checklists.length > 0 ? (
              formData.checklists.map((checklist, index) => (
                <Paper key={index} variant="outlined" sx={{ p: 2 }}>
                  <Stack spacing={1.5}>
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                      <Typography variant="subtitle2">Checklist Item {index + 1}</Typography>
                      <IconButton
                        size="small"
                        onClick={() => handleRemoveChecklist(index)}
                        color="error"
                      >
                        <Delete fontSize="small" />
                      </IconButton>
                    </Stack>
                    <TextField
                      label="Requirement"
                      fullWidth
                      size="small"
                      value={checklist.requirement || ''}
                      onChange={(e) => handleChecklistChange(index, 'requirement', e.target.value)}
                      variant="outlined"
                      sx={minimalistTextFieldSx(theme)}
                      multiline
                      minRows={2}
                    />
                    <TextField
                      label="Status"
                      fullWidth
                      size="small"
                      value={checklist.status || ''}
                      onChange={(e) => handleChecklistChange(index, 'status', e.target.value)}
                      variant="outlined"
                      sx={minimalistTextFieldSx(theme)}
                    />
                  </Stack>
                </Paper>
              ))
            ) : (
              <Typography variant="body2" color="text.secondary" align="center" sx={{ py: 3 }}>
                No checklist items added yet.
              </Typography>
            )}
            <Button
              variant="outlined"
              size="small"
              startIcon={<Add />}
              onClick={handleAddChecklist}
              sx={{ alignSelf: 'flex-start' }}
            >
              Add Checklist Item
            </Button>
          </Stack>
        );
      } else {
        // Desktop Edit Mode: Table View
        checklistsContent = (
          <Stack spacing={2}>
            <TableContainer component={Paper} variant="outlined" sx={{ ...minimalistTableSx(theme), overflowX: 'auto' }}>
              <Table size="small" aria-label="checklists table">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ width: '5%' }}>No.</TableCell>
                    <TableCell sx={{ width: '45%' }}>Requirement</TableCell>
                    <TableCell sx={{ width: '40%' }}>Status</TableCell>
                    <TableCell sx={{ width: '10%', textAlign: 'right' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>{/* Ensure no leading/trailing whitespace within TableBody */}
                  {formData.checklists && formData.checklists.length > 0 ? (
                    formData.checklists.map((checklist, index) => (
                      <TableRow key={index} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                        <TableCell>{index + 1}</TableCell>
                        <TableCell>
                          <TextField
                            fullWidth
                            size="small"
                            value={checklist.requirement || ''}
                            onChange={(e) => handleChecklistChange(index, 'requirement', e.target.value)}
                            variant="outlined"
                            sx={minimalistTextFieldSx(theme)}
                            multiline
                            minRows={1} // Allow multiline but start small
                          />
                        </TableCell>
                        <TableCell>
                          <TextField
                            fullWidth
                            size="small"
                            value={checklist.status || ''}
                            onChange={(e) => handleChecklistChange(index, 'status', e.target.value)}
                            variant="outlined"
                            sx={minimalistTextFieldSx(theme)}
                          />
                        </TableCell>
                        <TableCell align="right">
                          <IconButton
                            size="small"
                            onClick={() => handleRemoveChecklist(index)}
                            color="error"
                          >
                            <Delete fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={4} align="center" sx={{ py: 3 }}>
                        No checklists added
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            <Button
              variant="outlined"
              size="small"
              startIcon={<Add />}
              onClick={handleAddChecklist}
              sx={{ alignSelf: 'flex-start' }}
            >
              Add Checklist Item
            </Button>
          </Stack>
        );
      }
    } else {
      // View Mode - conditional based on device
      if (isMobile) {
        // Mobile View Mode: Stacked View
        checklistsContent = (
          <Stack spacing={2}>
            {siteDetails.checklists && siteDetails.checklists.length > 0 ? (
              siteDetails.checklists.map((checklist, index) => (
                <Paper key={index} variant="outlined" sx={{ p: 2 }}>
                  <Stack spacing={1}>
                    <Typography variant="subtitle2">Checklist Item {index + 1}</Typography>
                    <Stack spacing={0.5}>
                      <Typography variant="caption" color="text.secondary">Requirement:</Typography>
                      <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>{checklist.requirement || '-'}</Typography>
                    </Stack>
                    <Stack spacing={0.5}>
                      <Typography variant="caption" color="text.secondary">Status:</Typography>
                      <Typography variant="body2">{checklist.status || '-'}</Typography>
                    </Stack>
                  </Stack>
                </Paper>
              ))
            ) : (
              <Typography variant="body2" color="text.secondary" align="center" sx={{ py: 3 }}>
                No checklist items available.
              </Typography>
            )}
          </Stack>
        );
      } else {
        // Desktop View Mode: Table View
        checklistsContent = siteDetails.checklists && siteDetails.checklists.length > 0 ? (
          <TableContainer component={Paper} variant="outlined" sx={{ ...minimalistTableSx(theme), overflowX: 'auto' }}>
            <Table size="small" aria-label="checklists table">
              <TableHead>
                <TableRow>
                   <TableCell sx={{ width: '5%' }}>No.</TableCell>
                   <TableCell sx={{ width: '55%' }}>Requirement</TableCell>
                   <TableCell sx={{ width: '40%' }}>Status</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>{/* Ensure no leading/trailing whitespace within TableBody */}
                {siteDetails.checklists.map((checklist, index) => (
                  <TableRow key={index} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell sx={{ whiteSpace: 'pre-wrap' }}>{checklist.requirement}</TableCell> {/* Allow wrap */}
                    <TableCell>{checklist.status}</TableCell>
                  </TableRow>
                ))}{/* Ensure no leading/trailing whitespace within TableBody */}
              </TableBody>
            </Table>
          </TableContainer>
        ) : (
          <Typography variant="body2" color="text.secondary">
            No checklists available.
          </Typography>
        );
      }
    }
  }

  // --- Define Actions Content Variable ---
  let actionsContent = null;
  if (siteDetails) { // Ensure siteDetails exists
    if (editMode) {
      if (isMobile) {
        // Mobile Edit Mode: Stacked View
        actionsContent = (
          <Stack spacing={2}>
            {formData.actions && formData.actions.length > 0 ? (
              formData.actions.map((action, index) => (
                <Paper key={index} variant="outlined" sx={{ p: 2 }}>
                  <Stack spacing={1.5}>
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                      <Typography variant="subtitle2">Action Item {index + 1}</Typography>
                      <IconButton
                        size="small"
                        onClick={() => handleRemoveAction(index)}
                        color="error"
                      >
                        <Delete fontSize="small" />
                      </IconButton>
                    </Stack>
                    <TextField
                      label="Action"
                      fullWidth
                      size="small"
                      value={action.action || ''}
                      onChange={(e) => handleActionChange(index, 'action', e.target.value)}
                      variant="outlined"
                      sx={minimalistTextFieldSx(theme)}
                      multiline
                      minRows={2}
                    />
                    <TextField
                      label="Responsibility"
                      fullWidth
                      size="small"
                      value={action.responsibility || ''}
                      onChange={(e) => handleActionChange(index, 'responsibility', e.target.value)}
                      variant="outlined"
                      sx={minimalistTextFieldSx(theme)}
                    />
                  </Stack>
                </Paper>
              ))
            ) : (
              <Typography variant="body2" color="text.secondary" align="center" sx={{ py: 3 }}>
                No action items added yet.
              </Typography>
            )}
            <Button
              variant="outlined"
              size="small"
              startIcon={<Add />}
              onClick={handleAddAction}
              sx={{ alignSelf: 'flex-start' }}
            >
              Add Action Item
            </Button>
          </Stack>
        );
      } else {
        // Desktop Edit Mode: Table View
        actionsContent = (
          <Stack spacing={2}>
            <TableContainer component={Paper} variant="outlined" sx={{ ...minimalistTableSx(theme), overflowX: 'auto' }}>
              <Table size="small" aria-label="actions table">
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ width: '5%' }}>No.</TableCell>
                    <TableCell sx={{ width: '45%' }}>Action</TableCell>
                    <TableCell sx={{ width: '40%' }}>Responsibility</TableCell>
                    <TableCell sx={{ width: '10%', textAlign: 'right' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>{/* Ensure no leading/trailing whitespace within TableBody */}
                  {formData.actions && formData.actions.length > 0 ? (
                    formData.actions.map((action, index) => (
                      <TableRow key={index} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                        <TableCell>{index + 1}</TableCell>
                        <TableCell>
                          <TextField
                            fullWidth
                            size="small"
                            value={action.action || ''}
                            onChange={(e) => handleActionChange(index, 'action', e.target.value)}
                            variant="outlined"
                            sx={minimalistTextFieldSx(theme)}
                            multiline
                            minRows={1}
                          />
                        </TableCell>
                        <TableCell>
                          <TextField
                            fullWidth
                            size="small"
                            value={action.responsibility || ''}
                            onChange={(e) => handleActionChange(index, 'responsibility', e.target.value)}
                            variant="outlined"
                            sx={minimalistTextFieldSx(theme)}
                          />
                        </TableCell>
                        <TableCell align="right">
                          <IconButton
                            size="small"
                            onClick={() => handleRemoveAction(index)}
                            color="error"
                          >
                            <Delete fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={4} align="center" sx={{ py: 3 }}>
                        No actions added
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            <Button
              variant="outlined"
              size="small"
              startIcon={<Add />}
              onClick={handleAddAction}
              sx={{ alignSelf: 'flex-start' }}
            >
              Add Action Item
            </Button>
          </Stack>
        );
      }
    } else {
      // View Mode - conditional based on device
      if (isMobile) {
        // Mobile View Mode: Stacked View
        actionsContent = (
          <Stack spacing={2}>
            {siteDetails.actions && siteDetails.actions.length > 0 ? (
              siteDetails.actions.map((action, index) => (
                <Paper key={index} variant="outlined" sx={{ p: 2 }}>
                  <Stack spacing={1}>
                    <Typography variant="subtitle2">Action Item {index + 1}</Typography>
                    <Stack spacing={0.5}>
                      <Typography variant="caption" color="text.secondary">Action:</Typography>
                      <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>{action.action || '-'}</Typography>
                    </Stack>
                    <Stack spacing={0.5}>
                      <Typography variant="caption" color="text.secondary">Responsibility:</Typography>
                      <Typography variant="body2">{action.responsibility || '-'}</Typography>
                    </Stack>
                  </Stack>
                </Paper>
              ))
            ) : (
              <Typography variant="body2" color="text.secondary" align="center" sx={{ py: 3 }}>
                No action items available.
              </Typography>
            )}
          </Stack>
        );
      } else {
        // Desktop View Mode: Table View
        actionsContent = siteDetails.actions && siteDetails.actions.length > 0 ? (
          <TableContainer component={Paper} variant="outlined" sx={{ ...minimalistTableSx(theme), overflowX: 'auto' }}>
            <Table size="small" aria-label="actions table">
              <TableHead>
                <TableRow>
                  <TableCell sx={{ width: '5%' }}>No.</TableCell>
                  <TableCell sx={{ width: '50%' }}>Action</TableCell>
                  <TableCell sx={{ width: '45%' }}>Responsibility</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>{/* Ensure no leading/trailing whitespace within TableBody */}
                {siteDetails.actions.map((action, index) => (
                  <TableRow key={index} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell sx={{ whiteSpace: 'pre-wrap' }}>{action.action}</TableCell> {/* Allow wrap */}
                    <TableCell>{action.responsibility}</TableCell>
                  </TableRow>
                ))}{/* Ensure no leading/trailing whitespace within TableBody */}
              </TableBody>
            </Table>
          </TableContainer>
        ) : (
          <Typography variant="body2" color="text.secondary">
            No actions defined.
          </Typography>
        );
      }
    }
  }
  // --- End Content Variables ---

  if (error) {
    return (
      <div className="container rounded-s bg-white p-4 text-black dark:bg-gray-600 dark:text-white">
        <Alert severity="error">{error}</Alert>
      </div>
    );
  }

  if (!siteDetails) {
    return (
      <div className="container rounded-s bg-white p-4 text-black dark:bg-gray-600 dark:text-white">
        <Alert severity="warning">No site details found</Alert>
      </div>
    );
  }

  return (
    <>
      <div className=" bg-primary p-4 text-white dark:bg-gray-600 dark:text-white">
        <h2 className="mb-6 text-lg font-bold">
          Site Details{' '}
          {siteDetails &&
            ((siteDetails.sphere_id && `(Site ID: ${siteDetails.sphere_id})`) ||
              (siteDetails.dtc_no && `(Site ID: ${siteDetails.dtc_no})`))}
        </h2>
      </div>

      <div className="container p-4">
        <div className="rounded-s bg-white p-4 text-black dark:bg-gray-600 dark:text-white">
          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              alignItems: { xs: 'flex-start', sm: 'center' },
              justifyContent: 'space-between',
              gap: 2,
            }}
            className="mb-4"
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: { xs: 'column', md: 'row' },
                alignItems: { xs: 'flex-start', md: 'center' },
                gap: 1,
                width: { xs: '100%', sm: 'auto' },
              }}
            >
              <Button
                startIcon={<ArrowBack />}
                onClick={handleBack}
                className="dark:text-white"
                variant="outlined"
                fullWidth={false}
                sx={{ mb: { xs: 1, md: 0 }, width: { xs: '100%', md: 'auto' } }}
              >
                Back to Site List
              </Button>

              <Button
                startIcon={<Edit />}
                onClick={handleEditClick}
                className="dark:text-white"
                variant="contained"
                color={editMode ? 'secondary' : 'primary'}
                sx={{ mb: { xs: 1, md: 0 }, width: { xs: '100%', md: 'auto' } }}
              >
                {editMode ? 'Exit Edit Mode' : 'Edit Mode'}
              </Button>
              {editMode && (
                <>
                  <Button
                    onClick={handleSave}
                    className="dark:text-white"
                    variant="contained"
                    color="success"
                    disabled={saveLoading}
                    sx={{ mb: { xs: 1, md: 0 }, width: { xs: '100%', md: 'auto' } }}
                  >
                    {saveLoading ? <CircularProgress size={24} /> : 'Save Changes'}
                  </Button>
                  <Button
                    onClick={handleCancelEdit}
                    className="text-white"
                    variant="contained"
                    color="warning"
                    sx={{ mb: { xs: 1, md: 0 }, width: { xs: '100%', md: 'auto' } }}
                  >
                    Cancel
                  </Button>
                  <Button
                    startIcon={<Delete />}
                    onClick={handleDeleteClick}
                    className="dark:text-white"
                    variant="contained"
                    color="error"
                    sx={{ mb: { xs: 1, md: 0 }, width: { xs: '100%', md: 'auto' } }}
                  >
                    Delete
                  </Button>
                </>
              )}
              {!editMode && (
                <ExportOptions
                  siteDetails={{
                    ...siteDetails,
                    findings, // Pass the findings from the dedicated API using property shorthand
                    attachments, // Pass the attachments data for finding images
                  }}
                  buttonProps={{
                    className: 'dark:text-white',
                    sx: { mb: { xs: 1, md: 0 }, width: { xs: '100%', md: 'auto' } },
                  }}
                  onSuccess={(type) =>
                    enqueueSnackbar(`Site details exported to ${type} successfully`)
                  }
                  onError={(type, err) => {
                    console.error(`Error exporting to ${type}:`, err);
                    enqueueSnackbar(`Failed to export site details to ${type}`, {
                      variant: 'error',
                    });
                  }}
                />
              )}
            </Box>

            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2,
                width: { xs: '100%', sm: 'auto' },
                justifyContent: { xs: 'flex-start', sm: 'flex-end' },
              }}
            >
              <Box sx={{ textAlign: { xs: 'left', sm: 'right' }, width: '100%' }}>
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: { xs: 'flex-start', sm: 'flex-end' },
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  <Typography
                    variant="caption"
                    color="textSecondary"
                    className="dark:text-gray-300"
                    sx={{ minWidth: 'fit-content' }} // Prevent label wrapping
                  >
                    Created:
                  </Typography>
                  <Typography variant="caption" className="dark:text-white">
                    {siteDetails.created_by ? `${siteDetails.created_by} on ` : ''}
                    {formatDateTime(siteDetails.created_at)}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: { xs: 'flex-start', sm: 'flex-end' },
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  <Typography
                    variant="caption"
                    color="textSecondary"
                    className="dark:text-gray-300"
                    sx={{ minWidth: 'fit-content' }} // Prevent label wrapping
                  >
                    Updated:
                  </Typography>
                  <Typography variant="caption" className="dark:text-white">
                    {siteDetails.updated_by ? `${siteDetails.updated_by} on ` : ''}
                    {formatDateTime(siteDetails.updated_at)}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>{' '}
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Paper elevation={3} className="p-4 dark:bg-gray-700">
                <Typography variant="h6" className="mb-2 dark:text-white">
                  Site Information
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6} md={4} lg={2}>
                    <EditableField
                      label="Sphere ID"
                      name="sphere_id"
                      value={siteDetails.sphere_id}
                      editMode={editMode}
                      formData={formData}
                      onChange={handleInputChange}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={2}>
                    <EditableField
                      label="DTC No"
                      name="dtc_no"
                      value={siteDetails.dtc_no}
                      editMode={editMode}
                      formData={formData}
                      onChange={handleInputChange}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={2}>
                    <EditableField
                      label="Site ID"
                      name="site_id"
                      value={siteDetails.site_id}
                      editMode={editMode}
                      formData={formData}
                      onChange={handleInputChange}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={2}>
                    <EditableField
                      label="Site Name"
                      name="site_name"
                      value={siteDetails.site_name}
                      editMode={editMode}
                      formData={formData}
                      onChange={handleInputChange}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={2}>
                    <EditableField
                      label="Site Address"
                      name="site_address"
                      value={siteDetails.site_address || 'n/a'}
                      editMode={editMode}
                      formData={formData}
                      onChange={handleInputChange}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={2}>
                    <EditableField
                      label="Lat / Long"
                      name="lat_long"
                      value={siteDetails.lat_long || 'n/a'}
                      editMode={editMode}
                      formData={formData}
                      onChange={handleInputChange}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={2}>
                    <EditableField
                      label="SOF No"
                      name="sof_no"
                      value={siteDetails.sof_no}
                      editMode={editMode}
                      formData={formData}
                      onChange={handleInputChange}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={2}>
                    <EditableField
                      label="Nova ID / OD No"
                      name="nova_id"
                      value={siteDetails.nova_id}
                      editMode={editMode}
                      formData={formData}
                      onChange={handleInputChange}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={2}>
                    <EditableField
                      label="Product & SLA/SLG"
                      name="product_sla"
                      value={siteDetails.product_sla}
                      editMode={editMode}
                      formData={formData}
                      onChange={handleInputChange}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={2}>
                    <EditableField
                      label="Exchange"
                      name="exchange"
                      value={siteDetails.exchange}
                      editMode={editMode}
                      formData={formData}
                      onChange={handleInputChange}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={2}>
                    {editMode ? (
                      isMobile ? (
                        // Mobile Edit Mode: TextField triggering Drawer
                        <>
                          <TextField
                            label="Workgroup"
                            value={formData.workgroup || ''}
                            onClick={() => setWorkgroupDrawerOpen(true)}
                            variant="outlined"
                            fullWidth
                            size="small"
                            InputProps={{
                              readOnly: true, // Make it read-only visually
                            }}
                            sx={{ ...minimalistTextFieldSx(theme), cursor: 'pointer' }} // Add pointer cursor
                          />
                          <Typography
                            variant="caption"
                            className="mt-1 block text-gray-500 dark:text-gray-400"
                          >
                            Workgroup for access control
                          </Typography>
                          <Drawer
                            anchor="bottom"
                            open={workgroupDrawerOpen}
                            onClose={() => setWorkgroupDrawerOpen(false)}
                            PaperProps={{ sx: { maxHeight: '50vh' } }} // Limit height
                          >
                            <AppBar position="static" sx={{ mb: 1, backgroundColor: 'background.paper', color: 'text.primary', boxShadow: 'none', borderBottom: (theme) => `1px solid ${theme.palette.divider}` }}>
                              <Toolbar variant="dense">
                                <Typography variant="h6" sx={{ flexGrow: 1 }}>
                                  Select Workgroup
                                </Typography>
                                <Button color="inherit" onClick={() => setWorkgroupDrawerOpen(false)}>
                                  Done
                                </Button>
                              </Toolbar>
                            </AppBar>
                            <List>
                              {[
                                { value: '', label: 'Select a workgroup' },
                                { value: 'MC1', label: 'MC1' },
                                { value: 'FCR', label: 'FCR' },
                                { value: 'GLOBAL', label: 'GLOBAL' },
                              ].map((option) => (
                                <ListItem key={option.value} disablePadding>
                                  <ListItemButton
                                    selected={formData.workgroup === option.value}
                                    onClick={() => {
                                      handleInputChange({ target: { name: 'workgroup', value: option.value } });
                                      setWorkgroupDrawerOpen(false);
                                    }}
                                  >
                                    <ListItemText primary={option.label} primaryTypographyProps={{ sx: { textAlign: 'center' } }} />
                                  </ListItemButton>
                                </ListItem>
                              ))}
                            </List>
                          </Drawer>
                        </>
                      ) : (
                        // Desktop Edit Mode: Standard Select
                        <>
                          <FormControl fullWidth size="small" variant="outlined" sx={minimalistSelectSx(theme).formControl}>
                            <InputLabel id="workgroup-select-label">Workgroup</InputLabel>
                            <Select
                              labelId="workgroup-select-label"
                              id="workgroup-select"
                              name="workgroup"
                              value={formData.workgroup || ''}
                              onChange={handleInputChange}
                              label="Workgroup"
                              sx={minimalistSelectSx(theme)}
                            >
                              <MenuItem value=""><em>Select a workgroup</em></MenuItem>
                              <MenuItem value="MC1">MC1</MenuItem>
                              <MenuItem value="FCR">FCR</MenuItem>
                              <MenuItem value="GLOBAL">GLOBAL</MenuItem>
                            </Select>
                          </FormControl>
                          <Typography
                            variant="caption"
                            className="mt-1 block text-gray-500 dark:text-gray-400"
                          >
                            Workgroup for access control
                          </Typography>
                        </>
                      )
                    ) : (
                      // View Mode
                      <div>
                        <Typography
                          variant="caption"
                          className="mb-1 block text-gray-600 dark:text-gray-300"
                        >
                          Workgroup
                        </Typography>
                        <Typography variant="body2" className="dark:text-white">
                          {siteDetails.workgroup || '-'}
                        </Typography>
                      </div>
                    )}
                  </Grid>
                </Grid>
              </Paper>
            </Grid>

            <Grid item xs={12} md={12}>
              <Paper elevation={3} className="p-4 dark:bg-gray-700">
                <Typography variant="h6" className="mb-2 dark:text-white">
                  Customer Details
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6} md={4} lg={2}>
                    <EditableField
                      label="Customer Name"
                      name="customer_name"
                      value={siteDetails.customer_name}
                      editMode={editMode}
                      formData={formData}
                      onChange={handleInputChange}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={2}>
                    <EditableField
                      label="End Customer"
                      name="end_customer"
                      value={siteDetails.end_customer}
                      editMode={editMode}
                      formData={formData}
                      onChange={handleInputChange}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={2}>
                    <EditableField
                      label="Survey Date"
                      name="date"
                      // Format value in view mode, pass raw value in edit mode
                      value={editMode ? formData.date : formatDateToDDMMYYYY(siteDetails.date)}
                      type="date"
                      editMode={editMode}
                      // Pass raw value to formData for editing
                      formData={editMode ? formData : { date: siteDetails.date }}
                      onChange={handleInputChange}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={2}>
                    <EditableField
                      label="Time"
                      name="time"
                      // Format value in view mode, pass raw value in edit mode
                      value={editMode ? formData.time : formatTimeToAMPM(siteDetails.time)}
                      type="time"
                      editMode={editMode}
                      // Pass raw value to formData for editing
                      formData={editMode ? formData : { time: siteDetails.time }}
                      onChange={handleInputChange}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={2}>
                    <EditableField
                      label="Requirement"
                      name="requirement"
                      value={siteDetails.requirement}
                      editMode={editMode}
                      formData={formData}
                      onChange={handleInputChange}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={2}>
                    <EditableField
                      label="Project Manager"
                      name="pm"
                      value={siteDetails.pm}
                      editMode={editMode}
                      formData={formData}
                      onChange={handleInputChange}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="subtitle1" gutterBottom color="text.secondary"> {/* Use subtitle for sub-section */}
                      Representatives
                    </Typography>
                    {representativesContent} {/* Render the variable here */}
                  </Grid>
                </Grid>
              </Paper>
            </Grid>
            <Grid item xs={12} md={12}>
              <Paper elevation={3} className="p-4 dark:bg-gray-700">
                <Typography variant="h6" className="mb-2 dark:text-white">
                  Findings & Actions
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        mb: 2,
                      }}
                    >
                      <Typography
                        variant="body2"
                        color="textSecondary"
                        className="dark:text-gray-300"
                      >
                        Findings
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        {findingsEditMode && (
                          <Button
                            variant="outlined"
                            size="small"
                            onClick={handleOpenAddFindingDialog}
                            className="dark:text-white"
                          >
                            Add Finding
                          </Button>
                        )}
                        <Button
                          startIcon={<Edit />}
                          onClick={handleFindingsEditClick}
                          className="dark:text-white"
                          variant="outlined"
                          color={findingsEditMode ? 'secondary' : 'primary'}
                          size="small"
                        >
                          {findingsEditMode ? 'Exit Edit Mode' : 'Edit Findings'}
                        </Button>
                      </Box>
                    </Box>

                    {/* Findings Display: Table (Desktop) or Stack (Mobile) */}
                    {findingsLoading ? (
                      <Box display="flex" justifyContent="center" my={2}> <CircularProgress size={24} /> </Box>
                    ) : findingsError ? (
                      <Alert severity="error" className="mb-2"> {findingsError} </Alert>
                    ) : isMobile ? (
                      // Mobile View: Stacked Cards
                      <Stack spacing={2} sx={{ mb: 2 }}>
                        {findings && findings.length > 0 ? (
                          findings.map((finding, index) => (
                            <Paper key={finding.id || index} variant="outlined" sx={{ p: 2 }}>
                              <Stack spacing={1}>
                                <Stack direction="row" justifyContent="space-between" alignItems="center">
                                  <Typography variant="subtitle2">Finding {index + 1}</Typography>
                                  {findingsEditMode && (
                                    <Box>
                                      <IconButton
                                        size="small"
                                        onClick={() => handleOpenEditFindingDialog(finding)}
                                        sx={{ mr: 0.5 }}
                                      >
                                        <Edit fontSize="small" color="primary" />
                                      </IconButton>
                                      <IconButton
                                        size="small"
                                        onClick={() => handleDeleteFinding(finding.id)}
                                        color="error"
                                      >
                                        <Delete fontSize="small" />
                                      </IconButton>
                                    </Box>
                                  )}
                                </Stack>
                                <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                                  {finding.description || '-'}
                                </Typography>
                              </Stack>
                            </Paper>
                          ))
                        ) : (
                          <Typography variant="body2" color="text.secondary" align="center" sx={{ py: 3 }}>
                            No findings recorded.
                          </Typography>
                        )}
                      </Stack>
                    ) : (
                      // Desktop View: Table
                      <TableContainer
                        component={Paper}
                        variant="outlined"
                        sx={{ ...minimalistTableSx(theme), overflowX: 'auto', mb: 2 }}
                      >
                        <Table size="small" aria-label="findings table">
                          <TableHead>
                            <TableRow>
                              <TableCell sx={{ width: '5%' }}>No.</TableCell>
                              <TableCell sx={{ width: findingsEditMode ? '75%' : '95%' }}>Description</TableCell>
                              {findingsEditMode && (
                                <TableCell sx={{ width: '20%', textAlign: 'right' }}>Actions</TableCell>
                              )}
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {findings && findings.length > 0 ? (
                              findings.map((finding, index) => (
                                <TableRow key={finding.id || index}>
                                  <TableCell>{index + 1}</TableCell>
                                  <TableCell>{finding.description}</TableCell>
                                  {findingsEditMode && (
                                    <TableCell align="right">
                                      <Box display="flex" gap={1} justifyContent="flex-end">
                                        <IconButton size="small" onClick={() => handleOpenEditFindingDialog(finding)}>
                                          <Edit color="primary" fontSize="small" />
                                        </IconButton>
                                        <IconButton size="small" onClick={() => handleDeleteFinding(finding.id)}>
                                          <Delete color="error" fontSize="small" />
                                        </IconButton>
                                      </Box>
                                    </TableCell>
                                  )}
                                </TableRow>
                              ))
                            ) : (
                              <TableRow>
                                <TableCell colSpan={findingsEditMode ? 3 : 2} align="center">
                                  No findings recorded
                                </TableCell>
                              </TableRow>
                            )}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    )}
                  </Grid>
                  <Grid item xs={12}>
                    <Typography
                      variant="body2"
                      color="textSecondary"
                      className="mb-2 dark:text-gray-300"
                    >
                      Checklists
                    </Typography>
                    {checklistsContent} {/* Render the variable here */}
                  </Grid>
                  <Grid item xs={12}>
                    <Typography
                      variant="body2"
                      color="textSecondary"
                      className="dark:text-gray-300"
                    >
                      Actions
                    </Typography>
                    {actionsContent} {/* Render the variable here */}
                  </Grid>
                  <Grid item xs={12} sm={6} md={4} lg={2}>
                    <EditableField
                      label="CME Status"
                      name="cme_status"
                      value={siteDetails.cme_status}
                      editMode={editMode}
                      formData={formData}
                      onChange={handleInputChange}
                    />
                  </Grid>
                </Grid>
              </Paper>
            </Grid>

            {/* Attachments Grid */}
            <Grid item xs={12}>
              <Paper elevation={3} className="p-4 dark:bg-gray-700">
                <Typography variant="h6" className="mb-2 dark:text-white">
                  Attachments
                </Typography>
                <Grid container spacing={2}>
                  {editMode && (
                    <Grid item xs={12} sm={6} md={4}>
                      <Box className="mb-4">
                        {/* Attachment Type Select / Drawer */}
                        {isMobile ? (
                          <>
                            <TextField
                              label="Attachment Type"
                              value={selectedAttachmentType || ''}
                              onClick={() => setAttachmentTypeDrawerOpen(true)}
                              variant="outlined"
                              fullWidth
                              size="small"
                              InputProps={{ readOnly: true }}
                              sx={{ ...minimalistTextFieldSx(theme), cursor: 'pointer', mb: 2 }}
                            />
                            <Drawer
                              anchor="bottom"
                              open={attachmentTypeDrawerOpen}
                              onClose={() => setAttachmentTypeDrawerOpen(false)}
                              PaperProps={{ sx: { maxHeight: '60vh' } }}
                            >
                              <AppBar position="static" sx={{ mb: 1, backgroundColor: 'background.paper', color: 'text.primary', boxShadow: 'none', borderBottom: (theme) => `1px solid ${theme.palette.divider}` }}>
                                <Toolbar variant="dense">
                                  <Typography variant="h6" sx={{ flexGrow: 1 }}>
                                    Select Attachment Type
                                  </Typography>
                                  <Button color="inherit" onClick={() => setAttachmentTypeDrawerOpen(false)}>
                                    Done
                                  </Button>
                                </Toolbar>
                              </AppBar>
                              <List>
                                <ListItem disablePadding>
                                    <ListItemButton
                                        selected={selectedAttachmentType === ''}
                                        onClick={() => {
                                            setSelectedAttachmentType('');
                                            setAttachmentTypeDrawerOpen(false);
                                        }}
                                    >
                                        <ListItemText primary={<em>Select a type</em>} primaryTypographyProps={{ sx: { textAlign: 'center' } }} />
                                    </ListItemButton>
                                </ListItem>
                                {ATTACHMENT_TYPES.map((type) => (
                                  <ListItem key={type} disablePadding>
                                    <ListItemButton
                                      selected={selectedAttachmentType === type}
                                      onClick={() => {
                                        setSelectedAttachmentType(type);
                                        setAttachmentTypeDrawerOpen(false);
                                      }}
                                    >
                                      <ListItemText primary={type} primaryTypographyProps={{ sx: { textAlign: 'center' } }} />
                                    </ListItemButton>
                                  </ListItem>
                                ))}
                              </List>
                            </Drawer>
                            
                            {/* Custom Attachment Type for Mobile (when 'Other' is selected) */}
                            {selectedAttachmentType === 'Other' && (
                              <TextField
                                fullWidth
                                size="small"
                                label="Specify Attachment Type"
                                value={customAttachmentType}
                                onChange={(e) => setCustomAttachmentType(e.target.value)}
                                variant="outlined"
                                className="mb-2 dark:text-white"
                                placeholder="Enter custom attachment type"
                                sx={minimalistTextFieldSx(theme)}
                                required
                              />
                            )}
                          </>
                        ) : (
                          <FormControl fullWidth variant="outlined" size="small" className="mb-2">
                            <InputLabel id="attachment-type-label" className="dark:text-white">
                              Attachment Type
                            </InputLabel>
                            <Select
                              labelId="attachment-type-label"
                              id="attachment-type"
                              value={selectedAttachmentType}
                              onChange={handleAttachmentTypeChange}
                              label="Attachment Type"
                              className="dark:text-white"
                              sx={minimalistSelectSx(theme)}
                              // sx={{
                              //   '& .MuiInputBase-input': {
                              //     color: 'inherit',
                              //   },
                              //   '& .MuiOutlinedInput-root': {
                              //     '& fieldset': {
                              //       borderColor: 'inherit',
                              //     },
                              //     '&:hover fieldset': {
                              //       borderColor: 'inherit',
                              //     },
                              //     '&.Mui-focused fieldset': {
                              //       borderColor: 'inherit',
                              //     },
                              //   },
                              // }}
                            >
                              <MenuItem value="">
                                <em>Select a type</em>
                              </MenuItem>
                              {ATTACHMENT_TYPES.map((type) => (
                                <MenuItem key={type} value={type}>
                                  {type}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        )}

                        {/* Custom Attachment Type Input (only when 'Other' is selected) */}
                        {selectedAttachmentType === 'Other' && (
                          <TextField
                            fullWidth
                            size="small"
                            label="Specify Attachment Type"
                            value={customAttachmentType}
                            onChange={(e) => setCustomAttachmentType(e.target.value)}
                            variant="outlined"
                            className="mb-2 dark:text-white"
                            placeholder="Enter custom attachment type"
                            sx={minimalistTextFieldSx(theme)}
                            required
                          />
                        )}

                        {/* Finding Selection / Drawer */}
                        {isMobile ? (
                           <>
                            <TextField
                              label="Associated Finding (Optional)"
                              value={selectedFinding ? (findings.find(f => f.id === selectedFinding)?.description || '').substring(0, 50) + ((findings.find(f => f.id === selectedFinding)?.description || '').length > 50 ? '...' : '') : 'None (Site-level attachment)'}
                              onClick={() => setFindingUploadDrawerOpen(true)}
                              variant="outlined"
                              fullWidth
                              size="small"
                              InputProps={{ readOnly: true }}
                              sx={{ ...minimalistTextFieldSx(theme), cursor: 'pointer', mb: 2 }}
                            />
                            <Drawer
                              anchor="bottom"
                              open={findingUploadDrawerOpen}
                              onClose={() => setFindingUploadDrawerOpen(false)}
                              PaperProps={{ sx: { maxHeight: '60vh' } }}
                            >
                              <AppBar position="static" sx={{ mb: 1, backgroundColor: 'background.paper', color: 'text.primary', boxShadow: 'none', borderBottom: (theme) => `1px solid ${theme.palette.divider}` }}>
                                <Toolbar variant="dense">
                                  <Typography variant="h6" sx={{ flexGrow: 1 }}>
                                    Select Associated Finding
                                  </Typography>
                                  <Button color="inherit" onClick={() => setFindingUploadDrawerOpen(false)}>
                                    Done
                                  </Button>
                                </Toolbar>
                              </AppBar>
                              <List>
                                <ListItem disablePadding>
                                  <ListItemButton
                                    selected={selectedFinding === ''}
                                    onClick={() => {
                                      setSelectedFinding('');
                                      setFindingUploadDrawerOpen(false);
                                    }}
                                  >
                                    <ListItemText primary={<em>None (Site-level attachment)</em>} primaryTypographyProps={{ sx: { textAlign: 'center' } }} />
                                  </ListItemButton>
                                </ListItem>
                                {findings.map((finding) => (
                                  <ListItem key={finding.id} disablePadding>
                                    <ListItemButton
                                      selected={selectedFinding === finding.id}
                                      onClick={() => {
                                        setSelectedFinding(finding.id);
                                        setFindingUploadDrawerOpen(false);
                                      }}
                                    >
                                      <ListItemText
                                        primary={finding.description}
                                        primaryTypographyProps={{
                                          noWrap: true,
                                          style: {
                                            textOverflow: 'ellipsis',
                                            overflow: 'hidden',
                                            whiteSpace: 'nowrap',
                                            textAlign: 'center' // Center text
                                          },
                                        }}
                                      />
                                    </ListItemButton>
                                  </ListItem>
                                ))}
                              </List>
                            </Drawer>
                          </>
                        ) : (
                          <FormControl fullWidth variant="outlined" size="small" className="mb-2">
                            <InputLabel id="finding-label" className="dark:text-white">
                              Associated Finding (Optional)
                            </InputLabel>
                            <Select
                              labelId="finding-label"
                              id="finding-select"
                              value={selectedFinding}
                              onChange={handleFindingChange}
                              label="Associated Finding (Optional)"
                              className="dark:text-white"
                              sx={minimalistSelectSx(theme)}
                              // sx={{
                              //   '& .MuiInputBase-input': {
                              //     color: 'inherit',
                              //   },
                              //   '& .MuiOutlinedInput-root': {
                              //     '& fieldset': {
                              //       borderColor: 'inherit',
                              //     },
                              //     '&:hover fieldset': {
                              //       borderColor: 'inherit',
                              //     },
                              //     '&.Mui-focused fieldset': {
                              //       borderColor: 'inherit',
                              //     },
                              //   },
                              // }}
                            >
                              <MenuItem value="">
                                <em>None (Site-level attachment)</em>
                              </MenuItem>
                              {findings.map((finding) => (
                                <MenuItem key={finding.id} value={finding.id}>
                                  {finding.description.length > 50
                                    ? `${finding.description.substring(0, 50)}...`
                                    : finding.description}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        )}

                        {/* Caption Field */}
                        <TextField
                          fullWidth
                          size="small"
                          label="Caption (Optional)"
                          value={attachmentCaption}
                          onChange={handleCaptionChange}
                          variant="outlined"
                          className="mb-2 dark:text-white"
                          sx={{
                            '& .MuiInputBase-input': {
                              color: 'inherit',
                            },
                            '& .MuiOutlinedInput-root': {
                              '& fieldset': {
                                borderColor: 'inherit',
                              },
                              '&:hover fieldset': {
                                borderColor: 'inherit',
                              },
                              '&.Mui-focused fieldset': {
                                borderColor: 'inherit',
                              },
                            },
                            '& .MuiInputLabel-root': {
                              color: 'inherit',
                            },
                          }}
                        />

                        <Button
                          component="label"
                          variant="contained"
                          startIcon={<CloudUpload />}
                          className="dark:bg-blue-600"
                          disabled={!selectedAttachmentType || 
                                   (selectedAttachmentType === 'Other' && !customAttachmentType) || 
                                   attachmentLoading}
                        >
                          {attachmentLoading ? 'Uploading...' : 'Upload File'}
                          <input
                            type="file"
                            hidden
                            onChange={handleFileUpload}
                            disabled={!selectedAttachmentType || 
                                     (selectedAttachmentType === 'Other' && !customAttachmentType) || 
                                     attachmentLoading}
                          />
                        </Button>

                        <Typography
                          variant="caption"
                          display="block"
                          className="mt-1 dark:text-gray-300"
                        >
                          {selectedFinding
                            ? 'Note: Only JPG, JPEG, and PNG files are allowed for finding attachments'
                            : 'Select an attachment type first, then upload your file'}
                        </Typography>
                      </Box>
                    </Grid>
                  )}

                  <Grid item xs={12}>
                    <Typography variant="subtitle2" className="mb-2 dark:text-white">
                      Uploaded Attachments
                    </Typography>

                    {attachmentLoading ? (
                      <Box display="flex" justifyContent="center" my={2}>
                        <CircularProgress size={24} />
                      </Box>
                    ) : attachments.length > 0 ? (
                      isMobile && !editMode ? (
                        // Mobile View Mode: Stacked Cards
                        <Stack spacing={2}>
                          {attachments.map((attachment, index) => {
                            // Find the associated finding if any
                            const associatedFinding = attachment.finding_id
                              ? findings.find((f) => f.id === attachment.finding_id)
                              : null;
                              
                            return (
                              <Paper key={attachment.id} variant="outlined" sx={{ p: 2 }}>
                                <Stack spacing={1}>
                                  <Typography variant="subtitle2">Attachment {index + 1}</Typography>
                                  
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Typography variant="caption" color="text.secondary">Type:</Typography>
                                    <Typography variant="body2">{attachment.type || '-'}</Typography>
                                  </Box>
                                  
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Typography variant="caption" color="text.secondary">File Name:</Typography>
                                    <Typography variant="body2" sx={{ maxWidth: '70%', wordBreak: 'break-all' }}>{attachment.file_name || '-'}</Typography>
                                  </Box>
                                  
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Typography variant="caption" color="text.secondary">Caption:</Typography>
                                    <Typography variant="body2">{attachment.caption || '-'}</Typography>
                                  </Box>
                                  
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Typography variant="caption" color="text.secondary">Finding:</Typography>
                                    <Typography variant="body2" sx={{ maxWidth: '70%', textAlign: 'right' }}>
                                      {associatedFinding
                                        ? associatedFinding.description.length > 30
                                          ? `${associatedFinding.description.substring(0, 30)}...`
                                          : associatedFinding.description
                                        : '-'}
                                    </Typography>
                                  </Box>
                                  
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Typography variant="caption" color="text.secondary">Created:</Typography>
                                    <Typography variant="body2">{new Date(attachment.created_at).toLocaleString()}</Typography>
                                  </Box>
                                  
                                  <Button
                                    startIcon={<Download />}
                                    onClick={() => handleDownloadAttachment(attachment.id)}
                                    variant="outlined"
                                    size="small"
                                    sx={{ alignSelf: 'flex-start', mt: 1 }}
                                  >
                                    Download
                                  </Button>
                                </Stack>
                              </Paper>
                            );
                          })}
                        </Stack>
                      ) : isMobile && editMode ? (
                        // Mobile Edit Mode: Stacked Cards with edit/delete options
                        <Stack spacing={2}>
                          {attachments.map((attachment, index) => {
                            // Find the associated finding if any
                            const associatedFinding = attachment.finding_id
                              ? findings.find((f) => f.id === attachment.finding_id)
                              : null;
                              
                            return (
                              <Paper key={attachment.id} variant="outlined" sx={{ p: 2 }}>
                                <Stack spacing={1}>
                                  <Stack direction="row" justifyContent="space-between" alignItems="center">
                                    <Typography variant="subtitle2">Attachment {index + 1}</Typography>
                                    <Box>
                                      <IconButton 
                                        size="small" 
                                        onClick={() => handleEditAttachmentClick(attachment)}
                                        sx={{ mr: 0.5 }}
                                      >
                                        <Edit fontSize="small" color="primary" />
                                      </IconButton>
                                      <IconButton 
                                        size="small" 
                                        onClick={() => handleDeleteAttachmentClick(attachment)}
                                        color="error"
                                      >
                                        <Delete fontSize="small" />
                                      </IconButton>
                                    </Box>
                                  </Stack>
                                  
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Typography variant="caption" color="text.secondary">Type:</Typography>
                                    <Typography variant="body2">{attachment.type || '-'}</Typography>
                                  </Box>
                                  
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Typography variant="caption" color="text.secondary">File Name:</Typography>
                                    <Typography variant="body2" sx={{ maxWidth: '70%', wordBreak: 'break-all' }}>{attachment.file_name || '-'}</Typography>
                                  </Box>
                                  
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Typography variant="caption" color="text.secondary">Caption:</Typography>
                                    <Typography variant="body2">{attachment.caption || '-'}</Typography>
                                  </Box>
                                  
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Typography variant="caption" color="text.secondary">Finding:</Typography>
                                    <Typography variant="body2" sx={{ maxWidth: '70%', textAlign: 'right' }}>
                                      {associatedFinding
                                        ? associatedFinding.description.length > 30
                                          ? `${associatedFinding.description.substring(0, 30)}...`
                                          : associatedFinding.description
                                        : '-'}
                                    </Typography>
                                  </Box>
                                  
                                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                    <Typography variant="caption" color="text.secondary">Created:</Typography>
                                    <Typography variant="body2">{new Date(attachment.created_at).toLocaleString()}</Typography>
                                  </Box>
                                  
                                  <Button
                                    startIcon={<Download />}
                                    onClick={() => handleDownloadAttachment(attachment.id)}
                                    variant="outlined"
                                    size="small"
                                    sx={{ alignSelf: 'flex-start', mt: 1 }}
                                  >
                                    Download
                                  </Button>
                                </Stack>
                              </Paper>
                            );
                          })}
                        </Stack>
                      ) : (
                        // Desktop View Mode: Table View
                        <TableContainer
                          component={Paper}
                          variant="outlined" // Add outlined variant
                          sx={{ ...minimalistTableSx(theme), overflowX: 'auto' }} // Apply minimalist style
                        >
                          <Table size="small" aria-label="attachments table">
                            <TableHead>
                              <TableRow>
                                <TableCell sx={{ width: '5%' }}>No.</TableCell> {/* Consistent width */}
                                <TableCell sx={{ width: '15%' }}>Type</TableCell> {/* Adjusted width */}
                                <TableCell sx={{ width: '25%' }}> {/* Adjusted width */}
                                   File Name
                                 </TableCell>
                                <TableCell sx={{ width: '15%' }}> {/* Adjusted width */}
                                   Caption
                                 </TableCell>
                                <TableCell sx={{ width: '10%' }}> {/* Adjusted width */}
                                   Associated Finding
                                 </TableCell>
                                <TableCell sx={{ width: '10%', textAlign: 'right' }}> {/* Consistent width & alignment */}
                                   Actions
                                 </TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {attachments.map((attachment, index) => { // Removed hardcoded styles from cells below
                                // Find the associated finding if any
                                const associatedFinding = attachment.finding_id
                                  ? findings.find((f) => f.id === attachment.finding_id)
                                  : null;

                                return (
                                  <TableRow key={attachment.id}>
                                    <TableCell>{index + 1}</TableCell>
                                    <TableCell>
                                      {attachment.type}
                                    </TableCell>
                                    <TableCell sx={{ wordBreak: 'break-all' }}> {/* Allow breaking long filenames */}
                                      {attachment.file_name}
                                    </TableCell>
                                    <TableCell>
                                      {attachment.caption || '-'}
                                    </TableCell>
                                    <TableCell>
                                      {associatedFinding
                                        ? associatedFinding.description.length > 30
                                          ? `${associatedFinding.description.substring(0, 30)}...`
                                          : associatedFinding.description
                                        : '-'}
                                    </TableCell>
                                    <TableCell align="right"> {/* Consistent alignment */}
                                      <Box display="flex" gap={1} justifyContent="flex-end"> {/* Justify icons right */}
                                        <IconButton
                                          size="small"
                                          onClick={() => handleDownloadAttachment(attachment.id)}
                                          className="dark:text-white"
                                        >
                                          <Download color="primary" fontSize="small" />
                                        </IconButton>

                                        {editMode && (
                                          <>
                                            <IconButton
                                              size="small"
                                              onClick={() => handleEditAttachmentClick(attachment)}
                                              className="dark:text-white"
                                            >
                                              <Edit color="primary" fontSize="small" />
                                            </IconButton>
                                            <IconButton
                                              size="small"
                                              onClick={() => handleDeleteAttachmentClick(attachment)}
                                              className="dark:text-white"
                                            >
                                              <Delete color="error" fontSize="small" />
                                            </IconButton>
                                          </>
                                        )}
                                      </Box>
                                    </TableCell>
                                  </TableRow>
                                );
                              })}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      )
                    ) : (
                      <Typography
                        variant="body2"
                        color="textSecondary"
                        className="dark:text-gray-400"
                      >
                        No attachments uploaded yet
                      </Typography>
                    )}
                  </Grid>
                </Grid>
              </Paper>
            </Grid>
          </Grid>
        </div>
      </div>

      {/* Dialogs are now siblings to the main Container, within the top-level fragment */}
      {/* Delete Site Confirmation Dialog */}
      <Dialog
        open={openDeleteConfirmDialog}
        onClose={() => setOpenDeleteConfirmDialog(false)}
        maxWidth="xs"
        fullWidth
        PaperProps={{ sx: { bgcolor: 'background.paper' } }}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography variant="body1">
            Are you sure you want to delete this site? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={() => setOpenDeleteConfirmDialog(false)} variant="outlined" color="inherit">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            color="error"
            variant="contained"
            disabled={deleteLoading}
            startIcon={deleteLoading ? <CircularProgress size={16} color="inherit"/> : <Delete />}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Add/Edit Finding Dialog */}
      <Dialog
        open={openFindingDialog}
        onClose={handleCloseFindingDialog}
        maxWidth="sm"
        fullWidth
        PaperProps={{ sx: { bgcolor: 'background.paper' } }}
      >
        <DialogTitle>{currentFinding?.id ? 'Edit Finding' : 'Add New Finding'}</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            name="description"
            label="Description"
            type="text"
            fullWidth
            multiline
            rows={4}
            value={currentFinding?.description || ''}
            onChange={handleFindingInputChange}
            variant="outlined"
            sx={{ mt: 1, ...minimalistTextFieldSx(theme) }}
          />
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={handleCloseFindingDialog} variant="outlined" color="inherit">
            Cancel
          </Button>
          <Button
            onClick={handleSaveFinding}
            color="primary"
            variant="contained"
            disabled={findingSaveLoading}
            startIcon={findingSaveLoading ? <CircularProgress size={16} color="inherit"/> : <Save />}
          >
            Save Finding
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Attachment Confirmation Dialog */}
      <Dialog
        open={openDeleteAttachmentDialog}
        onClose={() => setOpenDeleteAttachmentDialog(false)}
        maxWidth="xs"
        fullWidth
         PaperProps={{ sx: { bgcolor: 'background.paper' } }}
      >
        <DialogTitle>Confirm Delete Attachment</DialogTitle>
        <DialogContent>
          <Typography variant="body1">
            Are you sure you want to delete this attachment?
          </Typography>
          {attachmentToDelete && (
            <Typography variant="body2" sx={{ mt: 2, color: 'text.secondary', wordBreak: 'break-all' }}>
              File: {attachmentToDelete.file_name}
            </Typography>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={() => setOpenDeleteAttachmentDialog(false)} variant="outlined" color="inherit">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDeleteAttachment}
            color="error"
            variant="contained"
            startIcon={<Delete />}
          >
            Delete Attachment
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Attachment Dialog */}
      <Dialog
        open={openEditAttachmentDialog}
        onClose={() => setOpenEditAttachmentDialog(false)}
        maxWidth="sm"
        fullWidth
         PaperProps={{ sx: { bgcolor: 'background.paper' } }}
      >
        <DialogTitle>Edit Attachment Details</DialogTitle>
        <DialogContent id="edit-attachment-dialog-content"> {/* Add ID for Drawer container */}
          {attachmentToEdit && (() => { // Use an IIFE to define variables before JSX
            {/* Determine if the file is an image based on extension */}
            const fileName = attachmentToEdit.file_name || '';
            const isImageFile = /\.(jpe?g|png)$/i.test(fileName); // CORRECTED: Remove extra backslash before dot

            return (
              <Stack spacing={2} sx={{ pt: 1 }}>
                <Typography variant="subtitle2" component="div">
                  File: <Typography component="span" sx={{ color: 'text.secondary', wordBreak: 'break-all'}}>{attachmentToEdit.file_name}</Typography>
                </Typography>
                <Typography variant="caption" component="div" sx={{ color: 'text.secondary' }}>
                    Type: {attachmentToEdit.type}
                </Typography>

                <TextField
                  margin="dense"
                  name="caption"
                  label="Caption"
                  type="text"
                  fullWidth
                  value={attachmentToEdit.caption || ''}
                  onChange={handleAttachmentEditChange}
                  variant="outlined"
                   sx={minimalistTextFieldSx(theme)}
                />


                {/* Associated Finding Select / Drawer */}
                {isMobile ? (
                  <>
                    <TextField
                      label="Associated Finding (Optional)"
                      value={attachmentToEdit.finding_id ? (findings.find(f => f.id === attachmentToEdit.finding_id)?.description || '').substring(0, 60) + ((findings.find(f => f.id === attachmentToEdit.finding_id)?.description || '').length > 60 ? '...' : '') : 'None (Site-level)'}
                      onClick={() => { if (isImageFile) setFindingEditDrawerOpen(true); }} // Only open if image
                      variant="outlined"
                      fullWidth
                      size="small"
                      disabled={!isImageFile} // Disable if not an image file
                      InputProps={{ readOnly: true }}
                      sx={{
                        ...minimalistTextFieldSx(theme),
                        cursor: !isImageFile ? 'not-allowed' : 'pointer', // Adjust cursor
                        mt: 1
                      }}
                    />
                     {!isImageFile && ( // Add helper text when disabled
                        <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                            Only image files (JPG, JPEG, PNG) can be associated with findings.
                        </Typography>
                     )}
                    <Drawer
                      anchor="bottom"
                      open={findingEditDrawerOpen}
                      onClose={() => setFindingEditDrawerOpen(false)}
                      PaperProps={{ sx: { maxHeight: '60vh' } }}
                       // Prevent closing dialog when clicking inside drawer
                      ModalProps={{
                        container: document.getElementById('edit-attachment-dialog-content'), // Attach drawer modal to dialog content
                        style: { position: 'absolute' } // Necessary for correct positioning within dialog
                      }}
                    >
                      <AppBar position="static" sx={{ mb: 1, backgroundColor: 'background.paper', color: 'text.primary', boxShadow: 'none', borderBottom: (theme) => `1px solid ${theme.palette.divider}` }}>
                         <Toolbar variant="dense">
                            <Typography variant="h6" sx={{ flexGrow: 1 }}>
                              Select Associated Finding
                            </Typography>
                            <Button color="inherit" onClick={() => setFindingEditDrawerOpen(false)}>
                              Done
                            </Button>
                          </Toolbar>
                      </AppBar>
                      <List>
                         <ListItem disablePadding>
                            <ListItemButton
                                selected={!attachmentToEdit.finding_id}
                                onClick={() => {
                                    handleAttachmentEditChange({ target: { name: 'finding_id', value: '' } });
                                    setFindingEditDrawerOpen(false);
                                }}
                            >
                                <ListItemText primary={<em>None (Site-level)</em>} primaryTypographyProps={{ sx: { textAlign: 'center' } }} />
                            </ListItemButton>
                          </ListItem>
                         {findings.map((finding) => (
                          <ListItem key={finding.id} disablePadding>
                            <ListItemButton
                              selected={attachmentToEdit.finding_id === finding.id}
                              onClick={() => {
                                handleAttachmentEditChange({ target: { name: 'finding_id', value: finding.id } });
                                setFindingEditDrawerOpen(false);
                              }}
                            >
                              <ListItemText
                                primary={finding.description}
                                primaryTypographyProps={{
                                  noWrap: true,
                                  style: {
                                    textOverflow: 'ellipsis',
                                    overflow: 'hidden',
                                    whiteSpace: 'nowrap',
                                    textAlign: 'center' // Center text
                                  },
                                }}
                              />
                            </ListItemButton>
                          </ListItem>
                        ))}
                      </List>
                    </Drawer>
                  </>
                ) : (
                  <FormControl fullWidth size="small" sx={{ mt: 1 }} disabled={!isImageFile}> {/* Disable FormControl */}
                    <InputLabel id="edit-finding-label">Associated Finding (Optional)</InputLabel>
                    <Select
                      labelId="edit-finding-label"
                      label="Associated Finding (Optional)"
                      name="finding_id"
                      value={attachmentToEdit.finding_id || ''}
                      onChange={handleAttachmentEditChange}
                      sx={minimalistSelectSx(theme)}
                      // Disabled prop is inherited
                    >
                       <MenuItem value=""><em>None (Site-level)</em></MenuItem>
                       {findings.map((finding) => (
                          <MenuItem key={finding.id} value={finding.id}>
                            {finding.description.length > 60
                              ? `${finding.description.substring(0, 60)}...`
                              : finding.description}
                          </MenuItem>
                        ))}
                    </Select>
                    {!isImageFile && ( // Add helper text when disabled
                        <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                            Only image files (JPG, JPEG, PNG) can be associated with findings.
                        </Typography>
                     )}
                  </FormControl>
                )}

                {/* Update the existing Alert to use the file extension check */}
                {attachmentToEdit.finding_id && !isImageFile &&
                    <Alert severity="warning" variant="outlined" sx={{ mt: 1 }}>
                     Note: Only image files (JPG, JPEG, PNG) can be associated with findings. This file might not display correctly in reports.
                    </Alert>
                 }
              </Stack>
            ); // end return from IIFE
          })()} {/* Immediately invoke the function */}
        </DialogContent>
        <DialogActions sx={{ p: 2 }}>
          <Button onClick={() => setOpenEditAttachmentDialog(false)} variant="outlined" color="inherit">
            Cancel
          </Button>
          <Button
            onClick={handleSaveAttachmentEdit}
            color="primary"
            variant="contained"
            startIcon={<Save />}
          >
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
