// Next, React, Tw
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';

// Mui
import {
  But<PERSON>,
  <PERSON>ert,
  Modal,
  <PERSON>,
  Typography,
  TextField,
  Button as <PERSON>i<PERSON><PERSON><PERSON>,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from '@mui/material';
import { Delete } from '@mui/icons-material';

// Packages
import { useSnackbar } from 'notistack';

// Components
import Layout from '../../layouts/module/smart-tool';

// Others
import { SMART_TOOL_ENDPOINT } from '../../utils/smart-tool';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

// ----------------------------------------------------------------------

// Helper function to format date string
const formatDateTime = (dateString) => {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return '-';
    }
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
    const year = date.getFullYear();
    let hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
    const hoursStr = String(hours).padStart(2, '0');

    return `${day}-${month}-${year} ${hoursStr}:${minutes} ${ampm}`;
  } catch (e) {
    console.error("Error formatting date:", dateString, e);
    return '-'; // Return fallback on error
  }
};

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { push, query } = useRouter();
  const { q } = query;
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const { setParam } = useParamContext();
  const { user } = useAuthContext();
  const { isAdmin } = useModuleRoleContext();

  const [sites, setSites] = useState([]);
  const [error, setError] = useState(null);
  const [redirectError, setRedirectError] = useState(null);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [siteToDelete, setSiteToDelete] = useState(null);
  const [paginationModel, setPaginationModel] = useState({
    page: 0,
    pageSize: 10,
  });
  const [totalRows, setTotalRows] = useState(0);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [workgroupFilter, setWorkgroupFilter] = useState('all');

  // New state for create site modal
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [newSite, setNewSite] = useState({
    customerName: '',
    siteName: '',
    sphereId: '',
    dtcNo: '',
    workgroup: '',
  });

  const handleOpenCreateModal = () => {
    setIsCreateModalOpen(true);
  };

  const handleCloseCreateModal = () => {
    setIsCreateModalOpen(false);
    setNewSite({
      customerName: '',
      siteName: '',
      sphereId: '',
      dtcNo: '',
      workgroup: '',
    });
  };

  const handleCreateSite = async () => {
    if (!newSite.sphereId && !newSite.dtcNo) {
      enqueueSnackbar('Please provide either Sphere ID or DTC Number', {
        variant: 'error',
      });
      return;
    }

    try {
      const response = await axios.post(`${SMART_TOOL_ENDPOINT}/sites`, {
        sphere_id: newSite.sphereId,
        dtc_no: newSite.dtcNo,
        workgroup: newSite.workgroup || null,
        user_full_name: user?.name,
      });

      if (response.data.success) {
        const newSiteData = response.data.data;
        setSites((prevSites) => [
          {
            id: newSiteData.id,
            site_id: newSiteData.site_id,
            sphere_id: newSiteData.sphere_id,
            customer_name: newSiteData.customer_name,
            site_name: newSiteData.site_name,
            created_at: newSiteData.created_at,
            updated_at: newSiteData.updated_at,
          },
          ...prevSites,
        ]);

        handleCloseCreateModal();
        push(`/smart-tool/${newSiteData.id}`);
      } else {
        enqueueSnackbar(response.data.message || 'Failed to create site', {
          variant: 'error',
        });
      }
    } catch (err) {
      enqueueSnackbar(err.response?.data?.message || err.message || 'Failed to create site', {
        variant: 'error',
      });
    }
  };

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setNewSite((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key) direction = sortConfig.direction === 'asc' ? 'desc' : 'asc';
    setSortConfig({ key, direction });
  };

  useEffect(() => {
    const fetchSites = async () => {
      dispatch(setIsLoading(true));
      setError(null);
      setSites([]);
      setTotalRows(0);

      try {
        const response = await axios.get(`${SMART_TOOL_ENDPOINT}/sites`, {
          params: {
            page: paginationModel.page + 1,
            page_size: paginationModel.pageSize,
          },
        });

        if (response.data.success) {
          if (!response?.data?.data) {
            setSites([]);
            setTotalRows(0);
          } else {
            const transformedData = response.data.data.map((item) => ({
              site_id: item.site_id,
              id: item.id,
              sphere_id: item.sphere_id,
              customer_name: item.customer_name,
              site_name: item.site_name,
              sof_no: item.sof_no || '-',
              dtc_no: item.dtc_no || '-',
              nova_id: item.nova_id || '-',
              site_id: item.site_id || '-',
              end_customer: item.end_customer || '-',
              site_address: item.site_address || '-',
              lat_long: item.lat_long || '-',
              pm: item.pm || '-',
              cme_status: item.cme_status || '-',
              btc_number: item.btc_number || '-',
              workgroup: item.workgroup || '-',
              created_at: item.created_at,
              updated_at: item.updated_at,
              created_by: item.created_by || '-',
              updated_by: item.updated_by || '-',
            }));

            setSites(transformedData);
            setTotalRows(response.data.meta.count || response.data.meta.total_count);
          }
          setError(null);
        } else {
          setError('Failed to fetch sites');
        }
      } catch (err) {
        setError(err.message || 'Failed to fetch sites');
      } finally {
        dispatch(setIsLoading(false));
      }
    };

    fetchSites();
  }, [paginationModel]);

  if (error) {
    return (
      <div className="container rounded-s bg-white p-4 text-black dark:bg-gray-600 dark:text-white">
        <Alert severity="error">{error}</Alert>
      </div>
    );
  }

  // Filter sites based on user role, workgroup, and selected workgroup filter
  const filteredSites = sites
    .filter((site) => {
      // First apply workgroup dropdown filter if not "all"
      if (workgroupFilter !== 'all') {
        return site.workgroup === workgroupFilter;
      }

      // Then apply role-based filtering
      // If user is admin or super admin, show all sites
      if (isAdmin || user?.isSuperAdmin) {
        return true;
      }

      // If user is regular user, filter by workgroup
      // If site workgroup is null/N/A or matches user's workgroup, show the site
      // If user's workgroup is null/N/A, show all sites
      const userWorkgroup = user?.workgroup;
      const siteWorkgroup = site.workgroup;

      if (!userWorkgroup || userWorkgroup === '-' || userWorkgroup === 'N/A') {
        return true;
      }

      return !siteWorkgroup || siteWorkgroup === '-' || siteWorkgroup === userWorkgroup;
    })
    // Then apply search filtering
    .filter((o) => {
      if ([undefined, '']?.includes(q)) return true;
      return JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase());
    })
    .sort((a, b) => {
      if (!sortConfig.key) return 0;

      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];

      if (aValue === bValue) return 0;

      if (['created_at', 'updated_at']?.includes(sortConfig?.key)) {
        return sortConfig.direction === 'asc'
          ? new Date(aValue) - new Date(bValue)
          : new Date(bValue) - new Date(aValue);
      }

      return sortConfig.direction === 'asc'
        ? aValue.toString().localeCompare(bValue.toString())
        : bValue.toString().localeCompare(aValue.toString());
    });

  const handleDeleteSite = async (site, e) => {
    e.stopPropagation();
    setSiteToDelete(site);
    setDeleteConfirmOpen(true);
  };

  const confirmDeleteSite = async () => {
    if (!siteToDelete) return;

    try {
      await axios.delete(`${SMART_TOOL_ENDPOINT}/sites/${siteToDelete.id}`);

      setSites((prevSites) => prevSites.filter((site) => site.id !== siteToDelete.id));
      setDeleteConfirmOpen(false);
      setSiteToDelete(null);
    } catch (deleteError) {
      enqueueSnackbar(
        deleteError.response?.data?.message || deleteError.message || 'Failed to delete site',
        {
          variant: 'error',
        }
      );
      setDeleteConfirmOpen(false);
      setSiteToDelete(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteConfirmOpen(false);
    setSiteToDelete(null);
  };

  return (
    <>
      <div className="bg-primary p-4 text-white dark:bg-gray-600 dark:text-white">
        <h2 className="mb-4 text-lg font-bold">Site List</h2>
        <p className="text-xs">List of All Registered Sites.</p>
      </div>

      <div className="container p-4">
        <div className="rounded-s bg-white p-4 text-black dark:bg-gray-600 dark:text-white">
          {redirectError && (
            <Alert severity="error" onClose={() => setRedirectError(null)} className="mb-4">
              {redirectError}
            </Alert>
          )}
          <div className="mb-4 flex flex-col items-start gap-4 md:flex-row md:items-center">
            <div className="flex w-full flex-grow flex-col items-start gap-2 sm:flex-row sm:items-center">
              <input
                type="text"
                placeholder="Search..."
                value={q}
                onChange={(e) => setParam({ q: e.target.value })}
                className="flex-grow rounded-lg border border-gray-300 p-2 text-xs dark:border-gray-500 dark:bg-gray-700"
              />

              {/* Workgroup Filter Dropdown */}
              <div className="flex items-center gap-2">
                <span className="whitespace-nowrap text-xs">Workgroup:</span>
                <select
                  value={workgroupFilter}
                  onChange={(e) => setWorkgroupFilter(e.target.value)}
                  className="rounded-lg border border-gray-300 p-2 text-xs dark:border-gray-500 dark:bg-gray-700"
                  aria-label="Filter by Workgroup"
                >
                  <option value="all">All</option>
                  <option value="MC1">MC1</option>
                  <option value="FCR">FCR</option>
                  <option value="GLOBAL">GLOBAL</option>
                  {/* Extract unique workgroups from sites that aren't already in the predefined list */}
                  {Array.from(
                    new Set(
                      sites
                        .map((site) => site.workgroup)
                        .filter((wg) => wg && wg !== '-' && !['MC1', 'FCR', 'GLOBAL'].includes(wg))
                    )
                  ).map((workgroup) => (
                    <option key={workgroup} value={workgroup}>
                      {workgroup}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <button
              type="button"
              className="bg-primary whitespace-nowrap rounded-lg px-4 py-2 text-xs text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
              onClick={handleOpenCreateModal}
            >
              Create New Site
            </button>
          </div>

          <Modal
            open={isCreateModalOpen}
            onClose={handleCloseCreateModal}
            aria-labelledby="create-site-modal-title"
            className="flex items-center justify-center"
          >
            <Box className="mx-4 w-full max-w-md rounded-lg bg-white p-6 dark:bg-gray-800">
              <Typography
                id="create-site-modal-title"
                variant="h6"
                component="h2"
                className="mb-4 text-gray-900 dark:text-white"
              >
                Create New Site
              </Typography>
              <div className="space-y-4">
                <TextField
                  fullWidth
                  label="Sphere ID"
                  name="sphereId"
                  value={newSite.sphereId}
                  onChange={handleInputChange}
                  variant="outlined"
                  className="mb-4"
                  size="small"
                />
                <TextField
                  fullWidth
                  label="DTC Number"
                  name="dtcNo"
                  value={newSite.dtcNo}
                  onChange={handleInputChange}
                  variant="outlined"
                  className="mb-4"
                  size="small"
                />
                <div className="mb-4">
                  <Typography
                    variant="caption"
                    className="mb-1 block text-gray-600 dark:text-gray-300"
                  >
                    Workgroup
                  </Typography>
                  <select
                    name="workgroup"
                    value={newSite.workgroup}
                    onChange={handleInputChange}
                    className="w-full rounded-lg border border-gray-300 p-2 text-xs dark:border-gray-500 dark:bg-gray-700"
                  >
                    <option value="">Select a workgroup</option>
                    <option value="MC1">MC1</option>
                    <option value="FCR">FCR</option>
                    <option value="GLOBAL">GLOBAL</option>
                  </select>
                  <Typography
                    variant="caption"
                    className="mt-1 block text-gray-500 dark:text-gray-400"
                  >
                    Assign a workgroup for access control
                  </Typography>
                </div>
                <div className="flex justify-end space-x-2">
                  <MuiButton onClick={handleCloseCreateModal} color="secondary" variant="outlined">
                    Cancel
                  </MuiButton>
                  <MuiButton onClick={handleCreateSite} color="primary" variant="contained">
                    Create Site
                  </MuiButton>
                </div>
              </div>
            </Box>
          </Modal>

          {sites.length === 0 && (
            <Alert severity="info" className="mb-4">
              No sites found. Click &quot;Create New Site&quot; to add a site.
            </Alert>
          )}

          {sites.length > 0 && (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-500">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th
                        onClick={() => handleSort('sphere_id')}
                        className="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100"
                      >
                        Sphere ID{' '}
                        {sortConfig.key === 'sphere_id' &&
                          (sortConfig.direction === 'asc' ? '▲' : '▼')}
                      </th>
                      <th
                        onClick={() => handleSort('dtc_no')}
                        className="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100"
                      >
                        DTC No{' '}
                        {sortConfig.key === 'dtc_no' &&
                          (sortConfig.direction === 'asc' ? '▲' : '▼')}
                      </th>
                      <th
                        onClick={() => handleSort('nova_id')}
                        className="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100"
                      >
                        Nova ID{' '}
                        {sortConfig.key === 'nova_id' &&
                          (sortConfig.direction === 'asc' ? '▲' : '▼')}
                      </th>
                      <th
                        onClick={() => handleSort('site_id')}
                        className="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100"
                      >
                        Site ID{' '}
                        {sortConfig.key === 'site_id' &&
                          (sortConfig.direction === 'asc' ? '▲' : '▼')}
                      </th>
                      <th
                        onClick={() => handleSort('site_name')}
                        className="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100"
                      >
                        Site Name{' '}
                        {sortConfig.key === 'site_name' &&
                          (sortConfig.direction === 'asc' ? '▲' : '▼')}
                      </th>
                      <th
                        onClick={() => handleSort('customer_name')}
                        className="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100"
                      >
                        Customer Name{' '}
                        {sortConfig.key === 'customer_name' &&
                          (sortConfig.direction === 'asc' ? '▲' : '▼')}
                      </th>
                      <th
                        onClick={() => handleSort('end_customer')}
                        className="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100"
                      >
                        End Customer{' '}
                        {sortConfig.key === 'end_customer' &&
                          (sortConfig.direction === 'asc' ? '▲' : '▼')}
                      </th>
                      <th
                        onClick={() => handleSort('site_address')}
                        className="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100"
                      >
                        Site Address{' '}
                        {sortConfig.key === 'site_address' &&
                          (sortConfig.direction === 'asc' ? '▲' : '▼')}
                      </th>
                      <th
                        onClick={() => handleSort('lat_long')}
                        className="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100"
                      >
                        Lat / Long{' '}
                        {sortConfig.key === 'lat_long' &&
                          (sortConfig.direction === 'asc' ? '▲' : '▼')}
                      </th>
                      <th
                        onClick={() => handleSort('pm')}
                        className="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100"
                      >
                        PM {sortConfig.key === 'pm' && (sortConfig.direction === 'asc' ? '▲' : '▼')}
                      </th>
                      <th
                        onClick={() => handleSort('workgroup')}
                        className="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100"
                      >
                        Workgroup{' '}
                        {sortConfig.key === 'workgroup' &&
                          (sortConfig.direction === 'asc' ? '▲' : '▼')}
                      </th>
                      <th
                        onClick={() => handleSort('created_by')}
                        className="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100"
                      >
                        Created By{' '}
                        {sortConfig.key === 'created_by' &&
                          (sortConfig.direction === 'asc' ? '▲' : '▼')}
                      </th>
                      <th
                        onClick={() => handleSort('created_at')}
                        className="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100"
                      >
                        Created At{' '}
                        {sortConfig.key === 'created_at' &&
                          (sortConfig.direction === 'asc' ? '▲' : '▼')}
                      </th>
                      <th
                        onClick={() => handleSort('updated_by')}
                        className="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100"
                      >
                        Updated By{' '}
                        {sortConfig.key === 'updated_by' &&
                          (sortConfig.direction === 'asc' ? '▲' : '▼')}
                      </th>
                      <th
                        onClick={() => handleSort('updated_at')}
                        className="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-100"
                      >
                        Updated At{' '}
                        {sortConfig.key === 'updated_at' &&
                          (sortConfig.direction === 'asc' ? '▲' : '▼')}
                      </th>
                      <th
                        className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300"
                      >
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-500 dark:bg-gray-600">
                    {filteredSites
                      .slice(
                        paginationModel.page * paginationModel.pageSize,
                        (paginationModel.page + 1) * paginationModel.pageSize
                      )
                      .map((site) => (
                        <tr
                          key={site.id}
                          className="cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
                          onClick={() => push(`/smart-tool/${site.id}`)}
                        >
                          <td className="whitespace-nowrap px-6 py-4 text-xs text-gray-500 dark:text-gray-400">
                            {site.sphere_id || '-'}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-xs text-gray-500 dark:text-gray-400">
                            {site.dtc_no || '-'}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-xs text-gray-500 dark:text-gray-400">
                            {site.nova_id || '-'}
                          </td>
                          <td className="whitespace-pre-wrap px-6 py-4 text-xs text-gray-500 dark:text-gray-400">
                            {site.site_id || '-'}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-xs text-gray-500 dark:text-gray-400">
                            {site.site_name || '-'}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-xs text-gray-500 dark:text-gray-400">
                            {site.customer_name || '-'}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-xs text-gray-500 dark:text-gray-400">
                            {site.end_customer || '-'}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-xs text-gray-500 dark:text-gray-400">
                            {site.site_address || '-'}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-xs text-gray-500 dark:text-gray-400">
                            {site.lat_long || '-'}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-xs text-gray-500 dark:text-gray-400">
                            {site.pm || '-'}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-xs text-gray-500 dark:text-gray-400">
                            {site.workgroup || '-'}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-xs text-gray-500 dark:text-gray-400">
                            {site.created_by || '-'}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-xs text-gray-500 dark:text-gray-400">
                            {formatDateTime(site.created_at)}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-xs text-gray-500 dark:text-gray-400">
                            {site.updated_by || '-'}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-xs text-gray-500 dark:text-gray-400">
                            {formatDateTime(site.updated_at)}
                          </td>
                          <td className="whitespace-nowrap px-6 py-4 text-right text-xs font-medium">
                            <div className="flex items-center justify-end">
                              <IconButton
                                size="small"
                                color="error"
                                onClick={(e) => handleDeleteSite(site, e)}
                                className="hover:bg-red-100 dark:hover:bg-red-900"
                              >
                                <Delete fontSize="small" />
                              </IconButton>
                            </div>
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>

              <div className="mt-4 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <select
                    value={paginationModel.pageSize}
                    onChange={(e) =>
                      setPaginationModel({
                        ...paginationModel,
                        pageSize: Number(e.target.value),
                        page: 0,
                      })
                    }
                    className="rounded-lg border border-gray-300 p-2 text-xs dark:border-gray-500 dark:bg-gray-700"
                  >
                    <option value={10}>10 per page</option>
                    <option value={25}>25 per page</option>
                    <option value={50}>50 per page</option>
                  </select>
                  <span className="text-xs text-gray-500 dark:text-gray-300">
                    Total: {totalRows} items
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <button
                    type="button"
                    onClick={() =>
                      setPaginationModel({
                        ...paginationModel,
                        page: paginationModel.page - 1,
                      })
                    }
                    disabled={paginationModel.page === 0}
                    className="rounded-lg border border-gray-300 px-4 py-2 text-xs disabled:opacity-50 dark:border-gray-500 dark:bg-gray-700"
                  >
                    Previous
                  </button>
                  <span className="text-xs text-gray-500 dark:text-gray-300">
                    Page {paginationModel.page + 1} of{' '}
                    {Math.ceil(totalRows / paginationModel.pageSize)}
                  </span>
                  <button
                    type="button"
                    onClick={() =>
                      setPaginationModel({
                        ...paginationModel,
                        page: paginationModel.page + 1,
                      })
                    }
                    disabled={(paginationModel.page + 1) * paginationModel.pageSize >= totalRows}
                    className="rounded-lg border border-gray-300 px-3 py-1 text-xs disabled:opacity-50 dark:border-gray-500 dark:bg-gray-700"
                  >
                    Next
                  </button>
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      <Dialog
        open={deleteConfirmOpen}
        onClose={handleDeleteCancel}
        aria-labelledby="delete-site-dialog-title"
        aria-describedby="delete-site-dialog-description"
      >
        <DialogTitle id="delete-site-dialog-title">Confirm Site Deletion</DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-site-dialog-description">
            Are you sure you want to delete the site &quot;{siteToDelete?.site_name}&quot; with Site
            ID {siteToDelete?.site_id}? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="primary">
            Cancel
          </Button>
          <Button onClick={confirmDeleteSite} color="error" autoFocus>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
