// Next, React, Tw
import Image from 'next/image';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions, Divider } from '@mui/material';
import { EditOutlined, PostAddOutlined } from '@mui/icons-material';

// Packages
import * as R from 'ramda';
import moment from 'moment';
import { useState, useEffect } from 'react';

// Components
import Layout from '../../layouts/module/payme';
import ExportExcelButton from '../../components/Shared/ExportExcelButton';
import { TablePaginationCustom } from '../../components/Shared/table';
import FilterRoleButtonGroup from '../../components/Shared/FilterRoleButtonGroup';
import {
  TextInput,
  SelectInput,
  SearchInput,
  DateInput,
} from '../../components/Shared/CustomInput';

// Others
import axios from '../../utils/axios';
import { useSnackbar } from '../../components/Shared/snackbar';
import {
  toUpperCaseFirstLetter,
  sortArrayOfObjectsByCertainKeyAlphabetically,
  getFilteredStaffData,
} from '../../utils/shared';
import { AUM_ENDPOINT } from '../../utils/aum';
import { PAYME_ENDPOINT } from '../../utils/payme';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and vars
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const { query, replace } = useRouter();
  const { setParam } = useParamContext();

  const { allStaffs } = useSelector((state) => state.aum);
  const { page, rowsPerPage } = useSelector((state) => state.simi);
  const { q, year, dataRole } = query;

  // Table
  const [tableData, setTableData] = useState([]);
  const getBodyCellStyle = () => 'text-center whitespace-nowrap px-1 text-xs';
  const getTableData = () => {
    const temp = getFilteredStaffData(tableData, dataRole, 'position');
    if ([undefined, '']?.includes(q)) {
      return temp;
    }
    return temp.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  };

  // Dialog (Add/Edit user)
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const DIALOG_DATA = {
    user_id: null,
    monthly_contribution: null,
    others_contribution: null,
    year,
  };
  const [dialogData, setDialogData] = useState(DIALOG_DATA);
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };

  // Dialog (Add/Edit collection)
  const [dialogOpenCollection, setDialogOpenCollection] = useState(false);
  const [editModeDialogCollection, setEditModeDialogCollection] = useState(false);
  const DIALOG_DATA_COLLECTION = {
    user_id: null,
    start_month: null,
    end_month: null,
    year: null,
    others: false,
  };
  const [dialogDataCollection, setDialogDataCollection] = useState(DIALOG_DATA_COLLECTION);
  const handleDialogDataChangeCollection = (event) => {
    const { name, value } = event.target;
    setDialogDataCollection((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const handleClickOpenDialogCollection = (editMode, data) => {
    setEditModeDialogCollection(editMode);
    setDialogDataCollection({ ...dialogDataCollection, user_id: data.user_id });
    setDialogOpenCollection(true);
  };

  const handleDialogClose = async (action) => {
    if (action) {
      try {
        if (Object.values(dialogData).includes(null)) {
          enqueueSnackbar('Please fill in all field.', {
            variant: 'error',
          });
          return;
        }
        dialogData.user_id = dialogData.user_id.trim();
        dialogData.monthly_contribution = Number(dialogData.monthly_contribution);
        dialogData.others_contribution = Number(dialogData.others_contribution);

        let response;
        switch (action) {
          case 'post':
            response = await axios.get(`${AUM_ENDPOINT}/user/v1/${dialogData?.user_id}`);
            if (response.data.status === 'success' && response.data.data != null) {
              response = await axios.post(`${PAYME_ENDPOINT}/user`, dialogData);
            }
            break;
          case 'put':
            response = await axios.put(
              `${PAYME_ENDPOINT}/user/all/${dialogData.user_id}`,
              dialogData
            );
            break;
          case 'delete':
            response = await axios.delete(`${PAYME_ENDPOINT}/user/${dialogData.user_id}`);
            break;
          default:
            break;
        }

        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        /* empty */
      }
    }
    setDialogData(DIALOG_DATA);
    setDialogOpen(false);
    fetchData();
  };

  const handleDialogCloseCollection = async (action) => {
    if (action) {
      if (Object.values(dialogDataCollection).includes(null)) {
        enqueueSnackbar('Please fill in all field.', {
          variant: 'error',
        });
        return;
      }

      let message;
      let response;
      let statusVariant;

      dialogDataCollection.others = dialogDataCollection.others === 'true';

      try {
        response = await axios.post(`${PAYME_ENDPOINT}/collect`, dialogDataCollection);

        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch (error) {
        const errorMessage =
          error.response?.data?.message || error.message || 'An unexpected error occurred';
        enqueueSnackbar(errorMessage, {
          variant: 'error',
        });
      }
    }
    setDialogOpenCollection(false);
  };

  // Others

  const getRoleText = (role) => {
    role = role.toLowerCase();

    if (role.includes('penolong') || role.includes('kerani') || role.includes('secretary')) {
      return 'Non-Executive';
    }
    if (role.includes('exec') || role.includes('asst')) {
      return 'Assistant Manager';
    }
    if (role.includes('mgr') || role.includes('manager')) {
      return 'Manager';
    }
    if (role.includes('agm') || role.includes('head market')) {
      return 'Assistant General Manager';
    }
    if (role.includes('vp')) {
      return 'Vice President';
    }
    if (role.includes('evp')) {
      return 'Executive Vice President';
    }
    if (!role.includes('head market') || role.includes('gm')) {
      return 'General Manager';
    }
    return 'Unknown';
  };

  const [, setFirstAndLastMonths] = useState({ firstTrueMonth: null, lastTrueMonth: null });

  const getFirstAndLastTrueMonths = (data) => {
    const months = [
      'jan',
      'feb',
      'mar',
      'apr',
      'may',
      'jun',
      'jul',
      'aug',
      'sep',
      'oct',
      'nov',
      'dec',
    ];

    let firstTrueMonth = null;
    let lastTrueMonth = null;

    for (let i = 0; i < months.length; i += 1) {
      if (data[months[i]]) {
        if (firstTrueMonth === null) {
          firstTrueMonth = months[i];
        }
        lastTrueMonth = months[i];
      }
    }

    return { firstTrueMonth, lastTrueMonth };
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${PAYME_ENDPOINT}/user/year/${year}`);
      const response3 = await axios.get(`${PAYME_ENDPOINT}/config/year/${year}`);

      if (response?.data?.data) {
        response.data.data = response.data.data.map((o) => ({ ...o, staff_id: o?.user_id }));
        let temp = R.pipe(
          R.groupBy(R.prop('staff_id')),
          R.values,
          R.map(R.mergeAll)
        )([...response.data.data, ...allStaffs])?.filter(
          (o) => o?.monthly_contribution !== undefined
        );
        temp = sortArrayOfObjectsByCertainKeyAlphabetically(temp, 'name');
        setTableData(temp);
      }

      if (response3?.data?.data) {
        const result_data = response3?.data?.data;
        const data_month = getFirstAndLastTrueMonths(response3?.data?.data[0]);
        setFirstAndLastMonths(data_month);
        const collection = {
          start_month: data_month.firstTrueMonth,
          end_month: data_month.lastTrueMonth,
          year: result_data[0].year,
          others: result_data[0].others,
        };
        setDialogDataCollection(collection);
      }
    } catch (error) {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };
  useEffect(() => {
    if (allStaffs?.length === 0) return;

    if (!year || !dataRole) {
      replace(`/payme/user?year=${moment()?.format('YYYY')}&dataRole=all`);
      return;
    }
    fetchData();
  }, [year, allStaffs?.length]);

  return (
    <>
      <div className="container mx-auto p-1 md:p-4">
        <div className="rounded-xl bg-white p-4 text-black dark:bg-gray-600 dark:text-white">
          <div className="flex flex-col gap-4">
            <div className="flex items-center">
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-4">
                  <p className="text-xl font-semibold">List of User</p>
                  <div className="w-[150px]">
                    <DateInput
                      views={['year']}
                      value={year}
                      returnedFormat="YYYY"
                      onChange={(event) => setParam({ year: event?.target?.value })}
                    />
                  </div>
                </div>
                <p className="text-sm">List of member registered</p>
              </div>
              <div />
            </div>
            <Divider />
            <div className="flex flex-col items-center justify-between gap-4 md:flex-row md:gap-0">
              <FilterRoleButtonGroup />
              <div className="flex items-center justify-center gap-2">
                <SearchInput />
                {getTableData().length > 0 && (
                  <ExportExcelButton
                    key={getTableData()?.length}
                    data={getTableData()}
                    filename={`collection-${dataRole}-${year}.csv`}
                  />
                )}
                <button
                  type="button"
                  className="flex items-center justify-center gap-2 rounded-[4px] border border-[#e5e7eb] bg-[#ceff8f] px-4 py-2 text-xs font-semibold text-black"
                  onClick={() => handleClickOpenDialog(false)}
                >
                  <Image src="/assets/icons/Add User.svg" alt="User Icon" width={14} height={14} />
                  Add New User
                </button>
              </div>
            </div>
            <div className="mt-4 overflow-x-auto scrollbar">
              <table className="min-w-full bg-white text-black">
                <thead>
                  <tr>
                    {[
                      'No.',
                      'Staff ID',
                      'Full Name',
                      'Working Level',
                      'Monthly Contribution',
                      'Others Contribution',
                      'Email',
                      'Status',
                      '',
                    ].map((label, i) => (
                      <td key={i} className="bg-payme text-center text-sm text-white">
                        <p className={label === 'Full Name' ? 'text-left' : 'text-center'}>
                          {label}
                        </p>
                      </td>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {getTableData()
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((row, i) => (
                      <tr
                        key={i}
                        className="transition duration-150 odd:bg-[#f8f8f8] even:bg-white hover:bg-gray-200"
                      >
                        <td className={getBodyCellStyle()}>{i + 1 + rowsPerPage * page}</td>
                        <td className={getBodyCellStyle()}>{row.user_id.toUpperCase()}</td>
                        <td className={getBodyCellStyle()}>
                          <div className="flex items-center gap-4">
                            <p className="whitespace-nowrap">{row.name}</p>
                          </div>
                        </td>
                        <td className={getBodyCellStyle()}>{getRoleText(row.position)}</td>
                        <td className={getBodyCellStyle()}>{row.monthly_contribution}</td>
                        <td className={getBodyCellStyle()}>{row.others_contribution}</td>
                        <td className={getBodyCellStyle()}>{row.email}</td>
                        <td className={getBodyCellStyle()}>
                          {toUpperCaseFirstLetter(row.user_status)}
                        </td>
                        <td className={getBodyCellStyle()}>
                          <EditOutlined
                            fontSize="medium"
                            onClick={() => handleClickOpenDialog(true, row)}
                            className="hover:cursor-pointer"
                          />
                          <PostAddOutlined
                            fontSize="medium"
                            onClick={() => handleClickOpenDialogCollection(false, row)}
                            className="hover:cursor-pointer"
                          />
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
            <TablePaginationCustom count={getTableData()?.length} />
          </div>
        </div>
      </div>

      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-payme text-center text-white">
          {!editModeDialog ? 'Add New' : 'Edit'} User
        </DialogTitle>
        <DialogContent>
          <div className="flex w-[400px] flex-col gap-4 px-2 py-4">
            <TextInput
              name="user_id"
              value={dialogData?.user_id}
              placeholder="Staff ID"
              disabled={editModeDialog}
              onChange={handleDialogDataChange}
            />
            <SelectInput
              type="number"
              name="monthly_contribution"
              value={dialogData?.monthly_contribution}
              placeholder="Monthly Contribution (RM)"
              options={[5, 10, 15, 20, 25, 30, 50, 100]}
              onChange={handleDialogDataChange}
            />
            <TextInput
              type="number"
              name="others_contribution"
              value={dialogData?.others_contribution}
              placeholder="Others Contribution (RM)"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="year"
              value={dialogData?.year}
              placeholder="Year"
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between">
            <div className="flex">
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('delete')}
                  className="bg-[#ff6d6d] p-2 text-white"
                >
                  Delete
                </button>
              )}
            </div>
            <div className="flex gap-2">
              <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
                Cancel
              </button>
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-payme p-2 text-white"
                >
                  Save
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-payme p-2 text-white"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>

      <Dialog open={dialogOpenCollection} onClose={() => handleDialogCloseCollection(false)}>
        <DialogTitle className="bg-payme text-center text-white">
          {!editModeDialogCollection ? 'Add New' : 'Edit'} Collection
        </DialogTitle>
        <DialogContent>
          <div className="flex flex-col gap-4 p-4">
            <SelectInput
              name="year"
              value={dialogDataCollection.year}
              placeholder="Year"
              onChange={handleDialogDataChangeCollection}
              options={['2022', '2023', '2024', '2025', '2026', '2027', '2028']}
            />

            <SelectInput
              name="start_month"
              value={dialogDataCollection.start_month}
              placeholder="Start Month"
              onChange={handleDialogDataChangeCollection}
              options={[
                'jan',
                'feb',
                'mar',
                'apr',
                'may',
                'jun',
                'jul',
                'aug',
                'sep',
                'oct',
                'nov',
                'dec',
              ].map((o) => ({
                value: o,
                label: o,
              }))}
            />

            <SelectInput
              name="end_month"
              value={dialogDataCollection.end_month}
              placeholder="End Month"
              onChange={handleDialogDataChangeCollection}
              options={[
                'jan',
                'feb',
                'mar',
                'apr',
                'may',
                'jun',
                'jul',
                'aug',
                'sep',
                'oct',
                'nov',
                'dec',
              ].map((o) => ({
                value: o,
                label: o,
              }))}
            />

            <SelectInput
              name="others"
              value={dialogDataCollection.others}
              placeholder="Others Config"
              onChange={handleDialogDataChangeCollection}
              options={['true', 'false'].map((o) => ({
                value: o,
                label: o,
              }))}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-end">
            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => handleDialogCloseCollection(false)}
                className="p-2"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={() => handleDialogCloseCollection('post')}
                className="bg-payme p-2 text-white"
              >
                Add
              </button>
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
}
