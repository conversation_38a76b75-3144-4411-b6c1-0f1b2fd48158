// Next, React, Tw
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Mui
import { Switch, Divider } from '@mui/material';

// Packages
import moment from 'moment';

// Components
import Layout from '../../layouts/module/payme';
import { useSnackbar } from '../../components/Shared/snackbar';
import { DateInput } from '../../components/Shared/CustomInput';

// Others
import { PAYME_ENDPOINT } from '../../utils/payme';
import axios from '../../utils/axios';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const { enqueueSnackbar } = useSnackbar();
  const { query, asPath, replace } = useRouter();
  const { year } = query;
  const { setParam } = useParamContext();
  const dispatch = useDispatch();

  // Form
  const FORM_DATA = {
    apr: true,
    aug: true,
    dec: true,
    feb: true,
    jan: true,
    jul: true,
    jun: true,
    mar: true,
    may: true,
    nov: true,
    oct: true,
    sep: true,
  };
  const [formData, setFormData] = useState(FORM_DATA);
  const handleFormDataChange = async (event) => {
    const { name, checked } = event.target;
    handleFormSubmit('put', { [name]: checked });
  };

  const handleFormSubmit = async (action, keyValue) => {
    if (action) {
      try {
        if (Object.values(formData).includes(null)) {
          enqueueSnackbar('Please fill in all field.', {
            variant: 'error',
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'center',
            },
          });
          return;
        }
        let response;
        switch (action) {
          case 'put':
            response = await axios.put(`${PAYME_ENDPOINT}/config/all/${year}`, {
              ...formData,
              ...keyValue,
            });
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
      } catch (error) {
        // console.log(error);
      }
    }
    fetchData();
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${PAYME_ENDPOINT}/config/year/${year}`);
      if (response?.data?.status === 'success' && response.data.data.length > 0) {
        setFormData(response.data.data[0]);
      }
    } catch (error) {
      // console.log(error);
      if (error?.message === 'config data not found') {
        await axios.post(`${PAYME_ENDPOINT}/config/${year}`, FORM_DATA);
        const response = await axios.get(`${PAYME_ENDPOINT}/config/year/${year}`);
        setFormData(response.data.data[0]);
      }
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if ([undefined, '']?.includes(year)) {
      replace(`${asPath}?year=${moment()?.format('YYYY')}`);
      return;
    }
    fetchData();
  }, [year]);

  return (
    <div className="container text-black dark:text-white">
      <div className="my-8 flex flex-col gap-4">
        <div className="flex items-center">
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-4">
              <p className="text-xl font-semibold">Year Config</p>
              <p className="rounded-[4px] bg-[#f6faff] p-2 text-sm font-semibold text-[#0070ff]">
                {year}
              </p>
            </div>
            <p className="text-sm">Setup available month for payment</p>
          </div>
          <div />
        </div>
        <Divider />
        <div className="flex w-full flex-col justify-start gap-4 rounded-xl border bg-white p-8 shadow-xl dark:bg-gray-600 md:flex-row">
          <div className="mt-6 flex w-full flex-col md:mt-0 md:w-1/5">
            <div className="flex w-full items-center gap-2">
              <p className="whitespace-nowrap text-sm font-bold">Select Year</p>
              <div className="w-[150px]">
                <DateInput
                  views={['year']}
                  value={year}
                  returnedFormat="YYYY"
                  onChange={(event) => {
                    setParam({ year: event?.target?.value });
                  }}
                />
              </div>
            </div>
            <div>&nbsp;</div>
            {[
              {
                label: 'January',
                key: 'jan',
              },
              {
                label: 'February',
                key: 'feb',
              },
              {
                label: 'March',
                key: 'mar',
              },
              {
                label: 'April',
                key: 'apr',
              },
              {
                label: 'May',
                key: 'may',
              },
              {
                label: 'June',
                key: 'jun',
              },
              {
                label: 'July',
                key: 'jul',
              },
              {
                label: 'August',
                key: 'aug',
              },
              {
                label: 'September',
                key: 'sep',
              },
              {
                label: 'October',
                key: 'oct',
              },
              {
                label: 'November',
                key: 'nov',
              },
              {
                label: 'December',
                key: 'dec',
              },
              {
                label: 'Others',
                key: 'others',
              },
            ]?.map((o, i) => (
              <div key={i} className="flex">
                <div className="flex w-full items-center text-sm">
                  <p>{o?.label}</p>
                </div>
                <div className="flex items-center">
                  <Switch
                    name={o?.key}
                    onChange={handleFormDataChange}
                    checked={formData?.[o?.key]}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
