// Next, React, Tw
import { useState, useEffect } from 'react';
import { twMerge } from 'tailwind-merge';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';
import Image from 'next/image';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';

// Packages
import moment from 'moment';
import * as yup from 'yup';

// Components
import {
  TextInput,
  SelectInput,
  SearchInput,
  DateInput,
} from '../../components/Shared/CustomInput';
import { TablePaginationCustom } from '../../components/Shared/table';
import Layout from '../../layouts/module/payme';
import { CustomAvatar } from '../../components/Shared/custom-avatar';
import ReactAnimatedNumber from '../../components/Shared/ReactAnimatedNumber';
import TableSorting, { SortableTableHeader } from '../../components/Shared/TableSorting';

// Others
import axios from '../../utils/axios';
import { useSnackbar } from '../../components/Shared/snackbar';
import { toUpperCaseFirstLetter } from '../../utils/shared';
import { PAYME_ENDPOINT, getRoleText } from '../../utils/payme';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard and Vars
  const { enqueueSnackbar } = useSnackbar();
  const { query, push } = useRouter();
  const { q, year } = query;
  const { setParam, replaceParam } = useParamContext();
  const { user } = useAuthContext();
  const { isAdmin } = useModuleRoleContext();
  const dispatch = useDispatch();
  const { page, rowsPerPage } = useSelector((state) => state.simi);

  const [cardData, setCardData] = useState({});

  // Table
  const [tableData, setTableData] = useState([]);
  const [sortedTableData, setSortedTableData] = useState([]);
  
  const getTableData = () => {
    const dataToFilter = sortedTableData.length > 0 ? sortedTableData : tableData;
    if ([undefined, '']?.includes(q)) {
      return dataToFilter;
    }
    return dataToFilter.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  };
  
  const handleSort = (sortedData) => {
    setSortedTableData(sortedData);
  };
  const getBodyCellStyle = () => 'text-center whitespace-nowrap px-1 text-xs';

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }
    setDialogOpen(true);
  };

  const schema = yup.object().shape({
    user_id: yup.string().required(),
    user_reference: yup.string().required(),
    user_category: yup.string().required(),
    user_amount: yup.number().required(),
    year: yup.string().required(),
  });
  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }
      try {
        let response;
        const formData = new FormData();
        formData.append('user_id', payload?.user_id);
        formData.append('user_reference', payload?.user_reference);
        formData.append('user_amount', payload?.user_amount);
        formData.append('user_category', payload?.user_category);
        formData.append('year', payload?.year);

        switch (action) {
          case 'post':
            response = await axios.post(`${PAYME_ENDPOINT}/payment`, formData);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = 'Successfully added new expense';
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        /* empty */
      }
    }
    setDialogData({});
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const getStatusStyle = (status) => {
    if (status.toLowerCase().includes('pending')) {
      return 'bg-[#fff1aa]';
    }
    if (status.toLowerCase().includes('verified')) {
      return 'bg-[#a9ffb7]';
    }
    if (status.toLowerCase().includes('rejected')) {
      return 'bg-[#ffcdb7]';
    }
    return 'bg-white';
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(
        `${PAYME_ENDPOINT}/dashboard/user_summary/${user?.staff_id}/${year}`
      );
      setCardData(response.data.data[0]);
    } catch {
      setCardData({});
    }
    try {
      let param = `/user_id/${user?.staff_id}`;
      if (isAdmin) {
        param = '/all/keyword';
      }
      const response2 = await axios.get(`${PAYME_ENDPOINT}/transaction${param}`);
      setTableData(response2.data.data);
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if ([undefined, '']?.includes(year)) {
      replaceParam({ year: moment()?.format('YYYY') });
      return;
    }
    fetchData();
  }, [isAdmin, year]);

  return (
    <div className="container mx-auto p-1 md:p-4">
      <div className="rounded-xl bg-white text-black dark:bg-gray-600 dark:text-white">
        <div className="mx-auto flex w-full flex-col justify-center gap-6 p-4">
          <div className="flex items-center gap-4">
            <p className="text-sm font-bold">Summary</p>
            <div className="w-[150px]">
              <DateInput
                views={['year']}
                value={year}
                returnedFormat="YYYY"
                onChange={(event) => {
                  setParam({ year: event?.target?.value });
                }}
              />
            </div>
          </div>
          <div className="flex flex-col justify-between gap-4 md:flex-row">
            {[
              {
                title: 'Total Collection',
                number: cardData?.total_collection,
                customerStyle: 'bg-[#E3FFE7]',
                route: '/payme/collection',
                icon: '/assets/icons/payme - collection.svg',
              },
              {
                title: 'Total Pending Payment',
                number: cardData?.total_pending_payment,
                customerStyle: 'bg-[#F6E5E5]',
                route: undefined,
                icon: '/assets/icons/payme - pending.svg',
              },
              {
                title: 'Total Expenses',
                number: cardData?.total_expenses,
                customerStyle: 'bg-[#e3f5ff]',
                route: '/payme/expenses',
                icon: '/assets/icons/payme - expenses.svg',
              },
              {
                title: 'Total Pending Verification',
                number: cardData?.total_pending_verification,
                customerStyle: 'bg-[#e5ecf6]',
                route: '/payme/pending-verification',
                icon: '/assets/icons/payme - verification.svg',
              },
            ]?.map((o, i) => (
              <button
                type="button"
                className={`flex w-full flex-col gap-2 rounded-[4px]  p-4 text-black hover:cursor-pointer md:w-1/4 ${o?.customerStyle}`}
                onClick={() => {
                  if (o?.route) {
                    push(o?.route);
                  }
                }}
              >
                <div>
                  <p className="text-sm font-bold">{o?.title}</p>
                </div>
                <div className="flex w-full items-end justify-between gap-2 text-3xl font-bold">
                  <p>
                    RM &nbsp;
                    <ReactAnimatedNumber
                      value={o?.number}
                      formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                    />
                  </p>
                  <Image src={o.icon} alt="Card Image" width={40} height={40} />
                </div>
              </button>
            ))}
          </div>
          <div className="flex flex-col gap-2">
            <div className="flex">
              <p className="text-sm font-bold">Latest Transaction of Payment</p>
            </div>
            <div className="flex flex-col justify-between gap-2 md:flex-row">
              <SearchInput />
              <button
                type="button"
                className="rounded-[4px] border border-[#e5e7eb] bg-[#ceff8f] px-2 py-1 text-xs font-semibold text-black"
                onClick={() => handleClickOpenDialog(false)}
              >
                Add Payment
              </button>
            </div>
            <div className="my-4 overflow-x-auto scrollbar">
              <table className="min-w-full bg-white text-black">
                <thead>
                  <tr>
                    <TableSorting data={getTableData()} onSort={handleSort}>
                      {({ handleSort, getSortIcon }) => (
                        <>
                          <td className="bg-payme text-sm text-white">
                            <p className="text-center">No.</p>
                          </td>
                          <SortableTableHeader 
                            label="Staff Id" 
                            sortKey="user_id" 
                            handleSort={handleSort} 
                            getSortIcon={getSortIcon} 
                            className="bg-payme text-sm text-white"
                          />
                          <SortableTableHeader 
                            label="Name" 
                            sortKey="user_full_name" 
                            handleSort={handleSort} 
                            getSortIcon={getSortIcon} 
                            className="bg-payme text-sm text-white"
                          />
                          <SortableTableHeader 
                            label="Status" 
                            sortKey="status" 
                            handleSort={handleSort} 
                            getSortIcon={getSortIcon} 
                            className="bg-payme text-sm text-white"
                          />
                          <SortableTableHeader 
                            label="Role" 
                            sortKey="user_role" 
                            handleSort={handleSort} 
                            getSortIcon={getSortIcon} 
                            className="bg-payme text-sm text-white"
                          />
                          <SortableTableHeader 
                            label="Amount" 
                            sortKey="transaction_amount" 
                            handleSort={handleSort} 
                            getSortIcon={getSortIcon} 
                            className="bg-payme text-sm text-white"
                          />
                          <SortableTableHeader 
                            label="Category" 
                            sortKey="payment_method" 
                            handleSort={handleSort} 
                            getSortIcon={getSortIcon} 
                            className="bg-payme text-sm text-white"
                          />
                          <SortableTableHeader 
                            label="Last Updated" 
                            sortKey="last_updated" 
                            handleSort={handleSort} 
                            getSortIcon={getSortIcon} 
                            className="bg-payme text-sm text-white"
                          />
                        </>
                      )}
                    </TableSorting>
                  </tr>
                </thead>
                <tbody>
                  {getTableData()
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((row, i) => (
                      <tr
                        key={i}
                        className="transition duration-150 odd:bg-[#f8f8f8] even:bg-white hover:bg-gray-200"
                      >
                        <td className={getBodyCellStyle()}>{i + 1 + rowsPerPage * page}</td>
                        <td className={getBodyCellStyle()}>{row.user_id.toUpperCase()}</td>
                        <td className={twMerge(getBodyCellStyle(), '')}>
                          <div className="flex items-center gap-4">
                            <CustomAvatar name={row?.user_full_name} className="h-6 w-6" />
                            <p className="whitespace-nowrap">{row.user_full_name}</p>
                          </div>
                        </td>
                        <td className={twMerge(getBodyCellStyle(), getStatusStyle(row.status))}>
                          {toUpperCaseFirstLetter(row.status)}
                        </td>
                        <td className={getBodyCellStyle()}>{getRoleText(row.user_role)}</td>
                        <td className={getBodyCellStyle()}>{row.transaction_amount}</td>
                        <td className={getBodyCellStyle()}>
                          {toUpperCaseFirstLetter(row.payment_method)}
                        </td>
                        <td className={getBodyCellStyle()}>
                          {moment(row.last_updated).format('YYYY-MM-DD')}
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
            <TablePaginationCustom count={getTableData()?.length} />
          </div>
        </div>
        <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
          <DialogTitle className="bg-payme text-center text-white">Add New Payment</DialogTitle>
          <DialogContent>
            <div className="flex w-full flex-col gap-4 px-2 py-4  md:w-[400px]">
              <TextInput
                name="user_id"
                value={dialogData?.user_id}
                placeholder="Staff ID"
                onChange={handleDialogDataChange}
              />
              <TextInput
                name="user_reference"
                value={dialogData?.user_reference}
                placeholder="User reference"
                onChange={handleDialogDataChange}
              />
              <TextInput
                name="user_amount"
                value={dialogData?.user_amount}
                placeholder="Amount"
                onChange={handleDialogDataChange}
              />
              <SelectInput
                name="user_category"
                value={dialogData?.user_category}
                placeholder="Category"
                options={['monthly', 'other']}
                onChange={handleDialogDataChange}
              />
              <DateInput
                views={['year']}
                returnedFormat="YYYY"
                name="year"
                value={dialogData?.year}
                placeholder="Year"
                onChange={handleDialogDataChange}
              />
            </div>
          </DialogContent>
          <DialogActions>
            <div className="flex w-full justify-between">
              <div className="flex w-full justify-between gap-2">
                <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-payme cta-btn"
                >
                  Save
                </button>
              </div>
            </div>
          </DialogActions>
        </Dialog>
      </div>
    </div>
  );
}
