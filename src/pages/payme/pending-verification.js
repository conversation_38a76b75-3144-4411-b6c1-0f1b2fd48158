// Next, React, Tw
import Image from 'next/image';
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions, Divider } from '@mui/material';

// Packages
import moment from 'moment';
import { AttachFile } from '@mui/icons-material';

// Components
import Layout from '../../layouts/module/payme';
import ExportExcelButton from '../../components/Shared/ExportExcelButton';
import { CustomAvatar } from '../../components/Shared/custom-avatar';
import { TablePaginationCustom } from '../../components/Shared/table';
import { SearchInput } from '../../components/Shared/CustomInput';

// Others
import axios from '../../utils/axios';
import { useSnackbar } from '../../components/Shared/snackbar';
import { toUpperCaseFirstLetter } from '../../utils/shared';
import { PAYME_ENDPOINT, getRoleText } from '../../utils/payme';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Page.getLayout = (page) => <Layout>{page}</Layout>;

// ----------------------------------------------------------------------

export default function Page() {
  // Standard
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuthContext();
  const { isAdmin } = useModuleRoleContext();
  const { query } = useRouter();
  const { q } = query;
  const { page, rowsPerPage } = useSelector((state) => state.simi);
  const dispatch = useDispatch();

  // Table
  const [tableData, setTableData] = useState([]);
  const getBodyCellStyle = () => 'text-center whitespace-nowrap px-1 text-xs';

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [, setEditModeDialog] = useState(false);
  const DIALOG_DATA = {
    amount: null,
    remark: null,
    payment_date: null,
    attachment: null,
  };
  const [dialogData, setDialogData] = useState(DIALOG_DATA);

  const getTableData = () => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  };

  const handleClickOpenDialog = (editMode, data) => {
    setDialogData(data);
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };

  const handleDialogClose = async (action) => {
    if (action) {
      try {
        if (Object.values(dialogData).includes(null)) {
          enqueueSnackbar('Please fill in all field.', {
            variant: 'error',
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'center',
            },
          });
          return;
        }
        let response;
        switch (action) {
          case 'put-verify':
            await axios.put(`${PAYME_ENDPOINT}/transaction/${dialogData.transaction_id}`, {
              key: 'status',
              value: 'verified',
            });
            break;
          case 'put-reject':
            await axios.put(`${PAYME_ENDPOINT}/transaction/${dialogData.transaction_id}`, {
              key: 'status',
              value: 'rejected',
            });
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
      } catch (error) {
        // console.log(error);
      }
    }

    setDialogData(DIALOG_DATA);
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      let param = `/user_id/${user?.staff_id}`;
      if (isAdmin) {
        param = '/all/keyword';
      }
      const response = await axios.get(`${PAYME_ENDPOINT}/transaction${param}`);
      if (response.data.status === 'success') {
        const temp = response.data.data.filter((o) => o?.status.toLowerCase().includes('pending'));

        setTableData(temp);
      }
    } catch (error) {
      // console.log(error);
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div className="container mx-auto p-1 md:p-4">
        <div className="rounded-xl bg-white text-black dark:bg-gray-600 dark:text-white">
          <div className="mx-auto flex w-full flex-col justify-center gap-6 p-2">
            <div className="flex flex-col gap-2">
              <div className="flex flex-col items-center justify-between gap-4 md:flex-row md:gap-0">
                <div className="flex items-center">
                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-4">
                      <p className="text-xl font-semibold">Pending Verification</p>
                      <p className="rounded-[4px] bg-[#f6faff] p-2 text-sm font-semibold text-[#0070ff]">
                        {moment().format('YYYY')}
                      </p>
                    </div>
                    <p className="text-sm ">List of transactions to be verified</p>
                  </div>
                  <div />
                </div>
              </div>
              <Divider />
              <div className="mt-2 flex justify-end gap-2">
                <SearchInput />
                {tableData.length > 0 && (
                  <ExportExcelButton
                    data={tableData}
                    filename={`pending-verification-${moment().format('YYYY')}.csv`}
                  />
                )}
              </div>
              <div className="mt-2 overflow-x-auto scrollbar">
                <table className="min-w-full bg-white text-black">
                  <thead>
                    <tr>
                      {[
                        'No.',
                        'Staff Id',
                        'Name',
                        'Status',
                        'Role',
                        'Amount',
                        'Category',
                        'Last Updated',
                        '',
                      ].map((label, i) => (
                        <td key={i} className="bg-payme text-center text-sm text-white">
                          {label}
                        </td>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {getTableData()
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((row, i) => (
                        <tr
                          key={i}
                          className="transition duration-150 odd:bg-[#f8f8f8] even:bg-white hover:bg-gray-200"
                        >
                          <td className={getBodyCellStyle()}>{i + 1 + rowsPerPage * page}</td>
                          <td className={getBodyCellStyle()}>{row.user_id.toUpperCase()}</td>
                          <td className={getBodyCellStyle()}>
                            <div className="flex items-center gap-4">
                              <CustomAvatar name={row?.user_full_name} className="h-8 w-8" />
                              <p className="whitespace-nowrap">{row.user_full_name}</p>
                            </div>
                          </td>
                          <td className={getBodyCellStyle()}>
                            <p className="whitespace-nowrap rounded-xl bg-[#fff1aa] p-1 text-center text-black">
                              {toUpperCaseFirstLetter(row.status)}
                            </p>
                          </td>
                          <td className={getBodyCellStyle()}>{getRoleText(row.user_role)}</td>
                          <td className={getBodyCellStyle()}>{row.transaction_amount}</td>
                          <td className={getBodyCellStyle()}>
                            {toUpperCaseFirstLetter(row.payment_method)}
                          </td>
                          <td className={getBodyCellStyle()}>
                            {moment(row.last_updated).format('YYYY-MM-DD HH:mm')}
                          </td>
                          <td className={getBodyCellStyle()}>
                            <div className="flex gap-4">
                              <button
                                type="button"
                                onClick={() => handleClickOpenDialog(true, row)}
                              >
                                <Image
                                  src="/assets/icons/View Icon.svg"
                                  alt="View Icon"
                                  width={25}
                                  height={25}
                                />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
              <TablePaginationCustom count={getTableData()?.length} />
            </div>
          </div>
        </div>
      </div>

      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-payme text-white">
          <div className="flex flex-col gap-1">
            <p>Payment Details</p>
            <p>{dialogData?.user_full_name}</p>
          </div>
        </DialogTitle>
        <DialogContent>
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-4">
              <div className="w-[7rem] whitespace-nowrap">Status </div>
              <div className="">:</div>
              <div className="whitespace-nowrap">{toUpperCaseFirstLetter(dialogData.status)}</div>
            </div>
            <div className="flex items-center gap-4">
              <div className="w-[7rem] whitespace-nowrap">Date</div>
              <div className="">:</div>
              <div className="text-center">{dialogData.last_updated}</div>
            </div>
            <div className="flex items-center gap-4">
              <div className="w-[7rem] whitespace-nowrap">Reference</div>
              <div className="">:</div>
              <div className="text-center">{dialogData.payment_reference}</div>
            </div>
            <div className="flex items-center gap-4">
              <div className="w-[7rem] whitespace-nowrap">Attachment</div>
              <div className="">:</div>
              <div className="w-[7rem] whitespace-nowrap">
                <span className="flex justify-center rounded-md bg-[#ffbc70] p-1 text-center">
                  <AttachFile /> Download
                </span>
              </div>
            </div>
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex justify-end gap-2">
            <button
              type="button"
              onClick={() => handleDialogClose(false)}
              className="rounded-md border border-[#b7b7b7] px-2 py-1"
            >
              Close
            </button>
            <button
              type="button"
              onClick={() => handleDialogClose('put-verify')}
              className="rounded-md bg-[#b7ffc3] px-2 py-1 text-black"
            >
              Verify
            </button>
            <button
              type="button"
              onClick={() => handleDialogClose('put-reject')}
              className="rounded-md bg-[#ffcdb7] px-2 py-1 text-black"
            >
              Reject
            </button>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
}
