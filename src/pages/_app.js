// scroll bar
import 'simplebar-react/dist/simplebar.min.css';
import '../styles/global.css';

// lazy image
import 'react-lazy-load-image-component/src/effects/blur.css';

// doc viewer
import '@cyntler/react-doc-viewer/dist/index.css';

// leaflet map
import 'leaflet/dist/leaflet.css';

// ----------------------------------------------------------------------

import PropTypes from 'prop-types';
// next
import Head from 'next/head';
import { Provider } from 'react-redux';

import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { ThemeProvider } from 'next-themes';
// utils
import store from '../utils/store';
// theme
import Theme from '../theme';
// components
import ProgressBar from '../components/Shared/progress-bar';
import SnackbarProvider from '../components/Shared/snackbar';
import TokenExpiredSnackbar from '../components/Shared/snackbar/TokenExpiredSnackbar';
import { MotionLazyContainer } from '../components/Shared/animate';
import { AuthProvider } from '../utils/auth/JwtContext';
import ModuleRoleProvider from '../utils/auth/ModuleRoleProvider';
import ParamProvider from '../utils/auth/ParamProvider';

// ----------------------------------------------------------------------

MyApp.propTypes = {
  Component: PropTypes.elementType,
  pageProps: PropTypes.object,
};

export default function MyApp(props) {
  const { Component, pageProps } = props;

  const getLayout = Component.getLayout ?? ((page) => page);

  return (
    <>
      <Head>
        <title>SIMI</title>
        <meta name="viewport" content="initial-scale=1, width=device-width" />
      </Head>

      <AuthProvider>
        <Provider store={store}>
          <ModuleRoleProvider>
            <ParamProvider>
              <MotionLazyContainer>
                <ThemeProvider attribute="class">
                  <Theme>
                    <SnackbarProvider>
                      <TokenExpiredSnackbar>
                        <ProgressBar />
                        <LocalizationProvider dateAdapter={AdapterDayjs}>
                          {getLayout(<Component {...pageProps} />)}
                        </LocalizationProvider>
                      </TokenExpiredSnackbar>
                    </SnackbarProvider>
                  </Theme>
                </ThemeProvider>
              </MotionLazyContainer>
            </ParamProvider>
          </ModuleRoleProvider>
        </Provider>
      </AuthProvider>
    </>
  );
}
