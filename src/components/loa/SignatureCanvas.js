// Next, React, Tw
import { useEffect, useRef, useState } from 'react';

// Mui
import { DialogContent, DialogActions, Divider } from '@mui/material';

// Packages
import PropTypes from 'prop-types';

// Components
import { TextInput } from '../Shared/CustomInput';

// Others
import axios from '../../utils/axios';
import { AUM_ENDPOINT } from '../../utils/aum';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { useSnackbar } from '../Shared/snackbar';

const SignatureCanvas = ({ setDialogOpen, handleSubmitAndApprove }) => {
  // Standard and Vars
  const { user } = useAuthContext();
  const canvasRef = useRef(null);
  const fileInputRef = useRef(null);
  const { enqueueSnackbar } = useSnackbar();
  const [approveSignature, setApproveSignature] = useState('');

  // FORM
  const FORM_DATA = {
    no_ic: '',
  };

  const [formData, setFormData] = useState(FORM_DATA);
  const handleformDataChange = (event) => {
    const { name, value } = event.target;
    setFormData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  // Others
  const convertToBase64 = (file) =>
    new Promise((resolve, reject) => {
      const fileReader = new FileReader();
      fileReader.readAsDataURL(file);
      fileReader.onload = () => {
        resolve(fileReader.result);
      };
      fileReader.onerror = (error) => {
        reject(error);
      };
    });

  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!['jpeg', 'jpg', 'png'].includes(file.type.split('/')[file.type.split('/').length - 1])) {
      enqueueSnackbar('Please select an image with .jpeg, .jpg, or .png extension.', {
        variant: 'error',
      });
      return;
    }
    const base64 = await convertToBase64(file);
    const canvas = canvasRef?.current;
    const context = canvas.getContext('2d');

    const image = new Image();
    image.onload = () => {
      context.drawImage(image, 0, 0, 300, 150);
    };
    image.src = base64;
    setApproveSignature(base64);
  };

  const getSignature = () => {
    if (approveSignature !== '') {
      return approveSignature;
    }
    return canvasRef?.current.toDataURL('image/jpeg');
  };

  const validateUser = async () => {
    try {
      const response = await axios.post(
        `${AUM_ENDPOINT}/user/v1/validate/${user?.staff_id}/no_ic/${formData?.no_ic?.trim()}`
      );
      if (!response?.data?.data) {
        enqueueSnackbar('Failed to authenticate.', {
          variant: 'error',
        });
      }
      return response?.data?.data;
    } catch {
      enqueueSnackbar('Failed to authenticate.', {
        variant: 'error',
      });
    }
    return false;
  };

  const initializeCanvas = () => {
    const canvas = canvasRef?.current;
    const context = canvas.getContext('2d');
    const coord = { prev: { x: 0, y: 0 }, curr: { x: 0, y: 0 } };
    let paint = false;
    const getPosition = (event) => {
      let mouseX;
      let mouseY;
      if (event.offsetX) {
        mouseX = event.offsetX;
        mouseY = event.offsetY;
      } else if (event.layerX) {
        mouseX = event.layerX;
        mouseY = event.layerY;
      }
      coord.x = mouseX;
      coord.y = mouseY;
    };
    const startPainting = (event) => {
      paint = true;
      getPosition(event);
    };
    const stopPainting = () => {
      paint = false;
    };

    const sketch = (event) => {
      if (!paint) return;

      context.beginPath();
      context.lineWidth = 3;
      context.lineCap = 'round';
      context.strokeStyle = 'black';
      context.moveTo(coord.x, coord.y);
      getPosition(event);
      context.lineTo(coord.x, coord.y);
      context.stroke();
    };

    canvas.addEventListener('mousedown', startPainting);
    canvas.addEventListener('mouseup', stopPainting);
    canvas.addEventListener('mousemove', sketch);
  };

  useEffect(() => {
    initializeCanvas();
  }, []);

  return (
    <>
      <DialogContent>
        <div className="flex flex-col gap-4 p-2">
          <canvas ref={canvasRef} className="h-[150px] w-[300px] border-2 border-black" />
          {approveSignature === '' && (
            <p className="text-center">
              or upload your signature{' '}
              <button type="button" onClick={() => fileInputRef.current.click()}>
                <span className="text-[#1800e7] underline">here</span>
              </button>
              .
            </p>
          )}
          <input type="file" ref={fileInputRef} className="hidden" onChange={handleFileUpload} />
          <Divider />
          <TextInput
            name="no_ic"
            value={formData?.no_ic}
            placeholder="IC No. for Authentication"
            onChange={handleformDataChange}
          />
        </div>
      </DialogContent>
      <DialogActions>
        <div className="flex justify-end gap-4">
          <button type="button" onClick={() => setDialogOpen(false)} className="p-2">
            Cancel
          </button>
          <button
            type="button"
            onClick={async () => {
              if (!(await validateUser())) {
                return;
              }
              handleSubmitAndApprove('approve', getSignature());
            }}
            className="bg-[#1a1a1c] p-2 text-white"
          >
            Approve
          </button>
        </div>
      </DialogActions>
    </>
  );
};

SignatureCanvas.propTypes = {
  setDialogOpen: PropTypes.func,
  handleSubmitAndApprove: PropTypes.func,
};

export default SignatureCanvas;
