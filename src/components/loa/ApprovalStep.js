// Next, React, Tw
import { useState } from 'react';

// Mui
import { StepLabel } from '@mui/material';

// Packages
import PropTypes from 'prop-types';

// Components
import UserPopup from '../Shared/UserPopup';

// Others
import { toUpperCaseFirstLetter } from '../../utils/shared';
import { useSimiContext } from '../../utils/simi';

const ApprovalStepLabel = ({ approval }) => {
  // Standard and Vars
  const { getCertainStaffInfoFromName } = useSimiContext();
  // Others
  const [popOverPosition, setPopOverPosition] = useState(null);
  return (
    <StepLabel
      onClick={(event) => {
        setPopOverPosition(popOverPosition === null ? event.currentTarget : null);
      }}
      style={{ cursor: 'pointer' }}
    >
      <p className="font-semibold text-black">{approval?.approval_type}</p>
      {approval?.approval_type?.toLowerCase() !== 'completed' && (
        <p className="px-4 text-black">
          <UserPopup
            label={toUpperCaseFirstLetter(
              `${approval?.user_name?.split(' ')[0]} ${approval?.user_name?.split(' ')[1]}`
            )}
            staff_id={getCertainStaffInfoFromName(approval?.user_name, 'staff_id')}
          />
        </p>
      )}
    </StepLabel>
  );
};
ApprovalStepLabel.propTypes = {
  approval: PropTypes.object,
};
export default ApprovalStepLabel;
