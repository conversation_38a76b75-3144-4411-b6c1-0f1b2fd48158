// Next, React, Tw
import { useSelector, useDispatch } from 'react-redux';
import { twMerge } from 'tailwind-merge';
import { useRouter } from 'next/router';

// Packages
import * as yup from 'yup';

// Components
import { useSnackbar } from '../Shared/snackbar';
import WarnBeforeActionPopupButton from '../Shared/WarnBeforeActionPopupButton';

// Others
import { useAuthContext } from '../../utils/auth/useAuthContext';
import axios from '../../utils/axios';
import { LOA_ENDPOINT, loaIsEditable } from '../../utils/loa';
import { setIsLoading } from '../../utils/store/loadingReducer';

const LoaActionSection = () => {
  // Standard
  const { user } = useAuthContext();
  const { enqueueSnackbar } = useSnackbar();
  const { push, query } = useRouter();
  const { loaId } = query;
  const { loaData, loaTcvData, approvalList } = useSelector((state) => state.loa);

  const dispatch = useDispatch();

  // Dialog

  const MESSAGE = 'Please provide';
  const schema = yup.object({
    approval: yup.string().required(`${MESSAGE} approval`),
    created_by: yup.string().required(`${MESSAGE} created by`)?.default(user?.staff_id),
    customer: yup.string().required(`${MESSAGE} customer`),
    justification: yup.string().required(`${MESSAGE} justification`),
    loa: yup.string().required(`${MESSAGE} loa`),
    margin: yup.string().required(`${MESSAGE} margin`),
    service_description: yup.string().required(`${MESSAGE} service description`),
    sourcing_method: yup.string().required(`${MESSAGE} sourcing method`),
    status: yup.string().required(`${MESSAGE} status`),
    supplier: yup.string().required(`${MESSAGE} supplier`),
    type_approval: yup.string().required(`${MESSAGE} approval type`),
    total_contract_value: yup.object().shape({
      currency: yup.string().required(`${MESSAGE} currency`),
      currency_exchange: yup.string().optional(),
      mrc: yup.string().required(`${MESSAGE} MRC`),
      om: yup.string().required(`${MESSAGE} OM`),
      otc: yup.string().required(`${MESSAGE} OTC`),
      tcv: yup.string().optional(),
      tcv_in_rm: yup.string().required(`${MESSAGE} TCV in RM`),
      term: yup.string().required(`${MESSAGE} term`),
      total_om: yup.string().required(`${MESSAGE} total OM`),
    }),
  });

  const handleDelete = async () => {
    dispatch(setIsLoading(true));
    // Delete the LOA
    try {
      const response = await axios.delete(`${LOA_ENDPOINT}/${loaId}`);

      let statusVariant;
      let message;
      if (response.data.status === 'success') {
        statusVariant = 'success';
        message = response.data.data;
      } else {
        statusVariant = 'error';
        message = 'Failed';
      }

      enqueueSnackbar(message, {
        variant: statusVariant,
      });
    } catch (error) {
      // console.log(error);
    }

    // Delete Approval
    try {
      for (let i = 0; i < approvalList.length; i += 1) {
        if (approvalList[i]?.id !== undefined) {
          // eslint-disable-next-line
          await axios.delete(`${LOA_ENDPOINT}/approval/${approvalList[i]?.id}`);
        }
      }
    } catch (error) {
      // console.log(error);
    }

    // Delete remarks
    try {
      await axios.delete(`${LOA_ENDPOINT}/remark/${loaId}`);
    } catch (error) {
      // console.log(error);
    }

    // Delete history
    try {
      await axios.delete(`${LOA_ENDPOINT}/history/loa_id/${loaId}`);
    } catch (error) {
      // console.log(error);
    }

    // Delete Attachments
    try {
      await axios.delete(`${LOA_ENDPOINT}/file/loa_id/${loaId}`);
    } catch (error) {
      // console.log(error);
    }
    dispatch(setIsLoading(false));
    push('/loa');
  };

  const handleSaveDraft = async () => {
    // Check and Save LOA
    let payload = {
      ...loaData,
      status: 'draft',
      total_contract_value: loaTcvData,
    };
    try {
      payload = await schema.validate(payload);
    } catch (error) {
      // console.log(error);
    }
    dispatch(setIsLoading(true));
    try {
      const response = await axios.put(`${LOA_ENDPOINT}/${loaId}`, payload);
      let statusVariant;
      let message;
      if (response.data.status === 'success') {
        statusVariant = 'success';
        message = response.data.data;
      } else {
        statusVariant = 'error';
        message = 'Failed';
      }

      enqueueSnackbar(message, {
        variant: statusVariant,
      });
    } catch (error) {
      // console.log(error);
    }
    dispatch(setIsLoading(false));

    if (window) window.location.reload();
  };

  return (
    <div className={twMerge('flex w-full flex-col gap-2 md:flex-row md:justify-between')}>
      {loaIsEditable(loaData?.status) && (
        <WarnBeforeActionPopupButton onCancel={() => {}} onApprove={() => handleDelete()} />
      )}
      <div className="flex flex-col gap-2 md:flex-row">
        {loaIsEditable(loaData?.status) && (
          <button type="button" className="bg-loa cta-btn" onClick={() => handleSaveDraft()}>
            Save
          </button>
        )}
      </div>
    </div>
  );
};

export default LoaActionSection;
