// Next, React, Tw
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions, Divider } from '@mui/material';

// Components
import { TablePaginationCustom } from '../Shared/table';
import { SearchInput } from '../Shared/CustomInput';
import UserPopup from '../Shared/UserPopup';

// Others
import { LOA_ENDPOINT, useLoaContext } from '../../utils/loa';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { useSnackbar } from '../Shared/snackbar';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

const LoaTable = () => {
  // Standard
  const dispatch = useDispatch();
  const { user } = useAuthContext();
  const { query, push } = useRouter();
  const { q } = query;
  const { enqueueSnackbar } = useSnackbar();
  const { isAdmin } = useModuleRoleContext();

  const { handleCreateHistory } = useLoaContext();

  // Table
  const [tableData, setTableData] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(10);
  const onChangePage = (event, newPage) => {
    setPage(newPage);
  };
  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  })();

  const bodyCellStyle = 'text-center whitespace-nowrap p-1 text-sm';

  const getStatusComponent = (status) => {
    if (status.includes('pending')) {
      return (
        <div className="w-full rounded-3xl bg-[#69aeff] px-2 py-1 text-center font-semibold text-white">
          {status?.toUpperCase()}
        </div>
      );
    }
    if (status.includes('completed')) {
      return (
        <div className="w-full rounded-3xl bg-[#51cd26] px-2 py-1 text-center text-white">
          {status?.toUpperCase()}
        </div>
      );
    }
    return (
      <div className="w-full rounded-3xl bg-[#ffbc57] px-2 py-1 text-center font-semibold text-white">
        {status?.toUpperCase()}
      </div>
    );
  };

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const handleClickOpenDialog = (editMode, data) => {
    setDialogOpen(true);
  };

  const handleDialogClose = async (action) => {
    if (action) {
      dispatch(setIsLoading(true));

      try {
        const FORM_DATA = {
          approval: '',
          contract_term: '',
          created_by: user?.staff_id,
          customer: '',
          justification: '',
          loa: '',
          margin: '',
          service_description: '',
          sourcing_method: '',
          status: 'empty',
          supplier: '',
          type_approval: '',
        };
        const TCV_DATA = {
          currency: '',
          currency_exchange: '',
          mrc: '',
          om: '',
          otc: '',
          tcv: '',
          tcv_in_rm: '',
          term: '',
          total_om: '',
        };
        const response = await axios.post(`${LOA_ENDPOINT}`, {
          ...FORM_DATA,
          total_contract_value: TCV_DATA,
        });
        if (response.data.status === 'success') {
          await handleCreateHistory(response?.data?.data?.id, `LOA is created.`);
          enqueueSnackbar('New LOA created!', {
            variant: 'success',
          });
          push(`/loa/list/${response?.data?.data?.id}`);
        }
      } catch {
        /* empty */
      }
      dispatch(setIsLoading(false));
    }
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${LOA_ENDPOINT}/all/keyword`);
      if (response?.data?.data) {
        response.data.data.reverse();
        setTableData(response.data.data);
      }
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div className="flex flex-col justify-between gap-2 md:flex-row">
        <div className="flex flex-col">
          <p className="text-sm font-bold">All LOA</p>
          <p className="text-xs font-semibold">List of LOA Created</p>
        </div>
        <div className="flex gap-2">
          <SearchInput />
          {isAdmin && (
            <button
              type="button"
              className="bg-loa cta-btn"
              onClick={() => handleClickOpenDialog(false)}
            >
              NEW LOA
            </button>
          )}
        </div>
      </div>
      <Divider />
      <div className="mt-4 overflow-x-auto scrollbar">
        <table className="min-w-full">
          <thead>
            <tr>
              {['No.', 'LOA ID', 'Status', 'Created By', 'Created Date'].map((cell, i) => (
                <td key={i} className={`${bodyCellStyle} bg-loa text-sm text-white`}>
                  {cell}
                </td>
              ))}
            </tr>
          </thead>
          <tbody>
            {filteredTableData
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((row, i) => (
                <tr key={i} className={`${i % 2 === 0 ? 'bg-[#f8f8f8]' : 'bg-white'}`}>
                  <td className={bodyCellStyle}>{i + 1 + rowsPerPage * page}</td>
                  <td className={`${bodyCellStyle} text-xs`}>
                    <Link href={`/loa/list/${row?.id}`}>
                      <p className="text-[#3e9aee] underline">{row?.loa_id_name}</p>
                    </Link>
                  </td>
                  <td className={`${bodyCellStyle} text-xs`}>{getStatusComponent(row?.status)}</td>
                  <td className={`${bodyCellStyle} text-xs`}>
                    <UserPopup label={row?.created_by?.toUpperCase()} staff_id={row?.created_by} />
                  </td>
                  <td className={`${bodyCellStyle} text-xs`}>{row?.created_date}</td>
                </tr>
              ))}
          </tbody>
        </table>
      </div>
      <TablePaginationCustom
        count={filteredTableData.length}
        page={page}
        rowsPerPage={rowsPerPage}
        onPageChange={onChangePage}
      />
      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-loa text-center text-white">New LOA</DialogTitle>
        <DialogContent>
          <div className="mt-8 flex w-full flex-col gap-4 p-2 md:w-[400px]">
            <p className="text-center">Create?</p>
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
              Cancel
            </button>
            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => handleDialogClose('post')}
                className="bg-loa cta-btn"
              >
                Yes
              </button>
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default LoaTable;
