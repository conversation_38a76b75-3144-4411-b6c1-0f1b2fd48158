// Next, React, Tw
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Divider } from '@mui/material';

// Components
import { TablePaginationCustom } from '../Shared/table';
import { SearchInput } from '../Shared/CustomInput';

// Others
import { LOA_ENDPOINT } from '../../utils/loa';
import axios from '../../utils/axios';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { setIsLoading } from '../../utils/store/loadingReducer';

const PendingLoaTable = () => {
  // Standard
  const { user } = useAuthContext();
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { q } = query;

  // Table
  const [tableData, setTabledata] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(10);
  const onChangePage = (event, newPage) => {
    setPage(newPage);
  };
  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  })();

  const bodyCellStyle = 'text-center whitespace-nowrap p-1 text-sm';

  // Others
  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${LOA_ENDPOINT}/approval/approval_user/${user?.staff_id}`);
      if (response.data.status === 'success' && response.data.data !== null) {
        response.data.data = response.data.data.filter(
          (o) => o?.approval_status?.toLowerCase() === 'pending'
        );
        response.data.data.reverse();
        setTabledata(response.data.data);
      }
    } catch {
      setTabledata([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div className="flex flex-col justify-between gap-2 md:flex-row">
        <div className="flex flex-col">
          <p className="text-sm font-bold">My Basket</p>
          <p className="text-xs font-semibold">List of LOA Pending at Me</p>
        </div>
        <div className="flex">
          <SearchInput />
        </div>
      </div>
      <Divider />
      <div className="mt-4 overflow-x-auto scrollbar">
        <table className="min-w-full">
          <thead>
            <tr>
              {['No.', 'LOA ID', 'Role', 'Modified At'].map((cell, i) => (
                <td key={i} className={`${bodyCellStyle} bg-loa text-sm text-white`}>
                  {cell}
                </td>
              ))}
            </tr>
          </thead>
          <tbody>
            {filteredTableData.map((row, i) => (
              <tr className={`${i % 2 === 0 ? 'bg-[#f8f8f8]' : 'bg-white'}`}>
                <td className={bodyCellStyle}>{i + 1 + rowsPerPage * page}</td>
                <td className={`${bodyCellStyle} text-xs`}>
                  <Link href={`/loa/list/${row?.loa_id}`}>
                    <p className="text-[#3e9aee] underline">{row?.loa_id_name}</p>
                  </Link>
                </td>
                <td className={`${bodyCellStyle} text-xs`}>{row?.approval_type?.toUpperCase()}</td>
                <td className={`${bodyCellStyle} text-xs`}>{row?.modified_at}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <TablePaginationCustom
        count={filteredTableData.length}
        page={page}
        rowsPerPage={rowsPerPage}
        onPageChange={onChangePage}
      />
    </>
  );
};

export default PendingLoaTable;
