// React, Next, Tw
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';

// Components
import { SelectInput } from '../Shared/CustomInput';

// Others
import axios from '../../utils/axios';
import { LOA_ENDPOINT } from '../../utils/loa';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useAuthContext } from '../../utils/auth/useAuthContext';

const ApprovalSelectInput = () => {
  // Standard
  const dispatch = useDispatch();
  const router = useRouter();
  const { loaId } = router.query;
  const { user } = useAuthContext();

  const [escalationFlowList, setEscalationFlowList] = useState([]);

  // Others
  const handleOnSelect = async (event) => {
    const newApprovals = escalationFlowList[event?.target?.value];

    let existingApprovals = [];
    // Fetch Existings
    try {
      const response = await axios.get(`${LOA_ENDPOINT}/approvals/loa_id/${loaId}`);
      existingApprovals = response?.data?.data || [];
    } catch {
      existingApprovals = [];
    }

    // Delete Existing
    for (let i = 0; i < existingApprovals?.length; i += 1) {
      try {
        await axios.delete(`${LOA_ENDPOINT}/approvals/${existingApprovals?.[i]?.id}`);
      } catch {
        return;
      }
    }

    // Create new
    try {
      await axios.post(`${LOA_ENDPOINT}/approvals`, {
        is_disabled: true,
        loa_id: loaId,
        status: 'pending',
        type: 'preparer',
        user_name_array: [user?.name],
      });
    } catch {
      return;
    }
    for (let i = 0; i < newApprovals?.escalation_user?.length; i += 1) {
      try {
        await axios.post(`${LOA_ENDPOINT}/approvals`, {
          is_disabled: true,
          loa_id: loaId,
          status: 'pending',
          type: newApprovals?.escalation_user?.[i]?.approval_type,
          user_name_array: [newApprovals?.escalation_user?.[i]?.user_name],
        });
      } catch {
        return;
      }
    }
    if (window) window?.location?.reload();
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${LOA_ENDPOINT}/escalation/all/keyword`);

      setEscalationFlowList(response?.data?.data || []);
    } catch (error) {
      setEscalationFlowList([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [loaId]);

  return (
    <SelectInput
      defaultLabel="Select New"
      onChange={handleOnSelect}
      options={escalationFlowList?.map((o, i) => ({
        label: `${o?.escalation_type} - (${o?.escalation_user?.reduce((prev, curr) => `${prev}${curr?.approval_type},`, '')?.slice(0, -1)})`,
        value: i,
      }))}
    />
  );
};

export default ApprovalSelectInput;
