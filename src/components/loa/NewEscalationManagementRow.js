// Next, React, Tw
import { useSelector } from 'react-redux';

// Mui
import { Remove, Add } from '@mui/icons-material';

// Packages
import PropTypes from 'prop-types';

// Components
import { TextInput, AutoCompleteTextInput } from '../Shared/CustomInput';

const NewEscalationManagementRow = ({ escalationList, setEscalationList }) => {
  // Standard and Vars

  const { allStaffs } = useSelector((state) => state.aum);

  // Others

  return (
    <div className="min-w-[600px] px-2">
      {escalationList?.length === 0 && (
        <div className="flex  items-center justify-center gap-2">
          <button
            type="button"
            className="flex"
            onClick={() => {
              setEscalationList([
                {
                  approval_type: '',
                  user_name: '',
                },
              ]);
            }}
          >
            <Add />
          </button>
        </div>
      )}
      {escalationList?.map((o, i) => (
        <div key={i} className="flex items-center gap-2">
          <div className="w-[300px]">
            <TextInput
              value={o?.approval_type}
              placeholder="Type"
              onChange={(event) => {
                const temp = {
                  ...o,
                  approval_type: event?.target?.value,
                };
                setEscalationList((prev) => {
                  const temp2 = [...prev];
                  temp2[i] = temp;
                  return temp2;
                });
              }}
            />
          </div>

          <div className="w-[500px]">
            <AutoCompleteTextInput
              placeholder="Staff"
              value={o?.user_name}
              options={allStaffs?.map((p) => p?.name)}
              onChange={(event) => {
                const temp = {
                  ...o,
                  user_name: event?.target?.value,
                };
                setEscalationList((prev) => {
                  const temp2 = [...prev];
                  temp2[i] = temp;
                  return temp2;
                });
              }}
            />
          </div>
          <div className="flex items-center gap-2">
            <button
              type="button"
              className="flex"
              onClick={() => {
                setEscalationList((prev) => {
                  const temp = [...prev];
                  temp.splice(i, 1);
                  return temp;
                });
              }}
            >
              <Remove className="text-red-500" />
            </button>
            <button
              type="button"
              className="flex"
              onClick={() => {
                setEscalationList((prev) => {
                  const temp = [...prev];
                  temp?.splice(i + 1, 0, {
                    approval_type: '',
                    user_name: '',
                  });
                  return temp;
                });
              }}
            >
              <Add className="text-loa" />
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

NewEscalationManagementRow.propTypes = {
  escalationList: PropTypes?.object,
  setEscalationList: PropTypes?.func,
};

export default NewEscalationManagementRow;
