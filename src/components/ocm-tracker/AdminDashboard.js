// Next, React, Tw
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import { twMerge } from 'tailwind-merge';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { Edit, Visibility } from '@mui/icons-material';

// Packages
import moment from 'moment-business-days';
import { useSnackbar } from 'notistack';
import * as yup from 'yup';

// Components
import { TablePaginationCustom } from '../Shared/table';
import { SearchInput, TextInput, TimeInput } from '../Shared/CustomInput';
import ReactAnimatedNumber from '../Shared/ReactAnimatedNumber';

// Others
import { OCM_TRACKER_ENDPOINT } from '../../utils/ocm-tracker';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { fetchOcmsData } from '../../utils/store/ocmTrackerReducer';
import { useSimiContext } from '../../utils/simi';
import { checkAndReplaceStringWithHyphen } from '../../utils/shared';

const AdminDashboard = () => {
  // Standard and Vars
  const dispatch = useDispatch();
  const { push, query } = useRouter();
  const { q } = query;
  const { user } = useAuthContext();
  const { page, rowsPerPage } = useSelector((state) => state.simi);
  const { enqueueSnackbar } = useSnackbar();

  const { ocmsList } = useSelector((state) => state.ocmTracker);
  const { getCertainStaffInfoFromStaffId } = useSimiContext();

  // Table
  const bodyCellStyle = 'text-center py-1 text-sm';

  const filteredOcmsList = (() => {
    const temp = ocmsList;
    if ([undefined, '']?.includes(q)) {
      return temp;
    }
    return temp.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) setDialogData(data);
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };

  const schema = yup.object({
    created_by_staff_id: yup
      .string()
      .required('Please provide created by staff id.')
      ?.default(user?.staff_id),
    location: yup.string().required('Please provide location.'),
    time_from: yup.number().required('Please provide time from.'),
    time_to: yup.number().required('Please provide time to.'),
  });

  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }

      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${OCM_TRACKER_ENDPOINT}/ocm`, payload);
            break;
          case 'put':
            response = await axios.put(`${OCM_TRACKER_ENDPOINT}/ocm/${payload?.id}`, payload);
            break;
          case 'delete':
            response = await axios.delete(`${OCM_TRACKER_ENDPOINT}/ocm/${payload?.id}`);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    fetchData();
    setDialogData({});
    setDialogOpen(false);
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    await dispatch(fetchOcmsData(`/all/keyword`));
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div className="container mx-auto flex flex-col items-start justify-between gap-1 p-1 dark:text-white md:flex-row md:p-4">
        {[
          {
            label: 'Past OCM',
            value: filteredOcmsList?.filter((o) => o?.time_to < moment().unix())?.length,
          },
          {
            label: 'Upcoming OCM',
            value: filteredOcmsList?.filter((o) => o?.time_from > moment().unix())?.length,
          },
          {
            label: 'Total OCM',
            value: filteredOcmsList?.length,
          },
        ]?.map((o, i) => (
          <div key={i} className="flex w-full flex-col px-1 md:w-1/3">
            <div
              className={twMerge(
                'bg-ocm-tracker flex h-[90px] w-full cursor-pointer flex-col justify-between gap-2 rounded-md p-2 text-white'
              )}
            >
              <p className="text-wrap text-sm font-semibold">{o?.label}</p>
              <p className="text-right text-3xl">
                <ReactAnimatedNumber
                  value={o?.value}
                  formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                />
              </p>
            </div>
          </div>
        ))}
      </div>
      <div className="container mx-auto flex gap-2 p-1 md:p-4">
        <div className="flex-grow">
          <div className="flex flex-col gap-2 rounded-md bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
            <div className="flex w-full justify-between">
              <div className="flex gap-2">
                <SearchInput />
              </div>
              <button
                type="button"
                className="bg-ocm-tracker  cta-btn"
                onClick={() => handleClickOpenDialog(false)}
              >
                New OCM
              </button>
            </div>
            <div className="flex w-full flex-col">
              <div className="overflow-x-auto scrollbar">
                <table className="min-w-full bg-white text-black">
                  <thead>
                    <tr>
                      {['No.', 'OCM ID', 'Time', 'Location', 'Created By', ''].map((label, i) => (
                        <td
                          key={i}
                          className="bg-ocm-tracker whitespace-nowrap px-4 text-center text-white"
                        >
                          {label}
                        </td>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {filteredOcmsList
                      ?.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      ?.map((row, i) => (
                        <tr key={i} className="odd:bg-white even:bg-[#f8f8f8] ">
                          <td className={bodyCellStyle}>{i + 1}</td>
                          <td className={bodyCellStyle}>{row?.ocm_id}</td>
                          <td className={bodyCellStyle}>
                            {moment.unix(row?.time_from)?.format('DD/MMM/YYYY hh:mm A')} -
                            {moment.unix(row?.time_to)?.format('DD/MMM/YYYY hh:mm A')}
                          </td>
                          <td className={bodyCellStyle}>{row?.location}</td>
                          <td className={bodyCellStyle}>
                            {checkAndReplaceStringWithHyphen(
                              getCertainStaffInfoFromStaffId(row?.created_by_staff_id, 'name')
                            )}
                          </td>
                          <td className={twMerge(bodyCellStyle, 'flex gap-2')}>
                            <button
                              type="button"
                              onClick={() => push(`/ocm-tracker/${row?.ocm_id}`)}
                            >
                              <Visibility className="text-ocm-tracker" />
                            </button>
                            <button type="button" onClick={() => handleClickOpenDialog(true, row)}>
                              <Edit className="text-ocm-tracker" />
                            </button>
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
              <TablePaginationCustom count={filteredOcmsList.length} />
            </div>
          </div>
        </div>
      </div>
      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-ocm-tracker text-center text-white">
          {!editModeDialog ? 'New' : 'Edit'} OCM
        </DialogTitle>
        <DialogContent>
          <div className="my-8 flex w-full flex-col gap-2 p-2 md:w-[400px]">
            <TextInput
              name="location"
              value={dialogData?.location}
              placeholder="Location"
              onChange={handleDialogDataChange}
            />
            <TimeInput
              name="time_from"
              value={dialogData?.time_from}
              placeholder="From"
              returnedFormat="unix"
              onChange={handleDialogDataChange}
            />
            <TimeInput
              name="time_to"
              value={dialogData?.time_to}
              placeholder="To"
              returnedFormat="unix"
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
              Cancel
            </button>
            <div className="flex gap-2">
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-ocm-tracker cta-btn"
                >
                  Save
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('delete')}
                  className=" cta-btn bg-red-500"
                >
                  Delete
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-ocm-tracker cta-btn"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default AdminDashboard;
