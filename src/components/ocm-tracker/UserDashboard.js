// Next, React, Tw
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import { twMerge } from 'tailwind-merge';

// Components
import ReactAnimatedNumber from '../Shared/ReactAnimatedNumber';
import PapersListTable from './PapersListTable';

// Others
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { fetchPapersData } from '../../utils/store/ocmTrackerReducer';

const UserDashboard = () => {
  // Standard and Vars
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { q } = query;
  const { user } = useAuthContext();

  const { papersList } = useSelector((state) => state.ocmTracker);

  // Table

  const filteredTableData = (() => {
    const temp = papersList;
    if ([undefined, '']?.includes(q)) {
      return temp;
    }
    return temp.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    await dispatch(fetchPapersData(`/created_by_staff_id/${user?.staff_id}`));
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [user?.staff_id]);

  return (
    <>
      <div className="container mx-auto flex flex-col items-start justify-between gap-1 p-1 dark:text-white md:flex-row md:p-4">
        {[
          {
            label: 'Pending',
            value: filteredTableData?.filter((o) => o?.status === 'pending')?.length,
          },
          {
            label: 'Queried',
            value: filteredTableData?.filter((o) => o?.status === 'queried')?.length,
          },
          {
            label: 'Approved',
            value: filteredTableData?.filter((o) => o?.status === 'approved')?.length,
          },
          {
            label: 'Total',
            value: filteredTableData?.length,
          },
        ]?.map((o, i) => (
          <div key={i} className="flex w-full flex-col px-1 md:w-1/3">
            <div
              className={twMerge(
                'bg-ocm-tracker flex h-[90px] w-full cursor-pointer flex-col justify-between gap-2 rounded-md p-2 text-white'
              )}
            >
              <p className="text-wrap text-sm font-semibold">{o?.label}</p>
              <p className="text-right text-3xl">
                <ReactAnimatedNumber
                  value={o?.value}
                  formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                />
              </p>
            </div>
          </div>
        ))}
      </div>
      <PapersListTable
        key={filteredTableData?.length}
        tableData={filteredTableData}
        fetchData={fetchData}
      />
    </>
  );
};

export default UserDashboard;
