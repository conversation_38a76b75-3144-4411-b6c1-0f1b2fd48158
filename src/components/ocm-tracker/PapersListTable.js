// Next, React, Tw
import { useState } from 'react';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import { twMerge } from 'tailwind-merge';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { Edit, Visibility } from '@mui/icons-material';

// Packages
import { useSnackbar } from 'notistack';
import PropTypes from 'prop-types';

// Components
import { TablePaginationCustom } from '../Shared/table';
import { SearchInput, TextAreaInput, SelectInput } from '../Shared/CustomInput';

// Others
import {
  OCM_TRACKER_ENDPOINT,
  useOcmTrackerContext,
  getStatusColor,
  STRATEGY_TEAM_EMAILS,
} from '../../utils/ocm-tracker';
import axios from '../../utils/axios';
import { useSimiContext } from '../../utils/simi';
import { checkAndReplaceStringWithHyphen } from '../../utils/shared';
import { useAuthContext } from '../../utils/auth/useAuthContext';

const PapersListTable = ({ fetchData }) => {
  // Standard and Vars
  const { push, query, asPath } = useRouter();
  const { q } = query;
  const { page, rowsPerPage } = useSelector((state) => state.simi);
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuthContext();

  const { papersList } = useSelector((state) => state.ocmTracker);
  const { getCertainStaffInfoFromStaffId, handleSendEmail } = useSimiContext();
  const { paperSchema } = useOcmTrackerContext();

  // Table
  const bodyCellStyle = 'text-center py-1 text-sm';

  const filteredTableData = (() => {
    const temp = papersList;
    if ([undefined, '']?.includes(q)) {
      return temp;
    }
    return temp.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) setDialogData(data);
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };

  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await paperSchema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }

      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${OCM_TRACKER_ENDPOINT}/paper`, payload);
            break;
          case 'put':
            response = await axios.put(`${OCM_TRACKER_ENDPOINT}/paper/${payload?.id}`, payload);
            break;
          case 'delete':
            response = await axios.delete(`${OCM_TRACKER_ENDPOINT}/paper/${payload?.id}`);
            break;
          default:
            break;
        }
        if (response.data.status === 'success') {
          switch (action) {
            case 'post':
              await handleSendEmail(
                STRATEGY_TEAM_EMAILS,
                [user?.email],
                `New Paper`,
                `                    
                  <p>New Paper created.</p>\n
                  <p>Kindly, please click this <a href="${process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT}${asPath}">link</a> to review it.</p>
                `
              );
              break;
            default:
              break;
          }
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    fetchData();
    setDialogData({});
    setDialogOpen(false);
  };

  // Others

  const paperTypeOptions = ['matters arising', 'strategy', 'customer', 'business performance'];

  const paperCategoryOptions = (() => {
    const paperTypeAndCategoriesMapping = {
      'matters arising': ['n/a'],
      strategy: ['new', 'update', 'info'],
      customer: ['new', 'existing'],
      'business performance': ['new', 'update', 'info'],
    };
    return paperTypeAndCategoriesMapping[dialogData?.type] || [];
  })();

  const existingCustomerTitleOptions = (() => {
    let temp = [
      'Increase Credit Limit',
      'Novation | New Customer | From Customer A (existing) to Customer B (new cust)',
      'Novation | Existing Customer | From Customer A (existing) to Customer B (existing) ',
      'Change of Ownership | From TM Tech to TMRO',
      'Change of Payment Method | From Prepayment to Postpayment',
    ];
    if (
      ['Division Malaysia Carrier Sales', 'Division International Sales']?.includes(user?.division)
    )
      temp = [
        'Establish MSA | For existing customer, but NO MSA',
        'Establish MSA | For existing customer (existing MSA with TM Tech, to add Supplementary Agreement with TMRO)',
        ...temp,
      ];
    return temp;
  })();

  return (
    <>
      <div className="container mx-auto flex gap-2 p-1 md:p-4">
        <div className="flex-grow">
          <div className="flex flex-col gap-2 rounded-md bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
            <div className="flex w-full justify-between">
              <div className="flex gap-2">
                <SearchInput />
              </div>
              <button
                type="button"
                className="bg-ocm-tracker  cta-btn"
                onClick={() => handleClickOpenDialog(false)}
              >
                New Paper
              </button>
            </div>
            <div className="flex w-full flex-col">
              <div className="overflow-x-auto scrollbar">
                <table className="min-w-full bg-white text-black">
                  <thead>
                    <tr>
                      {[
                        'No.',
                        'Paper ID',
                        'Type',
                        'Category',
                        'Title',
                        'Status',
                        'Created By',
                        '',
                      ].map((label, i) => (
                        <td
                          key={i}
                          className="bg-ocm-tracker whitespace-nowrap px-4 text-center text-white"
                        >
                          {label}
                        </td>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {filteredTableData
                      ?.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      ?.map((row, i) => (
                        <tr key={i} className="odd:bg-white even:bg-[#f8f8f8] ">
                          <td className={bodyCellStyle}>{i + 1}</td>
                          <td className={bodyCellStyle}>{row?.paper_id}</td>
                          <td className={bodyCellStyle}>{row?.type?.toUpperCase()}</td>
                          <td className={bodyCellStyle}>{row?.category?.toUpperCase()}</td>
                          <td className={bodyCellStyle}>{row?.title}</td>
                          <td className={twMerge(bodyCellStyle, getStatusColor(row?.status))}>
                            {row?.status?.toUpperCase()}
                          </td>
                          <td className={bodyCellStyle}>
                            {checkAndReplaceStringWithHyphen(
                              getCertainStaffInfoFromStaffId(row?.created_by_staff_id, 'name')
                            )}
                          </td>
                          <td className={twMerge(bodyCellStyle, 'flex gap-2')}>
                            <button
                              type="button"
                              onClick={() => push(`/ocm-tracker/papers/${row?.paper_id}`)}
                            >
                              <Visibility className="text-ocm-tracker" />
                            </button>
                            <button type="button" onClick={() => handleClickOpenDialog(true, row)}>
                              <Edit className="text-ocm-tracker" />
                            </button>
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
              <TablePaginationCustom count={filteredTableData.length} />
            </div>
          </div>
        </div>
      </div>
      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-ocm-tracker text-center text-white">
          {!editModeDialog ? 'New' : 'Edit'} Paper
        </DialogTitle>
        <DialogContent>
          <div className="my-8 flex w-full flex-col gap-2 p-2 md:w-[400px]">
            <SelectInput
              name="type"
              value={dialogData?.type}
              placeholder="Paper Type"
              options={paperTypeOptions}
              onChange={handleDialogDataChange}
            />
            <SelectInput
              name="category"
              value={dialogData?.category}
              placeholder="Category"
              options={paperCategoryOptions}
              onChange={handleDialogDataChange}
            />
            {dialogData?.type === 'customer' && dialogData?.category === 'existing' ? (
              <SelectInput
                name="title"
                value={dialogData?.title}
                placeholder="Title"
                options={existingCustomerTitleOptions}
                onChange={handleDialogDataChange}
              />
            ) : (
              <TextAreaInput
                name="title"
                value={dialogData?.title}
                placeholder="Title"
                onChange={handleDialogDataChange}
              />
            )}
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
              Cancel
            </button>
            <div className="flex gap-2">
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-ocm-tracker cta-btn"
                >
                  Save
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('delete')}
                  className=" cta-btn bg-red-500"
                >
                  Delete
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-ocm-tracker cta-btn"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

PapersListTable.propTypes = {
  fetchData: PropTypes.func,
};

export default PapersListTable;
