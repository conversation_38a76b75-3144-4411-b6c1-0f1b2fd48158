// Next, React, Tw
import { useRouter } from 'next/router';
import { useSelector, useDispatch } from 'react-redux';
import { useEffect, useState, useCallback } from 'react';
import PropTypes from 'prop-types';

// Packages
import * as R from 'ramda';

// Components
import {
  TextInput,
  SelectInput,
  AutoCompleteTextInput,
  BinarySwitchInput,
} from '../Shared/CustomInput';
import ActionButtonGroup from './ActionButtonGroup';
import SelectRemoveStaffListContainer from '../Shared/SelectRemoveStaffListContainer';
import { useSnackbar } from '../Shared/snackbar';


import { IconButton } from './ui/EnhancedButton';

// Wrapper component to match approval workflow styling
const FormField = ({ label, children, required = false }) => (
  <div className="flex flex-col gap-1">
    <p className="text-xs font-medium text-gray-700">
      {label?.toUpperCase()}{required && <span className="font-semibold text-red-500">&nbsp;*</span>}
    </p>
    {children}
  </div>
);

// Others
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { setProjectData, fetchProjectData } from '../../utils/store/faReducer';
import { ALL_TM_CUSTOMER_ACCOUNT_ENDPOINT } from '../../utils/shared';
import { AUM_ENDPOINT } from '../../utils/aum';
import { FA_ENDPOINT } from '../../utils/fa';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { setBreadCrumbsList } from '../../utils/store/simiReducer';

const ProjectDetails = ({ scenarioId, exportMode = false, onProjectInfoChange = null, disabled = false }) => {
  // Standard
  const { query } = useRouter();
  const { projectId } = query;
  const dispatch = useDispatch();
  const { isAdmin } = useModuleRoleContext();
  const { user } = useAuthContext();
  const { enqueueSnackbar } = useSnackbar();

  const { projectData } = useSelector((state) => state.fa);

  // Allow editing if user is admin OR if user created the project
  const canEdit = isAdmin || (projectData?.created_by_staff_id === user?.staff_id);
  
  // Check if project is in draft status (handle empty string as draft)
  const isDraftStatus = !projectData?.status || 
                       projectData?.status?.toString().toLowerCase().trim() === 'draft' || 
                       projectData?.status?.toString().trim() === '';
  
  const IS_DISABLED = disabled || !isDraftStatus || !canEdit;

  const [accountList, setAccountList] = useState([]);
  const [originalProjectData, setOriginalProjectData] = useState(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Form
  const handleFormDataChange = (event, fieldName = null) => {
    let name, value;

    // Handle both event objects and direct values from AutoCompleteTextInput
    if (event && typeof event === 'object' && event.target) {
      // Standard event object from regular inputs
      ({ name, value } = event.target);
    } else if (fieldName) {
      // Direct value with explicit field name (for custom handling)
      name = fieldName;
      value = event;
    } else {
      // Handle cases where event might be undefined or malformed
      console.error('Invalid event object passed to handleFormDataChange:', event);
      return;
    }

    // Handle boolean fields properly
    let processedValue = value;
    if (name === 'stamping_fees_needed') {
      processedValue = value === 'true' || value === true;
    }
    
    // Only convert empty string to null for select fields, but preserve actual selected values
    // Don't convert "-" as it might be a valid selected value in some cases
    if (processedValue === '') {
      processedValue = null;
    }
    
    // Update Redux state with the new field value
    const newState = { ...projectData, [name]: processedValue };
    dispatch(setProjectData(newState));
    
    // Track changes for save button
    if (originalProjectData) {
      // Check if the new value is different from the original
      const originalValue = originalProjectData[name];
      const hasChanged = originalValue !== processedValue;
      
      if (hasChanged && !hasUnsavedChanges) {
        setHasUnsavedChanges(true);
      }
    }
  };

  // Save functionality
  const handleSaveProjectInfo = async () => {
    if (!hasUnsavedChanges) {
      return;
    }

    dispatch(setIsLoading(true));

    try {
      // Helper function to preserve actual values instead of converting to null
      const preserveValue = (value) => {
        // Only return empty string if the value is actually null or undefined
        // This preserves empty strings that the user intentionally cleared
        return value === null || value === undefined ? '' : value;
      };

      // Ensure all fields are properly included in the save payload
      const savePayload = {
        ...projectData,
        entity: 'TM Tech',
        // Set proper defaults for required fields
        currency: projectData?.currency || 'myr',
        budgeted_in_aop: projectData?.budgeted_in_aop || 'no',
        viewer_staff_id_list: projectData?.viewer_staff_id_list || [],
        exchange_rate: projectData?.exchange_rate || 0,
        stamping_fees_needed: Boolean(projectData?.stamping_fees_needed),
        // Preserve actual field values (including empty strings)
        customer_name: preserveValue(projectData?.customer_name),
        purpose: preserveValue(projectData?.purpose),
        product_segment: preserveValue(projectData?.product_segment),
        level_of_authority: preserveValue(projectData?.level_of_authority),
        customer_segment: preserveValue(projectData?.customer_segment),
        dtc_no: preserveValue(projectData?.dtc_no),
        nrp_reference_no: preserveValue(projectData?.nrp_reference_no)
      };

      console.log('Save payload debug:', {
        original: projectData,
        payload: savePayload,
        problemFields: {
          customer_name: { original: projectData?.customer_name, final: savePayload.customer_name },
          purpose: { original: projectData?.purpose, final: savePayload.purpose },
          product_segment: { original: projectData?.product_segment, final: savePayload.product_segment }
        }
      });

      const response = await axios.put(`${FA_ENDPOINT}/project/${projectId}`, savePayload);

      if (response.data.status === 'success') {
        // Force fresh data fetch from API to ensure persistence
        const fetchResult = await dispatch(fetchProjectData(projectId));
        
        // Reset change tracking after successful fetch
        if (fetchResult.payload && Object.keys(fetchResult.payload).length > 0) {
          setOriginalProjectData({ ...fetchResult.payload });
          setHasUnsavedChanges(false);
        }
        
        enqueueSnackbar('Project information saved successfully', {
          variant: 'success',
        });
      } else {
        throw new Error(response.data.message || 'Save failed');
      }
    } catch (error) {
      console.error('Save project info error:', error);
      enqueueSnackbar(`Failed to save: ${error.message}`, {
        variant: 'error',
      });
    } finally {
      dispatch(setIsLoading(false));
    }
  };

  // Others

  const fetchAllAccounts = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${ALL_TM_CUSTOMER_ACCOUNT_ENDPOINT}`);
      if (response?.data?.data) {
        // Format account list as objects with label and value properties
        const formattedAccounts = response?.data?.data?.map((o) => ({
          label: o?.name,
          value: o?.name
        }));
        setAccountList(formattedAccounts);
      }
    } catch {
      setAccountList([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchAllAccounts();
  }, [projectId]);

  // Breadcrumb is now handled at the page level

  // Initialize original project data and update parent component with save state
  useEffect(() => {
    if (projectData && Object.keys(projectData).length > 0) {
      // Only update original data when we don't have it yet (initial load)
      // or when we explicitly reset it (after successful save)
      if (!originalProjectData) {
        setOriginalProjectData({ ...projectData });
        setHasUnsavedChanges(false);
      }
    }
  }, [projectData]);

  // Update parent component with save state
  useEffect(() => {
    if (onProjectInfoChange) {
      onProjectInfoChange({
        hasChanges: hasUnsavedChanges,
        isSaving: false,
        disabled: IS_DISABLED,
        onSave: handleSaveProjectInfo
      });
    }
  }, [hasUnsavedChanges, IS_DISABLED, onProjectInfoChange]);

  if (exportMode) {
    return (
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between pb-2">
          <p className="text-lg font-bold">Profile</p>
        </div>
        <div className="flex flex-col gap-4">
        {/* <SelectInput
            name="entity"
            value={projectData?.entity}
            placeholder="Entity"
            options={['TM Tech', 'TM HK', 'TM SG', 'TM US']}
            onChange={handleFormDataChange}
            disabled={IS_DISABLED}
          /> */}
        <TextInput
          name="name"
          value={projectData?.name}
          placeholder="Project Name"
          onChange={handleFormDataChange}
          disabled={IS_DISABLED}
        />
        <AutoCompleteTextInput
          name="customer_name"
          value={projectData?.customer_name}
          placeholder="Customer"
          options={accountList}
          onChange={(value) => handleFormDataChange(value, 'customer_name')}
          disabled={IS_DISABLED}
          optionMustBeSelected={false}
        />
        <SelectInput
          name="type"
          value={projectData?.type}
          placeholder="FA Type"
          options={['new', 'revised', 'part of business case']}
          onChange={handleFormDataChange}
          disabled={IS_DISABLED}
        />
        <div className="flex flex-col">
          <SelectInput
            name="category"
            value={projectData?.category}
            placeholder="Category"
            options={['single', 'hybrid']}
            onChange={handleFormDataChange}
            disabled={IS_DISABLED}
          />
          <div className="pl-2 text-xs italic text-gray-500">
            Note: 'Single' category allows only one scenario, 'Hybrid' allows multiple scenarios
          </div>
        </div>
        <SelectInput
          name="customer_segment"
          value={projectData?.customer_segment}
          placeholder="Customer Segment"
          options={['domestic', 'international']}
          onChange={handleFormDataChange}
          disabled={IS_DISABLED}
        />
        <SelectInput
          name="product_segment"
          value={projectData?.product_segment}
          placeholder="Product Segment"
          options={['domestic', 'international']}
          onChange={handleFormDataChange}
          disabled={IS_DISABLED}
        />
        <div className="flex items-center justify-end gap-2">
          <div className="flex-grow">
            <SelectInput
              name="currency"
              value={projectData?.currency}
              placeholder="Currency"
              options={['myr', 'usd']}
              onChange={handleFormDataChange}
              disabled={IS_DISABLED}
            />
          </div>

        </div>
        {projectData?.currency === 'usd' && (
          <TextInput
            type="number"
            name="exchange_rate"
            value={projectData?.exchange_rate}
            placeholder={`Exchange Rate - ${projectData?.exchange_rate_as_of_date}`}
            onChange={handleFormDataChange}
            disabled={IS_DISABLED}
          />
        )}
        <SelectInput
          name="purpose"
          value={projectData?.purpose}
          placeholder="Purpose"
          options={[
            'quotation',
            'upgrade',
            'renewal',
            'relocation/migration',
            'event base',
            'tender',
          ]}
          onChange={handleFormDataChange}
          disabled={IS_DISABLED}
          allowOtherValue
        />
        <SelectInput
          name="level_of_authority"
          value={projectData?.level_of_authority}
          placeholder="LOA"
          options={[
            'itf',
            'pcm',
            'ocm',
            'boc',
            'mc',
            'board',
            'pac',
            'jpp1',
            'jpp2',
            'tier 2 (vp)',
            'tier 2 (gm)',
            'tier 2 (agm)',
            'n/a',
          ]}
          onChange={handleFormDataChange}
          disabled={IS_DISABLED}
          allowOtherValue
        />
        <TextInput
          name="nrp_reference_no"
          value={projectData?.nrp_reference_no}
          placeholder="NRP Ref. No."
          onChange={handleFormDataChange}
          disabled={IS_DISABLED}
          showRedAsteric={false}
        />
        <TextInput
          name="dtc_no"
          value={projectData?.dtc_no}
          placeholder="DTC No."
          onChange={handleFormDataChange}
          disabled={IS_DISABLED}
          showRedAsteric={false}
        />
        <SelectInput
          name="budgeted_in_aop"
          value={projectData?.budgeted_in_aop}
          placeholder="Budgeted in AOP"
          options={['no', 'yes']}
          onChange={handleFormDataChange}
          disabled={IS_DISABLED}
        />
        <BinarySwitchInput
          name="stamping_fees_needed"
          value={Boolean(projectData?.stamping_fees_needed)}
          placeholder="Stamping Fees Needed"
          onChange={handleFormDataChange}
          disabled={IS_DISABLED}
        />
          <div className="w-full rounded-lg border border-blue-200 bg-blue-50 px-3 py-2">
            <p className="text-xs font-medium text-blue-700 mb-2">Viewers</p>
            <SelectRemoveStaffListContainer
              key={projectId}
              INITIAL_STAFF_LIST={projectData?.viewer_staff_id_list}
              KEY="staff_id"
              CALLBACK_FUNCTION={(selectedStaffList) => {
                const newStaffList = R.pluck('staff_id')(selectedStaffList);
                dispatch(
                  setProjectData({
                    ...projectData,
                    viewer_staff_id_list: newStaffList,
                  })
                );
                // Track changes for save button
                if (originalProjectData) {
                  // Check if the new staff list is different from the original
                  const originalStaffList = originalProjectData.viewer_staff_id_list || [];
                  const hasChanged = JSON.stringify(originalStaffList.sort()) !== JSON.stringify(newStaffList.sort());
                  
                  if (hasChanged && !hasUnsavedChanges) {
                    setHasUnsavedChanges(true);
                  }
                }
              }}
              disabled={IS_DISABLED}
              customColor="#4F46E5" /* Indigo color */
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-lg border border-slate-300 bg-white p-6 text-black">
      <div className="space-y-8">
          {/* Basic Information Section */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">
              Basic Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField label="Project Name" required>
                <TextInput
                  name="name"
                  value={projectData?.name}
                  placeholder=""
                  onChange={handleFormDataChange}
                  disabled={IS_DISABLED}
                  showRedAsteric={false}
                />
              </FormField>
              <FormField label="Customer">
                <AutoCompleteTextInput
                  name="customer_name"
                  value={projectData?.customer_name}
                  placeholder=""
                  options={accountList}
                  onChange={(value) => handleFormDataChange(value, 'customer_name')}
                  disabled={IS_DISABLED}
                  optionMustBeSelected={false}
                  hideExternalLabel={true}
                />
              </FormField>
              <FormField label="FA Type" required>
                <SelectInput
                  name="type"
                  value={projectData?.type}
                  placeholder=""
                  options={['new', 'revised', 'part of business case']}
                  onChange={handleFormDataChange}
                  disabled={IS_DISABLED}
                  showRedAsteric={false}
                />
              </FormField>
              <div className="flex flex-col gap-1">
                <FormField label="Category" required>
                  <SelectInput
                    name="category"
                    value={projectData?.category}
                    placeholder=""
                    options={['single', 'hybrid']}
                    onChange={handleFormDataChange}
                    disabled={IS_DISABLED}
                    showRedAsteric={false}
                  />
                </FormField>
                <div className="text-xs italic text-gray-500 mt-1">
                  Note: 'Single' allows one scenario, 'Hybrid' allows multiple
                </div>
              </div>
            </div>
          </div>

          {/* Business Information Section */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">
              Business Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField label="Customer Segment">
                <SelectInput
                  name="customer_segment"
                  value={projectData?.customer_segment}
                  placeholder=""
                  options={['domestic', 'international']}
                  onChange={handleFormDataChange}
                  disabled={IS_DISABLED}
                  showRedAsteric={false}
                />
              </FormField>
              <FormField label="Product Segment">
                <SelectInput
                  name="product_segment"
                  value={projectData?.product_segment}
                  placeholder=""
                  options={['domestic', 'international']}
                  onChange={handleFormDataChange}
                  disabled={IS_DISABLED}
                  showRedAsteric={false}
                />
              </FormField>
              <FormField label="Purpose">
                <SelectInput
                  name="purpose"
                  value={projectData?.purpose}
                  placeholder=""
                  options={[
                    'quotation',
                    'upgrade',
                    'renewal',
                    'relocation/migration',
                    'event base',
                    'tender',
                  ]}
                  onChange={handleFormDataChange}
                  disabled={IS_DISABLED}
                  allowOtherValue
                  showRedAsteric={false}
                />
              </FormField>
              <FormField label="Level of Authority (LOA)">
                <SelectInput
                  name="level_of_authority"
                  value={projectData?.level_of_authority}
                  placeholder=""
                  options={[
                    'itf',
                    'pcm',
                    'ocm',
                    'boc',
                    'mc',
                    'board',
                    'pac',
                    'jpp1',
                    'jpp2',
                    'tier 2 (vp)',
                    'tier 2 (gm)',
                    'tier 2 (agm)',
                    'n/a',
                  ]}
                  onChange={handleFormDataChange}
                  disabled={IS_DISABLED}
                  allowOtherValue
                  showRedAsteric={false}
                />
              </FormField>
            </div>
          </div>

          {/* Financial Information Section */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">
              Financial Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField label="Currency" required>
                <div className="flex items-center gap-2">
                  <div className="flex-grow">
                    <SelectInput
                      name="currency"
                      value={projectData?.currency}
                      placeholder=""
                      options={['myr', 'usd']}
                      onChange={handleFormDataChange}
                      disabled={IS_DISABLED}
                      showRedAsteric={false}
                    />
                  </div>
                  {projectData?.currency === 'usd' && (
                    <div className="relative group">
                      <button
                        type="button"
                        disabled={IS_DISABLED}
                        onClick={async () => {
                          try {
                            const response = await axios.get(`${AUM_ENDPOINT}/exchange_rate/v1/latest/keyword`);
                            if (response?.data?.data?.[0]?.data?.USD) {
                              dispatch(
                                setProjectData({
                                  ...projectData,
                                  exchange_rate: response.data.data[0].data.USD,
                                })
                              );
                            }
                          } catch (error) {
                            console.error('Failed to fetch exchange rate:', error);
                          }
                        }}
                        className="p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                      </button>
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                        Fetch USD → MYR from BNM
                      </div>
                    </div>
                  )}
                </div>
              </FormField>
              {projectData?.currency === 'usd' && (
                <FormField label="Exchange Rate">
                  <TextInput
                    type="number"
                    name="exchange_rate"
                    value={projectData?.exchange_rate}
                    placeholder=""
                    onChange={handleFormDataChange}
                    disabled={IS_DISABLED}
                    showRedAsteric={false}
                  />
                </FormField>
              )}
              <FormField label="Budgeted in AOP">
                <SelectInput
                  name="budgeted_in_aop"
                  value={projectData?.budgeted_in_aop}
                  placeholder=""
                  options={['no', 'yes']}
                  onChange={handleFormDataChange}
                  disabled={IS_DISABLED}
                  showRedAsteric={false}
                />
              </FormField>
              <FormField label="Stamping Fees Needed" required>
                <BinarySwitchInput
                  name="stamping_fees_needed"
                  value={Boolean(projectData?.stamping_fees_needed)}
                  placeholder=""
                  onChange={handleFormDataChange}
                  disabled={IS_DISABLED}
                  showRedAsteric={false}
                />
              </FormField>
            </div>
          </div>

          {/* Reference Information Section */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">
              Reference Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField label="NRP Reference No.">
                <TextInput
                  name="nrp_reference_no"
                  value={projectData?.nrp_reference_no}
                  placeholder=""
                  onChange={handleFormDataChange}
                  disabled={IS_DISABLED}
                  showRedAsteric={false}
                />
              </FormField>
              <FormField label="DTC No.">
                <TextInput
                  name="dtc_no"
                  value={projectData?.dtc_no}
                  placeholder=""
                  onChange={handleFormDataChange}
                  disabled={IS_DISABLED}
                  showRedAsteric={false}
                />
              </FormField>
            </div>
          </div>

          {/* Project Access Section */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">
              Project Access
            </h3>
            <div className="w-full rounded-lg border border-blue-200 bg-blue-50 px-4 py-4">
              <p className="text-sm font-medium text-blue-700 mb-3">Project Viewers</p>
              <SelectRemoveStaffListContainer
                key={projectId}
                INITIAL_STAFF_LIST={projectData?.viewer_staff_id_list}
                KEY="staff_id"
                CALLBACK_FUNCTION={(selectedStaffList) => {
                  const newStaffList = R.pluck('staff_id')(selectedStaffList);
                  dispatch(
                    setProjectData({
                      ...projectData,
                      viewer_staff_id_list: newStaffList,
                    })
                  );
                  // Track changes for save button
                  if (originalProjectData) {
                    // Check if the new staff list is different from the original
                    const originalStaffList = originalProjectData.viewer_staff_id_list || [];
                    const hasChanged = JSON.stringify(originalStaffList.sort()) !== JSON.stringify(newStaffList.sort());
                    
                    if (hasChanged && !hasUnsavedChanges) {
                      setHasUnsavedChanges(true);
                    }
                  }
                }}
                disabled={IS_DISABLED}
                customColor="#2563EB" /* Blue-600 to match design system */
              />
            </div>
          </div>


      </div>
    </div>
  );
};

ProjectDetails.propTypes = {
  scenarioId: PropTypes.string,
  exportMode: PropTypes.bool,
  onProjectInfoChange: PropTypes.func,
  disabled: PropTypes.bool,
};

export default ProjectDetails;
