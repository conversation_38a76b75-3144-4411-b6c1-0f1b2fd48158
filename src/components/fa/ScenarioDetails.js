// Next, React, Tw
import { useEffect, useState, useCallback, useRef } from 'react';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';

// Mui
import { Tabs, Tab, Divider, Tooltip } from '@mui/material';

// Components
import AllCapex from './AllCapex';
import AllOpex from './AllOpex';
import AllRevenue from './AllRevenue';
import FinancialAnalysis from './FinancialAnalysis';
import ProjectDetails from './ProjectDetails';
import Summary from './Summary';
import CollapsibleSection from '../Shared/CollapsibleSection';
import { DocumentIcon, ChartBarIcon, CollectionIcon } from '../Shared/SectionIcons';

// Others
import { useParamContext } from '../../utils/auth/ParamProvider';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { fetchScenarioData } from '../../utils/store/faReducer';
import moment from 'moment';

const ScenarioDetails = () => {
  // Standard and Vars
  const { setParam, replaceParam } = useParamContext();
  const { query } = useRouter();
  const { tab, scenarioId, projectId } = query;
  const dispatch = useDispatch();
  const { scenarioData, projectData } = useSelector((state) => state.fa);

  // Add a state to track the active tab
  const [activeTab, setActiveTab] = useState(tab || 'capex');
  
  // State for storing calculated totals from child components
  const [capexTotal, setCapexTotal] = useState(null);
  const [opexTotals, setOpexTotals] = useState({
    otc: null,
    otcAmortise: null,
    mrc: null
  });
  const [revenueTotals, setRevenueTotals] = useState({
    otc: null,
    otcAmortise: null,
    mrc: null,
    contractValue: null
  });

  // Parallax scroll state for scenario subheader
  const [scrollY, setScrollY] = useState(0);
  const [isScrolled, setIsScrolled] = useState(false);
  const subheaderRef = useRef(null);

  // Parallax scroll handler for scenario subheader
  const handleScroll = useCallback(() => {
    const scrollContainer = document.getElementById('scroll-container');
    if (!scrollContainer) return;

    const scrollTop = scrollContainer.scrollTop;
    const threshold = 25; // Reduced threshold for earlier trigger
    const fadeRange = 15; // Reduced fade range for quicker transition

    setScrollY(scrollTop);

    // Smooth transition zone
    if (scrollTop > threshold + fadeRange) {
      setIsScrolled(true);
    } else if (scrollTop < threshold - fadeRange) {
      setIsScrolled(false);
    }
  }, []);

  // Setup scroll listener with proper cleanup
  useEffect(() => {
    const scrollContainer = document.getElementById('scroll-container');
    if (!scrollContainer) return;

    let ticking = false;
    let animationId = null;
    
    const throttledHandleScroll = () => {
      if (!ticking) {
        animationId = requestAnimationFrame(() => {
          if (scrollContainer) { // Check if container still exists
            handleScroll();
          }
          ticking = false;
          animationId = null;
        });
        ticking = true;
      }
    };

    scrollContainer.addEventListener('scroll', throttledHandleScroll, { passive: true });

    return () => {
      scrollContainer?.removeEventListener('scroll', throttledHandleScroll);
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [handleScroll]);

  // Others
  const fetchAllData = async () => {
    dispatch(setIsLoading(true));
    await dispatch(fetchScenarioData(scenarioId));
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchAllData();
  }, [scenarioId]);

  useEffect(() => {
    if (!tab) replaceParam({ tab: 'capex' });
    else setActiveTab(tab);
  }, [tab]);

  // Calculate key metrics for the scenario dashboard
  const getTotalCapex = () => {
    if (!scenarioData?.total_capex || Number(scenarioData?.total_capex) === 0) {
      return null;
    }
    return Number(scenarioData.total_capex).toLocaleString();
  };

  const getTotalOpex = () => {
    if (!scenarioData?.total_opex || Number(scenarioData?.total_opex) === 0) {
      return null;
    }
    return Number(scenarioData.total_opex).toLocaleString();
  };

  const getTotalRevenue = () => {
    if (!scenarioData?.total_revenue || Number(scenarioData?.total_revenue) === 0) {
      return null;
    }
    return Number(scenarioData.total_revenue).toLocaleString();
  };

  // Add a function to handle export for each section
  const handleExport = (section) => {
    setParam({ screenshotDialogOpen: true, exportSection: section });
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header section - with parallax fade out animation */}
      <div
        ref={subheaderRef}
        className="bg-gradient-to-r from-gray-50 to-blue-50 border-b border-gray-200 shadow-sm sticky top-0 z-30 pb-3 transition-opacity duration-300 ease-out"
        style={{
          opacity: isScrolled ? 0 : 1,
          pointerEvents: isScrolled ? 'none' : 'auto'
        }}
      >
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div>
              <div className="flex items-center gap-2">
                <h1 className="text-xl font-bold text-gray-800">{scenarioData?.name || 'Scenario Details'}</h1>
                <span className="px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary">
                  {scenarioData?.scenario_id || 'N/A'}
                </span>
              </div>
              <div className="mt-1 flex flex-wrap items-center gap-x-4 gap-y-2 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <span className="font-medium">Contract Period:</span> 
                  <span>{scenarioData?.contract_period_in_month} Months</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="font-medium">Project:</span> 
                  <span className="text-primary">{projectData?.name}</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="font-medium">Created:</span> 
                  <span>{scenarioData?.created_at ? moment(scenarioData.created_at).format('DD MMM YYYY') : 'N/A'}</span>
                </div>
              </div>
            </div>

          </div>
        </div>

        {/* Navigation Cards - updated to show description text instead of 0 */}
        <div className="container mx-auto px-4 mt-3">
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3">
            {/* Profile Card */}
            <div 
              onClick={() => setParam({ tab: 'profile' })}
              className={`bg-white/90 backdrop-blur-sm p-3 rounded-lg shadow-sm border cursor-pointer transition-all duration-200 flex flex-col h-full
                ${activeTab === 'profile' ? 'border-primary ring-1 ring-primary' : 'border-gray-200 hover:border-gray-300'}`}
            >
              <div className="flex items-center gap-1.5 mb-1">
                <DocumentIcon />
                <span className="text-xs font-medium text-gray-700">Profile</span>
              </div>
              <div className="text-xs text-gray-500">Scenario Information</div>
            </div>

            {/* CAPEX Card */}
            <div 
              onClick={() => setParam({ tab: 'capex' })}
              className={`bg-white/90 backdrop-blur-sm p-3 rounded-lg shadow-sm border cursor-pointer transition-all duration-200 flex flex-col h-full
                ${activeTab === 'capex' ? 'border-primary ring-1 ring-primary' : 'border-gray-200 hover:border-gray-300'}`}
            >
              <div className="flex items-center gap-1.5 mb-1">
                <CollectionIcon />
                <span className="text-xs font-medium text-gray-700">CAPEX</span>
              </div>
              <div className="text-xs text-gray-500 mt-auto">Capital expenditure details</div>
            </div>

            {/* OPEX Card */}
            <div 
              onClick={() => setParam({ tab: 'opex' })}
              className={`bg-white/90 backdrop-blur-sm p-3 rounded-lg shadow-sm border cursor-pointer transition-all duration-200 flex flex-col h-full
                ${activeTab === 'opex' ? 'border-primary ring-1 ring-primary' : 'border-gray-200 hover:border-gray-300'}`}
            >
              <div className="flex items-center gap-1.5 mb-1">
                <CollectionIcon />
                <span className="text-xs font-medium text-gray-700">OPEX</span>
              </div>
              <div className="text-xs text-gray-500 mt-auto">Operating cost details</div>
            </div>

            {/* Revenue Card */}
            <div 
              onClick={() => setParam({ tab: 'revenue' })}
              className={`bg-white/90 backdrop-blur-sm p-3 rounded-lg shadow-sm border cursor-pointer transition-all duration-200 flex flex-col h-full
                ${activeTab === 'revenue' ? 'border-primary ring-1 ring-primary' : 'border-gray-200 hover:border-gray-300'}`}
            >
              <div className="flex items-center gap-1.5 mb-1">
                <CollectionIcon />
                <span className="text-xs font-medium text-gray-700">Revenue</span>
              </div>
              <div className="text-xs text-gray-500 mt-auto">Revenue streams</div>
            </div>

            {/* Financial Analysis Card */}
            <div 
              onClick={() => setParam({ tab: 'fa' })}
              className={`bg-white/90 backdrop-blur-sm p-3 rounded-lg shadow-sm border cursor-pointer transition-all duration-200 flex flex-col h-full
                ${activeTab === 'fa' ? 'border-primary ring-1 ring-primary' : 'border-gray-200 hover:border-gray-300'}`}
            >
              <div className="flex items-center gap-1.5 mb-1">
                <ChartBarIcon />
                <span className="text-xs font-medium text-gray-700">Financial Analysis</span>
              </div>
              <div className="text-xs text-gray-500 mt-auto">Detailed metrics</div>
            </div>

            {/* Summary Card */}
            <div 
              onClick={() => setParam({ tab: 'summary' })}
              className={`bg-white/90 backdrop-blur-sm p-3 rounded-lg shadow-sm border cursor-pointer transition-all duration-200 flex flex-col h-full
                ${activeTab === 'summary' ? 'border-primary ring-1 ring-primary' : 'border-gray-200 hover:border-gray-300'}`}
            >
              <div className="flex items-center gap-1.5 mb-1">
                <ChartBarIcon />
                <span className="text-xs font-medium text-gray-700">Summary</span>
              </div>
              <div className="text-xs text-gray-500 mt-auto">Key financial figures</div>
            </div>
          </div>
          
          <div className="flex justify-end mt-2">
            <Tooltip title="Click on any card to view detailed information">
              <div className="text-gray-500 cursor-help flex items-center text-xs gap-1">
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Click a card to view section details</span>
              </div>
            </Tooltip>
          </div>
        </div>
      </div>

      {/* Tab content with improved layout */}
      <div className="container mx-auto px-4 py-6">
        {tab === 'profile' && (
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <div className="border-b border-gray-200 bg-gray-50 px-4 py-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <DocumentIcon />
                  <h2 className="font-semibold text-lg">Scenario Information</h2>
                </div>
              </div>
            </div>
            <div className="p-4">
              <ProjectDetails scenarioId={scenarioData?.id} disabled={true} />
            </div>
          </div>
        )}

        {tab === 'capex' && (
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <div className="border-b border-gray-200 bg-gray-50 px-4 py-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CollectionIcon />
                  <h2 className="font-semibold text-lg">CAPEX Details</h2>
                  <Tooltip title="Capital expenditures">
                    <div className="text-gray-400 cursor-help">
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </div>
                  </Tooltip>
                  <span className="ml-2 px-2 py-0.5 text-xs bg-blue-100 text-blue-800 rounded-full">
                    Total: {capexTotal !== null ? capexTotal.toLocaleString() : (scenarioData?.total_capex ? Number(scenarioData.total_capex).toLocaleString() : 'N/A')} {projectData?.currency?.toUpperCase()}
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <button
                    type="button"
                    className="bg-primary hover:bg-primary/90 flex items-center gap-1.5 whitespace-nowrap rounded-lg px-4 py-2 text-sm font-medium text-white shadow-sm transition-all duration-200"
                    onClick={() => handleExport('capex')}
                  >
                    <svg
                      className="h-4 w-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      ></path>
                    </svg>
                    Export
                  </button>
                </div>
              </div>
            </div>
            <div className="p-4">
              <AllCapex 
                scenarioId={scenarioData?.id} 
                showExportButton={false} 
                onDataLoaded={(capexData) => {
                  // Calculate total from the capexData directly, just like in the table's Total row
                  const total = capexData?.reduce((prev, curr) => prev + (curr?.amount || 0), 0);
                  setCapexTotal(total);
                }}
              />
            </div>
          </div>
        )}

        {tab === 'opex' && (
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <div className="border-b border-gray-200 bg-gray-50 px-4 py-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CollectionIcon />
                  <h2 className="font-semibold text-lg">OPEX Details</h2>
                  <div className="ml-2 flex flex-wrap gap-2">
                    <span className="px-2 py-0.5 text-xs bg-blue-100 text-blue-800 rounded-full">
                      OTC: {opexTotals.otc !== null ? opexTotals.otc.toLocaleString() : 'N/A'} {projectData?.currency?.toUpperCase()}
                    </span>
                    <span className="px-2 py-0.5 text-xs bg-green-100 text-green-800 rounded-full">
                      OTC Amortise: {opexTotals.otcAmortise !== null ? opexTotals.otcAmortise.toLocaleString() : 'N/A'} {projectData?.currency?.toUpperCase()}
                    </span>
                    <span className="px-2 py-0.5 text-xs bg-purple-100 text-purple-800 rounded-full">
                      MRC: {opexTotals.mrc !== null ? opexTotals.mrc.toLocaleString() : 'N/A'} {projectData?.currency?.toUpperCase()}
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <button
                    type="button"
                    className="bg-primary hover:bg-primary/90 flex items-center gap-1.5 whitespace-nowrap rounded-lg px-4 py-2 text-sm font-medium text-white shadow-sm transition-all duration-200"
                    onClick={() => handleExport('opex')}
                  >
                    <svg
                      className="h-4 w-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      ></path>
                    </svg>
                    Export
                  </button>
                  <Tooltip title="Operating expenditures - ongoing costs for running the business">
                    <div className="text-gray-400 cursor-help">
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </div>
                  </Tooltip>
                </div>
              </div>
            </div>
            <div className="p-4">
              <AllOpex 
                scenarioId={scenarioData?.id} 
                showExportButton={false} 
                onDataLoaded={(opexData) => {
                  // Use exact totals from the opex data table with correct field names
                  const otcTotal = opexData?.reduce((prev, curr) => prev + (curr?.otc_amount || 0), 0);
                  const otcAmortiseTotal = opexData?.reduce((prev, curr) => prev + (curr?.otc_amortise_amount || 0), 0);
                  const mrcTotal = opexData?.reduce((prev, curr) => prev + (curr?.mrc_amount || 0), 0);
                  
                  setOpexTotals({
                    otc: otcTotal,
                    otcAmortise: otcAmortiseTotal,
                    mrc: mrcTotal
                  });
                }}
              />
            </div>
          </div>
        )}

        {tab === 'revenue' && (
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <div className="border-b border-gray-200 bg-gray-50 px-4 py-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CollectionIcon />
                  <h2 className="font-semibold text-lg">Revenue Details</h2>
                  <div className="ml-2 flex flex-wrap gap-2">
                    <span className="px-2 py-0.5 text-xs bg-blue-100 text-blue-800 rounded-full">
                      OTC: {revenueTotals.otc !== null ? revenueTotals.otc.toLocaleString() : 'N/A'} {projectData?.currency?.toUpperCase()}
                    </span>
                    <span className="px-2 py-0.5 text-xs bg-green-100 text-green-800 rounded-full">
                      OTC Amortise: {revenueTotals.otcAmortise !== null ? revenueTotals.otcAmortise.toLocaleString() : 'N/A'} {projectData?.currency?.toUpperCase()}
                    </span>
                    <span className="px-2 py-0.5 text-xs bg-purple-100 text-purple-800 rounded-full">
                      MRC: {revenueTotals.mrc !== null ? revenueTotals.mrc.toLocaleString() : 'N/A'} {projectData?.currency?.toUpperCase()}
                    </span>
                    <span className="px-2 py-0.5 text-xs bg-amber-100 text-amber-800 rounded-full">
                      Contract Value: {revenueTotals.contractValue !== null ? revenueTotals.contractValue.toLocaleString() : 'N/A'} {projectData?.currency?.toUpperCase()}
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <button
                    type="button"
                    className="bg-primary hover:bg-primary/90 flex items-center gap-1.5 whitespace-nowrap rounded-lg px-4 py-2 text-sm font-medium text-white shadow-sm transition-all duration-200"
                    onClick={() => handleExport('revenue')}
                  >
                    <svg
                      className="h-4 w-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      ></path>
                    </svg>
                    Export
                  </button>
                  <Tooltip title="Income generated from business activities">
                    <div className="text-gray-400 cursor-help">
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </div>
                  </Tooltip>
                </div>
              </div>
            </div>
            <div className="p-4">
              <AllRevenue 
                scenarioId={scenarioData?.id} 
                showExportButton={false} 
                onDataLoaded={(revenueData) => {
                  // Use exact totals from the revenue data table with correct field names
                  const otcTotal = revenueData?.reduce((prev, curr) => prev + (curr?.otc_amount || 0), 0);
                  const otcAmortiseTotal = revenueData?.reduce((prev, curr) => prev + (curr?.otc_amortise || 0), 0);
                  const mrcTotal = revenueData?.reduce((prev, curr) => prev + (curr?.mrc_amount || 0), 0);
                  const contractValueTotal = revenueData?.reduce((prev, curr) => prev + (curr?.total_contract_value || 0), 0);
                  
                  setRevenueTotals({
                    otc: otcTotal,
                    otcAmortise: otcAmortiseTotal,
                    mrc: mrcTotal,
                    contractValue: contractValueTotal
                  });
                }}
              />
            </div>
          </div>
        )}

        {tab === 'fa' && (
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <div className="border-b border-gray-200 bg-gray-50 px-4 py-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <ChartBarIcon />
                  <h2 className="font-semibold text-lg">Financial Analysis</h2>
                </div>
                <Tooltip title="Comprehensive analysis of financial metrics and projections">
                  <div className="text-gray-400 cursor-help">
                    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                </Tooltip>
              </div>
            </div>
            <div className="p-4">
            <FinancialAnalysis scenarioId={scenarioData?.id} showExportButton />
            </div>
          </div>
        )}

        {tab === 'summary' && (
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <div className="border-b border-gray-200 bg-gray-50 px-4 py-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <ChartBarIcon />
                  <h2 className="font-semibold text-lg">Summary</h2>
                </div>
                <Tooltip title="Overview of key financial figures and metrics">
                  <div className="text-gray-400 cursor-help">
                    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                </Tooltip>
              </div>
            </div>
            <div className="p-4">
            <Summary scenarioId={scenarioData?.id} showExportButton />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ScenarioDetails;
