// Next, React, Tw
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Components
import AllFinancialAnalysis from './AllFinancialAnalysis';
import ScreenshotDialog from '../Shared/ScreenshotDialog';

// Others
import { useParamContext } from '../../utils/auth/ParamProvider';

const Summary = ({ scenarioId, showExportButton = true, exportMode = false, hideHeader = false }) => {
  const { projectData } = useSelector((state) => state.fa);
  const { setParam } = useParamContext();
  const router = useRouter();

  const handleExport = () => {
    // Open the dialog directly using setParam with 'screenshotDialogOpen' parameter
    // but stay on the current page by using replaceParam
    setParam({ 
      screenshotDialogOpen: 'true',
      // Preserve other parameters from the URL
      ...router.query
    });
  };

  return (
    <>
      <div className={`flex flex-col gap-2 ${!exportMode && !hideHeader && 'rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white'}`}>
        {showExportButton && !exportMode && !hideHeader && (
          <div className="flex justify-end mb-0">
            <button
              type="button"
              className="bg-primary hover:bg-primary/90 flex items-center whitespace-nowrap rounded-lg px-4 py-1.5 text-sm font-medium text-white shadow-sm transition-all duration-200"
              onClick={handleExport}
            >
              Export Details
            </button>
          </div>
        )}
        {!hideHeader && (
          <div className="flex items-center justify-between">
            <p className="bg-fa w-full text-center text-lg font-bold text-white">
              Financial Analysis Summary
            </p>
          </div>
        )}
        <AllFinancialAnalysis filterByEndorsed={false} showApproverEndorsementCell />
      </div>
      {!exportMode && (
        <ScreenshotDialog fileName={`SUMMARY-${projectData?.name}.pdf`} isLandscape>
          <div className="flex w-full flex-col gap-4 p-4">
            <div className="bg-primary w-full text-center text-white">FINANCIAL ANALYSIS SUMMARY</div>
            <AllFinancialAnalysis filterByEndorsed={false} showApproverEndorsementCell />
          </div>
        </ScreenshotDialog>
      )}
    </>
  );
};

Summary.propTypes = {
  scenarioId: PropTypes.string,
  showExportButton: PropTypes.bool,
  exportMode: PropTypes.bool,
  hideHeader: PropTypes.bool,
};

export default Summary;
