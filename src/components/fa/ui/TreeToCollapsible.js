import React, { useState } from 'react';
import { twMerge } from 'tailwind-merge';
import PropTypes from 'prop-types';

/**
 * Simple utility component to replace tree symbols (├──, └──) with modern collapse/expand functionality
 * This is a lightweight alternative for existing complex dynamic tree structures
 */
export const TreeSymbolReplacer = ({ 
  isLast = false, 
  children, 
  level = 0,
  className,
  showCollapse = true,
  defaultCollapsed = false,
  onToggle,
  ...props 
}) => {
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);

  const handleToggle = () => {
    setIsCollapsed(!isCollapsed);
    if (onToggle) onToggle(!isCollapsed);
  };

  // For non-collapsible items, just show a modern bullet point
  if (!showCollapse) {
    return (
      <div className={twMerge('flex items-center gap-2', className)} {...props}>
        <span className="text-gray-400 text-sm">•</span>
        {children}
      </div>
    );
  }

  return (
    <div className={twMerge('flex items-center gap-2', className)} {...props}>
      <button
        onClick={handleToggle}
        className="flex items-center justify-center w-4 h-4 rounded-sm hover:bg-gray-200 transition-colors duration-150"
        aria-label={isCollapsed ? 'Expand' : 'Collapse'}
      >
        <svg
          className={twMerge(
            'w-3 h-3 text-gray-600 transition-transform duration-200',
            isCollapsed ? 'rotate-0' : 'rotate-90'
          )}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5l7 7-7 7"
          />
        </svg>
      </button>
      {children}
    </div>
  );
};

/**
 * Simple wrapper to convert tree-like display to modern UI
 * This replaces ├── and └── symbols with clean modern styling
 */
export const ModernTreeItem = ({ 
  children, 
  isLast = false,
  level = 0,
  className,
  ...props 
}) => {
  // Calculate indentation based on level
  const paddingLeft = level * 16; // 16px per level

  return (
    <div 
      className={twMerge('flex items-center gap-2', className)} 
      style={{ paddingLeft: `${paddingLeft}px` }}
      {...props}
    >
      <span className="text-blue-500 text-sm font-medium">•</span>
      {children}
    </div>
  );
};

TreeSymbolReplacer.propTypes = {
  isLast: PropTypes.bool,
  children: PropTypes.node.isRequired,
  level: PropTypes.number,
  className: PropTypes.string,
  showCollapse: PropTypes.bool,
  defaultCollapsed: PropTypes.bool,
  onToggle: PropTypes.func,
};

ModernTreeItem.propTypes = {
  children: PropTypes.node.isRequired,
  isLast: PropTypes.bool,
  level: PropTypes.number,
  className: PropTypes.string,
};

export default TreeSymbolReplacer;