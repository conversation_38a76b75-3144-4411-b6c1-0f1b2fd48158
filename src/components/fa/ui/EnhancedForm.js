import React from 'react';
import { twMerge } from 'tailwind-merge';
import PropTypes from 'prop-types';

/**
 * Enhanced Form Components for FA Module
 * Provides consistent, professional form styling with better UX
 */

/**
 * Form Group Component for organizing form fields
 */
export const FormGroup = ({ 
  children, 
  className,
  ...props 
}) => {
  return (
    <div
      className={twMerge('fa-input-group', className)}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * Enhanced Form Label
 */
export const FormLabel = ({ 
  children, 
  className,
  required = false,
  htmlFor,
  ...props 
}) => {
  return (
    <label
      htmlFor={htmlFor}
      className={twMerge('fa-input-label', className)}
      {...props}
    >
      {children}
      {required && <span className="text-red-500 ml-1">*</span>}
    </label>
  );
};

/**
 * Enhanced Text Input
 */
export const EnhancedTextInput = ({ 
  className,
  error,
  helperText,
  label,
  required = false,
  icon,
  ...props 
}) => {
  return (
    <FormGroup>
      {label && (
        <FormLabel required={required} htmlFor={props.id}>
          {label}
        </FormLabel>
      )}
      <div className="relative">
        {icon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <div className="text-gray-400">
              {icon}
            </div>
          </div>
        )}
        <input
          className={twMerge(
            'fa-input',
            icon ? 'pl-10' : '',
            error ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : '',
            className
          )}
          {...props}
        />
      </div>
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
      {helperText && !error && (
        <p className="mt-1 text-sm text-gray-500">{helperText}</p>
      )}
    </FormGroup>
  );
};

/**
 * Enhanced Select Input
 */
export const EnhancedSelectInput = ({ 
  className,
  error,
  helperText,
  label,
  required = false,
  options = [],
  placeholder = 'Select an option',
  ...props 
}) => {
  return (
    <FormGroup>
      {label && (
        <FormLabel required={required} htmlFor={props.id}>
          {label}
        </FormLabel>
      )}
      <select
        className={twMerge(
          'fa-input',
          error ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : '',
          className
        )}
        {...props}
      >
        <option value="" disabled>
          {placeholder}
        </option>
        {options.map((option, index) => (
          <option key={index} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
      {helperText && !error && (
        <p className="mt-1 text-sm text-gray-500">{helperText}</p>
      )}
    </FormGroup>
  );
};

/**
 * Enhanced Textarea Input
 */
export const EnhancedTextareaInput = ({ 
  className,
  error,
  helperText,
  label,
  required = false,
  rows = 4,
  ...props 
}) => {
  return (
    <FormGroup>
      {label && (
        <FormLabel required={required} htmlFor={props.id}>
          {label}
        </FormLabel>
      )}
      <textarea
        rows={rows}
        className={twMerge(
          'fa-input resize-none',
          error ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : '',
          className
        )}
        {...props}
      />
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
      {helperText && !error && (
        <p className="mt-1 text-sm text-gray-500">{helperText}</p>
      )}
    </FormGroup>
  );
};

/**
 * Enhanced Date Input
 */
export const EnhancedDateInput = ({ 
  className,
  error,
  helperText,
  label,
  required = false,
  ...props 
}) => {
  return (
    <FormGroup>
      {label && (
        <FormLabel required={required} htmlFor={props.id}>
          {label}
        </FormLabel>
      )}
      <input
        type="date"
        className={twMerge(
          'fa-input',
          error ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : '',
          className
        )}
        {...props}
      />
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
      {helperText && !error && (
        <p className="mt-1 text-sm text-gray-500">{helperText}</p>
      )}
    </FormGroup>
  );
};

/**
 * Enhanced Number Input
 */
export const EnhancedNumberInput = ({ 
  className,
  error,
  helperText,
  label,
  required = false,
  prefix,
  suffix,
  ...props 
}) => {
  return (
    <FormGroup>
      {label && (
        <FormLabel required={required} htmlFor={props.id}>
          {label}
        </FormLabel>
      )}
      <div className="relative">
        {prefix && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span className="text-gray-500 text-sm">{prefix}</span>
          </div>
        )}
        <input
          type="number"
          className={twMerge(
            'fa-input',
            prefix ? 'pl-8' : '',
            suffix ? 'pr-8' : '',
            error ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : '',
            className
          )}
          {...props}
        />
        {suffix && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <span className="text-gray-500 text-sm">{suffix}</span>
          </div>
        )}
      </div>
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
      {helperText && !error && (
        <p className="mt-1 text-sm text-gray-500">{helperText}</p>
      )}
    </FormGroup>
  );
};

/**
 * Enhanced Switch Input
 */
export const EnhancedSwitchInput = ({ 
  className,
  label,
  description,
  checked,
  onChange,
  ...props 
}) => {
  return (
    <div className="flex items-center justify-between">
      <div className="flex-1">
        {label && (
          <div className="text-sm font-medium text-gray-700">{label}</div>
        )}
        {description && (
          <div className="text-sm text-gray-500">{description}</div>
        )}
      </div>
      <button
        type="button"
        className={twMerge(
          'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
          checked ? 'bg-blue-600' : 'bg-gray-200',
          className
        )}
        onClick={() => onChange && onChange(!checked)}
        {...props}
      >
        <span
          className={twMerge(
            'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
            checked ? 'translate-x-5' : 'translate-x-0'
          )}
        />
      </button>
    </div>
  );
};

/**
 * Form Actions Component for form buttons
 */
export const FormActions = ({ 
  children, 
  className,
  align = 'right',
  ...props 
}) => {
  const alignClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end',
    between: 'justify-between'
  };

  return (
    <div
      className={twMerge(
        'flex items-center gap-3 pt-6 border-t border-gray-200',
        alignClasses[align],
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

// PropTypes
FormGroup.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string
};

FormLabel.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  required: PropTypes.bool,
  htmlFor: PropTypes.string
};

EnhancedTextInput.propTypes = {
  className: PropTypes.string,
  error: PropTypes.string,
  helperText: PropTypes.string,
  label: PropTypes.string,
  required: PropTypes.bool,
  icon: PropTypes.node
};

EnhancedSelectInput.propTypes = {
  className: PropTypes.string,
  error: PropTypes.string,
  helperText: PropTypes.string,
  label: PropTypes.string,
  required: PropTypes.bool,
  options: PropTypes.arrayOf(PropTypes.shape({
    value: PropTypes.string.isRequired,
    label: PropTypes.string.isRequired
  })),
  placeholder: PropTypes.string
};

export default {
  FormGroup,
  FormLabel,
  EnhancedTextInput,
  EnhancedSelectInput,
  EnhancedTextareaInput,
  EnhancedDateInput,
  EnhancedNumberInput,
  EnhancedSwitchInput,
  FormActions
};
