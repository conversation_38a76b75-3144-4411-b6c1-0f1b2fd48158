import React from 'react';
import { twMerge } from 'tailwind-merge';
import PropTypes from 'prop-types';

/**
 * Enhanced Badge Components for FA Module
 * Provides consistent, professional badge styling for status indicators and labels
 */

/**
 * Base Enhanced Badge Component
 */
export const EnhancedBadge = ({ 
  children, 
  className,
  variant = 'default',
  size = 'normal',
  icon,
  iconPosition = 'left',
  dot = false,
  ...props 
}) => {
  const baseClasses = 'fa-badge';

  const variants = {
    default: 'bg-gray-100 text-gray-800',
    primary: 'bg-blue-100 text-blue-800',
    secondary: 'bg-gray-100 text-gray-800',
    success: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    danger: 'bg-red-100 text-red-800',
    info: 'bg-blue-100 text-blue-800'
  };

  const sizes = {
    small: 'px-2 py-0.5 text-xs',
    normal: 'px-2.5 py-0.5 text-xs',
    large: 'px-3 py-1 text-sm'
  };

  return (
    <span
      className={twMerge(
        baseClasses,
        variants[variant],
        sizes[size],
        className
      )}
      {...props}
    >
      {dot && (
        <span className={twMerge(
          'inline-block w-2 h-2 rounded-full mr-1.5',
          variant === 'success' ? 'bg-green-400' :
          variant === 'warning' ? 'bg-yellow-400' :
          variant === 'danger' ? 'bg-red-400' :
          variant === 'primary' ? 'bg-blue-400' :
          'bg-gray-400'
        )} />
      )}
      {icon && iconPosition === 'left' && (
        <span className="mr-1">{icon}</span>
      )}
      {children}
      {icon && iconPosition === 'right' && (
        <span className="ml-1">{icon}</span>
      )}
    </span>
  );
};

/**
 * Status Badge Component for project/scenario status
 */
export const StatusBadge = ({ 
  status,
  className,
  ...props 
}) => {
  const getStatusVariant = (status) => {
    const normalizedStatus = status?.toLowerCase() || '';
    
    if (normalizedStatus.includes('completed') || normalizedStatus.includes('approved')) {
      return 'success';
    }
    if (normalizedStatus.includes('pending') || normalizedStatus.includes('waiting')) {
      return 'warning';
    }
    if (normalizedStatus.includes('rejected') || normalizedStatus.includes('failed')) {
      return 'danger';
    }
    if (normalizedStatus.includes('draft')) {
      return 'default';
    }
    if (normalizedStatus.includes('endorsement') || normalizedStatus.includes('review')) {
      return 'info';
    }
    return 'default';
  };

  const getStatusIcon = (status) => {
    const normalizedStatus = status?.toLowerCase() || '';
    
    if (normalizedStatus.includes('completed') || normalizedStatus.includes('approved')) {
      return (
        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      );
    }
    if (normalizedStatus.includes('pending') || normalizedStatus.includes('waiting')) {
      return (
        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
    }
    if (normalizedStatus.includes('rejected') || normalizedStatus.includes('failed')) {
      return (
        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      );
    }
    return null;
  };

  const variant = getStatusVariant(status);
  const icon = getStatusIcon(status);

  return (
    <EnhancedBadge
      variant={variant}
      icon={icon}
      className={className}
      {...props}
    >
      {status?.toUpperCase() || 'UNKNOWN'}
    </EnhancedBadge>
  );
};

/**
 * Priority Badge Component
 */
export const PriorityBadge = ({ 
  priority,
  className,
  ...props 
}) => {
  const getPriorityVariant = (priority) => {
    const normalizedPriority = priority?.toLowerCase() || '';
    
    if (normalizedPriority === 'high' || normalizedPriority === 'urgent') {
      return 'danger';
    }
    if (normalizedPriority === 'medium' || normalizedPriority === 'normal') {
      return 'warning';
    }
    if (normalizedPriority === 'low') {
      return 'success';
    }
    return 'default';
  };

  const variant = getPriorityVariant(priority);

  return (
    <EnhancedBadge
      variant={variant}
      dot
      className={className}
      {...props}
    >
      {priority?.toUpperCase() || 'NORMAL'}
    </EnhancedBadge>
  );
};

/**
 * Currency Badge Component
 */
export const CurrencyBadge = ({ 
  currency,
  className,
  ...props 
}) => {
  const getCurrencyIcon = (currency) => {
    const normalizedCurrency = currency?.toLowerCase() || '';
    
    if (normalizedCurrency === 'usd') {
      return '$';
    }
    if (normalizedCurrency === 'myr') {
      return 'RM';
    }
    if (normalizedCurrency === 'eur') {
      return '€';
    }
    if (normalizedCurrency === 'gbp') {
      return '£';
    }
    return currency?.toUpperCase();
  };

  return (
    <EnhancedBadge
      variant="info"
      size="small"
      className={className}
      {...props}
    >
      {getCurrencyIcon(currency)}
    </EnhancedBadge>
  );
};

/**
 * Count Badge Component for displaying numbers
 */
export const CountBadge = ({ 
  count,
  max = 99,
  showZero = false,
  className,
  ...props 
}) => {
  if (!showZero && (!count || count === 0)) {
    return null;
  }

  const displayCount = count > max ? `${max}+` : count;

  return (
    <EnhancedBadge
      variant="danger"
      size="small"
      className={twMerge('rounded-full min-w-[1.25rem] h-5 flex items-center justify-center', className)}
      {...props}
    >
      {displayCount}
    </EnhancedBadge>
  );
};

/**
 * Tag Badge Component for categories/tags
 */
export const TagBadge = ({ 
  children,
  color = 'blue',
  removable = false,
  onRemove,
  className,
  ...props 
}) => {
  const colorVariants = {
    blue: 'bg-blue-100 text-blue-800 hover:bg-blue-200',
    green: 'bg-green-100 text-green-800 hover:bg-green-200',
    yellow: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
    red: 'bg-red-100 text-red-800 hover:bg-red-200',
    purple: 'bg-purple-100 text-purple-800 hover:bg-purple-200',
    gray: 'bg-gray-100 text-gray-800 hover:bg-gray-200'
  };

  return (
    <span
      className={twMerge(
        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors',
        colorVariants[color],
        removable ? 'pr-1' : '',
        className
      )}
      {...props}
    >
      {children}
      {removable && (
        <button
          onClick={onRemove}
          className="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-black/10 transition-colors"
        >
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </span>
  );
};

/**
 * Notification Badge Component
 */
export const NotificationBadge = ({ 
  children,
  count,
  className,
  ...props 
}) => {
  return (
    <div className="relative inline-block">
      {children}
      {count > 0 && (
        <CountBadge
          count={count}
          className={twMerge(
            'absolute -top-2 -right-2 transform',
            className
          )}
          {...props}
        />
      )}
    </div>
  );
};

// PropTypes
EnhancedBadge.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  variant: PropTypes.oneOf(['default', 'primary', 'secondary', 'success', 'warning', 'danger', 'info']),
  size: PropTypes.oneOf(['small', 'normal', 'large']),
  icon: PropTypes.node,
  iconPosition: PropTypes.oneOf(['left', 'right']),
  dot: PropTypes.bool
};

StatusBadge.propTypes = {
  status: PropTypes.string.isRequired,
  className: PropTypes.string
};

PriorityBadge.propTypes = {
  priority: PropTypes.string.isRequired,
  className: PropTypes.string
};

CurrencyBadge.propTypes = {
  currency: PropTypes.string.isRequired,
  className: PropTypes.string
};

CountBadge.propTypes = {
  count: PropTypes.number.isRequired,
  max: PropTypes.number,
  showZero: PropTypes.bool,
  className: PropTypes.string
};

TagBadge.propTypes = {
  children: PropTypes.node.isRequired,
  color: PropTypes.oneOf(['blue', 'green', 'yellow', 'red', 'purple', 'gray']),
  removable: PropTypes.bool,
  onRemove: PropTypes.func,
  className: PropTypes.string
};

export default EnhancedBadge;
