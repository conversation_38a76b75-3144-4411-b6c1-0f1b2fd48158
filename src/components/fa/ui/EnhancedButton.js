import React from 'react';
import { twMerge } from 'tailwind-merge';
import PropTypes from 'prop-types';

/**
 * Enhanced Button Components for FA Module
 * Provides consistent, professional button styling with various variants
 */

/**
 * Base Enhanced Button Component
 */
export const EnhancedButton = ({ 
  children, 
  className,
  variant = 'primary',
  size = 'normal',
  icon,
  iconPosition = 'left',
  loading = false,
  disabled = false,
  fullWidth = false,
  ...props 
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

  const variants = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500',
    secondary: 'bg-gray-100 hover:bg-gray-200 text-gray-700 focus:ring-gray-500',
    success: 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500',
    warning: 'bg-yellow-600 hover:bg-yellow-700 text-white focus:ring-yellow-500',
    danger: 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500',
    outline: 'border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 focus:ring-blue-500',
    ghost: 'bg-transparent hover:bg-gray-100 text-gray-700 focus:ring-gray-500'
  };

  const sizes = {
    small: 'px-3 py-1.5 text-sm',
    normal: 'px-4 py-2 text-sm',
    large: 'px-6 py-3 text-base'
  };

  const LoadingSpinner = () => (
    <svg
      className="animate-spin -ml-1 mr-2 h-4 w-4"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );

  return (
    <button
      className={twMerge(
        baseClasses,
        variants[variant],
        sizes[size],
        fullWidth ? 'w-full' : '',
        className
      )}
      disabled={disabled || loading}
      {...props}
    >
      {loading && <LoadingSpinner />}
      {!loading && icon && iconPosition === 'left' && (
        <span className="mr-2">{icon}</span>
      )}
      {children}
      {!loading && icon && iconPosition === 'right' && (
        <span className="ml-2">{icon}</span>
      )}
    </button>
  );
};

/**
 * Icon Button Component
 */
export const IconButton = ({ 
  children, 
  className,
  variant = 'ghost',
  size = 'normal',
  tooltip,
  ...props 
}) => {
  const sizes = {
    small: 'p-1',
    normal: 'p-2',
    large: 'p-3'
  };

  const button = (
    <EnhancedButton
      className={twMerge('rounded-full', sizes[size], className)}
      variant={variant}
      {...props}
    >
      {children}
    </EnhancedButton>
  );

  if (tooltip) {
    return (
      <div className="relative group">
        {button}
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
          {tooltip}
        </div>
      </div>
    );
  }

  return button;
};

/**
 * Button Group Component
 */
export const ButtonGroup = ({ 
  children, 
  className,
  orientation = 'horizontal',
  ...props 
}) => {
  const orientationClasses = {
    horizontal: 'flex-row [&>button:not(:first-child)]:ml-0 [&>button:not(:first-child)]:rounded-l-none [&>button:not(:last-child)]:rounded-r-none [&>button:not(:last-child)]:border-r-0',
    vertical: 'flex-col [&>button:not(:first-child)]:mt-0 [&>button:not(:first-child)]:rounded-t-none [&>button:not(:last-child)]:rounded-b-none [&>button:not(:last-child)]:border-b-0'
  };

  return (
    <div
      className={twMerge(
        'inline-flex',
        orientationClasses[orientation],
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * Floating Action Button Component
 */
export const FloatingActionButton = ({ 
  children, 
  className,
  position = 'bottom-right',
  ...props 
}) => {
  const positions = {
    'bottom-right': 'fixed bottom-6 right-6',
    'bottom-left': 'fixed bottom-6 left-6',
    'top-right': 'fixed top-6 right-6',
    'top-left': 'fixed top-6 left-6'
  };

  return (
    <EnhancedButton
      className={twMerge(
        'rounded-full w-14 h-14 shadow-lg hover:shadow-xl z-50',
        positions[position],
        className
      )}
      {...props}
    >
      {children}
    </EnhancedButton>
  );
};

/**
 * Split Button Component
 */
export const SplitButton = ({ 
  children, 
  className,
  variant = 'primary',
  size = 'normal',
  dropdownItems = [],
  onMainClick,
  ...props 
}) => {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <div className="relative inline-flex">
      <EnhancedButton
        variant={variant}
        size={size}
        className={twMerge('rounded-r-none border-r border-white/20', className)}
        onClick={onMainClick}
        {...props}
      >
        {children}
      </EnhancedButton>
      <div className="relative">
        <EnhancedButton
          variant={variant}
          size={size}
          className="rounded-l-none px-2"
          onClick={() => setIsOpen(!isOpen)}
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </EnhancedButton>
        {isOpen && (
          <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
            <div className="py-1">
              {dropdownItems.map((item, index) => (
                <button
                  key={index}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  onClick={() => {
                    item.onClick && item.onClick();
                    setIsOpen(false);
                  }}
                >
                  {item.label}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
      {isOpen && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

/**
 * Link Button Component (looks like button but behaves like link)
 */
export const LinkButton = ({ 
  children, 
  className,
  variant = 'ghost',
  ...props 
}) => {
  return (
    <EnhancedButton
      as="a"
      className={twMerge('no-underline', className)}
      variant={variant}
      {...props}
    >
      {children}
    </EnhancedButton>
  );
};

// PropTypes
EnhancedButton.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  variant: PropTypes.oneOf(['primary', 'secondary', 'success', 'warning', 'danger', 'outline', 'ghost']),
  size: PropTypes.oneOf(['small', 'normal', 'large']),
  icon: PropTypes.node,
  iconPosition: PropTypes.oneOf(['left', 'right']),
  loading: PropTypes.bool,
  disabled: PropTypes.bool,
  fullWidth: PropTypes.bool
};

IconButton.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  variant: PropTypes.oneOf(['primary', 'secondary', 'success', 'warning', 'danger', 'outline', 'ghost']),
  size: PropTypes.oneOf(['small', 'normal', 'large']),
  tooltip: PropTypes.string
};

ButtonGroup.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  orientation: PropTypes.oneOf(['horizontal', 'vertical'])
};

FloatingActionButton.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  position: PropTypes.oneOf(['bottom-right', 'bottom-left', 'top-right', 'top-left'])
};

SplitButton.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  variant: PropTypes.oneOf(['primary', 'secondary', 'success', 'warning', 'danger', 'outline', 'ghost']),
  size: PropTypes.oneOf(['small', 'normal', 'large']),
  dropdownItems: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string.isRequired,
    onClick: PropTypes.func
  })),
  onMainClick: PropTypes.func
};

export default EnhancedButton;
