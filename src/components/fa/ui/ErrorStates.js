import React from 'react';
import { twMerge } from 'tailwind-merge';

/**
 * Base Error Component
 */
const ErrorMessage = ({ 
  title, 
  message, 
  icon, 
  variant = 'error',
  className = "",
  children 
}) => {
  const variantStyles = {
    error: 'bg-red-50 border-red-200 text-red-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    info: 'bg-blue-50 border-blue-200 text-blue-800'
  };

  const iconStyles = {
    error: 'text-red-500',
    warning: 'text-yellow-500', 
    info: 'text-blue-500'
  };

  const defaultIcon = (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  );

  return (
    <div className={twMerge(
      "border rounded-lg p-4",
      variantStyles[variant],
      className
    )}>
      <div className="flex items-start gap-3">
        <div className={iconStyles[variant]}>
          {icon || defaultIcon}
        </div>
        <div className="flex-1">
          {title && (
            <h3 className="font-semibold mb-1">{title}</h3>
          )}
          {message && (
            <p className="text-sm mb-3">{message}</p>
          )}
          {children}
        </div>
      </div>
    </div>
  );
};

/**
 * Network Error Component
 */
export const NetworkError = ({ onRetry, className = "" }) => (
  <ErrorMessage
    title="Connection Issue"
    message="Unable to connect to the server. Please check your internet connection and try again."
    icon={
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
              d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
      </svg>
    }
    className={className}
  >
    <div className="flex gap-2">
      <button
        onClick={onRetry}
        className="bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 transition-colors"
      >
        Try Again
      </button>
      <button
        onClick={() => window.location.reload()}
        className="bg-gray-100 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors"
      >
        Refresh Page
      </button>
    </div>
  </ErrorMessage>
);

/**
 * Data Error Component
 */
export const DataError = ({ onRetry, details, className = "" }) => (
  <ErrorMessage
    title="Data Loading Error"
    message="There was a problem loading the financial data. This might be due to incomplete calculations or server issues."
    icon={
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
    }
    className={className}
  >
    {details && (
      <div className="bg-red-100 border border-red-200 rounded p-2 mb-3">
        <p className="text-xs font-medium text-red-800">Technical Details:</p>
        <code className="text-xs text-red-700">{details}</code>
      </div>
    )}
    <div className="flex gap-2">
      <button
        onClick={onRetry}
        className="bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 transition-colors"
      >
        Reload Data
      </button>
    </div>
  </ErrorMessage>
);

/**
 * Validation Error Component
 */
export const ValidationError = ({ errors = [], onFix, className = "" }) => (
  <ErrorMessage
    title="Validation Issues"
    message="Please fix the following issues before proceeding:"
    variant="warning"
    icon={
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
      </svg>
    }
    className={className}
  >
    <ul className="list-disc list-inside space-y-1 mb-3">
      {errors.map((error, index) => (
        <li key={index} className="text-sm">{error}</li>
      ))}
    </ul>
    {onFix && (
      <button
        onClick={onFix}
        className="bg-yellow-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-yellow-700 transition-colors"
      >
        Fix Issues
      </button>
    )}
  </ErrorMessage>
);

/**
 * Permission Error Component
 */
export const PermissionError = ({ action, contact, className = "" }) => (
  <ErrorMessage
    title="Access Denied"
    message={`You don't have permission to ${action || 'perform this action'}. Please contact your administrator for access.`}
    icon={
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
      </svg>
    }
    className={className}
  >
    {contact && (
      <div className="flex gap-2">
        <button
          onClick={() => window.location.href = `mailto:${contact}`}
          className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
        >
          Contact Admin
        </button>
      </div>
    )}
  </ErrorMessage>
);

/**
 * Calculation Error Component  
 */
export const CalculationError = ({ field, suggestion, onRecalculate, className = "" }) => (
  <ErrorMessage
    title="Calculation Error"
    message={`There's an issue calculating ${field || 'financial metrics'}. This might be due to missing data or invalid values.`}
    icon={
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
              d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
      </svg>
    }
    className={className}
  >
    {suggestion && (
      <div className="bg-yellow-100 border border-yellow-200 rounded p-2 mb-3">
        <p className="text-xs font-medium text-yellow-800">Suggestion:</p>
        <p className="text-xs text-yellow-700">{suggestion}</p>
      </div>
    )}
    <div className="flex gap-2">
      <button
        onClick={onRecalculate}
        className="bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 transition-colors"
      >
        Recalculate
      </button>
    </div>
  </ErrorMessage>
);

/**
 * Empty State Component
 */
export const EmptyState = ({ 
  title = "No Data Available", 
  message = "There's no data to display at the moment.",
  action,
  actionLabel = "Add Data",
  icon,
  className = ""
}) => {
  const defaultIcon = (
    <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>
  );

  return (
    <div className={twMerge("text-center py-12", className)}>
      <div className="flex justify-center mb-4">
        {icon || defaultIcon}
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-500 mb-6 max-w-sm mx-auto">{message}</p>
      {action && (
        <button
          onClick={action}
          className="bg-blue-600 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 transition-colors"
        >
          {actionLabel}
        </button>
      )}
    </div>
  );
};

/**
 * Error Boundary Component
 */
export class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <ErrorMessage
          title="Something went wrong"
          message="An unexpected error occurred. Please refresh the page or contact support if the problem persists."
          className="m-4"
        >
          <div className="flex gap-2">
            <button
              onClick={() => window.location.reload()}
              className="bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 transition-colors"
            >
              Refresh Page
            </button>
            <button
              onClick={() => this.setState({ hasError: false, error: null })}
              className="bg-gray-100 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors"
            >
              Try Again
            </button>
          </div>
        </ErrorMessage>
      );
    }

    return this.props.children;
  }
}

export default {
  ErrorMessage,
  NetworkError,
  DataError,
  ValidationError,
  PermissionError,
  CalculationError,
  EmptyState,
  ErrorBoundary
}; 