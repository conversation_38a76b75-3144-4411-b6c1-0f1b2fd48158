import React from 'react';
import { 
  EnhancedTable, 
  EnhancedTableHeader, 
  EnhancedTableHeaderCell, 
  EnhancedTableBody,
  CollapsibleTableRowGroup,
  SubTableRow,
  EnhancedTableCell
} from './EnhancedTable';

/**
 * Demo component showing the new collapsible table functionality
 * This replaces the old tree structure (├──, └──) with modern collapsible rows
 */
const CollapsibleTableDemo = () => {
  // Sample data for demonstration
  const scenarios = [
    { id: 1, name: 'Scenario A', value: 1000000 },
    { id: 2, name: 'Scenario B', value: 1200000 },
    { id: 3, name: 'Scenario C', value: 950000 }
  ];

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">
          Enhanced Collapsible Tables - FA Module
        </h1>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <EnhancedTable variant="striped">
            <EnhancedTableHeader variant="primary">
              <tr>
                <EnhancedTableHeaderCell>Financial Component</EnhancedTableHeaderCell>
                {scenarios.map(scenario => (
                  <EnhancedTableHeaderCell key={scenario.id} align="right">
                    {scenario.name}
                  </EnhancedTableHeaderCell>
                ))}
              </tr>
            </EnhancedTableHeader>
            
            <EnhancedTableBody>
              {/* PROFIT AND LOSS Section Header */}
              <tr>
                <td colSpan={4} className="bg-blue-50 font-bold text-center py-3 text-sm border-b border-blue-200 px-4 text-blue-900">
                  PROFIT AND LOSS
                </td>
              </tr>

              {/* Revenue - Collapsible Group */}
              <CollapsibleTableRowGroup
                title="Revenue"
                defaultOpen={true}
                headerCells={scenarios.map(scenario => (
                  <span key={scenario.id} className="font-mono">
                    {formatCurrency(scenario.value * 1.2)}
                  </span>
                ))}
              >
                <SubTableRow title="OTC">
                  {scenarios.map(scenario => (
                    <EnhancedTableCell key={scenario.id} align="right" className="font-mono">
                      {formatCurrency(scenario.value * 0.3)}
                    </EnhancedTableCell>
                  ))}
                </SubTableRow>
                
                <SubTableRow title="OTC Amortised">
                  {scenarios.map(scenario => (
                    <EnhancedTableCell key={scenario.id} align="right" className="font-mono">
                      {formatCurrency(scenario.value * 0.4)}
                    </EnhancedTableCell>
                  ))}
                </SubTableRow>
                
                <SubTableRow title="ARC" isLast={true}>
                  {scenarios.map(scenario => (
                    <EnhancedTableCell key={scenario.id} align="right" className="font-mono">
                      {formatCurrency(scenario.value * 0.5)}
                    </EnhancedTableCell>
                  ))}
                </SubTableRow>
              </CollapsibleTableRowGroup>

              {/* OPEX - Collapsible Group */}
              <CollapsibleTableRowGroup
                title="OPEX"
                defaultOpen={false}
                headerCells={scenarios.map(scenario => (
                  <span key={scenario.id} className="font-mono text-red-600">
                    -{formatCurrency(scenario.value * 0.6)}
                  </span>
                ))}
              >
                <SubTableRow title="O&M">
                  {scenarios.map(scenario => (
                    <EnhancedTableCell key={scenario.id} align="right" className="font-mono text-red-600">
                      -{formatCurrency(scenario.value * 0.2)}
                    </EnhancedTableCell>
                  ))}
                </SubTableRow>
                
                <SubTableRow title="Outpayment">
                  {scenarios.map(scenario => (
                    <EnhancedTableCell key={scenario.id} align="right" className="font-mono text-red-600">
                      -{formatCurrency(scenario.value * 0.25)}
                    </EnhancedTableCell>
                  ))}
                </SubTableRow>
                
                <SubTableRow title="USP" isLast={true}>
                  {scenarios.map(scenario => (
                    <EnhancedTableCell key={scenario.id} align="right" className="font-mono text-red-600">
                      -{formatCurrency(scenario.value * 0.15)}
                    </EnhancedTableCell>
                  ))}
                </SubTableRow>
              </CollapsibleTableRowGroup>

              {/* EBITDA */}
              <tr>
                <td className="whitespace-nowrap bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700 border-b border-gray-200">
                  EBITDA
                </td>
                {scenarios.map(scenario => (
                  <EnhancedTableCell key={scenario.id} align="right" className="font-mono font-bold text-green-600">
                    {formatCurrency(scenario.value * 0.6)}
                  </EnhancedTableCell>
                ))}
              </tr>

              {/* CASH FLOW Section Header */}
              <tr>
                <td colSpan={4} className="bg-green-50 font-bold text-center py-3 text-sm border-b border-green-200 px-4 text-green-900">
                  CASH FLOW
                </td>
              </tr>

              {/* COGS - Collapsible Group */}
              <CollapsibleTableRowGroup
                title="COGS"
                defaultOpen={true}
                headerCells={scenarios.map(scenario => (
                  <span key={scenario.id} className="font-mono text-red-600">
                    -{formatCurrency(scenario.value * 0.4)}
                  </span>
                ))}
              >
                <SubTableRow title="CAPEX">
                  {scenarios.map(scenario => (
                    <EnhancedTableCell key={scenario.id} align="right" className="font-mono text-red-600">
                      -{formatCurrency(scenario.value * 0.3)}
                    </EnhancedTableCell>
                  ))}
                </SubTableRow>
                
                <SubTableRow title="OTC" isLast={true}>
                  {scenarios.map(scenario => (
                    <EnhancedTableCell key={scenario.id} align="right" className="font-mono text-red-600">
                      -{formatCurrency(scenario.value * 0.1)}
                    </EnhancedTableCell>
                  ))}
                </SubTableRow>
              </CollapsibleTableRowGroup>
            </EnhancedTableBody>
          </EnhancedTable>
        </div>

        {/* Benefits Section */}
        <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Benefits of New Collapsible Tables</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h3 className="text-md font-medium text-gray-800">✨ Enhanced User Experience</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Clean, modern collapsible interface</li>
                <li>• Intuitive expand/collapse controls</li>
                <li>• Visual icons for better categorization</li>
                <li>• Smooth animations and transitions</li>
              </ul>
            </div>
            <div className="space-y-3">
              <h3 className="text-md font-medium text-gray-800">🔧 Improved Functionality</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• No more confusing tree symbols (├──, └──)</li>
                <li>• Better space utilization</li>
                <li>• Consistent with modern UI patterns</li>
                <li>• Accessible keyboard navigation</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CollapsibleTableDemo;