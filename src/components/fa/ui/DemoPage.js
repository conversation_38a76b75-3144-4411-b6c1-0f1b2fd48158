import React, { useState } from 'react';
import {
  EnhancedCard,
  EnhancedCardHeader,
  EnhancedCardContent,
  StatsCard,
  EnhancedTable,
  EnhancedTableHeader,
  EnhancedTableHeaderCell,
  EnhancedTableBody,
  EnhancedTableRow,
  EnhancedTableCell,
  EnhancedButton,
  IconButton,
  FormDialog,
  EnhancedTextInput,
  EnhancedSelectInput,
  EnhancedNumberInput,
  StatusBadge,
  CurrencyBadge,
  PriorityBadge,
  ConfirmationDialog
} from './index';

/**
 * Demo Page Component
 * Demonstrates the enhanced UI components in action
 */
const DemoPage = () => {
  const [formOpen, setFormOpen] = useState(false);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    type: '',
    amount: '',
    currency: 'USD'
  });

  // Sample data for demonstration
  const statsData = [
    {
      title: 'Total Projects',
      value: 24,
      variant: 'primary',
      icon: (
        <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
      )
    },
    {
      title: 'Pending Approval',
      value: 8,
      variant: 'warning',
      icon: (
        <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    },
    {
      title: 'Completed',
      value: 16,
      variant: 'success',
      icon: (
        <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      )
    },
    {
      title: 'Total Value',
      value: '$2.4M',
      variant: 'default',
      icon: (
        <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      )
    }
  ];

  const tableData = [
    {
      id: 1,
      name: 'Project Alpha',
      status: 'pending',
      priority: 'high',
      amount: 150000,
      currency: 'USD',
      created: '2024-01-15'
    },
    {
      id: 2,
      name: 'Project Beta',
      status: 'completed',
      priority: 'medium',
      amount: 75000,
      currency: 'USD',
      created: '2024-01-10'
    },
    {
      id: 3,
      name: 'Project Gamma',
      status: 'approved',
      priority: 'low',
      amount: 200000,
      currency: 'EUR',
      created: '2024-01-08'
    }
  ];

  const handleFormSubmit = (e) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    setFormOpen(false);
    setFormData({ name: '', type: '', amount: '', currency: 'USD' });
  };

  const handleFormChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Enhanced FA Module UI Components
          </h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Professional, high-readability components designed for the Financial Analysis module.
            This demo showcases the enhanced design system in action.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statsData.map((stat, index) => (
            <StatsCard
              key={index}
              title={stat.title}
              value={stat.value}
              icon={stat.icon}
              variant={stat.variant}
            />
          ))}
        </div>

        {/* Main Content Card */}
        <EnhancedCard variant="elevated">
          <EnhancedCardHeader
            variant="primary"
            icon={
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z" />
              </svg>
            }
            actions={
              <div className="flex items-center gap-2">
                <IconButton
                  variant="ghost"
                  size="small"
                  tooltip="Refresh data"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </IconButton>
                <EnhancedButton
                  variant="primary"
                  size="small"
                  onClick={() => setFormOpen(true)}
                  icon={
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                  }
                >
                  Add Project
                </EnhancedButton>
              </div>
            }
          >
            Financial Analysis Projects
          </EnhancedCardHeader>

          <EnhancedCardContent padding="none">
            <EnhancedTable variant="striped">
              <EnhancedTableHeader>
                <EnhancedTableRow>
                  <EnhancedTableHeaderCell>Project Name</EnhancedTableHeaderCell>
                  <EnhancedTableHeaderCell>Status</EnhancedTableHeaderCell>
                  <EnhancedTableHeaderCell>Priority</EnhancedTableHeaderCell>
                  <EnhancedTableHeaderCell align="right">Amount</EnhancedTableHeaderCell>
                  <EnhancedTableHeaderCell>Created</EnhancedTableHeaderCell>
                  <EnhancedTableHeaderCell align="center">Actions</EnhancedTableHeaderCell>
                </EnhancedTableRow>
              </EnhancedTableHeader>
              <EnhancedTableBody>
                {tableData.map((row) => (
                  <EnhancedTableRow key={row.id} clickable>
                    <EnhancedTableCell variant="primary">
                      <div className="font-medium">{row.name}</div>
                    </EnhancedTableCell>
                    <EnhancedTableCell>
                      <StatusBadge status={row.status} />
                    </EnhancedTableCell>
                    <EnhancedTableCell>
                      <PriorityBadge priority={row.priority} />
                    </EnhancedTableCell>
                    <EnhancedTableCell align="right">
                      <div className="flex items-center justify-end gap-2">
                        <CurrencyBadge currency={row.currency} />
                        <span className="font-medium">
                          {new Intl.NumberFormat().format(row.amount)}
                        </span>
                      </div>
                    </EnhancedTableCell>
                    <EnhancedTableCell variant="muted">
                      {new Date(row.created).toLocaleDateString()}
                    </EnhancedTableCell>
                    <EnhancedTableCell align="center">
                      <div className="flex items-center justify-center gap-1">
                        <IconButton variant="ghost" size="small" tooltip="Edit">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </IconButton>
                        <IconButton 
                          variant="ghost" 
                          size="small" 
                          tooltip="Delete"
                          onClick={() => setConfirmOpen(true)}
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </IconButton>
                      </div>
                    </EnhancedTableCell>
                  </EnhancedTableRow>
                ))}
              </EnhancedTableBody>
            </EnhancedTable>
          </EnhancedCardContent>
        </EnhancedCard>

        {/* Form Dialog */}
        <FormDialog
          open={formOpen}
          onClose={() => setFormOpen(false)}
          onSubmit={handleFormSubmit}
          title="Add New Project"
          icon={
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          }
        >
          <div className="space-y-4">
            <EnhancedTextInput
              label="Project Name"
              name="name"
              value={formData.name}
              onChange={handleFormChange}
              required
              placeholder="Enter project name"
            />
            <EnhancedSelectInput
              label="Project Type"
              name="type"
              value={formData.type}
              onChange={handleFormChange}
              required
              options={[
                { value: 'capex', label: 'CAPEX Project' },
                { value: 'opex', label: 'OPEX Project' },
                { value: 'revenue', label: 'Revenue Project' }
              ]}
            />
            <EnhancedNumberInput
              label="Budget Amount"
              name="amount"
              value={formData.amount}
              onChange={handleFormChange}
              required
              prefix="$"
              placeholder="0.00"
            />
            <EnhancedSelectInput
              label="Currency"
              name="currency"
              value={formData.currency}
              onChange={handleFormChange}
              required
              options={[
                { value: 'USD', label: 'US Dollar (USD)' },
                { value: 'EUR', label: 'Euro (EUR)' },
                { value: 'MYR', label: 'Malaysian Ringgit (MYR)' }
              ]}
            />
          </div>
        </FormDialog>

        {/* Confirmation Dialog */}
        <ConfirmationDialog
          open={confirmOpen}
          onClose={() => setConfirmOpen(false)}
          onConfirm={() => {
            console.log('Item deleted');
            setConfirmOpen(false);
          }}
          title="Delete Project"
          message="Are you sure you want to delete this project? This action cannot be undone."
          variant="danger"
          icon={
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          }
        />
      </div>
    </div>
  );
};

export default DemoPage;
