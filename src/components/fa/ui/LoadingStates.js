import React from 'react';
import { twMerge } from 'tailwind-merge';

/**
 * Base Skeleton Component
 */
const Skeleton = ({ className = "", children, ...props }) => (
  <div 
    className={twMerge(
      "animate-pulse bg-gray-200 rounded", 
      className
    )} 
    {...props}
  >
    {children}
  </div>
);

/**
 * Financial Table Skeleton - for main financial analysis tables
 */
export const FinancialTableSkeleton = ({ rows = 10, columns = 6, showHeader = true }) => (
  <div className="w-full">
    {showHeader && (
      <div className="flex space-x-4 mb-4">
        <Skeleton className="h-8 flex-1" />
        <Skeleton className="h-8 w-32" />
        <Skeleton className="h-8 w-20" />
      </div>
    )}
    
    <div className="space-y-3">
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4">
          <Skeleton className="h-6 flex-1" />
          {Array.from({ length: columns - 1 }).map((_, colIndex) => (
            <Skeleton key={colIndex} className="h-6 w-20" />
          ))}
        </div>
      ))}
    </div>
  </div>
);

/**
 * Summary Cards Skeleton - for summary view
 */
export const SummaryCardsSkeleton = ({ cards = 4 }) => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
    {Array.from({ length: cards }).map((_, index) => (
      <div key={index} className="border border-gray-200 rounded-lg p-4">
        <Skeleton className="h-4 w-3/4 mb-2" />
        <Skeleton className="h-8 w-1/2 mb-1" />
        <Skeleton className="h-3 w-1/4" />
      </div>
    ))}
  </div>
);

/**
 * Approval Workflow Skeleton
 */
export const ApprovalWorkflowSkeleton = ({ steps = 4 }) => (
  <div className="space-y-4">
    <div className="flex items-center justify-between">
      <Skeleton className="h-4 w-32" />
      <Skeleton className="h-4 w-16" />
    </div>
    
    <div className="border border-gray-200 rounded-lg p-4">
      <div className="relative">
        <Skeleton className="h-px w-full mb-4" />
        <div className="flex justify-between">
          {Array.from({ length: steps }).map((_, index) => (
            <div key={index} className="flex flex-col items-center">
              <Skeleton className="w-6 h-6 rounded-full mb-2" />
              <Skeleton className="h-3 w-16 mb-1" />
              <Skeleton className="h-3 w-12" />
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
);

/**
 * Project Details Skeleton
 */
export const ProjectDetailsSkeleton = () => (
  <div className="space-y-6">
    <div>
      <Skeleton className="h-6 w-48 mb-4" />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index}>
            <Skeleton className="h-4 w-24 mb-2" />
            <Skeleton className="h-10 w-full" />
          </div>
        ))}
      </div>
    </div>
    
    <div>
      <Skeleton className="h-6 w-56 mb-4" />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index}>
            <Skeleton className="h-4 w-32 mb-2" />
            <Skeleton className="h-10 w-full" />
          </div>
        ))}
      </div>
    </div>
  </div>
);

/**
 * Scenario List Skeleton
 */
export const ScenarioListSkeleton = ({ scenarios = 3 }) => (
  <div className="space-y-4">
    {Array.from({ length: scenarios }).map((_, index) => (
      <div key={index} className="border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <Skeleton className="h-5 w-40" />
          <Skeleton className="h-6 w-20 rounded-full" />
        </div>
        <div className="grid grid-cols-3 gap-4">
          <div>
            <Skeleton className="h-3 w-16 mb-1" />
            <Skeleton className="h-4 w-24" />
          </div>
          <div>
            <Skeleton className="h-3 w-12 mb-1" />
            <Skeleton className="h-4 w-20" />
          </div>
          <div>
            <Skeleton className="h-3 w-14 mb-1" />
            <Skeleton className="h-4 w-16" />
          </div>
        </div>
      </div>
    ))}
  </div>
);

/**
 * Generic Loading Spinner
 */
export const LoadingSpinner = ({ size = "md", className = "" }) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8", 
    lg: "w-12 h-12"
  };

  return (
    <div className={twMerge("flex items-center justify-center", className)}>
      <div 
        className={twMerge(
          "animate-spin rounded-full border-2 border-b-transparent border-blue-600",
          sizeClasses[size]
        )} 
      />
    </div>
  );
};

/**
 * Full Page Loading State
 */
export const FullPageLoading = ({ message = "Loading..." }) => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50">
    <div className="text-center">
      <LoadingSpinner size="lg" className="mb-4" />
      <p className="text-gray-600 text-lg">{message}</p>
    </div>
  </div>
);

export default {
  Skeleton,
  FinancialTableSkeleton,
  SummaryCardsSkeleton,
  ApprovalWorkflowSkeleton,
  ProjectDetailsSkeleton,
  ScenarioListSkeleton,
  LoadingSpinner,
  FullPageLoading
}; 