import React from 'react';
import { twMerge } from 'tailwind-merge';
import PropTypes from 'prop-types';
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';

/**
 * Enhanced Dialog Components for FA Module
 * Provides consistent, professional dialog styling with better UX
 */

/**
 * Enhanced Dialog Component
 */
export const EnhancedDialog = ({ 
  children, 
  open,
  onClose,
  maxWidth = 'md',
  fullWidth = true,
  className,
  ...props 
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth={maxWidth}
      fullWidth={fullWidth}
      className={className}
      PaperProps={{
        className: 'fa-dialog',
        sx: {
          borderRadius: '16px',
          border: '1px solid #e5e7eb',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)',
          background: 'linear-gradient(135deg, #ffffff 0%, #fafafa 100%)',
          overflow: 'hidden'
        }
      }}
      BackdropProps={{
        sx: {
          backgroundColor: 'rgba(0, 0, 0, 0.4)',
          backdropFilter: 'blur(4px)'
        }
      }}
      {...props}
    >
      {children}
    </Dialog>
  );
};

/**
 * Enhanced Dialog Header
 */
export const EnhancedDialogHeader = ({ 
  children, 
  className,
  icon,
  onClose,
  variant = 'primary',
  ...props 
}) => {
  const variants = {
    primary: 'bg-gradient-to-r from-blue-600 to-blue-700',
    secondary: 'bg-gradient-to-r from-gray-600 to-gray-700',
    success: 'bg-gradient-to-r from-green-600 to-green-700',
    warning: 'bg-gradient-to-r from-yellow-600 to-yellow-700',
    danger: 'bg-gradient-to-r from-red-600 to-red-700'
  };

  return (
    <DialogTitle
      className={twMerge(
        'fa-dialog-header flex items-center justify-between',
        variants[variant],
        className
      )}
      {...props}
    >
      <div className="flex items-center gap-3">
        {icon && (
          <div className="flex-shrink-0">
            {icon}
          </div>
        )}
        <h2 className="fa-dialog-title">{children}</h2>
      </div>
      {onClose && (
        <button
          onClick={onClose}
          className="text-white hover:text-gray-200 transition-colors p-1 rounded-md hover:bg-white/10"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </DialogTitle>
  );
};

/**
 * Enhanced Dialog Content
 */
export const EnhancedDialogContent = ({ 
  children, 
  className,
  padding = 'normal',
  ...props 
}) => {
  const paddingVariants = {
    none: 'p-0',
    small: 'p-4',
    normal: 'p-6',
    large: 'p-8'
  };

  return (
    <DialogContent
      className={twMerge('fa-dialog-content', paddingVariants[padding], className)}
      sx={{
        background: 'linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%)',
        borderTop: '1px solid #f0f0f0'
      }}
      {...props}
    >
      {children}
    </DialogContent>
  );
};

/**
 * Enhanced Dialog Actions
 */
export const EnhancedDialogActions = ({ 
  children, 
  className,
  align = 'right',
  ...props 
}) => {
  const alignClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end',
    between: 'justify-between'
  };

  return (
    <DialogActions
      className={twMerge(
        'fa-dialog-actions flex items-center gap-3 px-6 py-4',
        alignClasses[align],
        className
      )}
      sx={{
        background: 'linear-gradient(135deg, #f9f9f9 0%, #f5f5f5 100%)',
        borderTop: '1px solid #e5e7eb'
      }}
      {...props}
    >
      {children}
    </DialogActions>
  );
};

/**
 * Confirmation Dialog Component
 */
export const ConfirmationDialog = ({ 
  open,
  onClose,
  onConfirm,
  title = 'Confirm Action',
  message = 'Are you sure you want to proceed?',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  variant = 'primary',
  icon,
  loading = false,
  ...props 
}) => {
  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    }
  };

  return (
    <EnhancedDialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      {...props}
    >
      <EnhancedDialogHeader
        variant={variant}
        icon={icon}
        onClose={onClose}
      >
        {title}
      </EnhancedDialogHeader>
      
      <EnhancedDialogContent>
        <div className="text-gray-700">
          {message}
        </div>
      </EnhancedDialogContent>
      
      <EnhancedDialogActions>
        <button
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          disabled={loading}
        >
          {cancelText}
        </button>
        <button
          onClick={handleConfirm}
          className={twMerge(
            'px-4 py-2 text-sm font-medium text-white rounded-lg transition-colors',
            variant === 'danger' ? 'bg-red-600 hover:bg-red-700' : 'bg-blue-600 hover:bg-blue-700',
            loading ? 'opacity-50 cursor-not-allowed' : ''
          )}
          disabled={loading}
        >
          {loading ? (
            <div className="flex items-center gap-2">
              <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
              Processing...
            </div>
          ) : (
            confirmText
          )}
        </button>
      </EnhancedDialogActions>
    </EnhancedDialog>
  );
};

/**
 * Form Dialog Component
 */
export const FormDialog = ({ 
  open,
  onClose,
  onSubmit,
  title,
  children,
  submitText = 'Save',
  cancelText = 'Cancel',
  variant = 'primary',
  icon,
  loading = false,
  submitDisabled = false,
  ...props 
}) => {
  const handleSubmit = (e) => {
    e.preventDefault();
    if (onSubmit) {
      onSubmit(e);
    }
  };

  return (
    <EnhancedDialog
      open={open}
      onClose={onClose}
      {...props}
    >
      <form onSubmit={handleSubmit}>
        <EnhancedDialogHeader
          variant={variant}
          icon={icon}
          onClose={onClose}
        >
          {title}
        </EnhancedDialogHeader>
        
        <EnhancedDialogContent>
          {children}
        </EnhancedDialogContent>
        
        <EnhancedDialogActions className="gap-4">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            disabled={loading}
          >
            {cancelText}
          </button>
          <button
            type="submit"
            className={twMerge(
              'px-4 py-2 text-sm font-medium text-white rounded-lg transition-colors',
              'bg-blue-600 hover:bg-blue-700',
              (loading || submitDisabled) ? 'opacity-50 cursor-not-allowed' : ''
            )}
            disabled={loading || submitDisabled}
          >
            {loading ? (
              <div className="flex items-center gap-2">
                <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                </svg>
                Saving...
              </div>
            ) : (
              submitText
            )}
          </button>
        </EnhancedDialogActions>
      </form>
    </EnhancedDialog>
  );
};

// PropTypes
EnhancedDialog.propTypes = {
  children: PropTypes.node.isRequired,
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  maxWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),
  fullWidth: PropTypes.bool,
  className: PropTypes.string
};

EnhancedDialogHeader.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  icon: PropTypes.node,
  onClose: PropTypes.func,
  variant: PropTypes.oneOf(['primary', 'secondary', 'success', 'warning', 'danger'])
};

EnhancedDialogContent.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  padding: PropTypes.oneOf(['none', 'small', 'normal', 'large'])
};

EnhancedDialogActions.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  align: PropTypes.oneOf(['left', 'center', 'right', 'between'])
};

export default EnhancedDialog;
