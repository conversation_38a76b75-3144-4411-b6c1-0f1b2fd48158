import React from 'react';
import { twMerge } from 'tailwind-merge';
import PropTypes from 'prop-types';

/**
 * Enhanced Card Component for FA Module
 * Provides consistent, professional card styling with proper spacing and visual hierarchy
 */
export const EnhancedCard = ({ 
  children, 
  className, 
  variant = 'default',
  ...props 
}) => {
  const baseClasses = 'bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden';
  
  const variants = {
    default: '',
    elevated: 'shadow-lg',
    bordered: 'border-2 border-blue-200',
    gradient: 'bg-gradient-to-br from-white to-blue-50'
  };

  return (
    <div
      className={twMerge(baseClasses, variants[variant], className)}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * Enhanced Card Header with gradient background and proper typography
 */
export const EnhancedCardHeader = ({ 
  children, 
  className, 
  icon,
  actions,
  variant = 'primary',
  ...props 
}) => {
  const variants = {
    primary: 'bg-gradient-to-r from-blue-600 to-blue-700',
    secondary: 'bg-gradient-to-r from-gray-600 to-gray-700',
    success: 'bg-gradient-to-r from-green-600 to-green-700',
    warning: 'bg-gradient-to-r from-yellow-600 to-yellow-700',
    danger: 'bg-gradient-to-r from-red-600 to-red-700'
  };

  return (
    <div
      className={twMerge(
        'text-white px-6 py-4 border-b border-gray-200 flex items-center justify-between',
        variants[variant],
        className
      )}
      {...props}
    >
      <div className="flex items-center gap-3">
        {icon && (
          <div className="flex-shrink-0">
            {icon}
          </div>
        )}
        <div className="text-lg font-semibold text-white">
          {children}
        </div>
      </div>
      {actions && (
        <div className="flex items-center gap-2">
          {actions}
        </div>
      )}
    </div>
  );
};

/**
 * Enhanced Card Content with proper padding and spacing
 */
export const EnhancedCardContent = ({ 
  children, 
  className, 
  padding = 'normal',
  ...props 
}) => {
  const paddingVariants = {
    none: 'p-0',
    small: 'p-6',
    normal: 'p-8',
    large: 'p-10'
  };

  return (
    <div
      className={twMerge(paddingVariants[padding], className)}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * Enhanced Section Component for organizing content
 */
export const EnhancedSection = ({ 
  title, 
  children, 
  className,
  headerActions,
  collapsible = false,
  defaultOpen = true,
  icon,
  ...props 
}) => {
  const [isOpen, setIsOpen] = React.useState(defaultOpen);

  return (
    <div className={twMerge('fa-section', className)} {...props}>
      <div className="fa-section-header">
        <div className="flex items-center gap-3">
          {icon && <div className="flex-shrink-0">{icon}</div>}
          <h3 className="fa-section-title">{title}</h3>
          {collapsible && (
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="ml-2 p-1 rounded-md hover:bg-gray-200 transition-colors"
            >
              <svg
                className={twMerge(
                  'w-4 h-4 transition-transform duration-200',
                  isOpen ? 'rotate-180' : ''
                )}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>
          )}
        </div>
        {headerActions && (
          <div className="flex items-center gap-2">
            {headerActions}
          </div>
        )}
      </div>
      {(!collapsible || isOpen) && (
        <div className="fa-section-content">
          {children}
        </div>
      )}
    </div>
  );
};

/**
 * Stats Card for displaying key metrics
 */
export const StatsCard = ({ 
  title, 
  value, 
  icon, 
  trend, 
  className,
  variant = 'default',
  ...props 
}) => {
  const variants = {
    default: 'border-gray-200',
    primary: 'border-blue-200 bg-blue-50',
    success: 'border-green-200 bg-green-50',
    warning: 'border-yellow-200 bg-yellow-50',
    danger: 'border-red-200 bg-red-50'
  };

  return (
    <div
      className={twMerge(
        'fa-stats-card border-l-4',
        variants[variant],
        className
      )}
      {...props}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="fa-stats-value">{value}</div>
          <div className="fa-stats-label">{title}</div>
          {trend && (
            <div className="mt-2 flex items-center text-sm">
              {trend}
            </div>
          )}
        </div>
        {icon && (
          <div className="flex-shrink-0 ml-4">
            <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
              {icon}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// PropTypes
EnhancedCard.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  variant: PropTypes.oneOf(['default', 'elevated', 'bordered', 'gradient'])
};

EnhancedCardHeader.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  icon: PropTypes.node,
  actions: PropTypes.node,
  variant: PropTypes.oneOf(['primary', 'secondary', 'success', 'warning', 'danger'])
};

EnhancedCardContent.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  padding: PropTypes.oneOf(['none', 'small', 'normal', 'large'])
};

EnhancedSection.propTypes = {
  title: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  headerActions: PropTypes.node,
  collapsible: PropTypes.bool,
  defaultOpen: PropTypes.bool,
  icon: PropTypes.node
};

StatsCard.propTypes = {
  title: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number, PropTypes.node]).isRequired,
  icon: PropTypes.node,
  trend: PropTypes.node,
  className: PropTypes.string,
  variant: PropTypes.oneOf(['default', 'primary', 'success', 'warning', 'danger'])
};

export default EnhancedCard;
