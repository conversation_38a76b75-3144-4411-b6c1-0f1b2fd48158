/**
 * Enhanced UI Components for FA Module
 * 
 * This module provides a comprehensive set of enhanced UI components
 * specifically designed for the Financial Analysis (FA) module.
 * 
 * Features:
 * - Consistent design system
 * - Professional styling
 * - High readability
 * - Responsive design
 * - Accessibility support
 * - Dark mode compatibility
 */

// Card Components
export {
  EnhancedCard,
  EnhancedCardHeader,
  EnhancedCardContent,
  EnhancedSection,
  StatsCard
} from './EnhancedCard';

// Table Components
export {
  EnhancedTable,
  EnhancedTableHeader,
  EnhancedTableHeaderCell,
  EnhancedTableBody,
  EnhancedTableRow,
  EnhancedTableCell,
  TableActions,
  TableActionButton,
  TableEmptyState,
  CollapsibleTableRowGroup,
  SubTableRow
} from './EnhancedTable';

// Form Components
export {
  FormGroup,
  FormLabel,
  EnhancedTextInput,
  EnhancedSelectInput,
  EnhancedTextareaInput,
  EnhancedDateInput,
  EnhancedNumberInput,
  EnhancedSwitchInput,
  FormActions
} from './EnhancedForm';

// Button Components
export {
  EnhancedButton,
  IconButton,
  ButtonGroup,
  FloatingActionButton,
  SplitButton,
  LinkButton
} from './EnhancedButton';

// Dialog Components
export {
  EnhancedDialog,
  EnhancedDialogHeader,
  EnhancedDialogContent,
  EnhancedDialogActions,
  ConfirmationDialog,
  FormDialog
} from './EnhancedDialog';

// Badge Components
export {
  EnhancedBadge,
  StatusBadge,
  PriorityBadge,
  CurrencyBadge,
  CountBadge,
  TagBadge,
  NotificationBadge
} from './EnhancedBadge';

// Tree/Collapsible Components
export {
  TreeSymbolReplacer,
  ModernTreeItem
} from './TreeToCollapsible';

// Default exports for convenience
export { default as Card } from './EnhancedCard';
export { default as Table } from './EnhancedTable';
export { default as Form } from './EnhancedForm';
export { default as Button } from './EnhancedButton';
export { default as Dialog } from './EnhancedDialog';
export { default as Badge } from './EnhancedBadge';

/**
 * Component Usage Examples:
 * 
 * // Card with header and content
 * <EnhancedCard>
 *   <EnhancedCardHeader icon={<Icon />} actions={<Button />}>
 *     Title
 *   </EnhancedCardHeader>
 *   <EnhancedCardContent>
 *     Content here
 *   </EnhancedCardContent>
 * </EnhancedCard>
 * 
 * // Professional table
 * <EnhancedTable>
 *   <EnhancedTableHeader>
 *     <EnhancedTableRow>
 *       <EnhancedTableHeaderCell>Column 1</EnhancedTableHeaderCell>
 *       <EnhancedTableHeaderCell>Column 2</EnhancedTableHeaderCell>
 *     </EnhancedTableRow>
 *   </EnhancedTableHeader>
 *   <EnhancedTableBody>
 *     <EnhancedTableRow clickable>
 *       <EnhancedTableCell>Data 1</EnhancedTableCell>
 *       <EnhancedTableCell>Data 2</EnhancedTableCell>
 *     </EnhancedTableRow>
 *   </EnhancedTableBody>
 * </EnhancedTable>
 * 
 * // Form with enhanced inputs
 * <FormDialog title="Add Item" open={open} onClose={onClose}>
 *   <EnhancedTextInput label="Name" required />
 *   <EnhancedSelectInput label="Type" options={options} />
 *   <EnhancedNumberInput label="Amount" prefix="$" />
 * </FormDialog>
 * 
 * // Status indicators
 * <StatusBadge status="pending" />
 * <CurrencyBadge currency="USD" />
 * <PriorityBadge priority="high" />
 */
