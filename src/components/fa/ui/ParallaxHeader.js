// React
import { useEffect, useState, useCallback, useRef } from 'react';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Components
import { useAuthContext } from '../../../utils/auth/useAuthContext';
import { useParamContext } from '../../../utils/auth/ParamProvider';

// Others
import moment from 'moment';

const ParallaxHeader = ({ projectData, projectId, isScrolled }) => {
  const { user } = useAuthContext();
  const { breadCrumbsList } = useSelector((state) => state?.simi);
  const { scenarioData } = useSelector((state) => state?.fa);
  const { asPath, query } = useRouter();
  const { setParam } = useParamContext();
  const [headerHeight, setHeaderHeight] = useState(61);

  // Get current module for logo and check if we're on scenario page
  const currentModule = asPath?.split('?')[0]?.split('/')[1];
  const { scenarioId, tab } = query;
  const isScenarioPage = !!scenarioId;

  // Navigation tabs for scenario pages (matching ScenarioDetails component)
  const scenarioTabs = [
    { id: 'profile', label: 'Profile', color: 'bg-blue-100 text-blue-800' },
    { id: 'capex', label: 'CAPEX', color: 'bg-green-100 text-green-800' },
    { id: 'opex', label: 'OPEX', color: 'bg-orange-100 text-orange-800' },
    { id: 'revenue', label: 'Revenue', color: 'bg-purple-100 text-purple-800' },
    { id: 'fa', label: 'Financial Analysis', color: 'bg-indigo-100 text-indigo-800' }, // Note: uses 'fa' not 'financial-analysis'
    { id: 'summary', label: 'Summary', color: 'bg-gray-100 text-gray-800' }
  ];

  // Handle tab navigation using setParam (same as ScenarioDetails)
  const handleTabClick = (tabId) => {
    setParam({ tab: tabId });
  };

  // Calculate exact header height dynamically
  useEffect(() => {
    const calculateHeaderHeight = () => {
      // Find the main header element and breadcrumb area
      const headerElement = document.querySelector('.bg-primary');
      const breadcrumbElement = document.querySelector('.bg-white.dark\\:bg-gray-600');

      if (headerElement) {
        const headerRect = headerElement.getBoundingClientRect();
        const totalHeight = headerRect.bottom;
        setHeaderHeight(totalHeight);
      }
    };

    // Wait for DOM to be ready
    setTimeout(calculateHeaderHeight, 100);
    window.addEventListener('resize', calculateHeaderHeight);

    return () => {
      window.removeEventListener('resize', calculateHeaderHeight);
    };
  }, []);

  return (
    <>
      {/* Compact Header - Smooth fade in/out animation */}
      <div
        className="fixed left-0 right-0 z-50 bg-white dark:bg-gray-600 dark:text-white transition-opacity duration-700 ease-out"
        style={{
          top: '51px', // Perfect position aligned with breadcrumb area
          height: '40px', // Match breadcrumb area height exactly
          opacity: isScrolled ? 1 : 0,
          pointerEvents: isScrolled ? 'auto' : 'none'
        }}
      >
        {/* Match the exact structure of standard layout breadcrumb area */}
        <div className="flex w-full items-center justify-start gap-4 pr-8" style={{ height: '40px', minHeight: '40px' }}>
          {/* Module Logo - Same as standard layout */}
          <div className="relative flex h-[40px] w-[40px] items-center justify-center">
            <img
              src={`/assets/moduleLogo/${currentModule}.webp`}
              alt="Module Logo"
              className="h-[40px] w-[40px]"
              loading="eager"
            />
          </div>

          {/* Breadcrumbs - Same as standard layout */}
          <div className="flex items-center gap-2 text-xs font-bold">
            {breadCrumbsList.map((o, i) => (
              <div key={i} className="flex gap-2">
                <a href={o.linkTo} className="whitespace-nowrap hover:cursor-pointer hover:underline">
                  {o?.label}
                </a>
                {i !== breadCrumbsList?.length - 1 && <span>/</span>}
              </div>
            ))}
          </div>

          {/* Project Info - Compact inline (only show when NOT on scenario page) */}
          {projectData && !isScenarioPage && (
            <div className="flex items-center gap-3 ml-4 pl-4 border-l border-slate-200 dark:border-gray-500">
              <div className="w-2 h-6 bg-gradient-to-b from-blue-600 to-blue-700 rounded-full"></div>
              <div className="flex items-center gap-3">
                <h2 className="text-sm font-bold text-slate-900 dark:text-white leading-tight">
                  {projectData?.name || 'Financial Analysis Project'}
                </h2>
                <div className="flex items-center gap-2">
                  <span className="text-xs text-slate-500 dark:text-gray-300 font-medium">ID:</span>
                  <code className="text-xs bg-slate-100 dark:bg-gray-700 text-slate-700 dark:text-gray-200 px-1 py-0.5 rounded font-mono">
                    {projectId}
                  </code>
                  <div className={`px-2 py-0.5 rounded-full text-xs font-semibold ${
                    projectData?.status === 'draft'
                      ? 'bg-amber-100 text-amber-800'
                      : projectData?.status === 'completed'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-blue-100 text-blue-800'
                  }`}>
                    {projectData?.status?.toUpperCase() || 'DRAFT'}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Scenario Info - Compact inline with navigation tabs */}
          {isScenarioPage && scenarioData && (
            <div className="flex items-center gap-3 ml-4 pl-4 border-l border-slate-200 dark:border-gray-500 flex-1">
              <div className="w-2 h-6 bg-gradient-to-b from-blue-600 to-blue-700 rounded-full"></div>

              {/* Scenario Info */}
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <h2 className="text-sm font-bold text-slate-900 dark:text-white leading-tight">
                    {scenarioData?.name || 'Scenario Details'}
                  </h2>
                  <code className="text-xs bg-blue-100 text-blue-700 px-1 py-0.5 rounded font-mono">
                    {scenarioData?.scenario_id || 'N/A'}
                  </code>
                </div>
                <div className="flex items-center gap-3 text-xs text-slate-600 dark:text-gray-300">
                  <span>
                    <span className="font-medium">Contract:</span> {scenarioData?.contract_period_in_month}M
                  </span>
                  <span>
                    <span className="font-medium">Project:</span> {projectData?.name}
                  </span>
                </div>
              </div>

              {/* Navigation Tabs - Compact */}
              <div className="flex items-center gap-1 ml-auto">
                {scenarioTabs.map((tabItem) => (
                  <button
                    key={tabItem.id}
                    onClick={() => handleTabClick(tabItem.id)}
                    className={`px-2 py-1 rounded text-xs font-medium transition-all duration-200 hover:scale-105 ${
                      (tab || 'capex') === tabItem.id
                        ? tabItem.color + ' shadow-sm'
                        : 'bg-slate-100 text-slate-600 hover:bg-slate-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    {tabItem.label}
                  </button>
                ))}
              </div>
            </div>
          )}


        </div>
      </div>
    </>
  );
};

export default ParallaxHeader;
