// Next, React, Tw
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { twMerge } from 'tailwind-merge';
import PropTypes from 'prop-types';

// Components
import UserPopup from '../../Shared/UserPopup';

// Others
import axios from '../../../utils/axios';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { checkAndReplaceStringWithHyphen } from '../../../utils/shared';

/**
 * Enhanced Approval Step Component
 */
const EnhancedApprovalStep = ({ 
  approval, 
  isActive, 
  isCompleted, 
  isLast, 
  stepNumber 
}) => {
  const { allStaffs } = useSelector((state) => state.aum);

  const formatName = (name) => {
    if (checkAndReplaceStringWithHyphen(name) === '-') return '-';
    const nameParts = name?.split(' ') || [];
    return nameParts.map(part => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()).join(' ');
  };

  const getStepIcon = () => {
    if (isCompleted) {
      return (
        <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
      );
    }

    if (isActive) {
      return (
        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
          <div className="w-2 h-2 bg-white rounded-full"></div>
        </div>
      );
    }

    return (
      <div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
        <span className="text-gray-600 font-medium text-xs">{stepNumber}</span>
      </div>
    );
  };

  const getStatusText = () => {
    if (approval?.type === 'completed') return 'Complete';
    if (isCompleted) return 'Approved';
    if (isActive) return 'Pending';
    return 'Waiting';
  };

  return (
    <div className="flex flex-col items-center text-center flex-1 min-w-0">
      {/* Step Icon */}
      <div className="mb-2">
        {getStepIcon()}
      </div>

      {/* Step Content */}
      <div className="w-full">
        <div className="text-xs font-medium text-gray-900 mb-1">
          {approval?.type?.replace(/\b\w/g, l => l.toUpperCase())}
        </div>
        <div className={twMerge(
          "text-xs mb-2",
          isCompleted ? "text-green-600" : isActive ? "text-blue-600" : "text-gray-500"
        )}>
          {getStatusText()}
        </div>
        
        {/* Show assigned users */}
        {approval?.user_name_array && approval.user_name_array.length > 0 && approval.type !== 'completed' && (
          <div className="space-y-1">
            {approval.user_name_array.slice(0, 2).map((userName, idx) => (
              <div key={idx} className="text-xs text-gray-700 truncate">
                {formatName(userName) !== '-' ? formatName(userName) : 'Not Assigned'}
              </div>
            ))}
            {approval.user_name_array.length > 2 && (
              <div className="text-xs text-gray-500">
                +{approval.user_name_array.length - 2} more
              </div>
            )}
          </div>
        )}
        
        {/* Show "Not Assigned" for empty or placeholder users */}
        {approval?.type !== 'completed' && (!approval?.user_name_array || 
          approval.user_name_array.length === 0 || 
          approval.user_name_array.every(name => !name || name.trim() === '')) && (
          <div className="text-xs text-red-500 italic font-medium">
            {approval?.type?.toLowerCase() === 'approver' ? 'Assign Approver' : 
             approval?.type?.toLowerCase() === 'reviewer' ? 'Select Reviewer' : 
             'Not Assigned'}
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Enhanced Approval Workflow Component
 */
export const EnhancedApprovalWorkflow = ({ 
  GET_ALL_APPROVALS_ENDPOINT,
  className,
  showHeader = true 
}) => {
  const dispatch = useDispatch();
  const [approvalsData, setApprovalsData] = useState([]);
  const [loading, setLoading] = useState(true);

  const activeStepIndex = approvalsData?.findIndex((approval) => 
    approval?.status?.toLowerCase() === 'pending'
  );

  const completedSteps = activeStepIndex === -1 ? approvalsData.length : activeStepIndex;
  const progressPercentage = approvalsData.length > 0 
    ? (completedSteps / approvalsData.length) * 100 
    : 0;

  const fetchData = async () => {
    if (!GET_ALL_APPROVALS_ENDPOINT) return;
    
    setLoading(true);
    try {
      const response = await axios.get(GET_ALL_APPROVALS_ENDPOINT);
      if (response.data.data) {
        const approvals = [...response.data.data];
        
        // Add completion step
        approvals.push({
          type: 'completed',
          status: activeStepIndex === -1 ? 'completed' : 'pending',
          user_name_array: []
        });
        
        setApprovalsData(approvals);
      }
    } catch (error) {
      console.error('Failed to fetch approval data:', error);
      setApprovalsData([]);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, [GET_ALL_APPROVALS_ENDPOINT]);

  // Listen for approval updates
  useEffect(() => {
    const handleApprovalUpdate = (event) => {
      if (event.detail.endpoint === GET_ALL_APPROVALS_ENDPOINT || event.detail.refreshAll) {
        fetchData();
      }
    };

    window.addEventListener('approvalUpdated', handleApprovalUpdate);
    return () => window.removeEventListener('approvalUpdated', handleApprovalUpdate);
  }, [GET_ALL_APPROVALS_ENDPOINT]);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className={twMerge("space-y-4", className)}>
      {/* Simple Progress */}
      {showHeader && (
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">
            {completedSteps} of {approvalsData.length} steps completed
          </span>
          <span className="font-medium text-blue-600">{Math.round(progressPercentage)}%</span>
        </div>
      )}

      {/* Approval Steps */}
      <div className="border border-gray-200 rounded-lg p-6">
        {approvalsData.length > 0 ? (
          <div className="relative">
            {/* Connection Line */}
            <div className="absolute top-3 left-0 right-0 h-px bg-gray-300 z-0">
              <div 
                className="h-full bg-green-500 transition-all duration-300"
                style={{ 
                  width: `${(completedSteps / Math.max(approvalsData.length - 1, 1)) * 100}%` 
                }}
              />
            </div>
            
            {/* Steps */}
            <div className="relative flex justify-between z-10 gap-4">
              {approvalsData.map((approval, index) => (
                <EnhancedApprovalStep
                  key={index}
                  approval={approval}
                  isActive={index === activeStepIndex}
                  isCompleted={index < activeStepIndex || (activeStepIndex === -1 && index < approvalsData.length)}
                  isLast={index === approvalsData.length - 1}
                  stepNumber={index + 1}
                />
              ))}
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-sm text-gray-500 mb-1">No approval workflow configured</p>
            <p className="text-xs text-gray-400">Configure approvers in the section below</p>
          </div>
        )}
      </div>
    </div>
  );
};

// PropTypes
EnhancedApprovalStep.propTypes = {
  approval: PropTypes.object.isRequired,
  isActive: PropTypes.bool.isRequired,
  isCompleted: PropTypes.bool.isRequired,
  isLast: PropTypes.bool.isRequired,
  stepNumber: PropTypes.number.isRequired
};

EnhancedApprovalWorkflow.propTypes = {
  GET_ALL_APPROVALS_ENDPOINT: PropTypes.string.isRequired,
  className: PropTypes.string,
  showHeader: PropTypes.bool
};

export default EnhancedApprovalWorkflow; 