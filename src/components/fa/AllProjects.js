// Next, React, Tw
import { useState, useEffect, useRef, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions, Autocomplete, TextField } from '@mui/material';
import { Edit, DeleteOutline } from '@mui/icons-material';

// Packages
import * as yup from 'yup';
import moment from 'moment';
import { twMerge } from 'tailwind-merge';
import dayjs from 'dayjs';
import { NextSeo } from 'next-seo';

// Components
import { TextInput, SearchInput, SelectInput } from '../Shared/CustomInput';
import UserPopup from '../Shared/UserPopup';
import StatusBadge from './StatusBadge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { EmptyState } from '@/components/ui/empty-state';
import {
  ConfirmationDialog
} from './ui/EnhancedDialog';

// Others
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { FA_ENDPOINT, useFaContext } from '../../utils/fa';
import { useSnackbar } from '../Shared/snackbar';
import { checkAndReplaceStringWithHyphen } from '../../utils/shared';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import isWeekend from 'date-fns/isWeekend';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

const AllProjects = () => {
  // Standard and Vars
  const dispatch = useDispatch();
  const { push, query } = useRouter();
  const { q } = query;
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuthContext();
  const { isAdmin } = useModuleRoleContext();
  const { handleCreateHistory } = useFaContext();

  const [rateData, setRateData] = useState(null);

  // Table
  const [tableData, setTableData] = useState([]);
  const headerCellStyle =
    'px-4 py-2.5 text-left text-xs font-medium text-gray-600 uppercase tracking-wide border-b border-gray-200';
  const bodyCellStyle = 'px-4 py-3 text-sm text-gray-700 dark:text-gray-300';
  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) return tableData;
    return tableData.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);

  // State for Dialog Inputs (moved outside renderContent for better control)
  const [dialogTitle, setDialogTitle] = useState('');
  const [dialogBudgetRange, setDialogBudgetRange] = useState('');

  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState(null);
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  

  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };


  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
      // Pre-fill dialog state when editing
      setDialogTitle(data?.name || '');
      
      // Define the available options for mapping
      const budgetOptions = [
        'below rm 100k',
        'between rm 100k and rm 500k',
        'between rm 500k and rm 5mil',
        'above rm 5mil'
      ];
      
      // Handle project value category from stored data
      const budgetValue = data?.total_capex_opex || '';
      
      if (budgetValue) {
        // Try to match stored value with available options
        const matchingOption = budgetOptions.find(option => 
          option.toLowerCase() === budgetValue.toLowerCase().trim()
        );
        setDialogBudgetRange(matchingOption || budgetValue);
      } else {
        // If no stored value, leave empty for user selection
        setDialogBudgetRange('');
      }
    } else {
      // Reset dialog state for new project with default status
      setDialogData({
        status: 'draft', // Set default status for new projects
      });
      setDialogTitle('');
      setDialogBudgetRange('');
    }
    setFormErrors({});
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };

  const schema = yup.object({
    name: yup.string().required('Project name is required.'),
    total_capex_opex: yup.string().required('Project value category is required.'),
    status: yup.string().required('Please provide status.')?.default('draft'),
    version_number: yup.number().required('Please provide version number.').default(1),
    viewer_staff_id_list: yup.array().required('Please provide staff list.').default([]),
    rates_locked_during_creation: yup.object().required('Please provide rates.').default(rateData),
    created_by_staff_id: yup
      .string()
      .required('Please provide created by.')
      ?.default(user?.staff_id),
  });

  const handleCreateApproval = async (projectId) => {
    // Fetch All Existing Approvals
    let existingApprovals = [];

    try {
      const response = await axios.get(`${FA_ENDPOINT}/approval/project_id/${projectId}`);
      existingApprovals = response?.data?.data || [];
    } catch {
      /* empty */
    }

    // Delete Existing Approvals
    for (let i = 0; i < existingApprovals?.length; i += 1) {
      try {
        await axios.delete(`${FA_ENDPOINT}/approval/${existingApprovals?.[i]?.id}`);
      } catch {
        /* empty */
      }
    }

    // Save New
    const projectIsAbove5mil = dialogData?.total_capex_opex?.includes('above');
    // Default reviewers available for selection (can be changed in Configure Approvers)
    const defaultReviewers = ['Siti Asrah Binti Idris', 'Noor Hanani'];

    const temp = [
      {
        type: 'requestor',
        user_name_array: [user?.name],
        is_disabled: true,
      },
      {
        type: 'approver',
        user_name_array: [], // Empty array - user needs to assign
        is_disabled: false,
      },
      {
        type: 'reviewer',
        user_name_array: [], // Empty array - user needs to select from available reviewers
        is_disabled: false,
      },
    ];

    if (projectIsAbove5mil) {
      temp?.push({
        type: 'endorser 1',
        user_name_array: ['Hanum Adini Bt Hamzah'],
        is_disabled: true,
      });
      temp?.push({
        type: 'endorser 2',
        user_name_array: ['Sulaiman Bin Yahaya'],
        is_disabled: true,
      });
    } else {
      temp?.push({
        type: 'endorser',
        user_name_array: ['Hanum Adini Bt Hamzah'],
        is_disabled: true,
      });
    }

    try {
      for (let i = 0; i < temp.length; i += 1) {
        await axios.post(`${FA_ENDPOINT}/approval`, {
          project_id: projectId,
          // Only requestor (first approval) starts as pending, others wait
          status: i === 0 ? 'pending' : 'waiting',
          ...temp?.[i],
        });
      }
    } catch {
      /* empty */
    }
  };

  // Add refs for the buttons that might keep focus
  const newProjectButtonRef = useRef(null);

  // Add useEffect to manage focus state
  useEffect(() => {
    // When dialog is closed, ensure buttons are not focused
    if (!dialogOpen) {
      // Reset focus using a timeout to ensure it happens after React rendering
      setTimeout(() => {
        if (document.activeElement) {
          document.activeElement.blur();
        }
      }, 100);
    }
  }, [dialogOpen]);

  const handleDialogClose = async (action) => {
    if (action === 'cancel') {
      // Reset dialog state and close
      setDialogTitle('');
      setDialogBudgetRange('');
      setDialogData({});
      setFormErrors({});
      setDialogOpen(false);
      return;
    }

    if (!action) return;

    setIsSubmitting(true);
    setFormErrors({}); // Clear previous errors immediately

    // Prepare data for validation
    const currentDialogData = {
      ...dialogData, 
      name: dialogTitle,
      total_capex_opex: dialogBudgetRange,
      created_by_staff_id: user?.staff_id,
      rates_locked_during_creation: rateData,
    };

    // Validate before proceeding for post/put actions
    if (['post', 'put'].includes(action)) {
      try {
        await schema.validate(currentDialogData, { abortEarly: false });
      } catch (validationError) {
        // Handle validation errors - build error object
        const errors = {};
        validationError.inner?.forEach((err) => {
          errors[err.path] = err.message;
        });
        setFormErrors(errors);
        setIsSubmitting(false);
        return; // Stop execution if validation fails
      }
    }

    // Execute API operations
    try {
      let response;
      switch (action) {
        case 'post':
          response = await axios.post(`${FA_ENDPOINT}/project`, currentDialogData);
          break;
        case 'put':
          response = await axios.put(`${FA_ENDPOINT}/project/${currentDialogData?.id}`, currentDialogData);
          break;
        case 'delete':
          response = await axios.delete(`${FA_ENDPOINT}/project/${currentDialogData?.id}`);
          break;
        default:
          throw new Error(`Unknown action: ${action}`);
      }
      
      // Handle successful response
      if (response.data.status === 'success') {
        // Execute post-creation tasks
        switch (action) {
          case 'post':
            const projectId = response.data.data?.split(' ')[2];
            if (projectId) {
              await Promise.all([
                handleCreateApproval(projectId),
                handleCreateHistory(projectId, 'Project was created.')
              ]);
            }
            break;
          case 'put':
            const updatedProjectId = response.data.data?.split(' ')[4];
            if (updatedProjectId) {
              await handleCreateApproval(updatedProjectId);
            }
            break;
          default:
            break;
        }

        enqueueSnackbar(response.data.data || 'Operation completed successfully', {
          variant: 'success',
        });
      } else {
        throw new Error(response.data.message || 'Operation failed');
      }

    } catch (error) {
      console.error(`${action} operation failed:`, error);
      enqueueSnackbar(error.message || 'Operation failed', {
        variant: 'error',
      });
    } finally {
      // Always cleanup and refresh data
      setIsSubmitting(false);
      setDialogTitle('');
      setDialogBudgetRange('');
      setDialogData({});
      setFormErrors({});
      setDialogOpen(false);
      
      // Remove focus
      if (document.activeElement) {
        document.activeElement.blur();
      }
      
      // Refresh data
      await fetchData();
    }
  };

  // Others
  const handleClickRow = (id) => push(`/fa/project/${id}`);

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      // Fetch all projects for both admin and non-admin users
      const response = await axios.get(`${FA_ENDPOINT}/project/all/keyword`);
      const allProjects = response?.data?.data || [];
      

      // Get all approvals regardless of admin status
      let approvals = [];
      try {
        const approvalsResponse = await axios.get(`${FA_ENDPOINT}/approval/all/keyword`);
        approvals = approvalsResponse?.data?.data || [];
      } catch {
        approvals = [];
      }

      if (isAdmin) {
        // Admin users can see all projects (including drafts) for management purposes
        // Add approval information to each project
        const projectsWithApprovalInfo = allProjects.map((project) => {
          // Find approvals for this project
          const projectApprovals = approvals.filter(
            (approval) => approval.project_id === project.id
          );

          // Check if user is set for approval and status is pending
          const requiresUserApproval = projectApprovals.some(
            (approval) =>
              approval.user_name_array &&
              approval.user_name_array.includes(user?.name) &&
              approval.status === 'pending'
          );

          return {
            ...project,
            requiresUserApproval,
          };
        });

        projectsWithApprovalInfo.reverse();
        setTableData(projectsWithApprovalInfo);
      } else {
        // Non-admin users can only see projects they created or are involved in approvals
        try {
          // Filter projects for non-admin users
          const filteredProjects = allProjects.filter((project) => {
            // Include projects created by the user (regardless of status)
            if (project.created_by_staff_id === user?.staff_id) {
              return true;
            }

            // For non-creators, only show projects that have been submitted (not in draft status)
            if (project.status === 'draft') {
              return false; // Hide draft projects from approvers/reviewers/endorsers
            }

            // Include submitted projects where the user is in any approval's user_name_array
            const projectApprovals = approvals.filter(
              (approval) => approval.project_id === project.id
            );

            return projectApprovals.some(
              (approval) =>
                approval.user_name_array && approval.user_name_array.includes(user?.name)
            );
          });

          // Add approval information to each project
          const projectsWithApprovalInfo = filteredProjects.map((project) => {
            // Find approvals for this project
            const projectApprovals = approvals.filter(
              (approval) => approval.project_id === project.id
            );

            // Check if user is set for approval and status is pending
            const requiresUserApproval = projectApprovals.some(
              (approval) =>
                approval.user_name_array &&
                approval.user_name_array.includes(user?.name) &&
                approval.status === 'pending'
            );

            return {
              ...project,
              requiresUserApproval,
            };
          });

          projectsWithApprovalInfo.reverse();
          setTableData(projectsWithApprovalInfo);
        } catch {
          // If fetching approvals fails, just show projects created by the user
          const userProjects = allProjects.filter(
            (project) => project.created_by_staff_id === user?.staff_id
          );
          userProjects.reverse();
          setTableData(userProjects);
        }
      }
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  const fetchRateData = async () => {
    try {
      const response = await axios.get(`${FA_ENDPOINT}/rate_percentage/all/keyword`);
      setRateData(response?.data?.data?.[0] || null);
    } catch {
      setRateData(null);
    }
  };

  useEffect(() => {
    fetchData();
  }, [isAdmin, user?.staff_id]);

  useEffect(() => {
    fetchRateData();
  }, []);

  // Filter and sorting state
  const [activeFilter, setActiveFilter] = useState('');
  const [sortConfig, setSortConfig] = useState({
    key: 'name', // Default sort by name
    direction: 'asc',
  });

  // Toggle filter function
  const toggleFilter = (filterName) => {
    if (activeFilter === filterName) {
      // If clicking the active filter, clear it
      setActiveFilter('');
    } else {
      // Set the new filter
      setActiveFilter(filterName);
    }
  };

  // Handle sort request
  const requestSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Get filtered table data
  const getFilteredTableData = () => {
    let filtered = tableData;

    if ([undefined, '']?.includes(q)) {
      // Apply status filter if active
      if (activeFilter) {
        return filtered.filter((project) =>
          project?.status?.toLowerCase()?.includes(activeFilter.toLowerCase())
        );
      }
      return filtered;
    }

    return filtered.filter((o) => JSON.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  };

  // Sort the filtered data
  const sortedTableData = useMemo(() => {
    let sortableItems = [...getFilteredTableData()];

    if (sortConfig.key) {
      sortableItems.sort((a, b) => {
        let aValue = a[sortConfig.key] || '';
        let bValue = b[sortConfig.key] || '';

        // Handle date sorting
        if (sortConfig.key === 'created_at') {
          aValue = new Date(aValue).getTime();
          bValue = new Date(bValue).getTime();
        }

        // Handle string comparison
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          if (sortConfig.direction === 'asc') {
            return aValue.localeCompare(bValue);
          } else {
            return bValue.localeCompare(aValue);
          }
        }

        // Handle number comparison
        if (sortConfig.direction === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
    }

    return sortableItems;
  }, [tableData, sortConfig, activeFilter, q]);

  // Add a function to handle delete confirmation
  const handleDeleteClick = (e, project) => {
    e.stopPropagation();
    setProjectToDelete(project);
    setDeleteConfirmOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!projectToDelete) return;
    
    dispatch(setIsLoading(true));
    try {
      const response = await axios.delete(`${FA_ENDPOINT}/project/${projectToDelete.id}`);
      
      if (response.data.status === 'success') {
        enqueueSnackbar('Project deleted successfully', {
          variant: 'success',
        });
        await handleCreateHistory(projectToDelete.id, 'Project was deleted by admin.');
      } else {
        enqueueSnackbar('Failed to delete project', {
          variant: 'error',
        });
      }
    } catch (error) {
      enqueueSnackbar('Failed to delete project', {
        variant: 'error',
      });
    }
    
    setDeleteConfirmOpen(false);
    setProjectToDelete(null);
    dispatch(setIsLoading(false));
    fetchData(); // Refresh data after deletion
  };

  return (
    <>
      <div className="container mx-auto px-4 py-4">
        <div className="overflow-hidden rounded-lg bg-white shadow-sm dark:bg-gray-800">
          {/* Header with search and filters */}
          <div className="flex flex-col items-center justify-between gap-3 border-b border-gray-200 bg-white px-4 py-3 sm:flex-row">
            <div className="flex items-center">
              <h2 className="text-lg font-medium text-gray-800">Financial Analysis Projects</h2>
            </div>

            <div className="flex items-center gap-4">
              {/* Legend moved to right side and made clickable */}
              <div className="mr-0 hidden items-center gap-3 md:flex">
                <div
                  className={`flex cursor-pointer items-center gap-1.5 rounded px-2 py-1 transition-colors ${
                    activeFilter === 'draft'
                      ? 'bg-blue-100 ring-1 ring-blue-400'
                      : 'hover:bg-gray-100'
                  }`}
                  onClick={() => toggleFilter('draft')}
                >
                  <span className="h-2.5 w-2.5 rounded-full bg-blue-500" />
                  <span className="text-xs text-gray-600">Draft</span>
                </div>
                <div
                  className={`flex cursor-pointer items-center gap-1.5 rounded px-2 py-1 transition-colors ${
                    activeFilter === 'pending approver'
                      ? 'bg-amber-100 ring-1 ring-amber-400'
                      : 'hover:bg-gray-100'
                  }`}
                  onClick={() => toggleFilter('pending approver')}
                >
                  <span className="h-2.5 w-2.5 rounded-full bg-amber-500" />
                  <span className="text-xs text-gray-600">Pending Approver</span>
                </div>
                <div
                  className={`flex cursor-pointer items-center gap-1.5 rounded px-2 py-1 transition-colors ${
                    activeFilter === 'completed'
                      ? 'bg-green-100 ring-1 ring-green-400'
                      : 'hover:bg-gray-100'
                  }`}
                  onClick={() => toggleFilter('completed')}
                >
                  <span className="h-2.5 w-2.5 rounded-full bg-green-500" />
                  <span className="text-xs text-gray-600">Completed</span>
                </div>
                {activeFilter && (
                  <div
                    className="flex cursor-pointer items-center gap-1.5 rounded px-2 py-1 hover:bg-gray-100"
                    onClick={() => setActiveFilter('')}
                  >
                    <span className="text-xs font-medium text-blue-600">Clear filter</span>
                  </div>
                )}
              </div>

              <div className="w-full min-w-[90px] md:w-auto">
                <SearchInput
                  placeholder="Search projects..."
                  className="w-full lg:w-[160px]"
                />
              </div>

              <button
                type="button"
                ref={newProjectButtonRef}
                className="bg-blue-600 hover:bg-blue-700 whitespace-nowrap rounded-lg px-5 py-2 text-sm font-medium text-white shadow-sm transition-all duration-200 flex items-center gap-2"
                onClick={() => handleClickOpenDialog(false)}
              >
                New Project
              </button>
            </div>
          </div>

          {/* Mobile-only legend */}
          <div className="flex flex-wrap items-center justify-center gap-2 border-b border-gray-200 bg-white px-4 py-2 md:hidden">
            <div
              className={`flex cursor-pointer items-center gap-1.5 rounded px-2 py-1 transition-colors ${
                activeFilter === 'draft' ? 'bg-blue-100 ring-1 ring-blue-400' : 'hover:bg-gray-100'
              }`}
              onClick={() => toggleFilter('draft')}
            >
              <span className="h-2.5 w-2.5 rounded-full bg-blue-500" />
              <span className="text-xs text-gray-600">Draft</span>
            </div>
            <div
              className={`flex cursor-pointer items-center gap-1.5 rounded px-2 py-1 transition-colors ${
                activeFilter === 'pending approver'
                  ? 'bg-amber-100 ring-1 ring-amber-400'
                  : 'hover:bg-gray-100'
              }`}
              onClick={() => toggleFilter('pending approver')}
            >
              <span className="h-2.5 w-2.5 rounded-full bg-amber-500" />
              <span className="text-xs text-gray-600">Pending Approver</span>
            </div>
            <div
              className={`flex cursor-pointer items-center gap-1.5 rounded px-2 py-1 transition-colors ${
                activeFilter === 'completed'
                  ? 'bg-green-100 ring-1 ring-green-400'
                  : 'hover:bg-gray-100'
              }`}
              onClick={() => toggleFilter('completed')}
            >
              <span className="h-2.5 w-2.5 rounded-full bg-green-500" />
              <span className="text-xs text-gray-600">Completed</span>
            </div>
            {activeFilter && (
              <div
                className="flex cursor-pointer items-center gap-1.5 rounded px-2 py-1 hover:bg-gray-100"
                onClick={() => setActiveFilter('')}
              >
                <span className="text-xs font-medium text-blue-600">Clear filter</span>
              </div>
            )}
          </div>

          {/* Show active filter indicator if filtering */}
          {activeFilter && (
            <div className="flex items-center justify-between border-b border-blue-200 bg-blue-50 px-4 py-2 text-sm">
              <div className="flex items-center">
                <span className="text-blue-700">Filtered by:</span>
                <span
                  className={`ml-2 rounded-full px-2 py-0.5 text-xs font-medium ${
                    activeFilter === 'draft'
                      ? 'bg-blue-100 text-blue-800'
                      : activeFilter === 'pending approver'
                        ? 'bg-amber-100 text-amber-800'
                        : 'bg-green-100 text-green-800'
                  }`}
                >
                  {activeFilter.toUpperCase()}
                </span>
              </div>
              <button
                className="text-xs font-medium text-blue-600 hover:text-blue-800"
                onClick={() => setActiveFilter('')}
              >
                Clear Filter
              </button>
            </div>
          )}

          {/* Table section */}
          <div className="overflow-x-auto rounded-b-xl bg-white dark:bg-gray-800">
            {sortedTableData?.length > 0 ? (
              <div className="min-w-full overflow-x-auto">
                <table className="min-w-full table-fixed border-collapse">
                  <thead className="sticky top-0 z-10 bg-gray-50">
                    <tr className="border-b border-gray-200">
                      {[
                        {
                          label: 'PROJECT NAME',
                          width: '250px',
                          minWidth: '200px',
                          mobileHide: false,
                          isSticky: true,
                          key: 'name',
                        },
                        {
                          label: 'CREATED BY',
                          width: '120px',
                          minWidth: '120px',
                          mobileHide: true,
                          key: 'created_by_staff_id',
                        },
                        {
                          label: 'STATUS',
                          width: '150px',
                          minWidth: '120px',
                          mobileHide: false,
                          key: 'status',
                        },
                        {
                          label: 'CREATED AT',
                          width: '120px',
                          minWidth: '120px',
                          mobileHide: true,
                          key: 'created_at',
                        },
                        { label: '', width: '50px', mobileHide: false, key: null },
                      ].map((col, i) => (
                        <th
                          key={i}
                          scope="col"
                          className={`border-b-2 border-gray-300 px-3 py-3 text-left text-sm font-semibold uppercase text-gray-800 ${col.mobileHide ? 'hidden md:table-cell' : ''} ${col.isSticky ? 'sticky left-0 z-20 bg-gray-50' : ''}`}
                          style={{ width: col.width, minWidth: col.minWidth }}
                        >
                          <div className="flex items-center">
                            {col.label}
                            {col.key && (
                              <button
                                className="ml-1 text-gray-500 hover:text-gray-700"
                                onClick={() => requestSort(col.key)}
                              >
                                <svg
                                  className="h-3.5 w-3.5"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  {sortConfig.key === col.key ? (
                                    sortConfig.direction === 'asc' ? (
                                      // Up arrow for ascending sort
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                        d="M5 15l7-7 7 7"
                                      />
                                    ) : (
                                      // Down arrow for descending sort
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                        d="M19 9l-7 7-7-7"
                                      />
                                    )
                                  ) : (
                                    // Filter icon for unsorted
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="2"
                                      d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
                                    />
                                  )}
                                </svg>
                              </button>
                            )}
                          </div>
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {sortedTableData.map((row, i) => {
                      // Determine row background based on status
                      let rowBgClass = '';
                      let textColor = 'text-gray-900';

                      if (row?.status?.toLowerCase()?.includes('completed')) {
                        rowBgClass = 'bg-green-50';
                      } else if (row?.status?.toLowerCase()?.includes('pending approver')) {
                        rowBgClass = 'bg-amber-50';
                      } else if (row?.status?.toLowerCase()?.includes('draft')) {
                        rowBgClass = 'bg-blue-50/50';
                      } else {
                        rowBgClass = 'bg-white';
                      }

                      return (
                        <tr
                          key={row.id || i}
                          className={twMerge(
                            'cursor-pointer border-b border-gray-200 transition-colors duration-150 hover:bg-gray-100',
                            rowBgClass
                          )}
                          onClick={() => handleClickRow(row?.id)}
                        >
                          {/* Project Name - Sticky Column */}
                          <td
                            className={`px-3 py-2.5 text-sm font-medium ${textColor} sticky left-0 z-10 ${rowBgClass}`}
                          >
                            <div className="max-w-[180px] truncate sm:max-w-xs">
                              {checkAndReplaceStringWithHyphen(row?.name)}
                            </div>
                            {/* Mobile-only status and date display */}
                            <div className="mt-1 flex items-center gap-2 md:hidden">
                              <StatusBadge status={row?.status} />
                              <span className="text-xs text-gray-500">
                                {dayjs(row?.created_at)?.format('DD MMM YYYY')}
                              </span>
                            </div>
                          </td>
                          {/* Created By */}
                          <td className="hidden px-3 py-2.5 text-sm text-gray-600 md:table-cell">
                            <UserPopup
                              label={row?.created_by_staff_id?.toUpperCase()}
                              staff_id={row?.created_by_staff_id}
                            />
                          </td>
                          {/* Status */}
                          <td className="hidden px-3 py-2.5 text-sm md:table-cell">
                            <StatusBadge status={row?.status} />
                          </td>
                          {/* Created At */}
                          <td className="hidden px-3 py-2.5 text-sm text-gray-500 md:table-cell">
                            {checkAndReplaceStringWithHyphen(
                              dayjs(row?.created_at)?.format('DD MMM YYYY')
                            )}
                          </td>
                          {/* Edit Action */}
                          <td
                            className="px-3 py-2.5 text-center text-sm"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <div className="flex items-center justify-center space-x-2">
                              <button
                                type="button"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleClickOpenDialog(true, row);
                                }}
                                className="hover:text-primary text-gray-500 transition-colors"
                                aria-label="Edit project"
                              >
                                <Edit className="h-4 w-4" />
                              </button>
                              
                              {/* Only show delete button for admin users */}
                              {isAdmin && (
                                <button
                                  type="button"
                                  onClick={(e) => handleDeleteClick(e, row)}
                                  className="text-gray-500 transition-colors hover:text-red-500"
                                  aria-label="Delete project"
                                >
                                  <DeleteOutline className="h-4 w-4" />
                                </button>
                              )}
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center px-4 py-8 text-center sm:py-16">
                <div className="mb-3 text-gray-300 dark:text-gray-600">
                  <svg
                    className="mx-auto h-12 w-12 sm:h-16 sm:w-16"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="1.5"
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    ></path>
                  </svg>
                </div>
                <p className="text-base font-medium text-gray-700 dark:text-gray-300">
                  No financial analysis projects found
                </p>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Create a new project to get started
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
      <Dialog open={dialogOpen} onClose={() => handleDialogClose('cancel')}>
        <DialogTitle className="border-b border-slate-200 bg-white px-6 py-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-slate-900">
                {editModeDialog ? 'Edit' : 'Create New'} Financial Analysis Project
              </h3>
              <p className="text-sm text-slate-500">
                {editModeDialog ? 'Update project details and settings' : 'Create a new financial analysis project'}
              </p>
            </div>
          </div>
        </DialogTitle>
        <DialogContent className="px-6 py-6">
          <div className="space-y-6 w-full md:w-[500px]">
            
            <div className="space-y-2 pt-2">
              <label className="text-sm font-medium text-slate-700">Project Title</label>
              <TextInput
                name="name"
                value={dialogTitle}
                placeholder="e.g., 5G Network Expansion Phase 2 - Central Region"
                onChange={(e) => setDialogTitle(e.target.value)}
                className="fa-input"
              />
              {formErrors.name && (
                <p className="text-xs text-red-600 mt-1">{formErrors.name}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium text-slate-700">Project Value Category</label>
              <SelectInput
                name="total_capex_opex"
                value={dialogBudgetRange}
                placeholder="Select the estimated total project value"
                onChange={(e) => setDialogBudgetRange(e.target.value)}
                options={[
                  { value: 'below rm 100k', label: 'Under RM 100,000 - Small Scale Project' },
                  { value: 'between rm 100k and rm 500k', label: 'RM 100,000 - RM 500,000 - Medium Scale Project' },
                  { value: 'between rm 500k and rm 5mil', label: 'RM 500,000 - RM 5 Million - Large Scale Project' },
                  { value: 'above rm 5mil', label: 'Above RM 5 Million - Major Capital Investment' },
                ]}
                className="fa-input"
              />
              {formErrors.total_capex_opex && (
                <p className="text-xs text-red-600 mt-1">{formErrors.total_capex_opex}</p>
              )}
            </div>




          </div>
        </DialogContent>
        <DialogActions className="border-t border-slate-200 bg-slate-50 px-6 py-4">
          <div className="flex w-full justify-between">
            <div className="flex items-center space-x-3">
              <button
                type="button"
                onClick={() => handleDialogClose('cancel')}
                className="px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors duration-200"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => setDeleteConfirmOpen(true)}
                  className="px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors duration-200"
                  disabled={isSubmitting}
                >
                  Delete
                </button>
              )}
            </div>

            <div className="flex items-center space-x-3">
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Creating...' : 'Create Project'}
                </button>
              )}

              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Saving...' : 'Save Changes'}
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
      <ConfirmationDialog
        open={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Delete Project"
        message={
          <div className="pt-4">
            <p className="mb-4 text-gray-700 leading-relaxed">
              Are you sure you want to delete the project <strong>"{projectToDelete?.name}"</strong>?
            </p>
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start">
                <svg className="w-5 h-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <div>
                  <p className="text-sm text-red-800 font-medium mb-2">Warning: This action cannot be undone</p>
                  <p className="text-sm text-red-700 leading-relaxed">
                    All associated scenarios, financial data, and analysis will be permanently deleted.
                  </p>
                </div>
              </div>
            </div>
          </div>
        }
        confirmText="Delete Project"
        cancelText="Cancel"
        variant="danger"
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        }
      />
    </>
  );
};

export default AllProjects;
