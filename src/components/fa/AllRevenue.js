// Next, React, Tw
import { useEffect, useState, Fragment } from 'react';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import * as yup from 'yup';
import moment from 'moment';
import { twMerge } from 'tailwind-merge';

// Components
import RevenueTable from './revenue/RevenueTable';
import RevenueSummaryTable from './revenue/RevenueSummaryTable';
import RevenueSupportingDocuments from './revenue/RevenueSupportingDocuments';
import RevenueDialog from './revenue/RevenueDialog';
import ExportExcelButton from '../Shared/ExportExcelButton';
import UploadCsvButton from '../Shared/UploadCsv';
import ScreenshotDialog from '../Shared/ScreenshotDialog';

// Others
import { useSnackbar } from '../Shared/snackbar';
import { sanitizeInput } from '../../utils/sanitize';
import axios from '../../utils/axios';
import { FA_ENDPOINT } from '../../utils/fa';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { setBreadCrumbsList } from '../../utils/store/simiReducer';
import { METRIC_ENDPOINT } from '../../utils/metric';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { getDisplayMode, calculateContractYears } from '../../utils/faDisplayUtils';

const AllRevenue = ({ scenarioId: propScenarioId, showExportButton = true, onDataLoaded, exportMode = false }) => {
  // Standard
  const { enqueueSnackbar } = useSnackbar();
  const { query } = useRouter();
  const { projectId, scenarioId } = query;
  const dispatch = useDispatch();
  const { isAdmin } = useModuleRoleContext();
  const { user } = useAuthContext();
  const { projectData, scenarioData } = useSelector((state) => state.fa);
  const { setParam } = useParamContext();

  // Allow editing if user is admin OR if user created the project
  const canEdit = isAdmin || (projectData?.created_by_staff_id === user?.staff_id);
  const IS_DISABLED = !canEdit || projectData?.IS_DISABLED;
  const [revenueData, setRevenueData] = useState([]);
  const [allProducts, setAllProducts] = useState([]);
  const [fileList, setFileList] = useState([]);

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const [dialogData, setDialogData] = useState({});

  const handleDialogDataChange = (event) => {
    // Check if event has target property to handle both native events and custom events
    const name = event.target ? event.target.name : '';
    const value = event.target ? event.target.value : '';

    if (name) {
      setDialogData((prevValues) => ({
        ...prevValues,
        [name]: value,
      }));
    }
  };

  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    } else {
      // For new entries, initialize with the scenario's expected_rfs_date and calculate contract end date
      const rfsDate = scenarioData?.expected_rfs_date;
      let contractEndDate = '';

      if (rfsDate && scenarioData?.contract_period_in_month) {
        try {
          const startDate = moment(rfsDate);
          if (startDate.isValid()) {
            const contractMonths = parseInt(scenarioData.contract_period_in_month, 10) || 1;
            const endDate = startDate
              .clone()
              .add(contractMonths, 'month')
              .subtract(1, 'days');

            contractEndDate = endDate.format('YYYY-MM-DD');
          }
        } catch (error) {
          console.error('Error calculating contract end date:', error);
        }
      }

      // Set default values for new revenue entries
      setDialogData({
        rfs_date: rfsDate,
        contract_end_date: contractEndDate,
        currency: 'myr',
        type: 'Operating Revenue',
        type_price: 'published',
        bandwidth_unit: 'MBPS',
        bandwidth_value: 0,
        mrc_amount: 0,
        otc_amount: 0,
        otc_amortise: 0,
        payment_term_in_days: 30,
        usp_needed: false
      });
    }
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };

  const schema = yup.object({
    bandwidth_unit: yup.string().required('Please provide bandwidth unit.'),
    bandwidth_value: yup.number().required('Please provide bandwidth.'),
    contract_end_date: yup.string().required('Please provide contract end date.'),
    currency: yup.string().required('Please provide currency.'),
    type: yup.string().required('Please provide type of revenue.'),
    description: yup.string().required('Please provide description.'),
    leg_a: yup.string().required('Please provide Leg A.'),
    leg_b: yup.string().required('Please provide Leg B.'),
    mrc_amount: yup.number().required('Please provide MRC amount.'),
    otc_amount: yup.number().required('Please provide OTC amount.'),
    otc_amortise: yup.number().required('Please provide OTC Amortise amount.'),
    payment_term_in_days: yup.number().required('Please provide payment term.'),
    product: yup.string().required('Please provide product.'),
    project_id: yup.string().required('Please provide project ID.')?.default(projectId),
    revenue_summary_by_year_array: yup.array().of(yup.number()).default([]),
    rfs_date: yup.string().required('Please provide RFS date.'),
    scenario_id: yup.string().required('Please provide scenario ID.')?.default(scenarioId),
    sla_cos: yup.string().required('Please provide SLA/COS.'),
    type_price: yup.string().required('Please provide type of price.'),
    usp_needed: yup.boolean().required('Please provide USP needed.')?.default(false),
  });

  const handleDialogClose = async (action, payload) => {
    console.log('handleDialogClose called with action:', action);
    console.log('Payload received:', payload);

    if (action) {
      // Prevent multiple simultaneous operations
      if (handleDialogClose._isProcessing) {
        console.log('Operation already in progress, skipping');
        return;
      }
      handleDialogClose._isProcessing = true;
      // Special case for delete - no need for validation
      if (action === 'delete') {
        dispatch(setIsLoading(true));
        try {
          const response = await axios.delete(`${FA_ENDPOINT}/revenue/${payload?.id}`);
          let statusVariant = 'error';
          let message = 'Failed';
          if (response.data.status === 'success') {
            statusVariant = 'success';
            message = response.data.data;
          }
          enqueueSnackbar(message, { variant: statusVariant });
        } catch (error) {
          console.error('Error deleting revenue:', error);
          enqueueSnackbar('Failed to delete', { variant: 'error' });
        }
        dispatch(setIsLoading(false));
        setDialogData({});
        setDialogOpen(false);
        fetchData();
        handleDialogClose._isProcessing = false;
        return;
      }

      // For post/put actions, validate payload
      try {
        // Sanitize input fields to prevent XSS
        const sanitizedPayload = {
          ...payload,
          description: sanitizeInput(payload.description),
          leg_a: sanitizeInput(payload.leg_a),
          leg_b: sanitizeInput(payload.leg_b),
          product: sanitizeInput(payload.product),
          sla_cos: sanitizeInput(payload.sla_cos)
        };

        const validatedPayload = await schema.validate(sanitizedPayload, { abortEarly: false })
          .catch(error => {
            console.error('Validation errors:', error.errors);
            enqueueSnackbar(error.errors.join(', '), {
              variant: 'error',
            });
            throw error;
          });

        console.log('Validated payload:', validatedPayload);

        // Save original values before currency conversion
        const originalMrcAmount = validatedPayload.mrc_amount;
        const originalOtcAmount = validatedPayload.otc_amount;
        const originalOtcAmortise = validatedPayload.otc_amortise;

        // Standardize with project currency
        if (projectData?.currency !== validatedPayload?.currency) {
          if (projectData?.exchange_rate === 0) {
            enqueueSnackbar("Exchange rate can't be 0", {
              variant: 'error',
            });
            return;
          }
          if (projectData?.currency === 'myr') {
            validatedPayload.mrc_amount *= projectData?.exchange_rate;
            validatedPayload.otc_amount *= projectData?.exchange_rate;
            validatedPayload.otc_amortise *= projectData?.exchange_rate;
          } else {
            validatedPayload.mrc_amount /= projectData?.exchange_rate;
            validatedPayload.otc_amount /= projectData?.exchange_rate;
            validatedPayload.otc_amortise /= projectData?.exchange_rate;
          }
        }

        // Define contractMonths here so it's available throughout the function
        const contractMonths = parseInt(scenarioData?.contract_period_in_month, 10) || 120;

        // Calculate revenue arrays using the new methods that account for gap years
        const mrcArray = calculateMrcArray(validatedPayload);
        const otcArray = calculateOtcArray(validatedPayload);
        const otcAmortisedArray = calculateOtcAmortisedArray(validatedPayload);

        // Set the calculated arrays in the payload
        if (mrcArray.length > 0) {
          validatedPayload.revenue_mrc_summary_by_year_array = mrcArray;
        } else {
          // Fallback to default if calculation failed
          const totalYears = Math.ceil(contractMonths / 12);
          validatedPayload.revenue_mrc_summary_by_year_array = Array(totalYears).fill(0);
        }

        if (otcArray.length > 0) {
          validatedPayload.revenue_otc_summary_by_year_array = otcArray;
        } else {
          // Fallback to default if calculation failed
          const totalYears = Math.ceil(contractMonths / 12);
          validatedPayload.revenue_otc_summary_by_year_array = Array(totalYears).fill(0);
          if (validatedPayload.otc_amount) {
            // Put OTC in first year (without considering gap)
            validatedPayload.revenue_otc_summary_by_year_array[0] = parseFloat(validatedPayload.otc_amount);
          }
        }

        if (otcAmortisedArray.length > 0) {
          validatedPayload.revenue_otc_amortised_summary_by_year_array = otcAmortisedArray;
        } else {
          // Fallback to default if calculation failed
          const totalYears = Math.ceil(contractMonths / 12);
          validatedPayload.revenue_otc_amortised_summary_by_year_array = Array(totalYears).fill(0);

          // Distribute OTC amortised evenly (without considering gap)
          if (validatedPayload.otc_amortise) {
            const amountPerYear = parseFloat(validatedPayload.otc_amortise) / Math.ceil(contractMonths / 12);
            for (let i = 0; i < totalYears; i++) {
              validatedPayload.revenue_otc_amortised_summary_by_year_array[i] = amountPerYear;
            }
          }
        }

        console.log('Revenue arrays:', {
          mrc: validatedPayload.revenue_mrc_summary_by_year_array,
          otc: validatedPayload.revenue_otc_summary_by_year_array,
          amortised: validatedPayload.revenue_otc_amortised_summary_by_year_array
        });

        // Calculate total with null checks
        validatedPayload.total_contract_value =
          parseFloat(validatedPayload?.otc_amount || 0) +
          parseFloat(validatedPayload?.otc_amortise || 0) +
          parseFloat(validatedPayload?.mrc_amount || 0) * contractMonths;

        // Restore original values for display in UI
        validatedPayload.mrc_amount = originalMrcAmount;
        validatedPayload.otc_amount = originalOtcAmount;
        validatedPayload.otc_amortise = originalOtcAmortise;

        dispatch(setIsLoading(true));

        // Send request based on action type
        let response;
        if (action === 'post') {
          response = await axios.post(`${FA_ENDPOINT}/revenue`, validatedPayload);
        } else if (action === 'put') {
          response = await axios.put(`${FA_ENDPOINT}/revenue/${validatedPayload?.id}`, validatedPayload);
        }

        // Handle response
        let statusVariant = 'error';
        let message = 'Failed';
        if (response && response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        }
        enqueueSnackbar(message, { variant: statusVariant });
        dispatch(setIsLoading(false));
        setDialogData({});
        setDialogOpen(false);
        fetchData();

      } catch (error) {
        handleDialogClose._isProcessing = false;
        console.error('Error processing revenue:', error);
        if (!error.errors) { // Not a validation error (already handled above)
          enqueueSnackbar('Error calculating revenue values', {
            variant: 'error',
          });
        }
        dispatch(setIsLoading(false));
      } finally {
        handleDialogClose._isProcessing = false;
      }
    } else {
      // Just close the dialog without doing anything
      setDialogData({});
      setDialogOpen(false);
    }
  };

  // Utility functions for revenue calculations

  // Calculate the year difference between project start date and RFS date
  function calculateYearDifference() {
    if (!scenarioData?.project_start_date || !scenarioData?.expected_rfs_date) {
      return 0;
    }

    const startYear = moment(scenarioData.project_start_date).year();
    const rfsYear = moment(scenarioData.expected_rfs_date).year();
    return Math.max(0, rfsYear - startYear);
  }

  // Helper function to calculate months in each year for proper proration
  function calculateMonthsInYear(yearIndex, rfsDate, contractMonths, yearDifference) {
    const rfsDateMoment = moment(rfsDate);
    const rfsYear = rfsDateMoment.year();

    // Calculate which calendar year this yearIndex represents
    const calendarYear = rfsYear + (yearIndex - yearDifference);

    // If this is a gap year (before contract starts), return 0
    if (yearIndex < yearDifference) {
      return 0;
    }

    // Calculate contract start and end dates
    const contractStartDate = rfsDateMoment.clone();
    const contractEndDate = contractStartDate.clone().add(contractMonths, 'months').subtract(1, 'day');

    // Calculate year boundaries
    const yearStart = moment(`${calendarYear}-01-01`);
    const yearEnd = moment(`${calendarYear}-12-31`);

    // Find the overlap between contract period and this calendar year
    const overlapStart = moment.max(contractStartDate, yearStart);
    const overlapEnd = moment.min(contractEndDate, yearEnd);

    // If no overlap, return 0
    if (overlapStart.isAfter(overlapEnd)) {
      return 0;
    }

    // Count complete months in the overlap period
    let monthsInThisYear = 0;
    let current = overlapStart.clone().startOf('month');
    
    while (current.isSameOrBefore(overlapEnd, 'month')) {
      monthsInThisYear++;
      current.add(1, 'month');
    }

    return monthsInThisYear;
  }

  // Calculate MRC array with gap years and proper proration
  function calculateMrcArray(data) {
    if (!data?.rfs_date || !scenarioData?.contract_period_in_month) {
      return [];
    }

    const monthlyAmount = parseFloat(data?.mrc_amount || 0);
    const contractMonths = parseInt(scenarioData.contract_period_in_month, 10) || 120;
    
    // Calculate contract years based on actual calendar years spanned
    const contractYears = calculateContractYears(data?.rfs_date, contractMonths);

    // Calculate year gap between project start and RFS date
    const yearDifference = calculateYearDifference();

    // Total years = gap years + contract years
    const totalYears = yearDifference + contractYears;

    // Create array with zeros for gap years, then fill with prorated MRC amounts
    const result = Array(totalYears).fill(0);

    // Fill contract years with prorated MRC amounts based on actual months in each year
    for (let i = 0; i < totalYears; i++) {
      if (i < yearDifference) {
        result[i] = 0; // Gap years
      } else {
        const monthsInThisYear = calculateMonthsInYear(i, data.rfs_date, contractMonths, yearDifference);
        result[i] = monthlyAmount * monthsInThisYear;
      }
    }

    return result;
  }

  // Calculate OTC Amortised array with gap years and proper proration
  function calculateOtcAmortisedArray(data) {
    if (!data?.rfs_date || !scenarioData?.contract_period_in_month) {
      console.log('Missing required data for OTC Amortised calculation');
      return [];
    }

    const totalAmount = parseFloat(data?.otc_amortise || 0);
    const contractMonths = parseInt(scenarioData.contract_period_in_month, 10) || 120;
    
    // Calculate contract years based on actual calendar years spanned
    const contractYears = calculateContractYears(data?.rfs_date, contractMonths);

    // Calculate year gap between project start and RFS date
    const yearDifference = calculateYearDifference();

    // Total years = gap years + contract years
    const totalYears = yearDifference + contractYears;

    // Create array with zeros for gap years
    const result = Array(totalYears).fill(0);

    // Calculate monthly amortization amount
    const monthlyAmortization = totalAmount / contractMonths;

    // Fill contract years with prorated amortized amounts based on actual months in each year
    for (let i = 0; i < totalYears; i++) {
      if (i < yearDifference) {
        result[i] = 0; // Gap years
      } else {
        const monthsInThisYear = calculateMonthsInYear(i, data.rfs_date, contractMonths, yearDifference);
        result[i] = monthlyAmortization * monthsInThisYear;
      }
    }

    console.log('OTC Amortised calculation result (with proration):', {
      totalAmount,
      contractMonths,
      contractYears,
      yearDifference,
      totalYears,
      monthlyAmortization,
      result,
      totalCalculated: result.reduce((sum, val) => sum + val, 0),
      expectedTotal: totalAmount
    });

    return result;
  }

  // Calculate OTC One-off array with gap years
  function calculateOtcArray(data) {
    if (!data?.rfs_date || !scenarioData?.contract_period_in_month) {
      console.log('Missing required data for OTC calculation');
      return [];
    }

    const otcAmount = parseFloat(data?.otc_amount || 0);
    const contractMonths = parseInt(scenarioData.contract_period_in_month, 10) || 120;
    
    // Calculate contract years based on actual calendar years spanned
    const contractYears = calculateContractYears(data?.rfs_date, contractMonths);

    // Calculate year gap between project start and RFS date
    const yearDifference = calculateYearDifference();

    // Total years = gap years + contract years
    const totalYears = yearDifference + contractYears;

    // Create array with zeros
    const result = Array(totalYears).fill(0);

    // Put OTC amount in the first year after the gap
    if (yearDifference < totalYears) {
      result[yearDifference] = otcAmount;
    }

    console.log('OTC array calculation:', {
      otcAmount,
      contractMonths,
      contractYears,
      yearDifference,
      totalYears,
      result
    });

    return result;
  }

  // Legacy function kept for backward compatibility
  function getMetrix(data, isMrc = true) {
    console.log('getMetrix input:', {
      data,
      isMrc,
      scenarioData: scenarioData,
      contract_period_in_month: scenarioData?.contract_period_in_month
    });

    if (!data?.rfs_date || !scenarioData?.contract_period_in_month) {
      console.log('Missing required data:', {
        rfs_date: data?.rfs_date,
        contract_period_in_month: scenarioData?.contract_period_in_month
      });
      return [];
    }

    const getContractPeriodInYearsArray = (date) => {
      const monthsInYear = 12;
      const contractMonths = parseInt(scenarioData.contract_period_in_month, 10);
      const momentDate = moment(date);
      const baseYears = Math.floor(contractMonths / monthsInYear);

      console.log('Contract period calculation:', {
        date,
        monthsInYear,
        contractMonths,
        momentDate: momentDate.format('YYYY-MM-DD'),
        baseYears
      });

      // Add extra year if not starting at beginning of year
      const needsExtraYear = !(momentDate.date() === 1 && momentDate.month() === 0);
      const totalYears = needsExtraYear ? baseYears + 1 : baseYears;

      console.log('Years calculation:', {
        needsExtraYear,
        totalYears
      });

      return Array.from({ length: totalYears || 1 }, (_, i) => i);
    };

    const startDate = moment(data.rfs_date);
    if (!startDate.isValid()) {
      console.log('Invalid start date', data.rfs_date);
      return [];
    }

    const contractMonths = parseInt(scenarioData.contract_period_in_month, 10) || 1;
    const endDate = startDate
      .clone()
      .add(contractMonths, 'month')
      .subtract(1, 'days');

    // Calculate month properties for revenue calculation
    const firstMonthNo = startDate.month();
    const lastMonthNo = endDate.month();
    const firstMonthNoOfDays = startDate.daysInMonth() - startDate.date() + 1;
    const lastMonthNoOfDays = endDate.date();

    console.log('Date calculations:', {
      startDate: startDate.format('YYYY-MM-DD'),
      endDate: endDate.format('YYYY-MM-DD'),
      firstMonthNo,
      lastMonthNo,
      firstMonthNoOfDays,
      lastMonthNoOfDays
    });

    // Calculate contract period and monthly amount
    const safeContractPeriod = Math.max(contractMonths || 1, 1);

    // Calculate monthly amount based on type
    const monthlyAmount = isMrc
      ? parseFloat(data?.mrc_amount || 0)
      : parseFloat(data?.otc_amortise || 0) / safeContractPeriod;

    console.log('Amount calculations:', {
      safeContractPeriod,
      monthlyAmount,
      otcAmortise: parseFloat(data?.otc_amortise || 0),
      mrcAmount: parseFloat(data?.mrc_amount || 0)
    });

    const yearsArray = getContractPeriodInYearsArray(data.rfs_date);

    // Return zeroed array if no valid years
    if (!yearsArray.length) {
      return [[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]];
    }

    return yearsArray.map((unused, yearIndex) => {
      return Array(12)
        .fill(0)
        .map((unused2, monthIndex) => {
          // Calculate days in month based on position
          let monthDays;
          let daysInMonth;

          try {
            // First month special case
            if (monthIndex === firstMonthNo && yearIndex === 0) {
              monthDays = firstMonthNoOfDays;
              daysInMonth = startDate.daysInMonth();
            }
            // Last month special case
            else if (monthIndex === lastMonthNo && yearIndex === yearsArray.length - 1) {
              monthDays = lastMonthNoOfDays;
              daysInMonth = endDate.daysInMonth();
            }
            // Regular months
            else {
              const currentMonth = moment(startDate).add(yearIndex * 12 + monthIndex - firstMonthNo, 'months');
              monthDays = currentMonth.daysInMonth();
              daysInMonth = monthDays;
            }

            // Skip months before contract starts
            if (yearIndex === 0 && monthIndex < firstMonthNo) {
              return 0;
            }

            // Skip months after contract ends
            if (yearIndex === yearsArray.length - 1 && monthIndex > lastMonthNo) {
              return 0;
            }

            // Ensure we don't divide by zero
            if (daysInMonth === 0) {
              return 0;
            }

            // Calculate revenue for valid months
            return (monthlyAmount * monthDays) / daysInMonth;
          } catch (error) {
            console.error('Error in month calculation', error);
            return 0;
          }
        });
    });
  }

  // Data fetching
  const fetchProducts = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${METRIC_ENDPOINT}/product/all/keyword`);
      if (response?.data?.data) {
        setAllProducts(response?.data?.data?.map((o) => o?.product_name));
      }
    } catch {
      setAllProducts([]);
    }
    dispatch(setIsLoading(false));
  };

  const fetchFileList = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${FA_ENDPOINT}/file/revenue/${scenarioId}`);
      setFileList(response?.data?.data || []);
    } catch (error) {
      console.error('Error fetching file list:', error);
      setFileList([]);
    }
    dispatch(setIsLoading(false));
  };

  const fetchData = async () => {
    // Prevent concurrent API calls
    if (fetchData._isLoading) {
      return;
    }
    fetchData._isLoading = true;
    
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${FA_ENDPOINT}/revenue/scenario_id/${scenarioId}`);
      if (response?.data?.data) {
        const processedData = response?.data?.data?.map((o) => ({
          ...o,
          revenue_summary_total: o?.revenue_summary_by_year_array?.reduce(
            (prev, curr) => prev + curr,
            0
          ),
        }));
        setRevenueData(processedData);
        
        // Safely call onDataLoaded callback
        if (onDataLoaded && typeof onDataLoaded === 'function') {
          try {
            onDataLoaded(processedData);
          } catch (callbackError) {
            console.error('Error in onDataLoaded callback:', callbackError);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching revenue data:', error);
      setRevenueData([]);
    } finally {
      dispatch(setIsLoading(false));
      fetchData._isLoading = false;
    }
  };

  useEffect(() => {
    if (propScenarioId || scenarioId) {
      fetchProducts();
      fetchData();
      fetchFileList();
    }
  }, [scenarioId, propScenarioId]);

  // Remove this effect as onDataLoaded is now called directly in fetchData
  // to prevent race conditions with stale data

  useEffect(() => {
    dispatch(
      setBreadCrumbsList([
        {
          linkTo: '/fa',
          label: 'FA List',
        },
        {
          linkTo: `/fa/project/${projectId}`,
          label: projectData?.name,
        },
        {
          linkTo: `/fa/project/${projectId}?scenarioId=${scenarioId}&tab=capex`,
          label: scenarioData?.name,
        },
        {
          linkTo: `/fa/project/${projectId}?scenarioId=${scenarioId}&tab=revenue`,
          label: 'REVENUE',
        },
      ])
    );
  }, [projectId, projectData?.name, scenarioData?.name, scenarioId]);

  return (
    <>

      <div className="flex items-center justify-between mb-6">
        <p className="text-lg font-bold">Revenue</p>
        {!IS_DISABLED && (
          <div className="flex items-center gap-3">
            <ExportExcelButton
              data={[{
                bandwidth_unit: 'string',
                bandwidth_value: 0,
                currency: 'myr',
                contract_end_date: '2012-12-21',
                description: 'string',
                leg_a: 'string',
                leg_b: 'string',
                mrc_amount: 0,
                otc_amortise: 0,
                otc_amount: 0,
                payment_term_in_days: 0,
                product: 'string',
                rfs_date: '2012-12-21',
                sla_cos: 'string',
                total_contract_value: 0,
                type: 'string',
                type_price: 'string',
              }]}
              filename="revenue_template.csv"
              buttonClassname="flex items-center gap-1.5 rounded-lg border border-gray-300 bg-white/90 px-3 py-1.5 text-xs font-medium text-gray-600 shadow-sm backdrop-blur-sm transition-all duration-200 hover:bg-white"
            >
              <svg className="h-3.5 w-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              Template
            </ExportExcelButton>

            <UploadCsvButton
              onUpload={async (data) => {
                await Promise.all(data?.map((item) => handleDialogClose('post', item)));
              }}
            >
              <button
                type="button"
                className="flex items-center gap-1.5 whitespace-nowrap rounded-lg bg-gray-200 px-3 py-1.5 text-xs font-medium text-gray-700 shadow-sm transition-all duration-200 hover:bg-gray-300"
              >
                <svg className="h-3.5 w-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                </svg>
                Upload CSV
              </button>
            </UploadCsvButton>
            <button
              type="button"
              className="bg-primary hover:bg-primary/90 flex items-center gap-1.5 whitespace-nowrap rounded-lg px-3 py-1.5 text-xs font-medium text-white shadow-sm transition-all duration-200"
              onClick={() => handleClickOpenDialog(false)}
            >
              <svg className="h-3.5 w-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4"></path>
              </svg>
              Add
            </button>
          </div>
        )}
      </div>

      <RevenueTable
        revenueData={revenueData}
        onRowClick={(row) => handleClickOpenDialog(true, row)}
        IS_DISABLED={IS_DISABLED}
      />

      <RevenueSummaryTable
        revenueData={revenueData}
        scenarioData={scenarioData}
      />

      <RevenueSupportingDocuments
        fileList={fileList}
        IS_DISABLED={IS_DISABLED}
        scenarioId={scenarioId}
        onFileListChange={fetchFileList}
      />

      <RevenueDialog
        open={dialogOpen}
        onClose={() => handleDialogClose(false)}
        editMode={editModeDialog}
        data={dialogData}
        onChange={handleDialogDataChange}
        onSubmit={handleDialogClose}
        allProducts={allProducts}
        scenarioData={scenarioData}
      />

      <ScreenshotDialog
        fileName={`REVENUE-${projectData?.name}-${scenarioData?.name}.pdf`}
        isLandscape
      >
        <div className="flex w-full flex-col gap-4 p-4">
          {/* Revenue Details Table */}
          <div className="bg-white w-full text-left text-gray-800 py-4 px-0 font-bold text-xl tracking-tight border-b border-gray-100">REVENUE DETAILS</div>
          <RevenueTable
            revenueData={revenueData}
            onRowClick={() => {}}
            IS_DISABLED={true}
          />

          {/* Revenue Summary Table */}
          {(() => {
            const { totalPeriods } = getDisplayMode(scenarioData);
            
            return (
              <>
                <div className="bg-white w-full text-left text-gray-800 py-4 px-0 font-bold text-xl tracking-tight border-b border-gray-100 mt-8">REVENUE SUMMARY</div>
          <div className="overflow-x-auto scrollbar">
            <table className="min-w-full bg-white border border-gray-100 rounded-lg overflow-hidden">
              <tbody>
                <tr>
                  <td className="whitespace-nowrap bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700 border-b border-gray-200 text-center">Description</td>
                  <td className="whitespace-nowrap bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700 border-b border-gray-200 text-center">Details</td>
                  {/* Use the first row's data to determine number of years, fallback to 11 years (Y0-Y10) */}
                  {Array.from({ length: totalPeriods }, (_, i) => (
                    <td key={i} className="whitespace-nowrap bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700 border-b border-gray-200">{`Y${i}`}</td>
                  ))}
                  <td className="whitespace-nowrap bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700 border-b border-gray-200">Total ({projectData?.currency?.toUpperCase()})</td>
                </tr>

                {revenueData.map((o, i) => (
                  <Fragment key={i}>
                    {/* OTC One Off Row */}
                    <tr>
                      <td className="text-center py-3 text-sm border-b border-gray-100 px-4 text-gray-700" rowSpan={3}>
                        {sanitizeInput(o?.description) || '-'}
                      </td>
                      <td className="text-center py-3 text-sm border-b border-gray-100 px-4 text-gray-700">OTC One Off</td>
                      {(o?.revenue_otc_summary_by_year_array || []).map((p, j) => (
                        <td key={j} className="text-right py-3 text-sm border-b border-gray-100 px-4 font-mono text-gray-700">
                          {Math.round(p || 0).toLocaleString()}
                        </td>
                      ))}
                      <td className={twMerge("text-right py-3 text-sm border-b border-blue-200 px-4 font-mono", 'bg-blue-50 font-bold text-blue-900')}>
                        {Math.round(
                          (o?.revenue_otc_summary_by_year_array || []).reduce(
                            (sum, value) => sum + (value || 0), 0
                          )
                        ).toLocaleString()}
                      </td>
                    </tr>

                    {/* OTC Amortised Row */}
                    <tr>
                      <td className="text-center py-3 text-sm border-b border-gray-100 px-4 text-gray-700">OTC Amortised</td>
                      {(o?.revenue_otc_amortised_summary_by_year_array || []).map((p, j) => (
                        <td key={j} className="text-right py-3 text-sm border-b border-gray-100 px-4 font-mono text-gray-700">
                          {Math.round(p || 0).toLocaleString()}
                        </td>
                      ))}
                      <td className={twMerge("text-right py-3 text-sm border-b border-blue-200 px-4 font-mono", 'bg-blue-50 font-bold text-blue-900')}>
                        {Math.round(
                          (o?.revenue_otc_amortised_summary_by_year_array || []).reduce(
                            (sum, value) => sum + (value || 0), 0
                          )
                        ).toLocaleString()}
                      </td>
                    </tr>

                    {/* ARC Row */}
                    <tr>
                      <td className="text-center py-3 text-sm border-b border-gray-100 px-4 text-gray-700">ARC</td>
                      {(o?.revenue_mrc_summary_by_year_array || []).map((p, j) => (
                        <td key={j} className="text-right py-3 text-sm border-b border-gray-100 px-4 font-mono text-gray-700">
                          {Math.round(p || 0).toLocaleString()}
                        </td>
                      ))}
                      <td className={twMerge("text-right py-3 text-sm border-b border-blue-200 px-4 font-mono", 'bg-blue-50 font-bold text-blue-900')}>
                        {Math.round(
                          (o?.revenue_mrc_summary_by_year_array || []).reduce(
                            (sum, value) => sum + (value || 0), 0
                          )
                        ).toLocaleString()}
                      </td>
                    </tr>

                    <tr className="bg-gray-50">
                      <td colSpan={2} className={twMerge("text-center py-3 text-sm border-b border-gray-200 px-4", 'text-center font-semibold text-gray-800')}>
                        Total
                      </td>
                      {(o?.revenue_mrc_summary_by_year_array || []).map((_, yearIndex) => {
                        const otcValue = o?.revenue_otc_summary_by_year_array?.[yearIndex] || 0;
                        const amortisedValue = o?.revenue_otc_amortised_summary_by_year_array?.[yearIndex] || 0;
                        const mrcValue = o?.revenue_mrc_summary_by_year_array?.[yearIndex] || 0;
                        const yearTotal = otcValue + amortisedValue + mrcValue;

                        return (
                          <td key={yearIndex} className={twMerge("text-right py-3 text-sm border-b border-gray-200 px-4 font-mono", 'font-semibold text-gray-800')}>
                            {Math.round(yearTotal).toLocaleString()}
                          </td>
                        );
                      })}
                      <td className={twMerge("text-right py-3 text-sm border-b border-blue-200 px-4 font-mono", 'bg-blue-50 font-bold text-blue-900')}>
                        {Math.round(
                          (o?.revenue_otc_summary_by_year_array || []).reduce((sum, val) => sum + (val || 0), 0) +
                          (o?.revenue_otc_amortised_summary_by_year_array || []).reduce((sum, val) => sum + (val || 0), 0) +
                          (o?.revenue_mrc_summary_by_year_array || []).reduce((sum, val) => sum + (val || 0), 0)
                        ).toLocaleString()}
                      </td>
                    </tr>
                  </Fragment>
                ))}

                <tr className="bg-blue-50">
                  <td colSpan={2} className={twMerge("text-center py-3 text-sm border-b border-blue-200 px-4", 'font-bold text-blue-900')}>
                    Grand Total
                  </td>
                  {(revenueData[0]?.revenue_mrc_summary_by_year_array || []).map((_, yearIndex) => {
                    const yearTotal = revenueData.reduce((prev, curr) => {
                      const mrc = curr?.revenue_mrc_summary_by_year_array?.[yearIndex] || 0;
                      const otc = curr?.revenue_otc_summary_by_year_array?.[yearIndex] || 0;
                      const amortised = curr?.revenue_otc_amortised_summary_by_year_array?.[yearIndex] || 0;
                      return prev + mrc + otc + amortised;
                    }, 0);

                    return (
                      <td key={yearIndex} className={twMerge("text-right py-3 text-sm border-b border-blue-200 px-4 font-mono", 'font-bold text-blue-900')}>
                        {Math.round(yearTotal).toLocaleString()}
                      </td>
                    );
                  })}
                  <td className={twMerge("text-right py-3 text-sm border-b border-blue-200 px-4 font-mono", 'font-bold text-blue-900')}>
                    {Math.round(
                      revenueData.reduce((prev, curr) => {
                        const mrc = (curr?.revenue_mrc_summary_by_year_array || []).reduce((sum, val) => sum + (val || 0), 0);
                        const otc = (curr?.revenue_otc_summary_by_year_array || []).reduce((sum, val) => sum + (val || 0), 0);
                        const amortised = (curr?.revenue_otc_amortised_summary_by_year_array || []).reduce((sum, val) => sum + (val || 0), 0);
                        return prev + mrc + otc + amortised;
                      }, 0)
                    ).toLocaleString()}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
              </>
            );
          })()}

          {/* Supporting Documents Table */}
          <div className="bg-white w-full text-left text-gray-800 py-4 px-0 font-bold text-xl tracking-tight border-b border-gray-100 mt-8">SUPPORTING DOCUMENTS</div>
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white border border-gray-100 rounded-lg overflow-hidden">
              <thead>
                <tr>
                  <td className="whitespace-nowrap bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700 border-b border-gray-200 text-left">
                    File Name
                  </td>
                </tr>
              </thead>
              <tbody>
                {fileList.length > 0 ? (
                  fileList.map((file, index) => (
                    <tr key={index} className="hover:bg-gray-50 transition-colors duration-200">
                      <td className="text-left py-3 text-sm border-b border-gray-100 px-4 text-gray-700">
                        {file.file_name}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td className="text-center py-3 text-sm border-b border-gray-100 px-4 text-gray-700">
                      No documents have been uploaded
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </ScreenshotDialog>
    </>
  );
};

AllRevenue.propTypes = {
  scenarioId: PropTypes.string,
  showExportButton: PropTypes.bool,
  onDataLoaded: PropTypes.func,
  exportMode: PropTypes.bool,
};

export default AllRevenue;
