        </div>
        
        {/* Fully Integrated Attachments Section */}
        <div className="flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <p className="text-lg font-bold">Expenses Supporting Documents</p>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white text-black">
              <thead>
                <tr>
                  <th className="bg-fa border border-white px-4 py-2 text-left text-xs font-medium text-white">
                    Supporting Documents
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td className="p-2">
                    <AttachmentBox
                      key={scenarioId}
                      TITLE="Expenses Supporting Documents"
                      GET_ALL_FILES_ENDPOINT={`${FA_ENDPOINT}/file/expense/${scenarioId}`}
                      UPLOAD_CERTAIN_FILE_ENDPOINT={`${FA_ENDPOINT}/file/upload/expense/${scenarioId}`}
                      DOWNLOAD_CERTAIN_FILE_ENDPOINT={`${FA_ENDPOINT}/file/download`}
                      DELETE_CERTAIN_FILE_ENDPOINT={`${FA_ENDPOINT}/file`}
                      DISABLE_UPLOAD={IS_DISABLED}
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div> 