import { useState } from 'react';
import { Dialog, DialogTitle } from '@mui/material';
import { useDispatch } from 'react-redux';
import { twMerge } from 'tailwind-merge';
import { useSnackbar } from '../../../components/Shared/snackbar';
import axios from '../../../utils/axios';
import { FA_ENDPOINT } from '../../../utils/fa';
import { setIsLoading } from '../../../utils/store/loadingReducer';

const RevenueSupportingDocuments = ({ fileList, IS_DISABLED, scenarioId, onFileListChange }) => {
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [focusedFile, setFocusedFile] = useState({});

  const downloadFile = async (file) => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${FA_ENDPOINT}/file/download/${file?.id}`, {
        responseType: 'blob',
      });
      
      const link = document.createElement('a');
      link.href = URL.createObjectURL(response?.data);
      link.download = file?.file_name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
    } catch (error) {
      console.error('Error downloading file:', error);
      enqueueSnackbar('Something went wrong', { variant: 'error' });
    }
    dispatch(setIsLoading(false));
  };

  const handleFileUpload = async (event) => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.post(
        `${FA_ENDPOINT}/file/upload/revenue/${scenarioId}`,
        { file: event.target.files[0] },
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      
      let statusVariant = 'error';
      let message = 'Failed';
      if (response.data.status === 'success' || response?.status === 201) {
        statusVariant = 'success';
        message = 'Success';
        onFileListChange();
      }
      
      enqueueSnackbar(message, {
        variant: statusVariant,
      });
    } catch {
      enqueueSnackbar('Something went wrong', {
        variant: 'error',
      });
    }
    dispatch(setIsLoading(false));
    event.target.value = null;
  };

  const handleDeleteFile = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.delete(`${FA_ENDPOINT}/file/${focusedFile?.id}`);
      
      let statusVariant = 'error';
      let message = 'Failed';
      if (response.data.status === 'success' || response?.status === 204) {
        statusVariant = 'success';
        message = 'Success';
        onFileListChange();
      }
      
      enqueueSnackbar(message, {
        variant: statusVariant,
      });
    } catch (error) {
      console.error('Error deleting file:', error);
      enqueueSnackbar('Something went wrong', { variant: 'error' });
    }
    setDeleteDialogOpen(false);
    dispatch(setIsLoading(false));
  };

  // Modern, minimalist, Notion-like styling
  const headerCellStyle = 'whitespace-nowrap bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700 border-b border-gray-200';
  const bodyCellStyle = 'text-center py-3 text-sm border-b border-gray-100 px-4 text-gray-700';

  return (
    <div className="flex flex-col gap-2 mt-10">
      <div className="flex items-center justify-between">
        <p className="text-lg font-bold">Supporting Documents</p>
        {!IS_DISABLED && (
          <button
            type="button"
            className="flex items-center gap-1.5 whitespace-nowrap rounded-lg bg-gray-200 px-3 py-1.5 text-xs font-medium text-gray-700 shadow-sm transition-all duration-200 hover:bg-gray-300"
            onClick={() => document.getElementById('revenue-file-upload').click()}
          >
            <svg className="h-3.5 w-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
            </svg>
            Upload Document
            <input
              id="revenue-file-upload"
              type="file"
              className="hidden"
              onChange={handleFileUpload}
            />
          </button>
        )}
      </div>
      
      <table className="min-w-full bg-white border border-gray-100 rounded-lg overflow-hidden">
        <thead>
          <tr>
            <td className={twMerge(headerCellStyle, 'w-[70%] text-left')}>
              File Name
            </td>
            <td className={headerCellStyle}>
              Actions
            </td>
          </tr>
        </thead>
        <tbody>
          {fileList.length > 0 ? (
            fileList.map((file, index) => {
              return (
                <tr key={index} className="hover:bg-gray-50 transition-colors duration-200">
                  <td className={twMerge(bodyCellStyle, 'text-left')}>{file.file_name}</td>
                  <td className={bodyCellStyle}>
                    <div className="flex justify-center space-x-2">
                      <button
                        type="button"
                        className="rounded-md bg-blue-50 p-1.5 text-blue-500 hover:bg-blue-100"
                        onClick={() => downloadFile(file)}
                      >
                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                      </button>
                      {!IS_DISABLED && (
                        <button
                          type="button"
                          className="rounded-md bg-red-50 p-1.5 text-red-500 hover:bg-red-100"
                          onClick={() => {
                            setFocusedFile(file);
                            setDeleteDialogOpen(true);
                          }}
                        >
                          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                          </svg>
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              );
            })
          ) : (
            <tr>
              <td colSpan="2" className={twMerge(bodyCellStyle, 'text-center text-gray-500 italic')}>
                No documents have been uploaded
              </td>
            </tr>
          )}
        </tbody>
      </table>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle className="bg-fa text-center text-white">
          Delete Document?
        </DialogTitle>
        <div className="flex flex-col gap-4 p-4">
          <p className="text-sm">Are you sure you want to delete "{focusedFile?.file_name || focusedFile?.name || 'this file'}"?</p>
        </div>
        <div className="flex justify-between gap-4 p-4">
          <button type="button" onClick={() => setDeleteDialogOpen(false)} className="p-2">
            Cancel
          </button>
          <button 
            type="button" 
            onClick={handleDeleteFile} 
            className="cta-btn bg-red-500"
          >
            Delete
          </button>
        </div>
      </Dialog>
    </div>
  );
};

export default RevenueSupportingDocuments; 