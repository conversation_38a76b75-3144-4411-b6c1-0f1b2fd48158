import { Fragment } from 'react';
import { twMerge } from 'tailwind-merge';
import { getDisplayMode, generatePeriodLabels } from '../../../utils/faDisplayUtils';

// Safe number check function
const safeNumber = (value) => {
  if (value === null || value === undefined || Number.isNaN(value)) {
    return 0;
  }
  return Number(value);
};

// Safe array sum function
const safeArraySum = (array) => {
  if (!Array.isArray(array) || array.length === 0) {
    return 0;
  }
  return array.reduce((sum, value) => sum + safeNumber(value), 0);
};

// Utility to format numbers with thousand separators and 0 decimals
const formatNumber = (num) => {
  if (isNaN(num)) return '-';
  return Number(num).toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 0 });
};

const RevenueSummaryTable = ({ revenueData, scenarioData }) => {
  // Modern, minimalist, Notion-like styling
  const headerCellStyle = 'whitespace-nowrap bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700 border-b border-gray-200';
  const bodyCellStyle = 'text-center py-3 text-sm border-b border-gray-100 px-4 text-gray-700';
  const numberCellStyle = twMerge(bodyCellStyle, 'text-right font-mono');
  const numberCellStyleBold = twMerge(bodyCellStyle, 'text-right font-bold text-gray-800');
  const numberCellStyleTotal = twMerge(bodyCellStyle, 'bg-blue-50 text-right font-bold text-blue-900 border-b border-blue-200');

  // Ensure revenueData is an array
  const safeRevenueData = Array.isArray(revenueData) ? revenueData : [];

  // Always use yearly display mode
  const { isMonthlyDisplay, totalPeriods, contractMonths, startGapMonths } = getDisplayMode(scenarioData);

  // Calculate actual periods needed based on data
  const actualPeriods = safeRevenueData.length > 0 ? Math.max(
    totalPeriods,
    ...safeRevenueData.map(o => Math.max(
      (o?.revenue_otc_summary_by_year_array || []).length,
      (o?.revenue_otc_amortised_summary_by_year_array || []).length,
      (o?.revenue_mrc_summary_by_year_array || []).length
    ))
  ) : totalPeriods;

  // Generate period labels (always Y0, Y1, etc.)
  const periodLabels = generatePeriodLabels(false, actualPeriods);

  return (
    <div className="flex flex-col gap-2 mt-8">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <p className="text-lg font-bold">Revenue - Summary</p>
          <div className="group relative">
            <svg className="w-4 h-4 text-gray-400 hover:text-gray-600 cursor-help" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
            </svg>
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
              Shows revenue distributed across calendar years using daily proration.<br/>
              Total may differ from contract value due to calendar year boundaries.
            </div>
          </div>
        </div>
      </div>
      <div className="overflow-x-auto scrollbar">
        <table className="min-w-full bg-white border border-gray-100 rounded-lg overflow-hidden">
          <thead>
            <tr>
              <td className={twMerge(headerCellStyle, 'text-center')}>Description</td>
              <td className={twMerge(headerCellStyle, 'text-center')}>Details</td>
              {/* Use period labels based on display mode */}
              {periodLabels.map((label, i) => (
                <td key={i} className={twMerge(headerCellStyle, 'text-right')}>{label}</td>
              ))}
              <td className={twMerge(headerCellStyle, 'text-right')}>Total</td>
            </tr>
          </thead>
          <tbody>

            {safeRevenueData.map((o, i) => (
              <Fragment key={i}>
                {/* Process data for display */}
                {(() => {
                  // Get yearly arrays from the data
                  const yearlyOtcArray = o?.revenue_otc_summary_by_year_array || [];
                  const yearlyAmortisedArray = o?.revenue_otc_amortised_summary_by_year_array || [];
                  const yearlyMrcArray = o?.revenue_mrc_summary_by_year_array || [];

                  // Ensure arrays match periodLabels length
                  const ensureCorrectLength = (array) => {
                    if (!array || array.length === 0) {
                      return Array(actualPeriods).fill(0);
                    }
                    // Pad with zeros if array is shorter than required
                    if (array.length < actualPeriods) {
                      return [...array, ...Array(actualPeriods - array.length).fill(0)];
                    }
                    // Don't truncate - use all available data
                    if (array.length > actualPeriods) {
                      return array; // Return the full array instead of truncating
                    }
                    return array;
                  };

                  const displayOtcArray = ensureCorrectLength(yearlyOtcArray);
                  const displayAmortisedArray = ensureCorrectLength(yearlyAmortisedArray);
                  const displayMrcArray = ensureCorrectLength(yearlyMrcArray);

                  return (
                    <>
                      {/* OTC One Off Row */}
                      <tr>
                        <td className={bodyCellStyle} rowSpan={3}>
                          {o?.description || '-'}
                        </td>
                        <td className={bodyCellStyle}>OTC One Off</td>
                        {displayOtcArray.map((p, j) => (
                          <td key={j} className={numberCellStyle}>{formatNumber(safeNumber(p))}</td>
                        ))}
                        <td className={numberCellStyleTotal}>{formatNumber(safeArraySum(yearlyOtcArray))}</td>
                      </tr>

                      {/* OTC Amortised Row */}
                      <tr>
                        <td className={bodyCellStyle}>OTC Amortised</td>
                        {displayAmortisedArray.map((p, j) => (
                          <td key={j} className={numberCellStyle}>{formatNumber(safeNumber(p))}</td>
                        ))}
                        <td className={numberCellStyleTotal}>{formatNumber(safeArraySum(yearlyAmortisedArray))}</td>
                      </tr>

                      {/* ARC Row */}
                      <tr>
                        <td className={bodyCellStyle}>ARC</td>
                        {displayMrcArray.map((p, j) => (
                          <td key={j} className={numberCellStyle}>{formatNumber(safeNumber(p))}</td>
                        ))}
                        <td className={numberCellStyleTotal}>{formatNumber(safeArraySum(yearlyMrcArray))}</td>
                      </tr>
                    </>
                  );
                })()}

                <tr className="bg-gray-50">
                  <td colSpan={2} className={twMerge(bodyCellStyle, 'text-center font-semibold text-gray-800 border-b border-gray-200')}>
                    Total
                  </td>
                  {(() => {
                    // Get yearly arrays from the data
                    const yearlyOtcArray = o?.revenue_otc_summary_by_year_array || [];
                    const yearlyAmortisedArray = o?.revenue_otc_amortised_summary_by_year_array || [];
                    const yearlyMrcArray = o?.revenue_mrc_summary_by_year_array || [];

                    // Ensure arrays match periodLabels length
                    const ensureCorrectLength = (array) => {
                      if (!array || array.length === 0) {
                        return Array(actualPeriods).fill(0);
                      }
                      if (array.length < actualPeriods) {
                        return [...array, ...Array(actualPeriods - array.length).fill(0)];
                      }
                      // Don't truncate - use all available data
                      if (array.length > actualPeriods) {
                        return array; // Return the full array instead of truncating
                      }
                      return array;
                    };

                    const displayOtcArray = ensureCorrectLength(yearlyOtcArray);
                    const displayAmortisedArray = ensureCorrectLength(yearlyAmortisedArray);
                    const displayMrcArray = ensureCorrectLength(yearlyMrcArray);

                    // Calculate totals for each period - use the maximum array length
                    const maxLength = Math.max(displayOtcArray.length, displayAmortisedArray.length, displayMrcArray.length, actualPeriods);
                    return Array.from({ length: maxLength }, (_, periodIndex) => {
                      const otcValue = safeNumber(displayOtcArray[periodIndex]);
                      const amortisedValue = safeNumber(displayAmortisedArray[periodIndex]);
                      const mrcValue = safeNumber(displayMrcArray[periodIndex]);
                      const periodTotal = otcValue + amortisedValue + mrcValue;

                      return (
                        <td key={periodIndex} className={numberCellStyleBold}>{formatNumber(periodTotal)}</td>
                      );
                    });
                  })()}
                  <td className={numberCellStyleTotal}>{formatNumber(
                    safeArraySum(o?.revenue_otc_summary_by_year_array) +
                    safeArraySum(o?.revenue_otc_amortised_summary_by_year_array) +
                    safeArraySum(o?.revenue_mrc_summary_by_year_array)
                  )}</td>
                </tr>
              </Fragment>
            ))}

            <tr className="bg-blue-50">
              <td colSpan={2} className={twMerge(bodyCellStyle, 'font-bold text-blue-900 border-b border-blue-200')}>
                Grand Total
              </td>
              {(() => {
                // Prepare arrays for grand totals
                const grandTotalArray = Array(actualPeriods).fill(0);

                // Calculate grand totals for each period
                safeRevenueData.forEach(entry => {
                  // Get yearly arrays from the data
                  const yearlyOtcArray = entry?.revenue_otc_summary_by_year_array || [];
                  const yearlyAmortisedArray = entry?.revenue_otc_amortised_summary_by_year_array || [];
                  const yearlyMrcArray = entry?.revenue_mrc_summary_by_year_array || [];

                  // Use the maximum length to ensure we capture all data
                  const maxEntryLength = Math.max(yearlyOtcArray.length, yearlyAmortisedArray.length, yearlyMrcArray.length);
                  
                  // Add to grand totals
                  for (let i = 0; i < Math.max(grandTotalArray.length, maxEntryLength); i++) {
                    if (i >= grandTotalArray.length) {
                      grandTotalArray.push(0); // Extend array if needed
                    }
                    grandTotalArray[i] +=
                      safeNumber(yearlyOtcArray[i]) +
                      safeNumber(yearlyAmortisedArray[i]) +
                      safeNumber(yearlyMrcArray[i]);
                  }
                });

                // Render the grand total cells
                return grandTotalArray.map((total, i) => (
                  <td key={i} className={numberCellStyleBold}>{formatNumber(total)}</td>
                ));
              })()}
              <td className={numberCellStyleTotal}>{formatNumber(
                safeRevenueData.reduce((prev, curr) => {
                  const mrc = safeArraySum(curr?.revenue_mrc_summary_by_year_array);
                  const otc = safeArraySum(curr?.revenue_otc_summary_by_year_array);
                  const amortised = safeArraySum(curr?.revenue_otc_amortised_summary_by_year_array);
                  return prev + mrc + otc + amortised;
                }, 0)
              )}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div className="mt-2 text-xs text-gray-600 italic">
        * Revenue Summary shows amounts distributed across calendar years using daily proration
      </div>
    </div>
  );
};

export default RevenueSummaryTable;
