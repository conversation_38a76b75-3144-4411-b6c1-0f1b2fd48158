import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import moment from 'moment';
import {
  TextInput,
  SelectInput,
  DateInput,
  AutoCompleteTextInput,
  BinarySwitchInput,
} from '../../Shared/CustomInput';

const RevenueDialog = ({
  open,
  onClose,
  editMode,
  data,
  onChange,
  onSubmit,
  allProducts,
  scenarioData
}) => {
  console.log('RevenueDialog props:', { open, editMode, data });

  // Function to calculate contract end date based on RFS date and contract period
  const calculateContractEndDate = (rfsDate) => {
    if (!rfsDate || !scenarioData?.contract_period_in_month) return '';

    try {
      const startDate = moment(rfsDate);
      if (!startDate.isValid()) return '';

      const contractMonths = parseInt(scenarioData.contract_period_in_month, 10) || 1;
      const endDate = startDate
        .clone()
        .add(contractMonths, 'month')
        .subtract(1, 'days');

      return endDate.format('YYYY-MM-DD');
    } catch (error) {
      console.error('Error calculating contract end date:', error);
      return '';
    }
  };

  const handleSubmit = (action, dialogData) => {
    console.log('Submitting dialog:', { action, dialogData });

    // Ensure numeric fields are converted to numbers
    const preparedData = {
      ...dialogData,
      mrc_amount: parseFloat(dialogData.mrc_amount || 0),
      otc_amount: parseFloat(dialogData.otc_amount || 0),
      otc_amortise: parseFloat(dialogData.otc_amortise || 0),
      bandwidth_value: parseFloat(dialogData.bandwidth_value || 0),
      payment_term_in_days: parseInt(dialogData.payment_term_in_days || 30, 10)
    };

    // For new revenue entries, always use the scenario's expected_rfs_date
    if (!editMode && scenarioData?.expected_rfs_date) {
      preparedData.rfs_date = scenarioData.expected_rfs_date;

      // Calculate and set contract end date based on RFS date and contract period
      preparedData.contract_end_date = calculateContractEndDate(preparedData.rfs_date);
    }

    // Set default values for required fields if they're missing
    if (!preparedData.product) {
      console.warn('Product field is required but missing');
      alert('Please provide a product');
      return;
    }

    // Set other required fields with defaults if missing
    if (!preparedData.type) preparedData.type = 'Operating Revenue';
    if (!preparedData.currency) preparedData.currency = 'myr';
    if (!preparedData.type_price) preparedData.type_price = 'published';
    if (!preparedData.bandwidth_unit) preparedData.bandwidth_unit = 'MBPS';
    if (!preparedData.sla_cos) preparedData.sla_cos = 'Standard';
    if (!preparedData.usp_needed) preparedData.usp_needed = false;

    console.log('Prepared data:', preparedData);
    onSubmit(action, preparedData);
  };

  return (
    <Dialog open={open} onClose={() => onClose(false)}>
      <DialogTitle className="bg-fa text-center text-white">
        {!editMode ? 'Add' : 'Edit'} Revenue
      </DialogTitle>
      <DialogContent>
        <div className="flex">
          <div className="my-8 flex w-full flex-col gap-2 p-2 md:w-[400px]">
            <SelectInput
              name="type"
              value={data?.type}
              placeholder="Type"
              options={['Operating Revenue', 'Non Operating Revenue', 'Interest Income']}
              onChange={onChange}
            />
            <TextInput
              name="description"
              value={data?.description}
              placeholder="Description"
              onChange={onChange}
            />
            <AutoCompleteTextInput
              name="product"
              value={data?.product}
              placeholder="Product"
              options={allProducts}
              onChange={(event) => {
                // Handle both event objects and direct values from AutoCompleteTextInput
                if (event && typeof event === 'object' && event.target) {
                  // Standard event object from regular inputs
                  onChange(event);
                } else {
                  // Direct value from AutoCompleteTextInput (when selecting from dropdown)
                  onChange({
                    target: {
                      name: 'product',
                      value: event
                    }
                  });
                }
              }}
            />
            <TextInput
              name="leg_a"
              value={data?.leg_a}
              placeholder="Leg A"
              onChange={onChange}
            />
            <TextInput
              name="leg_b"
              value={data?.leg_b}
              placeholder="Leg B"
              onChange={onChange}
            />
            <SelectInput
              name="bandwidth_unit"
              value={data?.bandwidth_unit}
              placeholder="Bandwidth Unit"
              options={['TBPS', 'GBPS', 'MBPS', 'KBPS']}
              onChange={onChange}
            />
            <TextInput
              type="number"
              name="bandwidth_value"
              value={data?.bandwidth_value}
              placeholder="Bandwidth Value"
              onChange={onChange}
            />
            <DateInput
              views={['year', 'month', 'day']}
              name="rfs_date"
              value={editMode ? data?.rfs_date : scenarioData?.expected_rfs_date}
              placeholder="RFS Date"
              returnedFormat="YYYY-MM-DD"
              onChange={(event) => {
                // First update the RFS date
                onChange(event);

                // Then calculate and update the contract end date
                const rfsDate = event.target.value;
                if (rfsDate) {
                  const endDate = calculateContractEndDate(rfsDate);
                  onChange({
                    target: {
                      name: 'contract_end_date',
                      value: endDate
                    }
                  });
                }
              }}
              disabled={!editMode}
            />
            <DateInput
              views={['year', 'month', 'day']}
              name="contract_end_date"
              value={editMode ? data?.contract_end_date : calculateContractEndDate(data?.rfs_date || scenarioData?.expected_rfs_date)}
              placeholder="Contract End Date"
              returnedFormat="YYYY-MM-DD"
              onChange={onChange}
              disabled={true} // Always disabled as it's calculated automatically
            />
          </div>
          <div className="my-8 flex w-full flex-col gap-2 p-2 md:w-[400px]">
            <SelectInput
              type="number"
              name="payment_term_in_days"
              value={data?.payment_term_in_days}
              placeholder="Payment Term (Days)"
              options={[30, 45, 60]}
              onChange={onChange}
            />
            <TextInput
              name="sla_cos"
              value={data?.sla_cos}
              placeholder="SLA/COS"
              onChange={onChange}
            />
            <SelectInput
              name="currency"
              value={data?.currency}
              options={['myr', 'usd']}
              placeholder="Currency"
              onChange={onChange}
            />
            <SelectInput
              name="type_price"
              value={data?.type_price}
              options={['published', 'discount', 'floor', 'customised']}
              placeholder="Type Price"
              onChange={onChange}
            />
            <TextInput
              type="number"
              name="otc_amount"
              value={data?.otc_amount}
              placeholder="OTC Amount"
              onChange={onChange}
            />
            <TextInput
              type="number"
              name="otc_amortise"
              value={data?.otc_amortise}
              placeholder="OTC (Amortise) Amount"
              onChange={onChange}
            />
            <TextInput
              type="number"
              name="mrc_amount"
              value={data?.mrc_amount}
              placeholder="MRC Amount"
              onChange={onChange}
            />
            <BinarySwitchInput
              name="usp_needed"
              value={data?.usp_needed}
              placeholder="USP Needed"
              onChange={onChange}
            />
          </div>
        </div>
      </DialogContent>
      <DialogActions>
        <div className="flex w-full justify-between gap-4">
          <div className="flex gap-2">
            <button type="button" onClick={() => onClose(false)} className="p-2">
              Cancel
            </button>
            {editMode && (
              <button
                type="button"
                onClick={() => handleSubmit('delete', data)}
                className="cta-btn bg-red-500"
              >
                Delete
              </button>
            )}
          </div>

          <div className="flex gap-2">
            {!editMode && (
              <button
                type="button"
                onClick={() => handleSubmit('post', data)}
                className="bg-fa cta-btn"
              >
                Create
              </button>
            )}
            {editMode && (
              <button
                type="button"
                onClick={() => handleSubmit('put', data)}
                className="bg-fa cta-btn"
              >
                Save
              </button>
            )}
          </div>
        </div>
      </DialogActions>
    </Dialog>
  );
};

export default RevenueDialog;