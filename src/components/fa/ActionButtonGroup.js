// Next, React, Tw
import { useSelector, useDispatch } from 'react-redux';
import { useRouter } from 'next/router';
import { useState, useCallback, useRef } from 'react';

// Packages
import * as yup from 'yup';
import moment from 'moment';

// Components
import { EnhancedButton } from './ui/EnhancedButton';

// Others
import { useSnackbar } from '../Shared/snackbar';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { FA_ENDPOINT } from '../../utils/fa';
import axios from '../../utils/axios';

const ActionButtonGroup = () => {
  // Standard and Vars
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const { projectData } = useSelector((state) => state.fa);
  const { query } = useRouter();
  const { projectId } = query;
  const [isSaving, setIsSaving] = useState(false);

  // Form
  const schema = yup.object({
    budgeted_in_aop: yup.string().required('Please provide budgeted in AOP.'),
    category: yup.string().required('Please provide category.'),
    currency: yup.string().required('Please provide currecncy.'),
    customer_name: yup.string().required('Please provide customer name.'),
    customer_segment: yup.string().required('Please provide customer segment.'),
    entity: yup.string().required('Please provide entity.')?.default('TM Tech'),
    exchange_rate: yup.number(),
    exchange_rate_as_of_date: yup.string()?.default(moment().format('YYYY-MM-DD')),
    name: yup.string().required('Please provide project name.'),
    nrp_reference_no: yup.string(),
    dtc_no: yup.string(),
    product_segment: yup.string().required('Please provide product segment.'),
    purpose: yup.string().required('Please provide purpose.'),
    status: yup.string().required('Please provide status.'),
    stamping_fees_needed: yup.boolean().required('Please provide stamping fees needed.'),
    type: yup.string().required('Please provide FA type.'),
    viewer_staff_id_list: yup.array().of(yup.string()).default([]),
    created_by_staff_id: yup.string().required('Please provide created by staff ID.'),
    level_of_authority: yup.string().required('Please provide LOA.'),
  });

  // Use ref to prevent multiple simultaneous submissions
  const isSubmitting = useRef(false);

  const handleFormSubmit = useCallback(async (action) => {
    // Prevent multiple simultaneous submissions
    if (isSubmitting.current) {
      console.log('Form submission already in progress, skipping');
      return;
    }
    
    if (action) {
      isSubmitting.current = true;
      let payload;
      
      try {
        setIsSaving(true);
        
        // Validate project data with consistent state
        const currentProjectData = { ...projectData, entity: 'TM Tech' };
        payload = await schema.validate(currentProjectData, { abortEarly: false });
        
      } catch (error) {
        enqueueSnackbar(Array.isArray(error.errors) ? error.errors.join(', ') : 'Validation failed', {
          variant: 'error',
        });
        setIsSaving(false);
        isSubmitting.current = false;
        return;
      }
      
      dispatch(setIsLoading(true));
      
      try {
        let response;
        switch (action) {
          case 'put':
            response = await axios.put(`${FA_ENDPOINT}/project/${projectId}`, payload);
            break;
          default:
            console.warn(`Unknown action: ${action}`);
            break;
        }
        
        let statusVariant = 'error';
        let message = 'Failed';
        
        if (response?.data?.status === 'success') {
          statusVariant = 'success';
          message = response.data.data || 'Changes saved successfully';
        }
        
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
        
        // Only reload if save was successful
        if (statusVariant === 'success') {
          // Use a small delay to ensure state updates are processed
          setTimeout(() => {
            if (window) window.location.reload();
          }, 500);
        }
        
      } catch (error) {
        console.error('Form submission error:', error);
        enqueueSnackbar(error?.response?.data?.message || 'Failed to save changes', {
          variant: 'error',
        });
      } finally {
        dispatch(setIsLoading(false));
        setIsSaving(false);
        isSubmitting.current = false;
      }
    } else {
      // If no action specified, just reload
      if (window) window.location.reload();
    }
  }, [projectData, projectId, schema, enqueueSnackbar, dispatch]);
  return (
    <div className="flex items-center gap-2">
      <button
        type="button"
        onClick={() => handleFormSubmit('put')}
        disabled={isSaving}
        className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isSaving ? (
          <>
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"/>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
            </svg>
            Saving...
          </>
        ) : (
          'Save Changes'
        )}
      </button>
    </div>
  );
};

export default ActionButtonGroup;
