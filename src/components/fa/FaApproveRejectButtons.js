// Next, React, Tailwind
import { useState, useEffect, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';

// MUI
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';

// Components
import { useSnackbar } from '../Shared/snackbar';

// Others
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { toUpperCaseFirstLetter } from '../../utils/shared';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { useSimiContext } from '../../utils/simi';

/**
 * A styled version of FaApproveRejectButtons component that directly matches
 * the Export Summary button styling in the project page.
 */
const FaApproveRejectButtons = (props) => {
  const { moduleColorCode } = useSimiContext();
  const { user } = useAuthContext();
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const { asPath } = useRouter();
  const { handleSendEmail, getCertainStaffInfoFromStaffId } = useSimiContext();
  
  const [approvalsData, setApprovalsData] = useState([]);
  const [type, setType] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [validationDialogOpen, setValidationDialogOpen] = useState(false);
  const [validationErrors, setValidationErrors] = useState([]);
  
  // Memoize expensive calculations to prevent unnecessary re-renders
  const currentStageIndex = useMemo(() => 
    approvalsData?.findIndex((o) => o?.status?.toLowerCase() === 'pending') ?? -1,
    [approvalsData]
  );
  
  const currentStageData = useMemo(() => 
    approvalsData?.[currentStageIndex],
    [approvalsData, currentStageIndex]
  );
  
  // Check if current user is the requestor (project creator)
  const requestorApproval = useMemo(() => 
    approvalsData?.find((o) => o?.type?.toLowerCase() === 'requestor'),
    [approvalsData]
  );
  
  const isRequestor = useMemo(() => 
    requestorApproval?.user_name_array?.some(name => 
      name?.toLowerCase()?.trim() === user?.name?.toLowerCase()?.trim()
    ) ?? false,
    [requestorApproval?.user_name_array, user?.name]
  );
  
  // Fallback: if no approval data loaded but we have validation data, assume user is requestor for draft projects
  const isLikelyRequestor = useMemo(() => 
    (!approvalsData || approvalsData.length === 0) && props.validationData,
    [approvalsData, props.validationData]
  );
  
  const handleRevert = async () => {
    try {
      // Reset approval workflow: only requestor becomes pending, others become waiting
      for (let i = 0; i < approvalsData?.length; i += 1) {
        const newStatus = approvalsData[i]?.type?.toLowerCase() === 'requestor' ? 'pending' : 'waiting';
        await axios.put(`${props.UPDATE_CERTAIN_APPROVAL_ENDPOINT}/${approvalsData[i]?.id}`, {
          ...approvalsData[i],
          status: newStatus,
        });
      }

      // Send E-Mail
      const recipientsList = approvalsData?.[0]?.user_name_array;
      const ccsList = [];

      await handleSendEmail(
        recipientsList?.map((o) => getCertainStaffInfoFromStaffId(o, 'email')),
        ccsList?.map((o) => getCertainStaffInfoFromStaffId(o, 'email')),
        `${props.NAME} - ${type === 'revert' ? 'REVERTED' : 'ESCALATED'}`,
        `
          <p>${props.NAME} has been ${type === 'revert' ? 'reverted' : 'escalated'} to you.</p>\n
          <p>Kindly, please click this <a href="${process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT}${asPath}">link</a> to review it.</p>
        `
      );

      // Callback
      if (props.REVERT_CALLBACK) props.REVERT_CALLBACK();

      // Success
      enqueueSnackbar('Success!', {
        variant: 'success',
      });
    } catch {
      enqueueSnackbar('Something went wrong!', {
        variant: 'error',
      });
    }
    fetchData();
  };

  // Validation function for requestor submission
  const validateSubmission = () => {
    const { allScenarios, allScenarioDetailsFilled, checkingScenarioDetails } = props.validationData || {};
    const errors = [];

    if (!allScenarios || allScenarios.length === 0) {
      errors.push('Create at least one scenario');
    }

    if (allScenarios && allScenarios.length > 0 && !allScenarioDetailsFilled && !checkingScenarioDetails) {
      errors.push('Add CAPEX, OPEX, and Revenue data to all scenarios');
    }

    if (checkingScenarioDetails) {
      errors.push('Please wait while we validate your scenario data');
    }

    return errors;
  };

  const handleSubmit = async () => {
    // Validate submission requirements for requestors
    if (isRequestor || isLikelyRequestor) {
      const errors = validateSubmission();
      
      if (errors.length > 0) {
        setValidationErrors(errors);
        setValidationDialogOpen(true);
        return;
      }
    }

    dispatch(setIsLoading(true));
    
    try {
      // Execute as transaction - if any step fails, the whole operation fails
      const updates = [];
      
      // Step 1: Update current stage
      updates.push(
        axios.put(`${props.UPDATE_CERTAIN_APPROVAL_ENDPOINT}/${currentStageData?.id}`, {
          ...currentStageData,
          status: 'done',
        })
      );

      // Step 2: Update next stage if exists
      const nextApprovalIndex = currentStageIndex + 1;
      if (nextApprovalIndex < approvalsData.length) {
        const nextApproval = approvalsData[nextApprovalIndex];
        updates.push(
          axios.put(`${props.UPDATE_CERTAIN_APPROVAL_ENDPOINT}/${nextApproval?.id}`, {
            ...nextApproval,
            status: 'pending',
          })
        );
      }

      // Execute all database updates first
      await Promise.all(updates);

      // Step 3: Send notification (non-critical - don't fail on email errors)
      const recipientsList = approvalsData?.[nextApprovalIndex]?.user_name_array;
      const ccsList = [];
      for (let i = 0; i <= currentStageIndex; i += 1) {
        ccsList.push(...(approvalsData?.[i]?.user_name_array || []));
      }

      if (recipientsList?.length > 0 && recipientsList[0] !== '') {
        try {
          await handleSendEmail(
            recipientsList.map((o) => getCertainStaffInfoFromStaffId(o, 'email')),
            ccsList.map((o) => getCertainStaffInfoFromStaffId(o, 'email')),
            `${props.NAME} - SUBMITTED FOR APPROVAL`,
            `<p>${props.NAME} has been submitted for your review and approval.</p>
             <p>Please click this <a href="${process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT}${asPath}">link</a> to review it.</p>`
          );
        } catch (emailError) {
          console.warn('Email notification failed:', emailError);
          // Don't fail the whole operation for email issues
        }
      }

      // Step 4: Execute callback
      if (props.SUBMIT_CALLBACK) {
        const nextStageType = approvalsData?.[nextApprovalIndex]?.type;
        const newStatus = nextStageType ? `pending ${nextStageType}` : 'completed';
        props.SUBMIT_CALLBACK(newStatus);
      }

      enqueueSnackbar('Project submitted successfully!', { variant: 'success' });
      
    } catch (error) {
      console.error('Submit error:', error);
      enqueueSnackbar(`Failed to submit project: ${error.message}`, { variant: 'error' });
    } finally {
      dispatch(setIsLoading(false));
      await fetchData(); // Refresh data after operation
    }
  };

  const handleApprove = async () => {
    dispatch(setIsLoading(true));
    
    try {
      // Execute database updates as transaction
      const updates = [];
      
      updates.push(
        axios.put(`${props.UPDATE_CERTAIN_APPROVAL_ENDPOINT}/${currentStageData?.id}`, {
          ...currentStageData,
          status: 'done',
        })
      );

      const nextApprovalIndex = currentStageIndex + 1;
      if (nextApprovalIndex < approvalsData.length) {
        const nextApproval = approvalsData[nextApprovalIndex];
        updates.push(
          axios.put(`${props.UPDATE_CERTAIN_APPROVAL_ENDPOINT}/${nextApproval?.id}`, {
            ...nextApproval,
            status: 'pending',
          })
        );
      }

      await Promise.all(updates);

      // Send notification (non-critical)
      const recipientsList = approvalsData?.[nextApprovalIndex]?.user_name_array;
      const ccsList = [];
      for (let i = 0; i <= currentStageIndex; i += 1) {
        ccsList.push(...(approvalsData?.[i]?.user_name_array || []));
      }

      if (recipientsList?.length > 0 && recipientsList[0] !== '') {
        try {
          await handleSendEmail(
            recipientsList.map((o) => getCertainStaffInfoFromStaffId(o, 'email')),
            ccsList.map((o) => getCertainStaffInfoFromStaffId(o, 'email')),
            `${props.NAME} - APPROVED AND FORWARDED`,
            `<p>${props.NAME} has been approved and forwarded to you for the next stage of review.</p>
             <p>Please click this <a href="${process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT}${asPath}">link</a> to review it.</p>`
          );
        } catch (emailError) {
          console.warn('Email notification failed:', emailError);
        }
      }

      if (props.APPROVE_CALLBACK) {
        const nextStageType = approvalsData?.[nextApprovalIndex]?.type;
        const newStatus = nextStageType ? `pending ${nextStageType}` : 'completed';
        props.APPROVE_CALLBACK(newStatus);
      }

      enqueueSnackbar('Project approved successfully!', { variant: 'success' });
      
    } catch (error) {
      console.error('Approve error:', error);
      enqueueSnackbar(`Failed to approve project: ${error.message}`, { variant: 'error' });
    } finally {
      dispatch(setIsLoading(false));
      await fetchData();
    }
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${props.GET_ALL_APPROVALS_ENDPOINT}`);
      if (response.data.data) {
        setApprovalsData(response.data.data);
      }
    } catch {
      setApprovalsData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  // Remove the disabled check - always show buttons when user has permission

  return (
    <>
      {/* Always show submit button for requestors on draft projects */}
      {(isRequestor || isLikelyRequestor) && (
        <div className="flex items-center gap-3">
          <button
            type="button"
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
            onClick={() => {
              setType('submit');
              setDialogOpen(true);
            }}
          >
            Submit
          </button>
        </div>
      )}
      
      {/* Show approve/revert buttons for non-requestor approvers */}
      {!isRequestor && currentStageData?.user_name_array?.some(name => 
        name?.toLowerCase()?.trim() === user?.name?.toLowerCase()?.trim()
      ) && (
        <div className="flex items-center gap-3">
          <button
            type="button"
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500/50"
            onClick={() => {
              setType('revert');
              setDialogOpen(true);
            }}
          >
            Revert
          </button>
          <button
            type="button"
            className="px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500/50"
            onClick={() => {
              setType('approve');
              setDialogOpen(true);
            }}
          >
            Approve
          </button>
        </div>
      )}

      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)}>
        <DialogTitle 
          className="bg-fa text-center text-white"
          style={{ backgroundColor: moduleColorCode }}
        >
          {toUpperCaseFirstLetter(type)} {props.documentName || 'Project'}
        </DialogTitle>
        <DialogContent>
          <div className="mt-8 flex w-full flex-col items-center gap-4 p-2 md:w-[400px]">
            Proceed?
          </div>
        </DialogContent>
        <DialogActions className="border-t border-slate-200 bg-slate-50 px-6 py-4">
          <div className="flex w-full justify-between">
            <button 
              type="button" 
              onClick={() => setDialogOpen(false)} 
              className="px-4 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors duration-200"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={async () => {
                if (type === 'revert') handleRevert();
                else if (type === 'submit') handleSubmit();
                else if (type === 'approve') handleApprove();
                setDialogOpen(false);
              }}
              className={`px-4 py-2 text-sm font-medium text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 ${
                type === 'revert' 
                  ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500/50' 
                  : type === 'approve'
                  ? 'bg-green-600 hover:bg-green-700 focus:ring-green-500/50'
                  : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500/50'
              }`}
            >
              Proceed
            </button>
          </div>
        </DialogActions>
      </Dialog>

      {/* Validation Dialog */}
      <Dialog open={validationDialogOpen} onClose={() => setValidationDialogOpen(false)}>
        <DialogTitle 
          className="bg-amber-500 text-center text-white"
        >
          Complete Required Data
        </DialogTitle>
        <DialogContent>
          <div className="mt-8 flex w-full flex-col gap-4 p-2 md:w-[450px]">
            <div className="flex items-start gap-3">
              <svg className="w-8 h-8 text-amber-500 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div className="flex-1">
                <p className="text-gray-700 mb-4">
                  Please complete the following requirements before submitting your project for approval:
                </p>
                <ul className="space-y-2">
                  {validationErrors.map((error, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0"></span>
                      <span className="text-gray-700">{error}</span>
                    </li>
                  ))}
                </ul>
                <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <p className="text-sm text-blue-800">
                    💡 <strong>Tip:</strong> Navigate to the "Scenarios" section to add financial data, or create a new scenario if none exist.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>
        <DialogActions className="border-t border-slate-200 bg-slate-50 px-6 py-4">
          <div className="flex w-full justify-center">
            <button 
              type="button" 
              onClick={() => setValidationDialogOpen(false)} 
              className="px-6 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors duration-200"
            >
              Got it, I'll complete the data
            </button>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default FaApproveRejectButtons;
