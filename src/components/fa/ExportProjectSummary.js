// Next, React, Tw
import { useSelector } from 'react-redux';

// Components
import AllFinancialAnalysis from './AllFinancialAnalysis';
import ScreenshotDialog from '../Shared/ScreenshotDialog';

// Others
import { useParamContext } from '../../utils/auth/ParamProvider';

const ExportProjectSummary = () => {
  // Standard and Vars
  const { projectData } = useSelector((state) => state.fa);
  const { setParam } = useParamContext();

  return (
    <>
      <button
        type="button"
        className="bg-primary hover:bg-primary/90 flex items-center gap-1.5 whitespace-nowrap rounded-lg px-5 py-2 text-sm font-medium text-white shadow-sm transition-all duration-200"
        onClick={() => setParam({ screenshotDialogOpen: true })}
      >
        <svg
          className="h-4 w-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          ></path>
        </svg>
        Export Summary
      </button>
      <ScreenshotDialog fileName={`FA-${projectData?.name}.pdf`} isLandscape>
        <div className="flex w-full flex-col gap-4 p-4">
          <div className="bg-primary w-full text-center text-white">FINANCIAL ANALYSIS SUMMARY</div>
          <p className="mx-auto w-1/3 text-sm">Customer: {projectData?.customer_name}</p>
          <AllFinancialAnalysis filterByEndorsed showApproverEndorsementCell={false} />
        </div>
      </ScreenshotDialog>
    </>
  );
};

export default ExportProjectSummary;
