import moment from 'moment';
import { calculateContractYears } from '../../../utils/faDisplayUtils';

/**
 * Calculate amortization and MRC arrays based on contract data
 * @param {Object} data - The OPEX data with start/end dates and amounts
 * @param {Object} scenarioData - The scenario data containing contract period
 * @returns {Object} Object containing amortise and MRC arrays by year
 */

// Ensure data has all required numeric fields properly formatted
const sanitizeOpexData = (data) => {
  const result = { ...data };
  
  // Ensure all numeric fields are properly converted to numbers
  result.otc_amount = Number(data?.otc_amount || 0);
  result.otc_amortise_amount = Number(data?.otc_amortise_amount || 0);
  result.mrc_amount = Number(data?.mrc_amount || 0);
  
  // For backwards compatibility, if otc_amortise_amount is 0 but otc_amount is set,
  // use the same value for amortization (common in many OPEX entries)
  if (result.otc_amortise_amount === 0 && result.otc_amount > 0) {
    result.otc_amortise_amount = result.otc_amount;
  }
  
  return result;
};

// Helper function to calculate months in each year for proper proration (same as REVENUE)
function calculateMonthsInYear(yearIndex, rfsDate, contractMonths, yearDifference) {
  const rfsDateMoment = moment(rfsDate);
  const rfsYear = rfsDateMoment.year();

  // Calculate which calendar year this yearIndex represents
  const calendarYear = rfsYear + (yearIndex - yearDifference);

  // If this is a gap year (before contract starts), return 0
  if (yearIndex < yearDifference) {
    return 0;
  }

  // Calculate contract start and end dates
  const contractStartDate = rfsDateMoment.clone();
  const contractEndDate = contractStartDate.clone().add(contractMonths, 'months').subtract(1, 'day');

  // Calculate year boundaries
  const yearStart = moment(`${calendarYear}-01-01`);
  const yearEnd = moment(`${calendarYear}-12-31`);

  // Find the overlap between contract period and this calendar year
  const overlapStart = moment.max(contractStartDate, yearStart);
  const overlapEnd = moment.min(contractEndDate, yearEnd);

  // If no overlap, return 0
  if (overlapStart.isAfter(overlapEnd)) {
    return 0;
  }

  // Calculate months in overlap period
  const monthsDiff = overlapEnd.diff(overlapStart, 'months', true);
  const monthsInThisYear = Math.max(0, Math.min(monthsDiff, 12));


  return monthsInThisYear;
}

export const getMetrix = (data, scenarioData) => {
  // Sanitize the data to ensure all numeric fields are properly formatted
  const sanitizedData = sanitizeOpexData(data);
  

  // Calculate year gap between project start and RFS date
  const calculateYearDifference = () => {
    if (!scenarioData?.project_start_date || !scenarioData?.expected_rfs_date) {
      return 0;
    }

    const startYear = moment(scenarioData.project_start_date).year();
    const rfsYear = moment(scenarioData.expected_rfs_date).year();
    const yearDifference = Math.max(0, rfsYear - startYear);


    return yearDifference;
  };

  const calculateAmortiseArray = () => {
    // Get contract info - ensure safe defaults
    const contractMonths = Number(scenarioData?.contract_period_in_month) || 120;
    
    // Calculate year gap between project start and RFS date
    const yearDifference = calculateYearDifference();
    
    // Calculate contract years based on actual calendar years spanned
    const contractYears = calculateContractYears(scenarioData?.expected_rfs_date, contractMonths);
    
    // Total years = gap years + contract years
    const totalYears = yearDifference + contractYears;
    
    const totalAmount = sanitizedData?.otc_amortise_amount;

    // Validate the final result to ensure it's a valid array length
    if (!Number.isFinite(totalYears) || totalYears < 1 || totalYears > 1000) {
      console.warn('Invalid totalYears calculated in OPEX amortise:', totalYears, 'Defaulting to 10 years');
      return Array(10).fill(0);
    }
    
    // Create array with zeros for gap years
    const result = Array(totalYears).fill(0);

    // Calculate monthly amortization amount
    const monthlyAmortization = totalAmount / contractMonths;

    // Fill contract years with prorated amortized amounts based on actual months in each year
    // Use RFS date for proration (same as REVENUE)
    for (let i = 0; i < totalYears; i++) {
      if (i < yearDifference) {
        result[i] = 0; // Gap years
      } else {
        const monthsInThisYear = calculateMonthsInYear(i, scenarioData.expected_rfs_date, contractMonths, yearDifference);
        result[i] = monthlyAmortization * monthsInThisYear;
      }
    }
    

    return result;
  };

  const calculateMrcArray = () => {
    // Get contract info - ensure safe defaults
    const contractMonths = Number(scenarioData?.contract_period_in_month) || 120;
    
    // Calculate year gap between project start and RFS date
    const yearDifference = calculateYearDifference();
    
    // Calculate contract years based on actual calendar years spanned
    const contractYears = calculateContractYears(scenarioData?.expected_rfs_date, contractMonths);
    
    // Total years = gap years + contract years
    const totalYears = yearDifference + contractYears;
    
    const monthlyAmount = sanitizedData?.mrc_amount;

    // Validate the final result to ensure it's a valid array length
    if (!Number.isFinite(totalYears) || totalYears < 1 || totalYears > 1000) {
      console.warn('Invalid totalYears calculated in OPEX MRC:', totalYears, 'Defaulting to 10 years');
      return Array(10).fill(0);
    }
    
    // Create array with zeros for gap years, then fill with prorated MRC amounts
    const result = Array(totalYears).fill(0);

    // Fill contract years with prorated MRC amounts based on actual months in each year
    // Use RFS date for proration (same as REVENUE)
    for (let i = 0; i < totalYears; i++) {
      if (i < yearDifference) {
        result[i] = 0; // Gap years
      } else {
        const monthsInThisYear = calculateMonthsInYear(i, scenarioData.expected_rfs_date, contractMonths, yearDifference);
        result[i] = monthlyAmount * monthsInThisYear;
      }
    }
    

    return result;
  };

  // Calculate both arrays
  const result = {
    amortiseArray: calculateAmortiseArray(),
    mrcArray: calculateMrcArray(),
  };
  
  
  return result;
};

/**
 * Calculate the total number of years needed for display based on contract data
 * @param {Object} data - The data containing start_date  
 * @param {Object} scenarioData - The scenario data containing contract period
 * @returns {number} The total number of years
 */
export const calculateTotalYears = (data, scenarioData) => {
  const contractMonths = Number(scenarioData?.contract_period_in_month) || 120;
  
  // Calculate year gap between project start and RFS date with proper validation
  let yearDifference = 0;
  try {
    const projectStartMoment = moment(scenarioData?.project_start_date);
    const rfsMoment = moment(scenarioData?.expected_rfs_date);
    
    if (projectStartMoment.isValid() && rfsMoment.isValid()) {
      const startYear = projectStartMoment.year();
      const rfsYear = rfsMoment.year();
      yearDifference = Math.max(0, rfsYear - startYear);
    }
  } catch (error) {
    console.error("Year difference calculation error:", error);
    yearDifference = 0;
  }
  
  // For 12-month contracts starting mid-year, we need 2 calendar years (same as REVENUE)
  const contractYears = contractMonths === 12 && scenarioData?.expected_rfs_date
    ? (() => {
        const rfsDate = moment(scenarioData.expected_rfs_date);
        const needsExtraYear = !(rfsDate.date() === 1 && rfsDate.month() === 0);
        const baseYears = Math.floor(contractMonths / 12);
        return needsExtraYear ? baseYears + 1 : baseYears;
      })()
    : Math.ceil(contractMonths / 12);
  
  let totalYears = yearDifference + contractYears;
  
  // Validate the final result to ensure it's a valid array length
  if (!Number.isFinite(totalYears) || totalYears < 1 || totalYears > 1000) {
    console.warn('Invalid totalYears calculated:', totalYears, 'Defaulting to 10 years');
    totalYears = 10; // Default to a reasonable value
  }
  
  // Ensure it's an integer
  totalYears = Math.floor(totalYears);
  
  
  return totalYears;
};

/**
 * Get the formatted label for OPEX type
 * @param {string} paramType - The param_type value ('rou' or 'non-rou')
 * @returns {string} The formatted type label
 */
export const getOpexTypeLabel = (paramType) => {
  switch (paramType) {
    case 'rou':
      return 'ROU';
    case 'non-rou':
      return 'Non-ROU';
    default:
      return 'Non-ROU';
  }
};
