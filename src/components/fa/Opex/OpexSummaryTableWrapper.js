import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import OpexSummaryTable from './OpexSummaryTable';

/**
 * Wrapper component for OpexSummaryTable that ensures data is properly sanitized
 * before being passed to the table component.
 */
const OpexSummaryTableWrapper = ({ opexData, scenarioData }) => {
  // Ensure data has all required numeric fields properly formatted at the source
  const sanitizeOpexDataArray = (dataArray) => {
    if (!dataArray || !Array.isArray(dataArray) || dataArray.length === 0) {
      return [];
    }

    // Create a deep copy of the data to avoid modifying the original
    return dataArray.map(item => {
      if (!item) return {};
      
      const result = { ...item };
      
      // Ensure all numeric fields are properly converted to numbers
      result.otc_amount = Number(item?.otc_amount || 0);
      result.otc_amortise_amount = Number(item?.otc_amortise_amount || 0);
      result.mrc_amount = Number(item?.mrc_amount || 0);
      
      // For backwards compatibility, if otc_amortise_amount is 0 but otc_amount is set,
      // use the same value for amortization (common in many OPEX entries)
      if (result.otc_amortise_amount === 0 && result.otc_amount > 0) {
        result.otc_amortise_amount = result.otc_amount;
        console.log('Wrapper: Auto-set otc_amortise_amount to match otc_amount for entry:', 
          { provider: result.provider, details: result.details, value: result.otc_amount });
      }
      
      return result;
    });
  };

  // Debug the data before rendering
  useEffect(() => {
    if (opexData && opexData.length > 0) {
      console.log('=== OPEX DATA BEFORE SANITIZATION ===');
      opexData.forEach((entry, index) => {
        console.log(`Entry #${index + 1}:`, {
          provider: entry?.provider,
          details: entry?.details,
          otc_amount: entry?.otc_amount,
          otc_amortise_amount: entry?.otc_amortise_amount,
          mrc_amount: entry?.mrc_amount,
          start_date: entry?.start_date
        });
      });
      
      // Log scenario data
      console.log('Scenario Data:', {
        contract_period_in_month: scenarioData?.contract_period_in_month
      });
    }
  }, [opexData, scenarioData]);

  // Sanitize the data and scenario
  const sanitizedData = sanitizeOpexDataArray(opexData);
  
  // Ensure the scenario data has valid contract period
  const sanitizedScenarioData = { 
    ...scenarioData,
    contract_period_in_month: Number(scenarioData?.contract_period_in_month || 120)
  };
  
  console.log("FINAL SANITIZED DATA AND SCENARIO:", {
    dataLength: sanitizedData.length,
    firstEntry: sanitizedData[0] ? {
      otc_amount: sanitizedData[0].otc_amount,
      otc_amortise_amount: sanitizedData[0].otc_amortise_amount,
      mrc_amount: sanitizedData[0].mrc_amount
    } : null,
    scenarioData: sanitizedScenarioData
  });

  return <OpexSummaryTable opexData={sanitizedData} scenarioData={sanitizedScenarioData} />;
};

OpexSummaryTableWrapper.propTypes = {
  opexData: PropTypes.array.isRequired,
  scenarioData: PropTypes.object,
};

export default OpexSummaryTableWrapper;
