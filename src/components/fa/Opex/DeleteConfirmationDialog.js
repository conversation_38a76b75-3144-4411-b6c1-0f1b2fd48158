import React from 'react';
import PropTypes from 'prop-types';
import { Dialog, DialogTitle } from '@mui/material';

const DeleteConfirmationDialog = ({ open, onClose, onConfirm, title, itemName, itemType }) => {
  return (
    <Dialog open={open} onClose={() => onClose()}>
      <DialogTitle className="bg-fa text-center text-white">
        {title || 'Delete Confirmation'}
      </DialogTitle>
      <div className="flex flex-col gap-4 p-4">
        <p className="text-sm">
          Are you sure you want to delete {itemType ? `this ${itemType}` : ''}
          {itemName ? ` "${itemName}"` : ''}?
        </p>
      </div>
      <div className="flex justify-between gap-4 p-4">
        <button type="button" onClick={() => onClose()} className="p-2">
          Cancel
        </button>
        <button 
          type="button" 
          onClick={() => onConfirm()}
          className="cta-btn bg-red-500"
        >
          Delete
        </button>
      </div>
    </Dialog>
  );
};

DeleteConfirmationDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  title: PropTypes.string,
  itemName: PropTypes.string,
  itemType: PropTypes.string
};

export default DeleteConfirmationDialog;
