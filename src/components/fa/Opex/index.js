import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import axios from '../../../utils/axios';
import { FA_ENDPOINT } from '../../../utils/fa';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { useSnackbar } from '../../Shared/snackbar';
import { sanitizeInput } from '../../../utils/sanitize';
import { useModuleRoleContext } from '../../../utils/auth/ModuleRoleProvider';
import { useAuthContext } from '../../../utils/auth/useAuthContext';
import { useParamContext } from '../../../utils/auth/ParamProvider';
import { setBreadCrumbsList } from '../../../utils/store/simiReducer';
import DocViewer, { DocViewerRenderers } from '@cyntler/react-doc-viewer';
import '@cyntler/react-doc-viewer/dist/index.css';

// Components
import OpexTable from './OpexTable';
import OpexSummaryTableWrapper from './OpexSummaryTableWrapper';
import OpexFormDialog from './OpexFormDialog';
import OpexDocumentsTable from './OpexDocumentsTable';
import DeleteConfirmationDialog from './DeleteConfirmationDialog';
import ExportExcelButton from '../../Shared/ExportExcelButton';
import UploadCsvButton from '../../Shared/UploadCsv';
import ScreenshotDialog from '../../Shared/ScreenshotDialog';
import { Dialog } from '@mui/material';

const AllOpex = ({ scenarioId: propScenarioId, showExportButton = true, onDataLoaded, exportMode = false }) => {
  // Standard
  const { enqueueSnackbar } = useSnackbar();
  const { query } = useRouter();
  const { projectId, scenarioId: queryScenarioId } = query;
  const scenarioId = propScenarioId || queryScenarioId;
  const dispatch = useDispatch();
  const { isAdmin } = useModuleRoleContext();
  const { user } = useAuthContext();
  const { projectData, scenarioData } = useSelector((state) => state.fa);
  const { setParam } = useParamContext();

  // State
  const [opexData, setOpexData] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const [dialogData, setDialogData] = useState({});
  const [fileList, setFileList] = useState([]);
  const [focusedFile, setFocusedFile] = useState({});
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [pdfUrl, setPdfUrl] = useState(null);

  // Allow editing if user is admin OR if user created the project
  const canEdit = isAdmin || (projectData?.created_by_staff_id === user?.staff_id);
  const IS_DISABLED = !canEdit || projectData?.IS_DISABLED;

  // Handlers for OPEX entries
  const handleClickOpenDialog = (editMode, data = {}) => {
    if (editMode) {
      setDialogData(data);
    } else {
      setDialogData({}); // Reset dialog data for new entry
    }
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };

  const handleDialogSubmit = async (action, payload) => {
    if (action === 'update') {
      // This is just updating the form data, not saving to backend
      setDialogData(payload);
      return;
    }
    
    if (['post', 'put', 'delete'].includes(action)) {
      // Prevent concurrent operations
      if (handleDialogSubmit._isProcessing) {
        console.log('Operation already in progress, skipping');
        return;
      }
      handleDialogSubmit._isProcessing = true;
      dispatch(setIsLoading(true));
      try {
        let response;
        let processedPayload = payload;
        
        // Sanitize input fields for post/put operations
        if (action === 'post' || action === 'put') {
          processedPayload = {
            ...payload,
            provider: sanitizeInput(payload?.provider),
            details: sanitizeInput(payload?.details),
            leg_a: sanitizeInput(payload?.leg_a),
            leg_b: sanitizeInput(payload?.leg_b)
          };
        }
        
        switch (action) {
          case 'post':
            response = await axios.post(`${FA_ENDPOINT}/opex`, processedPayload);
            break;
          case 'put':
            response = await axios.put(`${FA_ENDPOINT}/opex/${dialogData?.id}`, processedPayload);
            break;
          case 'delete':
            response = await axios.delete(`${FA_ENDPOINT}/opex/${dialogData?.id}`);
            break;
          default:
            break;
        }
        
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
          setDialogOpen(false);
          setDialogData({});
          fetchData();
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch (error) {
        enqueueSnackbar(error.message || 'Failed', {
          variant: 'error',
        });
      } finally {
        dispatch(setIsLoading(false));
        handleDialogSubmit._isProcessing = false;
      }
    } else {
      // Just close the dialog
      setDialogOpen(false);
      setDialogData({});
    }
  };

  // File functions
  const fetchFileList = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${FA_ENDPOINT}/file/opex/${scenarioId}`);
      setFileList(response?.data?.data || []);
    } catch (error) {
      console.error('Error fetching file list:', error);
      setFileList([]);
    }
    dispatch(setIsLoading(false));
  };
  
  const handleFileUpload = async (file) => {
    dispatch(setIsLoading(true));
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await axios.post(
        `${FA_ENDPOINT}/file/upload/opex/${scenarioId}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      
      let statusVariant = 'error';
      let message = 'Failed';
      if (response.data.status === 'success' || response?.status === 201) {
        statusVariant = 'success';
        message = 'Success';
        // Refresh the file list
        fetchFileList();
      }
      
      enqueueSnackbar(message, {
        variant: statusVariant,
      });
    } catch (error) {
      enqueueSnackbar('Something went wrong', {
        variant: 'error',
      });
    }
    dispatch(setIsLoading(false));
  };

  const handleFileDownload = async (file) => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${FA_ENDPOINT}/file/download/${file?.id}`, {
        responseType: 'blob',
      });

      const type = file?.file_name.split('.').pop();

      const typeMimeDictionary = {
        pdf: 'application/pdf',
      };

      if (Object?.keys(typeMimeDictionary)?.includes(type)) {
        const blobUrl = URL.createObjectURL(
          new Blob([response?.data], { type: typeMimeDictionary[type] })
        );
        setPdfUrl(blobUrl);
        setParam({ viewAttachmentDialogOpen: 'true' });
      } else {
        const link = document.createElement('a');
        link.href = URL.createObjectURL(response?.data);
        link.download = file?.file_name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);
      }
    } catch (error) {
      console.error('Error downloading file:', error);
      enqueueSnackbar('Something went wrong', { variant: 'error' });
    }
    dispatch(setIsLoading(false));
  };

  const handleDeleteFile = () => {
    if (!focusedFile?.id) return;

    dispatch(setIsLoading(true));
    axios.delete(`${FA_ENDPOINT}/file/${focusedFile?.id}`)
      .then((response) => {
        let statusVariant = 'error';
        let message = 'Failed';
        if (response.data.status === 'success' || response?.status === 204) {
          statusVariant = 'success';
          message = 'Success';
          // Refresh the file list
          fetchFileList();
        }
        
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      })
      .catch(() => {
        enqueueSnackbar('Something went wrong', { variant: 'error' });
      })
      .finally(() => {
        setFocusedFile({});
        setDeleteDialogOpen(false);
        dispatch(setIsLoading(false));
      });
  };

  // Data fetching
  const fetchData = async () => {
    if (!scenarioId) return;
    
    // Prevent concurrent API calls
    if (fetchData._isLoading) {
      return;
    }
    fetchData._isLoading = true;
    
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${FA_ENDPOINT}/opex/scenario_id/${scenarioId}`);

      if (response?.data?.data) {
        const processedData = response?.data?.data?.map((o) => ({
          ...o,
          opex_summary_total: o?.opex_summary_by_year_array?.reduce(
            (prev, curr) => prev + curr,
            0
          ),
        }));
        
        setOpexData(processedData);
        
        // Safely call onDataLoaded callback
        if (onDataLoaded && typeof onDataLoaded === 'function') {
          try {
            onDataLoaded(processedData);
          } catch (callbackError) {
            console.error('Error in onDataLoaded callback:', callbackError);
          }
        }
      }
    } catch (error) {
      setOpexData([]);
      console.error('Error fetching OPEX data:', error);
    } finally {
      dispatch(setIsLoading(false));
      fetchData._isLoading = false;
    }
  };

  // Initial data loading
  useEffect(() => {
    if (scenarioId) {
      fetchData();
      fetchFileList();
    }
  }, [scenarioId]);

  // Set breadcrumbs
  useEffect(() => {
    dispatch(
      setBreadCrumbsList([
        {
          linkTo: '/fa',
          label: 'FA List',
        },
        {
          linkTo: `/fa/project/${projectId}`,
          label: projectData?.name,
        },
        {
          linkTo: `/fa/project/${projectId}?scenarioId=${scenarioId}&tab=capex`,
          label: scenarioData?.name,
        },
        {
          linkTo: `/fa/project/${projectId}?scenarioId=${scenarioId}&tab=opex`,
          label: 'OPEX',
        },
      ])
    );
  }, [projectId, projectData?.name, scenarioData?.name, scenarioId]);

  // CSV upload template data
  const csvTemplateData = [{
    currency: 'myr',
    details: 'string',
    mrc_amount: 0,
    otc_amortise_amount: 0,
    otc_amount: 0,
    payment_term_in_days: 0,
    provider: 'string',
    leg_a: 'string',
    leg_b: 'string',
  }];

  return (
    <>
      <div className="flex flex-col gap-4">
        {showExportButton && !exportMode && (
          <div className="mb-4 flex w-full justify-end gap-4">
            <button
              type="button"
              className="bg-primary hover:bg-primary/90 flex items-center gap-1.5 whitespace-nowrap rounded-lg px-5 py-2 text-sm font-medium text-white shadow-sm transition-all duration-200"
              onClick={() => {
                setParam({ screenshotDialogOpen: true });
              }}
            >
              <svg
                className="h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              Export Details
            </button>
          </div>
        )}
        <div className="flex items-center justify-between mb-6">
          <p className="text-lg font-bold">OPEX Investment</p>
          {!IS_DISABLED && (
            <div className="flex items-center gap-3">
              <ExportExcelButton
                data={csvTemplateData}
                filename="opex_template.csv"
                buttonClassname="flex items-center gap-1.5 rounded-lg border border-gray-300 bg-white/90 px-3 py-1.5 text-xs font-medium text-gray-600 shadow-sm backdrop-blur-sm transition-all duration-200 hover:bg-white"
              >
                <svg
                  className="h-3.5 w-3.5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                Template
              </ExportExcelButton>

              <UploadCsvButton
                onUpload={async (data) => {
                  for (let i = 0; i < data?.length; i += 1) {
                    await handleDialogSubmit('post', data?.[i]);
                  }
                }}
              >
                <button
                  type="button"
                  className="flex items-center gap-1.5 whitespace-nowrap rounded-lg bg-gray-200 px-3 py-1.5 text-xs font-medium text-gray-700 shadow-sm transition-all duration-200 hover:bg-gray-300"
                >
                  <svg
                    className="h-3.5 w-3.5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
                    />
                  </svg>
                  Upload CSV
                </button>
              </UploadCsvButton>

              <button
                type="button"
                className="bg-primary hover:bg-primary/90 flex items-center gap-1.5 whitespace-nowrap rounded-lg px-3 py-1.5 text-xs font-medium text-white shadow-sm transition-all duration-200"
                onClick={() => handleClickOpenDialog(false)}
              >
                <svg className="h-3.5 w-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Add
              </button>
            </div>
          )}
        </div>
        
        {/* OPEX Table */}
          <OpexTable 
            opexData={opexData} 
            isDisabled={IS_DISABLED} 
            onRowClick={(row) => handleClickOpenDialog(true, row)}
            scenarioData={scenarioData}
          />

        {/* OPEX Summary Section */}
        <div className="flex flex-col gap-2 mt-10">
          <div className="flex items-center justify-between">
            <p className="text-lg font-bold">OPEX Summary</p>
          </div>
          <OpexSummaryTableWrapper opexData={opexData} scenarioData={scenarioData} />
        </div>

        {/* OPEX Documents Section */}
        <div className="flex flex-col gap-2 mt-10">
          <OpexDocumentsTable 
            fileList={fileList}
            isDisabled={IS_DISABLED}
            onUpload={handleFileUpload}
            onDownload={handleFileDownload}
            onDelete={(file) => {
              setFocusedFile(file);
              setDeleteDialogOpen(true);
            }}
          />
        </div>

        {/* Add/Edit Dialog */}
        <OpexFormDialog 
          open={dialogOpen}
          onClose={() => setDialogOpen(false)}
          editMode={editModeDialog}
          dialogData={dialogData}
          scenarioId={scenarioId}
          projectId={projectId}
          onSubmit={handleDialogSubmit}
          projectData={projectData}
          scenarioData={scenarioData}
        />

        {/* Delete Confirmation Dialog */}
        <DeleteConfirmationDialog 
          open={deleteDialogOpen}
          onClose={() => setDeleteDialogOpen(false)}
          onConfirm={handleDeleteFile}
          title="Delete Document?"
          itemName={focusedFile?.file_name || focusedFile?.name}
          itemType="file"
        />

        {/* Screenshot Dialog for export */}
        <ScreenshotDialog
          fileName={`OPEX-${projectData?.name}-${scenarioData?.name}.pdf`}
          isLandscape
        >
          <div className="flex w-full flex-col gap-4 p-4">
            <div className="bg-white w-full text-left text-gray-800 py-4 px-0 font-bold text-xl tracking-tight border-b border-gray-100">OPEX DETAILS</div>
            <OpexTable 
              opexData={opexData} 
              isDisabled={true} // Force disabled in export view
              onRowClick={() => {}} // No-op function
            />
            
            <div className="mt-6">
              <div className="bg-white w-full text-left text-gray-800 py-4 px-0 font-bold text-xl tracking-tight border-b border-gray-100">OPEX SUMMARY</div>
              <div className="mt-4">
                <OpexSummaryTableWrapper opexData={opexData} scenarioData={scenarioData} />
              </div>
            </div>
            
            <div className="mt-6">
              <div className="bg-white w-full text-left text-gray-800 py-4 px-0 font-bold text-xl tracking-tight border-b border-gray-100">SUPPORTING DOCUMENTS</div>
              <div className="overflow-x-auto mt-4">
                <table className="min-w-full bg-white border border-gray-100 rounded-lg overflow-hidden">
                  <thead>
                    <tr>
                      <th className="whitespace-nowrap bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700 border-b border-gray-200 text-left">
                        File Name
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {fileList.length > 0 ? (
                      fileList.map((file, index) => (
                        <tr key={index} className="hover:bg-gray-50 transition-colors duration-200">
                          <td className="text-left py-3 text-sm border-b border-gray-100 px-4 text-gray-700">
                            <div className="flex items-center">
                              <span className="truncate">{file.file_name}</span>
                            </div>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td className="text-center py-3 text-sm border-b border-gray-100 px-4 text-gray-700">
                          No documents have been uploaded
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </ScreenshotDialog>

        {/* PDF Viewer Dialog */}
        <Dialog
          open={query.viewAttachmentDialogOpen === 'true'}
          fullScreen
          onClose={() => setParam({ viewAttachmentDialogOpen: 'false' })}
        >
          <DocViewer
            documents={[
              {
                uri: pdfUrl,
              },
            ]}
            config={{
              header: {
                disableHeader: true,
                disableFileName: true,
                retainURLParams: false,
              },
              pdfVerticalScrollByDefault: true,
            }}
            pluginRenderers={DocViewerRenderers}
          />
        </Dialog>
      </div>
    </>
  );
};

AllOpex.propTypes = {
  scenarioId: PropTypes.string,
  showExportButton: PropTypes.bool,
  onDataLoaded: PropTypes.func,
  exportMode: PropTypes.bool,
};

export default AllOpex;
