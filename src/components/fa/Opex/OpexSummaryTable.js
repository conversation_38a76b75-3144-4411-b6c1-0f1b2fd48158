import React, { useEffect, Fragment } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { twMerge } from 'tailwind-merge';
import { checkAndReplaceNumberWithZero } from '../../../utils/shared';
import { getMetrix, calculateTotalYears, getOpexTypeLabel } from './OpexUtils';
import { getDisplayMode, generatePeriodLabels, calculateContractYears } from '../../../utils/faDisplayUtils';

// Ensure data has all required numeric fields properly formatted
const sanitizeOpexData = (data) => {
  const result = { ...data };

  // Ensure all numeric fields are properly converted to numbers
  result.otc_amount = Number(data?.otc_amount || 0);
  result.otc_amortise_amount = Number(data?.otc_amortise_amount || 0);
  result.mrc_amount = Number(data?.mrc_amount || 0);

  // For backwards compatibility, if otc_amortise_amount is 0 but otc_amount is set,
  // use the same value for amortization (common in many OPEX entries)
  if (result.otc_amortise_amount === 0 && result.otc_amount > 0) {
    result.otc_amortise_amount = result.otc_amount;
  }

  return result;
};


// Utility to format numbers with thousand separators and 0 decimals
const formatNumber = (num) => {
  if (isNaN(num)) return '-';
  return Number(num).toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 0 });
};

// Simple OPEX Summary Table
const OpexSummaryTable = ({ opexData, scenarioData }) => {

  // Styles
  const headerCellStyle = 'whitespace-nowrap bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700 border-b border-gray-200';
  const bodyCellStyle = 'text-center py-3 text-sm border-b border-gray-100 px-4 text-gray-700';
  const numberCellStyle = twMerge(bodyCellStyle, 'text-right font-mono');
  const numberCellStyleBold = twMerge(bodyCellStyle, 'text-right font-bold text-gray-800');
  const numberCellStyleTotal = twMerge(bodyCellStyle, 'bg-blue-50 text-right font-bold text-blue-900 border-b border-blue-200');

  if (!opexData || opexData.length === 0) {
    return (
      <div className="overflow-x-auto scrollbar">
        <table className="min-w-full bg-white border border-gray-100 rounded-lg overflow-hidden">
          <tbody>
            <tr>
              <td className={bodyCellStyle}>No OPEX data available</td>
            </tr>
          </tbody>
        </table>
      </div>
    );
  }

  // Calculate year gap between project start and RFS date (consistent with Revenue component)
  const calculateYearDifference = () => {
    if (!scenarioData?.project_start_date || !scenarioData?.expected_rfs_date) {
      return 0;
    }

    const startYear = moment(scenarioData.project_start_date).year();
    const rfsYear = moment(scenarioData.expected_rfs_date).year();
    const yearDifference = Math.max(0, rfsYear - startYear);


    return yearDifference;
  };

  const yearDifference = calculateYearDifference();
  const contractMonths = parseInt(scenarioData?.contract_period_in_month, 10) || 120;
  const contractYears = calculateContractYears(scenarioData?.expected_rfs_date, contractMonths);
  const totalYears = yearDifference + contractYears;
  const totalPeriods = totalYears; // Use calculated total years instead of getDisplayMode


  // Helper to get metrics for each entry
  const getMetrics = (entry) => getMetrix(entry, scenarioData);

  // Generate period labels (always Y0, Y1, etc.)
  const periodLabels = generatePeriodLabels(false, totalPeriods);

  // Grand totals - use corrected totalPeriods (now properly shows Y0, Y1 for 12-month mid-year contracts)
  const grandOtc = Array(totalPeriods).fill(0);
  const grandAmortise = Array(totalPeriods).fill(0);
  const grandMrc = Array(totalPeriods).fill(0);

  return (
    <div className="overflow-x-auto scrollbar">
      <table className="min-w-full bg-white text-black">
        <tbody>
          {/* Header row */}
          <tr>
            <td className={twMerge(headerCellStyle, 'w-[200px] overflow-hidden')}>Provider</td>
            <td className={twMerge(headerCellStyle, 'w-[200px] overflow-hidden')}>Details</td>
            <td className={twMerge(headerCellStyle, 'w-[150px] overflow-hidden')}>Entry Type</td>
            {periodLabels.map((label, i) => (
              <td key={i} className={twMerge(headerCellStyle, 'text-right')}>{label}</td>
            ))}
            <td className={twMerge(headerCellStyle, 'w-[200px] overflow-hidden text-right')}>Total</td>
          </tr>

          {/* Loop through all OPEX entries */}
          {opexData.map((entry, idx) => {
            const sanitized = sanitizeOpexData(entry);
            const metrics = getMetrics(sanitized);

            // First calculate yearly arrays
            const yearlyAmortiseArray = Array(totalYears).fill(0).map((_, i) => metrics.amortiseArray[i] || 0);
            const yearlyMrcArray = Array(totalYears).fill(0).map((_, i) => metrics.mrcArray[i] || 0);
            const yearlyOtcRow = Array(totalYears).fill(0);
            yearlyOtcRow[0] = sanitized.otc_amount;

            // Always use yearly arrays but limit to totalPeriods
            const amortiseArray = yearlyAmortiseArray.slice(0, totalPeriods);
            const mrcArray = yearlyMrcArray.slice(0, totalPeriods);
            const otcRow = yearlyOtcRow.slice(0, totalPeriods);

            // Add to grand totals
            for (let i = 0; i < totalPeriods; i++) {
              grandOtc[i] += otcRow[i] || 0;
              grandAmortise[i] += amortiseArray[i] || 0;
              grandMrc[i] += mrcArray[i] || 0;
            }

            return (
              <Fragment key={idx}>
                {/* OTC Row */}
                <tr>
                  <td className={bodyCellStyle} rowSpan={3}>{sanitized.provider}</td>
                  <td className={bodyCellStyle} rowSpan={3}>{sanitized.details}</td>
                  <td className={bodyCellStyle}>OTC</td>
                  {otcRow.map((amount, i) => (
                    <td key={i} className={numberCellStyle}>{formatNumber(amount)}</td>
                  ))}
                  <td className={numberCellStyleTotal}>{formatNumber(sanitized.otc_amount)}</td>
                </tr>
                {/* OTC Amortise Row */}
                <tr>
                  <td className={bodyCellStyle}>OTC Amortise</td>
                  {amortiseArray.map((amount, i) => (
                    <td key={i} className={numberCellStyle}>{formatNumber(amount)}</td>
                  ))}
                  <td className={numberCellStyleTotal}>
                    {formatNumber(amortiseArray.reduce((a, b) => a + b, 0))}
                  </td>
                </tr>
                {/* ARC Row */}
                <tr>
                  <td className={bodyCellStyle}>ARC</td>
                  {mrcArray.map((amount, i) => (
                    <td key={i} className={numberCellStyle}>{formatNumber(amount)}</td>
                  ))}
                  <td className={numberCellStyleTotal}>
                    {formatNumber(mrcArray.reduce((a, b) => a + b, 0))}
                  </td>
                </tr>
                {/* Subtotal Row */}
                <tr className="bg-gray-50">
                  <td colSpan={3} className={twMerge(bodyCellStyle, 'text-center font-semibold text-gray-800 border-b border-gray-200')}>Total</td>
                  {Array.from({ length: totalPeriods }, (_, i) => {
                    const periodTotal =
                      (otcRow[i] || 0) +
                      (amortiseArray[i] || 0) +
                      (mrcArray[i] || 0);
                    return (
                      <td key={i} className={numberCellStyleBold}>{formatNumber(periodTotal)}</td>
                    );
                  })}
                  <td className={numberCellStyleTotal}>
                    {formatNumber(sanitized.otc_amount +
                      amortiseArray.reduce((a, b) => a + b, 0) +
                      mrcArray.reduce((a, b) => a + b, 0))}
                  </td>
                </tr>
              </Fragment>
            );
          })}

          {/* Grand Total Row */}
          <tr className="bg-blue-50">
            <td colSpan={3} className={twMerge(bodyCellStyle, 'text-center font-bold text-blue-900 border-b border-blue-200')}>Grand Total</td>
            {Array.from({ length: totalPeriods }, (_, i) => {
              const periodTotal =
                (grandOtc[i] || 0) +
                (grandAmortise[i] || 0) +
                (grandMrc[i] || 0);
              return (
                <td key={i} className={numberCellStyleBold}>{formatNumber(periodTotal)}</td>
              );
            })}
            <td className={numberCellStyleTotal}>
              {formatNumber(grandOtc.reduce((a, b) => a + b, 0) +
                grandAmortise.reduce((a, b) => a + b, 0) +
                grandMrc.reduce((a, b) => a + b, 0))}
            </td>
          </tr>
        </tbody>
      </table>
      <div className="mt-2 text-xs text-gray-600 italic">
        * OPEX Summary shows amounts distributed across calendar years using daily proration
      </div>
    </div>
  );
};

OpexSummaryTable.propTypes = {
  opexData: PropTypes.array.isRequired,
  scenarioData: PropTypes.object,
};

export default OpexSummaryTable;
