import React from 'react';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';

class FAErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error('FA Module Error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Report to error tracking service in production
    if (process.env.NODE_ENV === 'production') {
      // Add your error reporting service here
      console.error('Production error in FA Module:', {
        error: error.toString(),
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        url: window.location.href,
        userAgent: navigator.userAgent
      });
    }
  }

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1
    }));
  };

  handleGoHome = () => {
    window.location.href = '/fa';
  };

  render() {
    if (this.state.hasError) {
      const { error, errorInfo, retryCount } = this.state;
      const isDevelopment = process.env.NODE_ENV === 'development';
      
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <div className="max-w-2xl w-full bg-white rounded-lg shadow-lg border border-gray-200">
            {/* Header */}
            <div className="border-b border-gray-200 p-6">
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0">
                  <AlertTriangle className="h-8 w-8 text-red-500" />
                </div>
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">
                    Financial Analysis Error
                  </h1>
                  <p className="text-sm text-gray-600 mt-1">
                    Something went wrong in the FA module
                  </p>
                </div>
              </div>
            </div>

            {/* Error Message */}
            <div className="p-6 space-y-4">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h3 className="text-sm font-medium text-red-800 mb-2">
                  Error Details
                </h3>
                <p className="text-sm text-red-700">
                  {error?.message || 'An unexpected error occurred'}
                </p>
                {retryCount > 0 && (
                  <p className="text-xs text-red-600 mt-2">
                    Retry attempts: {retryCount}
                  </p>
                )}
              </div>

              {/* Development Info */}
              {isDevelopment && error && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h3 className="text-sm font-medium text-gray-800 mb-2 flex items-center gap-2">
                    <Bug className="h-4 w-4" />
                    Development Info
                  </h3>
                  <div className="space-y-2">
                    <div>
                      <p className="text-xs font-medium text-gray-700">Error:</p>
                      <pre className="text-xs text-gray-600 bg-white p-2 rounded border overflow-x-auto">
                        {error.toString()}
                      </pre>
                    </div>
                    {error.stack && (
                      <div>
                        <p className="text-xs font-medium text-gray-700">Stack:</p>
                        <pre className="text-xs text-gray-600 bg-white p-2 rounded border overflow-x-auto max-h-40">
                          {error.stack}
                        </pre>
                      </div>
                    )}
                    {errorInfo?.componentStack && (
                      <div>
                        <p className="text-xs font-medium text-gray-700">Component Stack:</p>
                        <pre className="text-xs text-gray-600 bg-white p-2 rounded border overflow-x-auto max-h-40">
                          {errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Suggestions */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="text-sm font-medium text-blue-800 mb-2">
                  What you can try:
                </h3>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• Refresh the page to retry loading</li>
                  <li>• Check your internet connection</li>
                  <li>• Clear your browser cache and cookies</li>
                  <li>• Try accessing a different FA project</li>
                  {retryCount > 2 && (
                    <li>• Contact IT support if the problem persists</li>
                  )}
                </ul>
              </div>
            </div>

            {/* Actions */}
            <div className="border-t border-gray-200 p-6">
              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={this.handleRetry}
                  className="flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex-1"
                >
                  <RefreshCw className="h-4 w-4" />
                  Try Again
                </button>
                <button
                  onClick={this.handleGoHome}
                  className="flex items-center justify-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200 flex-1"
                >
                  <Home className="h-4 w-4" />
                  Go to FA List
                </button>
              </div>
              
              {/* Help Text */}
              <p className="text-xs text-gray-500 mt-4 text-center">
                If this error persists, please contact the development team with the error details above.
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default FAErrorBoundary;