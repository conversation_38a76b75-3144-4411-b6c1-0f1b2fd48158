// Next, React, Tw
import { useRef } from 'react';
import { useDispatch } from 'react-redux';

// Packages
import Papa from 'papaparse';
import * as R from 'ramda';
import * as yup from 'yup';
import PropTypes from 'prop-types';

// Others
import axios from '../../utils/axios';
import { FA_ENDPOINT } from '../../utils/fa';
import { useSnackbar } from '../Shared/snackbar';
import { setIsLoading } from '../../utils/store/loadingReducer';

const UploadBulkButton = ({ type, callback }) => {
  // Standard
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();

  // Others
  const fileInputRef = useRef(null);

  const schema = yup.object({
    type: yup.string().required('Please provide type.')?.default(type),
    value: yup.string().required('Please provide value.'),
  });

  const saveNewConfig = async (data) => {
    let payload;
    try {
      payload = await schema.validate(data, { abortEarly: false });
    } catch (error) {
      enqueueSnackbar(error.errors, {
        variant: 'error',
        anchorOrigin: {
          vertical: 'top',
          horizontal: 'center',
        },
      });
      return;
    }
    dispatch(setIsLoading(true));
    try {
      await axios.post(`${FA_ENDPOINT}/config`, payload);
    } catch {
      /* empty */
    }
    dispatch(setIsLoading(false));
  };

  const processData = async (data) => {
    const getColumn = () => {
      const temp = {
        bandwidth: 'SPEED',
        sla_cos: 'COS',
      };
      return temp[type];
    };

    const uniqueUploadedConfigs = R.compose(R.uniq, R.pluck(getColumn()))(data);

    const manipulateData = (data_) => {
      data_ = data_?.trim();
      if (type === 'bandwidth') {
        data_ = data_?.toLowerCase();
        const temp = Number(data_?.split(' ')[0]);
        if (data_?.includes('mbps')) {
          return temp / 1000;
        }
        return temp;
      }
      return data_;
    };

    // Save New Config
    for (let i = 0; i < uniqueUploadedConfigs.length; i += 1) {
      //   eslint-disable-next-line
      await saveNewConfig({
        value: manipulateData(uniqueUploadedConfigs[i]),
      });
    }
    if (callback) callback();
  };

  return (
    <>
      <button
        type="button"
        className="text-md bg-fa rounded-lg px-4 py-2 font-semibold text-white"
        onClick={() => fileInputRef?.current?.click()}
      >
        Upload Bulk
      </button>
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={(event) => {
          const extension =
            event?.target?.files[0]?.name?.split('.')[
              event.target.files[0].name.split('.').length - 1
            ];
          if (extension !== 'csv') {
            enqueueSnackbar('Please upload a CSV.', {
              variant: 'error',
              anchorOrigin: {
                vertical: 'top',
                horizontal: 'center',
              },
            });
            return;
          }
          Papa.parse(event?.target?.files[0], {
            header: true,
            complete: (results) => processData(results?.data),
          });
        }}
      />
    </>
  );
};

UploadBulkButton.propTypes = {
  type: PropTypes.string,
  callback: PropTypes.func,
};

export default UploadBulkButton;
