import { twMerge } from 'tailwind-merge';

const StatusBadge = ({ status }) => {
  const normalizedStatus = status?.toLowerCase() || 'unknown';
  let badgeClass = '';

  // Determine badge styling based on status
  if (normalizedStatus === 'draft') {
    badgeClass = 'bg-blue-100 text-blue-800 border border-blue-200';
  } else if (normalizedStatus === 'pending approver') {
    badgeClass = 'bg-amber-100 text-amber-800 border border-amber-200';
  } else if (normalizedStatus === 'completed') {
    badgeClass = 'bg-green-100 text-green-800 border border-green-200';
  } else if (normalizedStatus.includes('endorsement') || normalizedStatus.includes('endorser')) {
    badgeClass = 'bg-purple-100 text-purple-800 border border-purple-200';
  } else if (normalizedStatus.includes('sitting')) {
    badgeClass = 'bg-indigo-100 text-indigo-800 border border-indigo-200';
  } else if (normalizedStatus.includes('rejected') || normalizedStatus.includes('cancelled')) {
    badgeClass = 'bg-red-100 text-red-800 border border-red-200';
  } else if (normalizedStatus.includes('pending')) {
    badgeClass = 'bg-amber-100 text-amber-800 border border-amber-200';
  } else {
    badgeClass = 'bg-gray-100 text-gray-700 border border-gray-200';
  }

  return (
    <span className={twMerge('inline-block rounded px-2.5 py-0.5 text-xs font-medium', badgeClass)}>
      {status?.toUpperCase()}
    </span>
  );
};

export default StatusBadge;
