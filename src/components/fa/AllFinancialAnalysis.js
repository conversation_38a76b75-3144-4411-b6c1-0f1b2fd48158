// Next, React
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';

// Packages
import PropTypes from 'prop-types';

// Components
import FinancialAnalysis from './FinancialAnalysis';
import { CollapsibleTableRowGroup, SubTableRow } from './ui/EnhancedTable';

// Others
import axios from '../../utils/axios';
import { FA_ENDPOINT } from '../../utils/fa';
import { setIsLoading } from '../../utils/store/loadingReducer';

// Simple styling following OPEX pattern
const headerCellStyle = 'whitespace-nowrap bg-gray-50 px-4 py-3 text-sm font-medium text-gray-700 border-b border-gray-200';
const bodyCellStyle = 'text-center py-3 text-sm border-b border-gray-100 px-4 text-gray-700';
const numberCellStyle = 'text-right py-3 text-sm border-b border-gray-100 px-4 font-mono text-gray-700';
const subitemStyle = 'whitespace-nowrap bg-gray-50 px-4 py-3 text-sm font-medium text-gray-600 border-b border-gray-200 text-left pl-8';
const highlightStyle = 'bg-blue-50 font-bold text-center py-3 text-sm border-b border-blue-200 px-4 text-blue-900';

const AllFinancialAnalysis = ({ filterByEndorsed = false, showApproverEndorsementCell = true }) => {
  // Standard
  const { query } = useRouter();
  const { projectId } = query;
  const dispatch = useDispatch();
  const { projectData } = useSelector((state) => state.fa);

  // Table
  const [tableData, setTableData] = useState([]);
  const [cogsTotals, setCogsTotals] = useState({});
  
  // Enhanced cell styles for better visual hierarchy
  
  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${FA_ENDPOINT}/scenario/project_id/${projectId}`);

      if (response?.data?.data) {
        let temp = response?.data?.data;
        if (filterByEndorsed) temp = temp?.filter((o) => o?.scenario_endorsed);
        setTableData(temp);
        
        // Check COGS totals for each scenario
        const cogsCheck = {};
        for (const scenario of temp) {
          try {
            // Fetch OPEX data for this scenario to check for COGS items
            const opexResponse = await axios.get(`${FA_ENDPOINT}/opex/scenario_id/${scenario.id}`);
            const opexData = opexResponse?.data?.data || [];
            
            // Check if there are any COGS items (provider = 'cogs')
            const cogsItems = opexData.filter(item => item?.provider?.toLowerCase() === 'cogs');
            const hasCogsData = cogsItems.some(item => 
              (item?.opex_summary_by_year_array || []).some(yearAmount => yearAmount > 0)
            );
            
            cogsCheck[scenario.id] = hasCogsData;
          } catch {
            cogsCheck[scenario.id] = false;
          }
        }
        setCogsTotals(cogsCheck);
      }
    } catch {
      setTableData([]);
      setCogsTotals({});
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (!projectId) return;
    fetchData();
  }, [projectId]);

  return (
    <div className="w-full">
      {/* Simple table wrapper following OPEX pattern */}
      <div className="overflow-x-auto mb-10">
        <table className="bg-white text-black border-collapse table-fixed min-w-full">
          <tbody>
              {/* Contract Terms Header */}
              <tr>
                <td className={headerCellStyle}>
                  Contract Terms
                </td>
                {tableData?.map((scenario, i) => (
                  <td key={i} className={bodyCellStyle}>
                    {scenario?.contract_period_in_month} months
                  </td>
                ))}
              </tr>
              
              {/* Approver Endorsement */}
              {showApproverEndorsementCell && (
                <tr>
                  <td className={headerCellStyle}>
                    Approver Endorsement
                  </td>
                  {tableData?.map((scenario, i) => (
                    <td key={i} className={bodyCellStyle}>
                      <FinancialAnalysis
                        scenarioId={scenario?.id}
                        isSummary
                        showApproverEndorsementCell={false}
                        cellType="approver"
                      />
                    </td>
                  ))}
                </tr>
              )}
              
              {/* FA ID */}
              <tr>
                <td className={headerCellStyle}>
                  FA ID
                </td>
                {tableData?.map((scenario, i) => (
                  <td key={i} className={bodyCellStyle}>
                    {scenario?.scenario_id}
                  </td>
                ))}
              </tr>
              
              {/* Scenario Name */}
              <tr>
                <td className={headerCellStyle}>
                  Scenario
                </td>
                {tableData?.map((scenario, i) => (
                  <td key={i} className={bodyCellStyle}>
                    {scenario?.name}
                  </td>
                ))}
              </tr>
              
              {/* Currency */}
              <tr>
                <td className={headerCellStyle}>Currency</td>
                {tableData?.map((scenario, i) => (
                  <td key={i} className={bodyCellStyle}>
                    <FinancialAnalysis
                      scenarioId={scenario?.id}
                      isSummary
                      showApproverEndorsementCell={false}
                      cellType="currency"
                    />
                  </td>
                ))}
              </tr>
              
              {/* Profit and Loss Section - simple text instead of gradient header */}
              <tr>
                <td colSpan={1 + (tableData?.length || 0)} className={highlightStyle}>
                  PROFIT AND LOSS
                </td>
              </tr>
              
              {/* Revenue - Collapsible */}
              <CollapsibleTableRowGroup
                title="Revenue"
                defaultOpen={true}
                headerCells={tableData?.map((scenario, i) => (
                  <FinancialAnalysis
                    key={i}
                    scenarioId={scenario?.id}
                    isSummary
                    showApproverEndorsementCell={false}
                    cellType="revenue"
                  />
                ))}
              >
                {/* OTC */}
                <SubTableRow title="OTC">
                  {tableData?.map((scenario, i) => (
                    <td key={i} className={numberCellStyle}>
                      <FinancialAnalysis
                        scenarioId={scenario?.id}
                        isSummary
                        showApproverEndorsementCell={false}
                        cellType="otc"
                      />
                    </td>
                  ))}
                </SubTableRow>
                
                {/* OTC Amortised */}
                <SubTableRow title="OTC Amortised">
                  {tableData?.map((scenario, i) => (
                    <td key={i} className={numberCellStyle}>
                      <FinancialAnalysis
                        scenarioId={scenario?.id}
                        isSummary
                        showApproverEndorsementCell={false}
                        cellType="otc_amortised"
                      />
                    </td>
                  ))}
                </SubTableRow>
                
                {/* ARC */}
                <SubTableRow title="ARC" isLast={true}>
                  {tableData?.map((scenario, i) => (
                    <td key={i} className={numberCellStyle}>
                      <FinancialAnalysis
                        scenarioId={scenario?.id}
                        isSummary
                        showApproverEndorsementCell={false}
                        cellType="arc"
                      />
                    </td>
                  ))}
                </SubTableRow>
              </CollapsibleTableRowGroup>
              
              {/* COGS Section - simple header */}
              <tr>
                <td colSpan={1 + (tableData?.length || 0)} className={highlightStyle}>
                  COGS
                </td>
              </tr>
              
              {/* COGS Total */}
              <tr>
                <td className={headerCellStyle}>COGS</td>
                {tableData?.map((scenario, i) => (
                  <td key={i} className={numberCellStyle}>
                    <FinancialAnalysis
                      scenarioId={scenario?.id}
                      isSummary
                      showApproverEndorsementCell={false}
                      cellType="cogs"
                    />
                  </td>
                ))}
              </tr>
              
              {/* OPEX Section - simple header */}
              <tr>
                <td colSpan={1 + (tableData?.length || 0)} className={highlightStyle}>
                  OPEX
                </td>
              </tr>
              
              {/* OPEX - Collapsible */}
              <CollapsibleTableRowGroup
                title="OPEX"
                defaultOpen={true}
                headerCells={tableData?.map((scenario, i) => (
                  <FinancialAnalysis
                    key={i}
                    scenarioId={scenario?.id}
                    isSummary
                    showApproverEndorsementCell={false}
                    cellType="opex"
                  />
                ))}
              >
                {/* O&M */}
                <SubTableRow title="O&M">
                  {tableData?.map((scenario, i) => (
                    <td key={i} className={numberCellStyle}>
                      <FinancialAnalysis
                        scenarioId={scenario?.id}
                        isSummary
                        showApproverEndorsementCell={false}
                        cellType="om"
                      />
                    </td>
                  ))}
                </SubTableRow>
                
                {/* Outpayment */}
                <SubTableRow title="Outpayment">
                  {tableData?.map((scenario, i) => (
                    <td key={i} className={numberCellStyle}>
                      <FinancialAnalysis
                        scenarioId={scenario?.id}
                        isSummary
                        showApproverEndorsementCell={false}
                        cellType="outpayment"
                      />
                    </td>
                  ))}
                </SubTableRow>
                
                {/* USP */}
                <SubTableRow title="USP" isLast={true}>
                  {tableData?.map((scenario, i) => (
                    <td key={i} className={numberCellStyle}>
                      <FinancialAnalysis
                        scenarioId={scenario?.id}
                        isSummary
                        showApproverEndorsementCell={false}
                        cellType="usp"
                      />
                    </td>
                  ))}
                </SubTableRow>
              </CollapsibleTableRowGroup>
              
                          {/* EBITDA */}
            <tr>
              <td className={headerCellStyle}>
                EBITDA
              </td>
              {tableData?.map((scenario, i) => (
                <td key={i} className={numberCellStyle}>
                  <FinancialAnalysis
                    scenarioId={scenario?.id}
                    isSummary
                    showApproverEndorsementCell={false}
                    cellType="ebitda"
                  />
                </td>
              ))}
            </tr>
              
              {/* DEPRECIATION */}
              <tr>
                <td className={headerCellStyle}>DEPRECIATION</td>
                {tableData?.map((scenario, i) => (
                  <td key={i} className={numberCellStyle}>
                    <FinancialAnalysis
                      scenarioId={scenario?.id}
                      isSummary
                      showApproverEndorsementCell={false}
                      cellType="depreciation"
                    />
                  </td>
                ))}
              </tr>
              
                          {/* EBIT */}
            <tr>
              <td className={headerCellStyle}>
                EBIT
              </td>
              {tableData?.map((scenario, i) => (
                <td key={i} className={numberCellStyle}>
                  <FinancialAnalysis
                    scenarioId={scenario?.id}
                    isSummary
                    showApproverEndorsementCell={false}
                    cellType="ebit"
                  />
                </td>
              ))}
            </tr>
            
            {/* EBIT Margin */}
            <tr>
              <td className={headerCellStyle}>EBIT Margin</td>
              {tableData?.map((scenario, i) => (
                <td key={i} className={numberCellStyle}>
                  <FinancialAnalysis
                    scenarioId={scenario?.id}
                    isSummary
                    showApproverEndorsementCell={false}
                    cellType="ebit_margin"
                  />
                </td>
              ))}
            </tr>
              
              {/* TAX */}
              <tr>
                <td className={headerCellStyle}>TAX</td>
                {tableData?.map((scenario, i) => (
                  <td key={i} className={numberCellStyle}>
                    <FinancialAnalysis
                      scenarioId={scenario?.id}
                      isSummary
                      showApproverEndorsementCell={false}
                      cellType="tax"
                    />
                  </td>
                ))}
              </tr>
              
                          {/* PAT */}
            <tr>
              <td className={headerCellStyle}>
                PAT
              </td>
              {tableData?.map((scenario, i) => (
                <td key={i} className={numberCellStyle}>
                  <FinancialAnalysis
                    scenarioId={scenario?.id}
                    isSummary
                    showApproverEndorsementCell={false}
                    cellType="pat"
                  />
                </td>
              ))}
            </tr>
            
            {/* PAT Margin */}
            <tr>
              <td className={headerCellStyle}>PAT Margin</td>
              {tableData?.map((scenario, i) => (
                <td key={i} className={numberCellStyle}>
                  <FinancialAnalysis
                    scenarioId={scenario?.id}
                    isSummary
                    showApproverEndorsementCell={false}
                    cellType="pat_margin"
                  />
                </td>
              ))}
            </tr>
              
              {/* Cash Flow Section - simple header */}
              <tr>
                <td colSpan={1 + (tableData?.length || 0)} className={highlightStyle}>
                  CASH FLOW
                </td>
              </tr>
              
              {/* COGS - Collapsible - Only show if any scenario has COGS data */}
              {Object.values(cogsTotals).some(hasData => hasData) && (
                <CollapsibleTableRowGroup
                  title="COGS"
                  defaultOpen={true}
                  headerCells={tableData?.map((scenario, i) => (
                    <FinancialAnalysis
                      key={i}
                      scenarioId={scenario?.id}
                      isSummary
                      showApproverEndorsementCell={false}
                      cellType="cogs"
                    />
                  ))}
                >
                  {/* CAPEX */}
                  <SubTableRow title="CAPEX">
                    {tableData?.map((scenario, i) => (
                      <td key={i} className={numberCellStyle}>
                        <FinancialAnalysis
                          scenarioId={scenario?.id}
                          isSummary
                          showApproverEndorsementCell={false}
                          cellType="capex_cashflow"
                        />
                      </td>
                    ))}
                  </SubTableRow>
                  
                  {/* OTC */}
                  <SubTableRow title="OTC" isLast={true}>
                    {tableData?.map((scenario, i) => (
                      <td key={i} className={numberCellStyle}>
                        <FinancialAnalysis
                          scenarioId={scenario?.id}
                          isSummary
                          showApproverEndorsementCell={false}
                          cellType="otc_cashflow"
                        />
                      </td>
                    ))}
                  </SubTableRow>
                </CollapsibleTableRowGroup>
              )}
              
              {/* Operating Cash Flow Section - simple header */}
              <tr>
                <td colSpan={1 + (tableData?.length || 0)} className={highlightStyle}>
                  OPERATING CASH FLOW
                </td>
              </tr>
              
              {/* Net Income */}
              <tr>
                <td className={headerCellStyle}>Net Income</td>
                {tableData?.map((scenario, i) => (
                  <td key={i} className={numberCellStyle}>
                    <FinancialAnalysis
                      scenarioId={scenario?.id}
                      isSummary
                      showApproverEndorsementCell={false}
                      cellType="net_income"
                    />
                  </td>
                ))}
              </tr>
              
              {/* Add Back Depreciation */}
              <tr>
                <td className={headerCellStyle}>Add Back Depreciation</td>
                {tableData?.map((scenario, i) => (
                  <td key={i} className={numberCellStyle}>
                    <FinancialAnalysis
                      scenarioId={scenario?.id}
                      isSummary
                      showApproverEndorsementCell={false}
                      cellType="add_back_depreciation"
                    />
                  </td>
                ))}
              </tr>
              
              {/* Add Back OTC Amortised */}
              <tr>
                <td className={headerCellStyle}>Add Back OTC Amortised</td>
                {tableData?.map((scenario, i) => (
                  <td key={i} className={numberCellStyle}>
                    <FinancialAnalysis
                      scenarioId={scenario?.id}
                      isSummary
                      showApproverEndorsementCell={false}
                      cellType="add_back_otc_amortised"
                    />
                  </td>
                ))}
              </tr>
              
              {/* Nett Working Capital */}
              <tr>
                <td className={headerCellStyle}>Nett Working Capital</td>
                {tableData?.map((scenario, i) => (
                  <td key={i} className={numberCellStyle}>
                    <FinancialAnalysis
                      scenarioId={scenario?.id}
                      isSummary
                      showApproverEndorsementCell={false}
                      cellType="nett_working_capital"
                    />
                  </td>
                ))}
              </tr>
              
              {/* Nett Cashflow After Contract End */}
              <tr>
                <td className={headerCellStyle}>Nett Cashflow After Contract End</td>
                {tableData?.map((scenario, i) => (
                  <td key={i} className={numberCellStyle}>
                    <FinancialAnalysis
                      scenarioId={scenario?.id}
                      isSummary
                      showApproverEndorsementCell={false}
                      cellType="nett_cashflow_after_contract_end"
                    />
                  </td>
                ))}
              </tr>
              
                          {/* Operating Cash Flow */}
            <tr>
              <td className={headerCellStyle}>
                Operating Cash Flow
              </td>
              {tableData?.map((scenario, i) => (
                <td key={i} className={numberCellStyle}>
                  <FinancialAnalysis
                    scenarioId={scenario?.id}
                    isSummary
                    showApproverEndorsementCell={false}
                    cellType="operating_cash_flow"
                  />
                </td>
              ))}
            </tr>
              
              {/* CAPEX */}
              <tr>
                <td className={headerCellStyle}>CAPEX</td>
                {tableData?.map((scenario, i) => (
                  <td key={i} className={numberCellStyle}>
                    <FinancialAnalysis
                      scenarioId={scenario?.id}
                      isSummary
                      showApproverEndorsementCell={false}
                      cellType="capex"
                    />
                  </td>
                ))}
              </tr>
              
                          {/* Nett Cash Flow */}
            <tr>
              <td className={headerCellStyle}>
                Nett Cash Flow
              </td>
              {tableData?.map((scenario, i) => (
                <td key={i} className={numberCellStyle}>
                  <FinancialAnalysis
                    scenarioId={scenario?.id}
                    isSummary
                    showApproverEndorsementCell={false}
                    cellType="nett_cash_flow"
                  />
                </td>
              ))}
            </tr>
              
              {/* Opening Cash */}
              <tr>
                <td className={headerCellStyle}>Opening Cash</td>
                {tableData?.map((scenario, i) => (
                  <td key={i} className={numberCellStyle}>
                    <FinancialAnalysis
                      scenarioId={scenario?.id}
                      isSummary
                      showApproverEndorsementCell={false}
                      cellType="opening_cash"
                    />
                  </td>
                ))}
              </tr>
              
              {/* Closing Cash */}
              <tr>
                <td className={headerCellStyle}>Closing Cash</td>
                {tableData?.map((scenario, i) => (
                  <td key={i} className={numberCellStyle}>
                    <FinancialAnalysis
                      scenarioId={scenario?.id}
                      isSummary
                      showApproverEndorsementCell={false}
                      cellType="closing_cash"
                    />
                  </td>
                ))}
              </tr>
              
              {/* Performance Indicators Section - simple header */}
              <tr>
                <td colSpan={1 + (tableData?.length || 0)} className={highlightStyle}>
                  PERFORMANCE INDICATORS
                </td>
              </tr>
              
                          {/* IRR */}
            <tr>
              <td className={headerCellStyle}>
                IRR
              </td>
              {tableData?.map((scenario, i) => (
                <td key={i} className={numberCellStyle}>
                  <FinancialAnalysis
                    scenarioId={scenario?.id}
                    isSummary
                    showApproverEndorsementCell={false}
                    cellType="irr"
                  />
                </td>
              ))}
            </tr>
            
            {/* NPV */}
            <tr>
              <td className={headerCellStyle}>
                NPV
              </td>
              {tableData?.map((scenario, i) => (
                <td key={i} className={numberCellStyle}>
                  <FinancialAnalysis
                    scenarioId={scenario?.id}
                    isSummary
                    showApproverEndorsementCell={false}
                    cellType="npv"
                  />
                </td>
              ))}
            </tr>
            
            {/* Payback Period (Years) */}
            <tr>
              <td className={headerCellStyle}>Payback Period (Years)</td>
              {tableData?.map((scenario, i) => (
                <td key={i} className={numberCellStyle}>
                  <FinancialAnalysis
                    scenarioId={scenario?.id}
                    isSummary
                    showApproverEndorsementCell={false}
                    cellType="payback_period"
                  />
                </td>
              ))}
            </tr>
            
            {/* DPP (Years) */}
            <tr>
              <td className={headerCellStyle}>DPP (Years)</td>
              {tableData?.map((scenario, i) => (
                <td key={i} className={numberCellStyle}>
                  <FinancialAnalysis
                    scenarioId={scenario?.id}
                    isSummary
                    showApproverEndorsementCell={false}
                    cellType="dpp"
                  />
                </td>
              ))}
            </tr>
            </tbody>
        </table>
      </div>
    </div>
  );
};

AllFinancialAnalysis.propTypes = {
  filterByEndorsed: PropTypes.bool,
  showApproverEndorsementCell: PropTypes.bool,
};

export default AllFinancialAnalysis;
