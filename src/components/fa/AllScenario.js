// Next, React, Tw
import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { Edit, ContentCopy } from '@mui/icons-material';

// Packages
import * as yup from 'yup';
import moment from 'moment';

// Components
import { TextInput, SelectInput } from '../Shared/CustomInput';
import UserPopup from '../Shared/UserPopup';

// Others
import { useSnackbar } from '../Shared/snackbar';
import axios from '../../utils/axios';
import { FA_ENDPOINT } from '../../utils/fa';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { fetchAllScenarios } from '../../utils/store/faReducer';

const AllScenario = () => {
  // Standard
  const { enqueueSnackbar } = useSnackbar();
  const { query } = useRouter();
  const { projectId } = query;
  const dispatch = useDispatch();
  const { user } = useAuthContext();
  const { isAdmin } = useModuleRoleContext();
  const { projectData, allScenarios } = useSelector((state) => state?.fa);
  const { setParam } = useParamContext();

  // Allow editing if user is admin OR if user created the project
  const canEdit = isAdmin || (projectData?.created_by_staff_id === user?.staff_id);
  const IS_DISABLED = !canEdit || projectData?.IS_DISABLED;

  // Table

  // Minimalist cell styles for professional appearance
  const getCellStyle = (align = 'center') => `
    px-6 py-5 text-sm text-slate-700
    text-${align} whitespace-nowrap
    transition-colors duration-200
  `.trim();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const [cloneModeDialog, setCloneModeDialog] = useState(false);
  const [dialogData, setDialogData] = useState({ type: 'standard' });
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    // Format date values to YYYY-MM-DD
    let formattedValue = value;
    if (name.includes('date') && value) {
      const date = new Date(value);
      formattedValue = date.toISOString().split('T')[0]; // Ensures YYYY-MM-DD format
    }
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: formattedValue,
    }));
  };
  const handleClickOpenDialog = useCallback((editMode, data, cloneMode = false) => {
    if (editMode) {
      setDialogData(data);
    } else if (cloneMode) {
      // For clone mode, copy data but remove ID and modify name
      const cloneData = {
        ...data,
        id: undefined, // Remove ID so it creates a new scenario
        name: `${data.name} (Copy)`, // Add "(Copy)" to the name
        created_by_staff_id: user?.staff_id, // Set current user as creator
      };
      setDialogData(cloneData);
    } else {
      setDialogData({ type: 'standard' });
    }

    setEditModeDialog(editMode);
    setCloneModeDialog(cloneMode);
    setDialogOpen(true);
  }, [user?.staff_id]);

  // Expose the open dialog function globally for access from anywhere
  useEffect(() => {
    const openDialog = () => handleClickOpenDialog(false);
    window.openScenarioDialog = openDialog;

    // Cleanup function to prevent memory leaks
    return () => {
      if (window.openScenarioDialog === openDialog) {
        delete window.openScenarioDialog;
      }
    };
  }, [handleClickOpenDialog]);

  const schema = yup.object({
    contract_period_in_month: yup
      .number()
      .required('Please provide contract period (months).')
      .min(1, 'Contract period must be greater than or equal to 1 month')
      .max(240, 'Contract period must be less than or equal to 240 months'),
    name: yup.string().required('Please provide category.'),
    type: yup.string().required('Please provide type.').default('standard'),
    expected_rfs_date: yup.string()
      .required('Please provide expected RFS date')
      .matches(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format'),
    project_start_date: yup.string()
      .required('Please provide project start date')
      .matches(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format'),
    project_id: yup.string().required('Please provide category.')?.default(projectId),
    created_by_staff_id: yup
      .string()
      .required('Please provide created by staff id.')
      ?.default(user?.staff_id),
  });

  // Debug log to check conditions
  useEffect(() => {
    console.log('Debug scenario button visibility:', {
      'Project Status': projectData?.status,
      'Project Category': projectData?.category,
      'Is Admin': isAdmin,
      'Scenarios Count': allScenarios?.length,
      'IS_DISABLED': IS_DISABLED,
      'Button Should Show': projectData?.status === 'draft'
    });
  }, [projectData, allScenarios, isAdmin, IS_DISABLED]);

  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        // Ensure dates are in YYYY-MM-DD format before validation
        const dataToValidate = {
          ...dialogData,
          expected_rfs_date: dialogData.expected_rfs_date ? new Date(dialogData.expected_rfs_date).toISOString().split('T')[0] : null,
          project_start_date: dialogData.project_start_date ? new Date(dialogData.project_start_date).toISOString().split('T')[0] : null
        };
        payload = await schema.validate(dataToValidate, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }
      dispatch(setIsLoading(true));
      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${FA_ENDPOINT}/scenario`, payload);
            break;
          case 'put':
            response = await axios.put(`${FA_ENDPOINT}/scenario/${payload?.id}`, payload);
            break;
          case 'delete':
            response = await axios.delete(`${FA_ENDPOINT}/scenario/${payload?.id}`);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
      dispatch(setIsLoading(false));
    }
    setDialogData({ type: 'standard' });
    setEditModeDialog(false);
    setCloneModeDialog(false);
    setDialogOpen(false);
    dispatch(fetchAllScenarios(projectId));
  };

  // Others

  const handleClickRow = (id) => setParam({ scenarioId: id });

  return (
    <>
      <div className="rounded-lg border border-slate-300 bg-white p-6 text-black">
        <div className="space-y-6">
          {/* Header Section */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">
              Scenarios List
            </h3>
            {/* Hidden button for parent component to trigger - keep for compatibility */}
            <button
              type="button"
              className="add-scenario-button hidden"
              onClick={() => handleClickOpenDialog(false)}
            >
              Add Scenario
            </button>
          </div>
          {/* Enhanced Minimalist Table */}
          <div className="overflow-hidden rounded-xl border border-slate-200 bg-white shadow-sm">
            <table className="min-w-full divide-y divide-slate-200">
              <thead className="bg-slate-50">
                <tr>
                  {[
                    { label: 'Scenario', align: 'left', width: 'w-64' },
                    { label: 'Type', align: 'center', width: 'w-24' },
                    { label: 'Duration', align: 'center', width: 'w-28' },
                    { label: 'Timeline', align: 'center', width: 'w-48' },
                    { label: 'Created', align: 'center', width: 'w-32' },
                    { label: '', align: 'center', width: 'w-20' }
                  ].map((column, i) => (
                    <th
                      key={i}
                      className={`
                        ${column.width}
                        px-6 py-4
                        text-${column.align}
                        text-xs font-semibold text-slate-600 uppercase tracking-wider
                        border-b border-slate-200
                      `}
                    >
                      {column.label}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-slate-100 bg-white">
                {allScenarios.map((row, i) => (
                  <tr
                    key={i}
                    className="group cursor-pointer hover:bg-slate-50 transition-colors duration-200"
                  >
                    {/* Scenario Info */}
                    <td className={getCellStyle('left')} onClick={() => handleClickRow(row?.id)}>
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <span className="text-sm font-semibold text-blue-600">
                              {i + 1}
                            </span>
                          </div>
                        </div>
                        <div className="min-w-0 flex-1 space-y-1">
                          <p className="text-sm font-semibold text-slate-900 truncate">
                            {row?.name}
                          </p>
                          <p className="text-xs text-slate-500 font-mono">
                            ID: {row?.scenario_id}
                          </p>
                        </div>
                      </div>
                    </td>

                    {/* Type */}
                    <td className={getCellStyle('center')} onClick={() => handleClickRow(row?.id)}>
                      <span className="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-slate-100 text-slate-700">
                        {row?.type || 'Standard'}
                      </span>
                    </td>

                    {/* Duration */}
                    <td className={getCellStyle('center')} onClick={() => handleClickRow(row?.id)}>
                      <div className="text-center space-y-1">
                        <p className="text-sm font-semibold text-slate-900">
                          {row?.contract_period_in_month}
                        </p>
                        <p className="text-xs text-slate-500">months</p>
                      </div>
                    </td>

                    {/* Timeline */}
                    <td className={getCellStyle('center')} onClick={() => handleClickRow(row?.id)}>
                      <div className="text-center space-y-1">
                        <div className="text-xs text-slate-500">
                          Start: {moment(row?.project_start_date).format('MMM DD, YYYY')}
                        </div>
                        <div className="text-xs text-slate-500">
                          RFS: {moment(row?.expected_rfs_date).format('MMM DD, YYYY')}
                        </div>
                      </div>
                    </td>

                    {/* Created */}
                    <td className={getCellStyle('center')} onClick={() => handleClickRow(row?.id)}>
                      <div className="text-center space-y-1">
                        <UserPopup
                          label={row?.created_by_staff_id?.toUpperCase()}
                          staff_id={row?.created_by_staff_id}
                        />
                        <p className="text-xs text-slate-400">
                          {moment(row?.created_at)?.format('MMM DD')}
                        </p>
                      </div>
                    </td>

                    {/* Actions */}
                    <td className={getCellStyle('center')}>
                      <div className="flex items-center justify-center">
                        <div className="flex items-center space-x-1">
                          <button
                            type="button"
                            onClick={() => handleClickOpenDialog(true, row)}
                            title="Edit Scenario"
                            className="p-2 text-slate-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          {(projectData?.status === 'draft') && (
                            <button
                              type="button"
                              onClick={() => handleClickOpenDialog(false, row, true)}
                              title="Clone Scenario"
                              className="p-2 text-slate-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors duration-200"
                            >
                              <ContentCopy className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="border-b border-slate-200 bg-white px-6 py-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-slate-900">
                {cloneModeDialog ? 'Clone' : (!editModeDialog ? 'Create New' : 'Edit')} Scenario
              </h3>
              <p className="text-sm text-slate-500">
                {cloneModeDialog ? 'Create a copy of this scenario' : (!editModeDialog ? 'Add a new scenario to this project' : 'Update scenario details')}
              </p>
            </div>
          </div>
        </DialogTitle>
        <DialogContent className="px-6 py-6">
          <div className="space-y-6 w-full md:w-[400px]">
            
            <div className="space-y-2 pt-2">
              <label className="text-sm font-medium text-slate-700">Scenario Name</label>
              <TextInput
                name="name"
                value={dialogData?.name}
                placeholder="Enter scenario name"
                onChange={handleDialogDataChange}
                className="fa-input"
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium text-slate-700">Type</label>
              <SelectInput
                name="type"
                value={dialogData?.type || 'standard'}
                placeholder="Select scenario type"
                onChange={handleDialogDataChange}
                options={[{ value: 'standard', label: 'Standard' }]}
                className="fa-input"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium text-slate-700">Project Start</label>
                <TextInput
                  type="date"
                  name="project_start_date"
                  value={dialogData?.project_start_date || ''}
                  onChange={handleDialogDataChange}
                  className="fa-input"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium text-slate-700">Expected RFS</label>
                <TextInput
                  type="date"
                  name="expected_rfs_date"
                  value={dialogData?.expected_rfs_date || ''}
                  onChange={handleDialogDataChange}
                  className="fa-input"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium text-slate-700">Contract Period</label>
              <div className="relative">
                <TextInput
                  type="number"
                  name="contract_period_in_month"
                  value={dialogData?.contract_period_in_month}
                  placeholder="Enter the contract period in months"
                  onChange={handleDialogDataChange}
                  className="fa-input pr-16"
                />
                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-slate-500">
                  months
                </span>
              </div>
            </div>
          </div>
        </DialogContent>
        <DialogActions className="border-t border-slate-200 bg-slate-50 px-6 py-4">
          <div className="flex w-full justify-between">
            <div className="flex items-center space-x-3">
              <button
                type="button"
                onClick={() => handleDialogClose(false)}
                className="px-3 py-2 text-sm font-medium text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors duration-200"
              >
                Cancel
              </button>
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('delete')}
                  className="px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors duration-200"
                >
                  Delete
                </button>
              )}
            </div>

            <div className="flex items-center space-x-3">
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                >
                  Create
                </button>
              )}

              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                >
                  Update
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default AllScenario;
