// Packages
import PropTypes from 'prop-types';

const DistributionBreakdown = ({ lists }) => {
  const backgroundGradient = (index) => {
    switch (index) {
      case 0:
        return 'bg-gray-100 text-black';
      case 1:
        return 'bg-gray-200 text-black';

      case 2:
        return 'bg-gray-300 text-black';
      case 3:
        return 'bg-gray-400 text-white';
      case 4:
        return 'bg-gray-500 text-white';
      case 5:
        return 'bg-gray-600 text-white';
      case 6:
        return 'bg-gray-700 text-white';
      case 7:
        return 'bg-gray-800 text-white';
      default:
        return 'bg-gray-100 text-black';
    }
  };

  return (
    <div className="overflow-x-auto scrollbar">
      <table className="min-w-full bg-white text-black">
        <tbody>
          {lists?.map((item, index) => (
            <tr key={item.bandwidth}>
              <div className={`flex flex-col text-center ${backgroundGradient(index)}`}>
                <div>{item.count.toLocaleString()}</div>
                <div>{`${item.bandwidth} Mbps`}</div>
              </div>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

DistributionBreakdown.propTypes = {
  lists: PropTypes.arrayOf(
    PropTypes.shape({
      count: PropTypes.number.isRequired,
      bandwidth: PropTypes.number.isRequired,
    })
  ).isRequired,
};

export default DistributionBreakdown;
