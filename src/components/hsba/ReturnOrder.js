/* eslint-disable */

// Packages
const R = require('ramda');
const moment = require('moment');

moment.defaultFormat = 'YYYY-MM-DD HH:mm:ss';

function Order(order) {
  R.keys(order).forEach((key) => {
    this[key] = order[key];
  });

  this.FIRST_RETURNED_MONTH = R.pipe(R.prop('SETS'), R.head, (temp) =>
    moment(R.prop('RETURN_DATE', temp), moment.defaultFormat).format('YYYY-MM')
  )(order);
  this.FIRST_SET = R.pipe(R.prop('SETS'), R.head)(order);

  this._index = JSON.stringify(order);
}

Order.prototype.contains = function (keyword) {
  return R.test(new RegExp(keyword, 'i'), this._index);
};

Order.prototype.isActive = function () {
  return this.ACTIVE;
};

Order.prototype.isToCancel = function () {
  const suggestToCancel = R.any(
    R.compose(R.contains('cancel'), <PERSON><PERSON>toLower(), <PERSON>.prop('MIR_COMMENTS'))
  );
  return R.compose(suggestToCancel, R.prop('SETS'))(this);
};

Order.prototype.isReturnedAt = function (month) {
  const isReturnedAt = R.any(R.compose(R.contains(month), R.prop('RETURN_DATE')));
  return R.compose(isReturnedAt, R.prop('SETS'))(this);
};

Order.prototype.isBreached = function (month) {
  const isReturnedAt = R.compose(R.contains(month), R.prop('RETURN_DATE'));
  const isBreached = R.propEq('SLA_STATUS', 'Breached');
  return R.compose(R.any(R.allPass([isBreached, isReturnedAt])), R.prop('SETS'))(this);
};

Order.prototype.isMonitored = function () {
  const hasTarget = R.compose(R.complement(R.isEmpty()), R.prop('SLA_TARGET_AT'));
  const isMonitored = R.any(hasTarget);
  return R.compose(isMonitored, R.prop('SETS'))(this);
};

Order.prototype.isInJeopardy = function () {
  const alertPassed = R.any(R.compose(R.contains('passed'), R.prop('VERDICT')));
  return R.compose(alertPassed, R.prop('SETS'))(this);
};

Order.prototype.isMissingMir = function () {
  const isMissingMir = R.any(R.propEq('MIR_STATUS', ''));
  return R.compose(isMissingMir, R.prop('SETS'))(this);
};

Order.prototype.isMissingReturnDate = function () {
  const isMissingDate = R.any(R.propEq('VERDICT', 'Return date is missing'));
  return R.compose(isMissingDate, R.prop('SETS'))(this);
};

export default Order;
