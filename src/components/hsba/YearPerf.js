// Packages
import PropTypes from 'prop-types';
import * as R from 'ramda';

// Components
import Chart from '../Shared/ReactApexcharts';

const YearPerf = ({ data = [], customer }) => {
  const months = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ];

  const dataset = () => data;

  const series = () => {
    const populate = (temp) => R.pipe(R.flatten, R.take(12))([temp, Array(12)]);

    if (data === []) {
      return [
        {
          name: 'New install',
          data: [],
        },
        {
          name: 'Termination',
          data: [],
        },
      ];
    }

    // No customer selected
    if (customer === 'All') {
      return [
        {
          name: 'New install',
          data: populate(R.values(R.pluck('new_install', dataset()))),
        },
        {
          name: 'Termination',
          data: populate(R.values(<PERSON><PERSON>pluck('termination', dataset()))),
        },
      ];
    }
    return [
      {
        name: 'New install',
        data: populate(
          R.pipe(
            <PERSON>.pluck('breakdown'),
            R.map(
              R.values(),
              R.map(
                R.pipe(R.filter(R.propEq('customer', customer)), R.pluck('new_install'), R.values())
              )
            ),
            R.flatten()
          )(data)
        ),
      },
      {
        name: 'Termination',
        data: populate(
          R.pipe(
            R.pluck('breakdown'),
            R.map(
              R.values(),
              R.map(
                R.pipe(R.filter(R.propEq('customer', customer)), R.pluck('termination'), R.values())
              )
            ),
            R.flatten()
          )(data)
        ),
      },
    ];
  };

  const options = {
    legend: {
      position: 'top',
    },
    colors: ['#64CC6D', '#EB4C64'],
    chart: {
      toolbar: {
        show: false,
      },
      height: 500,
      type: 'bar',
      stacked: true,
      events: {
        dataPointSelection: (event, ctx, config) => {
          console.log('Clicked data point:', config.dataPointIndex);
        },
      },
    },
    plotOptions: {
      bar: {
        dataLabels: {
          position: 'top',
        },
        endingShape: 'rounded',
        columnWidth: '30%',
      },
    },
    dataLabels: {
      enabled: false,
      formatter: (val) => `${val} %`,
      offsetY: -20,
      style: {
        fontSize: '12px',
        colors: ['#304758'],
      },
    },
    xaxis: {
      categories: months,
      position: 'bottom',
      labels: {
        rotate: -90,
        rotateAlways: true,
      },
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
      crosshairs: {
        fill: {
          type: 'gradient',
          gradient: {
            colorFrom: '#D8E3F0',
            colorTo: '#BED1E6',
            stops: [0, 100],
            opacityFrom: 0.4,
            opacityTo: 0.5,
          },
        },
      },
      tooltip: {
        enabled: true,
        offsetY: -35,
      },
    },
  };

  return <Chart options={options} series={series()} type="bar" height={280} />;
};

YearPerf.propTypes = {
  data: PropTypes.object,
  customer: PropTypes.object,
};

export default YearPerf;
