// Packages
import PropTypes from 'prop-types';

// Components
import WarnBeforeActionPopupButton from '../Shared/WarnBeforeActionPopupButton';

// Others
import { useHsbaContext } from '../../utils/hsba';

const TringgerEngineButton = ({ process, payload }) => {
  // Standard and Vars
  const { triggerEngine } = useHsbaContext();

  return (
    <WarnBeforeActionPopupButton
      positiveSentiment
      onApprove={() => {
        triggerEngine(process, payload);
      }}
    >
      <p className="cta-btn bg-green-500">Trigger Engine</p>
    </WarnBeforeActionPopupButton>
  );
};

TringgerEngineButton.propTypes = {
  process: PropTypes.string,
  payload: PropTypes.object,
};

export default TringgerEngineButton;
