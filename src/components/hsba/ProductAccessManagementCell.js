// Next, React, Tw
import { useState } from 'react';

// Packages
import PropTypes from 'prop-types';

// Components
import UserPopup from '../Shared/UserPopup';

// Others
import { useSnackbar } from '../Shared/snackbar';
import axios from '../../utils/axios';
import { toUpperCaseFirstLetter, isCertainModuleSubModuleRole } from '../../utils/shared';
import { AUM_ENDPOINT } from '../../utils/aum';

const PRODUCTS = [
  'management-view',
  'cx-overview',
  'btu-stock-utilization',
  'distribution',
  'return-order',
  'demand-list',
  'inter-region',
  'cable-report',
];

const ProductAccessManagementCell = ({ rowData, i, rowsPerPage, page }) => {
  // Standard
  const { enqueueSnackbar } = useSnackbar();

  const handleActionButtonClick = async (submodule, role) => {
    const temp = row?.modules[0]?.submodules.find((o) => o?.submodule === submodule);
    if (!temp) {
      try {
        const response = await axios.post(
          `${AUM_ENDPOINT}/user/v1/submodule/hsba/${row?.staff_id}`,
          {
            role,
            submodule,
            tag: '',
          }
        );
        if (response.data.status === 'success') {
          enqueueSnackbar(`${response.data.data}`, {
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'center',
            },
          });
        }
      } catch {
        enqueueSnackbar('Failed to update', {
          variant: 'error',
        });
      }
    } else {
      try {
        const response = await axios.put(`${AUM_ENDPOINT}/user/v1/submodule/${temp?.id}`, {
          role,
          submodule,
          tag: 'string',
        });
        if (response.data.status === 'success') {
          enqueueSnackbar(`${response.data?.data}`, {
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'center',
            },
          });
        }
      } catch {
        enqueueSnackbar('Failed to update', {
          variant: 'error',
        });
      }
    }

    try {
      await axios.put(`${AUM_ENDPOINT}/user/v1/module/${row?.staff_id}`, {
        module: 'hsba',
        role: 'user',
        tag: '',
      });
    } catch {
      /* empty */
    }

    fetchData();
  };

  // Others
  const [row, setRow] = useState(rowData);
  const getBodyCellStyle = () => 'text-center py-1 text-xs';

  const fetchData = async () => {
    try {
      const response = await axios.get(`${AUM_ENDPOINT}/user/v1/module/hsba/${row?.staff_id}`);
      setRow(response?.data?.data[0]);
    } catch {
      /* empty */
    }
  };

  return (
    <>
      {PRODUCTS.map((product, index) => (
        <tr className={`${index === PRODUCTS.length - 1 && 'border-b border-gray-300'}`}>
          {index === 0 && (
            <>
              <td className={getBodyCellStyle()} rowSpan={PRODUCTS.length}>
                {i + 1 + rowsPerPage * page}
              </td>
              <td className={getBodyCellStyle()} rowSpan={PRODUCTS.length}>
                {row.name.toUpperCase()}
              </td>
              <td className={getBodyCellStyle()} rowSpan={PRODUCTS.length}>
                <UserPopup label={row.staff_id.toUpperCase()} staff_id={row.staff_id} />
              </td>
            </>
          )}
          <td className={getBodyCellStyle()}>
            {toUpperCaseFirstLetter(product.replace('-', ' '))}
          </td>
          <td className={getBodyCellStyle()}>
            {isCertainModuleSubModuleRole(row, 'hsba', product, 'user') ? 'Active' : 'Inactive'}
          </td>
          <td className={getBodyCellStyle()}>
            <div className="flex flex-col">
              {isCertainModuleSubModuleRole(row, 'hsba', product, 'user') ? (
                <button
                  type="button"
                  className="rounded-xl bg-red-500 px-2 font-semibold text-white"
                  onClick={(event) => handleActionButtonClick(product, 'pending')}
                >
                  Revoke Access
                </button>
              ) : (
                <button
                  type="button"
                  className="rounded-xl bg-green-500 px-2 font-semibold text-white"
                  onClick={(event) => handleActionButtonClick(product, 'user')}
                >
                  Allow Access
                </button>
              )}
            </div>
          </td>
        </tr>
      ))}
    </>
  );
};

ProductAccessManagementCell.propTypes = {
  rowData: PropTypes.object,
  i: PropTypes.number,
  rowsPerPage: PropTypes.number,
  page: PropTypes.number,
};

export default ProductAccessManagementCell;
