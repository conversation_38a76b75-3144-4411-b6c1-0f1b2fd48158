// Next, React, Tw
import { twMerge } from 'tailwind-merge';

// Packages
import PropTypes from 'prop-types';
import * as R from 'ramda';

// Components
import DistributionBreakdown from './DistributionBreakdown';

const DistributionPanel = ({
  bandwidths = [],
  lowerSpeed,
  upperSpeed,
  sortMode = 'count',
  showBreakdown = false,
  showResidential = true,
  showBusiness = true,
  showOthers = true,
  totalNationwide,
}) => {
  const isBusiness = R.propEq('product', 'business');

  const percentage = (value, total) => {
    if (total === 0) return '-';
    return `${((value / total) * 100).toFixed(1)} %`;
  };

  const percentageLabel = (list, group) => {
    if (group === 'total') return percentage(sum(list, group, 'integer'), total);

    return percentage(sum(list, group, 'integer'), sum(list, null, 'integer'));
  };

  const bandwidthMain = (data, speedGroup = null) => {
    const isLower = R.propSatisfies(R.lte(R.__, lowerSpeed), 'bandwidth');
    const isUpper = R.propSatisfies(R.gte(R.__, upperSpeed), 'bandwidth');

    const isMiddle = R.allPass([R.complement(isLower), R.complement(isUpper)]);

    const grouped = R.pipe(
      R.groupBy(R.prop('bandwidth')),
      R.map((temp) => ({
        count: R.pipe(R.pluck('count'), R.sum())(temp),
        bandwidth: R.pipe(R.head(), R.prop('bandwidth'))(temp),
      })),
      R.values()
    )(data);

    let filteredData;

    if (speedGroup === 'lower') {
      filteredData = R.filter(isLower)(grouped);
    } else if (speedGroup === 'upper') {
      filteredData = R.filter(isUpper)(grouped);
    } else if (speedGroup === 'middle') {
      filteredData = R.filter(isMiddle)(grouped);
    } else {
      filteredData = grouped;
    }

    const inAscendingOrder = (prop) => R.sortWith([R.ascend(R.prop(prop))]);

    return inAscendingOrder(sortMode)(filteredData);
  };

  const sum = (list, speedGroup = null, type = 'string') => {
    const filteredSet = bandwidthMain(list, speedGroup);
    return R.pipe(R.pluck('count'), R.sum())(filteredSet);
  };

  const state = R.pipe(R.head(), R.prop('state'))(bandwidths);

  const residential = R.filter(R.propEq('product', 'residential'), bandwidths);

  const business = R.filter(isBusiness)(bandwidths);

  const total = R.pipe(R.pluck('count'), R.sum())(bandwidths);

  const percentageNationwide = percentage(total, totalNationwide);

  const bodyCellStyle = 'bg-white text-center px-1';

  return (
    <tbody>
      <tr className="border-b border-gray-400 text-xs">
        <td className={twMerge(bodyCellStyle, 'text-left')}>
          <p style={{ fontSize: '0.7rem' }}>{state}</p>
          <p style={{ fontSize: '0.7rem', color: 'grey' }}>{percentageNationwide}</p>
        </td>
        <td className={twMerge(bodyCellStyle, 'bg-[#f5f5f5]')}>
          <p style={{ fontSize: '0.7rem' }} className="">
            {total.toLocaleString()}
          </p>
        </td>
        {showResidential && (
          <>
            <td className={bodyCellStyle}>
              <p style={{ fontSize: '0.7rem' }}>{sum(residential, 'lower').toLocaleString()}</p>
              <p style={{ fontSize: '0.675rem', color: 'grey' }}>
                {percentageLabel(residential, 'lower')}
              </p>
              {showBreakdown && (
                <DistributionBreakdown lists={bandwidthMain(residential, 'lower')} />
              )}
            </td>
            <td className={bodyCellStyle}>
              <p style={{ fontSize: '0.7rem' }}>{sum(residential, 'middle').toLocaleString()}</p>
              <p style={{ fontSize: '0.675rem', color: 'grey' }}>
                {percentageLabel(residential, 'middle')}
              </p>
              {showBreakdown && (
                <DistributionBreakdown lists={bandwidthMain(residential, 'middle')} />
              )}
            </td>
            <td className={bodyCellStyle}>
              <p style={{ fontSize: '0.7rem' }}>{sum(residential, 'upper').toLocaleString()}</p>
              <p style={{ fontSize: '0.675rem', color: 'grey' }}>
                {percentageLabel(residential, 'upper')}
              </p>
              {showBreakdown && (
                <DistributionBreakdown lists={bandwidthMain(residential, 'upper')} />
              )}
            </td>
            <td className={twMerge(bodyCellStyle, 'bg-[#f5f5f5]')}>
              <p style={{ fontSize: '0.7rem' }} className="has-text-weight-semibold">
                {sum(residential).toLocaleString()}
              </p>
            </td>
          </>
        )}
        {showBusiness && (
          <>
            <td className={bodyCellStyle}>
              <p style={{ fontSize: '0.7rem' }}>{sum(business, 'lower').toLocaleString()}</p>

              <p style={{ fontSize: '0.675rem', color: 'grey' }}>
                {percentageLabel(business, 'lower').toLocaleString()}
              </p>
              {showBreakdown && <DistributionBreakdown lists={bandwidthMain(business, 'lower')} />}
            </td>
            <td className={bodyCellStyle}>
              <p style={{ fontSize: '0.7rem' }}>{sum(business, 'middle').toLocaleString()}</p>

              <p style={{ fontSize: '0.675rem', color: 'grey' }}>
                {percentageLabel(business, 'middle').toLocaleString()}
              </p>
              {showBreakdown && <DistributionBreakdown lists={bandwidthMain(business, 'middle')} />}
            </td>
            <td className={bodyCellStyle}>
              <p style={{ fontSize: '0.7rem' }}>{sum(business, 'upper').toLocaleString()}</p>

              <p style={{ fontSize: '0.675rem', color: 'grey' }}>
                {percentageLabel(business, 'upper')}
              </p>
              {showBreakdown && <DistributionBreakdown lists={bandwidthMain(business, 'upper')} />}
            </td>
            <td className={twMerge(bodyCellStyle, 'bg-[#f5f5f5]')}>
              <p style={{ fontSize: '0.7rem' }} className="has-text-weight-semibold">
                {sum(business).toLocaleString()}
              </p>
            </td>
          </>
        )}
      </tr>
    </tbody>
  );
};

DistributionPanel.propTypes = {
  bandwidths: PropTypes.array.isRequired,
  lowerSpeed: PropTypes.number.isRequired,
  upperSpeed: PropTypes.number.isRequired,
  sortMode: PropTypes.string,
  showBreakdown: PropTypes.bool,
  showResidential: PropTypes.bool,
  showBusiness: PropTypes.bool,
  showOthers: PropTypes.bool,
  totalNationwide: PropTypes.number,
};

export default DistributionPanel;
