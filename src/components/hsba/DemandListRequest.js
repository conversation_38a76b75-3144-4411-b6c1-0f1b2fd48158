/* eslint-disable */

// Packages
const R = require('ramda');
const moment = require('moment');

moment.defaultFormat = 'YYYY-MM-DD HH:mm:ss';

function DemandListRequest(request, exclusionList) {
  R.keys(request).forEach((key) => {
    this[key] = request[key];
  });
  this._index = JSON.stringify(request);

  this.EXCLUSION = exclusionList.includes(request.ORBIT_ID);
  if (this.EXCLUSION) {
    console.log('Exclusion applied for', this.ORBIT_ID, this.REQUEST_ID);
  }
}

DemandListRequest.prototype.contains = function (keyword) {
  return R.test(new RegExp(keyword, 'i'), this._index);
};

const isBreached = R.propEq('BREACHED', true);
const isExcluded = R.propEq('EXCLUSION', true);
const isOpen = R.propEq('REQUEST_STATUS', 'Open');
const isInProgress = R.propEq('REQUEST_STATUS', 'In Progress');
const isRfsiMeet = R.propEq('RFSI_VERDICT', 'MEET');
const isRfsiJeopardy = R.propEq('RFSI_VERDICT', 'JEOPARDY');
const isRfsiBreached = R.propEq('RFSI_VERDICT', 'BREACHED');
const alertPassed = R.compose(R.contains('passed'), R.prop('VERDICT'));

DemandListRequest.prototype.isActive = function (month) {
  return this.ACTIVE;
};

DemandListRequest.prototype.isUnderCategory = function (category) {
  const isUnder = R.propEq('CATEGORY', category);
  return R.compose(R.allPass([isUnder]))(this);
};

DemandListRequest.prototype.isCreatedAt = function (month) {
  const isCreatedAt = R.compose(R.contains(month), R.prop('CREATED_AT'));
  return isCreatedAt(this);
};

DemandListRequest.prototype.isOpen = function () {
  return R.compose(R.allPass([isOpen]))(this);
};

DemandListRequest.prototype.isInProgress = function () {
  return R.compose(R.allPass([isInProgress]))(this);
};

DemandListRequest.prototype.isResponded = function () {
  return R.compose(R.allPass([R.complement(isInProgress), R.complement(isOpen)]))(this);
};

DemandListRequest.prototype.isBreached = function () {
  return R.compose(R.allPass([isBreached, R.complement(isExcluded)]))(this);
};

DemandListRequest.prototype.isExcluded = function () {
  return R.compose(R.allPass([isExcluded]))(this);
};

DemandListRequest.prototype.isInJeopardy = function () {
  return R.compose(R.allPass([alertPassed, R.complement(isExcluded)]))(this);
};

DemandListRequest.prototype.isMeet = function () {
  return R.compose(R.allPass([R.complement(isBreached), R.complement(isExcluded)]))(this);
};

DemandListRequest.prototype.isRfsiMeet = function () {
  return R.compose(R.allPass([isRfsiMeet]))(this);
};

DemandListRequest.prototype.isRfsiJeopardy = function () {
  return R.compose(R.allPass([isRfsiJeopardy]))(this);
};

DemandListRequest.prototype.isRfsiBreached = function () {
  return R.compose(R.allPass([isRfsiBreached]))(this);
};

export default DemandListRequest;
