// Next, React, Tw
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { Edit } from '@mui/icons-material';

// Packages
import * as yup from 'yup';

// Components
import { TablePaginationCustom } from '../Shared/table';
import { TextInput, SearchInput } from '../Shared/CustomInput';

// Others
import axios from '../../utils/axios';
import { HSBA_ENDPOINT } from '../../utils/hsba';
import { useSnackbar } from '../Shared/snackbar';
import { setIsLoading } from '../../utils/store/loadingReducer';

const CustomerConfiguration = () => {
  // Standard
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { q } = query;

  // Table
  const [tableData, setTableData] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(10);
  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  })();
  const bodyCellStyle = 'text-center whitespace-nowrap py-1 text-xs';

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const schema = yup.object({
    name: yup.string().required('Please provide name.'),
    abbreviation: yup.string().required('Please provide an abbreviation.'),
    accountNumber: yup.number().required('Please provide an account number.'),
  });
  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  const handleDialogClose = async (action) => {
    if (action) {
      let payload = dialogData;
      try {
        payload = await schema.validate(payload, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }

      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${HSBA_ENDPOINT}/customer`, payload);
            break;
          case 'put':
            response = await axios.put(`${HSBA_ENDPOINT}/customer/${payload?._id}`, payload);
            break;
          case 'delete':
            response = await axios.delete(`${HSBA_ENDPOINT}/customer/${payload?._id}`);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = 'Success';
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    setDialogData({});
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${HSBA_ENDPOINT}/customer`);
      if (response?.status === 200) {
        setTableData(response?.data);
      }
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div className="container mx-auto p-1 md:p-4">
        <div
          style={{ boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)' }}
          className="flex flex-col gap-2 rounded-xl bg-white p-4 text-black shadow-dashboardCard dark:bg-gray-600 dark:text-white dark:shadow-dashboardCardDark"
        >
          <div className="flex flex-col justify-between gap-2 md:flex-row">
            <SearchInput />
            <button
              type="button"
              className="rounded-[4px] bg-green-600 px-4 py-2 text-xs font-semibold text-white"
              onClick={() => handleClickOpenDialog(false)}
            >
              New Customer
            </button>
          </div>
          <div className="overflow-x-auto bg-white text-black scrollbar">
            <table className="min-w-full">
              <thead>
                <tr>
                  {['Name', 'Abbreviation', 'Account Number', ''].map((label, index) => (
                    <td
                      key={index}
                      className="whitespace-nowrap bg-[#fcfcfd] py-1 text-center text-sm"
                    >
                      {label}
                    </td>
                  ))}
                </tr>
              </thead>
              <tbody>
                {filteredTableData
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((row, index) => (
                    <tr
                      key={index}
                      className={`${`${index % 2 === 0 ? 'bg-[#f8f8f8]' : 'bg-white'}`}`}
                    >
                      <td className={bodyCellStyle}>{row?.name}</td>
                      <td className={bodyCellStyle}>{row?.abbreviation}</td>
                      <td className={bodyCellStyle}>{row?.accountNumber}</td>
                      <td className={bodyCellStyle}>
                        <div className="flex gap-2">
                          <button
                            type="button"
                            className="rounded-xl border border-[#ff7a03] p-1"
                            onClick={() => handleClickOpenDialog(true, row)}
                          >
                            <Edit sx={{ color: '#ff7a03' }} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
          <TablePaginationCustom
            count={tableData.length}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={(e, nextPage) => setPage(nextPage)}
          />
        </div>
      </div>

      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle>{!editModeDialog ? 'New' : 'Edit'} Customer</DialogTitle>
        <DialogContent>
          <div className="flex w-full flex-col gap-4 p-2 md:w-[400px]">
            <TextInput
              name="name"
              value={dialogData?.name}
              placeholder="Name"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="abbreviation"
              value={dialogData?.abbreviation}
              placeholder="Abbreviation"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="accountNumber"
              value={dialogData?.accountNumber}
              placeholder="Account Number"
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
              Cancel
            </button>
            <div className="flex gap-2">
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="rounded-[4px] bg-green-500 p-2 font-semibold text-white"
                >
                  Save
                </button>
              )}

              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="rounded-[4px] bg-green-500 p-2 font-semibold text-white"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default CustomerConfiguration;
