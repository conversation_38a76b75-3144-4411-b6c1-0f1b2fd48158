// Next, React, Tw
import { useState } from 'react';

// Mui
import { Button, Menu, MenuItem } from '@mui/material';
import { FileDownload, Description, PictureAsPdf } from '@mui/icons-material';

// Packages
import PropTypes from 'prop-types';

const ExportOptions = ({ siteDetails, buttonProps = {}, onSuccess, onError }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleExportToWord = () => {
    if (!siteDetails) {
      if (onError) {
        onError('Word', new Error('No site details to export'));
      }
      return;
    }

    try {
      // Process images first to create optimized versions
      const processImageAttachments = async (attachments) => {
        const processedAttachments = [...attachments];
        
        for (let i = 0; i < processedAttachments.length; i++) {
          const attachment = processedAttachments[i];
          
          if (attachment.base64Data && 
              (attachment.file_name?.toLowerCase().endsWith('.jpg') ||
               attachment.file_name?.toLowerCase().endsWith('.jpeg') ||
               attachment.file_name?.toLowerCase().endsWith('.png'))) {
            
            try {
              // Create an image element to load the base64 data
              const img = new Image();
              await new Promise((resolve, reject) => {
                img.onload = resolve;
                img.onerror = reject;
                img.src = `data:image/${attachment.file_name?.split('.').pop()};base64,${attachment.base64Data}`;
              });
              
              // Create a canvas to resize the image
              const canvas = document.createElement('canvas');
              let width = img.width;
              let height = img.height;
              
              // Calculate new dimensions while maintaining aspect ratio
              const maxWidth = 800;
              const maxHeight = 500;
              
              if (width > maxWidth || height > maxHeight) {
                const ratio = Math.min(maxWidth / width, maxHeight / height);
                width = width * ratio;
                height = height * ratio;
              }
              
              canvas.width = width;
              canvas.height = height;
              
              // Draw the resized image on the canvas
              const ctx = canvas.getContext('2d');
              ctx.drawImage(img, 0, 0, width, height);
              
              // Convert the canvas to a base64 data URL
              const resizedBase64 = canvas.toDataURL(`image/${attachment.file_name?.split('.').pop()}`, 0.8);
              
              // Update the attachment with the resized image data
              processedAttachments[i].resizedBase64 = resizedBase64.split(',')[1];
            } catch (err) {
              console.error('Error processing image:', err);
              // Keep the original if processing fails
            }
          }
        }
        
        return processedAttachments;
      };

      let htmlContent = `
        <html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'>
        <head>
          <meta charset='utf-8'>
          <title>Site Details - ${siteDetails.site_name || siteDetails.site_id || 'Export'}</title>
          <style>
            body { font-family: Times New Roman, sans-serif; font-size: 13px; }
            h1, h2, h3, h4, h4, h6 { color: #333; }
            table { border-collapse: collapse; width: 100%; margin-bottom: 10px; }
            th, td { border: 2px solid #000; padding-left: 2px; text-align: left; line-height: 1; vertical-align: top; }
            .section { margin-bottom: 10px; }
            .image-container { text-align: center; margin: 5px auto; }
            .image-caption { 
              font-style: normal; 
              font-size: 13px; 
              margin-top: 3px; 
              text-align: center; 
              width: 100%;
              display: block;
            }
            .image-placeholder { 
              border: 1px dashed #ccc; 
              padding: 10px; 
              text-align: center; 
              margin: 5px auto; 
              width: 600px; 
            }
            img.word-image {
              max-width: 800px !important;
              max-height: 500px !important;
              width: auto !important;
              height: auto !important;
              display: block !important;
              margin: 0 auto !important;
            }
            .img-td {
              text-align: center !important;
              width: 100% !important;
              padding: 8px !important;
            }
            .img-table {
              width: 100% !important;
              border: none !important;
              margin: 10px auto !important;
            }
            .img-table td {
              border: none !important;
            }
            .caption-cell {
              text-align: center !important;
              font-style: normal !important;
              font-size: 13px !important;
              padding-top: 3px !important;
              width: 100% !important;
            }
            /* Word VML specific styling */
            v\\:* { behavior: url(#default#VML); }
            o\\:* { behavior: url(#default#VML); }
            w\\:* { behavior: url(#default#VML); }
            .shape { behavior: url(#default#VML); }
          </style>
          <!-- Word-specific processing instructions -->
          <xml>
            <w:WordDocument>
              <w:View>Print</w:View>
              <w:Zoom>100</w:Zoom>
              <w:DoNotOptimizeForBrowser/>
            </w:WordDocument>
          </xml>
        </head>
        <body>
          <h4>Site Details Report</h4>
          <p>Generated on: ${new Date().toLocaleString()}</p>
          
          <div class='section'>
            <table>
            <tr>
                <td style="background-color: #ff9002; width: 120px;">Date</td>
                <td><b>${siteDetails.date || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Time</td>
                <td><b>${siteDetails.time || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Requirement</td>
                <td><b>${siteDetails.requirement || '-'}</b></td>
              </tr>
               <tr>
                <td style="background-color: #ff9002;">Customer Name</td>
                <td><b>${siteDetails.customer_name || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">End Customer</td>
                <td><b>${siteDetails.end_customer || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Site Address</td>
                <td><b>${siteDetails.site_address || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Lat / Long</td>
                <td><b>${siteDetails.lat_long || '-'}</b></td>
              </tr>
               <tr>
                <td style="background-color: #ff9002;">Sphere ID</td>
                <td><b>${siteDetails.sphere_id || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">SOF No</td>
                <td><b>${siteDetails.sof_no || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">DTC No</td>
                <td><b>${siteDetails.dtc_no || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Nova ID</td>
                <td><b>${siteDetails.nova_id || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Site ID</td>
                <td><b>${siteDetails.site_id || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Site Name</td>
                <td><b>${siteDetails.site_name || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Product SLA</td>
                <td><b>${siteDetails.product_sla || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Exchange</td>
                <td><b>${siteDetails.exchange || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Project Manager</td>
                <td><b>${siteDetails.pm || '-'}</b></td>
              </tr>
            </table>
          </div>`;

      // Always include Representatives section
      htmlContent += `
        <div class='section'>
          <h4>Representatives</h4>
          <table>
            <tr>
              <th style="background-color: #ff9002; text-align: center">No</th>
              <th style="background-color: #ff9002;">Name</th>
              <th style="background-color: #ff9002;">Unit</th>
              <th style="background-color: #ff9002;">Contact No</th>
            </tr>`;

      if (siteDetails.representatives && siteDetails.representatives.length > 0) {
        siteDetails.representatives.forEach((rep, index) => {
          htmlContent += `
            <tr>
              <td style="text-align: center">${index + 1}</td>
              <td>${rep.name || '-'}</td>
              <td>${rep.unit || '-'}</td>
              <td>${rep.contact_no || '-'}</td>
            </tr>`;
        });
      } else {
        htmlContent += `
          <tr>
            <td colspan="4" style="text-align: center">No representatives data available</td>
          </tr>`;
      }

      htmlContent += `
          </table>
        </div>`;

      // Separate table for Findings and CME Status
      htmlContent += `
        <div class='section'>
          <h4>Findings</h4>
          <table>
            <tr>
              <th style="background-color: #ff9002; text-align: center">No</th>
              <th style="background-color: #ff9002;">Finding</th>
            </tr>`;

      // Check if findings exist in siteDetails and use them
      const findingsData = siteDetails.findings || [];
      const attachmentsData = siteDetails.attachments || [];

      // Process the images asynchronously and then continue with the export
      processImageAttachments(attachmentsData).then(processedAttachments => {
        if (Array.isArray(findingsData) && findingsData.length > 0) {
          findingsData.forEach((finding, index) => {
            // With the dedicated API, findings now have a standard structure with a description property
            let findingText = '-';
            if (typeof finding === 'object' && finding !== null) {
              // Use the description property which is the standard field in the new structure
              findingText = finding.description || '-';
            } else if (finding) {
              // If it's a primitive value (string, number, etc.), use it directly
              // This is for backward compatibility
              findingText = finding;
            }

            // Find any image attachments associated with this finding
            const findingAttachments = processedAttachments.filter(
              (attachment) =>
                attachment.finding_id === finding.id &&
                (attachment.file_name?.toLowerCase().endsWith('.jpg') ||
                  attachment.file_name?.toLowerCase().endsWith('.jpeg') ||
                  attachment.file_name?.toLowerCase().endsWith('.png'))
            );

            htmlContent += `
              <tr>
                <td style="text-align: center">${index + 1}</td>
                <td>
                  ${findingText}
                  ${
                    findingAttachments.length > 0
                      ? findingAttachments
                          .map((attachment) => {
                            // Use the resized base64 data if available, otherwise use the original
                            const imageBase64 = attachment.resizedBase64 || attachment.base64Data;
                            const imageUrl = imageBase64
                              ? `data:image/${attachment.file_name?.split('.').pop()};base64,${imageBase64}`
                              : null;

                            return `
                                <table class="img-table" width="100%" border="0" cellspacing="0" cellpadding="0" align="center">
                                  <tr>
                                    <td class="img-td" align="center">
                                    ${
                                      imageUrl
                                        ? `<img src="${imageUrl}" alt="${attachment.caption || 'Finding image'}" class="word-image" 
                                          style="max-width:600px; max-height:400px; border:1px solid #ddd; display:block; margin:0 auto;">`
                                        : `<div class="image-placeholder">
                                          <p>Image: ${attachment.file_name}</p>
                                          <p style="font-size: 11px; color: #666;">Note: Images are viewable in the application but not in exported documents.</p>
                                        </div>`
                                    }
                                    </td>
                                  </tr>
                                  ${
                                    attachment.caption
                                      ? `<tr><td align="center" class="caption-cell" style="text-align: center; font-size: 13px; padding-top: 3px; width: 100%;">${attachment.caption}</td></tr>`
                                      : ''
                                  }
                                </table>
                              `;
                          })
                          .join('')
                      : ''
                  }
                </td>
              </tr>`;
          });
        } else {
          htmlContent += `
            <tr>
              <td colspan="2" style="text-align: center">No findings recorded</td>
            </tr>`;
        }

        htmlContent += `
            </table>
          </div>`;

        // CME Status table
        htmlContent += `
          <div class='section'>
            <h4>CME Status</h4>
            <table>
              <tr>
                <td style="background-color: #ff9002; width: 120px;">CME Status</td>
                <td><b>${siteDetails.cme_status || '-'}</b></td>
              </tr>
            </table>
          </div>`;

        // Always include Checklists section
        htmlContent += `
          <div class='section'>
            <h4>Checklists</h4>
            <table>
              <tr>
                <th style="background-color: #ff9002; text-align: center">No</th>
                <th style="background-color: #ff9002;">Requirement</th>
                <th style="background-color: #ff9002;">Status</th>
              </tr>`;

        if (siteDetails.checklists && siteDetails.checklists.length > 0) {
          siteDetails.checklists.forEach((checklist, index) => {
            htmlContent += `
              <tr>
                <td style="text-align: center">${index + 1}</td>
                <td>${checklist.requirement || '-'}</td>
                <td>${checklist.status || '-'}</td>
              </tr>`;
          });
        } else {
          htmlContent += `
            <tr>
              <td colspan="3" style="text-align: center">No checklists data available</td>
            </tr>`;
        }

        htmlContent += `
            </table>
          </div>`;

        // Always include Actions section
        htmlContent += `
          <div class='section'>
            <h4>Actions</h4>
            <table>
              <tr>
                <th style="background-color: #ff9002; text-align: center; width: 10px">No</th>
                <th style="background-color: #ff9002;">Action</th>
                <th style="background-color: #ff9002;">Responsibility</th>
              </tr>`;

        if (siteDetails.actions && siteDetails.actions.length > 0) {
          siteDetails.actions.forEach((action, index) => {
            htmlContent += `
              <tr>
                <td style="text-align: center">${index + 1}</td>
                <td>${action.action || '-'}</td>
                <td>${action.responsibility || '-'}</td>
              </tr>`;
          });
        } else {
          htmlContent += `
            <tr>
              <td colspan="3" style="text-align: center">No actions data available</td>
            </tr>`;
        }

        htmlContent += `
            </table>
          </div>`;

        htmlContent += `
          </body>
          </html>
        `;

        const blob = new Blob([htmlContent], { type: 'application/msword' });
        const url = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `Site_Details_${siteDetails.site_id || siteDetails.sphere_id || 'Export'}.doc`;
        document.body.appendChild(link);
        link.click();

        // Clean up
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        if (onSuccess) {
          onSuccess('Word');
        }
      }).catch(err => {
        console.error('Error processing images:', err);
        if (onError) {
          onError('Word', err);
        }
      });
    } catch (err) {
      console.error('Error exporting to Word:', err);
      if (onError) {
        onError('Word', err);
      }
    }

    handleClose();
  };

  const handleExportToPdf = () => {
    if (!siteDetails) {
      if (onError) {
        onError('PDF', new Error('No site details to export'));
      }
      return;
    }

    try {
      let htmlContent = `
        <html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'>
        <head>
          <meta charset='utf-8'>
          <title>Site Details - ${siteDetails.site_name || siteDetails.site_id || 'Export'}</title>
          <style>
            body { font-family: Times New Roman, sans-serif; font-size: 13px; }
            h1, h2, h3, h4, h4, h6 { color: #333; }
            table { border-collapse: collapse; width: 100%; margin-bottom: 10px; }
            th, td { border: 2px solid #000; padding-left: 2px; text-align: left; line-height: 1;}
            .section { margin-bottom: 10px; }
            .image-container { text-align: center; margin: 5px auto; }
            .finding-image { 
              max-width: 450px; 
              width: auto; 
              height: auto; 
              max-height: 250px; 
              display: block; 
              margin: 0 auto; 
              border: 1px solid #ddd; 
            }
            .image-caption { 
              font-style: normal; 
              font-size: 13px; 
              margin-top: 3px; 
              text-align: center; 
              width: 100%;
              display: block;
            }
            .image-placeholder { 
              border: 1px dashed #ccc; 
              padding: 10px; 
              text-align: center; 
              margin: 5px auto; 
              max-width: 450px; 
            }
            @media print {
              body { -webkit-print-color-adjust: exact; }
            }
          </style>
        </head>
        <body>
          <h4>Site Details Report</h4>
          <p>Generated on: ${new Date().toLocaleString()}</p>
          
          <div class='section'>
            <table>
            <tr>
                <td style="background-color: #ff9002; width: 120px;">Date</td>
                <td><b>${siteDetails.date || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Time</td>
                <td><b>${siteDetails.time || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Requirement</td>
                <td><b>${siteDetails.requirement || '-'}</b></td>
              </tr>
               <tr>
                <td style="background-color: #ff9002;">Customer Name</td>
                <td><b>${siteDetails.customer_name || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">End Customer</td>
                <td><b>${siteDetails.end_customer || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Site Address</td>
                <td><b>${siteDetails.site_address || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Lat / Long</td>
                <td><b>${siteDetails.lat_long || '-'}</b></td>
              </tr>
               <tr>
                <td style="background-color: #ff9002;">Sphere ID</td>
                <td><b>${siteDetails.sphere_id || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">SOF No</td>
                <td><b>${siteDetails.sof_no || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">DTC No</td>
                <td><b>${siteDetails.dtc_no || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Nova ID</td>
                <td><b>${siteDetails.nova_id || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Site ID</td>
                <td><b>${siteDetails.site_id || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Site Name</td>
                <td><b>${siteDetails.site_name || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Product SLA</td>
                <td><b>${siteDetails.product_sla || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Exchange</td>
                <td><b>${siteDetails.exchange || '-'}</b></td>
              </tr>
              <tr>
                <td style="background-color: #ff9002;">Project Manager</td>
                <td><b>${siteDetails.pm || '-'}</b></td>
              </tr>
            </table>
          </div>`;

      // Always include Representatives section
      htmlContent += `
        <div class='section'>
          <h4>Representatives</h4>
          <table>
            <tr>
              <th style="background-color: #ff9002; text-align: center">No</th>
              <th style="background-color: #ff9002;">Name</th>
              <th style="background-color: #ff9002;">Unit</th>
              <th style="background-color: #ff9002;">Contact No</th>
            </tr>`;

      if (siteDetails.representatives && siteDetails.representatives.length > 0) {
        siteDetails.representatives.forEach((rep, index) => {
          htmlContent += `
            <tr>
              <td style="text-align: center">${index + 1}</td>
              <td>${rep.name || '-'}</td>
              <td>${rep.unit || '-'}</td>
              <td>${rep.contact_no || '-'}</td>
            </tr>`;
        });
      } else {
        htmlContent += `
          <tr>
            <td colspan="4" style="text-align: center">No representatives data available</td>
          </tr>`;
      }

      htmlContent += `
          </table>
        </div>`;

      // Separate table for Findings and CME Status
      htmlContent += `
        <div class='section'>
          <h4>Findings</h4>
          <table>
            <tr>
              <th style="background-color: #ff9002; text-align: center">No</th>
              <th style="background-color: #ff9002;">Finding</th>
            </tr>`;

      // Check if findings exist in siteDetails and use them
      const findingsData = siteDetails.findings || [];
      const attachmentsData = siteDetails.attachments || [];

      if (Array.isArray(findingsData) && findingsData.length > 0) {
        findingsData.forEach((finding, index) => {
          // With the dedicated API, findings now have a standard structure with a description property
          let findingText = '-';
          if (typeof finding === 'object' && finding !== null) {
            // Use the description property which is the standard field in the new structure
            findingText = finding.description || '-';
          } else if (finding) {
            // If it's a primitive value (string, number, etc.), use it directly
            // This is for backward compatibility
            findingText = finding;
          }

          // Find any image attachments associated with this finding
          const findingAttachments = attachmentsData.filter(
            (attachment) =>
              attachment.finding_id === finding.id &&
              (attachment.file_name?.toLowerCase().endsWith('.jpg') ||
                attachment.file_name?.toLowerCase().endsWith('.jpeg') ||
                attachment.file_name?.toLowerCase().endsWith('.png'))
          );

          htmlContent += `
            <tr>
              <td style="text-align: center">${index + 1}</td>
              <td>
                ${findingText}
                ${
                  findingAttachments.length > 0
                    ? findingAttachments
                        .map((attachment) => {
                          // Construct a data URL for the image if we have the base64 data
                          const imageUrl = attachment.base64Data
                            ? `data:image/${attachment.file_name?.split('.').pop()};base64,${attachment.base64Data}`
                            : null;

                          return `
                              <div class="image-container">
                                ${
                                  imageUrl
                                    ? `<img src="${imageUrl}" alt="${attachment.caption || 'Finding image'}" class="finding-image">`
                                    : `<div class="image-placeholder">
                                      <p>Image: ${attachment.file_name}</p>
                                      <p style="font-size: 11px; color: #666;">Note: Images are viewable in the application but not in exported documents.</p>
                                    </div>`
                                }
                                ${
                                  attachment.caption
                                    ? `<div class="image-caption">${attachment.caption}</div>`
                                    : ''
                                }
                              </div>
                            `;
                        })
                        .join('')
                    : ''
                }
              </td>
            </tr>`;
        });
      } else {
        htmlContent += `
          <tr>
            <td colspan="2" style="text-align: center">No findings recorded</td>
          </tr>`;
      }

      htmlContent += `
          </table>
        </div>`;

      // CME Status table
      htmlContent += `
        <div class='section'>
          <h4>CME Status</h4>
          <table>
            <tr>
              <td style="background-color: #ff9002; width: 120px;">CME Status</td>
              <td><b>${siteDetails.cme_status || '-'}</b></td>
            </tr>
          </table>
        </div>`;

      // Always include Checklists section
      htmlContent += `
        <div class='section'>
          <h4>Checklists</h4>
          <table>
            <tr>
              <th style="background-color: #ff9002; text-align: center">No</th>
              <th style="background-color: #ff9002;">Requirement</th>
              <th style="background-color: #ff9002;">Status</th>
            </tr>`;

      if (siteDetails.checklists && siteDetails.checklists.length > 0) {
        siteDetails.checklists.forEach((checklist, index) => {
          htmlContent += `
            <tr>
              <td style="text-align: center">${index + 1}</td>
              <td>${checklist.requirement || '-'}</td>
              <td>${checklist.status || '-'}</td>
            </tr>`;
        });
      } else {
        htmlContent += `
          <tr>
            <td colspan="3" style="text-align: center">No checklists data available</td>
          </tr>`;
      }

      htmlContent += `
          </table>
        </div>`;

      // Always include Actions section
      htmlContent += `
        <div class='section'>
          <h4>Actions</h4>
          <table>
            <tr>
              <th style="background-color: #ff9002; text-align: center; width: 10px">No</th>
              <th style="background-color: #ff9002;">Action</th>
              <th style="background-color: #ff9002;">Responsibility</th>
            </tr>`;

      if (siteDetails.actions && siteDetails.actions.length > 0) {
        siteDetails.actions.forEach((action, index) => {
          htmlContent += `
            <tr>
              <td style="text-align: center">${index + 1}</td>
              <td>${action.action || '-'}</td>
              <td>${action.responsibility || '-'}</td>
            </tr>`;
        });
      } else {
        htmlContent += `
          <tr>
            <td colspan="3" style="text-align: center">No actions data available</td>
          </tr>`;
      }

      htmlContent += `
          </table>
        </div>`;

      htmlContent += `
        </body>
        </html>
      `;

      // Create a hidden iframe to load the HTML content
      const iframe = document.createElement('iframe');
      iframe.style.visibility = 'hidden';
      iframe.style.position = 'fixed';
      iframe.style.right = '0';
      iframe.style.bottom = '0';
      iframe.style.width = '0';
      iframe.style.height = '0';
      iframe.style.border = '0';
      document.body.appendChild(iframe);

      // Write the HTML content to the iframe
      iframe.contentWindow.document.open();
      iframe.contentWindow.document.write(htmlContent);
      iframe.contentWindow.document.close();

      // Wait for the content to load
      setTimeout(() => {
        try {
          // Print the iframe content
          iframe.contentWindow.focus();
          iframe.contentWindow.print();

          // Clean up
          setTimeout(() => {
            document.body.removeChild(iframe);
          }, 1000);

          if (onSuccess) {
            onSuccess('PDF');
          }
        } catch (printErr) {
          console.error('Error printing PDF:', printErr);
          document.body.removeChild(iframe);
          if (onError) {
            onError('PDF', printErr);
          }
        }
      }, 1000);
    } catch (err) {
      console.error('Error exporting to PDF:', err);
      if (onError) {
        onError('PDF', err);
      }
    }

    handleClose();
  };

  return (
    <>
      <Button
        startIcon={<FileDownload />}
        onClick={handleClick}
        variant="contained"
        color="secondary"
        {...buttonProps}
      >
        Export
      </Button>
      <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
        <MenuItem onClick={handleExportToWord}>
          <Description sx={{ mr: 1 }} />
          Export to Word
        </MenuItem>
        <MenuItem onClick={handleExportToPdf}>
          <PictureAsPdf sx={{ mr: 1 }} />
          Export to PDF
        </MenuItem>
      </Menu>
    </>
  );
};

ExportOptions.propTypes = {
  siteDetails: PropTypes.object.isRequired,
  buttonProps: PropTypes.object,
  onSuccess: PropTypes.func,
  onError: PropTypes.func,
};

export default ExportOptions;
