import PropTypes from 'prop-types';
import { Typography, TextField } from '@mui/material';

const EditableField = ({ label, name, value, type = 'text', editMode, formData, onChange }) => (
  <>
    <Typography variant="body2" color="textSecondary" className="dark:text-gray-300">
      {label}
    </Typography>
    {editMode ? (
      <TextField
        fullWidth
        type={type}
        name={name}
        value={formData[name] || ''}
        onChange={onChange}
        variant="outlined"
        size="small"
        className="mb-1 mt-1 dark:text-white"
        sx={{
          '& .MuiInputBase-input': {
            color: 'inherit',
          },
          '& .MuiOutlinedInput-root': {
            '& fieldset': {
              borderColor: 'inherit',
            },
            '&:hover fieldset': {
              borderColor: 'inherit',
            },
            '&.Mui-focused fieldset': {
              borderColor: 'inherit',
            },
          },
        }}
        InputLabelProps={{
          className: 'dark:text-white',
        }}
      />
    ) : (
      <Typography variant="body2" className="dark:text-white">
        {value || '-'}
      </Typography>
    )}
  </>
);

// PropTypes for EditableField
EditableField.propTypes = {
  label: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  value: PropTypes.any,
  type: PropTypes.string,
  editMode: PropTypes.bool.isRequired,
  formData: PropTypes.object.isRequired,
  onChange: PropTypes.func.isRequired,
};

export default EditableField;
