// Next, React, Tw
import { twMerge } from 'tailwind-merge';

// Packages
import PropTypes from 'prop-types';

// Components
import ReactAnimatedNumber from '../Shared/ReactAnimatedNumber';

// Others
import { useOasysContext, getJobStatusStyle, getTaskStatusStyle } from '../../utils/oasys';
import { useParamContext } from '../../utils/auth/ParamProvider';

const SummaryCards = ({ type }) => {
  // Standard and Vars
  const { setParam } = useParamContext();
  const { jobStatusValueMap, taskStatusValueMap } = useOasysContext();
  return (
    <div className="container mx-auto flex flex-col items-start justify-between gap-1 p-1 dark:text-white md:flex-row md:p-4">
      {(type === 'job' ? jobStatusValueMap : taskStatusValueMap)?.map((o, i) => (
        <div key={i} className="flex w-full flex-col px-1 md:w-1/6">
          {/* eslint-disable-next-line jsx-a11y/no-static-element-interactions */}
          <div
            className={twMerge(
              'flex h-[90px] w-full cursor-pointer flex-col justify-between gap-2 rounded-md p-2',
              type === 'job' ? getJobStatusStyle(o?.label) : getTaskStatusStyle(o?.label)
            )}
            onClick={() => setParam({ status: o?.label })}
          >
            <p className="text-wrap text-sm font-semibold">{o?.label?.toUpperCase()}</p>
            <p className="text-right text-xl">
              <ReactAnimatedNumber
                value={o?.value}
                formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
              />
            </p>
          </div>
        </div>
      ))}
    </div>
  );
};

SummaryCards.propTypes = {
  type: PropTypes.string,
};

export default SummaryCards;
