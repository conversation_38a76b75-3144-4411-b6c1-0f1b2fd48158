// Standard and Vars
import { useRouter } from 'next/router';
import { twMerge } from 'tailwind-merge';

// Packages
import PropTypes from 'prop-types';

// Others
import { useParamContext } from '../../utils/auth/ParamProvider';
import { useOasysContext } from '../../utils/oasys';

const FilterStatusButtonsGroup = ({ type }) => {
  // Standard and Vars
  const { query } = useRouter();
  const { status } = query;

  const { jobStatusValueMap, taskStatusValueMap } = useOasysContext();
  const { setParam } = useParamContext();

  return (
    <div className="flex w-full flex-col items-center md:flex-row">
      {(type === 'job' ? jobStatusValueMap : taskStatusValueMap)?.map((o, i) => (
        <button
          key={i}
          type="button"
          className={twMerge(
            'text-oasys border-oasys w-full whitespace-nowrap border bg-white p-1 text-xs',
            status === o?.label && 'bg-oasys text-white'
          )}
          onClick={() => setParam({ status: o?.label })}
        >
          {o?.label?.toUpperCase()}
        </button>
      ))}
    </div>
  );
};

FilterStatusButtonsGroup.propTypes = {
  type: PropTypes.string,
};

export default FilterStatusButtonsGroup;
