// Next, React, Tw
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';
import { twMerge } from 'tailwind-merge';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { Edit, Add, ArrowDropDown } from '@mui/icons-material';

// Packages
import * as yup from 'yup';

// Components
import { useSnackbar } from '../Shared/snackbar';
import { TextInput, BinarySwitchInput } from '../Shared/CustomInput';
import WarnBeforeActionPopupButton from '../Shared/WarnBeforeActionPopupButton';

// Others
import { OASYS_ENDPOINT } from '../../utils/oasys';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { sortArrayOfObjectsByCertainKeyAlphabetically } from '../../utils/shared';

const AllProjects = () => {
  // Standard and Vars
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const { setParam } = useParamContext();
  const { query } = useRouter();
  const { projectId } = query;

  const [showHiddenProject, setShowHiddenProject] = useState(false);
  const [allProjects, setAllProject] = useState([]);
  const [keyword, setKeyword] = useState(null);

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const schema = yup.object({
    name: yup.string().required(`Please provide name`),
    shown: yup.boolean().required(`Please provide shown`)?.default(true),
  });
  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };

  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }

      try {
        switch (action) {
          case 'post':
            await axios.post(`${OASYS_ENDPOINT}/projects`, payload);
            break;
          case 'put':
            await axios.put(`${OASYS_ENDPOINT}/projects/${payload?.id}`, payload);
            break;
          case 'delete':
            await axios.delete(`${OASYS_ENDPOINT}/projects/${dialogData?.id}`);
            break;
          default:
            break;
        }

        enqueueSnackbar('Success', {
          variant: 'success',
        });
      } catch {
        /* empty */
        enqueueSnackbar('Error', {
          variant: 'error',
        });
      }
    }
    setDialogData({});
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const filteredProjects = keyword
    ? allProjects?.filter((o) => o?.name?.toLowerCase()?.includes(keyword?.toLowerCase()))
    : allProjects;

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${OASYS_ENDPOINT}/projects/all/keyword`);
      const temp = response?.data?.data || [];

      setAllProject(sortArrayOfObjectsByCertainKeyAlphabetically(temp, 'name'));
    } catch {
      setAllProject([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div className="flex w-full flex-col">
        <div className="flex items-center justify-between text-gray-500">
          <p className="text-xs ">Projects</p>
          <button type="button" onClick={() => handleClickOpenDialog(false)} className="">
            <Add className="text-md " />
          </button>
        </div>
        <div className="flex items-center gap-1">
          {/* eslint-disable-next-line jsx-a11y/no-static-element-interactions */}
          <div
            onClick={() => setParam({ projectId: 'all' })}
            className={twMerge(
              'flex-grow cursor-pointer px-1 text-xs text-gray-500 hover:bg-gray-700 hover:text-white',
              projectId === 'all' && 'bg-oasys text-white'
            )}
          >
            All
          </div>
        </div>
        <div className="my-1 flex items-center gap-1">
          <input
            type="text"
            placeholder="Search projects..."
            className="w-full  border border-gray-300 px-1 text-xs focus:outline-none"
            value={keyword}
            onChange={(e) => setKeyword(e.target.value)}
          />
        </div>
        {filteredProjects
          ?.filter((o) => o?.shown)
          ?.map((o, i) => (
            <div key={i} className="flex items-center gap-1">
              {/* eslint-disable-next-line jsx-a11y/no-static-element-interactions */}
              <div
                onClick={() => setParam({ projectId: o?.id })}
                className={twMerge(
                  'flex-grow cursor-pointer px-1 text-xs text-gray-500 hover:bg-gray-700 hover:text-white',
                  projectId === o?.id && 'bg-oasys text-white'
                )}
              >
                {o?.name}
              </div>
              <button type="button" onClick={() => handleClickOpenDialog(true, o)}>
                <Edit className="text-sm text-black" />
              </button>
            </div>
          ))}
        <div className="flex justify-center">
          <button type="button" onClick={() => setShowHiddenProject(!showHiddenProject)}>
            <ArrowDropDown
              className={twMerge('text-oasys text-xl', showHiddenProject && 'rotate-180')}
            />
          </button>
        </div>
        {showHiddenProject &&
          filteredProjects
            ?.filter((o) => !o?.shown)
            ?.map((o, i) => (
              <div key={i} className="flex items-center gap-1">
                {/* eslint-disable-next-line jsx-a11y/no-static-element-interactions */}
                <div
                  onClick={() => setParam({ projectId: o?.id })}
                  className={twMerge(
                    'flex-grow cursor-pointer px-1 text-xs text-gray-500 hover:bg-gray-700 hover:text-white',
                    projectId === o?.id && 'bg-oasys text-white'
                  )}
                >
                  {o?.name}
                </div>
                <button type="button" onClick={() => handleClickOpenDialog(true, o)}>
                  <Edit className="text-sm text-black" />
                </button>
              </div>
            ))}
      </div>
      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-oasys">
          <p className="text-center text-white">{!editModeDialog ? 'New' : 'Edit'} Project</p>
        </DialogTitle>
        <DialogContent>
          <div className="flex w-[400px] flex-col gap-2 px-2 py-4">
            <TextInput
              name="name"
              value={dialogData?.name}
              placeholder="Name"
              onChange={handleDialogDataChange}
            />
            <BinarySwitchInput
              name="shown"
              value={dialogData?.shown}
              placeholder="Show Folder"
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full items-center justify-between">
            <div className="flex gap-2">
              <button type="button" onClick={() => handleDialogClose(false)} className="">
                Close
              </button>
              {editModeDialog && (
                <WarnBeforeActionPopupButton onApprove={() => handleDialogClose('delete')} />
              )}
            </div>
            <div className="flex gap-2">
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-oasys cta-btn"
                >
                  Create
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-oasys cta-btn"
                >
                  Save
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default AllProjects;
