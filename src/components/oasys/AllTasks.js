// Next, React, Tw
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Switch, Pagination, Divider } from '@mui/material';

// Packages
import PropTypes from 'prop-types';
import moment from 'moment';

// Components
import { UpdateTask } from './Task';
import BulkTasksUpdate from './BulkTasksUpdate';

// Others
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { AUM_ENDPOINT } from '../../utils/aum';
import { OASYS_ENDPOINT } from '../../utils/oasys';
import { useSimiContext } from '../../utils/simi';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { setTasksTableData, setAllProcessors } from '../../utils/store/oasysReducer';

const AllTasks = ({ endpoint }) => {
  // Standard and Vars
  const dispatch = useDispatch();
  const { moduleColorCode } = useSimiContext();
  const { query } = useRouter();
  const { jobId, viewMode } = query;
  const { replaceParam, setParam } = useParamContext();

  const { tasksTableData, role } = useSelector((state) => state.oasys);

  // Table
  const [page, setPage] = useState(1);

  // Others
  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${OASYS_ENDPOINT}/tasks/${endpoint}`);
      let temp = response?.data?.data || [];
      if (endpoint?.includes('processor')) temp = temp?.filter((o) => !o?.completed);
      temp = temp?.map((o) => ({
        ...o,
        aging: moment().startOf('day').diff(moment.unix(o?.created_at).startOf('day'), 'days'),
      }));
      dispatch(setTasksTableData(temp));
    } catch {
      dispatch(setTasksTableData([]));
    }
    dispatch(setIsLoading(false));
  };

  const fetchAllProcessors = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${AUM_ENDPOINT}/user/v1/module/oasys/all`);
      let temp = response?.data?.data || [];
      temp = temp?.filter((o) =>
        o?.modules?.[0]?.submodules?.find((p) => p?.submodule === 'processor' && p?.role === 'user')
      );
      dispatch(setAllProcessors(temp));
    } catch {
      dispatch(setAllProcessors([]));
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [jobId]);

  useEffect(() => {
    fetchAllProcessors();
  }, []);

  useEffect(() => {
    if (viewMode) return;
    replaceParam({ viewMode: 'single' });
  }, [viewMode]);

  return (
    <div className="flex flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
      <div className="flex w-full justify-end">
        {['executive', 'sales']?.includes(role) && (
          <div className="flex items-center">
            <p className="text-sm">Single View</p>
            <Switch
              checked={viewMode === 'bulk'}
              sx={{
                '& .MuiSwitch-switchBase': {
                  color: moduleColorCode,
                },
                '& .MuiSwitch-switchBase + .MuiSwitch-track': {
                  backgroundColor: moduleColorCode,
                },
                '& .MuiSwitch-switchBase.Mui-checked': {
                  color: moduleColorCode,
                },
                '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                  backgroundColor: moduleColorCode,
                },
              }}
              onChange={() => {
                if (viewMode === 'bulk') {
                  setParam({ viewMode: 'single' });
                  return;
                }
                setParam({ viewMode: 'bulk' });
              }}
            />
            <p className="text-sm">Bulk View</p>
          </div>
        )}
      </div>

      {tasksTableData?.length !== 0 && (
        <>
          {viewMode === 'single' && (
            <>
              <div className="flex w-full justify-center">
                <Pagination
                  count={tasksTableData?.length}
                  shape="rounded"
                  size="small"
                  sx={{
                    '& .MuiPaginationItem-root': { color: moduleColorCode },
                    '& .MuiPaginationItem-root.Mui-selected': {
                      backgroundColor: moduleColorCode,
                      color: 'white',
                    },
                    '& .MuiPaginationItem-ellipsis': { color: 'gray' },
                  }}
                  onChange={(event, newPage) => setPage(newPage)}
                />
              </div>
              <Divider />
              <UpdateTask
                key={tasksTableData?.length}
                taskId={tasksTableData?.[page - 1]?.id}
                disabled={false}
              />
            </>
          )}
          {viewMode === 'bulk' && <BulkTasksUpdate fetchData={fetchData} />}
        </>
      )}
      {tasksTableData?.length === 0 && <p className="text-center text-xs">No tasks, hooray :-p</p>}
    </div>
  );
};

AllTasks.propTypes = {
  endpoint: PropTypes.string,
};

export default AllTasks;
