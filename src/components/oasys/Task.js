// Next, React, Tw
import { useState, useEffect } from 'react';
import { twMerge } from 'tailwind-merge';
import { useSelector, useDispatch } from 'react-redux';

// Mui
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip,
  Stepper,
  Step,
  StepLabel,
  Menu,
} from '@mui/material';
import { Check, KeyboardArrowUp } from '@mui/icons-material';

// Packages
import PropTypes from 'prop-types';
import { useSnackbar } from 'notistack';
import * as yup from 'yup';

// Components
import { TextInput, SelectInput, DateInput, TextAreaInput } from '../Shared/CustomInput';
import CopyButton from '../Shared/CopyButton';
import AttachmentBox from '../Shared/AttachmentBox';
import RemarkBox from '../Shared/RemarkBox';
import HistoryBox from '../Shared/HistoryBox';
import WarnBeforeActionPopupButton from '../Shared/WarnBeforeActionPopupButton';

// Others
import {
  JOB_TYPE_KEY_LABEL_MAP,
  PROCESSOR_UPDATES_KEY_LABEL_MAP,
  OASYS_ENDPOINT,
  useOasysContext,
  TASK_STATUS_STYLE_MAP,
} from '../../utils/oasys';
import { useSimiContext } from '../../utils/simi';
import { setIsLoading, setIsSaving } from '../../utils/store/loadingReducer';
import axios from '../../utils/axios';

// ----------------------------------------------------------------------

// eslint-disable-next-line arrow-body-style
export const NewTask = ({ taskType, data, onChange, disabled }) => {
  // Standard and Vars

  return (
    <div className="relative flex w-full flex-col gap-8">
      <p className="text-md text-center font-semibold underline">{taskType}</p>

      <p className="text-center text-sm">Task Details</p>
      <div className="flex w-full flex-wrap">
        {(JOB_TYPE_KEY_LABEL_MAP?.[taskType] || [])?.map((o, i) => (
          <div key={i} className="flex w-full items-center gap-2 p-2 md:w-1/2">
            <div className="flex-grow">
              {(() => {
                if (o?.label?.toLowerCase()?.includes('date'))
                  return (
                    <DateInput
                      views={['year', 'month', 'day']}
                      name={o?.key}
                      value={data?.[o?.key]}
                      placeholder={o?.label}
                      returnedFormat="YYYY-MM-DD"
                      onChange={onChange}
                      showRedAsteric={false}
                    />
                  );
                if (o?.label?.toLowerCase()?.includes('remark'))
                  return (
                    <TextAreaInput
                      name={o?.key}
                      value={data?.[o?.key]}
                      placeholder={o?.label}
                      onChange={onChange}
                      showRedAsteric={false}
                    />
                  );
                if (o?.options)
                  return (
                    <SelectInput
                      name={o?.key}
                      value={data?.[o?.key]}
                      placeholder={o?.label}
                      options={o?.options?.split(',')?.map((p) => ({ value: p, label: p }))}
                      onChange={onChange}
                      showRedAsteric={false}
                    />
                  );
                return (
                  <TextInput
                    type={
                      taskType?.toLowerCase()?.includes('charge') ||
                      taskType?.toLowerCase()?.includes('price')
                        ? 'number'
                        : 'text'
                    }
                    name={o?.key}
                    value={data?.[o?.key]}
                    placeholder={o?.label}
                    onChange={onChange}
                    showRedAsteric={false}
                  />
                );
              })()}
            </div>
            <CopyButton content={data?.[o?.key]} />
          </div>
        ))}
        <div className="flex w-full items-center justify-center gap-2 p-2 md:w-1/2">
          <p className="text-xs">Attachment: </p>
          <input
            type="file"
            className=""
            onChange={(event) =>
              onChange({ target: { name: 'file', value: event.target.files[0] } })
            }
          />
        </div>
      </div>
    </div>
  );
};

NewTask.propTypes = {
  taskType: PropTypes.string,
  data: PropTypes.any,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
};

// ----------------------------------------------------------------------

// eslint-disable-next-line arrow-body-style
export const UpdateTask = ({ taskId, disabled }) => {
  // Standard and Vars
  const { enqueueSnackbar } = useSnackbar();
  const { userSubModules } = useSelector((state) => state.aum);
  const { allProcessors, tasksTableData, role } = useSelector((state) => state.oasys);
  const { newTaskSchema, saveTaskSchema, handleCreateHistory } = useOasysContext();
  const dispatch = useDispatch();
  const { moduleColorCode } = useSimiContext();

  const STATUSES = Object?.keys(TASK_STATUS_STYLE_MAP);
  const [anchorEl, setAnchorEl] = useState(null);
  const [showProcessorUpdatesSection, setShowProcessorUpdatesSection] = useState(false);
  const [originalTask, setOriginalTask] = useState(null);
  const [task, setTask] = useState(null);
  const [taskData, setTaskData] = useState(null);

  // Form
  const handleFormDataChange = (event) => {
    const { name, value } = event.target;
    setTaskData((prev) => ({ ...prev, [name]: value }));
  };

  const handleUpdateJobStatus = async () => {
    try {
      let response = await axios.get(`${OASYS_ENDPOINT}/tasks/incomplete/${originalTask?.job_id}`);
      const temp = response?.data?.data || null;
      if (!temp)
        response = await axios.put(`${OASYS_ENDPOINT}/status/${originalTask?.job_id}`, {
          status: 'completed',
        });
      enqueueSnackbar('Job Status Updated');
    } catch {
      /* empty */
    }
  };

  const handleFormSubmit = async (passedTask, passedTaskData) => {
    let payload;
    try {
      payload = await saveTaskSchema.validate(
        { ...(passedTask || task), data: passedTaskData || taskData },
        { abortEarly: false }
      );
    } catch (error) {
      enqueueSnackbar(error?.errors, {
        variant: 'error',
      });
      setTask(originalTask);
      return;
    }

    if (task?.status !== originalTask?.status)
      await handleCreateHistory(taskId, `Status changed to ${task?.status?.toUpperCase()}`);

    dispatch(setIsSaving(true));
    try {
      const response = await axios.put(`${OASYS_ENDPOINT}/tasks/${taskId}`, payload);
      const temp = response?.data?.data || null;
      await handleUpdateJobStatus();
      setTask({ ...temp, data: undefined });
      setOriginalTask({ ...temp, data: undefined });
      setTaskData(temp?.data);
    } catch {
      enqueueSnackbar('Error', {
        variant: 'error',
      });
    }
    dispatch(setIsSaving(false));
  };

  const handleCreateDuplicateTask = async () => {
    let payload = {
      ...task,
      status: 'pending review',
      data: { ...taskData, order_no: '', processor_staff_id: '' },
    };
    try {
      payload = await newTaskSchema.validate(payload, { abortEarly: false });
    } catch (error) {
      enqueueSnackbar(error?.errors, {
        variant: 'error',
      });
      throw new Error('Error');
    }
    dispatch(setIsLoading(true));
    try {
      await axios.post(`${OASYS_ENDPOINT}/tasks`, payload);
    } catch {
      enqueueSnackbar('Error', {
        variant: 'error',
      });
      throw new Error('Error');
    } finally {
      dispatch(setIsLoading(false));
    }
  };

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const schema = yup.object().shape({
    status: yup.string().required('Status is required'),
    remarks: yup.string().required('Remarks is required'),
  });

  const schema2 = yup.object().shape({
    blockage_type: yup.string().required('Blockage type is required'),
  });

  const handleDialogClose = async (action) => {
    if (action) {
      try {
        await schema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error?.errors, {
          variant: 'error',
        });
        return;
      }

      if (dialogData?.status === 'blocked')
        try {
          await schema2.validate(dialogData, { abortEarly: false });
        } catch (error) {
          enqueueSnackbar(error?.errors, {
            variant: 'error',
          });
          return;
        }

      await handleFormSubmit({ ...task, status: dialogData?.status });
      if (dialogData?.status === 'blocked')
        await handleCreateHistory(taskId, `Blockage type is ${dialogData?.blockage_type}`);
      await handleCreateHistory(taskId, `Status changed with remark: ${dialogData?.remarks}`);
      if (dialogData?.status === 'cancel-reraise') await handleCreateDuplicateTask();
      if (window) window?.location?.reload();
    }
    setAnchorEl(null);
    setDialogOpen(false);
  };

  // Others

  useEffect(() => {
    if (!taskId) return;
    if (tasksTableData?.length === 0) return;
    const temp = tasksTableData?.find((o) => o?.id === taskId);
    setTask(() => ({ ...temp, data: undefined }));
    setOriginalTask(() => ({ ...temp, data: undefined }));
    setTaskData(() => temp?.data);
  }, [taskId, tasksTableData?.length]);

  useEffect(() => {
    if (!task?.id) return;
    if (JSON?.stringify(task) !== JSON?.stringify(originalTask)) handleFormSubmit();
  }, [JSON?.stringify(task)]);

  return (
    <>
      <p className="text-md text-center font-semibold underline">{task?.task_type}</p>
      <div className="w-full overflow-x-scroll md:overflow-x-auto">
        <Stepper
          activeStep={(() => STATUSES?.findIndex((o) => o === originalTask?.status))()}
          alternativeLabel
        >
          {STATUSES.map((o, i) => (
            <Step
              key={i}
              sx={{
                '& .MuiStepLabel-root .Mui-completed': {
                  color: moduleColorCode,
                },
                '& .MuiStepLabel-root .Mui-active': {
                  color: moduleColorCode,
                },
              }}
            >
              <StepLabel>
                <p className="whitespace-nowrap text-xs font-semibold text-black">
                  {o?.toUpperCase()}
                </p>
              </StepLabel>
            </Step>
          ))}
        </Stepper>
      </div>
      <div className="relative flex w-full flex-col gap-4">
        <div className="flex items-center justify-between">
          <div className="flex flex-col gap-2">
            <div className="flex w-full flex-col gap-4 md:w-[250px]">
              <SelectInput
                name="processor_staff_id"
                value={task?.processor_staff_id}
                placeholder="Assigned Processor"
                options={allProcessors?.map((o) => ({ label: o?.name, value: o?.staff_id }))}
                defaultLabel="-"
                onChange={(event) => {
                  const { name, value } = event?.target;
                  setTask((prev) => ({
                    ...prev,
                    [name]: value,
                  }));
                }}
                showRedAsteric
                disabled={!['executive']?.includes(role) || task?.status === 'completed'}
              />
            </div>
            {['executive', 'processor']?.includes(role) && (
              <button
                type="button"
                className="bg-oasys cta-btn"
                onClick={(event) => setAnchorEl(event.currentTarget)}
              >
                Quick Action
              </button>
            )}

            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={() => setAnchorEl(null)}
              className="w-full"
            >
              <div className="flex w-full flex-col">
                {originalTask?.status !== 'completed' && originalTask?.status !== 'blocked' && (
                  <button
                    type="button"
                    className="w-full px-4 text-xs hover:bg-gray-500 hover:text-white"
                    onClick={() => {
                      setDialogData((prev) => ({ ...prev, status: 'blocked' }));
                      setDialogOpen(true);
                    }}
                  >
                    Mark as blocked
                  </button>
                )}
                {originalTask?.status !== 'canceled' && (
                  <button
                    type="button"
                    className="w-full px-4 text-xs hover:bg-gray-500 hover:text-white"
                    onClick={() => {
                      setDialogData((prev) => ({ ...prev, status: 'canceled' }));
                      setDialogOpen(true);
                    }}
                  >
                    Sales request to cancel
                  </button>
                )}
                {originalTask?.status !== 'cancel-reraise' && (
                  <button
                    type="button"
                    className="w-full px-4 text-xs hover:bg-gray-500 hover:text-white"
                    onClick={() => {
                      setDialogData((prev) => ({ ...prev, status: 'cancel-reraise' }));
                      setDialogOpen(true);
                    }}
                  >
                    Cancel and re-raise
                  </button>
                )}
              </div>
            </Menu>
          </div>
          <Tooltip title="Reviewed" className="">
            <button
              type="button"
              className={twMerge(
                'rounded-full border border-gray-500',
                ['in progress', 'completed']?.includes(task?.status) && 'bg-oasys border-0'
              )}
              onClick={() => {
                setTask((prev) => ({
                  ...prev,
                  status: prev?.status !== 'in progress' ? 'in progress' : 'pending review',
                }));
              }}
              disabled={!['executive']?.includes(role) || task?.status === 'completed'}
            >
              <Check
                className={twMerge(
                  'h-[35px] w-[35px] text-gray-500',
                  ['in progress', 'completed']?.includes(task?.status) && 'text-white'
                )}
              />
            </button>
          </Tooltip>
        </div>

        <p className="text-center text-sm">Processor Updates</p>
        <div className="flex w-full flex-wrap">
          {(PROCESSOR_UPDATES_KEY_LABEL_MAP || [])?.map((o, i) => (
            <div key={i} className="flex w-full items-center gap-2 p-2 md:w-1/2">
              <div className="flex-grow">
                {(() => {
                  if (o?.label?.toLowerCase()?.includes('date'))
                    return (
                      <DateInput
                        views={['year', 'month', 'day']}
                        name={o?.key}
                        value={taskData?.[o?.key]}
                        placeholder={o?.label}
                        returnedFormat="YYYY-MM-DD"
                        onChange={(event) => {
                          const { name, value } = event?.target;
                          handleFormDataChange(event);
                          handleFormSubmit(undefined, { ...taskData, [name]: value });
                        }}
                        disabled
                      />
                    );
                  if (o?.label?.toLowerCase()?.includes('remark'))
                    return (
                      <TextAreaInput
                        name={o?.key}
                        value={taskData?.[o?.key]}
                        placeholder={o?.label}
                        onChange={handleFormDataChange}
                        onBlur={() => handleFormSubmit()}
                        disabled
                      />
                    );
                  return (
                    <TextInput
                      type={
                        task?.task_type?.toLowerCase()?.includes('charge') ||
                        task?.task_type?.toLowerCase()?.includes('price')
                          ? 'number'
                          : 'text'
                      }
                      name={o?.key}
                      value={taskData?.[o?.key]}
                      placeholder={o?.label}
                      onChange={handleFormDataChange}
                      onBlur={() => handleFormSubmit()}
                      disabled
                    />
                  );
                })()}
              </div>
              <CopyButton content={taskData?.[o?.key]} />
            </div>
          ))}
        </div>

        <p className="text-center text-sm">Task Details</p>
        <div className="flex w-full flex-wrap">
          {(JOB_TYPE_KEY_LABEL_MAP?.[task?.task_type] || [])?.map((o, i) => (
            <div key={i} className="flex w-full items-center gap-2 p-2 md:w-1/2">
              <div className="flex-grow">
                {(() => {
                  if (o?.label?.toLowerCase()?.includes('date'))
                    return (
                      <DateInput
                        views={['year', 'month', 'day']}
                        name={o?.key}
                        value={taskData?.[o?.key]}
                        placeholder={o?.label}
                        returnedFormat="YYYY-MM-DD"
                        onChange={handleFormDataChange}
                        onBlur={() => handleFormSubmit()}
                        disabled
                        showRedAsteric={false}
                      />
                    );
                  if (o?.label?.toLowerCase()?.includes('remark'))
                    return (
                      <TextAreaInput
                        name={o?.key}
                        value={taskData?.[o?.key]}
                        placeholder={o?.label}
                        onChange={handleFormDataChange}
                        onBlur={() => handleFormSubmit()}
                        disabled
                        showRedAsteric={false}
                      />
                    );
                  if (o?.options)
                    return (
                      <SelectInput
                        name={o?.key}
                        value={taskData?.[o?.key]}
                        placeholder={o?.label}
                        options={o?.options?.split(',')?.map((p) => ({ value: p, label: p }))}
                        onChange={handleFormDataChange}
                        onBlur={() => handleFormSubmit()}
                        disabled
                        showRedAsteric={false}
                      />
                    );
                  return (
                    <TextInput
                      type={
                        task?.task_type?.toLowerCase()?.includes('charge') ||
                        task?.task_type?.toLowerCase()?.includes('price')
                          ? 'number'
                          : 'text'
                      }
                      name={o?.key}
                      value={taskData?.[o?.key]}
                      placeholder={o?.label}
                      onChange={handleFormDataChange}
                      onBlur={() => handleFormSubmit()}
                      disabled
                      showRedAsteric={false}
                    />
                  );
                })()}
              </div>
              <CopyButton content={taskData?.[o?.key]} />
            </div>
          ))}
        </div>

        <div className="container mx-auto flex flex-col gap-4 p-1 md:flex-row md:p-4">
          <div className="flex w-full flex-col gap-8 rounded-xl bg-white p-4  dark:bg-gray-600 dark:text-white md:w-1/3">
            <AttachmentBox
              key={taskId}
              TITLE="Attachment"
              GET_ALL_FILES_ENDPOINT={`${OASYS_ENDPOINT}/files/task/${taskId}`}
              UPLOAD_CERTAIN_FILE_ENDPOINT={`${OASYS_ENDPOINT}/files/upload/task/${taskId}`}
              DOWNLOAD_CERTAIN_FILE_ENDPOINT={`${OASYS_ENDPOINT}/files/download`}
              DELETE_CERTAIN_FILE_ENDPOINT={`${OASYS_ENDPOINT}/files`}
            />
          </div>
          <div className="flex w-full flex-col gap-8 rounded-xl bg-white p-4  dark:bg-gray-600 dark:text-white md:w-1/3">
            <HistoryBox
              key={taskId}
              GET_ALL_HISTORIES_ENDPOINT={`${OASYS_ENDPOINT}/historys/task_id/${taskId}`}
            />
          </div>
          <div className="flex w-full flex-col gap-8 rounded-xl bg-white p-4  dark:bg-gray-600 dark:text-white md:w-1/3">
            <RemarkBox
              key={taskId}
              documentIdKey="task_id"
              documentIdValue={taskId}
              GET_ALL_REMARKS_ENDPOINT={`${OASYS_ENDPOINT}/remarks/task_id/${taskId}`}
              POST_NEW_REMARK_ENDPOINT={`${OASYS_ENDPOINT}/remarks`}
            />
          </div>
        </div>
      </div>
      <div
        className={twMerge(
          'fixed bottom-0 left-1/2 z-50 flex w-full -translate-x-1/2 transform flex-col  gap-4  px-8 py-4',
          showProcessorUpdatesSection && 'bg-gray-200'
        )}
      >
        <div className="flex w-full justify-center">
          {['processor']?.includes(role) && (
            <button
              type="button"
              className="bg-oasys rounded-full p-1"
              onClick={() => setShowProcessorUpdatesSection((prev) => !prev)}
            >
              <KeyboardArrowUp
                className={twMerge('text-white', showProcessorUpdatesSection && 'rotate-180')}
              />
            </button>
          )}
        </div>

        {showProcessorUpdatesSection && (
          <>
            <p className="text-center text-sm">Processor Updates</p>
            <div className="flex w-full flex-wrap">
              {(PROCESSOR_UPDATES_KEY_LABEL_MAP || [])?.map((o, i) => (
                <div key={i} className="flex w-full items-center gap-2 p-2 md:w-1/2">
                  <div className="flex-grow">
                    {(() => {
                      if (o?.label?.toLowerCase()?.includes('date'))
                        return (
                          <DateInput
                            views={['year', 'month', 'day']}
                            name={o?.key}
                            value={taskData?.[o?.key]}
                            placeholder={o?.label}
                            returnedFormat="YYYY-MM-DD"
                            onChange={(event) => {
                              const { name, value } = event?.target;
                              handleFormDataChange(event);
                              handleFormSubmit(undefined, { ...taskData, [name]: value });
                            }}
                            disabled={
                              !userSubModules?.includes('processor') || task?.status === 'completed'
                            }
                          />
                        );
                      if (o?.label?.toLowerCase()?.includes('remark'))
                        return (
                          <TextAreaInput
                            name={o?.key}
                            value={taskData?.[o?.key]}
                            placeholder={o?.label}
                            onChange={handleFormDataChange}
                            onBlur={() => handleFormSubmit()}
                            disabled={
                              !userSubModules?.includes('processor') || task?.status === 'completed'
                            }
                          />
                        );
                      return (
                        <TextInput
                          type={
                            task?.task_type?.toLowerCase()?.includes('charge') ||
                            task?.task_type?.toLowerCase()?.includes('price')
                              ? 'number'
                              : 'text'
                          }
                          name={o?.key}
                          value={taskData?.[o?.key]}
                          placeholder={o?.label}
                          onChange={handleFormDataChange}
                          onBlur={() => handleFormSubmit()}
                          disabled={
                            !userSubModules?.includes('processor') || task?.status === 'completed'
                          }
                        />
                      );
                    })()}
                  </div>
                  <CopyButton content={taskData?.[o?.key]} />
                </div>
              ))}
              {task?.status !== 'completed' && (
                <div className="flex w-full justify-end gap-2 p-2 md:w-1/2">
                  <WarnBeforeActionPopupButton
                    onCancel={() => {}}
                    onApprove={() =>
                      setTask((prev) => ({
                        ...prev,
                        status: 'completed',
                      }))
                    }
                    disabled={
                      !userSubModules?.includes('processor') || task?.status === 'completed'
                    }
                    positiveSentiment
                  >
                    <p
                      className={twMerge(
                        'bg-oasys cta-btn',
                        (!userSubModules?.includes('processor') || task?.status === 'completed') &&
                          'bg-gray-500'
                      )}
                    >
                      Mark Completed
                    </p>
                  </WarnBeforeActionPopupButton>
                </div>
              )}
            </div>
          </>
        )}
      </div>
      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-oasys text-center text-white">{`Mark task as ${dialogData?.status?.toUpperCase()}`}</DialogTitle>
        <DialogContent>
          <div className="my-8 flex w-full flex-col gap-4 p-2 md:w-[400px]">
            {dialogData?.status === 'blocked' && (
              <SelectInput
                name="blockage_type"
                value={dialogData?.blockage_type}
                placeholder="Blockage Type"
                options={[
                  'System Issue',
                  'Open Order',
                  'Sales Hold',
                  'Pending Sales Verification',
                  'Inadequate Info or Documentation',
                  'Advance Termination Request',
                  'Send to Survey',
                  'Send for Feasability Check',
                ]}
                onChange={handleDialogDataChange}
              />
            )}

            <TextAreaInput
              name="remarks"
              value={dialogData?.remarks}
              placeholder="Remarks"
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <div className="flex gap-2">
              <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
                Cancel
              </button>
            </div>

            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => handleDialogClose('put')}
                className="bg-oasys cta-btn"
              >
                Update
              </button>
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

UpdateTask.propTypes = {
  taskId: PropTypes.string,
  disabled: PropTypes.bool,
};
