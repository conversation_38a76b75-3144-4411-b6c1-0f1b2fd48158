// Next, React, Tw
import { useSelector, useDispatch } from 'react-redux';
import { twMerge } from 'tailwind-merge';
import { useState } from 'react';

// Mui
import { Check } from '@mui/icons-material';

// Packages
import { useSnackbar } from 'notistack';
import PropTypes from 'prop-types';

// Components
import { TablePaginationCustom } from '../Shared/table';
import { SelectInput } from '../Shared/CustomInput';
import WarnBeforeActionPopupButton from '../Shared/WarnBeforeActionPopupButton';
import ExportExcelButton from '../Shared/ExportExcelButton';

// Others
import { checkAndReplaceStringWithHyphen } from '../../utils/shared';
import { setTasksTableData } from '../../utils/store/oasysReducer';
import {
  OASYS_ENDPOINT,
  useOasysContext,
  getTaskStatusStyle,
  TASKS_TABLE_HEADERS_FOR_EXPORT,
} from '../../utils/oasys';
import { setIsSaving } from '../../utils/store/loadingReducer';
import axios from '../../utils/axios';
import { useSimiContext } from '../../utils/simi';

const BulkTasksUpdate = ({ fetchData }) => {
  // Standard and Vars
  const dispatch = useDispatch();
  const { page, rowsPerPage } = useSelector((state) => state.simi);
  const { originalJobData, allProcessors, tasksTableData, role } = useSelector(
    (state) => state.oasys
  );
  const { saveTaskSchema } = useOasysContext();
  const { enqueueSnackbar } = useSnackbar();
  const { getCertainStaffInfoFromStaffId } = useSimiContext();

  // Form

  const [selectedStaffId, setSelectedStaffId] = useState('');

  const showUpdatesSection = tasksTableData?.filter((o) => o?.selected).length > 0;

  // Table
  const bodyCellStyle = 'text-center py-1 px-2 text-sm whitespace-nowrap';

  // Others

  const handleCellClick = (row) => {
    if (!['executive']?.includes(role)) return;
    if (row?.status === 'completed') return;
    dispatch(
      setTasksTableData(
        tasksTableData?.map((o) => ({
          ...o,
          selected: o?.id === row?.id ? !o?.selected : o?.selected,
        }))
      )
    );
  };

  const handleSingleTaskUpdate = async (data) => {
    let payload;
    try {
      payload = await saveTaskSchema.validate(data, { abortEarly: false });
    } catch (error) {
      enqueueSnackbar(error?.errors, {
        variant: 'error',
      });
      return false;
    }

    dispatch(setIsSaving(true));
    try {
      await axios.put(`${OASYS_ENDPOINT}/tasks/${data?.id}`, payload);
    } catch {
      enqueueSnackbar('Error', {
        variant: 'error',
      });
      return false;
    } finally {
      dispatch(setIsSaving(false));
    }
    return true;
  };

  const handleBulkUpdates = async () => {
    let filteredTasks = tasksTableData?.filter((o) => o?.selected);
    filteredTasks = filteredTasks?.map((o) => ({
      ...o,
      processor_staff_id: selectedStaffId,
    }));
    for (let i = 0; i < filteredTasks.length; i += 1) {
      await handleSingleTaskUpdate(filteredTasks[i]);
    }
    if (fetchData) fetchData();
  };

  return (
    <>
      <div className="flex items-center justify-end">
        <ExportExcelButton
          data={tasksTableData?.map((o) => ({
            ...o,
            ...o?.data,
          }))}
          headers={TASKS_TABLE_HEADERS_FOR_EXPORT}
        />
      </div>
      <div className="overflow-x-auto scrollbar">
        <table className="min-w-full bg-white text-black">
          <thead>
            <tr>
              {[
                'No.',
                'Job ID',
                'Task ID',
                'Customer',
                'Quote',
                'Site',
                'Order No.',
                'Service ID',
                'Status',
                'Aging',
                'Requestor',
                'Processor',
                'Reviewed',
              ].map((label, i) => (
                <td key={i} className="bg-oasys whitespace-nowrap px-4 text-center text-white">
                  {label}
                </td>
              ))}
            </tr>
          </thead>
          <tbody>
            {tasksTableData
              ?.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              ?.map((row, i) => (
                <tr
                  key={i}
                  className={twMerge(
                    'cursor-pointer bg-white text-black',
                    row?.selected && 'bg-black text-white'
                  )}
                >
                  <td className={bodyCellStyle} onClick={() => handleCellClick(row)}>
                    {i + 1}
                  </td>
                  <td className={bodyCellStyle} onClick={() => handleCellClick(row)}>
                    {checkAndReplaceStringWithHyphen(row?.job_id)}
                  </td>
                  <td className={bodyCellStyle} onClick={() => handleCellClick(row)}>
                    {checkAndReplaceStringWithHyphen(row?.task_id)}
                  </td>
                  <td className={bodyCellStyle} onClick={() => handleCellClick(row)}>
                    {checkAndReplaceStringWithHyphen(row?.data?.['Customer Name'])}
                  </td>
                  <td className={bodyCellStyle} onClick={() => handleCellClick(row)}>
                    {checkAndReplaceStringWithHyphen(row?.data?.['Quote Name'])}
                  </td>
                  <td className={bodyCellStyle} onClick={() => handleCellClick(row)}>
                    {checkAndReplaceStringWithHyphen(row?.data?.['Site ID/Site Name'])}
                  </td>
                  <td className={bodyCellStyle} onClick={() => handleCellClick(row)}>
                    {checkAndReplaceStringWithHyphen(row?.data?.order_no)}
                  </td>
                  <td className={bodyCellStyle} onClick={() => handleCellClick(row)}>
                    {checkAndReplaceStringWithHyphen(row?.data?.service_id)}
                  </td>
                  <td
                    className={twMerge(bodyCellStyle, getTaskStatusStyle(row?.status))}
                    onClick={() => handleCellClick(row)}
                  >
                    {checkAndReplaceStringWithHyphen(row?.status?.toUpperCase())}
                  </td>
                  <td
                    className={twMerge(
                      bodyCellStyle,
                      !['blocked', 'completed']?.includes(row?.status) &&
                        row?.aging > 3 &&
                        'bg-red-500 text-white'
                    )}
                  >
                    {checkAndReplaceStringWithHyphen(
                      ['blocked', 'completed']?.includes(row?.status) ? '-' : row?.aging
                    )}
                  </td>
                  <td className={bodyCellStyle} onClick={() => handleCellClick(row)}>
                    {checkAndReplaceStringWithHyphen(
                      getCertainStaffInfoFromStaffId(originalJobData?.created_by_staff_id, 'name')
                    )}
                  </td>
                  <td className={bodyCellStyle} onClick={() => handleCellClick(row)}>
                    {checkAndReplaceStringWithHyphen(
                      getCertainStaffInfoFromStaffId(row?.processor_staff_id, 'name')
                    )}
                  </td>
                  <td className={bodyCellStyle}>
                    <button
                      type="button"
                      disabled={!['executive']?.includes(role) || row?.status === 'completed'}
                      onClick={async () => {
                        const newStatus =
                          row?.status === 'in progress' ? 'pending review' : 'in progress';
                        const isSuccess = await handleSingleTaskUpdate({
                          ...row,
                          status: newStatus,
                        });
                        if (!isSuccess) return;
                        dispatch(
                          setTasksTableData(
                            tasksTableData?.map((o) => ({
                              ...o,
                              status: o?.id === row?.id ? newStatus : o?.status,
                            }))
                          )
                        );
                      }}
                    >
                      {' '}
                      <Check
                        className={twMerge(
                          'h-[35px] w-[35px] text-gray-500',
                          ['in progress', 'completed']?.includes(row?.status) && 'text-oasys'
                        )}
                      />
                    </button>
                  </td>
                </tr>
              ))}
          </tbody>
        </table>
      </div>
      <TablePaginationCustom count={tasksTableData.length} />
      <p className="-mt-8 text-center text-xs text-gray-500">Click on any row to select</p>
      {showUpdatesSection && (
        <div
          className={twMerge(
            'border-oasys fixed bottom-0 left-1/2 z-50 flex w-full -translate-x-1/2 transform items-center justify-center  gap-4 border-t-2 bg-white px-16 pb-16 pt-4'
          )}
        >
          <div className="w-[300px]">
            <SelectInput
              value={selectedStaffId}
              placeholder="New Assigned Processor"
              options={allProcessors?.map((o) => ({ label: o?.name, value: o?.staff_id }))}
              defaultLabel="-"
              onChange={(event) => setSelectedStaffId(event?.target?.value)}
              showRedAsteric
            />
          </div>
          <WarnBeforeActionPopupButton
            onCancel={() => {}}
            onApprove={() => handleBulkUpdates()}
            disabled={false}
            positiveSentiment
          >
            <p className={twMerge('bg-oasys cta-btn')}>Save</p>
          </WarnBeforeActionPopupButton>
        </div>
      )}
    </>
  );
};

BulkTasksUpdate.propTypes = {
  fetchData: PropTypes.func,
};

export default BulkTasksUpdate;
