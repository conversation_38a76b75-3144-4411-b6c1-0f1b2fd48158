// Next, React, Tw
import React from 'react';
import { useRouter } from 'next/router';
import { useSelector } from 'react-redux';
import { twMerge } from 'tailwind-merge';

// Mui
import { Visibility } from '@mui/icons-material';

// Packages
import moment from 'moment';

// Components
import { TablePaginationCustom } from '../Shared/table';
import { SearchInput } from '../Shared/CustomInput';
import ExportExcelButton from '../Shared/ExportExcelButton';
import FilterTableDataByDateRange from '../Shared/FilterTableDataByDateRange';

// Others
import { checkAndReplaceStringWithHyphen, checkAndReplaceNumberWithZero } from '../../utils/shared';
import { useOasysContext, JOB_STATUS_STYLE_MAP } from '../../utils/oasys';
import { useSimiContext } from '../../utils/simi';

const AllJobsTable = () => {
  // Standard and Vars
  const { page, rowsPerPage } = useSelector((state) => state.simi);
  const { push } = useRouter();
  const { getCertainStaffInfoFromStaffId } = useSimiContext();

  const { filteredJobsTableData } = useOasysContext();

  // Table
  const bodyCellStyle = 'text-center py-1 text-sm';

  return (
    <>
      <div className="flex w-full justify-between">
        <div className="flex gap-2">
          <FilterTableDataByDateRange />
          <SearchInput />
        </div>

        <ExportExcelButton data={filteredJobsTableData} />
      </div>
      <div className="flex w-full flex-col">
        <div className="overflow-x-auto scrollbar">
          <table className="min-w-full bg-white text-black">
            <thead>
              <tr>
                {[
                  'No.',
                  'Job ID',
                  'Status',
                  'On Hold End Date',
                  'On Hold Reason',
                  'No. of Tasks',
                  'Created By',
                  'Created At',
                  '',
                ].map((label, i) => (
                  <td key={i} className="bg-oasys whitespace-nowrap px-4 text-center text-white">
                    {label}
                  </td>
                ))}
              </tr>
            </thead>
            <tbody>
              {filteredJobsTableData
                ?.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                ?.map((row, i) => (
                  <tr key={i} className="odd:bg-white even:bg-[#f8f8f8] ">
                    <td className={bodyCellStyle}>{i + 1}</td>
                    <td className={bodyCellStyle}>
                      {checkAndReplaceStringWithHyphen(row?.job_id)}
                    </td>
                    <td className={twMerge(bodyCellStyle, JOB_STATUS_STYLE_MAP?.[row?.status])}>
                      {checkAndReplaceStringWithHyphen(row?.status?.toUpperCase())}
                    </td>
                    <td className={bodyCellStyle}>
                      {row?.on_hold_end_date !== 0
                        ? checkAndReplaceStringWithHyphen(
                            moment?.unix(row?.on_hold_end_date)?.format('YYYY-MM-DD')
                          )
                        : '-'}
                    </td>
                    <td className={bodyCellStyle}>
                      {checkAndReplaceStringWithHyphen(
                        row?.on_hold_end_date === 0 ? '-' : row?.status_remarks
                      )}
                    </td>
                    <td className={bodyCellStyle}>
                      {checkAndReplaceNumberWithZero(row?.counters)}
                    </td>
                    <td className={bodyCellStyle}>
                      {checkAndReplaceStringWithHyphen(
                        getCertainStaffInfoFromStaffId(row?.created_by_staff_id, 'name')
                      )}
                    </td>
                    <td className={bodyCellStyle}>
                      {checkAndReplaceStringWithHyphen(
                        moment?.unix(row?.created_at)?.format('YYYY-MM-DD HH:mm')
                      )?.toUpperCase()}
                    </td>
                    <td className={bodyCellStyle}>
                      <button type="button" onClick={() => push(`/oasys/${row?.job_id}`)}>
                        <Visibility className="text-oasys" />
                      </button>
                    </td>
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
        <TablePaginationCustom count={filteredJobsTableData.length} />
      </div>
    </>
  );
};

export default AllJobsTable;
