import { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useRouter } from 'next/router';

// Packages
import * as R from 'ramda';

// Components
import ReactApexcharts from '../Shared/ReactApexcharts';
import { SearchInput } from '../Shared/CustomInput';
import AllTasks from './AllTasks';

// Others
import { OASYS_ENDPOINT } from '../../utils/oasys';
import { checkAndReplaceStringWithHyphen, checkAndReplaceNumberWithZero } from '../../utils/shared';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { useSimiContext } from '../../utils/simi';

const AllProcessorsTable = () => {
  // Standard and Vars
  const { allStaffs } = useSelector((state) => state.aum);
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { setParam } = useParamContext();
  const { q, processor_staff_id } = query;
  const { getCertainStaffInfoFromStaffId } = useSimiContext();
  // Table
  const bodyCellStyle = 'text-center py-1 text-sm';
  const [tableData, setTableData] = useState([]);

  // Others

  const pieCategories = ['in progress', 'blocked'];

  const uniqueProcessors = R.uniq(R.pluck('processor_staff_id', tableData));

  const transformedProcessorsData = uniqueProcessors?.map((o) => ({
    name: allStaffs?.find((p) => p?.staff_id === o)?.name,
    staff_id: o,
    in_progress: tableData?.filter(
      (p) => p?.processor_staff_id === o && p?.status === 'in progress'
    )?.length,
    blocked: tableData?.filter((p) => p?.processor_staff_id === o && p?.status === 'blocked')
      ?.length,
    total: tableData?.filter((p) => p?.processor_staff_id === o)?.length,
  }));

  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return transformedProcessorsData;
    }
    return transformedProcessorsData.filter((o) =>
      JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase())
    );
  })();

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${OASYS_ENDPOINT}/tasks/all/keyword`);
      let temp = response?.data?.data || [];
      temp = temp?.filter((o) => o?.processor_staff_id !== '');
      setTableData(temp);
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="space-y-8">
      <div className="flex flex-col gap-2 rounded-md bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
        <div className="mx-auto w-64">
          <ReactApexcharts
            options={{
              chart: {
                type: 'pie',
              },
              labels: pieCategories?.map((o) => o?.toUpperCase()),
              legend: {
                position: 'right',
                verticalAlign: 'middle',
              },
              dataLabels: {
                enabled: true,
                formatter: (_, { seriesIndex, w }) => w.config.series[seriesIndex],
              },
            }}
            series={pieCategories?.map((o) => tableData?.filter((p) => p?.status === o)?.length)}
            type="pie"
          />
        </div>
        <SearchInput />
        <div className="overflow-x-auto scrollbar">
          <table className="min-w-full bg-white text-black">
            <thead>
              <tr>
                {['No.', 'Processor', 'Staff ID', 'In Progress', 'Blocked', 'Total'].map(
                  (label, i) => (
                    <td key={i} className="bg-oasys whitespace-nowrap px-4 text-center text-white">
                      {label}
                    </td>
                  )
                )}
              </tr>
            </thead>
            <tbody>
              {filteredTableData.map((row, i) => (
                <tr
                  key={i}
                  className="cursor-pointer odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-100"
                  onClick={() =>
                    setParam({
                      processor_staff_id:
                        processor_staff_id === row?.staff_id ? undefined : row?.staff_id,
                    })
                  }
                >
                  <td className={bodyCellStyle}>{i + 1}</td>
                  <td className={bodyCellStyle}>{checkAndReplaceStringWithHyphen(row?.name)}</td>
                  <td className={bodyCellStyle}>
                    {checkAndReplaceStringWithHyphen(row?.staff_id?.toUpperCase())}
                  </td>
                  <td className={bodyCellStyle}>
                    {checkAndReplaceNumberWithZero(row?.in_progress)}
                  </td>
                  <td className={bodyCellStyle}>{checkAndReplaceNumberWithZero(row?.blocked)}</td>
                  <td className={bodyCellStyle}>{checkAndReplaceNumberWithZero(row?.total)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <p className="text-center text-xs text-gray-500">
          Click on any processor to view assigned tasks list
        </p>
      </div>
      <p className="text-oasys text-center text-sm font-medium">
        {processor_staff_id &&
          `Selected Processor: ${getCertainStaffInfoFromStaffId(processor_staff_id, 'name')}`}
      </p>
      {!['', undefined, null]?.includes(processor_staff_id) && (
        <AllTasks key={processor_staff_id} endpoint={`processor_staff_id/${processor_staff_id}`} />
      )}
    </div>
  );
};

export default AllProcessorsTable;
