// React
import { useState } from 'react';
import { useDispatch } from 'react-redux';

// Material UI
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Chip,
  Autocomplete,
  Typography,
  Box
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

// Components
import { EnhancedButton } from '../fa/ui/EnhancedButton';

// Utils
import { createProject } from '../../utils/store/projectTrackerReducer';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { useSnackbar } from '../Shared/snackbar';
import * as yup from 'yup';

const validationSchema = yup.object({
  name: yup.string().required('Project name is required').min(3, 'Name must be at least 3 characters'),
  description: yup.string().required('Description is required').min(10, 'Description must be at least 10 characters'),
  priority: yup.string().required('Priority is required'),
  status: yup.string().required('Status is required'),
  start_date: yup.date().required('Start date is required'),
  end_date: yup.date()
    .required('End date is required')
    .min(yup.ref('start_date'), 'End date must be after start date'),
  owner: yup.string().required('Project owner is required'),
});

const ProjectCreateDialog = ({ open, onClose, onSuccess }) => {
  const dispatch = useDispatch();
  
  // Safe auth context usage with error handling
  let user = null;
  try {
    const authContext = useAuthContext();
    user = authContext?.user;
  } catch (error) {
    console.warn('Auth context not available:', error);
  }
  
  const { enqueueSnackbar } = useSnackbar();
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    priority: 'medium',
    status: 'planning',
    start_date: null,
    end_date: null,
    owner: user?.name || user?.full_name || '',
    budget: '',
    department: '',
    tags: [],
    goals: ['']
  });
  
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentTag, setCurrentTag] = useState('');

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const handleAddTag = (event) => {
    if (event.key === 'Enter' && currentTag.trim()) {
      event.preventDefault();
      if (!formData.tags.includes(currentTag.trim())) {
        setFormData(prev => ({
          ...prev,
          tags: [...prev.tags, currentTag.trim()]
        }));
      }
      setCurrentTag('');
    }
  };

  const handleRemoveTag = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleAddGoal = () => {
    setFormData(prev => ({
      ...prev,
      goals: [...prev.goals, '']
    }));
  };

  const handleGoalChange = (index, value) => {
    setFormData(prev => ({
      ...prev,
      goals: prev.goals.map((goal, i) => i === index ? value : goal)
    }));
  };

  const handleRemoveGoal = (index) => {
    if (formData.goals.length > 1) {
      setFormData(prev => ({
        ...prev,
        goals: prev.goals.filter((_, i) => i !== index)
      }));
    }
  };

  const validateForm = async () => {
    try {
      await validationSchema.validate(formData, { abortEarly: false });
      setErrors({});
      return true;
    } catch (error) {
      const newErrors = {};
      error.inner.forEach((err) => {
        newErrors[err.path] = err.message;
      });
      setErrors(newErrors);
      return false;
    }
  };

  const handleSubmit = async () => {
    if (!(await validateForm())) {
      enqueueSnackbar('Please fix the form errors', { variant: 'error' });
      return;
    }

    setIsSubmitting(true);
    try {
      const projectData = {
        ...formData,
        goals: formData.goals.filter(goal => goal.trim()),
        created_by: user?.name || user?.full_name || 'Unknown',
        created_by_staff_id: user?.staff_id || user?.id || 0,
        progress_percentage: 0,
        risk_score: 2, // Default low risk
      };

      await dispatch(createProject(projectData)).unwrap();
      
      enqueueSnackbar('Project created successfully!', { variant: 'success' });
      onSuccess?.();
      handleReset();
    } catch (error) {
      enqueueSnackbar(error || 'Failed to create project', { variant: 'error' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReset = () => {
    setFormData({
      name: '',
      description: '',
      priority: 'medium',
      status: 'planning',
      start_date: null,
      end_date: null,
      owner: user?.name || user?.full_name || '',
      budget: '',
      department: '',
      tags: [],
      goals: ['']
    });
    setErrors({});
    setCurrentTag('');
  };

  const handleClose = () => {
    handleReset();
    onClose();
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Dialog 
        open={open} 
        onClose={handleClose} 
        maxWidth="md" 
        fullWidth
        PaperProps={{
          className: "bg-white dark:bg-gray-800"
        }}
      >
        <DialogTitle className="text-xl font-semibold text-gray-900 dark:text-white">
          Create New Project
        </DialogTitle>
        
        <DialogContent className="space-y-4">
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" className="text-gray-900 dark:text-white mb-3">
                Basic Information
              </Typography>
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Project Name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                error={!!errors.name}
                helperText={errors.name}
                placeholder="Enter project name..."
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                error={!!errors.description}
                helperText={errors.description}
                placeholder="Describe the project objectives and scope..."
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!errors.priority}>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={formData.priority}
                  onChange={(e) => handleInputChange('priority', e.target.value)}
                  label="Priority"
                >
                  <MenuItem value="low">Low</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="high">High</MenuItem>
                  <MenuItem value="critical">Critical</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!errors.status}>
                <InputLabel>Status</InputLabel>
                <Select
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  label="Status"
                >
                  <MenuItem value="planning">Planning</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="on_hold">On Hold</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Timeline */}
            <Grid item xs={12}>
              <Typography variant="h6" className="text-gray-900 dark:text-white mb-3">
                Timeline
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <DatePicker
                label="Start Date"
                value={formData.start_date}
                onChange={(date) => handleInputChange('start_date', date)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    error={!!errors.start_date}
                    helperText={errors.start_date}
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <DatePicker
                label="End Date"
                value={formData.end_date}
                onChange={(date) => handleInputChange('end_date', date)}
                minDate={formData.start_date}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    error={!!errors.end_date}
                    helperText={errors.end_date}
                  />
                )}
              />
            </Grid>

            {/* Project Details */}
            <Grid item xs={12}>
              <Typography variant="h6" className="text-gray-900 dark:text-white mb-3">
                Project Details
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Project Owner"
                value={formData.owner}
                onChange={(e) => handleInputChange('owner', e.target.value)}
                error={!!errors.owner}
                helperText={errors.owner}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Department"
                value={formData.department}
                onChange={(e) => handleInputChange('department', e.target.value)}
                placeholder="e.g., IT, Marketing, Operations"
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Budget (Optional)"
                value={formData.budget}
                onChange={(e) => handleInputChange('budget', e.target.value)}
                placeholder="e.g., RM 50,000"
              />
            </Grid>

            {/* Tags */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Tags"
                value={currentTag}
                onChange={(e) => setCurrentTag(e.target.value)}
                onKeyPress={handleAddTag}
                placeholder="Type a tag and press Enter..."
                helperText="Press Enter to add tags"
              />
              <Box className="mt-2 flex flex-wrap gap-2">
                {formData.tags.map((tag, index) => (
                  <Chip
                    key={index}
                    label={tag}
                    onDelete={() => handleRemoveTag(tag)}
                    size="small"
                    variant="outlined"
                  />
                ))}
              </Box>
            </Grid>

            {/* Goals */}
            <Grid item xs={12}>
              <div className="flex items-center justify-between mb-2">
                <Typography variant="h6" className="text-gray-900 dark:text-white">
                  Project Goals
                </Typography>
                <EnhancedButton
                  variant="outlined"
                  size="small"
                  onClick={handleAddGoal}
                >
                  Add Goal
                </EnhancedButton>
              </div>
              <div className="space-y-2">
                {formData.goals.map((goal, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <TextField
                      fullWidth
                      placeholder={`Goal ${index + 1}...`}
                      value={goal}
                      onChange={(e) => handleGoalChange(index, e.target.value)}
                      size="small"
                    />
                    {formData.goals.length > 1 && (
                      <EnhancedButton
                        variant="text"
                        size="small"
                        onClick={() => handleRemoveGoal(index)}
                        className="text-red-600 hover:bg-red-50"
                      >
                        Remove
                      </EnhancedButton>
                    )}
                  </div>
                ))}
              </div>
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions className="px-6 pb-6">
          <EnhancedButton
            variant="outlined"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Cancel
          </EnhancedButton>
          <EnhancedButton
            variant="contained"
            onClick={handleSubmit}
            disabled={isSubmitting}
            loading={isSubmitting}
          >
            Create Project
          </EnhancedButton>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};

export default ProjectCreateDialog;