// React
import { useState } from 'react';

// Material UI
import {
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Divider,
  Box
} from '@mui/material';
import {
  Send,
  Timeline,
  CheckCircle,
  Warning,
  Edit,
  Delete,
  AttachFile,
  Comment
} from '@mui/icons-material';

// Utils
import { formatDistanceToNow } from 'date-fns';
import { useProjectTrackerContext } from '../../utils/project-tracker';
import { EnhancedButton } from '../fa/ui/EnhancedButton';

const ActivityFeed = ({ activities = [], project }) => {
  const [newComment, setNewComment] = useState('');
  const { handleCreateActivity } = useProjectTrackerContext();

  const handlePostComment = async () => {
    if (!newComment.trim()) return;
    
    try {
      await handleCreateActivity(project.id, newComment, 'comment');
      setNewComment('');
    } catch (error) {
      console.error('Failed to post comment:', error);
    }
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'milestone':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'risk':
        return <Warning className="w-5 h-5 text-yellow-600" />;
      case 'comment':
        return <Comment className="w-5 h-5 text-blue-600" />;
      case 'update':
        return <Edit className="w-5 h-5 text-gray-600" />;
      default:
        return <Timeline className="w-5 h-5 text-gray-600" />;
    }
  };

  const getActivityColor = (type) => {
    switch (type) {
      case 'milestone':
        return 'text-green-600 bg-green-100';
      case 'risk':
        return 'text-yellow-600 bg-yellow-100';
      case 'comment':
        return 'text-blue-600 bg-blue-100';
      case 'update':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="space-y-6">
      {/* Project Overview */}
      <Card>
        <CardContent>
          <Typography variant="h6" className="text-gray-900 dark:text-white mb-4">
            Project Overview
          </Typography>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Typography variant="subtitle2" className="text-gray-600 dark:text-gray-400 mb-2">
                Description
              </Typography>
              <Typography variant="body2" className="text-gray-900 dark:text-white mb-4">
                {project.description || 'No description provided'}
              </Typography>
              
              {project.goals && project.goals.length > 0 && (
                <>
                  <Typography variant="subtitle2" className="text-gray-600 dark:text-gray-400 mb-2">
                    Project Goals
                  </Typography>
                  <ul className="list-disc list-inside space-y-1">
                    {project.goals.map((goal, index) => (
                      <li key={index} className="text-sm text-gray-700 dark:text-gray-300">
                        {goal}
                      </li>
                    ))}
                  </ul>
                </>
              )}
            </div>
            
            <div>
              <Typography variant="subtitle2" className="text-gray-600 dark:text-gray-400 mb-2">
                Project Details
              </Typography>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Department:</span>
                  <span className="text-gray-900 dark:text-white">{project.department || 'Not specified'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Budget:</span>
                  <span className="text-gray-900 dark:text-white">{project.budget || 'Not specified'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Created:</span>
                  <span className="text-gray-900 dark:text-white">
                    {project.created_at ? new Date(project.created_at).toLocaleDateString() : 'Unknown'}
                  </span>
                </div>
              </div>
              
              {project.tags && project.tags.length > 0 && (
                <div className="mt-4">
                  <Typography variant="subtitle2" className="text-gray-600 dark:text-gray-400 mb-2">
                    Tags
                  </Typography>
                  <div className="flex flex-wrap gap-1">
                    {project.tags.map((tag, index) => (
                      <Chip key={index} label={tag} size="small" variant="outlined" />
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add Comment */}
      <Card>
        <CardContent>
          <Typography variant="h6" className="text-gray-900 dark:text-white mb-4">
            Add Comment
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={3}
            placeholder="Share an update, ask a question, or add a comment..."
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <EnhancedButton
                    variant="contained"
                    size="small"
                    onClick={handlePostComment}
                    disabled={!newComment.trim()}
                    startIcon={<Send />}
                  >
                    Post
                  </EnhancedButton>
                </InputAdornment>
              ),
            }}
          />
        </CardContent>
      </Card>

      {/* Activity Timeline */}
      <Card>
        <CardContent>
          <Typography variant="h6" className="text-gray-900 dark:text-white mb-4">
            Recent Activity
          </Typography>
          
          {activities.length === 0 ? (
            <div className="text-center py-8">
              <Timeline className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <Typography variant="body1" className="text-gray-500 mb-2">
                No activity yet
              </Typography>
              <Typography variant="body2" className="text-gray-400">
                Project activities will appear here as they happen
              </Typography>
            </div>
          ) : (
            <div className="space-y-4">
              {activities.map((activity, index) => (
                <div key={activity.id || index}>
                  <div className="flex items-start space-x-3">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getActivityColor(activity.type)}`}>
                      {getActivityIcon(activity.type)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <Typography variant="subtitle2" className="text-gray-900 dark:text-white">
                          {activity.action_by || 'System'}
                        </Typography>
                        <Chip
                          label={activity.type}
                          size="small"
                          className={`${getActivityColor(activity.type)} text-xs`}
                        />
                        <Typography variant="caption" className="text-gray-500">
                          {activity.timestamp ? 
                            formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true }) : 
                            'Just now'
                          }
                        </Typography>
                      </div>
                      
                      <Typography variant="body2" className="text-gray-700 dark:text-gray-300">
                        {activity.description}
                      </Typography>
                      
                      {activity.attachments && activity.attachments.length > 0 && (
                        <div className="mt-2 flex items-center space-x-2">
                          <AttachFile className="w-4 h-4 text-gray-400" />
                          <div className="flex flex-wrap gap-1">
                            {activity.attachments.map((file, idx) => (
                              <Chip
                                key={idx}
                                label={file.name}
                                size="small"
                                variant="outlined"
                                className="text-xs"
                                icon={<AttachFile />}
                              />
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {index < activities.length - 1 && (
                    <Divider className="my-4 ml-8" />
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ActivityFeed;