// React
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Material UI
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  Avatar,
  Chip
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  Assignment,
  CheckCircle,
  Warning,
  Schedule,
  People,
  Timeline,
  AttachMoney,
  Speed
} from '@mui/icons-material';

// Charts
import dynamic from 'next/dynamic';
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false });

// Utils
import { fetchDashboardStats } from '../../utils/store/projectTrackerReducer';
import { useProjectTrackerContext } from '../../utils/project-tracker';

const StatsCard = ({ title, value, subtitle, icon, trend, trendValue, color = 'primary' }) => {
  const colorClasses = {
    primary: 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400',
    success: 'bg-green-50 text-green-600 dark:bg-green-900/20 dark:text-green-400',
    warning: 'bg-yellow-50 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400',
    error: 'bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-400'
  };

  return (
    <Card className="h-full">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${colorClasses[color]}`}>
            {icon}
          </div>
          {trend && (
            <div className={`flex items-center ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
              {trend === 'up' ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
              <span className="text-sm font-medium ml-1">{trendValue}</span>
            </div>
          )}
        </div>
        
        <Typography variant="h4" className="font-bold text-gray-900 dark:text-white mb-1">
          {value}
        </Typography>
        
        <Typography variant="body2" className="text-gray-600 dark:text-gray-400 mb-1">
          {title}
        </Typography>
        
        {subtitle && (
          <Typography variant="caption" className="text-gray-500 dark:text-gray-500">
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );
};

const DashboardStats = () => {
  const dispatch = useDispatch();
  const { 
    dashboardStats, 
    loading: { dashboardStats: isLoading } 
  } = useSelector((state) => state.projectTracker);

  const { getHealthColor } = useProjectTrackerContext();

  useEffect(() => {
    dispatch(fetchDashboardStats());
  }, [dispatch]);

  // Sample data - replace with actual data from dashboardStats
  const stats = {
    totalProjects: dashboardStats?.totalProjects || 24,
    activeProjects: dashboardStats?.activeProjects || 18,
    completedProjects: dashboardStats?.completedProjects || 45,
    overdueProjects: dashboardStats?.overdueProjects || 3,
    avgProgress: dashboardStats?.avgProgress || 67,
    totalBudget: dashboardStats?.totalBudget || 2500000,
    teamMembers: dashboardStats?.teamMembers || 32,
    avgVelocity: dashboardStats?.avgVelocity || 4.2,
    healthDistribution: dashboardStats?.healthDistribution || {
      excellent: 8,
      good: 12,
      warning: 3,
      critical: 1
    },
    priorityDistribution: dashboardStats?.priorityDistribution || {
      critical: 2,
      high: 8,
      medium: 12,
      low: 2
    },
    monthlyProgress: dashboardStats?.monthlyProgress || [
      { month: 'Jan', progress: 45 },
      { month: 'Feb', progress: 52 },
      { month: 'Mar', progress: 61 },
      { month: 'Apr', progress: 67 },
      { month: 'May', progress: 72 },
      { month: 'Jun', progress: 78 }
    ]
  };

  // Chart configurations for ApexCharts
  const healthChartOptions = {
    chart: {
      type: 'donut'
    },
    labels: ['Excellent', 'Good', 'Warning', 'Critical'],
    colors: ['#10B981', '#3B82F6', '#F59E0B', '#EF4444'],
    legend: {
      position: 'bottom'
    },
    responsive: [{
      breakpoint: 480,
      options: {
        chart: {
          width: 200
        }
      }
    }]
  };

  const healthChartSeries = [
    stats.healthDistribution.excellent,
    stats.healthDistribution.good,
    stats.healthDistribution.warning,
    stats.healthDistribution.critical
  ];

  const priorityChartOptions = {
    chart: {
      type: 'bar'
    },
    xaxis: {
      categories: ['Critical', 'High', 'Medium', 'Low']
    },
    colors: ['#EF4444', '#F59E0B', '#3B82F6', '#10B981'],
    legend: {
      show: false
    }
  };

  const priorityChartSeries = [{
    name: 'Projects',
    data: [
      stats.priorityDistribution.critical,
      stats.priorityDistribution.high,
      stats.priorityDistribution.medium,
      stats.priorityDistribution.low
    ]
  }];

  const progressChartOptions = {
    chart: {
      type: 'area'
    },
    xaxis: {
      categories: stats.monthlyProgress.map(item => item.month)
    },
    yaxis: {
      title: {
        text: 'Progress %'
      }
    },
    colors: ['#3B82F6'],
    fill: {
      type: 'gradient',
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 0.7,
        opacityTo: 0.3
      }
    }
  };

  const progressChartSeries = [{
    name: 'Average Progress',
    data: stats.monthlyProgress.map(item => item.progress)
  }];

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded-xl mb-4"></div>
                <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} lg={3}>
          <StatsCard
            title="Total Projects"
            value={stats.totalProjects}
            subtitle="All projects in system"
            icon={<Assignment className="w-6 h-6" />}
            color="primary"
          />
        </Grid>
        
        <Grid item xs={12} sm={6} lg={3}>
          <StatsCard
            title="Active Projects"
            value={stats.activeProjects}
            subtitle="Currently in progress"
            icon={<Timeline className="w-6 h-6" />}
            trend="up"
            trendValue="+12%"
            color="success"
          />
        </Grid>

        <Grid item xs={12} sm={6} lg={3}>
          <StatsCard
            title="Completed Projects"
            value={stats.completedProjects}
            subtitle="Successfully finished"
            icon={<CheckCircle className="w-6 h-6" />}
            trend="up"
            trendValue="+8%"
            color="success"
          />
        </Grid>

        <Grid item xs={12} sm={6} lg={3}>
          <StatsCard
            title="Overdue Projects"
            value={stats.overdueProjects}
            subtitle="Require attention"
            icon={<Warning className="w-6 h-6" />}
            trend={stats.overdueProjects > 0 ? "down" : "up"}
            trendValue="-5%"
            color={stats.overdueProjects > 0 ? "error" : "success"}
          />
        </Grid>
      </Grid>

      {/* Secondary Metrics */}
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} lg={3}>
          <StatsCard
            title="Average Progress"
            value={`${stats.avgProgress}%`}
            subtitle="Across all projects"
            icon={<Speed className="w-6 h-6" />}
            color="primary"
          />
        </Grid>

        <Grid item xs={12} sm={6} lg={3}>
          <StatsCard
            title="Total Budget"
            value={`RM ${(stats.totalBudget / 1000000).toFixed(1)}M`}
            subtitle="Combined project budgets"
            icon={<AttachMoney className="w-6 h-6" />}
            color="success"
          />
        </Grid>

        <Grid item xs={12} sm={6} lg={3}>
          <StatsCard
            title="Team Members"
            value={stats.teamMembers}
            subtitle="Active across projects"
            icon={<People className="w-6 h-6" />}
            color="primary"
          />
        </Grid>

        <Grid item xs={12} sm={6} lg={3}>
          <StatsCard
            title="Avg Velocity"
            value={stats.avgVelocity.toFixed(1)}
            subtitle="Activities per day"
            icon={<TrendingUp className="w-6 h-6" />}
            trend="up"
            trendValue="+15%"
            color="success"
          />
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3}>
        {/* Project Health Distribution */}
        <Grid item xs={12} md={6} lg={4}>
          <Card>
            <CardContent className="p-6">
              <Typography variant="h6" className="text-gray-900 dark:text-white mb-4">
                Project Health
              </Typography>
              <Box className="h-64">
                <Chart
                  options={healthChartOptions}
                  series={healthChartSeries}
                  type="donut"
                  height="100%"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Priority Distribution */}
        <Grid item xs={12} md={6} lg={4}>
          <Card>
            <CardContent className="p-6">
              <Typography variant="h6" className="text-gray-900 dark:text-white mb-4">
                Priority Distribution
              </Typography>
              <Box className="h-64">
                <Chart
                  options={priorityChartOptions}
                  series={priorityChartSeries}
                  type="bar"
                  height="100%"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Progress Trend */}
        <Grid item xs={12} md={12} lg={4}>
          <Card>
            <CardContent className="p-6">
              <Typography variant="h6" className="text-gray-900 dark:text-white mb-4">
                Progress Trend
              </Typography>
              <Box className="h-64">
                <Chart
                  options={progressChartOptions}
                  series={progressChartSeries}
                  type="area"
                  height="100%"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Activity Summary */}
      <Card>
        <CardContent className="p-6">
          <Typography variant="h6" className="text-gray-900 dark:text-white mb-4">
            Key Insights
          </Typography>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <Typography variant="subtitle2" className="text-blue-900 dark:text-blue-100 mb-2">
                Most Active Department
              </Typography>
              <Typography variant="body2" className="text-blue-700 dark:text-blue-300">
                {dashboardStats?.mostActiveDepartment || 'IT Department'}
              </Typography>
            </div>
            
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
              <Typography variant="subtitle2" className="text-green-900 dark:text-green-100 mb-2">
                On-Time Delivery Rate
              </Typography>
              <Typography variant="body2" className="text-green-700 dark:text-green-300">
                {dashboardStats?.onTimeRate || '85%'}
              </Typography>
            </div>

            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
              <Typography variant="subtitle2" className="text-purple-900 dark:text-purple-100 mb-2">
                Average Project Duration
              </Typography>
              <Typography variant="body2" className="text-purple-700 dark:text-purple-300">
                {dashboardStats?.avgDuration || '4.2 months'}
              </Typography>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DashboardStats;