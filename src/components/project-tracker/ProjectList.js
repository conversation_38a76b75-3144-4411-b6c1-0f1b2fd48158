// Next, React, Tailwind
import { useState, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Material UI
import {
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  InputAdornment,
  IconButton,
  Tooltip,
  Box,
  Typography
} from '@mui/material';
import { Search, Clear, FilterList, ViewModule, ViewList } from '@mui/icons-material';

// Components
import ProjectCard from './ProjectCard';
import ProjectCreateDialog from './ProjectCreateDialog';
import { EnhancedButton } from '../fa/ui/EnhancedButton';
import { EmptyState } from '../ui/empty-state';

// Others
import { fetchAllProjects, setFilters } from '../../utils/store/projectTrackerReducer';
import { useProjectTrackerContext } from '../../utils/project-tracker';

const ProjectList = () => {
  const dispatch = useDispatch();
  const { 
    projects, 
    loading: { projects: isLoading }, 
    filters 
  } = useSelector((state) => state.projectTracker);
  
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [showFilters, setShowFilters] = useState(false);
  
  const { calculateProjectHealth } = useProjectTrackerContext();

  // Enhanced filtering logic
  const filteredProjects = useMemo(() => {
    return projects.filter(project => {
      // Text search
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        if (!project.name?.toLowerCase().includes(searchTerm) &&
            !project.description?.toLowerCase().includes(searchTerm) &&
            !project.owner?.toLowerCase().includes(searchTerm)) {
          return false;
        }
      }

      // Status filter
      if (filters.status && project.status !== filters.status) {
        return false;
      }

      // Priority filter
      if (filters.priority && project.priority !== filters.priority) {
        return false;
      }

      // Health filter
      if (filters.health) {
        const projectHealth = calculateProjectHealth(project);
        if (projectHealth !== filters.health) {
          return false;
        }
      }

      // Owner filter
      if (filters.owner && project.owner !== filters.owner) {
        return false;
      }

      return true;
    });
  }, [projects, filters, calculateProjectHealth]);

  const handleFilterChange = (filterKey, value) => {
    dispatch(setFilters({ [filterKey]: value }));
  };

  const clearFilters = () => {
    dispatch(setFilters({
      status: '',
      priority: '',
      health: '',
      owner: '',
      search: ''
    }));
  };

  const activeFiltersCount = Object.values(filters).filter(Boolean).length;

  useEffect(() => {
    dispatch(fetchAllProjects(filters));
  }, [dispatch, filters]);

  // Get unique owners for filter dropdown
  const uniqueOwners = [...new Set(projects.map(p => p.owner).filter(Boolean))];

  return (
    <div className="space-y-6">
      {/* Search and Filter Controls */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <TextField
              fullWidth
              placeholder="Search projects by name, description, or owner..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search className="text-gray-400" />
                  </InputAdornment>
                ),
                endAdornment: filters.search && (
                  <InputAdornment position="end">
                    <IconButton 
                      size="small" 
                      onClick={() => handleFilterChange('search', '')}
                    >
                      <Clear />
                    </IconButton>
                  </InputAdornment>
                )
              }}
              size="small"
            />
          </div>

          {/* Filter Toggle */}
          <div className="flex items-center gap-2">
            <Tooltip title="Toggle Filters">
              <IconButton
                onClick={() => setShowFilters(!showFilters)}
                color={showFilters ? 'primary' : 'default'}
              >
                <FilterList />
                {activeFiltersCount > 0 && (
                  <Chip
                    label={activeFiltersCount}
                    size="small"
                    color="primary"
                    className="absolute -top-1 -right-1 w-5 h-5 text-xs"
                  />
                )}
              </IconButton>
            </Tooltip>

            {/* View Mode Toggle */}
            <div className="flex border rounded-md overflow-hidden">
              <IconButton
                size="small"
                onClick={() => setViewMode('grid')}
                color={viewMode === 'grid' ? 'primary' : 'default'}
                className="rounded-none"
              >
                <ViewModule />
              </IconButton>
              <IconButton
                size="small"
                onClick={() => setViewMode('list')}
                color={viewMode === 'list' ? 'primary' : 'default'}
                className="rounded-none"
              >
                <ViewList />
              </IconButton>
            </div>

            <EnhancedButton
              variant="contained"
              onClick={() => setCreateDialogOpen(true)}
            >
              New Project
            </EnhancedButton>
          </div>
        </div>

        {/* Expanded Filters */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <FormControl size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  label="Status"
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="planning">Planning</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="on_hold">On Hold</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                  <MenuItem value="cancelled">Cancelled</MenuItem>
                </Select>
              </FormControl>

              <FormControl size="small">
                <InputLabel>Priority</InputLabel>
                <Select
                  value={filters.priority}
                  onChange={(e) => handleFilterChange('priority', e.target.value)}
                  label="Priority"
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="critical">Critical</MenuItem>
                  <MenuItem value="high">High</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="low">Low</MenuItem>
                </Select>
              </FormControl>

              <FormControl size="small">
                <InputLabel>Health</InputLabel>
                <Select
                  value={filters.health}
                  onChange={(e) => handleFilterChange('health', e.target.value)}
                  label="Health"
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="excellent">Excellent</MenuItem>
                  <MenuItem value="good">Good</MenuItem>
                  <MenuItem value="warning">Warning</MenuItem>
                  <MenuItem value="critical">Critical</MenuItem>
                </Select>
              </FormControl>

              <FormControl size="small">
                <InputLabel>Owner</InputLabel>
                <Select
                  value={filters.owner}
                  onChange={(e) => handleFilterChange('owner', e.target.value)}
                  label="Owner"
                >
                  <MenuItem value="">All</MenuItem>
                  {uniqueOwners.map(owner => (
                    <MenuItem key={owner} value={owner}>{owner}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </div>

            {activeFiltersCount > 0 && (
              <div className="mt-3 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Active filters:</span>
                  {Object.entries(filters).map(([key, value]) => 
                    value && (
                      <Chip
                        key={key}
                        label={`${key}: ${value}`}
                        size="small"
                        onDelete={() => handleFilterChange(key, '')}
                        variant="outlined"
                      />
                    )
                  )}
                </div>
                <EnhancedButton
                  variant="outlined"
                  size="small"
                  onClick={clearFilters}
                >
                  Clear All
                </EnhancedButton>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <Typography variant="h6" className="text-gray-900 dark:text-white">
          Projects
          <span className="ml-2 text-sm text-gray-500 font-normal">
            ({filteredProjects.length} {filteredProjects.length === 1 ? 'project' : 'projects'})
          </span>
        </Typography>
      </div>

      {/* Project Grid/List */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white dark:bg-gray-800 rounded-lg p-6 animate-pulse">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-3"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
            </div>
          ))}
        </div>
      ) : filteredProjects.length === 0 ? (
        <EmptyState
          title="No projects found"
          description={
            activeFiltersCount > 0 
              ? "Try adjusting your filters or search terms" 
              : "Get started by creating your first project"
          }
          action={
            <EnhancedButton
              variant="contained"
              onClick={() => setCreateDialogOpen(true)}
            >
              Create Project
            </EnhancedButton>
          }
        />
      ) : (
        <Grid 
          container 
          spacing={3}
          className={viewMode === 'list' ? 'grid-cols-1' : ''}
        >
          {filteredProjects.map((project) => (
            <Grid 
              item 
              xs={12} 
              sm={viewMode === 'grid' ? 6 : 12} 
              lg={viewMode === 'grid' ? 4 : 12} 
              key={project.id}
            >
              <ProjectCard 
                project={project} 
                onEdit={(project) => {
                  // Handle edit
                  console.log('Edit project:', project);
                }}
                onDelete={(project) => {
                  // Handle delete
                  console.log('Delete project:', project);
                }}
              />
            </Grid>
          ))}
        </Grid>
      )}

      {/* Create Project Dialog */}
      <ProjectCreateDialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        onSuccess={() => {
          setCreateDialogOpen(false);
          dispatch(fetchAllProjects(filters));
        }}
      />
    </div>
  );
};

export default ProjectList;