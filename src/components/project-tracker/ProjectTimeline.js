// React
import { useMemo } from 'react';

// Material UI
import {
  <PERSON>,
  CardContent,
  Typography,
  Box,
  Chip,
  LinearProgress,
  Tooltip,
  Avatar
} from '@mui/material';
import {
  PlayArrow,
  CheckCircle,
  Schedule,
  Flag,
  Warning
} from '@mui/icons-material';

// Utils
import { format, differenceInDays } from 'date-fns';

const ProjectTimeline = ({ project, milestones = [] }) => {
  // Calculate project timeline
  const timelineData = useMemo(() => {
    const items = [];
    
    // Project start
    if (project.start_date) {
      items.push({
        id: 'start',
        type: 'project',
        title: 'Project Started',
        date: project.start_date,
        status: 'completed',
        description: `${project.name} officially began`
      });
    }

    // Add milestones
    milestones.forEach((milestone) => {
      items.push({
        id: milestone.id,
        type: 'milestone',
        title: milestone.title,
        date: milestone.due_date,
        status: milestone.status,
        description: milestone.description,
        progress: milestone.progress_percentage
      });
    });

    // Project end
    if (project.end_date) {
      items.push({
        id: 'end',
        type: 'project',
        title: 'Project Due',
        date: project.end_date,
        status: new Date() > new Date(project.end_date) ? 'overdue' : 'pending',
        description: `Target completion date`
      });
    }

    // Sort by date
    return items.sort((a, b) => new Date(a.date) - new Date(b.date));
  }, [project, milestones]);

  const getStatusIcon = (status, type) => {
    if (type === 'project') {
      if (status === 'completed') return <PlayArrow className="w-5 h-5" />;
      if (status === 'overdue') return <Warning className="w-5 h-5" />;
      return <Flag className="w-5 h-5" />;
    }

    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5" />;
      case 'in_progress':
        return <Schedule className="w-5 h-5" />;
      case 'overdue':
        return <Warning className="w-5 h-5" />;
      default:
        return <Schedule className="w-5 h-5" />;
    }
  };

  const getStatusColor = (status, type) => {
    if (type === 'project') {
      if (status === 'completed') return 'bg-blue-600';
      if (status === 'overdue') return 'bg-red-600';
      return 'bg-gray-600';
    }

    switch (status) {
      case 'completed':
        return 'bg-green-600';
      case 'in_progress':
        return 'bg-blue-600';
      case 'overdue':
        return 'bg-red-600';
      default:
        return 'bg-gray-600';
    }
  };

  const getStatusLabel = (status) => {
    const labels = {
      completed: 'Completed',
      in_progress: 'In Progress',
      pending: 'Pending',
      overdue: 'Overdue'
    };
    return labels[status] || status;
  };

  const calculateDaysFromNow = (date) => {
    const days = differenceInDays(new Date(date), new Date());
    if (days === 0) return 'Today';
    if (days === 1) return 'Tomorrow';
    if (days === -1) return 'Yesterday';
    if (days > 0) return `In ${days} days`;
    return `${Math.abs(days)} days ago`;
  };

  return (
    <div className="space-y-6">
      {/* Timeline Overview */}
      <Card>
        <CardContent>
          <Typography variant="h6" className="text-gray-900 dark:text-white mb-4">
            Project Timeline Overview
          </Typography>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="text-center">
              <Typography variant="h6" className="text-blue-600 font-bold">
                {project.start_date ? format(new Date(project.start_date), 'MMM dd, yyyy') : 'Not set'}
              </Typography>
              <Typography variant="caption" className="text-gray-500">
                Start Date
              </Typography>
            </div>
            
            <div className="text-center">
              <Typography variant="h6" className="text-green-600 font-bold">
                {milestones.filter(m => m.status === 'completed').length} / {milestones.length}
              </Typography>
              <Typography variant="caption" className="text-gray-500">
                Milestones Completed
              </Typography>
            </div>
            
            <div className="text-center">
              <Typography variant="h6" className="text-orange-600 font-bold">
                {project.end_date ? format(new Date(project.end_date), 'MMM dd, yyyy') : 'Not set'}
              </Typography>
              <Typography variant="caption" className="text-gray-500">
                Target End Date
              </Typography>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                Overall Progress
              </Typography>
              <Typography variant="body2" className="text-gray-900 dark:text-white font-medium">
                {project.progress_percentage || 0}%
              </Typography>
            </div>
            <LinearProgress
              variant="determinate"
              value={project.progress_percentage || 0}
              className="h-2 rounded-full"
              sx={{
                backgroundColor: 'rgba(0,0,0,0.1)',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: project.progress_percentage >= 75 
                    ? '#10B981' 
                    : project.progress_percentage >= 50 
                    ? '#3B82F6' 
                    : project.progress_percentage >= 25 
                    ? '#F59E0B' 
                    : '#EF4444',
                },
              }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Detailed Timeline - Custom Implementation */}
      <Card>
        <CardContent>
          <Typography variant="h6" className="text-gray-900 dark:text-white mb-4">
            Detailed Timeline
          </Typography>
          
          {timelineData.length === 0 ? (
            <div className="text-center py-8">
              <Schedule className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <Typography variant="body1" className="text-gray-500 mb-2">
                No timeline data available
              </Typography>
              <Typography variant="body2" className="text-gray-400">
                Add milestones to create a project timeline
              </Typography>
            </div>
          ) : (
            <div className="space-y-6">
              {timelineData.map((item, index) => (
                <div key={item.id} className="flex items-start space-x-4">
                  {/* Timeline dot and line */}
                  <div className="flex flex-col items-center">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white ${getStatusColor(item.status, item.type)}`}>
                      {getStatusIcon(item.status, item.type)}
                    </div>
                    {index < timelineData.length - 1 && (
                      <div className="w-px h-12 bg-gray-300 dark:bg-gray-600 mt-2"></div>
                    )}
                  </div>

                  {/* Timeline content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <Typography variant="subtitle1" className="text-gray-900 dark:text-white font-medium">
                          {item.title}
                        </Typography>
                        <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                          {format(new Date(item.date), 'MMM dd, yyyy')}
                        </Typography>
                      </div>
                      <div className="text-right">
                        <Chip
                          label={getStatusLabel(item.status)}
                          size="small"
                          className={`mb-1 ${
                            item.status === 'completed' ? 'bg-green-100 text-green-800' :
                            item.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                            item.status === 'overdue' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}
                        />
                        <Typography variant="caption" className="text-gray-500 block">
                          {calculateDaysFromNow(item.date)}
                        </Typography>
                      </div>
                    </div>
                    
                    <Typography variant="body2" className="text-gray-700 dark:text-gray-300 mb-3">
                      {item.description}
                    </Typography>
                    
                    {item.type === 'milestone' && typeof item.progress === 'number' && (
                      <div className="mt-3">
                        <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                          <span>Progress</span>
                          <span>{item.progress}%</span>
                        </div>
                        <LinearProgress
                          variant="determinate"
                          value={item.progress}
                          size="small"
                          className="h-1 rounded-full"
                        />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Critical Milestones */}
      {milestones.length > 0 && (
        <Card>
          <CardContent>
            <Typography variant="h6" className="text-gray-900 dark:text-white mb-4">
              Critical Milestones
            </Typography>
            
            <div className="space-y-3">
              {milestones
                .filter(m => m.is_critical || m.status === 'overdue')
                .map((milestone) => (
                  <div 
                    key={milestone.id}
                    className="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800"
                  >
                    <div className="flex items-center space-x-3">
                      <Warning className="w-5 h-5 text-yellow-600" />
                      <div>
                        <Typography variant="subtitle2" className="text-gray-900 dark:text-white">
                          {milestone.title}
                        </Typography>
                        <Typography variant="caption" className="text-gray-600 dark:text-gray-400">
                          Due: {format(new Date(milestone.due_date), 'MMM dd, yyyy')}
                        </Typography>
                      </div>
                    </div>
                    
                    <Chip
                      label={milestone.status === 'overdue' ? 'Overdue' : 'Critical'}
                      size="small"
                      className={milestone.status === 'overdue' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}
                    />
                  </div>
                ))}
              
              {milestones.filter(m => m.is_critical || m.status === 'overdue').length === 0 && (
                <div className="text-center py-4">
                  <CheckCircle className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                    No critical milestones requiring attention
                  </Typography>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ProjectTimeline;