// Next, React, Tailwind
import { useState } from 'react';
import { useRouter } from 'next/router';
import { twMerge } from 'tailwind-merge';

// Material UI
import {
  Card,
  CardContent,
  Avatar,
  AvatarGroup,
  LinearProgress,
  IconButton,
  Menu,
  MenuItem,
  Chip,
  Tooltip
} from '@mui/material';
import {
  MoreVert,
  TrendingUp,
  TrendingDown,
  Warning,
  CheckCircle,
  Schedule,
  People
} from '@mui/icons-material';

// Utils
import { useProjectTrackerContext } from '../../utils/project-tracker';
import { formatDistanceToNow } from 'date-fns';

const ProjectCard = ({ project, onEdit, onDelete }) => {
  const router = useRouter();
  const { getHealthColor, getStatusColor, getPriorityColor } = useProjectTrackerContext();
  const [menuAnchor, setMenuAnchor] = useState(null);

  const handleMenuOpen = (event) => {
    event.stopPropagation();
    setMenuAnchor(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
  };

  const handleCardClick = () => {
    if (project?.id) {
      router.push(`/project-tracker/project/${project.id}`);
    }
  };

  const health = project.health || 'good';
  const healthColor = getHealthColor(health);
  const statusColor = getStatusColor(project.status);
  const priorityColor = getPriorityColor(project.priority);

  const getHealthIcon = () => {
    switch (health) {
      case 'excellent':
        return <CheckCircle className="w-4 h-4" />;
      case 'good':
        return <TrendingUp className="w-4 h-4" />;
      case 'warning':
        return <Schedule className="w-4 h-4" />;
      case 'critical':
        return <Warning className="w-4 h-4" />;
      default:
        return <TrendingUp className="w-4 h-4" />;
    }
  };

  const getRiskLevel = () => {
    const riskScore = project.risk_score || 0;
    if (riskScore >= 8) return { level: 'High', color: 'text-red-600' };
    if (riskScore >= 5) return { level: 'Medium', color: 'text-yellow-600' };
    return { level: 'Low', color: 'text-green-600' };
  };

  const risk = getRiskLevel();

  return (
    <Card 
      className={twMerge(
        "h-full cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02]",
        "border border-gray-200 dark:border-gray-700"
      )}
      onClick={handleCardClick}
    >
      <CardContent className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1 truncate">
              {project.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
              {project.description}
            </p>
          </div>
          <IconButton
            size="small"
            onClick={handleMenuOpen}
            className="ml-2 flex-shrink-0"
          >
            <MoreVert fontSize="small" />
          </IconButton>
        </div>

        {/* Status and Priority Badges */}
        <div className="flex items-center gap-2 mb-4">
          <Chip
            label={project.status?.replace('_', ' ')}
            size="small"
            className={`${statusColor} font-medium`}
          />
          <Chip
            label={project.priority}
            size="small"
            className={`${priorityColor} font-medium`}
          />
        </div>

        {/* Health and Risk Indicators */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <div className={`flex items-center space-x-1 px-2 py-1 rounded-full ${healthColor}`}>
              {getHealthIcon()}
              <span className="text-xs font-medium capitalize">{health}</span>
            </div>
          </div>
          <div className="flex items-center space-x-2 text-xs">
            <span className="text-gray-500">Risk:</span>
            <span className={`font-medium ${risk.color}`}>{risk.level}</span>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
            <span>Progress</span>
            <span>{project.progress_percentage || 0}%</span>
          </div>
          <LinearProgress
            variant="determinate"
            value={project.progress_percentage || 0}
            className="h-2 rounded-full"
            sx={{
              backgroundColor: 'rgba(0,0,0,0.1)',
              '& .MuiLinearProgress-bar': {
                backgroundColor: project.progress_percentage >= 75 
                  ? '#10B981' 
                  : project.progress_percentage >= 50 
                  ? '#3B82F6' 
                  : project.progress_percentage >= 25 
                  ? '#F59E0B' 
                  : '#EF4444',
              },
            }}
          />
        </div>

        {/* Team and Timeline */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <People className="w-4 h-4 text-gray-500" />
            <AvatarGroup max={4} sx={{ '& .MuiAvatar-root': { width: 24, height: 24, fontSize: 12 } }}>
              {project.team?.map((member, index) => (
                <Avatar key={index} alt={member.name}>
                  {member.name?.charAt(0)}
                </Avatar>
              )) || []}
            </AvatarGroup>
          </div>
          
          <div className="text-xs text-gray-500">
            {project.end_date && (
              <Tooltip title={`Due: ${new Date(project.end_date).toLocaleDateString()}`}>
                <span>
                  Due {formatDistanceToNow(new Date(project.end_date), { addSuffix: true })}
                </span>
              </Tooltip>
            )}
          </div>
        </div>

        {/* Milestones indicator */}
        {project.milestones_count && (
          <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500">Milestones</span>
              <span className="text-gray-700 dark:text-gray-300">
                {project.completed_milestones || 0} / {project.milestones_count}
              </span>
            </div>
          </div>
        )}
      </CardContent>

      {/* Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
        onClick={(e) => e.stopPropagation()}
      >
        <MenuItem onClick={() => { handleMenuClose(); onEdit?.(project); }}>
          Edit Project
        </MenuItem>
        <MenuItem onClick={() => { 
          handleMenuClose(); 
          if (project?.id) {
            router.push(`/project-tracker/project/${project.id}`);
          }
        }}>
          View Details
        </MenuItem>
        <MenuItem 
          onClick={() => { handleMenuClose(); onDelete?.(project); }}
          className="text-red-600"
        >
          Delete Project
        </MenuItem>
      </Menu>
    </Card>
  );
};

export default ProjectCard;