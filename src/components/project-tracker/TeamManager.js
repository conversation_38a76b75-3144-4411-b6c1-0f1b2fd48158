// React
import { useState } from 'react';

// Material UI
import {
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Tooltip,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Person,
  Email,
  Phone,
  Business
} from '@mui/icons-material';

// Components
import { EnhancedButton } from '../fa/ui/EnhancedButton';
import { EmptyState } from '../ui/empty-state';

// Utils
import { useSnackbar } from '../Shared/snackbar';

const TeamManager = ({ projectId, team = [] }) => {
  const { enqueueSnackbar } = useSnackbar();
  
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingMember, setEditingMember] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    role: '',
    department: '',
    phone: '',
    skills: [],
    availability: 100
  });

  const handleOpenDialog = (member = null) => {
    if (member) {
      setEditingMember(member);
      setFormData({
        name: member.name || '',
        email: member.email || '',
        role: member.role || '',
        department: member.department || '',
        phone: member.phone || '',
        skills: member.skills || [],
        availability: member.availability || 100
      });
    } else {
      setEditingMember(null);
      setFormData({
        name: '',
        email: '',
        role: '',
        department: '',
        phone: '',
        skills: [],
        availability: 100
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingMember(null);
  };

  const handleFormChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    if (!formData.name.trim() || !formData.email.trim()) {
      enqueueSnackbar('Name and email are required', { variant: 'error' });
      return;
    }

    try {
      // Here you would typically make an API call to add/update team member
      enqueueSnackbar(
        editingMember ? 'Team member updated successfully' : 'Team member added successfully',
        { variant: 'success' }
      );
      
      handleCloseDialog();
    } catch (error) {
      enqueueSnackbar('Failed to save team member', { variant: 'error' });
    }
  };

  const getRoleColor = (role) => {
    const colors = {
      'project manager': 'text-purple-600 bg-purple-100',
      'tech lead': 'text-blue-600 bg-blue-100',
      'developer': 'text-green-600 bg-green-100',
      'designer': 'text-pink-600 bg-pink-100',
      'analyst': 'text-orange-600 bg-orange-100',
      'qa': 'text-red-600 bg-red-100',
      'stakeholder': 'text-gray-600 bg-gray-100'
    };
    return colors[role?.toLowerCase()] || colors.developer;
  };

  const getAvailabilityColor = (availability) => {
    if (availability >= 80) return 'success';
    if (availability >= 50) return 'warning';
    return 'error';
  };

  // Calculate team statistics
  const stats = {
    total: team.length,
    fullTime: team.filter(m => (m.availability || 100) >= 80).length,
    partTime: team.filter(m => (m.availability || 100) < 80 && (m.availability || 100) >= 50).length,
    limited: team.filter(m => (m.availability || 100) < 50).length,
    avgAvailability: team.length > 0 
      ? team.reduce((sum, m) => sum + (m.availability || 100), 0) / team.length 
      : 0
  };

  // Group team members by role
  const teamByRole = team.reduce((acc, member) => {
    const role = member.role || 'Other';
    if (!acc[role]) acc[role] = [];
    acc[role].push(member);
    return acc;
  }, {});

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <Grid container spacing={3}>
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent className="text-center">
              <Typography variant="h4" className="font-bold text-blue-600">
                {stats.total}
              </Typography>
              <Typography variant="caption" className="text-gray-500">
                Team Members
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent className="text-center">
              <Typography variant="h4" className="font-bold text-green-600">
                {stats.fullTime}
              </Typography>
              <Typography variant="caption" className="text-gray-500">
                Full Time (80%+)
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent className="text-center">
              <Typography variant="h4" className="font-bold text-orange-600">
                {stats.partTime}
              </Typography>
              <Typography variant="caption" className="text-gray-500">
                Part Time (50-79%)
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={6} sm={3}>
          <Card>
            <CardContent className="text-center">
              <Typography variant="h4" className="font-bold text-purple-600">
                {stats.avgAvailability.toFixed(0)}%
              </Typography>
              <Typography variant="caption" className="text-gray-500">
                Avg Availability
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Team Members */}
      <Card>
        <CardContent className="p-0">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
            <Typography variant="h6" className="text-gray-900 dark:text-white">
              Team Members
            </Typography>
            <EnhancedButton
              variant="contained"
              startIcon={<Add />}
              onClick={() => handleOpenDialog()}
            >
              Add Member
            </EnhancedButton>
          </div>

          {team.length === 0 ? (
            <div className="p-6">
              <EmptyState
                title="No team members yet"
                description="Add team members to track project assignments and availability"
                action={
                  <EnhancedButton
                    variant="contained"
                    onClick={() => handleOpenDialog()}
                  >
                    Add Team Member
                  </EnhancedButton>
                }
              />
            </div>
          ) : (
            <div className="p-6">
              {Object.entries(teamByRole).map(([role, members]) => (
                <div key={role} className="mb-8">
                  <Typography variant="h6" className="text-gray-900 dark:text-white mb-4 flex items-center">
                    <Person className="w-5 h-5 mr-2" />
                    {role} ({members.length})
                  </Typography>
                  
                  <Grid container spacing={3}>
                    {members.map((member) => (
                      <Grid item xs={12} md={6} lg={4} key={member.id}>
                        <Card className="h-full">
                          <CardContent>
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex items-center space-x-3">
                                <Avatar sx={{ width: 48, height: 48 }}>
                                  {member.name?.charAt(0)}
                                </Avatar>
                                <div>
                                  <Typography variant="subtitle1" className="text-gray-900 dark:text-white font-medium">
                                    {member.name}
                                  </Typography>
                                  <Typography variant="caption" className="text-gray-500">
                                    {member.department}
                                  </Typography>
                                </div>
                              </div>
                              
                              <div className="flex items-center space-x-1">
                                <Tooltip title="Edit Member">
                                  <IconButton
                                    size="small"
                                    onClick={() => handleOpenDialog(member)}
                                  >
                                    <Edit fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip title="Remove Member">
                                  <IconButton
                                    size="small"
                                    className="text-red-600 hover:bg-red-50"
                                  >
                                    <Delete fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              </div>
                            </div>

                            <div className="space-y-2 mb-4">
                              <div className="flex items-center space-x-2 text-sm text-gray-600">
                                <Email className="w-4 h-4" />
                                <span>{member.email}</span>
                              </div>
                              
                              {member.phone && (
                                <div className="flex items-center space-x-2 text-sm text-gray-600">
                                  <Phone className="w-4 h-4" />
                                  <span>{member.phone}</span>
                                </div>
                              )}
                              
                              {member.department && (
                                <div className="flex items-center space-x-2 text-sm text-gray-600">
                                  <Business className="w-4 h-4" />
                                  <span>{member.department}</span>
                                </div>
                              )}
                            </div>

                            {/* Role Chip */}
                            <div className="mb-3">
                              <Chip
                                label={member.role}
                                size="small"
                                className={`${getRoleColor(member.role)} font-medium`}
                              />
                            </div>

                            {/* Skills */}
                            {member.skills && member.skills.length > 0 && (
                              <div className="mb-3">
                                <Typography variant="caption" className="text-gray-500 block mb-1">
                                  Skills
                                </Typography>
                                <div className="flex flex-wrap gap-1">
                                  {member.skills.slice(0, 3).map((skill, index) => (
                                    <Chip
                                      key={index}
                                      label={skill}
                                      size="small"
                                      variant="outlined"
                                      className="text-xs"
                                    />
                                  ))}
                                  {member.skills.length > 3 && (
                                    <Chip
                                      label={`+${member.skills.length - 3}`}
                                      size="small"
                                      variant="outlined"
                                      className="text-xs"
                                    />
                                  )}
                                </div>
                              </div>
                            )}

                            {/* Availability */}
                            <div>
                              <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                                <span>Availability</span>
                                <span>{member.availability || 100}%</span>
                              </div>
                              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div
                                  className={`h-2 rounded-full ${
                                    getAvailabilityColor(member.availability || 100) === 'success' 
                                      ? 'bg-green-600' 
                                      : getAvailabilityColor(member.availability || 100) === 'warning'
                                      ? 'bg-yellow-600'
                                      : 'bg-red-600'
                                  }`}
                                  style={{ width: `${member.availability || 100}%` }}
                                />
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Team Member Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingMember ? 'Edit Team Member' : 'Add Team Member'}
        </DialogTitle>
        
        <DialogContent className="space-y-4">
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Full Name"
                value={formData.name}
                onChange={(e) => handleFormChange('name', e.target.value)}
                margin="normal"
                required
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => handleFormChange('email', e.target.value)}
                margin="normal"
                required
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Role</InputLabel>
                <Select
                  value={formData.role}
                  onChange={(e) => handleFormChange('role', e.target.value)}
                  label="Role"
                >
                  <MenuItem value="Project Manager">Project Manager</MenuItem>
                  <MenuItem value="Tech Lead">Tech Lead</MenuItem>
                  <MenuItem value="Developer">Developer</MenuItem>
                  <MenuItem value="Designer">Designer</MenuItem>
                  <MenuItem value="Analyst">Analyst</MenuItem>
                  <MenuItem value="QA">QA</MenuItem>
                  <MenuItem value="Stakeholder">Stakeholder</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Department"
                value={formData.department}
                onChange={(e) => handleFormChange('department', e.target.value)}
                margin="normal"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                value={formData.phone}
                onChange={(e) => handleFormChange('phone', e.target.value)}
                margin="normal"
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="number"
                label="Availability %"
                value={formData.availability}
                onChange={(e) => handleFormChange('availability', Math.min(100, Math.max(0, parseInt(e.target.value) || 0)))}
                margin="normal"
                inputProps={{ min: 0, max: 100 }}
                helperText="Percentage of time available for this project"
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Skills (comma separated)"
                value={formData.skills.join(', ')}
                onChange={(e) => handleFormChange('skills', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
                margin="normal"
                placeholder="React, Node.js, Project Management..."
                helperText="Enter skills separated by commas"
              />
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions>
          <EnhancedButton
            variant="outlined"
            onClick={handleCloseDialog}
          >
            Cancel
          </EnhancedButton>
          <EnhancedButton
            variant="contained"
            onClick={handleSubmit}
          >
            {editingMember ? 'Update Member' : 'Add Member'}
          </EnhancedButton>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default TeamManager;