// React
import { useState } from 'react';
import { useDispatch } from 'react-redux';

// Material UI
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Tooltip,
  Rating
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  Add,
  Edit,
  Delete,
  Warning,
  ErrorOutline,
  CheckCircle,
  Schedule
} from '@mui/icons-material';

// Components
import { EnhancedButton } from '../fa/ui/EnhancedButton';
import { EmptyState } from '../ui/empty-state';

// Utils
import { createRisk } from '../../utils/store/projectTrackerReducer';
import { useSnackbar } from '../Shared/snackbar';
import { format } from 'date-fns';

const RiskManager = ({ projectId, risks = [] }) => {
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingRisk, setEditingRisk] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'technical',
    impact: 3,
    likelihood: 3,
    status: 'identified',
    owner: '',
    mitigation_plan: '',
    due_date: null
  });

  const handleOpenDialog = (risk = null) => {
    if (risk) {
      setEditingRisk(risk);
      setFormData({
        title: risk.title || '',
        description: risk.description || '',
        category: risk.category || 'technical',
        impact: risk.impact || 3,
        likelihood: risk.likelihood || 3,
        status: risk.status || 'identified',
        owner: risk.owner || '',
        mitigation_plan: risk.mitigation_plan || '',
        due_date: risk.due_date ? new Date(risk.due_date) : null
      });
    } else {
      setEditingRisk(null);
      setFormData({
        title: '',
        description: '',
        category: 'technical',
        impact: 3,
        likelihood: 3,
        status: 'identified',
        owner: '',
        mitigation_plan: '',
        due_date: null
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingRisk(null);
  };

  const handleFormChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    if (!formData.title.trim()) {
      enqueueSnackbar('Risk title is required', { variant: 'error' });
      return;
    }

    try {
      const riskScore = formData.impact * formData.likelihood;
      
      await dispatch(createRisk({
        projectId,
        riskData: {
          ...formData,
          risk_score: riskScore,
          due_date: formData.due_date?.toISOString()
        }
      })).unwrap();

      enqueueSnackbar(
        editingRisk ? 'Risk updated successfully' : 'Risk created successfully',
        { variant: 'success' }
      );
      
      handleCloseDialog();
    } catch (error) {
      enqueueSnackbar('Failed to save risk', { variant: 'error' });
    }
  };

  const getRiskScoreColor = (score) => {
    if (score >= 20) return 'error';
    if (score >= 12) return 'warning';
    if (score >= 6) return 'info';
    return 'success';
  };

  const getRiskScoreLabel = (score) => {
    if (score >= 20) return 'Critical';
    if (score >= 12) return 'High';
    if (score >= 6) return 'Medium';
    return 'Low';
  };

  const getStatusColor = (status) => {
    const colors = {
      identified: 'warning',
      analyzing: 'info',
      mitigating: 'primary',
      monitoring: 'secondary',
      resolved: 'success',
      closed: 'default'
    };
    return colors[status] || 'default';
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'resolved':
      case 'closed':
        return <CheckCircle className="w-4 h-4" />;
      case 'mitigating':
      case 'monitoring':
        return <Schedule className="w-4 h-4" />;
      default:
        return <Warning className="w-4 h-4" />;
    }
  };

  const getCategoryColor = (category) => {
    const colors = {
      technical: 'text-blue-600 bg-blue-100',
      operational: 'text-green-600 bg-green-100',
      financial: 'text-purple-600 bg-purple-100',
      external: 'text-orange-600 bg-orange-100',
      regulatory: 'text-red-600 bg-red-100',
      resource: 'text-gray-600 bg-gray-100'
    };
    return colors[category] || colors.technical;
  };

  // Calculate risk statistics
  const stats = {
    total: risks.length,
    critical: risks.filter(r => (r.impact * r.likelihood) >= 20).length,
    high: risks.filter(r => {
      const score = r.impact * r.likelihood;
      return score >= 12 && score < 20;
    }).length,
    open: risks.filter(r => !['resolved', 'closed'].includes(r.status)).length,
    overdue: risks.filter(r => 
      r.due_date && 
      new Date(r.due_date) < new Date() && 
      !['resolved', 'closed'].includes(r.status)
    ).length
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <div className="space-y-6">
        {/* Stats Cards */}
        <Grid container spacing={3}>
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent className="text-center">
                <Typography variant="h4" className="font-bold text-blue-600">
                  {stats.total}
                </Typography>
                <Typography variant="caption" className="text-gray-500">
                  Total Risks
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent className="text-center">
                <Typography variant="h4" className="font-bold text-red-600">
                  {stats.critical}
                </Typography>
                <Typography variant="caption" className="text-gray-500">
                  Critical Risks
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent className="text-center">
                <Typography variant="h4" className="font-bold text-orange-600">
                  {stats.high}
                </Typography>
                <Typography variant="caption" className="text-gray-500">
                  High Risks
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent className="text-center">
                <Typography variant="h4" className="font-bold text-yellow-600">
                  {stats.overdue}
                </Typography>
                <Typography variant="caption" className="text-gray-500">
                  Overdue
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Risks Table */}
        <Card>
          <CardContent className="p-0">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
              <Typography variant="h6" className="text-gray-900 dark:text-white">
                Risk Register
              </Typography>
              <EnhancedButton
                variant="contained"
                startIcon={<Add />}
                onClick={() => handleOpenDialog()}
              >
                Add Risk
              </EnhancedButton>
            </div>

            {risks.length === 0 ? (
              <div className="p-6">
                <EmptyState
                  title="No risks identified"
                  description="Proactively identify and manage project risks"
                  action={
                    <EnhancedButton
                      variant="contained"
                      onClick={() => handleOpenDialog()}
                    >
                      Identify Risk
                    </EnhancedButton>
                  }
                />
              </div>
            ) : (
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Risk</TableCell>
                    <TableCell>Category</TableCell>
                    <TableCell>Risk Score</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Owner</TableCell>
                    <TableCell>Due Date</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {risks.map((risk) => {
                    const riskScore = risk.impact * risk.likelihood;
                    return (
                      <TableRow key={risk.id}>
                        <TableCell>
                          <div>
                            <Typography variant="subtitle2" className="text-gray-900 dark:text-white">
                              {risk.title}
                            </Typography>
                            {risk.description && (
                              <Typography variant="caption" className="text-gray-500 block mt-1">
                                {risk.description.length > 100 
                                  ? `${risk.description.substring(0, 100)}...` 
                                  : risk.description
                                }
                              </Typography>
                            )}
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <Chip
                            label={risk.category}
                            size="small"
                            className={`${getCategoryColor(risk.category)} font-medium`}
                          />
                        </TableCell>
                        
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Chip
                              label={`${riskScore} - ${getRiskScoreLabel(riskScore)}`}
                              size="small"
                              color={getRiskScoreColor(riskScore)}
                            />
                            <div className="text-xs text-gray-500">
                              <div>Impact: {risk.impact}/5</div>
                              <div>Likelihood: {risk.likelihood}/5</div>
                            </div>
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <Chip
                            label={risk.status}
                            size="small"
                            color={getStatusColor(risk.status)}
                            icon={getStatusIcon(risk.status)}
                          />
                        </TableCell>
                        
                        <TableCell>
                          <Typography variant="body2">
                            {risk.owner || 'Unassigned'}
                          </Typography>
                        </TableCell>
                        
                        <TableCell>
                          {risk.due_date ? (
                            <div>
                              <Typography variant="body2">
                                {format(new Date(risk.due_date), 'MMM dd, yyyy')}
                              </Typography>
                              {new Date(risk.due_date) < new Date() && 
                               !['resolved', 'closed'].includes(risk.status) && (
                                <Typography variant="caption" className="text-red-600">
                                  Overdue
                                </Typography>
                              )}
                            </div>
                          ) : (
                            <Typography variant="body2" className="text-gray-500">
                              No due date
                            </Typography>
                          )}
                        </TableCell>
                        
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <Tooltip title="Edit Risk">
                              <IconButton
                                size="small"
                                onClick={() => handleOpenDialog(risk)}
                              >
                                <Edit fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Delete Risk">
                              <IconButton
                                size="small"
                                className="text-red-600 hover:bg-red-50"
                              >
                                <Delete fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* Risk Dialog */}
        <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
          <DialogTitle>
            {editingRisk ? 'Edit Risk' : 'Identify New Risk'}
          </DialogTitle>
          
          <DialogContent className="space-y-4">
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Risk Title"
                  value={formData.title}
                  onChange={(e) => handleFormChange('title', e.target.value)}
                  margin="normal"
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Risk Description"
                  value={formData.description}
                  onChange={(e) => handleFormChange('description', e.target.value)}
                  margin="normal"
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Category</InputLabel>
                  <Select
                    value={formData.category}
                    onChange={(e) => handleFormChange('category', e.target.value)}
                    label="Category"
                  >
                    <MenuItem value="technical">Technical</MenuItem>
                    <MenuItem value="operational">Operational</MenuItem>
                    <MenuItem value="financial">Financial</MenuItem>
                    <MenuItem value="external">External</MenuItem>
                    <MenuItem value="regulatory">Regulatory</MenuItem>
                    <MenuItem value="resource">Resource</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={formData.status}
                    onChange={(e) => handleFormChange('status', e.target.value)}
                    label="Status"
                  >
                    <MenuItem value="identified">Identified</MenuItem>
                    <MenuItem value="analyzing">Analyzing</MenuItem>
                    <MenuItem value="mitigating">Mitigating</MenuItem>
                    <MenuItem value="monitoring">Monitoring</MenuItem>
                    <MenuItem value="resolved">Resolved</MenuItem>
                    <MenuItem value="closed">Closed</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography component="legend" className="text-sm text-gray-600 mb-2">
                  Impact (1 = Very Low, 5 = Very High)
                </Typography>
                <Rating
                  value={formData.impact}
                  onChange={(event, newValue) => handleFormChange('impact', newValue)}
                  max={5}
                  size="large"
                />
                <Typography variant="caption" className="text-gray-500 block mt-1">
                  Current: {formData.impact}/5
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography component="legend" className="text-sm text-gray-600 mb-2">
                  Likelihood (1 = Very Low, 5 = Very High)
                </Typography>
                <Rating
                  value={formData.likelihood}
                  onChange={(event, newValue) => handleFormChange('likelihood', newValue)}
                  max={5}
                  size="large"
                />
                <Typography variant="caption" className="text-gray-500 block mt-1">
                  Current: {formData.likelihood}/5
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Risk Owner"
                  value={formData.owner}
                  onChange={(e) => handleFormChange('owner', e.target.value)}
                  margin="normal"
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <DatePicker
                  label="Due Date"
                  value={formData.due_date}
                  onChange={(date) => handleFormChange('due_date', date)}
                  renderInput={(params) => (
                    <TextField {...params} fullWidth margin="normal" />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Mitigation Plan"
                  value={formData.mitigation_plan}
                  onChange={(e) => handleFormChange('mitigation_plan', e.target.value)}
                  margin="normal"
                  placeholder="Describe how this risk will be mitigated..."
                />
              </Grid>

              {/* Risk Score Preview */}
              <Grid item xs={12}>
                <Card className="bg-gray-50 dark:bg-gray-800">
                  <CardContent>
                    <Typography variant="subtitle2" className="mb-2">
                      Risk Score Preview
                    </Typography>
                    <div className="flex items-center space-x-4">
                      <div className="text-center">
                        <Typography variant="h4" className="font-bold text-blue-600">
                          {formData.impact * formData.likelihood}
                        </Typography>
                        <Typography variant="caption">
                          Risk Score
                        </Typography>
                      </div>
                      <div>
                        <Chip
                          label={getRiskScoreLabel(formData.impact * formData.likelihood)}
                          color={getRiskScoreColor(formData.impact * formData.likelihood)}
                        />
                        <Typography variant="caption" className="block mt-1 text-gray-500">
                          Impact ({formData.impact}) × Likelihood ({formData.likelihood})
                        </Typography>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </DialogContent>

          <DialogActions>
            <EnhancedButton
              variant="outlined"
              onClick={handleCloseDialog}
            >
              Cancel
            </EnhancedButton>
            <EnhancedButton
              variant="contained"
              onClick={handleSubmit}
            >
              {editingRisk ? 'Update Risk' : 'Add Risk'}
            </EnhancedButton>
          </DialogActions>
        </Dialog>
      </div>
    </LocalizationProvider>
  );
};

export default RiskManager;