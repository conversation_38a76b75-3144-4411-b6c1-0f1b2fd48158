// React
import { useState } from 'react';
import { useDispatch } from 'react-redux';

// Material UI
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  IconButton,
  Chip,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Tooltip
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  Add,
  Edit,
  Delete,
  CheckCircle,
  Schedule,
  Flag,
  Warning
} from '@mui/icons-material';

// Components
import { EnhancedButton } from '../fa/ui/EnhancedButton';
import { EmptyState } from '../ui/empty-state';

// Utils
import { createMilestone } from '../../utils/store/projectTrackerReducer';
import { useSnackbar } from '../Shared/snackbar';
import { format } from 'date-fns';

const MilestoneManager = ({ projectId, milestones = [] }) => {
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingMilestone, setEditingMilestone] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    due_date: null,
    status: 'pending',
    priority: 'medium',
    progress_percentage: 0,
    is_critical: false
  });

  const handleOpenDialog = (milestone = null) => {
    if (milestone) {
      setEditingMilestone(milestone);
      setFormData({
        title: milestone.title || '',
        description: milestone.description || '',
        due_date: milestone.due_date ? new Date(milestone.due_date) : null,
        status: milestone.status || 'pending',
        priority: milestone.priority || 'medium',
        progress_percentage: milestone.progress_percentage || 0,
        is_critical: milestone.is_critical || false
      });
    } else {
      setEditingMilestone(null);
      setFormData({
        title: '',
        description: '',
        due_date: null,
        status: 'pending',
        priority: 'medium',
        progress_percentage: 0,
        is_critical: false
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingMilestone(null);
  };

  const handleFormChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    if (!formData.title.trim()) {
      enqueueSnackbar('Milestone title is required', { variant: 'error' });
      return;
    }

    try {
      await dispatch(createMilestone({
        projectId,
        milestoneData: {
          ...formData,
          due_date: formData.due_date?.toISOString()
        }
      })).unwrap();

      enqueueSnackbar(
        editingMilestone ? 'Milestone updated successfully' : 'Milestone created successfully',
        { variant: 'success' }
      );
      
      handleCloseDialog();
    } catch (error) {
      enqueueSnackbar('Failed to save milestone', { variant: 'error' });
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      completed: 'success',
      in_progress: 'info',
      pending: 'default',
      overdue: 'error'
    };
    return colors[status] || 'default';
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'in_progress':
        return <Schedule className="w-4 h-4" />;
      case 'overdue':
        return <Warning className="w-4 h-4" />;
      default:
        return <Flag className="w-4 h-4" />;
    }
  };

  const getPriorityColor = (priority) => {
    const colors = {
      critical: 'text-red-600 bg-red-100',
      high: 'text-orange-600 bg-orange-100',
      medium: 'text-yellow-600 bg-yellow-100',
      low: 'text-green-600 bg-green-100'
    };
    return colors[priority] || colors.medium;
  };

  // Calculate milestone statistics
  const stats = {
    total: milestones.length,
    completed: milestones.filter(m => m.status === 'completed').length,
    inProgress: milestones.filter(m => m.status === 'in_progress').length,
    overdue: milestones.filter(m => m.status === 'overdue').length,
    critical: milestones.filter(m => m.is_critical).length
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <div className="space-y-6">
        {/* Stats Cards */}
        <Grid container spacing={3}>
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent className="text-center">
                <Typography variant="h4" className="font-bold text-blue-600">
                  {stats.total}
                </Typography>
                <Typography variant="caption" className="text-gray-500">
                  Total Milestones
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent className="text-center">
                <Typography variant="h4" className="font-bold text-green-600">
                  {stats.completed}
                </Typography>
                <Typography variant="caption" className="text-gray-500">
                  Completed
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent className="text-center">
                <Typography variant="h4" className="font-bold text-blue-600">
                  {stats.inProgress}
                </Typography>
                <Typography variant="caption" className="text-gray-500">
                  In Progress
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Card>
              <CardContent className="text-center">
                <Typography variant="h4" className="font-bold text-red-600">
                  {stats.overdue}
                </Typography>
                <Typography variant="caption" className="text-gray-500">
                  Overdue
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Milestones Table */}
        <Card>
          <CardContent className="p-0">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
              <Typography variant="h6" className="text-gray-900 dark:text-white">
                Project Milestones
              </Typography>
              <EnhancedButton
                variant="contained"
                startIcon={<Add />}
                onClick={() => handleOpenDialog()}
              >
                Add Milestone
              </EnhancedButton>
            </div>

            {milestones.length === 0 ? (
              <div className="p-6">
                <EmptyState
                  title="No milestones yet"
                  description="Create your first milestone to track project progress"
                  action={
                    <EnhancedButton
                      variant="contained"
                      onClick={() => handleOpenDialog()}
                    >
                      Create Milestone
                    </EnhancedButton>
                  }
                />
              </div>
            ) : (
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Milestone</TableCell>
                    <TableCell>Due Date</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Priority</TableCell>
                    <TableCell>Progress</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {milestones.map((milestone) => (
                    <TableRow key={milestone.id}>
                      <TableCell>
                        <div>
                          <div className="flex items-center space-x-2">
                            <Typography variant="subtitle2" className="text-gray-900 dark:text-white">
                              {milestone.title}
                            </Typography>
                            {milestone.is_critical && (
                              <Chip
                                label="Critical"
                                size="small"
                                color="error"
                                variant="outlined"
                              />
                            )}
                          </div>
                          {milestone.description && (
                            <Typography variant="caption" className="text-gray-500 block mt-1">
                              {milestone.description}
                            </Typography>
                          )}
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        {milestone.due_date ? (
                          <div>
                            <Typography variant="body2">
                              {format(new Date(milestone.due_date), 'MMM dd, yyyy')}
                            </Typography>
                            <Typography variant="caption" className="text-gray-500">
                              {new Date(milestone.due_date) < new Date() ? 
                                `${Math.ceil((new Date() - new Date(milestone.due_date)) / (1000 * 60 * 60 * 24))} days overdue` :
                                `${Math.ceil((new Date(milestone.due_date) - new Date()) / (1000 * 60 * 60 * 24))} days left`
                              }
                            </Typography>
                          </div>
                        ) : (
                          <Typography variant="body2" className="text-gray-500">
                            No due date
                          </Typography>
                        )}
                      </TableCell>
                      
                      <TableCell>
                        <Chip
                          label={milestone.status}
                          size="small"
                          color={getStatusColor(milestone.status)}
                          icon={getStatusIcon(milestone.status)}
                        />
                      </TableCell>
                      
                      <TableCell>
                        <Chip
                          label={milestone.priority}
                          size="small"
                          className={`${getPriorityColor(milestone.priority)} font-medium`}
                        />
                      </TableCell>
                      
                      <TableCell>
                        <div className="w-24">
                          <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                            <span>{milestone.progress_percentage || 0}%</span>
                          </div>
                          <LinearProgress
                            variant="determinate"
                            value={milestone.progress_percentage || 0}
                            size="small"
                            className="h-1 rounded-full"
                          />
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Tooltip title="Edit Milestone">
                            <IconButton
                              size="small"
                              onClick={() => handleOpenDialog(milestone)}
                            >
                              <Edit fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete Milestone">
                            <IconButton
                              size="small"
                              className="text-red-600 hover:bg-red-50"
                            >
                              <Delete fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>

        {/* Milestone Dialog */}
        <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
          <DialogTitle>
            {editingMilestone ? 'Edit Milestone' : 'Create New Milestone'}
          </DialogTitle>
          
          <DialogContent className="space-y-4">
            <TextField
              fullWidth
              label="Milestone Title"
              value={formData.title}
              onChange={(e) => handleFormChange('title', e.target.value)}
              margin="normal"
            />
            
            <TextField
              fullWidth
              multiline
              rows={3}
              label="Description"
              value={formData.description}
              onChange={(e) => handleFormChange('description', e.target.value)}
              margin="normal"
            />

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <DatePicker
                  label="Due Date"
                  value={formData.due_date}
                  onChange={(date) => handleFormChange('due_date', date)}
                  renderInput={(params) => (
                    <TextField {...params} fullWidth margin="normal" />
                  )}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={formData.status}
                    onChange={(e) => handleFormChange('status', e.target.value)}
                    label="Status"
                  >
                    <MenuItem value="pending">Pending</MenuItem>
                    <MenuItem value="in_progress">In Progress</MenuItem>
                    <MenuItem value="completed">Completed</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth margin="normal">
                  <InputLabel>Priority</InputLabel>
                  <Select
                    value={formData.priority}
                    onChange={(e) => handleFormChange('priority', e.target.value)}
                    label="Priority"
                  >
                    <MenuItem value="low">Low</MenuItem>
                    <MenuItem value="medium">Medium</MenuItem>
                    <MenuItem value="high">High</MenuItem>
                    <MenuItem value="critical">Critical</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  type="number"
                  label="Progress %"
                  value={formData.progress_percentage}
                  onChange={(e) => handleFormChange('progress_percentage', Math.min(100, Math.max(0, parseInt(e.target.value) || 0)))}
                  margin="normal"
                  inputProps={{ min: 0, max: 100 }}
                />
              </Grid>
            </Grid>
          </DialogContent>

          <DialogActions>
            <EnhancedButton
              variant="outlined"
              onClick={handleCloseDialog}
            >
              Cancel
            </EnhancedButton>
            <EnhancedButton
              variant="contained"
              onClick={handleSubmit}
            >
              {editingMilestone ? 'Update' : 'Create'}
            </EnhancedButton>
          </DialogActions>
        </Dialog>
      </div>
    </LocalizationProvider>
  );
};

export default MilestoneManager;