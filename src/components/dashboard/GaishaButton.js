// Next, react, Tw
import { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/router';

// Mui
import { Tooltip } from '@mui/material';

// Packages
import PropTypes from 'prop-types';

// Others

const GaishaButton = ({ disabled = false }) => {
  // Standard and Vars
  const { push } = useRouter();

  const [pulseDuration, setPulseDuration] = useState('1s');

  // Others

  const getRandomPulseDuration = () => {
    const min = 0.5;
    const max = 3.5;
    return `${(Math.random() * (max - min) + min).toFixed(2)}s`;
  };

  useEffect(() => {
    let intervalId;
    if (!disabled) {
      const updatePulseDuration = () => setPulseDuration(getRandomPulseDuration());
      intervalId = setInterval(updatePulseDuration, 3000);
      updatePulseDuration();
    }
    return () => (intervalId ? clearInterval(intervalId) : null);
  }, []);

  return (
    <Tooltip title="TANYA">
      <span>
        <button
          type="button"
          disabled={disabled}
          onClick={() => push('/tanya')}
          className="joyride-dashboard-step-7 disabled:grayscale"
          style={{ '--pulse-duration': !disabled ? pulseDuration : 0 }}
        >
          <Image
            src="/gaisha/logo.svg"
            alt="TANYA Logo"
            width={50}
            height={50}
            className="animate-pulse-custom"
            style={{ width: "auto", height: "auto", maxHeight: "50px" }}
            priority
          />
        </button>
      </span>
    </Tooltip>
  );
};

GaishaButton.propTypes = {
  disabled: PropTypes.bool,
};

export default GaishaButton;
