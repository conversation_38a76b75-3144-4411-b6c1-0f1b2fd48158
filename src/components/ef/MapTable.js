// Next, React, Tw
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { twMerge } from 'tailwind-merge';

// Others
import { EF_ENDPOINT } from '../../utils/ef';
import axios from '../../utils/axios';
import { toUpperCaseFirstLetter } from '../../utils/shared';
import { setIsLoading } from '../../utils/store/loadingReducer';

const MapTableComponent = () => {
  // Standard and Vars
  const [mapTableData, setMapTableData] = useState([]);
  const dispatch = useDispatch();

  // Table
  const bodyCellStyle = 'text-xs text-center whitespace-nowrap px-1';

  // Others
  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${EF_ENDPOINT}/summary/map`);
      if (response?.data?.data) {
        response.data.data = response.data.data.map((o) => ({
          ...o,
          total_available_power: o.total_available_power / 1000,
        }));
        setMapTableData(response.data.data);
      }
    } catch (error) {
      // console.log(error);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <table className="min-w-full">
      <thead>
        <tr>
          {['State', 'Available Rack', 'Available Power (kW)'].map((label, index) => (
            <td
              key={index}
              className="whitespace-nowrap bg-[#536eef] px-2 py-1 text-center text-sm text-white"
            >
              {label}
            </td>
          ))}
        </tr>
      </thead>
      <tbody>
        {mapTableData.map((row, i) => (
          <tr key={`${i}-${row?.state}`} className="hover:bg-gray-100">
            <td className={twMerge(bodyCellStyle, 'text-left')}>
              {toUpperCaseFirstLetter(row?.state)}
            </td>
            <td className={bodyCellStyle}>{row?.total_available_rack}</td>
            <td className={bodyCellStyle}>{row?.total_available_power}</td>
          </tr>
        ))}
      </tbody>
    </table>
  );
};

export default MapTableComponent;
