// Next, React, Tw
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { Edit } from '@mui/icons-material';

// Packages
import * as R from 'ramda';

// Components
import { TextInput, SelectInput } from '../Shared/CustomInput';

// Others
import { getStatusDisplay, EF_ENDPOINT } from '../../utils/ef';
import axios from '../../utils/axios';
import { useSnackbar } from '../Shared/snackbar';
import { setIsLoading } from '../../utils/store/loadingReducer';

const PortTableDetailsComponent = () => {
  // Standard and Vars
  const { enqueueSnackbar } = useSnackbar();
  const { query } = useRouter();
  const { patchPanelId } = query;
  const dispatch = useDispatch();

  const [ports, setPorts] = useState([]);

  // Table
  const [tableData, setTableData] = useState({});
  const bodyCellStyle = 'text-center whitespace-nowrap py-1 text-xs';

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const DIALOG_DATA = {
    port_description: null,
    port_name: null,
    port_status: null,
  };
  const [dialogData, setDialogData] = useState(DIALOG_DATA);
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    setDialogData(data);
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  const handleDialogClose = async (action) => {
    if (action) {
      dispatch(setIsLoading(true));
      try {
        if (Object.values(dialogData).includes(null)) {
          enqueueSnackbar('Please fill in all field.', {
            variant: 'error',
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'center',
            },
          });
          return;
        }
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${EF_ENDPOINT}/port/${patchPanelId}`, dialogData);
            break;
          case 'put':
            response = await axios.put(`${EF_ENDPOINT}/port/${dialogData.id}`, dialogData);
            break;
          case 'delete':
            response = await axios.delete(`${EF_ENDPOINT}/port/${dialogData.id}`);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch (error) {
        // console.log(error);
      }
      dispatch(setIsLoading(false));
    }

    setDialogData(DIALOG_DATA);
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${EF_ENDPOINT}/port/patch_panel_id/${patchPanelId}`);
      if (response?.data?.data) {
        setTableData(R.indexBy(R.prop('port_name'), response?.data?.data));
      }
    } catch (error) {
      // console.log(error);
      setTableData({});
    }
    try {
      const response = await axios.get(`${EF_ENDPOINT}/patch_panel/id/${patchPanelId}`);
      if (response?.data?.data) {
        const temp = [];

        for (let i = 1; i <= Number(response?.data?.data[0]?.total_port); i += 1) {
          temp.push(`Port #${i.toString().padStart(2, '0')}`);
        }
        setPorts(temp);
      }
    } catch (error) {
      // console.log(error);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [patchPanelId]);

  return (
    <>
      <div className="overflow-x-auto scrollbar">
        <table className="min-w-full">
          <thead>
            <tr>
              {['Name', 'Status', 'Description', 'Modified Date', ''].map((label, index) => (
                <td
                  key={index}
                  className="whitespace-nowrap bg-[#fcfcfd] px-4 text-center text-sm font-bold"
                >
                  {label}
                </td>
              ))}
            </tr>
          </thead>
          <tbody>
            {ports.map((row, i) => (
              <tr
                key={`${i}-${row?.id}`}
                className={`${`${
                  i % 2 === 0 ? 'bg-[#f8f8f8]' : 'bg-white'
                } text-xs hover:bg-gray-200`}`}
              >
                <td className={bodyCellStyle}>{row}</td>
                <td className={bodyCellStyle}>{getStatusDisplay(tableData[row]?.port_status)}</td>
                <td className={bodyCellStyle}>{tableData[row]?.port_description}</td>
                <td className={bodyCellStyle}>{tableData[row]?.modified_date}</td>
                <td className={bodyCellStyle}>
                  <div className="flex gap-2">
                    {['active', 'inactive', 'reserved'].includes(tableData[row]?.port_status) ? (
                      <button
                        type="button"
                        className="rounded-[4px] border border-[#ff7a03] p-1"
                        onClick={() =>
                          handleClickOpenDialog(true, {
                            id: tableData[row]?.id,
                            port_name: row,
                            port_status: tableData[row]?.port_status,
                            port_description: tableData[row]?.port_description,
                          })
                        }
                      >
                        <Edit sx={{ color: '#ff7a03' }} />
                      </button>
                    ) : (
                      <button
                        type="button"
                        className="rounded-[4px] border border-[#ff7a03] p-1"
                        onClick={() =>
                          handleClickOpenDialog(false, {
                            port_name: row,
                            port_status: tableData[row]?.port_status,
                            port_description: tableData[row]?.port_description,
                          })
                        }
                      >
                        <Edit sx={{ color: '#ff7a03' }} />
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle sx={{ backgroundColor: '#536eef', color: 'white' }}>
          <p className="text-lg font-bold">Port</p>
        </DialogTitle>
        <DialogContent>
          <div className="flex w-[400px] flex-col gap-2 p-2">
            <TextInput
              name="port_name"
              value={dialogData?.port_name}
              placeholder="Name"
              onChange={handleDialogDataChange}
              disabled
            />
            <TextInput
              name="port_description"
              value={dialogData?.port_description}
              placeholder="Description"
              onChange={handleDialogDataChange}
            />
            <SelectInput
              name="port_status"
              value={dialogData?.port_status}
              placeholder="Status"
              options={['inactive', 'active', 'reserved']}
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between">
            <div className="flex gap-2">
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('delete')}
                  className="bg-[#ff6d6d] p-2 text-white"
                >
                  Delete
                </button>
              )}
            </div>
            <div className="flex gap-2">
              <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
                Cancel
              </button>
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-[#1a1a1c] p-2 text-white"
                >
                  Save
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-[#1a1a1c] p-2 text-white"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default PortTableDetailsComponent;
