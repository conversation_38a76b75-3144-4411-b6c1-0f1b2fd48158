// Next, React, Tw
import * as R from 'ramda';
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { AddCircle, Edit, Visibility } from '@mui/icons-material';

// Components
import ExportExcelButton from '../Shared/ExportExcelButton';
import { TextInput, SelectInput, SearchInput } from '../Shared/CustomInput';

// Others
import { getStatusDisplay, EF_ENDPOINT } from '../../utils/ef';
import axios from '../../utils/axios';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { useSnackbar } from '../Shared/snackbar';
import { toUpperCaseFirstLetter } from '../../utils/shared';
import { setIsLoading } from '../../utils/store/loadingReducer';

const PatchPanelTableDetailsComponent = () => {
  // Standard and Vars
  const { enqueueSnackbar } = useSnackbar();
  const { setParam } = useParamContext();
  const router = useRouter();
  const { q, siteId } = router.query;
  const dispatch = useDispatch();

  // Table
  const [tableData, setTableData] = useState([]);
  const [tableData2, setTableData2] = useState([]);
  const bodyCellStyle = 'text-center whitespace-nowrap py-1 text-xs';
  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  })();
  const filteredTableData2 = (() => {
    if ([undefined, '']?.includes(q)) {
      return tableData2;
    }
    return tableData2.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  })();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const DIALOG_DATA = {
    patch_panel_status: 'active',
    patch_panel_type: null,
  };
  const [dialogData, setDialogData] = useState(DIALOG_DATA);
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  const handleDialogClose = async (action) => {
    let mmrRackId;
    dispatch(setIsLoading(true));
    try {
      let response = await axios.get(`${EF_ENDPOINT}/rack/site_id/${siteId}`);
      response.data.data = response.data.data.filter((o) => o?.rack_type === 'mmr');
      mmrRackId = response.data.data[0]?.id;
      if (mmrRackId === undefined) {
        response = await axios.post(`${EF_ENDPOINT}/rack/${siteId}`, {
          customer_name: `-`,
          rack_name: `-`,
          rack_status: 'active',
          rack_type: 'mmr',
          total_power: 0,
        });
        mmrRackId = response.data.data.split(' ')[2];
      }
    } catch (error) {
      // console.log(error);
    }
    dispatch(setIsLoading(false));

    if (action) {
      dispatch(setIsLoading(true));
      try {
        if (Object.values(dialogData).includes(null)) {
          enqueueSnackbar('Please fill in all field.', {
            variant: 'error',
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'center',
            },
          });
          return;
        }
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${EF_ENDPOINT}/patch_panel/${mmrRackId}`, {
              ...dialogData,
            });
            break;
          case 'put':
            response = await axios.put(`${EF_ENDPOINT}/patch_panel/${dialogData.id}`, dialogData);
            break;
          case 'delete':
            response = await axios.delete(`${EF_ENDPOINT}/patch_panel/${dialogData.id}`);
            response = await axios.delete(`${EF_ENDPOINT}/rack/${dialogData.rack_id}`);

            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch (error) {
        // console.log(error);
      }
      dispatch(setIsLoading(false));
    }
    setDialogData(DIALOG_DATA);
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const fetchData = async () => {
    let response = [];
    let response2 = [];
    dispatch(setIsLoading(true));
    try {
      response = await axios.get(`${EF_ENDPOINT}/patch_panel/site_id/${siteId}`);
      if (response?.data?.data) {
        response = response?.data?.data;
      }
    } catch (error) {
      // console.log(error);
    }
    try {
      response2 = await axios.get(`${EF_ENDPOINT}/rack/site_id/${siteId}`);
      if (response2?.data?.data) {
        response2 = response2.data.data.map((o) => ({ ...o, rack_id: o?.id }));
      }
    } catch (error) {
      // console.log(error);
    }

    response = response?.map((o) => ({ patch_panel_id: o?.id, ...o }));

    let temp = R.pipe(
      R.groupBy(R.prop('rack_id')),
      R.values,
      R.map(R.mergeAll)
    )([...response, ...response2]);

    response = response?.filter(
      (o) => o?.patch_panel_type === 'FTC' || o?.patch_panel_type === 'PCM'
    );

    temp = temp.filter((o) => o?.rack_type !== 'mmr' && o?.patch_panel_name !== undefined);
    setTableData(response);
    setTableData2(temp);
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [siteId]);

  return (
    <>
      <div className="flex flex-col justify-between gap-2 md:flex-row">
        <SearchInput />
        <div className="flex w-full items-center justify-end gap-4 md:w-auto">
          <button type="button" className="w-full" onClick={() => handleClickOpenDialog(false)}>
            <div className="flex gap-2 rounded-xl border border-[#ff9b42] p-2 text-[#ff9b42]">
              <AddCircle />
              <p>Add FTC / PCM</p>
            </div>
          </button>
          <ExportExcelButton
            data={[...filteredTableData, ...filteredTableData2]}
            filename="mmr-table-details.csv"
          />
        </div>
      </div>

      <div className="overflow-x-auto scrollbar">
        <table className="min-w-full">
          <thead>
            <tr>
              {['Patch Panel Name', 'Status', 'Patch Panel Type', ''].map((label, index) => (
                <td
                  key={index}
                  className="whitespace-nowrap bg-[#fcfcfd] py-1 text-center text-sm font-semibold"
                >
                  {label}
                </td>
              ))}
            </tr>
          </thead>
          <tbody>
            {filteredTableData.map((row, i) => (
              <tr key={i} className={`${`${i % 2 === 0 ? 'bg-[#f8f8f8]' : 'bg-white'}`}`}>
                <td className={bodyCellStyle}>{toUpperCaseFirstLetter(row.patch_panel_name)}</td>
                <td>{getStatusDisplay(row.patch_panel_status)}</td>
                <td className={bodyCellStyle}>{row.patch_panel_type.toUpperCase()}</td>
                <td className={bodyCellStyle}>
                  <div className="flex gap-2">
                    <button
                      type="button"
                      className="rounded-xl border border-[#ff7a03] p-1"
                      onClick={() =>
                        setParam({
                          rackId: row?.rack_id,
                          patchPanelId: row?.patch_panel_id,
                          component: 12,
                        })
                      }
                    >
                      <Visibility sx={{ color: '#ff7a03' }} />
                    </button>
                    <button
                      type="button"
                      className="rounded-xl border border-[#ff7a03] p-1"
                      onClick={() => handleClickOpenDialog(true, row)}
                    >
                      <Edit sx={{ color: '#ff7a03' }} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
            {filteredTableData2.map((row, i) => (
              <tr key={i} className={`${`${i % 2 === 0 ? 'bg-[#f8f8f8]' : 'bg-white'}`}`}>
                <td className={bodyCellStyle}>
                  {toUpperCaseFirstLetter(row?.rack_name)} ({row?.patch_panel_name})
                </td>
                <td>{getStatusDisplay(row?.rack_status)}</td>
                <td className={bodyCellStyle}>CUSTOMER</td>
                <td className={bodyCellStyle}>
                  <div className="flex gap-2">
                    <button
                      type="button"
                      className="rounded-xl border border-[#ff7a03] p-1"
                      onClick={() =>
                        setParam({
                          rackId: row?.rack_id,
                          patchPanelId: row?.patch_panel_id,
                          component: 2,
                        })
                      }
                    >
                      <Visibility sx={{ color: '#ff7a03' }} />
                    </button>
                    <button
                      type="button"
                      className="rounded-xl border border-gray-300 p-1"
                      disabled
                      onClick={() => handleClickOpenDialog(true, row)}
                    >
                      <Edit className="text-gray-300" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-ef text-white">
          <p className="text-lg font-bold">FTC / PCM</p>
        </DialogTitle>
        <DialogContent>
          <div className="flex w-[400px] flex-col gap-4 p-2">
            <TextInput
              name="patch_panel_name"
              value={dialogData?.patch_panel_name}
              placeholder="Panel Name"
              onChange={handleDialogDataChange}
            />
            <SelectInput
              name="patch_panel_type"
              value={dialogData?.patch_panel_type}
              placeholder="Panel Type"
              options={['FTC', 'PCM']}
              onChange={handleDialogDataChange}
            />
            <SelectInput
              name="total_port"
              value={dialogData?.total_port}
              placeholder="No. of Port"
              onChange={handleDialogDataChange}
              options={['12', '24', '48']}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between">
            <div className="flex gap-2">
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('delete')}
                  className="bg-[#ff6d6d] p-2 text-white"
                >
                  Delete
                </button>
              )}
            </div>
            <div className="flex gap-2">
              <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
                Cancel
              </button>
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-[#1a1a1c] p-2 text-white"
                >
                  Save
                </button>
              )}

              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-[#1a1a1c] p-2 text-white"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default PatchPanelTableDetailsComponent;
