// Next, React, Tw
import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';

// Mui
import { useMediaQuery } from '@mui/material';

// Packages
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popup } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Components
import MapTableComponent from './MapTable';

// Others
import { EF_ENDPOINT } from '../../utils/ef';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';

const customIcon = new L.Icon({
  iconUrl: '/assets/icons/map_marker.svg',
  iconSize: [40, 40],
});

const OpenStreetMap = () => {
  // Standard and Vars
  const router = useRouter();
  const isSmallScreen = useMediaQuery('(max-width: 768px)');
  const zoomLevel = `${isSmallScreen ? 4 : 6}`;
  const mapRef = useRef();
  const dispatch = useDispatch();

  const [mapData, setMapData] = useState([]);

  // Others
  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${EF_ENDPOINT}/site/all/keyword`);
      if (response?.data?.data) {
        const temp = response.data.data.map((o) => ({
          ...o,
          location: [Number(o?.latitude), Number(o?.longitude)],
        }));
        setMapData(temp);
      }
    } catch (error) {
      // console.log(error);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="container">
      <div className="row">
        <div className="col">
          <div className="container">
            <div className="relative h-[45vh] md:h-[75vh]">
              <MapContainer
                center={{ lat: 4.907274122834303, lng: 108.32882287648644 }}
                zoom={zoomLevel}
                scrollWheelZoom={false}
                zoomControl={false}
                ref={mapRef}
                style={{
                  height: '100%',
                  width: '100%',
                  borderRadius: '16px',
                  overflow: 'hidden',
                  zIndex: '10',
                }}
              >
                <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
                {mapData.map((edge, index) => (
                  <Marker
                    key={index}
                    position={edge.location}
                    icon={customIcon}
                    eventHandlers={{
                      mouseover: (event) => event.target.openPopup(),
                      mouseout: (event) => event.target.closePopup(),
                      click: (event) => {
                        router.push(`/edge-facility/sites/${edge.id}`);
                      },
                    }}
                  >
                    <Popup>
                      <p>{edge.site}</p>
                    </Popup>
                  </Marker>
                ))}
              </MapContainer>
              <div className="absolute bottom-0 right-0 z-50 m-4 hidden overflow-x-auto bg-white scrollbar md:block">
                <MapTableComponent />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OpenStreetMap;
