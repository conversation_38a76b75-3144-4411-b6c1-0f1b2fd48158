// Next, React, Tw
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { AddCircle, Edit, Visibility } from '@mui/icons-material';

// Components
import ExportExcelButton from '../Shared/ExportExcelButton';
import { TextInput, SelectInput, SearchInput } from '../Shared/CustomInput';

// Others
import { getStatusDisplay, EF_ENDPOINT } from '../../utils/ef';
import axios from '../../utils/axios';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { TablePaginationCustom } from '../Shared/table';
import { useSnackbar } from '../Shared/snackbar';
import { toUpperCaseFirstLetter } from '../../utils/shared';
import { setIsLoading } from '../../utils/store/loadingReducer';

const PatchPanelTableDetailsComponent = () => {
  // Standard
  const { enqueueSnackbar } = useSnackbar();
  const { setParam } = useParamContext();
  const router = useRouter();
  const { q, rackId } = router.query;
  const dispatch = useDispatch();

  // Table
  const [tableData, setTableData] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(10);
  const bodyCellStyle = 'text-center whitespace-nowrap py-1 text-xs';
  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  })();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const DIALOG_DATA = {
    patch_panel_name: null,
    patch_panel_status: 'active',
    patch_panel_type: null,
  };
  const [dialogData, setDialogData] = useState(DIALOG_DATA);
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  const handleDialogClose = async (action) => {
    if (action) {
      try {
        if (Object.values(dialogData).includes(null)) {
          enqueueSnackbar('Please fill in all field.', {
            variant: 'error',
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'center',
            },
          });
          return;
        }
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${EF_ENDPOINT}/patch_panel/${rackId}`, dialogData);
            break;
          case 'put':
            response = await axios.put(`${EF_ENDPOINT}/patch_panel/${dialogData.id}`, dialogData);
            break;
          case 'delete':
            response = await axios.delete(`${EF_ENDPOINT}/patch_panel/${dialogData.id}`);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch (error) {
        // console.log(error);
      }
    }
    setDialogData(DIALOG_DATA);
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    let response = [];
    try {
      response = await axios.get(`${EF_ENDPOINT}/patch_panel/rack_id/${rackId}`);
      if (response?.data?.data) {
        response = response?.data?.data;
        response = response?.map((o) => ({ ...o, total_port: Number(o?.total_port) }));
      }
    } catch (error) {
      // console.log(error);
    }
    let response2;
    for (let i = 0; i < response?.length; i += 1) {
      try {
        // eslint-disable-next-line
        response2 = await axios.get(`${EF_ENDPOINT}/port/patch_panel_id/${response[i]?.id}`);
        if (response2.data.status === 'success' && response2?.data?.data !== null) {
          response[i].total_active_port = response2?.data?.data?.filter(
            (o) => o?.port_status === 'active' || o?.port_status === 'reserved'
          )?.length;
          response[i].total_available_port = response[i].total_port - response[i].total_active_port;
        }
      } catch (error) {
        response[i].total_active_port = 0;
        response[i].total_available_port = response[i].total_port;
        // console.log(error);
      }
    }
    setTableData(response);
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [rackId]);

  return (
    <>
      <div className="flex justify-between">
        <SearchInput />
        <div className="flex gap-2">
          <button type="button" onClick={() => handleClickOpenDialog(false)}>
            <div className="flex gap-2 rounded-[4px] border border-[#0c7314] bg-green-600 p-2 text-xs text-white">
              <AddCircle className="h-4 w-4" />
              <p>Add New Patch Panel</p>
            </div>
          </button>
          <ExportExcelButton data={filteredTableData} filename="patch-panel-table-details.csv" />
        </div>
      </div>

      <div className="overflow-x-auto scrollbar">
        <table className="min-w-full">
          <thead>
            <tr>
              {['Name', 'Status', 'Panel Type', 'Total Port', 'Available Port', ''].map(
                (label, index) => (
                  <td
                    key={index}
                    className="whitespace-nowrap bg-[#fcfcfd] py-1 text-center text-sm font-bold"
                  >
                    {label}
                  </td>
                )
              )}
            </tr>
          </thead>
          <tbody>
            {filteredTableData
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((row, i) => (
                <tr
                  key={`${i}-${row?.id}`}
                  className={`${`${
                    i % 2 === 0 ? 'bg-[#f8f8f8]' : 'bg-white'
                  } text-xs hover:bg-gray-200`}`}
                >
                  <td className={bodyCellStyle}>{toUpperCaseFirstLetter(row?.patch_panel_name)}</td>
                  <td className={bodyCellStyle}>{getStatusDisplay(row?.patch_panel_status)}</td>
                  <td className={bodyCellStyle}>{row?.patch_panel_type.toUpperCase()}</td>
                  <td className={bodyCellStyle}>{row?.total_port}</td>
                  <td className={bodyCellStyle}>{row.total_available_port}</td>
                  <td className={bodyCellStyle}>
                    <div className="flex gap-1">
                      <button
                        type="button"
                        className="rounded-[4px] border border-[#ff7a03] p-1"
                        onClick={() => setParam({ patchPanelId: row?.id, component: 3 })}
                      >
                        <Visibility sx={{ color: '#ff7a03' }} />
                      </button>
                      <button
                        type="button"
                        className="rounded-[4px] border border-[#ff7a03] p-1"
                        onClick={() => handleClickOpenDialog(true, row)}
                      >
                        <Edit sx={{ color: '#ff7a03' }} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
          </tbody>
        </table>
      </div>
      <TablePaginationCustom
        count={tableData.length}
        page={page}
        rowsPerPage={rowsPerPage}
        onPageChange={(e, nextPage) => setPage(nextPage)}
      />

      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle sx={{ backgroundColor: '#536eef', color: 'white' }}>
          <p className="text-lg font-bold">Patch Panel</p>
        </DialogTitle>
        <DialogContent>
          <div className="flex w-[400px] flex-col gap-4 p-2">
            <TextInput
              name="patch_panel_name"
              value={dialogData?.patch_panel_name}
              placeholder="Panel Name"
              onChange={handleDialogDataChange}
            />
            <SelectInput
              name="patch_panel_type"
              value={dialogData?.patch_panel_type}
              placeholder="Panel Type"
              options={['LC', 'FC', 'SC']}
              onChange={handleDialogDataChange}
            />
            <SelectInput
              name="total_port"
              value={dialogData?.total_port}
              placeholder="No. of Port"
              options={['12', '24', '48']}
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between">
            <div className="flex gap-2">
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('delete')}
                  className="bg-[#ff6d6d] p-2 text-white"
                >
                  Delete
                </button>
              )}
            </div>
            <div className="flex gap-2">
              <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
                Cancel
              </button>
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-[#1a1a1c] p-2 text-white"
                >
                  Save
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-[#1a1a1c] p-2 text-white"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default PatchPanelTableDetailsComponent;
