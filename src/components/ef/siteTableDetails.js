// Next, React, Tw
import { useRouter } from 'next/router';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Components
import ExportExcelButton from '../Shared/ExportExcelButton';
import { SearchInput } from '../Shared/CustomInput';

// Others
import { getStatusDisplay, EF_ENDPOINT } from '../../utils/ef';
import axios from '../../utils/axios';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { setIsLoading } from '../../utils/store/loadingReducer';

const SiteTableDetailsComponent = () => {
  // Standard
  const router = useRouter();
  const { q, siteId } = router.query;
  const { setParam } = useParamContext();
  const { isAdmin } = useModuleRoleContext();
  const dispatch = useDispatch();

  // Table
  const [tableData, setTableData] = useState([]);
  const bodyCellStyle = 'text-center whitespace-nowrap py-1 text-xs';
  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  })();

  const getTableHeader = () => {
    const temp = ['Rack Name', 'Rack Status', 'Rack Type'];
    if (isAdmin) {
      temp?.push('Customer');
    }
    temp?.push('Total Power');
    return temp;
  };

  // Others
  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${EF_ENDPOINT}/rack/site_id/${siteId}`);
      if (response?.data?.data) {
        response.data.data = response?.data?.data?.map((o) => ({
          ...o,
          total_power: o.total_power / 1000,
        }));
        setTableData(response.data.data);
      }
    } catch (error) {
      // console.log(error);
      setTableData({});
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [siteId]);

  return (
    <>
      <div className="flex justify-between">
        <SearchInput />
        <ExportExcelButton data={filteredTableData} filename="site-details.csv" />
      </div>
      <div className="overflow-x-auto scrollbar">
        <table className="min-w-full">
          <thead>
            <tr>
              {getTableHeader().map((label, index) => (
                <td
                  key={index}
                  className="whitespace-nowrap bg-[#fcfcfd] px-4 text-center text-sm font-bold"
                >
                  {label}
                </td>
              ))}
            </tr>
          </thead>
          <tbody>
            {filteredTableData.map((row, i) => (
              <tr
                key={`${i}-${row?.id}`}
                className={`${`${
                  i % 2 === 0 ? 'bg-[#f8f8f8]' : 'bg-white'
                } text-xs hover:bg-gray-200`}`}
              >
                <td>
                  <button
                    type="button"
                    onClick={() => setParam({ rackId: row?.id, component: 1 })}
                    className="flex w-full justify-center font-bold"
                  >
                    <p className="text-center text-xs text-[#5da9ef] underline">{row?.rack_name}</p>
                  </button>
                </td>
                <td>{getStatusDisplay(row?.rack_status)}</td>
                <td className={bodyCellStyle}>{row?.rack_type?.toUpperCase()}</td>
                {isAdmin && <td className={bodyCellStyle}>{row?.customer_name}</td>}

                <td className={bodyCellStyle}>{row.total_power}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </>
  );
};

export default SiteTableDetailsComponent;
