// Next, React, Tw
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { Edit, Visibility } from '@mui/icons-material';

// Components
import { TablePaginationCustom } from '../Shared/table';
import ExportExcelButton from '../Shared/ExportExcelButton';
import { TextInput, SelectInput, SearchInput } from '../Shared/CustomInput';

// Others
import { getStatusDisplay, EF_ENDPOINT } from '../../utils/ef';
import axios from '../../utils/axios';
import { useSnackbar } from '../Shared/snackbar';
import { toUpperCaseFirstLetter } from '../../utils/shared';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { setIsLoading } from '../../utils/store/loadingReducer';

const RackTableDetailsComponent = () => {
  // Standard
  const { enqueueSnackbar } = useSnackbar();
  const { query } = useRouter();
  const { setParam } = useParamContext();
  const { isAdmin } = useModuleRoleContext();
  const dispatch = useDispatch();
  const { q, siteId, rackId } = query;

  // Table
  const [tableData, setTableData] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(10);

  const tableHeader = (() => {
    let temp = ['Rack Type'];
    if (isAdmin) {
      temp?.push('Customer Name');
    }
    temp = [...temp, ...['Rack Status', 'Rack Size', 'Total Power (kW)', '']];
    return temp;
  })();
  const bodyCellStyle = 'text-center whitespace-nowrap py-1 text-xs';
  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  })();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const DIALOG_DATA = {
    customer_name: null,
    rack_size: null,
    rack_status: 'active',
    rack_type: 'customer',
    total_power: null,
  };
  const [dialogData, setDialogData] = useState(DIALOG_DATA);
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  const handleDialogClose = async (action) => {
    let response;
    if (action) {
      dispatch(setIsLoading(true));
      try {
        if (Object.values(dialogData).includes(null)) {
          enqueueSnackbar('Please fill in all field.', {
            variant: 'error',
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'center',
            },
          });
          return;
        }
        dialogData.total_power = Number(dialogData.total_power);
        switch (action) {
          case 'post':
            response = await axios.post(`${EF_ENDPOINT}/rack/${siteId}`, dialogData);
            break;
          case 'put':
            response = await axios.put(`${EF_ENDPOINT}/rack/${dialogData.id}`, dialogData);
            break;
          case 'delete':
            response = await axios.delete(`${EF_ENDPOINT}/rack/${dialogData.id}`);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch (error) {
        // console.log(error);
      }
      dispatch(setIsLoading(false));
    }

    setDialogData(DIALOG_DATA);
    setDialogOpen(false);
    fetchData(response?.data?.data.split(' ')[2]);
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${EF_ENDPOINT}/rack/id/${rackId}`);
      if (response?.data?.data) {
        setTableData(response.data.data);
      }
    } catch (error) {
      // console.log(error);
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [rackId]);

  return (
    <>
      <div className="flex justify-between">
        <SearchInput />
        <ExportExcelButton data={filteredTableData} filename="rack-table-details.csv" />
      </div>

      <div className="overflow-x-auto scrollbar">
        <table className="min-w-full">
          <thead>
            <tr>
              {tableHeader.map((label, index) => (
                <td
                  key={index}
                  className="whitespace-nowrap bg-[#fcfcfd] px-4 text-center text-sm font-bold "
                >
                  {label}
                </td>
              ))}
            </tr>
          </thead>
          <tbody>
            {filteredTableData
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((row, i) => (
                <tr
                  key={`${i}-${row?.id}`}
                  className={`${`${
                    i % 2 === 0 ? 'bg-[#f8f8f8]' : 'bg-white'
                  } text-xs hover:bg-gray-200`}`}
                >
                  <td className={bodyCellStyle}>{row?.rack_type.toUpperCase()}</td>
                  {isAdmin && (
                    <td className={bodyCellStyle}>{toUpperCaseFirstLetter(row?.customer_name)}</td>
                  )}
                  <td className={bodyCellStyle}>{getStatusDisplay(row?.rack_status)}</td>
                  <td className={bodyCellStyle}>{row?.rack_size}</td>
                  <td className={bodyCellStyle}>{row.total_power / 1000}</td>
                  <td className={bodyCellStyle}>
                    <div className="flex gap-1">
                      <button
                        type="button"
                        className="rounded-[4px] border border-[#ff7a03] p-1"
                        onClick={() => setParam({ rackId: row?.id, component: 2 })}
                      >
                        <Visibility sx={{ color: '#ff7a03' }} />
                      </button>
                      <button
                        type="button"
                        className="rounded-[4px] border border-[#ff7a03] p-1"
                        onClick={() => handleClickOpenDialog(true, row)}
                      >
                        <Edit sx={{ color: '#ff7a03' }} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
          </tbody>
        </table>
      </div>
      <TablePaginationCustom
        count={tableData.length}
        page={page}
        rowsPerPage={rowsPerPage}
        onPageChange={(e, nextPage) => setPage(nextPage)}
      />

      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle sx={{ backgroundColor: '#536eef', color: 'white' }}>
          <p className="text-lg font-bold">Customer</p>
        </DialogTitle>
        <DialogContent>
          <div className="flex w-[400px] flex-col gap-1 p-2">
            <TextInput
              name="customer_name"
              value={dialogData?.customer_name}
              placeholder="Customer Name"
              onChange={handleDialogDataChange}
            />
            <SelectInput
              type="number"
              name="total_power"
              value={dialogData?.total_power}
              placeholder="Total power (kW)"
              onChange={handleDialogDataChange}
              options={(() => {
                let temp = [
                  {
                    value: 0,
                    label: '0 kW',
                  },
                  {
                    value: 3000,
                    label: '3 kW',
                  },
                  {
                    value: 5000,
                    label: '5 kW',
                  },
                ];
                if (
                  (dialogData?.rack_name?.includes('KG') ||
                    dialogData?.rack_name?.includes('KBU')) &&
                  (dialogData?.rack_name?.includes('07') || dialogData?.rack_name?.includes('08'))
                ) {
                  temp = [
                    ...temp,

                    {
                      value: 8000,
                      label: '8 kW',
                    },
                    {
                      value: 10000,
                      label: '10 kW',
                    },
                    {
                      value: 12000,
                      label: '12 kW',
                    },
                  ];
                }
                if (
                  !(
                    dialogData?.rack_name?.includes('KG') || dialogData?.rack_name?.includes('KBU')
                  ) &&
                  dialogData?.rack_name?.includes('08')
                ) {
                  temp = [
                    ...temp,
                    {
                      value: 8000,
                      label: '8 kW',
                    },
                    {
                      value: 10000,
                      label: '10 kW',
                    },
                    {
                      value: 12000,
                      label: '12 kW',
                    },
                  ];
                }
                return temp;
              })()}
            />
            <SelectInput
              name="rack_status"
              value={dialogData?.rack_status}
              placeholder="Status"
              options={['active', 'inactive', 'reserved']}
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-end">
            <div className="flex gap-2">
              <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
                Cancel
              </button>
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-[#1a1a1c] p-2 text-white"
                >
                  Save
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-[#1a1a1c] p-2 text-white"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default RackTableDetailsComponent;
