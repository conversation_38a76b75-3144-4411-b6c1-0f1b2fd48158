// Next, React, Tw
import { useState } from 'react';

// Packages
import * as yup from 'yup';

// Components
import { useSnackbar } from '../Shared/snackbar';
import { TextInput } from '../Shared/CustomInput';

const FutureSalary = () => {
  // Standard
  const { enqueueSnackbar } = useSnackbar();

  // Form
  const MESSAGE = 'Please provide';
  const schema = yup.object({
    monthlySalary: yup
      .number()
      .required(`${MESSAGE} monthly salary.`)
      .min(1500, 'Salary must be greater than or equal to 1500')
      .max(50000, 'Salary must be less than or equal to 50000')
      .transform((value) => Number(value)),
    annualIncrementInPercentage: yup
      .number()
      .required(`${MESSAGE} annual increment.`)
      .min(0, 'Annual Increment must be greater than or equal to 0')
      .max(100, 'Annual Increment must be less than or equal to 100')
      .transform((value) => Number(value)),
    numberOfYears: yup
      .number()
      .required(`${MESSAGE} number of years.`)
      .min(0, 'Number of years must be greater than or equal to 0')
      .max(100, 'Number of years must be less than or equal to 100')
      .transform((value) => Number(value)),
  });
  const [formData, setFormData] = useState({});
  const handleFormDataChange = (event) => {
    const { name, value } = event.target;
    setFormData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleFormDataSubmit = async () => {
    let payload;
    try {
      payload = await schema.validate(formData, { abortEarly: false });
    } catch (error) {
      enqueueSnackbar(error.errors, {
        variant: 'error',
      });
      return;
    }
    let temp = payload.monthlySalary;
    for (let i = 0; i < payload.numberOfYears; i += 1) {
      // eslint-disable-next-line
      temp = temp + (temp * payload?.annualIncrementInPercentage) / 100;
    }
    setFormData((prevValues) => ({
      ...prevValues,
      futureSalary: temp?.toFixed(2),
    }));
  };
  return (
    <form
      className="mx-auto flex w-1/2 flex-col gap-2"
      onSubmit={(event) => {
        event.preventDefault();
        handleFormDataSubmit();
      }}
    >
      <p className="mb-4 text-center text-3xl font-bold">Future Salary Calculator</p>
      <TextInput
        type="number"
        name="monthlySalary"
        placeholder="Monthly Salary (RM)"
        value={formData?.monthlySalary}
        onChange={handleFormDataChange}
      />
      <TextInput
        type="number"
        name="annualIncrementInPercentage"
        placeholder="Annual Increment (%)"
        value={formData?.annualIncrementInPercentage}
        onChange={handleFormDataChange}
      />
      <TextInput
        type="number"
        name="numberOfYears"
        placeholder="Number of Years"
        value={formData?.numberOfYears}
        onChange={handleFormDataChange}
      />
      <div className="flex w-full justify-end">
        <button type="submit" className="rounded-lg bg-green-500 px-2 py-1 text-white">
          Submit
        </button>
      </div>
      {formData?.futureSalary && (
        <p className="text-center font-bold">
          Your salary in <strong>{formData?.numberOfYears}</strong> years is{' '}
          <strong>RM {formData?.futureSalary}</strong> / month
        </p>
      )}
    </form>
  );
};

export default FutureSalary;
