import React from 'react';
import { twMerge } from 'tailwind-merge';

export const Card = ({ className, children, ...props }) => {
  return (
    <div
      className={twMerge(
        'overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

export const CardHeader = ({ className, children, ...props }) => {
  return (
    <div
      className={twMerge('border-b border-gray-200 px-4 py-3 dark:border-gray-700', className)}
      {...props}
    >
      {children}
    </div>
  );
};

export const CardTitle = ({ className, children, ...props }) => {
  return (
    <h3
      className={twMerge('text-lg font-medium text-gray-800 dark:text-gray-200', className)}
      {...props}
    >
      {children}
    </h3>
  );
};

export const CardContent = ({ className, children, ...props }) => {
  return (
    <div className={twMerge('p-4', className)} {...props}>
      {children}
    </div>
  );
};
