// React
import React from 'react';

// Material UI
import { Box, Typography } from '@mui/material';
import { Inbox } from '@mui/icons-material';

export const EmptyState = ({ 
  title = "No data found", 
  description = "There's nothing to show here yet", 
  icon: Icon = Inbox,
  action = null 
}) => {
  return (
    <Box className="flex flex-col items-center justify-center py-12 text-center">
      <Icon className="w-16 h-16 text-gray-400 mb-4" />
      <Typography variant="h6" className="text-gray-900 dark:text-white mb-2">
        {title}
      </Typography>
      <Typography variant="body2" className="text-gray-600 dark:text-gray-400 mb-6 max-w-md">
        {description}
      </Typography>
      {action && (
        <Box>
          {action}
        </Box>
      )}
    </Box>
  );
};