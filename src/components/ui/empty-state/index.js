import React from 'react';

export const EmptyState = ({ icon, title, description, children, className = '' }) => {
  return (
    <div
      className={`flex flex-col items-center justify-center px-4 py-8 text-center sm:py-16 ${className}`}
    >
      {icon && <div className="mb-3 text-gray-300 dark:text-gray-600">{icon}</div>}
      {!icon && (
        <div className="mb-3 text-gray-300 dark:text-gray-600">
          <svg
            className="mx-auto h-12 w-12 sm:h-16 sm:w-16"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="1.5"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            ></path>
          </svg>
        </div>
      )}
      {title && <p className="text-base font-medium text-gray-700 dark:text-gray-300">{title}</p>}
      {description && (
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">{description}</p>
      )}
      {children && <div className="mt-4">{children}</div>}
    </div>
  );
};
