/* eslint-disable import/no-extraneous-dependencies */
import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import dynamic from 'next/dynamic';

// Use dynamic import for ApexCharts to avoid SSR issues
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false });

const TimelineChart = ({ project }) => {
  // Standard milestone order
  const milestones = [
    { key: 'ocm', label: 'OCM' },
    { key: 'pcm_concept', label: 'PCM Concept' },
    { key: 'poc', label: 'POC' },
    { key: 'be', label: 'BE' },
    { key: 'ia', label: 'IA' },
    { key: 'pcm_biz', label: 'PCM Biz Case' },
    { key: 'dev', label: 'DEV (System)' },
    { key: 'proc', label: 'PROC' },
    { key: 'launch_checkpoint', label: 'Launch Checkpoint' },
    { key: 'launch_rfs', label: 'Launch RFS' },
    { key: 'pir', label: 'PIR' },
  ];

  // Get project year from first available date
  const getProjectYear = () => {
    const foundDate = milestones
      .map((milestone) => project[milestone.key]?.planDate)
      .find((date) => date && date !== 'Date?' && date !== '-');

    if (foundDate) {
      const parts = foundDate.split('-');
      if (parts.length === 3) {
        const year = parts[2];
        return year.length === 2 ? `20${year}` : year;
      }
    }
    return new Date().getFullYear().toString();
  };

  // Convert month abbreviation to number (0-11)
  const getMonthNumber = (month) => {
    const months = {
      Jan: 0,
      Feb: 1,
      Mar: 2,
      Apr: 3,
      May: 4,
      Jun: 5,
      Jul: 6,
      Aug: 7,
      Sep: 8,
      Oct: 9,
      Nov: 10,
      Dec: 11,
    };
    return months[month] || 0;
  };

  // Parse date string in format DD-MMM-YY (e.g., "01-Jan-23")
  const parseDate = (dateStr) => {
    if (!dateStr || dateStr === 'Date?' || dateStr === '-') return null;
    
    try {
      const parts = dateStr.split('-');
      if (parts.length !== 3) {
        console.warn('Invalid date format:', dateStr);
        return null;
      }
      
      const day = parseInt(parts[0], 10);
      const month = getMonthNumber(parts[1]);
      const yearStr = parts[2];
      const year = yearStr.length === 2 ? parseInt(`20${yearStr}`, 10) : parseInt(yearStr, 10);
      
      if (isNaN(day) || isNaN(year)) {
        console.warn('Invalid day or year:', dateStr);
        return null;
      }
      
      const date = new Date(year, month, day);
      return date.getTime();
    } catch (error) {
      console.warn('Error parsing date:', dateStr, error);
      return null;
    }
  };

  // For debugging
  useEffect(() => {
    // Test parsing the startDate
    if (project.startDate) {
      console.log('Project Start Date:', project.startDate);
      console.log('Parsed as:', parseDate(project.startDate) ? new Date(parseDate(project.startDate)).toISOString() : null);
    }
    
    // Test parsing some milestone dates
    if (project.ocm?.startPlanDate) {
      console.log('OCM Start Plan Date:', project.ocm.startPlanDate);
      console.log('Parsed as:', parseDate(project.ocm.startPlanDate) ? new Date(parseDate(project.ocm.startPlanDate)).toISOString() : null);
    }
    
    if (project.ocm?.planDate) {
      console.log('OCM Plan Date:', project.ocm.planDate);
      console.log('Parsed as:', parseDate(project.ocm.planDate) ? new Date(parseDate(project.ocm.planDate)).toISOString() : null);
    }
  }, [project]);

  const projectYear = getProjectYear();

  // Always include all milestones in the same order
  const planDates = milestones.map((m) => {
    // Use startPlanDate if available, otherwise fallback to older structure
    const startDate = parseDate(project[m.key]?.startPlanDate);
    const endDate = parseDate(project[m.key]?.planDate);
    
    // If we have both start and end dates, create a range bar
    if (startDate && endDate) {
      return {
        x: m.label,
        y: [startDate, endDate],
      };
    }
    
    // Fallback to single point if only one date is available
    if (endDate) {
      return {
        x: m.label,
        y: [endDate, endDate],
      };
    }
    
    return {
      x: m.label,
      y: [null, null],
    };
  });

  const actualDates = milestones.map((m) => {
    // Use startActualDate if available, otherwise fallback to older structure
    const startDate = parseDate(project[m.key]?.startActualDate);
    const endDate = parseDate(project[m.key]?.actualDate);
    
    // If we have both start and end dates, create a range bar
    if (startDate && endDate) {
      return {
        x: m.label,
        y: [startDate, endDate],
      };
    }
    
    // Fallback to single point if only one date is available
    if (endDate) {
      return {
        x: m.label,
        y: [endDate, endDate],
      };
    }
    
    return {
      x: m.label,
      y: [null, null],
    };
  });

  const options = {
    chart: {
      height: 320,
      type: 'rangeBar',
      fontFamily: 'inherit',
      toolbar: {
        show: false,
      },
      animations: {
        speed: 500,
      },
    },
    plotOptions: {
      bar: {
        horizontal: true,
        barHeight: '55%',
        rangeBarOverlap: true,
        borderRadius: 2,
      },
    },
    xaxis: {
      type: 'datetime',
      min: new Date(`${projectYear}-01-01`).getTime(),
      max: new Date(`${projectYear}-12-31`).getTime(),
      labels: {
        format: 'MMM',
        style: {
          colors: '#64748b',
          fontSize: '12px',
        },
      },
      axisBorder: {
        show: true,
        color: '#e2e8f0',
      },
      tickAmount: 12,
      axisTicks: {
        show: true,
        height: 4,
      },
    },
    yaxis: {
      labels: {
        style: {
          colors: '#475569',
          fontSize: '12px',
          fontWeight: 500,
        },
      },
      axisBorder: {
        show: true,
        color: '#e2e8f0',
      },
      reversed: true,
      categories: milestones.map((m) => m.label),
    },
    colors: ['#94a3b8', '#10b981'],
    legend: {
      position: 'top',
      horizontalAlign: 'left',
      markers: {
        radius: 2,
      },
      itemMargin: {
        horizontal: 8,
      },
    },
    tooltip: {
      shared: false,
      theme: 'light',
      style: {
        fontSize: '12px',
      },
      custom({ seriesIndex, dataPointIndex, w }) {
        const data = w.globals.initialSeries[seriesIndex].data[dataPointIndex];
        
        // Check if the data point is valid
        if (!data || !data.y || !data.y[0] || !data.y[1]) {
          return `<div class="p-2">
            <div class="font-medium">${data?.x || 'Unknown'}</div>
            <div class="mt-1">No date available</div>
          </div>`;
        }
        
        const startDate = new Date(data.y[0]).toLocaleDateString('en-GB', {
          day: '2-digit',
          month: 'short',
          year: '2-digit',
        });
        
        const endDate = new Date(data.y[1]).toLocaleDateString('en-GB', {
          day: '2-digit',
          month: 'short',
          year: '2-digit',
        });
        
        // Show proper range or single date
        const dateDisplay = data.y[0] === data.y[1] 
          ? `<div class="mt-1">${seriesIndex === 0 ? 'Planned' : 'Actual'}: ${endDate}</div>` 
          : `<div class="mt-1">${seriesIndex === 0 ? 'Planned' : 'Actual'}: ${startDate} to ${endDate}</div>`;
          
        return `<div class="p-2">
          <div class="font-medium">${data.x}</div>
          ${dateDisplay}
        </div>`;
      },
    },
    grid: {
      borderColor: '#e2e8f0',
      padding: {
        right: 16,
      },
      xaxis: {
        lines: {
          show: true,
        },
      },
    },
  };

  const series = [
    {
      name: 'Planned',
      data: planDates,
    },
    {
      name: 'Actual',
      data: actualDates,
    },
  ];

  return (
    <div className="w-full">
      <h3 className="mb-4 text-base font-medium text-gray-900">Project Timeline</h3>
      <div className="overflow-hidden">
        <Chart options={options} series={series} type="rangeBar" height={320} />
      </div>
    </div>
  );
};

TimelineChart.propTypes = {
  project: PropTypes.shape({}).isRequired,
};

export default TimelineChart;
