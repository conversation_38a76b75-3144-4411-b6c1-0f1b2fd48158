import React from 'react';
import PropTypes from 'prop-types';
import ProjectInfoCard from './ProjectInfoCard';
import Timeline<PERSON>hart from './TimelineChart';

const ProjectCard = ({ project }) => {
  const headerClasses =
    'whitespace-nowrap border border-gray-200 bg-gray-100 px-1.5 py-1 text-center font-medium text-gray-700 text-xs';
  const subheaderClasses =
    'whitespace-nowrap border border-gray-200 bg-gray-100 px-1.5 py-1 text-center text-xs font-medium text-gray-700';
  const cellClasses = 'whitespace-nowrap border border-gray-200 px-1.5 py-1 text-xs text-gray-900';
  const centerCellClasses = `${cellClasses} text-center`;

  // Display the date with appropriate background color based on status
  const getBackgroundColor = (date) => {
    if (!date || date === '-') return '';
    if (date === 'Date?') return 'bg-yellow-50';
    return 'bg-green-50';
  };
  
  // Debug data for the first project
  React.useEffect(() => {
    console.log('Project data:', project.projectName);
    if (project.ocm) {
      console.log('OCM data:', { 
        startPlanDate: project.ocm.startPlanDate,
        planDate: project.ocm.planDate,
        startActualDate: project.ocm.startActualDate,
        actualDate: project.ocm.actualDate
      });
    }
  }, [project]);

  return (
    <div className="mb-8 overflow-hidden rounded-lg border border-gray-200 bg-white shadow">
      <div className="space-y-6 p-6">
        {/* Top Section */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-5">
          {/* Header Section */}
          <div className="rounded-lg bg-gray-50 p-4 lg:col-span-2">
            <ProjectInfoCard project={project} />
          </div>

          {/* Chart Section */}
          <div className="rounded-lg bg-white p-4 lg:col-span-3">
            <TimelineChart project={project} />
          </div>
        </div>

        {/* Table Section */}
        <div className="overflow-hidden rounded-lg border">
          <div className="overflow-x-auto">
            <table className="w-full table-fixed">
              <thead>
                <tr className="bg-gray-50">
                  <th className={headerClasses} rowSpan="2" style={{ width: '40px' }}>
                    NO
                  </th>
                  <th className={headerClasses} rowSpan="2" style={{ width: '60px' }}>
                    ITEM
                  </th>
                  {[
                    'OCM',
                    'PCM (Concept)',
                    'POC',
                    'BE',
                    'IA (System, Financial)',
                    'PCM (Biz)',
                    'DEV (System)',
                    'PROC',
                    'LAUNCH CHECKPOINT',
                    'LAUNCH RFS',
                    'PIR',
                  ].map((header) => (
                    <th
                      key={header}
                      colSpan="3"
                      className={headerClasses}
                      style={{ width: '190px' }}
                    >
                      {header}
                    </th>
                  ))}
                  <th className={headerClasses} rowSpan="2" style={{ width: '60px' }}>
                    TOTAL
                    <br />
                    100%
                  </th>
                  <th className={headerClasses} rowSpan="2" style={{ width: '85px' }}>
                    Achievement on
                    <br />
                    Progress
                    <br />
                    Completion
                  </th>
                  <th className={headerClasses} rowSpan="2" style={{ width: '70px' }}>
                    Plan RFS
                  </th>
                  <th className={headerClasses} rowSpan="2" style={{ width: '70px' }}>
                    Revised
                    <br />
                    RFS
                  </th>
                  <th className={headerClasses} rowSpan="2" style={{ width: '70px' }}>
                    Actual RFS
                  </th>
                  <th colSpan="2" className={headerClasses} style={{ width: '120px' }}>
                    Delay Month
                  </th>
                  <th colSpan="2" className={headerClasses} style={{ width: '120px' }}>
                    Achievement on
                    <br />
                    Timely Completion
                  </th>
                  <th className={headerClasses} rowSpan="2" style={{ width: '150px' }}>
                    Highlight/Challenges
                  </th>
                  <th className={headerClasses} rowSpan="2" style={{ width: '150px' }}>
                    Support Required
                  </th>
                </tr>
                <tr className="bg-gray-50">
                  {[...Array(11)].map((_, i) => (
                    <React.Fragment key={i}>
                      <th className={subheaderClasses} style={{ width: '70px' }}>
                        START
                      </th>
                      <th className={subheaderClasses} style={{ width: '70px' }}>
                        END
                      </th>
                      <th className={subheaderClasses} style={{ width: '50px' }}>
                        %
                      </th>
                    </React.Fragment>
                  ))}
                  <th className={subheaderClasses}>
                    Actual vs.
                    <br />
                    Plan
                  </th>
                  <th className={subheaderClasses}>
                    Actual vs.
                    <br />
                    Revised
                  </th>
                  <th className={subheaderClasses}>
                    Actual vs.
                    <br />
                    Plan
                  </th>
                  <th className={subheaderClasses}>
                    Actual vs.
                    <br />
                    Revised
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {/* Plan Row */}
                <tr>
                  <td rowSpan="2" className={centerCellClasses}>
                    {project.id}
                  </td>
                  <td className={`${cellClasses} font-medium text-gray-700`}>Plan</td>
                  {[
                    'ocm',
                    'pcm_concept',
                    'poc',
                    'be',
                    'ia',
                    'pcm_biz',
                    'dev',
                    'proc',
                    'launch_checkpoint',
                    'launch_rfs',
                    'pir',
                  ].map((key) => (
                    <React.Fragment key={key}>
                      <td
                        className={`${centerCellClasses} ${getBackgroundColor(project[key]?.startPlanDate)}`}
                      >
                        {project[key]?.startPlanDate || '-'}
                      </td>
                      <td
                        className={`${centerCellClasses} ${getBackgroundColor(project[key]?.planDate)}`}
                      >
                        {project[key]?.planDate || '-'}
                      </td>
                      <td
                        className={`${centerCellClasses}`}
                      >
                        {project[key]?.planPercentage?.toFixed(1) || '0.0'}%
                      </td>
                    </React.Fragment>
                  ))}
                  <td rowSpan="2" className={centerCellClasses}>
                    {project.total?.plan || '0.0'}%
                  </td>
                  <td rowSpan="2" className={centerCellClasses}>
                    {project.achievement || '0.0'}%
                  </td>
                  <td rowSpan="2" className={centerCellClasses}>
                    {project.planRFS || '-'}
                  </td>
                  <td rowSpan="2" className={centerCellClasses}>
                    {project.revisedRFS || '-'}
                  </td>
                  <td rowSpan="2" className={centerCellClasses}>
                    {project.actualRFS || '-'}
                  </td>
                  <td rowSpan="2" className={centerCellClasses}>
                    {project.delay?.vsPlanned || '-'}
                  </td>
                  <td rowSpan="2" className={centerCellClasses}>
                    {project.delay?.vsRevised || '-'}
                  </td>
                  <td rowSpan="2" className={centerCellClasses}>
                    {project.timelyCompletion?.vsPlanned || '-'}
                  </td>
                  <td rowSpan="2" className={centerCellClasses}>
                    {project.timelyCompletion?.vsRevised || '-'}
                  </td>
                  <td rowSpan="2" className={cellClasses}>
                    {project.highlight || '-'}
                  </td>
                  <td rowSpan="2" className={cellClasses}>
                    {project.support || '-'}
                  </td>
                </tr>
                {/* Actual Row */}
                <tr>
                  <td className={`${cellClasses} font-medium text-gray-700`}>Actual</td>
                  {[
                    'ocm',
                    'pcm_concept',
                    'poc',
                    'be',
                    'ia',
                    'pcm_biz',
                    'dev',
                    'proc',
                    'launch_checkpoint',
                    'launch_rfs',
                    'pir',
                  ].map((key) => (
                    <React.Fragment key={key}>
                      <td
                        className={`${centerCellClasses} ${getBackgroundColor(project[key]?.startActualDate)}`}
                      >
                        {project[key]?.startActualDate || '-'}
                      </td>
                      <td
                        className={`${centerCellClasses} ${getBackgroundColor(project[key]?.actualDate)}`}
                      >
                        {project[key]?.actualDate || '-'}
                      </td>
                      <td
                        className={`${centerCellClasses}`}
                      >
                        {project[key]?.actualPercentage?.toFixed(1) || '0.0'}%
                      </td>
                    </React.Fragment>
                  ))}
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

ProjectCard.propTypes = {
  project: PropTypes.object.isRequired,
};

export default ProjectCard;
