// Next, React, Tw
import { Fragment, useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Packages
import PropTypes from 'prop-types';
import { ChevronDown, ChevronUp } from 'lucide-react';

// Components
import ReactApexcharts from '../Shared/ReactApexcharts';

// Others
import { METRIC_ENDPOINT } from '../../utils/metric';
import { setIsLoading } from '../../utils/store/loadingReducer';

const InitiativeChart = ({ initiativeData }) => {
  const formatDate = (dateString) => {
    if (!dateString || dateString.includes('0001-01-01')) return null;
    return new Date(dateString);
  };

  const formatDisplayDate = (date) => {
    if (!date) return '';
    return date.toLocaleDateString('en-MY', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const chartOptions = {
    chart: {
      type: 'rangeBar',
      height: 300,
      fontFamily: 'inherit',
      toolbar: {
        show: false,
      },
      animations: {
        enabled: true,
        easing: 'easeinout',
      },
      sparkline: {
        enabled: false,
      },
      zoom: false,
      selection: false,
      states: {
        normal: {
          filter: {
            type: 'none',
          },
        },
        hover: {
          filter: {
            type: 'none',
          },
        },
        active: {
          allowMultipleDataPointsSelection: false,
          filter: {
            type: 'none',
          },
        },
      },
    },
    plotOptions: {
      bar: {
        horizontal: true,
        barHeight: '40%',
        borderRadius: 2,
        distributed: true,
      },
    },
    xaxis: {
      type: 'datetime',
      min: new Date('2024-01-01').getTime(),
      max: new Date('2025-12-31').getTime(),
      range: undefined,
      position: 'bottom',
      tickAmount: 12,
      convertedCatToNumeric: false,
      axisTicks: {
        show: true,
        color: '#E5E7EB',
        height: 6,
      },
      axisBorder: {
        show: true,
        color: '#E5E7EB',
      },
      labels: {
        datetimeUTC: false,
        format: 'MMM yy',
        style: {
          fontSize: '12px',
          fontWeight: 500,
        },
        rotate: -45,
        offsetY: 10,
      },
      crosshairs: {
        show: true,
        position: 'back',
        stroke: {
          color: '#b1b1b1',
          width: 1,
          dashArray: 3,
        },
      },
    },
    grid: {
      show: true,
      borderColor: '#f3f4f6',
      xaxis: {
        lines: {
          show: true,
        },
      },
      padding: {
        top: 15,
        right: 35,
        bottom: 40,
        left: 25,
      },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: '12px',
        },
      },
    },
    stroke: {
      width: 1,
      colors: ['#fff'],
    },
    fill: {
      type: 'solid',
      opacity: 0.6,
    },
    legend: {
      position: 'top',
      horizontalAlign: 'left',
      fontSize: '13px',
    },
    tooltip: {
      custom({ series, dataPointIndex }) {
        try {
          const phase = initiativeData.data[dataPointIndex].x;
          
          // Get both start and end dates - start could be from y[0] or from startDate field if provided
          const startDate = formatDate(
            initiativeData.data[dataPointIndex].startDate || initiativeData.data[dataPointIndex].y[0]
          );
          const endDate = formatDate(initiativeData.data[dataPointIndex].y[1]);

          const isActual =
            endDate && !initiativeData.data[dataPointIndex].y[1].includes('0001-01-01');
          const status = isActual
            ? '<div class="text-blue-500">● Actual</div>'
            : '<div class="text-red-700">● Planned</div>';
            
          // Display logic for showing start and end dates
          const startDateDisplay = startDate ? `<div class="mt-2">Start: ${formatDisplayDate(startDate)}</div>` : '';
          const endDateDisplay = endDate ? `<div class="mt-1">End: ${formatDisplayDate(endDate)}</div>` : '';

          return `
            <div class="p-4">
              <div class="font-bold mb-2">${phase}</div>
              ${startDateDisplay}
              ${endDateDisplay}
              <div class="mt-3 font-medium">${status}</div>
            </div>
          `;
        } catch (err) {
          console.error('Error generating tooltip:', err);
          return '<div class="p-2">Error displaying data</div>';
        }
      },
    },
    colors: [
      ({ dataPointIndex }) => {
        try {
          // Get raw data point
          const rawData = initiativeData.data[dataPointIndex];
          if (!rawData) return '#B91C1C'; // Default to plan color

          // Check if actual date exists and is not the default date
          const actualDate = rawData.y[1];
          const isActualDate = actualDate && !actualDate.includes('0001-01-01');
          return isActualDate ? '#3B82F6' : '#B91C1C';
        } catch (err) {
          console.error('Error determining color:', err);
          return '#B91C1C'; // Default to plan color
        }
      },
    ],
    theme: {
      mode: 'light',
    },
  };

  const processedData = [
    {
      name: 'Phases',
      data: initiativeData.data
        .map((item) => {
          // Get start date from either the startDate field or y[0]
          const startDate = item.startDate ? new Date(item.startDate).getTime() : new Date(item.y[0]).getTime();
          
          // Get end date from y[1]
          const endDate = item.y[1] && !item.y[1].includes('0001-01-01')
            ? new Date(item.y[1]).getTime()
            : startDate; // If no valid end date, use start date
          
          return {
            x: item.x,
            y: [startDate, endDate],
          };
        })
        .filter((item) => !Number.isNaN(item.y[0])),
    },
  ];

  return (
    <div className="min-w-0 select-none">
      <div className="w-full">
        <ReactApexcharts
          options={chartOptions}
          series={processedData}
          type="rangeBar"
          height={300}
        />
      </div>
    </div>
  );
};

InitiativeChart.propTypes = {
  initiativeData: PropTypes.shape({
    name: PropTypes.string.isRequired,
    data: PropTypes.arrayOf(
      PropTypes.shape({
        x: PropTypes.string.isRequired,
        y: PropTypes.arrayOf(PropTypes.string).isRequired,
        startDate: PropTypes.string, // Optional startDate field
      })
    ).isRequired,
  }).isRequired,
};

const TimelineView = ({ data }) => {
  const [expandedInitiatives, setExpandedInitiatives] = useState(new Set());
  const [expandAll, setExpandAll] = useState(false);

  const toggleInitiative = (initiativeName) => {
    setExpandedInitiatives((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(initiativeName)) {
        newSet.delete(initiativeName);
      } else {
        newSet.add(initiativeName);
      }
      return newSet;
    });
  };

  const handleExpandAll = () => {
    setExpandAll(!expandAll);
    if (!expandAll) {
      setExpandedInitiatives(new Set(data.map((i) => i.name)));
    } else {
      setExpandedInitiatives(new Set());
    }
  };

  const sortedData = [...data].sort((a, b) => a.name.localeCompare(b.name));

  return (
    <div className="w-full">
      <div className="mb-4 flex justify-end">
        <button
          type="button"
          onClick={handleExpandAll}
          className="flex items-center space-x-1 rounded bg-gray-100 px-3 py-1 text-sm transition-colors duration-200 hover:bg-gray-200"
        >
          {expandAll ? (
            <>
              <ChevronUp className="h-4 w-4" />
              <span>Collapse All</span>
            </>
          ) : (
            <>
              <ChevronDown className="h-4 w-4" />
              <span>Expand All</span>
            </>
          )}
        </button>
      </div>

      <div className="space-y-4">
        {sortedData.map((initiative) => (
          <div key={initiative.name} className="rounded-lg border bg-white shadow-sm">
            <button
              type="button"
              onClick={() => toggleInitiative(initiative.name)}
              className="flex w-full items-center justify-between p-4 hover:bg-gray-50"
            >
              <span className="font-medium">{initiative.name}</span>
              {expandedInitiatives.has(initiative.name) ? (
                <ChevronUp className="h-5 w-5 text-gray-500" />
              ) : (
                <ChevronDown className="h-5 w-5 text-gray-500" />
              )}
            </button>

            <div
              className={`overflow-hidden transition-all duration-300 ease-in-out ${
                expandedInitiatives.has(initiative.name) ? 'max-h-[300px] border-t' : 'max-h-0'
              }`}
            >
              <div className="w-full p-4">
                <InitiativeChart initiativeData={initiative} />
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 flex items-center space-x-6">
        <div className="flex items-center">
          <div className="mr-2 h-1.5 w-4 bg-red-700 lg:h-2 lg:w-6" />
          <span className="text-sm">Plan Date</span>
        </div>
        <div className="flex items-center">
          <div className="mr-2 h-1.5 w-4 bg-blue-500 lg:h-2 lg:w-6" />
          <span className="text-sm">Actual Date</span>
        </div>
      </div>
    </div>
  );
};

TimelineView.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      data: PropTypes.arrayOf(
        PropTypes.shape({
          x: PropTypes.string.isRequired,
          y: PropTypes.arrayOf(PropTypes.string).isRequired,
        })
      ).isRequired,
    })
  ).isRequired,
};

const Timeline = () => {
  const dispatch = useDispatch();
  const [timelineData, setTimelineData] = useState([]);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        dispatch(setIsLoading(true));
        setError(null);

        const response = await fetch(`${METRIC_ENDPOINT}/reporting/timeline`);
        if (!response.ok) {
          throw new Error('Failed to fetch data');
        }

        const result = await response.json();
        setTimelineData(result.series || []);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err.message);
      } finally {
        dispatch(setIsLoading(false));
      }
    };

    fetchData();
  }, [dispatch]);

  return (
    <div className="w-full rounded-lg bg-white p-6 shadow">
      <div className="mb-6">
        <h2 className="mb-4 text-xl font-semibold">Initiative Tracking (DEMO)</h2>

        {error && <div className="mb-4 rounded bg-red-100 p-4 text-red-700">{error}</div>}

        {timelineData.length > 0 && <TimelineView data={timelineData} />}
      </div>
    </div>
  );
};

export default Timeline;
