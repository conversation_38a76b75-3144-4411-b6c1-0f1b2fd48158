import React from 'react';
import PropTypes from 'prop-types';

const InfoRow = ({ label, value }) => (
  <div className="flex items-center justify-between border-b border-gray-100 py-2 last:border-b-0">
    <span className="text-sm font-medium text-gray-500">{label}</span>
    <span className="text-sm text-gray-900">{value || '-'}</span>
  </div>
);

InfoRow.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.string,
};

InfoRow.defaultProps = {
  value: '-',
};

const getStatusColor = (status) => {
  if (!status) return '';
  
  switch (status.toLowerCase()) {
    case 'on track':
      return 'text-green-600';
    case 'delayed':
      return 'text-red-600';
    case 'at risk':
      return 'text-amber-600';
    case 'completed':
      return 'text-blue-600';
    default:
      return '';
  }
};

const ProjectInfoCard = ({ project }) => (
  <div className="w-full">
    <h3 className="mb-4 text-base font-medium text-gray-900">{project.projectName || 'Project Details'}</h3>
    <div className="flex flex-col gap-2">
      <InfoRow label="Initiative" value={project.initiative} />
      <InfoRow label="Project Owner" value={project.projectOwner} />
      <InfoRow label="Project Sponsor" value={project.projectSponsor} />
      <InfoRow label="Start Date" value={project.startDate} />
      <InfoRow label="End Date" value={project.endDate} />
      <div className="flex items-center justify-between border-b border-gray-100 py-2 last:border-b-0">
        <span className="text-sm font-medium text-gray-500">Status</span>
        <span className={`text-sm font-medium ${getStatusColor(project.status)}`}>{project.status || '-'}</span>
      </div>
      <div className="flex items-center justify-between border-b border-gray-100 py-2 last:border-b-0">
        <span className="text-sm font-medium text-gray-500">Completion</span>
        <div className="flex items-center">
          <div className="mr-2 h-2 w-24 rounded-full bg-gray-200">
            <div
              className="h-2 rounded-full bg-blue-600"
              style={{ width: `${project.completion || 0}%` }}
            />
          </div>
          <span className="text-sm text-gray-900">{project.completion || 0}%</span>
        </div>
      </div>
      {project.description && (
        <div className="flex flex-col border-b border-gray-100 py-2 last:border-b-0">
          <span className="mb-1 text-sm font-medium text-gray-500">Description</span>
          <p className="text-sm text-gray-900">{project.description}</p>
        </div>
      )}
    </div>
  </div>
);

ProjectInfoCard.propTypes = {
  project: PropTypes.shape({
    initiative: PropTypes.string,
    projectName: PropTypes.string,
    projectOwner: PropTypes.string,
    projectSponsor: PropTypes.string,
    startDate: PropTypes.string,
    endDate: PropTypes.string,
    status: PropTypes.string,
    completion: PropTypes.number,
    description: PropTypes.string,
  }).isRequired,
};

export default ProjectInfoCard;
