// Next, React
import { useRef } from 'react';
import Link from 'next/link';

// Mui
import { Tooltip } from '@mui/material';
import { Info } from '@mui/icons-material';

// Packages
import { useSnackbar } from 'notistack';

// Others
import axios from '../../utils/axios';
import { VOTING_ENDPOINT } from '../../utils/voting';

const UploadCandidatesButton = ({ onCandidatesUploaded }) => {
  // Standard and Vars
  const fileInputRef = useRef(null);
  const { enqueueSnackbar } = useSnackbar();

  const handleFileUpload = (event) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const csvContent = e.target.result;
        const rows = csvContent.split('\n').filter(row => row.trim());
        
        // Skip header row and process data
        const candidates = rows.slice(1).map(row => {
          const [name, staffId] = row.split(',');
          return {
            candidate_name: name?.trim(),
            candidate_staff_id: staffId?.trim()
          };
        });

        // Validate data - only name is required
        const invalidCandidates = candidates.filter(c => !c.candidate_name);

        if (invalidCandidates.length > 0) {
          enqueueSnackbar('Some candidates are missing name', { 
            variant: 'error' 
          });
          return;
        }

        // Ensure staff_id is at least an empty string if not provided
        const validatedCandidates = candidates.map(c => ({
          ...c,
          candidate_staff_id: c.candidate_staff_id || ''
        }));

        // Pass validated candidates back to parent component
        onCandidatesUploaded(validatedCandidates);

        enqueueSnackbar('Candidates uploaded successfully', { 
          variant: 'success' 
        });
      } catch (error) {
        enqueueSnackbar('Failed to process file', { 
          variant: 'error' 
        });
      }
    };

    reader.readAsText(file);
  };

  return (
    <div className="flex items-center gap-2">
      <Tooltip title="Download template to see required format">
        <Link href="/voting/template.csv">
          <Info />
        </Link>
      </Tooltip>
      <button
        type="button"
        className="bg-voting rounded-md px-2 py-1 text-white"
        onClick={() => fileInputRef?.current?.click()}
      >
        Upload CSV
      </button>
      <input
        ref={fileInputRef}
        type="file"
        accept=".csv"
        className="hidden"
        onChange={handleFileUpload}
      />
    </div>
  );
};

export default UploadCandidatesButton;
