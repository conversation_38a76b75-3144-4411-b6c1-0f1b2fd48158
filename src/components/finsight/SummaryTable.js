// Next, React, Tw
import { Fragment } from 'react';
import { twMerge } from 'tailwind-merge';
import { useRouter } from 'next/router';

// Mui
import { Divider } from '@mui/material';

// Packages
import PropTypes from 'prop-types';
import moment from 'moment';
import * as R from 'ramda';

// Others
import { checkAndReplaceStringWithHyphen, toUpperCaseFirstLetter } from '../../utils/shared';
import {
  MONTHS_ARRAY,
  QUARTERS_ARRAY,
  tidyUpNumber,
  reconstructSourcesArray,
  useFinsightContext,
} from '../../utils/finsight';

const SummaryTable = ({ segment, page }) => {
  // Standard and Vars
  const { query } = useRouter();
  const { view } = query;

  const { filteredSourcesData } = useFinsightContext();
  const array = (() => {
    if (segment === 'Overall TMG') return filteredSourcesData;
    return filteredSourcesData.filter((source) => source?.segment === segment);
  })();

  // Table
  const headerCellStyle = 'bg-finsight whitespace-nowrap text-sm px-4 text-center text-white';
  const bodyCellStyle = 'text-center p-1 text-xs';

  // Others
  const uniqueCategories = R.uniq(R.pluck('category', array));
  const types = R.uniq(R.pluck('type', array));
  const currentMonth = moment().month();
  const isUsingActualForMonth = (month) => month < currentMonth;
  const isUsingActualForQuarter = (quarter) => quarter * 3 < currentMonth;

  const getCustomBgBasedOnVar = (num, type = 'revenue') => {
    if (num === 0) return 'bg-[#0070c0]';
    let num_ = num;
    if (type === 'cost') num_ = num * -1;
    if (num_ > 0) return 'bg-[#00b050]';
    return 'bg-[#c00000]';
  };

  return (
    <>
      <Divider />
      <p className="text-center text-xl font-bold underline">{segment}</p>
      <div className="overflow-x-auto ">
        <table className="min-w-full bg-white text-black">
          <thead>
            <tr>
              <td className={twMerge(headerCellStyle, 'bg-white')} />
              {view === 'month' && (
                <>
                  {MONTHS_ARRAY?.map((month) => (
                    <td
                      key={month}
                      colSpan={3}
                      className={twMerge(
                        headerCellStyle,
                        isUsingActualForMonth(month) ? 'bg-green-700' : 'bg-[#7030a0]',
                        'border-x-8 border-white'
                      )}
                    >
                      {moment()?.month(month)?.format('MMM YYYY')} (
                      {isUsingActualForMonth(month) ? 'A' : 'F'})
                    </td>
                  ))}
                </>
              )}
              {view === 'quarter' && (
                <>
                  {QUARTERS_ARRAY?.map((quarter) => (
                    <td
                      key={quarter}
                      colSpan={3}
                      className={twMerge(
                        headerCellStyle,
                        'bg-[#ed7d31]',
                        'border-x-8 border-white'
                      )}
                    >
                      {`Q${quarter} ${moment()?.year()}`}
                    </td>
                  ))}
                </>
              )}
              {view === 'ytd' && (
                <td
                  colSpan={3}
                  className={twMerge(headerCellStyle, 'bg-[#002060]', 'border-x-8 border-white')}
                >
                  {`YTD ${moment()?.format('MMM YYYY')} (F)`}
                </td>
              )}
            </tr>
          </thead>
          <tbody>
            {types?.map((type) => (
              <Fragment key={type}>
                <tr>
                  <td className={twMerge(headerCellStyle, 'bg-[#4472c4]')}>
                    {toUpperCaseFirstLetter(type)}
                  </td>
                  {view === 'month' && (
                    <>
                      {MONTHS_ARRAY?.map((month) => (
                        <Fragment key={month}>
                          <td
                            className={twMerge(
                              headerCellStyle,
                              isUsingActualForMonth(month) ? 'bg-green-700' : 'bg-[#7030a0]',
                              'border-l-8 border-white'
                            )}
                          >{`${moment()?.month(month)?.format('MMM')} (${isUsingActualForMonth(month) ? 'A' : 'F'})`}</td>
                          <td
                            className={twMerge(
                              headerCellStyle,
                              isUsingActualForMonth(month) ? 'bg-green-700' : 'bg-[#7030a0]'
                            )}
                          >{`${moment()?.month(month)?.format('MMM')} (B)`}</td>
                          <td
                            className={twMerge(
                              headerCellStyle,
                              isUsingActualForMonth(month) ? 'bg-green-700' : 'bg-[#7030a0]',
                              'border-r-8 border-white'
                            )}
                          >
                            Var
                          </td>
                        </Fragment>
                      ))}
                    </>
                  )}
                  {view === 'quarter' && (
                    <>
                      {QUARTERS_ARRAY?.map((quarter) => (
                        <Fragment key={quarter}>
                          <td
                            className={twMerge(
                              headerCellStyle,
                              'bg-[#ed7d31]',
                              'border-l-8 border-white'
                            )}
                          >
                            {`Q${quarter}-${moment()?.format('YY')} (${quarter < currentMonth / 3 ? 'A' : 'F'})`}
                          </td>
                          <td className={twMerge(headerCellStyle, 'bg-[#ed7d31]')}>
                            {`Q${quarter}-${moment()?.format('YY')} (B)`}
                          </td>
                          <td
                            className={twMerge(
                              headerCellStyle,
                              'bg-[#ed7d31]',
                              'border-r-8 border-white'
                            )}
                          >
                            Var
                          </td>
                        </Fragment>
                      ))}
                    </>
                  )}
                  {view === 'ytd' && (
                    <>
                      <td
                        className={twMerge(
                          headerCellStyle,
                          'bg-[#002060]',
                          'border-l-8 border-white'
                        )}
                      >{`YTD ${moment()?.format('MMM YYYY')} (F)`}</td>
                      <td
                        className={twMerge(headerCellStyle, 'bg-[#002060]')}
                      >{`YTD ${moment()?.format('MMM YYYY')} (B)`}</td>
                      <td
                        className={twMerge(
                          headerCellStyle,
                          'bg-[#002060]',
                          'border-r-8 border-white'
                        )}
                      >
                        Var
                      </td>
                    </>
                  )}
                </tr>
                {reconstructSourcesArray(
                  array?.filter((row) => row?.type === type),
                  page
                )?.map((row, i) => (
                  <tr
                    key={i}
                    className={twMerge(
                      'bg-gray-100',
                      row?.tag !== '' && 'bg-[#d9d9d9]',
                      uniqueCategories?.includes(row?.name) && 'bg-[#ddebf7]'
                    )}
                  >
                    <td
                      className={twMerge(
                        bodyCellStyle,
                        'whitespace-nowrap text-left',
                        uniqueCategories?.includes(row?.name) && 'font-bold'
                      )}
                    >
                      {checkAndReplaceStringWithHyphen(row?.name)}
                    </td>
                    {view === 'month' && (
                      <>
                        {MONTHS_ARRAY?.map((month) => {
                          const a =
                            row?.[isUsingActualForMonth(month) ? 'actual' : 'forecast']
                              ?.without_dcph?.[month];
                          const b = row?.budget?.without_dcph?.[month];
                          return (
                            <Fragment key={month}>
                              <td className={twMerge(bodyCellStyle, 'border-l-8 border-white')}>
                                {tidyUpNumber(a)}
                              </td>
                              <td className={bodyCellStyle}>{tidyUpNumber(b)}</td>
                              <td
                                className={twMerge(
                                  bodyCellStyle,
                                  'border-r-8 border-white font-semibold text-white',
                                  getCustomBgBasedOnVar(a - b, type)
                                )}
                              >
                                {tidyUpNumber(a - b)}
                              </td>
                            </Fragment>
                          );
                        })}
                      </>
                    )}
                    {view === 'quarter' && (
                      <>
                        {QUARTERS_ARRAY?.map((quarter) => {
                          const aArray = row?.[
                            isUsingActualForQuarter(quarter) ? 'actual' : 'forecast'
                          ]?.without_dcph?.filter(
                            (_, j) => j <= quarter * 3 && j >= quarter * 3 - 2
                          );
                          const bArray = row?.budget?.without_dcph?.filter(
                            (_, j) => j <= quarter * 3 && j >= quarter * 3 - 2
                          );
                          const a = aArray?.reduce((prev, curr) => prev + curr, 0);
                          const b = bArray?.reduce((prev, curr) => prev + curr, 0);
                          return (
                            <Fragment key={quarter}>
                              <td className={twMerge(bodyCellStyle, 'border-l-8 border-white')}>
                                {tidyUpNumber(a)}
                              </td>
                              <td className={bodyCellStyle}>{tidyUpNumber(b)}</td>
                              <td
                                className={twMerge(
                                  bodyCellStyle,
                                  'border-r-8 border-white font-semibold text-white',
                                  getCustomBgBasedOnVar(a - b, type)
                                )}
                              >
                                {tidyUpNumber(a - b)}
                              </td>
                            </Fragment>
                          );
                        })}
                      </>
                    )}
                    {view === 'ytd' &&
                      (() => {
                        const aArray = row?.forecast?.without_dcph?.filter(
                          (_, j) => j < currentMonth
                        );
                        const bArray = row?.budget?.without_dcph?.filter(
                          (_, j) => j < currentMonth
                        );
                        const a = aArray?.reduce((prev, curr) => prev + curr, 0);
                        const b = bArray?.reduce((prev, curr) => prev + curr, 0);
                        return (
                          <>
                            <td className={twMerge(bodyCellStyle, 'border-l-8 border-white')}>
                              {tidyUpNumber(a)}
                            </td>
                            <td className={bodyCellStyle}>{tidyUpNumber(b)}</td>
                            <td
                              className={twMerge(
                                bodyCellStyle,
                                'border-r-8 border-white font-semibold text-white',
                                getCustomBgBasedOnVar(a - b, type)
                              )}
                            >
                              {tidyUpNumber(a - b)}
                            </td>
                          </>
                        );
                      })()}
                  </tr>
                ))}
              </Fragment>
            ))}
          </tbody>
        </table>
      </div>
    </>
  );
};

SummaryTable.propTypes = {
  segment: PropTypes.string,
  page: PropTypes.string,
};

export default SummaryTable;
