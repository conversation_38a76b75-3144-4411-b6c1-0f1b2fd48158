// Next, React, Tw
import { Fragment } from 'react';
import { twMerge } from 'tailwind-merge';

// Packages
import PropTypes from 'prop-types';
import moment from 'moment';
import * as R from 'ramda';

// Others
import { checkAndReplaceStringWithHyphen, toUpperCaseFirstLetter } from '../../utils/shared';
import {
  FIGURE_TYPES,
  MONTHS_ARRAY,
  reconstructSourcesArray,
  tidyUpNumber,
} from '../../utils/finsight';

const MonthlyBreakdownTable = ({ array }) => {
  // Table
  const headerCellStyle = 'bg-finsight whitespace-nowrap px-4 text-center text-sm text-white';
  const bodyCellStyle = 'text-center p-1 text-xs';

  // Others
  const uniqueCategories = R.uniq(R.pluck('category', array));
  const types = R.uniq(R.pluck('type', array));

  const getCustomBgBasedOnType = (figureType) => {
    if (figureType === 'budget') return 'bg-[#4472c4]';
    if (figureType === 'actual') return 'bg-[#00b050]';
    if (figureType === 'forecast') return 'bg-[#7030a0]';
    return '';
  };

  return (
    <div className="overflow-x-auto ">
      <table className="min-w-full bg-white  text-black">
        <thead>
          <tr>
            <td colSpan={2} className={headerCellStyle} />
            {FIGURE_TYPES?.map((figureType) => (
              <Fragment key={figureType}>
                <td
                  colSpan={13}
                  className={twMerge(
                    headerCellStyle,
                    getCustomBgBasedOnType(figureType),
                    'border-x-8 border-white'
                  )}
                >
                  {`${moment()?.year()}${figureType?.split('')[0]?.toUpperCase()}`} with DCPH
                </td>
                <td
                  colSpan={13}
                  className={twMerge(
                    headerCellStyle,
                    getCustomBgBasedOnType(figureType),
                    'border-x-8 border-white'
                  )}
                >
                  {`${moment()?.year()}${figureType?.split('')[0]?.toUpperCase()}`} DCPH
                </td>
              </Fragment>
            ))}
          </tr>
        </thead>
        <tbody>
          {types?.map((type) => (
            <Fragment key={type}>
              <tr>
                {[toUpperCaseFirstLetter(type), 'Product Code'].map((label) => (
                  <td key={label} className={headerCellStyle}>
                    {label}
                  </td>
                ))}
                {FIGURE_TYPES?.map((figureType) => (
                  <Fragment key={figureType}>
                    {MONTHS_ARRAY?.map((num, i) => (
                      <td
                        key={num}
                        className={twMerge(
                          headerCellStyle,
                          getCustomBgBasedOnType(figureType),
                          'border-white',
                          i === 0 && 'border-l-8'
                        )}
                      >
                        {moment()?.month(num)?.format('MMM')}
                      </td>
                    ))}
                    <td
                      className={twMerge(
                        headerCellStyle,
                        getCustomBgBasedOnType(figureType),
                        'border-white',
                        'border-r-8'
                      )}
                    >
                      FY {moment()?.format('YYYY')}
                    </td>
                    {MONTHS_ARRAY?.map((num, i) => (
                      <td
                        key={num}
                        className={twMerge(
                          headerCellStyle,
                          getCustomBgBasedOnType(figureType),
                          'border-white',
                          i === 0 && 'border-l-8'
                        )}
                      >
                        {moment()?.month(num)?.format('MMM')}
                      </td>
                    ))}
                    <td
                      className={twMerge(
                        headerCellStyle,
                        getCustomBgBasedOnType(figureType),
                        'border-white',
                        'border-r-8'
                      )}
                    >
                      FY {moment()?.format('YYYY')}
                    </td>
                  </Fragment>
                ))}
              </tr>
              {reconstructSourcesArray(array, 'monthly-breakdown')
                ?.filter((o) => o?.type === type)
                ?.map((row) => (
                  <tr
                    key={row?.id}
                    className={twMerge(
                      'bg-gray-100',
                      row?.tag !== '' && 'bg-[#d9d9d9]',
                      uniqueCategories?.includes(row?.name) && 'bg-[#ddebf7]'
                    )}
                  >
                    <td
                      className={twMerge(
                        bodyCellStyle,
                        'whitespace-nowrap text-left',
                        row?.tag !== '' && 'pl-4',
                        uniqueCategories?.includes(row?.name) && 'font-bold'
                      )}
                    >
                      {checkAndReplaceStringWithHyphen(row?.name)}
                    </td>
                    <td className={twMerge(bodyCellStyle, 'whitespace-nowrap')}>
                      {checkAndReplaceStringWithHyphen(row?.product_code)}
                    </td>
                    {FIGURE_TYPES?.map((figure) => (
                      <Fragment key={figure}>
                        {row?.[figure]?.with_dcph?.map((num, i) => (
                          <td
                            key={`with_dcph_${num}`}
                            className={twMerge(
                              bodyCellStyle,
                              'border-white',
                              i === 0 && 'border-l-8'
                            )}
                          >
                            {tidyUpNumber(num)}
                          </td>
                        ))}
                        <td className={twMerge(bodyCellStyle, 'border-white', 'border-r-8')}>
                          {tidyUpNumber(R.sum(row?.[figure]?.with_dcph))}
                        </td>
                        {row?.[figure]?.dcph?.map((num, i) => (
                          <td
                            key={`dcph_${num}`}
                            className={twMerge(
                              bodyCellStyle,
                              'border-white',
                              i === 0 && 'border-l-8'
                            )}
                          >
                            {tidyUpNumber(num)}
                          </td>
                        ))}
                        <td className={twMerge(bodyCellStyle, 'border-white', 'border-r-8')}>
                          {tidyUpNumber(R.sum(row?.[figure]?.dcph))}
                        </td>
                      </Fragment>
                    ))}
                  </tr>
                ))}
            </Fragment>
          ))}
        </tbody>
      </table>
    </div>
  );
};

MonthlyBreakdownTable.propTypes = {
  array: PropTypes.array,
};

export default MonthlyBreakdownTable;
