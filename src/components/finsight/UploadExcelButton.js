// Next, React, Tw
import Link from 'next/link';
import { useRef } from 'react';
import { useDispatch } from 'react-redux';

// Mui
import { Tooltip } from '@mui/material';
import { Info } from '@mui/icons-material';

// Packages
import * as xlsx from 'xlsx';
import moment from 'moment';
import { useSnackbar } from 'notistack';

// Others
import {
  FINSIGHT_ENDPOINT,
  FIGURE_TYPES_COLUMN_MAPPING,
  MONTHS_ARRAY,
  SEGMENTS,
} from '../../utils/finsight';
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';

const UploadExcelButton = () => {
  // Standard and Vars
  const fileInputRef = useRef(null);
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();

  // Others
  const handleSaveSource = async (payload) => {
    dispatch(setIsLoading(true));
    for (let i = 0; i < payload?.length; i += 1) {
      const data = payload?.[i];
      try {
        await axios.put(`${FINSIGHT_ENDPOINT}/sources`, data);
      } catch {
        /* empty */
      }
    }
    dispatch(setIsLoading(false));
  };

  const columnLetterToColumnIndex = (col) => {
    let index = 0;
    for (let i = 0; i < col.length; i += 1) {
      index = index * 26 + (col.charCodeAt(i) - 65 + 1);
    }
    return index - 1;
  };

  const columnIndexToColumnLetter = (colIndex) => {
    let letter = '';
    while (colIndex >= 0) {
      letter = String.fromCharCode((colIndex % 26) + 65) + letter;
      colIndex = Math.floor(colIndex / 26) - 1;
    }
    return letter?.toUpperCase();
  };

  const handleFileUpload = (event) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      // Parse Excel
      const binaryStr = e.target?.result;
      const workbook = xlsx.read(binaryStr, { type: 'binary' });

      // Check Availability of Sheet
      if (!workbook.Sheets?.['Monthly breakdown']) {
        enqueueSnackbar('Monthly breakdown sheet not found.', { variant: 'error' });
        return;
      }

      const sheet = workbook.Sheets['Monthly breakdown'];

      const findRowNumber = (column, searchTerm, startRow = 1) => {
        for (let row = startRow; row <= 1000; row += 1) {
          const cell = sheet[`${column}${row}`];
          if (cell && cell.v?.toLowerCase()?.trim() === searchTerm?.toLowerCase()?.trim())
            return row;
        }
        return null;
      };

      // Make sure all segments are available
      if (
        !findRowNumber('B', SEGMENTS[0]) ||
        !findRowNumber('B', SEGMENTS[2]) ||
        !findRowNumber('B', SEGMENTS[3], 400) ||
        !findRowNumber('B', SEGMENTS[4], 400) ||
        !findRowNumber('B', SEGMENTS[5], 400)
      ) {
        enqueueSnackbar('One of the segment is missing.', { variant: 'error' });
        return;
      }

      const SEGMENTS_BREAKDOWN = [
        {
          segment: SEGMENTS[0],
          revenue: {
            start: findRowNumber('B', 'Revenue', findRowNumber('B', SEGMENTS[0])) + 1,
            end: findRowNumber('B', 'Total', findRowNumber('B', SEGMENTS[0])),
            categories: ['Voice', 'Data', 'IRU', 'Others'],
            tags: [
              {
                name: 'Other Global Voice',
                child: 5,
              },
              {
                name: 'Domestic Leased',
                child: 3,
              },
              {
                name: 'Int Leased',
                child: 7,
              },
              { name: 'Managed Wavelength', child: 2 },
              { name: 'Other Data', child: 7 },
              { name: 'Global Colocation', child: 2 },
              { name: 'CDN', child: 5 },
              { name: 'Other Tel', child: 3 },
            ],
          },
          cost: {
            start: findRowNumber('B', 'Operating Cost', findRowNumber('B', SEGMENTS[0])) + 1,
            end: findRowNumber('B', 'Total Operating Cost', findRowNumber('B', SEGMENTS[0])),
            categories: ['Direct Cost', 'Manpower Cost', 'Other Operating Cost'],
            tags: [{ name: 'Voice', child: 6 }],
          },
        },
        {
          segment: SEGMENTS[2],
          revenue: {
            start: findRowNumber('B', 'Revenue', findRowNumber('B', SEGMENTS[2])) + 1,
            end: findRowNumber('B', 'Total', findRowNumber('B', SEGMENTS[2])),
            categories: ['Voice', 'Data', 'IRU', 'Others'],
            tags: [
              {
                name: 'Other Domestic Voice',
                child: 5,
              },
              {
                name: 'Domestic Leased',
                child: 10,
              },
              {
                name: 'Domestic Ethernet',
                child: 4,
              },
              {
                name: 'Int Leased',
                child: 4,
              },
              {
                name: '5G Fibre Leasing (DNB)',
                child: 2,
              },
              {
                name: 'Other Data',
                child: 10,
              },
              {
                name: 'CDN',
                child: 5,
              },
              {
                name: 'Global Colocation',
                child: 2,
              },
              {
                name: 'Other Tel',
                child: 6,
              },
            ],
          },
          cost: {
            start: findRowNumber('B', 'Operating Cost', findRowNumber('B', SEGMENTS[2])) + 1,
            end: findRowNumber('B', 'Total Operating Cost', findRowNumber('B', SEGMENTS[2])),
            categories: ['Direct Cost', 'Manpower Cost', 'Other Operating Cost'],
            tags: [{ name: 'Voice', child: 4 }],
          },
        },
        {
          segment: SEGMENTS[3],
          revenue: {
            start: findRowNumber('B', 'Revenue', findRowNumber('B', SEGMENTS[3], 400)) + 1,
            end: findRowNumber('B', 'Total', findRowNumber('B', SEGMENTS[3], 400)),
            categories: ['Data', 'IRU', 'Others'],
            tags: [],
          },
          cost: {
            start: findRowNumber('B', 'Operating Cost', findRowNumber('B', SEGMENTS[3], 400)) + 1,
            end: findRowNumber('B', 'Total Operating Cost', findRowNumber('B', SEGMENTS[3], 400)),
            categories: ['Direct Cost', 'Manpower Cost', 'Other Operating Cost'],
            tags: [{ name: 'Voice', child: 4 }],
          },
        },
        {
          segment: SEGMENTS[4],
          revenue: {
            start: findRowNumber('B', 'Revenue', findRowNumber('B', SEGMENTS[4], 400)) + 1,
            end: findRowNumber('B', 'Total', findRowNumber('B', SEGMENTS[4], 400)),
            categories: ['Data', 'IRU', 'Others'],
            tags: [],
          },
          cost: {
            start: findRowNumber('B', 'Operating Cost', findRowNumber('B', SEGMENTS[4], 400)) + 1,
            end: findRowNumber('B', 'Total Operating Cost', findRowNumber('B', SEGMENTS[4], 400)),
            categories: ['Direct Cost', 'Manpower Cost', 'Other Operating Cost'],
            tags: [{ name: 'Voice', child: 4 }],
          },
        },
        {
          segment: SEGMENTS[5],
          revenue: {
            start:
              findRowNumber('B', 'Elimination Revenue', findRowNumber('B', SEGMENTS[5], 400)) + 1,
            end: findRowNumber('B', 'Total', findRowNumber('B', SEGMENTS[5], 400)),
            categories: ['Intra WS Elim', 'Net Intra Elim'],
            tags: [],
          },
          cost: {
            start: findRowNumber('B', 'Elimination Cost', findRowNumber('B', SEGMENTS[5], 400)) + 1,
            end: findRowNumber('B', 'Total', findRowNumber('B', SEGMENTS[5], 400)),
            categories: ['Intra WS Elim', 'Net Intra Elim'],
            tags: [
              { name: 'Direct Cost', child: 4 },
              { name: 'OOC', child: 2 },
            ],
          },
        },
      ];

      const sourceArray = [];

      SEGMENTS_BREAKDOWN?.map((temp) => {
        const pushIntoSourceArray = (type) => {
          let category = '';
          let tag = '';
          const year = String(moment()?.year());

          let ticker = 0;
          let maxTicker = 1;
          for (let i = temp?.[type]?.start; i < temp?.[type]?.end; i += 1) {
            const name = sheet?.[`B${i}`]?.v?.trim();
            if (temp?.[type]?.categories?.includes(name)) {
              category = name;
              continue;
            }
            if (temp?.[type]?.tags?.map((o) => o?.name)?.includes(name)) {
              tag = name;
              ticker = 0;
              // eslint-disable-next-line no-loop-func
              maxTicker = temp?.[type]?.tags?.find((t) => t?.name === tag)?.child;
              continue;
            }
            if (ticker >= maxTicker) tag = '';

            let source = {
              year,
              segment: temp?.segment,
              category,
              tag,
              type,
              name,
              product_code: sheet?.[`C${i}`]?.v,
            };
            FIGURE_TYPES_COLUMN_MAPPING?.map((o) => {
              source = {
                ...source,
                [o?.type]: {
                  dcph: MONTHS_ARRAY.map(
                    (month) =>
                      sheet?.[
                        `${columnIndexToColumnLetter(columnLetterToColumnIndex(o?.dcph) + month)}${i}`
                      ]?.v
                  ),
                  with_dcph: MONTHS_ARRAY.map(
                    (month) =>
                      sheet?.[
                        `${columnIndexToColumnLetter(columnLetterToColumnIndex(o?.withDcph) + month)}${i}`
                      ]?.v
                  ),
                },
              };
              return null;
            });
            sourceArray?.push(source);
            ticker += 1;
          }
        };
        Object?.keys(temp)
          ?.filter((o) => o !== 'segment')
          ?.map((o) => pushIntoSourceArray(o));
        return null;
      });
      await handleSaveSource(sourceArray);
      if (window) window?.location?.reload();
    };

    reader.readAsBinaryString(file);
  };

  return (
    <div className="flex items-center gap-2">
      <Tooltip title="Please refer example template in the guideline tab.">
        <Link href="/finsight/guidelines">
          <Info />
        </Link>
      </Tooltip>
      <button
        type="button"
        className="cta-btn bg-finsight"
        onClick={() => fileInputRef?.current?.click()}
      >
        Upload Excel
      </button>
      <input
        ref={fileInputRef}
        type="file"
        accept=".xlsx, .xls"
        className="hidden"
        onChange={handleFileUpload}
      />
    </div>
  );
};

export default UploadExcelButton;
