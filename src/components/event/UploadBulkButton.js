// Next, React, Tw
import { useRouter } from 'next/router';
import { useRef } from 'react';
import { useDispatch } from 'react-redux';

// Packages
import Papa from 'papaparse';
import PropTypes from 'prop-types';

// Others
import axios from '../../utils/axios';
import { EVENT_ENDPOINT } from '../../utils/event';
import { useSnackbar } from '../Shared/snackbar';
import { setIsLoading } from '../../utils/store/loadingReducer';

const UploadBulkButton = ({ type, attendeeArray }) => {
  // Standard
  const { query } = useRouter();
  const { eventId } = query;
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();

  // Others
  const fileInputRef = useRef(null);

  const saveNewAttendee = async (data) => {
    dispatch(setIsLoading(true));
    try {
      await axios.post(`${EVENT_ENDPOINT}/attendees`, data);
    } catch (error) {
      //   console.log(error);
    }
    dispatch(setIsLoading(false));
  };

  const updateExistingAttendee = async (data) => {
    dispatch(setIsLoading(true));
    try {
      await axios.put(`${EVENT_ENDPOINT}/attendees/${data?.id}`, data);
    } catch (error) {
      //   console.log(error);
    }
    dispatch(setIsLoading(false));
  };

  const processData = async (data) => {
    for (let i = 0; i < data?.length; i += 1) {
      if (type === 'new') {
        if (attendeeArray?.find((o) => o?.staff_id === data?.[i]?.['Staff ID']?.toLowerCase()))
          continue;

        await saveNewAttendee({
          event_id: eventId,
          rsvp: 'yes',
          staff_id: data?.[i]?.['Staff ID']?.toLowerCase()?.trim(),
          // healthy: data?.[i]['Healthy (Yes/No)'] === 'Yes',
          // sex: data?.[i].Sex?.toLowerCase(),
          // room_info: data?.[i]?.['Room Number'],
          // team_info: data?.[i]?.['Group Name'],
        });
      } else if (type === 'update') {
        const payload = attendeeArray?.find(
          (o) => o?.staff_id === data?.[i]?.['Staff ID']?.toLowerCase()
        );
        payload.rsvp = 'yes';
        // payload.canopy_info = data?.[i]?.Canopy;
        await updateExistingAttendee(payload);
      }
    }

    if (window) window.location.reload();
  };

  return (
    <>
      <button
        type="button"
        className="bg-capry cta-btn"
        onClick={() => fileInputRef?.current?.click()}
      >
        {type?.toUpperCase()} UPLOAD
      </button>
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={(event) => {
          const extension =
            event?.target?.files[0]?.name?.split('.')[
              event.target.files[0].name.split('.').length - 1
            ];
          if (extension !== 'csv') {
            enqueueSnackbar('Please upload a CSV.', {
              variant: 'error',
            });
            return;
          }
          Papa.parse(event?.target?.files[0], {
            header: true,
            complete: (results) => processData(results?.data),
          });
        }}
      />
    </>
  );
};

UploadBulkButton.propTypes = {
  type: PropTypes.string,
  attendeeArray: PropTypes.any,
};

export default UploadBulkButton;
