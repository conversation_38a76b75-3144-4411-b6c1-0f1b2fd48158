// Next, React, Tw
import { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/router';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';

// Packages
import * as yup from 'yup';
import * as R from 'ramda';

// Components
import ReactApexcharts from '../Shared/ReactApexcharts';
import { TextInput } from '../Shared/CustomInput';
import ReactAnimatedNumber from '../Shared/ReactAnimatedNumber';

// Others
import axios from '../../utils/axios';
import { METRIC_ENDPOINT } from '../../utils/metric';
import { AUM_ENDPOINT } from '../../utils/aum';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { useSnackbar } from '../Shared/snackbar';

const RevenueSection = () => {
  // Standard and Vars
  const { user } = useAuthContext();
  const { asPath, query } = useRouter();
  const { enqueueSnackbar } = useSnackbar();
  const { productId, productAbbreviation, year, month } = query;

  const [allRevenueData, setAllRevenueData] = useState([]);
  const [userSubModules, setUserSubModules] = useState([]);

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const schema = yup.object({
    actual: yup
      .number()
      .required('Please provide actual revenue.')
      .transform((value) => Number(value)),
    actual_ytd: yup
      .number()
      .required('Please provide actual YTD number.')
      .transform((value) => Number(value)),
    full_year_target: yup
      .number()
      .required('Please provide full year target revenue.')
      .transform((value) => Number(value)),
    gap: yup
      .number()
      .required('Please provide gap.')
      .transform((value) => Number(value)),
    month: yup.string().required('Please provide month.'),
    product_id: yup.string().required('Please provide product ID.'),
    target: yup
      .number()
      .required('Please provide target revenue.')
      .transform((value) => Number(value)),
    target_ytd: yup
      .number()
      .required('Please provide target YTD revenue.')
      .transform((value) => Number(value)),
    year: yup.string().required('Please provide year.'),
  });
  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  const handleDialogClose = async (action) => {
    if (action) {
      let payload = {
        ...dialogData,
        gap: dialogData.actual - dialogData.target,
        year,
        month,
        product_id: productId,
      };
      try {
        payload = await schema.validate(payload, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }

      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${METRIC_ENDPOINT}/revenue`, payload);
            break;
          case 'put':
            response = await axios.put(`${METRIC_ENDPOINT}/revenue/${payload?.id}`, payload);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    setDialogData({});
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const filteredDataByYear = allRevenueData?.filter((o) => o?.year === year);

  const filteredDataByMonth = (() => {
    if (month === 'all') {
      return filteredDataByYear;
    }
    return filteredDataByYear?.filter((o) => o?.month === month);
  })();

  const addAllValueInCertainKey = (dataArray, key) => R.pipe(R.pluck(key), R.sum)(dataArray);

  const series = (() => {
    const getCertainMonthActualOrTargetNumber = (localMonth, key) => {
      const temp = allRevenueData?.filter((o) => o?.month === localMonth);
      if (temp?.length === 0) {
        return 0;
      }
      return R.pipe(R.pluck(key), R.sum)(temp);
    };

    const monthList = [];

    for (let i = 1; i <= 12; i += 1) {
      monthList.push(`${String(i)?.padStart(2, '0')}`);
    }

    return [
      {
        name: 'Actual',
        data: monthList?.map((o) => getCertainMonthActualOrTargetNumber(o, 'actual')),
      },
      {
        name: 'Target',
        data: monthList?.map((o) => getCertainMonthActualOrTargetNumber(o, 'target')),
      },
      {
        name: 'YTD (A)',
        data: monthList?.map((o) => getCertainMonthActualOrTargetNumber(o, 'actual_ytd')),
      },
      {
        name: 'Full Year (T)',
        data: monthList?.map((o) => getCertainMonthActualOrTargetNumber(o, 'full_year_target')),
      },
    ];
  })();

  const options = (() => {
    const temp = {
      chart: {
        toolbar: {
          show: false,
        },
        height: 500,
        type: 'bar',
        stacked: true,
      },
      plotOptions: {
        bar: {
          dataLabels: {
            position: 'top',
          },
          endingShape: 'rounded',
          columnWidth: '30%',
        },
      },
      dataLabels: {
        enabled: false,
      },
      xaxis: {
        categories: [
          'Jan',
          'Feb',
          'Mar',
          'Apr',
          'May',
          'Jun',
          'Jul',
          'Aug',
          'Sep',
          'Oct',
          'Nov',
          'Dec',
        ],
        position: 'bottom',
        labels: {
          rotate: -90,
          rotateAlways: true,
        },
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
        crosshairs: {
          fill: {
            type: 'gradient',
            gradient: {
              colorFrom: '#D8E3F0',
              colorTo: '#BED1E6',
              stops: [0, 100],
              opacityFrom: 0.4,
              opacityTo: 0.5,
            },
          },
        },
        tooltip: {
          enabled: true,
          offsetY: -35,
        },
      },
    };
    return temp;
  })();

  const memoizedComponent = useMemo(
    () => (
      <>
        <div className="flex w-full justify-center">
          <p className="text-md font-semibold underline">Revenue</p>
        </div>
        <div className="flex w-full items-center">
          <div className="flex w-1/3 flex-col items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              viewBox="0 0 30 30"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-[#48c78e]"
            >
              <line x1="22" y1="12" x2="2" y2="12" />{' '}
              <path d="M5.45 5.11L2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z" />{' '}
              <line x1="6" y1="16" x2="6" y2="16" /> <line x1="10" y1="16" x2="10" y2="16" />
            </svg>
            <p className="text-xs">YTD (A)</p>
            <p className="text-sm font-bold md:text-xl">
              RM&nbsp;
              <ReactAnimatedNumber
                value={addAllValueInCertainKey(filteredDataByMonth, 'actual_ytd')}
                formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
              />
            </p>
          </div>
          <div className="flex w-1/3 flex-col items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              viewBox="0 0 30 30"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-[#ff6602]"
            >
              <ellipse cx="12" cy="5" rx="9" ry="3" />
              <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3" />{' '}
              <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5" />
            </svg>
            <p className="text-xs">YTD (T)</p>
            <p className="text-sm font-bold md:text-xl">
              RM&nbsp;
              <ReactAnimatedNumber
                value={addAllValueInCertainKey(filteredDataByMonth, 'target_ytd')}
                formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
              />
            </p>
          </div>
          <div className="flex w-1/3 flex-col items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              viewBox="0 0 30 30"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-[#3e8ed0]"
            >
              <path d="M5 17H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1" />
              <polygon points="12 15 17 21 7 21 12 15" />
            </svg>
            <p className="text-xs">FY (T)</p>
            <p className="text-sm font-bold md:text-xl">
              RM&nbsp;
              <ReactAnimatedNumber
                value={addAllValueInCertainKey(filteredDataByMonth, 'full_year_target')}
                formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
              />
            </p>
          </div>
        </div>
        <div className="flex w-full items-center">
          <div className="flex w-1/3 flex-col items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-[#48c78e]"
            >
              <path d="M3 3v18h18" /> <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3" />
            </svg>
            <p className="text-xs">ACTUAL</p>
            <p className="text-sm font-bold md:text-xl">
              RM&nbsp;
              <ReactAnimatedNumber
                value={addAllValueInCertainKey(filteredDataByMonth, 'actual')}
                formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
              />
            </p>
          </div>
          <div className="flex w-1/3 flex-col items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              viewBox="0 0 30 30"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-[#ff6602]"
            >
              <path d="M12 20v-6M6 20V10M18 20V4" />
            </svg>
            <p className="text-xs">TARGET</p>
            <p className="text-sm font-bold md:text-xl">
              RM&nbsp;
              <ReactAnimatedNumber
                value={addAllValueInCertainKey(filteredDataByMonth, 'target')}
                formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
              />
            </p>
          </div>
          <div className="flex w-1/3 flex-col items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-[#3e8ed0]"
            >
              <path d="M12 22a10 10 0 1 0 0-20 10 10 0 0 0 0 20z" />
              <path d="M19 6l-7 6V2.5" />
            </svg>
            <p className="text-xs">GAP</p>
            <p className="text-sm font-bold md:text-xl">
              RM&nbsp;
              <ReactAnimatedNumber
                value={addAllValueInCertainKey(filteredDataByMonth, 'gap')}
                formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
              />
            </p>
          </div>
        </div>

        <div className="rounded-md bg-white px-2">
          <ReactApexcharts options={options} series={series} type="line" height={300} />
        </div>
      </>
    ),
    // eslint-disable-next-line
    [JSON?.stringify(filteredDataByYear), JSON?.stringify(filteredDataByMonth)]
  );

  const fetchData = async () => {
    try {
      const response = await axios.get(`${METRIC_ENDPOINT}/revenue/product_id/${productId}`);
      setAllRevenueData(response?.data?.data || []);
    } catch {
      setAllRevenueData([]);
    }
  };

  const fetchData2 = async () => {
    try {
      const response = await axios.get(
        `${AUM_ENDPOINT}/user/v1/module/${asPath.split('/')[1]}/${user?.staff_id}`
      );
      if (response?.data?.data === null) {
        return;
      }
      const temp2 = R.pluck(
        'submodule',
        response.data.data[0]?.modules[0]?.submodules.filter((o) => o?.role === 'admin')
      );
      setUserSubModules(temp2);
    } catch {
      setUserSubModules([]);
    }
  };

  useEffect(() => {
    fetchData();
  }, [productId, year, month]);

  useEffect(() => {
    fetchData2();
  }, []);

  return (
    <>
      <div className="flex w-full flex-col gap-4 p-2">
        <div className="flex h-[40px] w-full items-center justify-end px-4">
          {month !== 'all' &&
            (user?.isSuperAdmin || userSubModules?.includes(productAbbreviation)) && (
              <button
                type="button"
                className="flex"
                onClick={() => {
                  if (filteredDataByMonth?.length === 0) {
                    handleClickOpenDialog(false);
                    return;
                  }
                  handleClickOpenDialog(true, filteredDataByMonth[0]);
                }}
              >
                <p className="text-blue-500 underline">Edit</p>
              </button>
            )}
        </div>
        {memoizedComponent}
      </div>

      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle>{!editModeDialog ? 'New' : 'Edit'} Revenue Data</DialogTitle>
        <DialogContent>
          <div className="flex flex-col gap-4 p-2">
            <TextInput
              name="actual"
              value={dialogData?.actual}
              placeholder="Actual"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="target"
              value={dialogData?.target}
              placeholder="Target"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="actual_ytd"
              value={dialogData?.actual_ytd}
              placeholder="Actual (YTD)"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="target_ytd"
              value={dialogData?.target_ytd}
              placeholder="Target (YTD)"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="full_year_target"
              value={dialogData?.full_year_target}
              placeholder="Full Year Target"
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
              Cancel
            </button>
            <div className="flex gap-2">
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="rounded-[4px] bg-[#d43801] p-2 font-semibold text-white"
                >
                  Save
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="rounded-[4px] bg-[#d43801] p-2 font-semibold text-white"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default RevenueSection;
