// Next, React, Tw
import { twMerge } from 'tailwind-merge';
import { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/router';

// Mui
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  Tabs,
  Tab,
} from '@mui/material';

// Packages
import * as yup from 'yup';
import * as R from 'ramda';

// Components
import ReactAnimatedNumber from '../Shared/ReactAnimatedNumber';
import { METRIC_ENDPOINT } from '../../utils/metric';
import { useSnackbar } from '../Shared/snackbar';
import ReactApexcharts from '../Shared/ReactApexcharts';
import { TextInput } from '../Shared/CustomInput';

// Others
import axios from '../../utils/axios';
import { useParamContext } from '../../utils/auth/ParamProvider';

const PhysicalSection = () => {
  // Standard
  const { query } = useRouter();
  const { enqueueSnackbar } = useSnackbar();
  const { setParam } = useParamContext();
  const { productId, year, month, week, tab } = query;

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog] = useState(false);
  const schema = yup.object({
    month: yup.string().required('Please provide month.'),
    product_id: yup.string().required('Please provide a product ID.'),
    total_capacity: yup
      .number()
      .required('Please provide total capacity.')
      .transform((value) => Number(value)),
    total_capacity_new_install: yup
      .number()
      .required('Please provide total capacity new install.')
      .transform((value) => Number(value)),
    total_capacity_termination: yup
      .number()
      .required('Please provide total capacity termination.')
      .transform((value) => Number(value)),
    total_circuit: yup
      .number()
      .required('Please provide total circuit.')
      .transform((value) => Number(value)),
    total_circuit_new_install: yup
      .number()
      .required('Please provide total circuit new install.')
      .transform((value) => Number(value)),
    total_circuit_termination: yup
      .number()
      .required('Please provide total circuit termination.')
      .transform((value) => Number(value)),
    week: yup.string().required('Please provide week.'),
    year: yup.string().required('Please provide year.'),
  });
  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleDialogClose = async (action) => {
    if (action) {
      let payload = {
        ...dialogData,
        total_capacity:
          dialogData.total_capacity_new_install - dialogData.total_capacity_termination,
        total_circuit: dialogData.total_circuit_new_install - dialogData.total_circuit_termination,
        year,
        month,
        week,
        product_id: productId,
      };
      try {
        payload = await schema.validate(payload, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }

      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${METRIC_ENDPOINT}/circuit`, payload);
            break;
          case 'put':
            response = await axios.put(`${METRIC_ENDPOINT}/circuit/${payload?.id}`, payload);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    setDialogData({});
    setDialogOpen(false);
    fetchData();
  };

  // Others
  const [allPhysicalData, setAllPhysicalData] = useState([]);

  const transformedData = (() =>
    allPhysicalData?.map((o) => ({
      ...o,
      active: {
        ...o?.active,
        total_capacity: o.active.total_capacity / 1000000000,
      },
      new_install: {
        ...o?.new_install,
        total_capacity: o.new_install.total_capacity / 1000000000,
      },
      termination: {
        ...o?.termination,
        total_capacity: o.termination.total_capacity / 1000000000,
      },
    })))();
  const filteredArray = (() => {
    let temp = transformedData;
    if (year === 'all') {
      return temp?.filter((o) => o?.year === '0');
    }
    temp = temp?.filter((o) => o?.year === year);
    if (month === 'all') {
      return temp?.filter((o) => o?.month === '0');
    }
    temp = temp?.filter((o) => o?.month === month);
    if (['', undefined, 'all']?.includes(week)) {
      return temp?.filter((o) => o?.week === '0');
    }
    return temp?.filter((o) => o?.week === week);
  })();

  const getNumbersFromCertainKey = (firstKey, secondKey) =>
    filteredArray?.length > 0 ? filteredArray[0][firstKey][secondKey] : 0;

  const lifeTimeSummaryObjectArray = transformedData
    ?.filter((o) => o?.year !== '0' && o?.month === '0' && o?.week === '0')
    ?.map((o) => ({ ...o, year: Number(o?.year) }))
    ?.sort((a, b) => a.year - b.year);

  const annualSummaryObjectArray = transformedData
    ?.filter((o) => o?.year === year && o?.month !== '0' && o?.week === '0')
    ?.map((o) => ({ ...o, month: Number(o?.month) }))
    ?.sort((a, b) => a.month - b.month);

  const series = (physicalOrCapacity) => {
    const getNumberArray = (newInstallOrTermination) => {
      let temp;
      if (year === 'all') {
        temp = lifeTimeSummaryObjectArray;
      } else if (month === 'all') {
        temp = annualSummaryObjectArray;
      } else {
        temp = [];
      }

      return temp?.map((o) => o[newInstallOrTermination][physicalOrCapacity]);
    };

    return [
      {
        name: 'New Install',
        data: getNumberArray('new_install'),
      },
      {
        name: 'Termination',
        data: getNumberArray('termination'),
      },
    ];
  };

  const activeSeries = (() => {
    const getNumberArray = (capacityOrCircuit) => {
      let temp;
      if (year === 'all') {
        temp = lifeTimeSummaryObjectArray;
      } else if (month === 'all') {
        temp = annualSummaryObjectArray;
      } else {
        temp = [];
      }

      return temp?.map((o) => o?.active[capacityOrCircuit]);
    };

    return [
      {
        name: 'Total Capacity',
        data: getNumberArray('total_capacity'),
      },
      {
        name: 'Total Circuit',
        data: getNumberArray('total_circuit'),
      },
    ];
  })();

  const categories = (() => {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    if (year === 'all') {
      return R.pipe(R.pluck('year'), R.uniq)(lifeTimeSummaryObjectArray);
    }
    const temp = R.pipe(R.pluck('month'), R.uniq)(annualSummaryObjectArray);
    return temp?.map((o) => months[o - 1]);
  })();
  const options = (() => {
    const temp = {
      chart: {
        toolbar: {
          show: false,
        },
        height: 500,
        type: 'bar',
        stacked: true,
      },
      plotOptions: {
        bar: {
          dataLabels: {
            position: 'top',
          },
          endingShape: 'rounded',
          columnWidth: '30%',
        },
      },
      dataLabels: {
        enabled: false,
      },
      xaxis: {
        categories,
        position: 'bottom',
        labels: {
          rotate: -90,
          rotateAlways: true,
        },
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
        crosshairs: {
          fill: {
            type: 'gradient',
            gradient: {
              colorFrom: '#D8E3F0',
              colorTo: '#BED1E6',
              stops: [0, 100],
              opacityFrom: 0.4,
              opacityTo: 0.5,
            },
          },
        },
        tooltip: {
          enabled: true,
          offsetY: -35,
        },
      },
    };
    return temp;
  })();
  const activeOptions = (() => {
    const temp = {
      chart: {
        toolbar: {
          show: false,
        },
        height: 500,
      },
      dataLabels: {
        enabled: false,
      },
      xaxis: {
        categories,
        position: 'bottom',
        labels: {
          rotate: -90,
          rotateAlways: true,
        },
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
        crosshairs: {
          fill: {
            type: 'gradient',
            gradient: {
              colorFrom: '#D8E3F0',
              colorTo: '#BED1E6',
              stops: [0, 100],
              opacityFrom: 0.4,
              opacityTo: 0.5,
            },
          },
        },
        tooltip: {
          enabled: true,
          offsetY: -35,
        },
      },
      yaxis: [
        {
          axisTicks: {
            show: true,
          },
          axisBorder: {
            show: true,
          },
          labels: {
            style: {
              colors: '#008FFB',
            },
          },
          title: {
            text: 'Total Capacity / Gbps',
            style: {
              color: '#008FFB',
            },
          },
        },
        {
          opposite: true,
          axisTicks: {
            show: true,
          },
          axisBorder: {
            show: true,
          },
          labels: {
            style: {
              colors: '#00E396',
            },
          },
          title: {
            text: 'Total Circuit',
            style: {
              color: '#00E396',
            },
          },
        },
      ],
      legend: {
        show: false,
      },
    };
    return temp;
  })();

  const memoizedComponent = useMemo(
    () => (
      <>
        <div className="flex w-full">
          <div className="flex w-1/2 flex-col items-center gap-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-[#ff6602]"
            >
              <ellipse cx="12" cy="5" rx="9" ry="3" />
              <path d="M21 12c0 1.66-4 3-9 3s-9-1.34-9-3" />
              <path d="M3 5v14c0 1.66 4 3 9 3s9-1.34 9-3V5" />
            </svg>
            <p>ACTIVE PHYSICAL</p>
            <p className="text-3xl font-bold">
              <ReactAnimatedNumber
                value={getNumbersFromCertainKey('active', 'total_circuit')}
                formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
              />
            </p>
          </div>
          <div className="flex w-1/2 flex-col items-center gap-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-[#7a7a7a] dark:text-white"
            >
              <line x1="22" y1="12" x2="2" y2="12" />
              <path d="M5.45 5.11L2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z" />
              <line x1="6" y1="16" x2="6" y2="16" />
              <line x1="10" y1="16" x2="10" y2="16" />
            </svg>
            <p>ACTIVE CAPACITY</p>
            <p className="text-3xl font-bold">
              <ReactAnimatedNumber
                value={getNumbersFromCertainKey('active', 'total_capacity')}
                formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
              />
              <span className="text-lg">&nbsp;Gbps</span>
            </p>
          </div>
        </div>

        <div className="flex w-full justify-center">
          <p className="text-md font-semibold underline">Physical</p>
        </div>
        <div className="flex w-full items-center">
          <div className="flex w-1/3 flex-col items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-[#48c78e]"
            >
              <path d="M3 3v18h18" /> <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3" />
            </svg>
            <p className="text-xs">NEW INSTALL</p>
            <p className="text-sm font-bold md:text-xl">
              <ReactAnimatedNumber
                value={getNumbersFromCertainKey('new_install', 'total_circuit')}
                formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
              />
            </p>
          </div>
          <div className="flex w-1/3 flex-col items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-[#f14768]"
            >
              <path d="M3 3v18h18" /> <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3" />
            </svg>
            <p className="text-xs">TERMINATION</p>
            <p className="text-sm font-bold md:text-xl">
              <ReactAnimatedNumber
                value={getNumbersFromCertainKey('termination', 'total_circuit')}
                formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
              />
            </p>
          </div>
          <div className="flex w-1/3 flex-col items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-[#3e8ed0]"
            >
              <path d="M3 3v18h18" /> <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3" />
            </svg>
            <p className="text-xs">NET. TOTAL</p>
            <p className="text-sm font-bold md:text-xl">
              <ReactAnimatedNumber
                value={
                  getNumbersFromCertainKey('new_install', 'total_circuit') -
                  getNumbersFromCertainKey('termination', 'total_circuit')
                }
                formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
              />
            </p>
          </div>
        </div>

        <Divider />
        <div className="flex w-full justify-center">
          <p className="text-md font-semibold underline">Capacity</p>
        </div>
        <div className="flex w-full items-center">
          <div className="flex w-1/3 flex-col items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-[#48c78e]"
            >
              <path d="M3 3v18h18" /> <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3" />
            </svg>
            <p className="text-xs">NEW INSTALL</p>
            <p className="text-sm font-bold md:text-xl">
              <ReactAnimatedNumber
                value={getNumbersFromCertainKey('new_install', 'total_capacity')}
                formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
              />
            </p>
          </div>
          <div className="flex w-1/3 flex-col items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-[#f14768]"
            >
              <path d="M3 3v18h18" /> <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3" />
            </svg>
            <p className="text-xs">TERMINATION</p>
            <p className="text-sm font-bold md:text-xl">
              <ReactAnimatedNumber
                value={getNumbersFromCertainKey('termination', 'total_capacity')}
                formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
              />
            </p>
          </div>
          <div className="flex w-1/3 flex-col items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-[#3e8ed0]"
            >
              <path d="M3 3v18h18" /> <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3" />
            </svg>
            <p className="text-xs">NET. TOTAL</p>
            <p className="text-sm font-bold md:text-xl">
              <ReactAnimatedNumber
                value={
                  getNumbersFromCertainKey('new_install', 'total_capacity') -
                  getNumbersFromCertainKey('termination', 'total_capacity')
                }
                formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
              />
            </p>
          </div>
        </div>
      </>
    ),
    [JSON?.stringify(filteredArray)]
  );

  const fetchData = async () => {
    try {
      const response = await axios.get(`${METRIC_ENDPOINT}/circuit/product_id/${productId}`);
      if (response?.data?.status === 'success' && response?.data?.data !== null) {
        setAllPhysicalData(response?.data?.data);
      }
    } catch {
      setAllPhysicalData([]);
    }
  };

  useEffect(() => {
    fetchData();
  }, [productId, year, month, week]);

  return (
    <>
      <div className="flex w-full flex-col gap-4 p-2">
        <div
          className={twMerge(
            'flex h-[40px] w-full items-center px-4',
            `${week !== 'all' ? 'justify-between' : ''}`
          )}
        >
          {year !== 'all' && month !== 'all' && (
            <select
              className="peer h-full w-[200px] rounded-lg border  bg-transparent bg-white px-3 py-2.5 text-xs text-black outline outline-0 transition-all placeholder-shown:border focus:border-2 focus:border-[#ff7b7b] focus:outline-0 disabled:border-2 disabled:border-t-transparent disabled:outline-0"
              value={week}
              onChange={(event) => setParam({ week: event.target.value })}
            >
              <option value="">Entire Month</option>
              {['1', '2', '3', '4', '5'].map((o, i) => (
                <option key={i} value={o}>
                  Week {o}
                </option>
              ))}
            </select>
          )}

          {/* {(user?.isSuperAdmin || userSubModules?.includes(productAbbreviation)) && (
            <div className="flex w-full items-center justify-end">
              <button
                type="button"
                className="flex"
                onClick={() => {
                  handleClickOpenDialog(true, filteredArray[0]);
                }}
              >
                <p className="text-blue-500 underline">Edit</p>
              </button>
            </div>
          )} */}
        </div>
        {memoizedComponent}
        <div className="flex justify-center">
          <Tabs
            value={tab}
            onChange={(event, newValue) => setParam({ tab: newValue })}
            centered
            className="text-black dark:text-white"
          >
            <Tab label="Active" value="0" />
            <Tab label="TrendPhysical" value="1" />
            <Tab label="Trend Capacity" value="2" />
          </Tabs>
        </div>
        <div className="rounded-md bg-white px-2 text-black">
          {tab === '0' && (
            <ReactApexcharts options={activeOptions} series={activeSeries} height={200} />
          )}
          {tab === '1' && (
            <ReactApexcharts
              options={options}
              series={series('total_circuit')}
              type="bar"
              height={200}
            />
          )}
          {tab === '2' && (
            <ReactApexcharts
              options={options}
              series={series('total_capacity')}
              type="bar"
              height={200}
            />
          )}
        </div>
      </div>
      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle>{!editModeDialog ? 'New' : 'Edit'} Physical Data</DialogTitle>
        <DialogContent>
          <div className="flex flex-col gap-4 p-2">
            <TextInput
              name="total_capacity_new_install"
              value={dialogData?.total_capacity_new_install}
              placeholder="Total Capacity (New Install)"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="total_capacity_termination"
              value={dialogData?.total_capacity_termination}
              placeholder="Total Capacity (Termination)"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="total_circuit_new_install"
              value={dialogData?.total_circuit_new_install}
              placeholder="Total Circuit (New Install)"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="total_circuit_termination"
              value={dialogData?.total_circuit_termination}
              placeholder="Total Circuit (Termination)"
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
              Cancel
            </button>
            <div className="flex gap-2">
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="rounded-[4px] bg-[#d43801] p-2 font-semibold text-white"
                >
                  Save
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="rounded-[4px] bg-[#d43801] p-2 font-semibold text-white"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default PhysicalSection;
