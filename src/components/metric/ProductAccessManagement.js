// Next, React, Tw
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Packages
import PropTypes from 'prop-types';
import * as R from 'ramda';

// Components
import { TablePaginationCustom } from '../Shared/table';
import ProductAccessManagementRow from './ProductAccessManagementRow';
import { SearchInput, SelectInput } from '../Shared/CustomInput';

// Others
import axios from '../../utils/axios';
import { AUM_ENDPOINT } from '../../utils/aum';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { setIsLoading } from '../../utils/store/loadingReducer';

const ProductAccessManagement = ({ PRODUCTS, module }) => {
  // Standards
  const { setParam } = useParamContext();
  const { query } = useRouter();
  const { q, selectedDivision } = query;
  const dispatch = useDispatch();
  const { isLoading } = useSelector((state) => state.loading);

  // Table
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(10);
  const [tableData, setTableData] = useState([]);
  const onChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  })();

  const headerCellStyle =
    'whitespace-nowrap bg-[#408ed0] px-4 text-center text-white text-sm font-semibold';

  // Others
  const [divisionList, setDivisionList] = useState([]);

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${AUM_ENDPOINT}/user/v1/module/metric/all`);
      let filtered_user = response.data.data;
      setDivisionList(R.uniq(R.pluck('division', response.data.data)));

      if (selectedDivision !== 'All' && selectedDivision !== undefined) {
        filtered_user = filtered_user.filter((o) => o?.division === selectedDivision);
      }

      filtered_user = filtered_user.filter(
        (o) => o.modules?.find((m) => m.module === 'metric')?.role !== 'admin'
      );

      filtered_user.reverse();
      setTableData(filtered_user);
    } catch (error) {
      console.error('Error fetching data:', error);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, [selectedDivision]);

  return (
    <div className="container mx-auto p-1 md:p-4">
      <div
        style={{ boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)' }}
        className="flex flex-col gap-4 rounded-xl bg-white p-4 dark:bg-gray-600 dark:text-white"
      >
        <p className="w-full text-center text-3xl font-bold md:text-left">
          Product Access Management
        </p>
        {!isLoading && (
          <>
            <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
              <div className="flex flex-col items-center gap-2 md:flex-row">
                <SelectInput
                  value={selectedDivision}
                  placeholder="Filter by Division"
                  options={['All', ...divisionList]}
                  onChange={(event) => setParam({ selectedDivision: event.target.value })}
                />
              </div>

              <SearchInput />
            </div>

            <div className="my-4 overflow-x-auto scrollbar">
              <table className="min-w-full bg-white text-black">
                <thead>
                  <tr>
                    <td rowSpan={2} className={headerCellStyle}>
                      No.
                    </td>
                    <td rowSpan={2} className={headerCellStyle}>
                      Name
                    </td>
                    <td rowSpan={2} className={headerCellStyle}>
                      Staff ID
                    </td>
                    <td colSpan={3} className={headerCellStyle}>
                      Products
                    </td>
                  </tr>
                  <tr>
                    <td className={headerCellStyle}>Name</td>
                    <td className={headerCellStyle}>Status</td>
                    <td className={headerCellStyle}>Action</td>
                  </tr>
                </thead>
                <tbody>
                  {filteredTableData
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((row, i) => (
                      <ProductAccessManagementRow
                        PRODUCTS={PRODUCTS}
                        module={module}
                        rowData={row}
                        i={i}
                        rowsPerPage={rowsPerPage}
                        page={page}
                      />
                    ))}
                </tbody>
              </table>
            </div>
            <TablePaginationCustom
              count={tableData.length}
              page={page}
              rowsPerPage={rowsPerPage}
              onPageChange={onChangePage}
            />
          </>
        )}
      </div>
    </div>
  );
};

ProductAccessManagement.propTypes = {
  PRODUCTS: PropTypes.array,
  module: PropTypes.string,
};

export default ProductAccessManagement;
