// Next, React, Tw
import { useState, useEffect } from 'react';

// Packages
import PropTypes from 'prop-types';
import { Editor } from 'react-draft-wysiwyg';
import { EditorState, convertToRaw, ContentState } from 'draft-js';
import htmlToDraft from 'html-to-draftjs';
import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
import draftToHtml from 'draftjs-to-html';

const HtmlEditor = ({ name, value, onChange, placeholder = '', disabled = false }) => {
  // Standard and Vars
  const convertHtmlToEditorState = (html) => {
    if (!html) return EditorState.createEmpty();
    const blocksFromHtml = htmlToDraft(html);
    const { contentBlocks, entityMap } = blocksFromHtml;
    const contentState = ContentState.createFromBlockArray(contentBlocks, entityMap);
    return EditorState.createWithContent(contentState);
  };

  const [editorState, setEditorState] = useState(() => convertHtmlToEditorState(value));

  useEffect(() => {
    setEditorState(convertHtmlToEditorState(value));
  }, [value]);

  return (
    <div className="min-h-[300px] w-full rounded-md border p-2">
      <Editor
        editorState={editorState}
        onEditorStateChange={(newState) => setEditorState(newState)}
        onBlur={() => {
          if (onChange)
            onChange({
              target: {
                name,
                value: draftToHtml(convertToRaw(editorState.getCurrentContent())),
              },
            });
        }}
        toolbar={{
          options: ['inline', 'blockType', 'fontSize', 'list', 'textAlign', 'link', 'history'],
        }}
        placeholder={placeholder}
        toolbarHidden={disabled}
        readOnly={disabled}
      />
    </div>
  );
};

HtmlEditor.propTypes = {
  name: PropTypes.string,
  value: PropTypes.any,
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
  disabled: PropTypes.bool,
};

export default HtmlEditor;
