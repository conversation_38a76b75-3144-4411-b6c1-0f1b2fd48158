// Next, React, Tw
import { useState } from 'react';

// Mui
import { Tooltip } from '@mui/material';
import { AttachMoney } from '@mui/icons-material';

// Packages
import PropTypes from 'prop-types';
import { TailSpin } from 'react-loader-spinner';
import { useSnackbar } from 'notistack';

// Components
import { IconButton } from '../../fa/ui/EnhancedButton';

// Others
import { checkAndReplaceNumberWithZero } from '../../../utils/shared';
import { AUM_ENDPOINT } from '../../../utils/aum';
import axios from '../../../utils/axios';

const FetchUsdToMyrExchangeRateButton = ({ onChange, disabled = false }) => {
  // Standard and Vars
  const [isLocallyLoading, setIsLocallyLoading] = useState(false);
  const { enqueueSnackbar } = useSnackbar();

  return (
    <IconButton
      variant="primary"
      size="normal"
      disabled={disabled || isLocallyLoading}
      loading={isLocallyLoading}
      tooltip="Fetch USD → MYR from BNM"
      onClick={async () => {
        try {
          setIsLocallyLoading(true);
          const response = await axios.get(`${AUM_ENDPOINT}/exchange_rate/v1/latest/keyword`);
          if (response?.data?.data?.[0]?.data?.USD && onChange)
            onChange(checkAndReplaceNumberWithZero(response?.data?.data?.[0]?.data?.USD, 2));
        } catch (error) {
          enqueueSnackbar(`Something went wrong`, {
            variant: 'error',
          });
        }
        setIsLocallyLoading(false);
      }}
    >
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
      </svg>
    </IconButton>
  );
};

FetchUsdToMyrExchangeRateButton.propTypes = {
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
};

export default FetchUsdToMyrExchangeRateButton;
