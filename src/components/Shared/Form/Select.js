import React from 'react';
import PropTypes from 'prop-types';

const Select = ({ label, value, onChange, children, className = '' }) => (
  <div className={`flex flex-col ${className}`}>
    {label && (
      <label htmlFor={label} className="mb-1 text-sm font-medium text-gray-700">
        {label}
      </label>
    )}
    <select
      id={label}
      value={value}
      onChange={onChange}
      className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
    >
      {children}
    </select>
  </div>
);

Select.propTypes = {
  label: PropTypes.string,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
};

export default Select;
