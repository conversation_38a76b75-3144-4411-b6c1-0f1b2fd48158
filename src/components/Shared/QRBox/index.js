// Next, React, Tw
import { useRef } from 'react';

// mui
import { Tooltip } from '@mui/material';
import { GetApp } from '@mui/icons-material';

// Packages
import { QRCodeCanvas } from 'qrcode.react';
import PropTypes from 'prop-types';

const QRBox = ({ key, value, size, showDownloadButton = false, ...other }) => {
  // Standard and Vars
  const qrCodeCanvas = useRef(null);

  return (
    <div ref={qrCodeCanvas} className="flex flex-col gap-2 bg-white p-2">
      {showDownloadButton && (
        <div className="flex justify-end">
          <button
            type="button"
            className="hover:scale-110"
            onClick={() => {
              const canvas = qrCodeCanvas.current.querySelector('canvas');
              const padding = 20;

              const paddedCanvas = document.createElement('canvas');
              paddedCanvas.width = canvas.width + padding * 2;
              paddedCanvas.height = canvas.height + padding * 2;

              const ctx = paddedCanvas.getContext('2d');

              ctx.fillStyle = 'white';
              ctx.fillRect(0, 0, paddedCanvas.width, paddedCanvas.height);

              ctx.drawImage(canvas, padding, padding);

              const imageURL = paddedCanvas.toDataURL('image/jpeg', 1.0);

              const link = document.createElement('a');
              link.href = imageURL;
              link.download = 'QR.jpg';
              link.click();
            }}
          >
            <Tooltip title="Download QR">
              <GetApp className="text-black" />
            </Tooltip>
          </button>
        </div>
      )}

      <QRCodeCanvas
        key={key}
        value={value}
        size={size}
        {...other}
        renderAs="canvas"
        className="m-auto "
      />
    </div>
  );
};

QRBox.propTypes = {
  key: PropTypes.any,
  value: PropTypes.string,
  size: PropTypes.number,
  showDownloadButton: PropTypes.bool,
};

export default QRBox;
