// Next, React, Tw
import { useRouter } from 'next/router';

// Mui
import { KeyboardArrowDown, KeyboardArrowUp, FilterAlt } from '@mui/icons-material';

// Packages
import PropTypes from 'prop-types';

// Others
import { useParamContext } from '../../../utils/auth/ParamProvider';

const SortButton = ({ objectKey }) => {
  // Standard and Vars
  const { query } = useRouter();
  const { sortedByKey, sortInAscendingOrder } = query;
  const { setParam } = useParamContext();

  // Others
  return (
    <button
      type="button"
      onClick={() => {
        if (!sortedByKey || sortedByKey !== objectKey) {
          setParam({ sortedByKey: objectKey, sortInAscendingOrder: 'true' });
          return;
        }
        if (sortInAscendingOrder === 'true') {
          setParam({ sortInAscendingOrder: 'false' });
          return;
        }
        setParam({ sortInAscendingOrder: 'true' });
      }}
      className="pl-1"
    >
      {(!sortedByKey || sortedByKey !== objectKey) && <FilterAlt className="h-[20px]" />}
      {objectKey === sortedByKey && sortInAscendingOrder === 'true' && (
        <KeyboardArrowDown className="h-[20px]" />
      )}
      {objectKey === sortedByKey && sortInAscendingOrder === 'false' && (
        <KeyboardArrowUp className="h-[20px]" />
      )}
    </button>
  );
};

SortButton.propTypes = {
  objectKey: PropTypes.string,
};

export default SortButton;
