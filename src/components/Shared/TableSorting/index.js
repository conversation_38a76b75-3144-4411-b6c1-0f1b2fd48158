import { useState } from 'react';
import { ArrowUpward, ArrowDownward, UnfoldMore } from '@mui/icons-material';

const TableSorting = ({ data, onSort, children }) => {
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });

  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    
    const sortedData = [...data].sort((a, b) => {
      let aValue = a[key];
      let bValue = b[key];

      // Handle different data types
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      if (typeof aValue === 'number') {
        return direction === 'asc' ? aValue - bValue : bValue - aValue;
      }
      
      // Handle dates
      if (key.includes('date') || key.includes('updated')) {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
        return direction === 'asc' ? aValue - bValue : bValue - aValue;
      }
      
      // Handle strings
      if (aValue < bValue) return direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return direction === 'asc' ? 1 : -1;
      return 0;
    });

    setSortConfig({ key, direction });
    onSort(sortedData);
  };

  const getSortIcon = (columnKey) => {
    if (sortConfig.key !== columnKey) {
      return <UnfoldMore className="ml-1 h-4 w-4 text-gray-400" />;
    }
    return sortConfig.direction === 'asc' 
      ? <ArrowUpward className="ml-1 h-4 w-4 text-white" />
      : <ArrowDownward className="ml-1 h-4 w-4 text-white" />;
  };

  return children({ handleSort, getSortIcon });
};

export const SortableTableHeader = ({ label, sortKey, handleSort, getSortIcon, className }) => (
  <td 
    className={`${className} cursor-pointer hover:bg-opacity-80 transition-colors select-none`}
    onClick={() => handleSort(sortKey)}
  >
    <div className="flex items-center justify-center">
      <span>{label}</span>
      {getSortIcon(sortKey)}
    </div>
  </td>
);

export default TableSorting;