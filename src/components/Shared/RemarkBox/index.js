// Next, React, Tw
import { useState, useEffect, useRef } from 'react';
import { twMerge } from 'tailwind-merge';

// Mui
import { Send, Person } from '@mui/icons-material';

// Packages
import * as yup from 'yup';
import PropTypes from 'prop-types';

// Others
import axios from '../../../utils/axios';
import { useSnackbar } from '../snackbar';
import { useAuthContext } from '../../../utils/auth/useAuthContext';
import { useSimiContext } from '../../../utils/simi';

const RemarkBox = ({
  parentHiddenDivRef,
  documentIdKey,
  documentIdValue,
  GET_ALL_REMARKS_ENDPOINT,
  POST_NEW_REMARK_ENDPOINT,
  CALLBACK_UPON_REMARK,
  isExporting = false,
}) => {
  // Standard
  const { user } = useAuthContext();
  const { enqueueSnackbar } = useSnackbar();
  const hiddenDivRef = useRef(null);
  const { moduleColorCode } = useSimiContext();

  const [remarkList, setRemarkList] = useState([]);

  // Form
  const schema = yup.object({
    created_by: yup.string().required('Please provide created by.')?.default(user.name),
    remark: yup.string().required('Please provide remark.'),
  });

  const [formData, setFormData] = useState({});
  const handleformDataChange = (event) => {
    const { name, value } = event.target;
    setFormData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const handleActionButtonClick = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(
          { ...formData, [documentIdKey]: documentIdValue },
          { abortEarly: false }
        );
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }
      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(POST_NEW_REMARK_ENDPOINT, payload);
            break;
          default:
            break;
        }

        if (response.data.status === 'success') {
          switch (action) {
            case 'post':
              if (CALLBACK_UPON_REMARK) CALLBACK_UPON_REMARK();
              break;
            default:
              break;
          }
        }

        let statusVariant = 'error';
        let message = 'Failed';
        if (response?.data?.status === 'success') {
          statusVariant = 'success';
          message = 'Success';
        }

        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch (error) {
        enqueueSnackbar('Something went wrong', {
          variant: 'error',
        });
      }
    }
    setFormData((prev) => ({ ...prev, remark: '' }));
    fetchData();
  };

  // Others

  const fetchData = async () => {
    try {
      const response = await axios.get(GET_ALL_REMARKS_ENDPOINT);
      if (response.data.status === 'success') {
        const temp = [];
        for (let i = 0; i < response.data.data.length; i += 1) {
          if (response.data.data[i]?.created_by === response.data.data[i + 1]?.created_by) {
            temp.push({ ...response.data.data[i], icon_needed: true });
          } else {
            temp.push(response.data.data[i]);
          }
        }
        setRemarkList(temp);
      }
    } catch (error) {
      setRemarkList([]);
    }
  };

  useEffect(() => {
    hiddenDivRef.current?.scrollIntoView();
    if (parentHiddenDivRef) parentHiddenDivRef?.current?.scrollIntoView();
    else document.getElementById('scroll-container').scrollTo(0, 0);
  }, []);

  useEffect(() => {
    fetchData();
    if (process?.env?.NODE_ENV === 'development') return;
    const timer = setInterval(async () => fetchData(), 5000);
    // eslint-disable-next-line consistent-return
    return () => {
      clearInterval(timer);
    };
  }, []);

  return (
    <div
      className={twMerge(
        'flex  w-full flex-col justify-between rounded-lg border border-slate-300 bg-white',
        !isExporting ? 'h-[300px]' : 'min-h-[300px]'
      )}
    >
      <div
        className={twMerge(
          'flex flex-grow flex-col px-4',
          !isExporting && 'overflow-y-scroll scrollbar-none'
        )}
      >
        <p className="w-full pt-4 text-center text-sm">Remarks</p>
        {remarkList.map((o, i) => (
          <div key={i} className="flex w-full items-center gap-1">
            <div className="min-h-[30px] min-w-[30px]">
              {!o?.icon_needed && <Person className="h-[30px] w-[30px]" />}
            </div>
            <div className="flex w-full flex-col px-2 py-1" key={i}>
              <div className="flex w-full justify-between">
                <p className="text-[10px]">{o?.created_by}</p>
              </div>
              <div className="flex w-full">
                <p
                  className="rounded-r-md px-2  py-[2px] text-xs text-white"
                  style={{ backgroundColor: moduleColorCode }}
                >
                  {o?.remark}
                </p>
              </div>
              <div className="flex w-full justify-between">
                <p className="text-[8px]">{o?.created_at}</p>
              </div>
            </div>
          </div>
        ))}
        <div ref={hiddenDivRef} />
      </div>
      {!isExporting && (
        <form
          onSubmit={(event) => {
            event.preventDefault();
          }}
          className="flex items-center justify-between p-1"
        >
          <textarea
            rows={1}
            placeholder="Type a remark..."
            className="flex-grow bg-white  p-1 text-xs text-black focus:outline-none"
            autoComplete="off"
            name="remark"
            value={formData?.remark}
            onChange={handleformDataChange}
          />
          <button type="button" onClick={() => handleActionButtonClick('post')}>
            <Send />
          </button>
        </form>
      )}
    </div>
  );
};

RemarkBox.propTypes = {
  parentHiddenDivRef: PropTypes.any,
  documentIdKey: PropTypes.string,
  documentIdValue: PropTypes.string,
  GET_ALL_REMARKS_ENDPOINT: PropTypes.string,
  POST_NEW_REMARK_ENDPOINT: PropTypes.string,
  CALLBACK_UPON_REMARK: PropTypes.func,
  isExporting: PropTypes.bool,
};

export default RemarkBox;
