// Next, React, Tw
import Link from 'next/link';
import { useState, useEffect } from 'react';

// Mui
import { Popover } from '@mui/material';
import { ArrowOutward } from '@mui/icons-material';

// Packages
import PropTypes from 'prop-types';

// Components
import ProfilePicture from '../User/ProfilePicture';
import ProfileDetails from '../User/ProfileDetails';

// Others
import axios from '../../../utils/axios';
import { AUM_ENDPOINT } from '../../../utils/aum';
import { useAuthContext } from '../../../utils/auth/useAuthContext';

const UserPopup = ({ label, staff_id }) => {
  // Standard and Vars
  const { user } = useAuthContext();
  const [popOverPosition, setPopOverPosition] = useState(null);

  const [userData, setUserData] = useState({});

  // Others
  const processedLabel = ['', undefined, null]?.includes(label) ? '-' : label;

  const fetchData = async () => {
    try {
      const response = await axios.get(`${AUM_ENDPOINT}/user/v1/${staff_id}`);
      setUserData(response.data.data[response.data.data.length - 1]);
    } catch {
      setUserData({});
    }
  };

  useEffect(() => {
    if (!staff_id) return;
    fetchData();
  }, [staff_id]);

  return (
    <>
      <button
        type="button"
        onClick={(event) =>
          setPopOverPosition(popOverPosition === null ? event.currentTarget : null)
        }
      >
        {processedLabel}
      </button>
      <Popover
        open={popOverPosition !== null}
        anchorEl={popOverPosition}
        onClose={() => setPopOverPosition(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        transformOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <div className="relative flex flex-col gap-4 p-4">
          {user?.isSuperAdmin && (
            <Link href={`/dashboard/view-user/${staff_id}`} className="absolute right-0 top-0 m-2 ">
              <ArrowOutward className="h-[20px]" />
            </Link>
          )}

          <div className="flex h-auto w-full flex-col items-center justify-center gap-2">
            <ProfilePicture userObject={userData} />
          </div>
          <div className="flex w-full flex-col gap-3">
            <ProfileDetails userObject={userData} />
          </div>
        </div>
      </Popover>
    </>
  );
};

UserPopup.propTypes = {
  label: PropTypes.string,
  staff_id: PropTypes.string,
};

export default UserPopup;
