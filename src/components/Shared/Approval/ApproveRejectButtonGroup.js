// Next, React, Tailwind
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';

// Packages
import PropTypes from 'prop-types';

// Components
import { useSnackbar } from '../snackbar';

// Others
import axios from '../../../utils/axios';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { toUpperCaseFirstLetter } from '../../../utils/shared';
import { useAuthContext } from '../../../utils/auth/useAuthContext';
import { useSimiContext } from '../../../utils/simi';

const ApproveRejectButtonGroup = ({
  GET_ALL_APPROVALS_ENDPOINT,
  UPDATE_CERTAIN_APPROVAL_ENDPOINT,
  REVERT_CALLBACK,
  SUBMIT_CALLBACK,
  APPROVE_CALLBACK,
  NAME,
  disabled = false,
  btnClassOverrides = {},
  dialogTitleClass = '',
  dialogButtonClass = '',
  dialogProceedClass = '',
  dialogCancelClass = '',
  documentName = 'Project'
}) => {
  // Standard and Vars
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuthContext();
  const { asPath } = useRouter();
  const { handleSendEmail, getCertainStaffInfoFromStaffId, moduleColorCode } = useSimiContext();

  const [approvalsData, setApprovalsData] = useState([]);
  const [type, setType] = useState(null);

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);

  // Others
  const currentStageIndex = approvalsData?.findIndex((o) => o?.status?.toLowerCase() === 'pending');

  const currentStageData = approvalsData?.[currentStageIndex];

  const handleRevert = async () => {
    try {
      // Update Approvals
      for (let i = 0; i < approvalsData?.length; i += 1) {
        await axios.put(`${UPDATE_CERTAIN_APPROVAL_ENDPOINT}/${approvalsData[i]?.id}`, {
          ...approvalsData[i],
          status: 'pending',
        });
      }

      // Send E-Mail
      const recipientsList = approvalsData?.[0]?.user_name_array;
      const ccsList = [];

      await handleSendEmail(
        recipientsList?.map((o) => getCertainStaffInfoFromStaffId(o, 'email')),
        ccsList?.map((o) => getCertainStaffInfoFromStaffId(o, 'email')),
        `${NAME} - ${type === 'revert' ? 'REVERTED' : 'ESCALATED'}`,
        `
          <p>${NAME} has been ${type === 'revert' ? 'reverted' : 'escalated'} to you.</p>\n
          <p>Kindly, please click this <a href="${process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT}${asPath}">link</a> to review it.</p>
        `
      );

      // Callback
      if (REVERT_CALLBACK) REVERT_CALLBACK();

      // Success
      enqueueSnackbar('Success!', {
        variant: 'success',
      });
    } catch {
      enqueueSnackbar('Something went wrong!', {
        variant: 'error',
      });
    }
    fetchData();
  };

  const handleSubmit = async () => {
    try {
      // Update Approval
      await axios.put(`${UPDATE_CERTAIN_APPROVAL_ENDPOINT}/${currentStageData?.id}`, {
        ...currentStageData,
        status: 'done',
      });

      // Send E-Mail
      const recipientsList = approvalsData?.[currentStageIndex + 1]?.user_name_array;
      const ccsList = (() => {
        const temp = [];
        for (let i = 0; i < currentStageIndex + 1; i += 1) {
          temp.push(...approvalsData?.[i]?.user_name_array);
        }
        return temp;
      })();

      await handleSendEmail(
        recipientsList?.map((o) => getCertainStaffInfoFromStaffId(o, 'email')),
        ccsList?.map((o) => getCertainStaffInfoFromStaffId(o, 'email')),
        `${NAME} - ${type === 'revert' ? 'REVERTED' : 'ESCALATED'}`,
        `
          <p>${NAME} has been ${type === 'revert' ? 'reverted' : 'escalated'} to you.</p>\n
          <p>Kindly, please click this <a href="${process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT}${asPath}">link</a> to review it.</p>
        `
      );

      // Callback
      if (SUBMIT_CALLBACK)
        SUBMIT_CALLBACK(`pending ${approvalsData?.[currentStageIndex + 1]?.type}`);

      enqueueSnackbar('Success!', {
        variant: 'success',
      });
    } catch {
      enqueueSnackbar('Something went wrong!', {
        variant: 'error',
      });
    }
    fetchData();
  };

  const handleApprove = async () => {
    try {
      // Update Approval
      await axios.put(`${UPDATE_CERTAIN_APPROVAL_ENDPOINT}/${currentStageData?.id}`, {
        ...currentStageData,
        status: 'done',
      });

      // Send E-Mail
      const getRecipientList = () => approvalsData?.[currentStageIndex + 1]?.user_name_array;
      const getCCList = () => {
        const temp = [];
        for (let i = 0; i < currentStageIndex + 1; i += 1) {
          temp.push(...approvalsData?.[i]?.user_name_array);
        }
        return temp;
      };

      await handleSendEmail(getRecipientList(), getCCList());

      // Callback

      if (APPROVE_CALLBACK)
        APPROVE_CALLBACK(
          approvalsData?.[currentStageIndex + 1]?.type
            ? `pending ${approvalsData?.[currentStageIndex + 1]?.type}`
            : 'completed'
        );

      enqueueSnackbar('Success!', {
        variant: 'success',
      });
    } catch {
      enqueueSnackbar('Something went wrong!', {
        variant: 'error',
      });
    }
    fetchData();
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${GET_ALL_APPROVALS_ENDPOINT}`);
      if (response.data.data) {
        setApprovalsData(response.data.data);
      }
    } catch {
      setApprovalsData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  if (disabled) return null;

  return (
    <>
      {currentStageData?.user_name_array?.includes(user?.name) && (
        <div className="flex flex-col items-center gap-2 md:flex-row">
          {currentStageIndex === 0 && (
            <button
              type="button"
              className={`cta-btn ${btnClassOverrides.submit || 'bg-green-500'}`}
              onClick={() => {
                setType('submit');
                setDialogOpen(true);
              }}
            >
              Submit
            </button>
          )}
          {currentStageIndex !== 0 && (
            <>
              <button
                type="button"
                className={`cta-btn ${btnClassOverrides.revert || 'bg-red-500'}`}
                onClick={() => {
                  setType('revert');
                  setDialogOpen(true);
                }}
              >
                Revert
              </button>
              <button
                type="button"
                className={`cta-btn ${btnClassOverrides.approve || 'bg-green-500'}`}
                onClick={() => {
                  setType('approve');
                  setDialogOpen(true);
                }}
              >
                Approve
              </button>
            </>
          )}
        </div>
      )}

      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)}>
        <DialogTitle 
          className={`text-center text-white ${dialogTitleClass || ''}`} 
          sx={{ backgroundColor: dialogTitleClass ? 'transparent' : moduleColorCode }}
        >
          {toUpperCaseFirstLetter(type)} {documentName}
        </DialogTitle>
        <DialogContent>
          <div className="mt-8 flex w-full flex-col items-center gap-4 p-2 md:w-[400px]">
            Proceed?
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <button 
              type="button" 
              onClick={() => setDialogOpen(false)} 
              className={`p-2 ${dialogButtonClass || ''} ${dialogCancelClass || ''}`}
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={async () => {
                if (type === 'revert') handleRevert();
                else if (type === 'submit') handleSubmit();
                else if (type === 'approve') handleApprove();
                setDialogOpen(false);
              }}
              className={`bg-fa cta-btn ${dialogButtonClass || ''} ${dialogProceedClass || ''}`}
            >
              Proceed
            </button>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

ApproveRejectButtonGroup.propTypes = {
  GET_ALL_APPROVALS_ENDPOINT: PropTypes.string.isRequired,
  UPDATE_CERTAIN_APPROVAL_ENDPOINT: PropTypes.string.isRequired,
  REVERT_CALLBACK: PropTypes.func,
  SUBMIT_CALLBACK: PropTypes.func,
  APPROVE_CALLBACK: PropTypes.func,
  NAME: PropTypes.string,
  disabled: PropTypes.bool,
  btnClassOverrides: PropTypes.object,
  dialogTitleClass: PropTypes.string,
  dialogButtonClass: PropTypes.string,
  dialogProceedClass: PropTypes.string,
  dialogCancelClass: PropTypes.string,
  documentName: PropTypes.string
};

export default ApproveRejectButtonGroup;
