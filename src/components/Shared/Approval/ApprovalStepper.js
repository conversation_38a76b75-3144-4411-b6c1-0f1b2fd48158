// Next, React, Tw
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Mui
import { Stepper, Step, StepLabel } from '@mui/material';

// Packages
import PropTypes from 'prop-types';

// Components
import UserPopup from '../UserPopup';

// Others
import axios from '../../../utils/axios';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { toUpperCaseFirstLetter, checkAndReplaceStringWithHyphen } from '../../../utils/shared';
import { useSimiContext } from '../../../utils/simi';

// ----------------------------------------------------------------------

const ApprovalStepLabel = ({ approval }) => {
  // Standards and Vars
  const { allStaffs } = useSelector((state) => state.aum);

  const [popOverPosition, setPopOverPosition] = useState(null);

  // Function to format names
  const formatName = (name) => {
    if (checkAndReplaceStringWithHyphen(name) === '-') return '-';
    
    const nameParts = name?.split(' ') || [];
    
    // Make all name parts uppercase for all approval types
    return nameParts.map(part => part.toUpperCase()).join(' ');
  };

  return (
    <StepLabel
      onClick={(event) => setPopOverPosition(popOverPosition === null ? event.currentTarget : null)}
      style={{ cursor: 'pointer' }}
    >
      <p className="font-semibold text-black">{approval?.type?.toUpperCase()}</p>
      {approval?.user_name_array?.map((o, i) => (
        <UserPopup
          key={i}
          label={formatName(o)}
          staff_id={allStaffs?.find((p) => p?.name === o)?.staff_id}
        />
      ))}
    </StepLabel>
  );
};

ApprovalStepLabel.propTypes = {
  approval: PropTypes.object,
};

// ----------------------------------------------------------------------

const ApprovalStepper = ({ GET_ALL_APPROVALS_ENDPOINT }) => {
  // Standard
  const dispatch = useDispatch();
  const { moduleColorCode } = useSimiContext();

  const [approvalsData, setApprovalsData] = useState([]);
  const [updateCounter, setUpdateCounter] = useState(0);

  // Others

  const activeStepNumber = (() => {
    const temp = approvalsData?.findIndex((o) => o?.status?.toLowerCase() === 'pending');
    if (temp === approvalsData?.length - 1) return temp + 1;
    return temp;
  })();

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${GET_ALL_APPROVALS_ENDPOINT}`);
      if (response.data.data) {
        setApprovalsData([
          ...response.data.data,
          {
            type: 'completed',
            status: 'pending',
          },
        ]);
      }
    } catch {
      setApprovalsData([]);
    }
    dispatch(setIsLoading(false));
  };

  // Listen for approval updates
  useEffect(() => {
    // Event handler for approval updates
    const handleApprovalUpdate = (event) => {
      // Check if this stepper should respond to the event
      if (event.detail.endpoint === GET_ALL_APPROVALS_ENDPOINT) {
        fetchData();
      } else {
        // If endpoints don't match exactly, compare project_id or other identifiers
        const currentEndpointParts = GET_ALL_APPROVALS_ENDPOINT.split('/');
        const updatedEndpointParts = event.detail.endpoint.split('/');
        
        // Extract IDs from endpoints (assuming they're at the end of the path)
        const currentId = currentEndpointParts[currentEndpointParts.length - 1];
        const updatedId = updatedEndpointParts[updatedEndpointParts.length - 1];
        
        if (currentId === updatedId) {
          fetchData();
        }
      }
    };

    // Register event listener
    window.addEventListener('approvalUpdated', handleApprovalUpdate);

    // Cleanup
    return () => {
      window.removeEventListener('approvalUpdated', handleApprovalUpdate);
    };
  }, [GET_ALL_APPROVALS_ENDPOINT]);

  // Fetch initial data
  useEffect(() => {
    fetchData();
  }, [updateCounter]);

  // Add a manual refresh function that any parent component can call
  const refreshStepper = () => {
    setUpdateCounter(prev => prev + 1);
  };

  return (
    <>
      {approvalsData?.length > 0 && (
        <div className="w-full overflow-x-scroll md:overflow-x-auto">
          <Stepper activeStep={activeStepNumber} alternativeLabel>
            {approvalsData.map((o, i) => (
              <Step
                key={i}
                sx={{
                  '& .MuiStepLabel-root .Mui-completed': {
                    color: moduleColorCode,
                  },
                  '& .MuiStepLabel-root .Mui-active': {
                    color: moduleColorCode,
                  },
                }}
              >
                <ApprovalStepLabel approval={o} />
              </Step>
            ))}
          </Stepper>
        </div>
      )}
    </>
  );
};

// Expose the refresh function
ApprovalStepper.refreshStepper = () => {
  window.dispatchEvent(new CustomEvent('approvalUpdated', { detail: { refreshAll: true } }));
};

ApprovalStepper.propTypes = {
  GET_ALL_APPROVALS_ENDPOINT: PropTypes.string,
};

export default ApprovalStepper;
