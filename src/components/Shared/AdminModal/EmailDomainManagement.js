// React
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Mui
import {
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Avatar,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Edit,
  Add,
  Domain,
  Settings,
  Delete,
  AlternateEmail
} from '@mui/icons-material';

// Components
import { TextInput } from '../CustomInput';
import { AdminCard, AdminTable } from './components';

// Utils
import axios from '../../../utils/axios';
import { useSnackbar } from '../snackbar';
import { AUM_ENDPOINT } from '../../../utils/aum';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { checkAndReplaceStringWithHyphen } from '../../../utils/shared';

// ----------------------------------------------------------------------

export default function EmailDomainManagement() {
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();

  // Table
  const [currentPage, setCurrentPage] = useState(0);
  const [itemsPerPage] = useState(15);
  const [tableData, setTableData] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');

  const filteredData = (() => {
    if (!searchQuery) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(searchQuery.toLowerCase()));
  })();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const INITIAL_DIALOG_DATA = {
    abbreviation: '',
    domain_name: '',
  };

  const [dialogData, setDialogData] = useState(INITIAL_DIALOG_DATA);

  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const handleClickOpenDialog = (editMode, data = null) => {
    if (editMode && data) {
      setDialogData(data);
    } else {
      setDialogData(INITIAL_DIALOG_DATA);
    }
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  
  const handleDialogClose = async (action) => {
    if (action) {
      try {
        if (!dialogData.abbreviation || !dialogData.domain_name) {
          enqueueSnackbar('Please fill in all required fields.', { variant: 'error' });
          return;
        }

        const submitData = {
          ...dialogData,
          domain_name: dialogData?.domain_name?.toLowerCase().trim()
        };

        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${AUM_ENDPOINT}/domain/v1`, submitData);
            break;
          case 'put':
            response = await axios.put(`${AUM_ENDPOINT}/domain/v1/${dialogData.id}`, submitData);
            break;
          case 'delete':
            response = await axios.delete(`${AUM_ENDPOINT}/domain/v1/${dialogData.id}`);
            break;
          default:
            break;
        }

        let statusVariant;
        let message;
        if (response?.data?.status === 'success') {
          statusVariant = 'success';
          message = response.data.data || 'Operation completed successfully';
        } else {
          statusVariant = 'error';
          message = 'Operation failed';
        }
        enqueueSnackbar(message, { variant: statusVariant });
      } catch (error) {
        enqueueSnackbar('Operation failed', { variant: 'error' });
      }
    }

    setDialogData(INITIAL_DIALOG_DATA);
    setDialogOpen(false);
    fetchData();
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${AUM_ENDPOINT}/domain/v1/all`);
      if (response.data.data) {
        setTableData(response.data.data);
      } else {
        setTableData([]);
      }
    } catch (error) {
      setTableData([]);
      enqueueSnackbar('Failed to fetch domains', { variant: 'error' });
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (searchQuery) {
      setCurrentPage(0);
    }
  }, [searchQuery]);

  const paginatedData = filteredData.slice(
    currentPage * itemsPerPage,
    currentPage * itemsPerPage + itemsPerPage
  );

  const tableColumns = [
    { label: '#', width: '60px' },
    { label: 'Staff ID Abbreviation', width: 'auto' },
    { label: 'Domain', width: 'auto' },
    { label: 'Actions', width: '120px' }
  ];

  const renderTableRow = (row, index) => {
    return (
      <>
        <td className="px-6 py-4">
          <Typography variant="body2" className="text-gray-600 font-medium">
            {index + 1 + itemsPerPage * currentPage}
          </Typography>
        </td>

        <td className="px-6 py-4">
          <div className="flex items-center gap-3">
            <Avatar
              sx={{
                width: 40,
                height: 40,
                backgroundColor: '#06b6d415',
                color: '#06b6d4',
                fontSize: '14px',
                fontWeight: 600
              }}
            >
              <AlternateEmail fontSize="small" />
            </Avatar>
            <div>
              <Typography variant="body2" className="font-semibold text-gray-900">
                {checkAndReplaceStringWithHyphen(row?.abbreviation?.toUpperCase())}
              </Typography>
              <Typography variant="caption" className="text-gray-500">
                Domain ID: {row?.id}
              </Typography>
            </div>
          </div>
        </td>

        <td className="px-6 py-4">
          <Typography variant="body2" className="text-gray-700 font-mono">
            {row?.domain_name}
          </Typography>
        </td>

        <td className="px-6 py-4">
          <Tooltip title="Edit Domain">
            <IconButton
              onClick={() => handleClickOpenDialog(true, row)}
              sx={{
                width: 36,
                height: 36,
                backgroundColor: '#6366f115',
                color: '#6366f1',
                '&:hover': {
                  backgroundColor: '#6366f125',
                }
              }}
            >
              <Edit fontSize="small" />
            </IconButton>
          </Tooltip>
        </td>
      </>
    );
  };

  return (
    <>
      <div className="h-full">
        <div className="p-8 space-y-8">
          {/* Search and Actions */}
          <AdminCard
            title="Email Domain Controls"
            subtitle="Search and manage email domains"
            icon={Settings}
            iconColor="#06b6d4"
          >
            <div className="flex flex-col lg:flex-row gap-4 justify-between">
              <div className="flex-1">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search domains by abbreviation or domain name..."
                  className="w-full h-10 px-4 text-sm border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:border-cyan-500 focus:ring-2 focus:ring-cyan-200 transition-all outline-none"
                />
              </div>

              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={() => handleClickOpenDialog(false)}
                sx={{
                  borderRadius: '12px',
                  textTransform: 'none',
                  fontWeight: 600,
                  px: 3,
                  py: 1.5,
                  backgroundColor: '#06b6d4',
                  '&:hover': {
                    backgroundColor: '#0891b2',
                  },
                  boxShadow: '0 4px 6px -1px rgba(6, 182, 212, 0.3)'
                }}
              >
                New Domain
              </Button>
            </div>
          </AdminCard>

          {/* Enhanced Email Domain Table */}
          <AdminTable
            title="Email Domain Directory"
            subtitle={`${filteredData.length} domains found`}
            icon={Domain}
            iconColor="#06b6d4"
            columns={tableColumns}
            data={paginatedData}
            renderRow={renderTableRow}
            searchQuery={searchQuery}
            pagination={{
              currentPage,
              itemsPerPage,
              totalItems: filteredData.length,
              onPageChange: setCurrentPage
            }}
          />
        </div>
      </div>

      {/* Enhanced Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => handleDialogClose(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '16px',
            overflow: 'hidden'
          }
        }}
      >
        <DialogTitle sx={{
          background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',
          color: 'white',
          fontWeight: 700,
          fontSize: '18px',
          display: 'flex',
          alignItems: 'center',
          gap: 2
        }}>
          <Avatar sx={{ width: 32, height: 32, backgroundColor: 'rgba(255,255,255,0.2)' }}>
            {editModeDialog ? <Edit fontSize="small" /> : <Add fontSize="small" />}
          </Avatar>
          {editModeDialog ? 'Edit Email Domain' : 'Add New Email Domain'}
        </DialogTitle>

        <DialogContent sx={{ p: 4 }}>
          <div className="space-y-6 pt-2">
            <div>
              <Typography variant="body2" className="font-semibold text-gray-700 mb-2">
                Staff ID Abbreviation *
              </Typography>
              <TextInput
                name="abbreviation"
                value={dialogData?.abbreviation}
                placeholder="Enter staff ID abbreviation (e.g., TM, EXT)"
                onChange={handleDialogDataChange}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '12px',
                    backgroundColor: '#f9fafb',
                    '&:hover': {
                      backgroundColor: '#f8fafc',
                    },
                    '&.Mui-focused': {
                      backgroundColor: 'white',
                      boxShadow: '0 0 0 3px rgba(6, 182, 212, 0.1)',
                    }
                  }
                }}
              />
            </div>

            <div>
              <Typography variant="body2" className="font-semibold text-gray-700 mb-2">
                Domain Name *
              </Typography>
              <TextInput
                name="domain_name"
                value={dialogData?.domain_name}
                placeholder="Enter domain name (e.g., company.com)"
                onChange={handleDialogDataChange}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '12px',
                    backgroundColor: '#f9fafb',
                    '&:hover': {
                      backgroundColor: '#f8fafc',
                    },
                    '&.Mui-focused': {
                      backgroundColor: 'white',
                      boxShadow: '0 0 0 3px rgba(6, 182, 212, 0.1)',
                    }
                  }
                }}
              />
            </div>
          </div>
        </DialogContent>

        <DialogActions sx={{ p: 4, pt: 0, gap: 2 }}>
          {editModeDialog && (
            <Button
              onClick={() => handleDialogClose('delete')}
              startIcon={<Delete />}
              sx={{
                borderRadius: '8px',
                textTransform: 'none',
                fontWeight: 600,
                color: '#ef4444',
                backgroundColor: '#ef444415',
                '&:hover': {
                  backgroundColor: '#ef444425',
                }
              }}
            >
              Delete
            </Button>
          )}

          <div className="flex-1" />

          <Button
            onClick={() => handleDialogClose(false)}
            sx={{
              borderRadius: '8px',
              textTransform: 'none',
              fontWeight: 600,
              color: '#6b7280'
            }}
          >
            Cancel
          </Button>

          <Button
            onClick={() => handleDialogClose(editModeDialog ? 'put' : 'post')}
            variant="contained"
            sx={{
              borderRadius: '8px',
              textTransform: 'none',
              fontWeight: 600,
              backgroundColor: '#06b6d4',
              '&:hover': {
                backgroundColor: '#0891b2',
              }
            }}
          >
            {editModeDialog ? 'Update Domain' : 'Create Domain'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
