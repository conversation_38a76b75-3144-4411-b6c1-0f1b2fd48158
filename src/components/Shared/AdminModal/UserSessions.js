// React
import { useState, useEffect, useCallback } from 'react';

// Mui
import {
  Typography,
  Button,
  Chip,
  CircularProgress,
  Box
} from '@mui/material';
import {
  FilterList,
  GetApp as ExportIcon,
  AccountTree as SessionIcon
} from '@mui/icons-material';

// Components
import AdminCard from './components/AdminCard';
import AdminTable from './components/AdminTable';
import ReactAnimatedNumber from '../ReactAnimatedNumber';
import { SelectInput } from '../CustomInput';

// Utils
import axios from '../../../utils/axios';
import { useSnackbar } from '../snackbar';
import { AUM_ENDPOINT } from '../../../utils/aum';
import * as XLSX from 'xlsx';

// ----------------------------------------------------------------------

export default function UserSessions() {
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(true);
  const [sessions, setSessions] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(0);
  const [itemsPerPage] = useState(15);
  const [filters, setFilters] = useState({
    module: 'hsba',
    sessionType: 'all',
    role: 'all',
  });

  const getModuleSessions = useCallback(async () => {
    try {
      setLoading(true);
      const endpoint =
        filters.module === 'all'
          ? `${AUM_ENDPOINT}/user/v1/sessions`
          : `${AUM_ENDPOINT}/user/v1/module/${filters.module}/sessions`;
      const response = await axios.get(endpoint);
      if (response?.data?.status === 'success') {
        setSessions(response.data.data);
      } else {
        throw new Error('Failed to fetch sessions');
      }
    } catch (error) {
      console.error('Error fetching sessions:', error);
      enqueueSnackbar(error.message || 'Failed to fetch sessions', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  }, [filters.module, enqueueSnackbar]);

  useEffect(() => {
    getModuleSessions();
  }, [getModuleSessions]);

  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    setCurrentPage(0); // Reset to first page when filtering
  };

  // Filter and search sessions
  const filteredSessions = sessions.filter((session) => {
    // Apply filters
    if (filters.module !== 'all' && session.module !== filters.module) return false;
    if (filters.sessionType !== 'all' && session.session_type !== filters.sessionType) return false;
    if (filters.role !== 'all' && session.module_role !== filters.role) return false;

    // Apply search
    if (searchQuery) {
      const searchStr = searchQuery.toLowerCase();
      return (
        session.staff_id.toLowerCase().includes(searchStr) ||
        session.name.toLowerCase().includes(searchStr) ||
        session.module.toLowerCase().includes(searchStr) ||
        session.module_role.toLowerCase().includes(searchStr)
      );
    }
    return true;
  });

  // Paginate data
  const paginatedData = filteredSessions.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  // Calculate statistics
  const stats = {
    totalSessions: sessions.length,
    webSessions: sessions.filter((s) => s.session_type === 'web').length,
    phoneSessions: sessions.filter((s) => s.session_type === 'phone').length,
  };

  // Export to Excel functionality
  const handleExportToExcel = () => {
    const headers = [
      'Staff ID',
      'Name',
      'Module',
      'Role',
      'Session Type',
      'Last Active',
      'Submodules',
    ];
    const data = filteredSessions.map((session) => [
      session.staff_id,
      session.name,
      session.module,
      session.module_role,
      session.session_type,
      session.last_active_str,
      session.submodules?.join(', ') || '',
    ]);

    const ws = XLSX.utils.aoa_to_sheet([headers, ...data]);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sessions');
    XLSX.writeFile(wb, `user_sessions_${new Date().toISOString().split('T')[0]}.xlsx`);
  };

  // Table columns configuration
  const tableColumns = [
    { label: '#', width: '60px' },
    { label: 'User', width: 'auto' },
    { label: 'Staff ID', width: '120px' },
    { label: 'Module', width: '120px' },
    { label: 'Role', width: '120px' },
    { label: 'Session Type', width: '120px' },
    { label: 'Last Active', width: '160px' },
    { label: 'Submodules', width: '200px' }
  ];

  // Render table row
  const renderTableRow = (session, index) => (
    <>
      <td className="px-6 py-4">
        <Typography variant="body2" className="text-gray-600 font-medium">
          {index + 1 + itemsPerPage * currentPage}
        </Typography>
      </td>

      <td className="px-6 py-4">
        <div>
          <Typography variant="body2" className="font-semibold text-gray-900">
            {session.name}
          </Typography>
          <Typography variant="caption" className="text-gray-600">
            {session.staff_id.toUpperCase()}
          </Typography>
        </div>
      </td>

      <td className="px-6 py-4">
        <div className="bg-gray-100 rounded-lg px-3 py-1 max-w-fit">
          <Typography variant="body2" className="font-mono font-semibold text-gray-700">
            {session.staff_id.toUpperCase()}
          </Typography>
        </div>
      </td>

      <td className="px-6 py-4">
        <Typography variant="body2" className="text-gray-700">
          {session.module}
        </Typography>
      </td>

      <td className="px-6 py-4">
        <Chip
          label={session.module_role}
          size="small"
          sx={{
            backgroundColor: session.module_role === 'admin' ? '#dbeafe' : '#f3f4f6',
            color: session.module_role === 'admin' ? '#1d4ed8' : '#6b7280',
            fontWeight: 600,
            fontSize: '12px'
          }}
        />
      </td>

      <td className="px-6 py-4">
        <Chip
          label={session.session_type}
          size="small"
          sx={{
            backgroundColor: session.session_type === 'web' ? '#dcfce7' : '#fef3c7',
            color: session.session_type === 'web' ? '#166534' : '#92400e',
            fontWeight: 600,
            fontSize: '12px'
          }}
        />
      </td>

      <td className="px-6 py-4">
        <Typography variant="body2" className="text-gray-700">
          {session.last_active_str}
        </Typography>
      </td>

      <td className="px-6 py-4">
        {session.submodules?.length > 0 ? (
          <div className="flex flex-wrap gap-1">
            {session.submodules.map((submodule, i) => (
              <Chip
                key={i}
                label={submodule}
                size="small"
                sx={{
                  backgroundColor: '#f1f5f9',
                  color: '#475569',
                  fontSize: '10px',
                  height: '20px'
                }}
              />
            ))}
          </div>
        ) : (
          <Typography variant="body2" className="text-gray-400">
            -
          </Typography>
        )}
      </td>
    </>
  );

  if (loading) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '400px'
      }}>
        <CircularProgress size={40} sx={{ color: '#f97316' }} />
      </Box>
    );
  }

  return (
    <div className="h-full overflow-auto">
      <div className="space-y-6">
        {/* Statistics Cards */}
        <div className="flex w-full justify-center">
          <div className="flex flex-col gap-4 md:flex-row">
            {[
              { title: 'Total Sessions', number: stats.totalSessions, color: '#f97316' },
              { title: 'Web Sessions', number: stats.webSessions, color: '#10b981' },
              { title: 'Mobile Sessions', number: stats.phoneSessions, color: '#6366f1' },
            ].map((stat, i) => (
              <div
                key={i}
                className="flex min-w-[150px] flex-col items-center gap-2 rounded-xl p-4 text-white shadow-sm"
                style={{ backgroundColor: stat.color }}
              >
                <p className="text-sm font-semibold">{stat.title}</p>
                <p className="text-center text-3xl font-bold">
                  <ReactAnimatedNumber
                    value={stat.number}
                    formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                  />
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Search and Filter Controls */}
        <AdminCard
          title="Session Controls"
          subtitle="Search and filter active user sessions"
          icon={FilterList}
          iconColor="#f97316"
        >
          <div className="flex flex-col lg:flex-row gap-4 justify-between">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              <div className="flex gap-4">
                <div className="min-w-[200px]">
                  <SelectInput
                    value={filters.module}
                    placeholder="Filter by Module"
                    options={[
                      { value: 'all', label: 'All Modules' },
                      { value: 'hsba', label: 'HSBA' },
                      // Add more modules as needed
                    ]}
                    onChange={(e) => handleFilterChange('module', e.target.value)}
                  />
                </div>
                <div className="min-w-[200px]">
                  <SelectInput
                    value={filters.sessionType}
                    placeholder="Filter by Type"
                    options={[
                      { value: 'all', label: 'All Types' },
                      { value: 'web', label: 'Web Sessions' },
                      { value: 'phone', label: 'Mobile Sessions' }
                    ]}
                    onChange={(e) => handleFilterChange('sessionType', e.target.value)}
                  />
                </div>
                <div className="min-w-[200px]">
                  <SelectInput
                    value={filters.role}
                    placeholder="Filter by Role"
                    options={[
                      { value: 'all', label: 'All Roles' },
                      { value: 'admin', label: 'Admin' },
                      { value: 'user', label: 'User' },
                      { value: 'pending', label: 'Pending' }
                    ]}
                    onChange={(e) => handleFilterChange('role', e.target.value)}
                  />
                </div>
              </div>

              <div className="flex-1">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search by name, staff ID, module, or role..."
                  className="w-full h-10 px-4 text-sm border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:border-orange-500 focus:ring-2 focus:ring-orange-200 transition-all outline-none"
                />
              </div>
            </div>

            <div className="flex items-center">
              <Button
                variant="contained"
                startIcon={<ExportIcon />}
                onClick={handleExportToExcel}
                sx={{
                  borderRadius: '12px',
                  textTransform: 'none',
                  fontWeight: 600,
                  px: 3,
                  py: 1.5,
                  backgroundColor: '#10b981',
                  '&:hover': {
                    backgroundColor: '#059669',
                  },
                  boxShadow: '0 4px 6px -1px rgba(16, 185, 129, 0.3)'
                }}
              >
                Export to Excel
              </Button>
            </div>
          </div>
        </AdminCard>

        {/* Enhanced Session Table */}
        <AdminTable
          title="Active Sessions"
          subtitle={`${filteredSessions.length} sessions found`}
          icon={SessionIcon}
          iconColor="#f97316"
          columns={tableColumns}
          data={paginatedData}
          renderRow={renderTableRow}
          searchQuery={searchQuery}
          pagination={{
            currentPage,
            itemsPerPage,
            totalItems: filteredSessions.length,
            onPageChange: setCurrentPage
          }}
        />
      </div>
    </div>
  );
}
