// React
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';

// Mui
import { 
  Typography, 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  Switch, 
  Button,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  FormControlLabel
} from '@mui/material';
import { 
  Edit,
  Add,
  Apps,
  Settings,
  CheckCircle,
  Cancel,
  Build,
  Search,
  Delete
} from '@mui/icons-material';

// Components
import { TextInput, SearchInput } from '../CustomInput';
import { StatCard, AdminCard, AdminTable } from './components';
import ReactAnimatedNumber from '../ReactAnimatedNumber';

// Utils
import axios from '../../../utils/axios';
import { useSnackbar } from '../snackbar';
import { toUpperCaseFirstLetter } from '../../../utils/shared';
import { AUM_ENDPOINT } from '../../../utils/aum';
import { setIsLoading } from '../../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

export default function ModuleManagement() {
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();

  // Table
  const [currentPage, setCurrentPage] = useState(0);
  const [itemsPerPage] = useState(15);
  const [tableData, setTableData] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');

  const filteredData = (() => {
    if (!searchQuery) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(searchQuery.toLowerCase()));
  })();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const INITIAL_DIALOG_DATA = {
    app_name: '',
    app_description: '',
    status: 'active',
    planned_maintenance: false,
  };

  const [dialogData, setDialogData] = useState(INITIAL_DIALOG_DATA);
  
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const handleClickOpenDialog = (editMode, data = null) => {
    if (editMode && data) {
      setDialogData(data);
    } else {
      setDialogData(INITIAL_DIALOG_DATA);
    }
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };

  const handleDialogClose = async (action) => {
    if (action) {
      try {
        if (!dialogData.app_name || !dialogData.app_description) {
          enqueueSnackbar('Please fill in all required fields.', { variant: 'error' });
          return;
        }
        
        const submitData = {
          ...dialogData,
          app_name: dialogData.app_name.toLowerCase()
        };
        
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${AUM_ENDPOINT}/module/v1`, submitData);
            break;
          case 'put':
            response = await axios.put(`${AUM_ENDPOINT}/module/v1/${dialogData.id}`, submitData);
            break;
          case 'delete':
            response = await axios.delete(`${AUM_ENDPOINT}/module/v1/${dialogData.id}`);
            break;
          default:
            break;
        }
        
        let statusVariant;
        let message;
        if (response?.data?.status === 'success') {
          statusVariant = 'success';
          message = response.data.data || 'Operation completed successfully';
        } else {
          statusVariant = 'error';
          message = 'Operation failed';
        }
        enqueueSnackbar(message, { variant: statusVariant });
      } catch (error) {
        enqueueSnackbar('Operation failed', { variant: 'error' });
      }
    }

    setDialogData(INITIAL_DIALOG_DATA);
    setDialogOpen(false);
    fetchData();
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${AUM_ENDPOINT}/module/v1/all`);
      if (response.data.data) {
        setTableData(response.data.data);
      }
    } catch (error) {
      enqueueSnackbar('Failed to fetch modules', { variant: 'error' });
    }
    dispatch(setIsLoading(false));
  };

  // Stats calculations
  const getStats = () => {
    const active = tableData.filter(m => m.status === 'active').length;
    const maintenance = tableData.filter(m => m.planned_maintenance).length;
    const inactive = tableData.filter(m => m.status === 'inactive').length;
    const total = tableData.length;

    return { active, maintenance, inactive, total };
  };

  const getStatusInfo = (status) => {
    switch (status) {
      case 'active':
        return { bg: '#10b98115', color: '#10b981', label: 'Active' };
      case 'inactive':
        return { bg: '#6b728015', color: '#6b7280', label: 'Inactive' };
      case 'maintenance':
        return { bg: '#f59e0b15', color: '#f59e0b', label: 'Maintenance' };
      default:
        return { bg: '#6b728015', color: '#6b7280', label: status };
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (searchQuery) {
      setCurrentPage(0);
    }
  }, [searchQuery]);

  const stats = getStats();
  const paginatedData = filteredData.slice(
    currentPage * itemsPerPage,
    currentPage * itemsPerPage + itemsPerPage
  );

  const tableColumns = [
    { label: '#', width: '60px' },
    { label: 'Module', width: 'auto' },
    { label: 'Description', width: '300px' },
    { label: 'Status', width: '120px' },
    { label: 'Maintenance', width: '140px' },
    { label: 'Actions', width: '120px' }
  ];

  const renderTableRow = (row, index) => {
    const statusInfo = getStatusInfo(row?.status);
    return (
      <>
        <td className="px-6 py-4">
          <Typography variant="body2" className="text-gray-600 font-medium">
            {index + 1 + itemsPerPage * currentPage}
          </Typography>
        </td>
        
        <td className="px-6 py-4">
          <div className="flex items-center gap-3">
            <Avatar 
              sx={{ 
                width: 40, 
                height: 40,
                backgroundColor: '#8b5cf615',
                color: '#8b5cf6',
                fontSize: '14px',
                fontWeight: 600
              }}
            >
              <Apps fontSize="small" />
            </Avatar>
            <div>
              <Typography variant="body2" className="font-semibold text-gray-900">
                {row?.app_name?.toUpperCase()}
              </Typography>
              <Typography variant="caption" className="text-gray-500">
                Module ID: {row?.id}
              </Typography>
            </div>
          </div>
        </td>

        <td className="px-6 py-4">
          <Typography variant="body2" className="text-gray-700 max-w-xs truncate">
            {row?.app_description}
          </Typography>
        </td>

        <td className="px-6 py-4">
          <Chip
            label={statusInfo.label}
            size="small"
            sx={{
              backgroundColor: statusInfo.bg,
              color: statusInfo.color,
              fontWeight: 600,
              fontSize: '12px'
            }}
          />
        </td>

        <td className="px-6 py-4">
          <div className="flex items-center gap-2">
            {row?.planned_maintenance ? (
              <Chip
                label="Scheduled"
                size="small"
                icon={<Build fontSize="small" />}
                sx={{
                  backgroundColor: '#f59e0b15',
                  color: '#f59e0b',
                  fontWeight: 600,
                  fontSize: '11px',
                  '& .MuiChip-icon': {
                    fontSize: '12px'
                  }
                }}
              />
            ) : (
              <Chip
                label="Normal"
                size="small"
                icon={<CheckCircle fontSize="small" />}
                sx={{
                  backgroundColor: '#10b98115',
                  color: '#10b981',
                  fontWeight: 600,
                  fontSize: '11px',
                  '& .MuiChip-icon': {
                    fontSize: '12px'
                  }
                }}
              />
            )}
          </div>
        </td>

        <td className="px-6 py-4">
          <Tooltip title="Edit Module">
            <IconButton
              onClick={() => handleClickOpenDialog(true, row)}
              sx={{
                width: 36,
                height: 36,
                backgroundColor: '#6366f115',
                color: '#6366f1',
                '&:hover': {
                  backgroundColor: '#6366f125',
                }
              }}
            >
              <Edit fontSize="small" />
            </IconButton>
          </Tooltip>
        </td>
      </>
    );
  };

  return (
    <>
      <div className="h-full">
        <div className="p-8 space-y-8">
          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              title="Active Modules"
              value={stats.active}
              subtitle={`${stats.total > 0 ? ((stats.active / stats.total) * 100).toFixed(1) : 0}% of total modules`}
              icon={CheckCircle}
              color="#10b981"
            />

            <StatCard
              title="In Maintenance"
              value={stats.maintenance}
              subtitle="Scheduled maintenance"
              icon={Build}
              color="#f59e0b"
              badge={stats.maintenance > 0 ? { label: 'Action Required', color: '#f59e0b' } : null}
            />

            <StatCard
              title="Inactive Modules"
              value={stats.inactive}
              subtitle="Disabled modules"
              icon={Cancel}
              color="#6b7280"
            />

            <StatCard
              title="Total Modules"
              value={stats.total}
              subtitle="Registered modules"
              icon={Apps}
              color="#6366f1"
            />
          </div>

          {/* Search and Actions */}
          <AdminCard
            title="Module Controls"
            subtitle="Search and manage system modules"
            icon={Settings}
            iconColor="#8b5cf6"
          >
            <div className="flex flex-col lg:flex-row gap-4 justify-between">
              <div className="flex-1">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search modules by name, description, or status..."
                  className="w-full h-10 px-4 text-sm border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all outline-none"
                />
              </div>

              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={() => handleClickOpenDialog(false)}
                sx={{
                  borderRadius: '12px',
                  textTransform: 'none',
                  fontWeight: 600,
                  px: 3,
                  py: 1.5,
                  backgroundColor: '#8b5cf6',
                  '&:hover': {
                    backgroundColor: '#7c3aed',
                  },
                  boxShadow: '0 4px 6px -1px rgba(139, 92, 246, 0.3)'
                }}
              >
                New Module
              </Button>
            </div>
          </AdminCard>

          {/* Enhanced Module Table */}
          <AdminTable
            title="Module Directory"
            subtitle={`${filteredData.length} modules found`}
            icon={Apps}
            iconColor="#6366f1"
            columns={tableColumns}
            data={paginatedData}
            renderRow={renderTableRow}
            searchQuery={searchQuery}
            pagination={{
              currentPage,
              itemsPerPage,
              totalItems: filteredData.length,
              onPageChange: setCurrentPage
            }}
          />
        </div>
      </div>

      {/* Enhanced Dialog */}
      <Dialog 
        open={dialogOpen} 
        onClose={() => handleDialogClose(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '16px',
            overflow: 'hidden'
          }
        }}
      >
        <DialogTitle sx={{ 
          background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
          color: 'white',
          fontWeight: 700,
          fontSize: '18px',
          display: 'flex',
          alignItems: 'center',
          gap: 2
        }}>
          <Avatar sx={{ width: 32, height: 32, backgroundColor: 'rgba(255,255,255,0.2)' }}>
            {editModeDialog ? <Edit fontSize="small" /> : <Add fontSize="small" />}
          </Avatar>
          {editModeDialog ? 'Edit Module' : 'Add New Module'}
        </DialogTitle>
        
        <DialogContent sx={{ p: 4 }}>
          <div className="space-y-6 pt-2">
            <div>
              <Typography variant="body2" className="font-semibold text-gray-700 mb-2">
                Module Name *
              </Typography>
              <TextInput
                name="app_name"
                value={dialogData?.app_name}
                placeholder="Enter module name (e.g., user-management)"
                onChange={handleDialogDataChange}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '12px',
                    backgroundColor: '#f9fafb',
                    '&:hover': {
                      backgroundColor: '#f8fafc',
                    },
                    '&.Mui-focused': {
                      backgroundColor: 'white',
                      boxShadow: '0 0 0 3px rgba(139, 92, 246, 0.1)',
                    }
                  }
                }}
              />
            </div>

            <div>
              <Typography variant="body2" className="font-semibold text-gray-700 mb-2">
                Description *
              </Typography>
              <TextInput
                name="app_description"
                value={dialogData?.app_description}
                placeholder="Enter module description"
                onChange={handleDialogDataChange}
                multiline
                rows={3}
                fullWidth
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '12px',
                    backgroundColor: '#f9fafb',
                    '&:hover': {
                      backgroundColor: '#f8fafc',
                    },
                    '&.Mui-focused': {
                      backgroundColor: 'white',
                      boxShadow: '0 0 0 3px rgba(139, 92, 246, 0.1)',
                    }
                  }
                }}
              />
            </div>

            <div className="bg-gray-50 rounded-2xl p-4">
              <Typography variant="body2" className="font-semibold text-gray-700 mb-3">
                Module Settings
              </Typography>
              <FormControlLabel
                control={
                  <Switch
                    name="planned_maintenance"
                    checked={dialogData.planned_maintenance}
                    onChange={(event) => {
                      const { name, checked } = event.target;
                      setDialogData((prevValues) => ({
                        ...prevValues,
                        [name]: checked,
                      }));
                    }}
                    sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': {
                        color: '#8b5cf6',
                      },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                        backgroundColor: '#8b5cf6',
                      },
                    }}
                  />
                }
                label={
                  <div>
                    <Typography variant="body2" className="font-medium text-gray-900">
                      Planned Maintenance Mode
                    </Typography>
                    <Typography variant="caption" className="text-gray-600">
                      Enable when module requires scheduled maintenance
                    </Typography>
                  </div>
                }
              />
            </div>
          </div>
        </DialogContent>
        
        <DialogActions sx={{ p: 4, pt: 0, gap: 2 }}>
          {editModeDialog && (
            <Button
              onClick={() => handleDialogClose('delete')}
              startIcon={<Delete />}
              sx={{
                borderRadius: '8px',
                textTransform: 'none',
                fontWeight: 600,
                color: '#ef4444',
                backgroundColor: '#ef444415',
                '&:hover': {
                  backgroundColor: '#ef444425',
                }
              }}
            >
              Delete
            </Button>
          )}
          
          <div className="flex-1" />
          
          <Button 
            onClick={() => handleDialogClose(false)}
            sx={{
              borderRadius: '8px',
              textTransform: 'none',
              fontWeight: 600,
              color: '#6b7280'
            }}
          >
            Cancel
          </Button>
          
          <Button 
            onClick={() => handleDialogClose(editModeDialog ? 'put' : 'post')}
            variant="contained"
            sx={{
              borderRadius: '8px',
              textTransform: 'none',
              fontWeight: 600,
              backgroundColor: '#8b5cf6',
              '&:hover': {
                backgroundColor: '#7c3aed',
              }
            }}
          >
            {editModeDialog ? 'Update Module' : 'Create Module'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
