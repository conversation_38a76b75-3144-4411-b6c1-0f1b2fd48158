// React
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Mui
import { 
  Typography, 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  Button,
  Chip,
  Avatar,
  Box,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  CheckCircle,
  Cancel,
  Block,
  PersonAdd,
  CloudUpload,
  FilterList,
  Search,
  People,
  AdminPanelSettings,
  Security,
  Person
} from '@mui/icons-material';

// Components
import { SelectInput, SearchInput } from '../CustomInput';
import ReactAnimatedNumber from '../ReactAnimatedNumber';
import UploadCsvButton from '../UploadCsv';

// Utils
import axios from '../../../utils/axios';
import { useSnackbar } from '../snackbar';
import { useAuthContext } from '../../../utils/auth/useAuthContext';
import { toUpperCaseFirstLetter } from '../../../utils/shared';
import { AUM_ENDPOINT } from '../../../utils/aum';
import { fetchAllStaffs } from '../../../utils/store/aumReducer';

// Others
import * as R from 'ramda';

// ----------------------------------------------------------------------

export default function UserManagement() {
  const { user } = useAuthContext();
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  
  const { allStaffs } = useSelector((state) => state.aum);
  const { page, rowsPerPage } = useSelector((state) => state.simi);
  
  const [selectedStaffId, setSelectedStaffId] = useState('');
  const [selectedAction, setSelectedAction] = useState('');
  const [filterDivision, setFilterDivision] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(0);
  const [itemsPerPage] = useState(15);

  // Dialog
  const [dialogTitle, setDialogTitle] = useState('');
  const [dialogContent, setDialogContent] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);

  const filteredTableData = (() => {
    let temp = allStaffs;
    if (filterDivision !== 'all') {
      temp = temp.filter((o) => o?.division === filterDivision);
    }
    if (searchQuery) {
      temp = temp.filter((o) => JSON.stringify(o).toLowerCase().includes(searchQuery.toLowerCase()));
    }
    return temp;
  })();

  const divisionsList = R.uniq(R.pluck('division', allStaffs));

  // Stats calculations
  const getStats = () => {
    const active = allStaffs?.filter(u => u.user_status === 'active')?.length || 0;
    const pending = allStaffs?.filter(u => u.user_status === 'pending approval')?.length || 0;
    const inactive = allStaffs?.filter(u => u.user_status === 'inactive')?.length || 0;
    const total = allStaffs?.length || 0;

    return { active, pending, inactive, total };
  };

  const handleDialogClose = async (approved) => {
    if (!approved) {
      setDialogOpen(false);
      return;
    }
    
    let response;
    try {
      switch (selectedAction) {
        case 'approve':
          response = await axios.put(`${AUM_ENDPOINT}/user/v1/status/${selectedStaffId}/active`);
          break;
        case 'reject':
          response = await axios.put(`${AUM_ENDPOINT}/user/v1/status/${selectedStaffId}/inactive`);
          break;
        case 'delete':
          response = await axios.delete(`${AUM_ENDPOINT}/user/v1/${selectedStaffId}`);
          break;
        default:
          break;
      }
    } catch (error) {
      console.log(error);
    }

    let statusVariant;
    let message;
    if (response?.data?.status === 'success') {
      statusVariant = 'success';
      message = response.data.data;
    } else {
      statusVariant = 'error';
      message = 'Failed';
    }
    enqueueSnackbar(message, { variant: statusVariant });
    dispatch(fetchAllStaffs());
    setDialogOpen(false);
  };

  const handleRegisterListOfUsers = async (data) => {
    for (let i = 0; i < data.length; i += 1) {
      try {
        await axios.post(`${AUM_ENDPOINT}/user/v1`, {
          staff_id: data[i]?.['Staff ID']?.toLowerCase()?.trim(),
        });
      } catch {
        /* empty */
      }
    }
    dispatch(fetchAllStaffs());
  };

  const handleClick = (staffId, action) => {
    setSelectedStaffId(staffId);
    setSelectedAction(action);

    switch (action) {
      case 'approve':
        setDialogTitle('Approve User Access?');
        setDialogContent("This will grant the user access to view the application's data and features.");
        break;
      case 'reject':
        setDialogTitle('Revoke User Access?');
        setDialogContent('The user will lose access and need to re-request if needed.');
        break;
      case 'delete':
        setDialogTitle('Delete User Account?');
        setDialogContent('This action cannot be undone. The user will need to re-register.');
        break;
      default:
        return;
    }

    setDialogOpen(true);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return { bg: '#10b98115', color: '#10b981', label: 'Active' };
      case 'pending approval':
        return { bg: '#f59e0b15', color: '#f59e0b', label: 'Pending' };
      case 'inactive':
        return { bg: '#6b728015', color: '#6b7280', label: 'Inactive' };
      case 'blocked':
        return { bg: '#ef444415', color: '#ef4444', label: 'Blocked' };
      default:
        return { bg: '#6b728015', color: '#6b7280', label: status };
    }
  };

  useEffect(() => {
    dispatch(fetchAllStaffs());
  }, [dispatch]);

  useEffect(() => {
    if (searchQuery || filterDivision !== 'all') {
      setCurrentPage(0);
    }
  }, [searchQuery, filterDivision]);

  const stats = getStats();
  const paginatedData = filteredTableData.slice(
    currentPage * itemsPerPage,
    currentPage * itemsPerPage + itemsPerPage
  );

  return (
    <>
      <div className="h-full">
        <div className="p-8 space-y-8">
          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-gradient-to-br from-white to-green-50 rounded-2xl p-6 border border-green-100 shadow-sm hover:shadow-md transition-all duration-300">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 rounded-xl bg-green-100 flex items-center justify-center">
                  <CheckCircle className="text-green-600" />
                </div>
                <div>
                  <Typography variant="caption" className="text-gray-600 font-medium uppercase tracking-wide">
                    Active Users
                  </Typography>
                </div>
              </div>
              <Typography variant="h2" className="font-bold text-green-700 mb-2">
                <ReactAnimatedNumber
                  value={stats.active}
                  formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                />
              </Typography>
              {stats.total > 0 && (
                <Typography variant="caption" className="text-green-600">
                  {((stats.active / stats.total) * 100).toFixed(1)}% of total users
                </Typography>
              )}
            </div>

            <div className="bg-gradient-to-br from-white to-amber-50 rounded-2xl p-6 border border-amber-100 shadow-sm hover:shadow-md transition-all duration-300">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 rounded-xl bg-amber-100 flex items-center justify-center">
                  <PersonAdd className="text-amber-600" />
                </div>
                <div>
                  <Typography variant="caption" className="text-gray-600 font-medium uppercase tracking-wide">
                    Pending Approval
                  </Typography>
                </div>
              </div>
              <Typography variant="h2" className="font-bold text-amber-700 mb-2">
                <ReactAnimatedNumber
                  value={stats.pending}
                  formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                />
              </Typography>
              {stats.pending > 0 && (
                <Chip 
                  label="Requires Action"
                  size="small"
                  sx={{
                    backgroundColor: '#fbbf2415',
                    color: '#f59e0b',
                    fontWeight: 600,
                    fontSize: '11px'
                  }}
                />
              )}
            </div>

            <div className="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 rounded-xl bg-gray-100 flex items-center justify-center">
                  <Block className="text-gray-600" />
                </div>
                <div>
                  <Typography variant="caption" className="text-gray-600 font-medium uppercase tracking-wide">
                    Inactive Users
                  </Typography>
                </div>
              </div>
              <Typography variant="h2" className="font-bold text-gray-700 mb-2">
                <ReactAnimatedNumber
                  value={stats.inactive}
                  formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                />
              </Typography>
              <Typography variant="caption" className="text-gray-500">
                Revoked or suspended access
              </Typography>
            </div>

            <div className="bg-gradient-to-br from-white to-blue-50 rounded-2xl p-6 border border-blue-100 shadow-sm hover:shadow-md transition-all duration-300">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 rounded-xl bg-blue-100 flex items-center justify-center">
                  <People className="text-blue-600" />
                </div>
                <div>
                  <Typography variant="caption" className="text-gray-600 font-medium uppercase tracking-wide">
                    Total Users
                  </Typography>
                </div>
              </div>
              <Typography variant="h2" className="font-bold text-blue-700 mb-2">
                <ReactAnimatedNumber
                  value={stats.total}
                  formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
                />
              </Typography>
              <Typography variant="caption" className="text-blue-600">
                Registered in system
              </Typography>
            </div>
          </div>

          {/* Filters and Actions */}
          <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
            <div className="flex flex-col lg:flex-row gap-4 justify-between">
              <div className="flex flex-col sm:flex-row gap-4 flex-1">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-xl bg-indigo-100 flex items-center justify-center">
                    <FilterList className="text-indigo-600" fontSize="small" />
                  </div>
                  <Typography variant="h6" className="font-semibold text-gray-900">
                    Filter & Search
                  </Typography>
                </div>
                
                <div className="flex gap-4 flex-1">
                  <div className="min-w-[200px]">
                    <SelectInput
                      value={filterDivision}
                      placeholder="All Divisions"
                      options={[
                        { value: 'all', label: 'All Divisions' },
                        ...divisionsList?.filter((o) => o !== '').map(div => ({ value: div, label: div }))
                      ]}
                      onChange={(event) => setFilterDivision(event.target.value)}
                    />
                  </div>
                  
                  <div className="flex-1">
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      placeholder="Search by name, staff ID, or division..."
                      className="w-full h-10 px-4 text-sm border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all outline-none"
                    />
                  </div>
                </div>
              </div>

              <div className="flex items-center">
                <UploadCsvButton
                  templateInJson={{ 'Staff ID': 'tm12345' }}
                  headerIsAtRow={0}
                  onUpload={async (data) => handleRegisterListOfUsers(data?.Sheet1)}
                >
                  <Button
                    variant="contained"
                    startIcon={<CloudUpload />}
                    sx={{
                      borderRadius: '12px',
                      textTransform: 'none',
                      fontWeight: 600,
                      px: 3,
                      py: 1.5,
                      backgroundColor: '#6366f1',
                      '&:hover': {
                        backgroundColor: '#4f46e5',
                      },
                      boxShadow: '0 4px 6px -1px rgba(99, 102, 241, 0.3)'
                    }}
                  >
                    Bulk Register
                  </Button>
                </UploadCsvButton>
              </div>
            </div>
          </div>

          {/* Enhanced User Table */}
          <div className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden">
            <div className="px-8 py-6 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-xl bg-purple-100 flex items-center justify-center">
                    <AdminPanelSettings className="text-purple-600" fontSize="small" />
                  </div>
                  <div>
                    <Typography variant="h6" className="font-semibold text-gray-900">
                      User Directory
                    </Typography>
                    <Typography variant="caption" className="text-gray-600">
                      {filteredTableData.length} users found
                    </Typography>
                  </div>
                </div>
                {(searchQuery || filterDivision !== 'all') && (
                  <Chip 
                    label={`${filteredTableData.length} filtered results`}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                )}
              </div>
            </div>
            
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="border-b border-gray-50">
                    {[
                      { label: '#', width: '60px' },
                      { label: 'User', width: 'auto' },
                      { label: 'Staff ID', width: '120px' },
                      { label: 'Division', width: '160px' },
                      { label: 'Status', width: '120px' },
                      { label: 'Actions', width: '200px' }
                    ].map((col, i) => (
                      <th 
                        key={i} 
                        className="px-6 py-4 text-left"
                        style={{ width: col.width }}
                      >
                        <Typography variant="caption" className="text-gray-700 font-semibold uppercase tracking-wider">
                          {col.label}
                        </Typography>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-50">
                  {paginatedData.map((row, i) => {
                    const statusInfo = getStatusColor(row?.user_status);
                    return (
                      <tr key={`${i}-${row?.staff_id}`} className="hover:bg-gray-50/50 transition-colors duration-150">
                        <td className="px-6 py-4">
                          <Typography variant="body2" className="text-gray-600 font-medium">
                            {i + 1 + itemsPerPage * currentPage}
                          </Typography>
                        </td>
                        
                        <td className="px-6 py-4">
                          <div className="flex items-center gap-3">
                            <Avatar 
                              sx={{ 
                                width: 40, 
                                height: 40,
                                backgroundColor: row?.isSuperAdmin ? '#6366f1' : '#10b981',
                                fontSize: '14px',
                                fontWeight: 600
                              }}
                            >
                              {row?.name?.charAt(0)?.toUpperCase() || row?.staff_id?.charAt(0)?.toUpperCase()}
                            </Avatar>
                            <div>
                              <Typography variant="body2" className="font-semibold text-gray-900">
                                {row?.name || 'Unknown User'}
                              </Typography>
                              {row?.isSuperAdmin && (
                                <Chip 
                                  label="Super Admin"
                                  size="small"
                                  icon={<Security fontSize="small" />}
                                  sx={{
                                    height: '20px',
                                    fontSize: '10px',
                                    fontWeight: 600,
                                    backgroundColor: '#dbeafe',
                                    color: '#1d4ed8',
                                    mt: 0.5,
                                    '& .MuiChip-icon': {
                                      fontSize: '12px'
                                    }
                                  }}
                                />
                              )}
                            </div>
                          </div>
                        </td>

                        <td className="px-6 py-4">
                          <div className="bg-gray-100 rounded-lg px-3 py-1 max-w-fit">
                            <Typography variant="body2" className="font-mono font-semibold text-gray-700">
                              {row?.staff_id?.toUpperCase()}
                            </Typography>
                          </div>
                        </td>

                        <td className="px-6 py-4">
                          <Typography variant="body2" className="text-gray-700">
                            {row?.division || 'N/A'}
                          </Typography>
                        </td>

                        <td className="px-6 py-4">
                          <Chip
                            label={statusInfo.label}
                            size="small"
                            sx={{
                              backgroundColor: statusInfo.bg,
                              color: statusInfo.color,
                              fontWeight: 600,
                              fontSize: '12px'
                            }}
                          />
                        </td>

                        <td className="px-6 py-4">
                          <div className="flex gap-2">
                            {(row?.user_status === 'pending approval' ||
                              row?.user_status === 'inactive') && (
                              <Tooltip title="Approve Access">
                                <IconButton
                                  onClick={() => handleClick(row?.staff_id, 'approve')}
                                  disabled={row?.staff_id === user.staff_id}
                                  sx={{
                                    width: 36,
                                    height: 36,
                                    backgroundColor: '#10b98115',
                                    color: '#10b981',
                                    '&:hover': {
                                      backgroundColor: '#10b98125',
                                    }
                                  }}
                                >
                                  <CheckCircle fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                            
                            {row?.user_status === 'active' && !row?.isSuperAdmin && (
                              <Tooltip title="Revoke Access">
                                <IconButton
                                  onClick={() => handleClick(row?.staff_id, 'reject')}
                                  disabled={row?.staff_id === user.staff_id}
                                  sx={{
                                    width: 36,
                                    height: 36,
                                    backgroundColor: '#f59e0b15',
                                    color: '#f59e0b',
                                    '&:hover': {
                                      backgroundColor: '#f59e0b25',
                                    }
                                  }}
                                >
                                  <Cancel fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                            
                            {row?.user_status === 'blocked' && (
                              <Tooltip title="Unblock User">
                                <IconButton
                                  onClick={() => handleClick(row?.staff_id, 'approve')}
                                  disabled={row.staff_id === user.staff_id}
                                  sx={{
                                    width: 36,
                                    height: 36,
                                    backgroundColor: '#10b98115',
                                    color: '#10b981',
                                    '&:hover': {
                                      backgroundColor: '#10b98125',
                                    }
                                  }}
                                >
                                  <CheckCircle fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {/* Enhanced Pagination */}
            <div className="px-8 py-6 border-t border-gray-100 bg-gray-50/50">
              {filteredTableData.length > itemsPerPage ? (
                <div className="flex items-center justify-between">
                  <Typography variant="body2" className="text-gray-600">
                    Showing {currentPage * itemsPerPage + 1} to {Math.min((currentPage + 1) * itemsPerPage, filteredTableData.length)} of {filteredTableData.length} users
                  </Typography>
                  <div className="flex gap-2">
                    <button
                      onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                      disabled={currentPage === 0}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-150"
                    >
                      Previous
                    </button>
                    <div className="px-4 py-2 text-sm font-semibold text-white bg-indigo-600 rounded-lg">
                      {currentPage + 1}
                    </div>
                    <button
                      onClick={() => setCurrentPage(Math.min(Math.ceil(filteredTableData.length / itemsPerPage) - 1, currentPage + 1))}
                      disabled={currentPage >= Math.ceil(filteredTableData.length / itemsPerPage) - 1}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-150"
                    >
                      Next
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-center">
                  <Typography variant="body2" className="text-gray-600">
                    Showing all {filteredTableData.length} users
                  </Typography>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Dialog */}
      <Dialog 
        open={dialogOpen} 
        onClose={() => setDialogOpen(false)}
        PaperProps={{
          sx: {
            borderRadius: '16px',
            padding: '8px'
          }
        }}
      >
        <DialogTitle sx={{ 
          fontWeight: 700,
          fontSize: '18px',
          color: '#111827'
        }}>
          {dialogTitle}
        </DialogTitle>
        <DialogContent sx={{ pb: 2 }}>
          <Typography variant="body1" sx={{ color: '#6b7280' }}>
            {dialogContent}
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button 
            onClick={() => handleDialogClose(false)}
            sx={{
              borderRadius: '8px',
              textTransform: 'none',
              fontWeight: 600,
              color: '#6b7280'
            }}
          >
            Cancel
          </Button>
          <Button 
            onClick={() => handleDialogClose(true)} 
            autoFocus
            variant="contained"
            sx={{
              borderRadius: '8px',
              textTransform: 'none',
              fontWeight: 600,
              backgroundColor: '#6366f1',
              '&:hover': {
                backgroundColor: '#4f46e5',
              }
            }}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
