// React
import { useState } from 'react';
import { useDispatch } from 'react-redux';
import dynamic from 'next/dynamic';

// Mui
import { Typography } from '@mui/material';

// Packages
import * as yup from 'yup';

// Components
import { TextInput, SelectInput } from '../CustomInput';

// Utils
import axios from '../../../utils/axios';
import { useSnackbar } from '../snackbar';
import { AUM_ENDPOINT } from '../../../utils/aum';
import { NOTIFICATION_ENDPOINT } from '../../../utils/notification';
import { setIsLoading } from '../../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

export default function BlastEmail() {
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  
  const HtmlEditor = dynamic(() => import('../HtmlEditor'), {
    ssr: false,
  });

  // Form
  const schema = yup.object({
    body: yup.string().required(`Please provide body`).default('string'),
    html_body: yup.string().required(`Please provide html body`),
    recipients: yup.array().of(yup.string()).required(`Please provide recipient`),
    cc: yup.array().of(yup.string()).default([]),
    sender: yup.string().required(`Please provide sender`).default('<EMAIL>'),
    subject: yup.string().required(`Please provide subject`).default('To Request For Quotation'),
  });

  const [formData, setFormData] = useState({});
  
  const handleFormDataChange = (event) => {
    const { name, value } = event.target;
    setFormData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  
  const handleFormDataSubmit = async () => {
    let payload = {
      ...formData,
      recipients: [...(await getRecipent(formData?.recipient))],
      cc: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
      html_body: `<!DOCTYPE html>\n<html lang="en">\n<head>\n<meta charset="UTF-8">\n<meta name="viewport" content="width=device-width, initial-scale=1.0">\n<title>Your Page Title</title>\n</head>\n<body>\n${formData?.html_body}\n</body>\n</html>`,
    };
    
    try {
      payload = await schema.validate(payload, { abortEarly: false });
    } catch (error) {
      enqueueSnackbar(error.errors, { variant: 'error' });
      return;
    }

    dispatch(setIsLoading(true));
    try {
      await axios.post(`${NOTIFICATION_ENDPOINT}/send_email_v2`, payload);
      enqueueSnackbar('Email sent successfully', { variant: 'success' });
      setFormData({});
    } catch {
      enqueueSnackbar('Failed to send email', { variant: 'error' });
    }
    dispatch(setIsLoading(false));
  };

  const getRecipent = async (systemModule) => {
    let apiEndpoint = `${AUM_ENDPOINT}/user/v1/module/${systemModule}/all`;
    if (systemModule === 'all') {
      apiEndpoint = `${AUM_ENDPOINT}/user/v1/all`;
    }
    try {
      const response = await axios.get(apiEndpoint);
      if (response?.data?.status === 'success' && response?.data?.data !== null) {
        return response?.data?.data?.map((o) => o?.email);
      }
    } catch (error) {
      console.error('Error fetching recipients:', error);
    }
    return [];
  };

  return (
    <div className="h-full overflow-auto">
      <Typography variant="h6" className="mb-4">Blast E-mail</Typography>
      
      <div className="flex flex-col items-center gap-4 p-4">
        <SelectInput
          name="recipient"
          value={formData?.recipient}
          placeholder="Recipient"
          options={[
            { label: 'All', value: 'all' },
            { label: 'Capry', value: 'capry' },
            { label: 'Data Center', value: 'data-center' },
            { label: 'Edge Facility', value: 'edge-facility' },
            { label: 'HSBA', value: 'hsba' },
            { label: 'LOA', value: 'loa' },
            { label: 'METRIC', value: 'metric' },
            { label: 'Payme', value: 'payme' },
            { label: 'Voting', value: 'voting' },
          ]}
          onChange={handleFormDataChange}
        />
        <TextInput
          name="subject"
          value={formData?.subject}
          placeholder="Subject"
          onChange={handleFormDataChange}
        />
        <HtmlEditor
          name="html_body"
          value={formData?.html_body}
          placeholder="HTML Body"
          onChange={handleFormDataChange}
        />
        <div className="flex h-10 w-full items-center justify-end">
          <button 
            type="button" 
            className="cta-btn bg-[#408ed0] text-white px-4 py-2 rounded"
            onClick={() => handleFormDataSubmit()}
          >
            Send Email
          </button>
        </div>
      </div>
    </div>
  );
}
