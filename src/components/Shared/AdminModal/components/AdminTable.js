import { Typo<PERSON>, Chip } from '@mui/material';

export default function AdminTable({
  title,
  subtitle,
  icon: IconComponent,
  iconColor = '#6366f1',
  columns = [],
  data = [],
  renderRow,
  pagination,
  searchQuery,
  className = '',
  ...props
}) {
  return (
    <div className={`bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden ${className}`} {...props}>
      {/* Header */}
      <div className="px-8 py-6 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {IconComponent && (
              <div 
                className="w-10 h-10 rounded-xl flex items-center justify-center"
                style={{ backgroundColor: `${iconColor}15` }}
              >
                <IconComponent style={{ color: iconColor }} fontSize="small" />
              </div>
            )}
            <div>
              {title && (
                <Typography variant="h6" className="font-semibold text-gray-900">
                  {title}
                </Typography>
              )}
              {subtitle && (
                <Typography variant="caption" className="text-gray-600">
                  {subtitle}
                </Typography>
              )}
            </div>
          </div>
          {searchQuery && (
            <Chip 
              label={`${data.length} results`}
              size="small"
              color="primary"
              variant="outlined"
            />
          )}
        </div>
      </div>
      
      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr className="border-b border-gray-50">
              {columns.map((col, i) => (
                <th 
                  key={i} 
                  className="px-6 py-4 text-left"
                  style={{ width: col.width }}
                >
                  <Typography variant="caption" className="text-gray-700 font-semibold uppercase tracking-wider">
                    {col.label}
                  </Typography>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-50">
            {data.map((row, index) => (
              <tr key={index} className="hover:bg-gray-50/50 transition-colors duration-150">
                {renderRow(row, index)}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination && (
        <div className="px-8 py-6 border-t border-gray-100 bg-gray-50/50">
          {pagination.totalItems > pagination.itemsPerPage ? (
            <div className="flex items-center justify-between">
              <Typography variant="body2" className="text-gray-600">
                Showing {pagination.currentPage * pagination.itemsPerPage + 1} to {Math.min((pagination.currentPage + 1) * pagination.itemsPerPage, pagination.totalItems)} of {pagination.totalItems} entries
              </Typography>
              <div className="flex gap-2">
                <button
                  onClick={() => pagination.onPageChange(Math.max(0, pagination.currentPage - 1))}
                  disabled={pagination.currentPage === 0}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-150"
                >
                  Previous
                </button>
                <div className="px-4 py-2 text-sm font-semibold text-white bg-indigo-600 rounded-lg">
                  {pagination.currentPage + 1}
                </div>
                <button
                  onClick={() => pagination.onPageChange(Math.min(Math.ceil(pagination.totalItems / pagination.itemsPerPage) - 1, pagination.currentPage + 1))}
                  disabled={pagination.currentPage >= Math.ceil(pagination.totalItems / pagination.itemsPerPage) - 1}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-150"
                >
                  Next
                </button>
              </div>
            </div>
          ) : (
            <div className="text-center">
              <Typography variant="body2" className="text-gray-600">
                Showing all {pagination.totalItems} entries
              </Typography>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
