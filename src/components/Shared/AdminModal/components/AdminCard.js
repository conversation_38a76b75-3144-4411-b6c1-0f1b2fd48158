import { Box, Typography } from '@mui/material';

export default function AdminCard({ 
  children, 
  title, 
  subtitle, 
  icon: IconComponent, 
  iconColor = '#6366f1',
  className = '',
  ...props 
}) {
  return (
    <div className={`bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden ${className}`} {...props}>
      {(title || subtitle || IconComponent) && (
        <div className="px-8 py-6 border-b border-gray-100">
          <div className="flex items-center gap-3">
            {IconComponent && (
              <div 
                className="w-10 h-10 rounded-xl flex items-center justify-center"
                style={{ backgroundColor: `${iconColor}15` }}
              >
                <IconComponent style={{ color: iconColor }} fontSize="small" />
              </div>
            )}
            <div>
              {title && (
                <Typography variant="h6" className="font-semibold text-gray-900">
                  {title}
                </Typography>
              )}
              {subtitle && (
                <Typography variant="caption" className="text-gray-600">
                  {subtitle}
                </Typography>
              )}
            </div>
          </div>
        </div>
      )}
      <div className="p-8">
        {children}
      </div>
    </div>
  );
}
