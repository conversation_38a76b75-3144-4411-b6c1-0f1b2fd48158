import { Typography, Chip } from '@mui/material';
import ReactAnimatedNumber from '../../ReactAnimatedNumber';

export default function StatCard({
  title,
  value,
  subtitle,
  icon: IconComponent,
  color = '#6366f1',
  badge,
  trend,
  className = '',
  ...props
}) {
  return (
    <div 
      className={`bg-gradient-to-br from-white rounded-2xl p-6 border shadow-sm hover:shadow-md transition-all duration-300 ${className}`}
      style={{ 
        borderColor: `${color}20`,
        backgroundImage: `linear-gradient(135deg, white 0%, ${color}08 100%)`
      }}
      {...props}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          {IconComponent && (
            <div 
              className="w-12 h-12 rounded-xl flex items-center justify-center"
              style={{ backgroundColor: `${color}15` }}
            >
              <IconComponent style={{ color }} />
            </div>
          )}
          <div>
            <Typography variant="caption" className="text-gray-600 font-medium uppercase tracking-wide">
              {title}
            </Typography>
          </div>
        </div>
        {badge && (
          <Chip 
            label={badge.label}
            size="small"
            sx={{
              backgroundColor: badge.color ? `${badge.color}15` : `${color}15`,
              color: badge.color || color,
              fontWeight: 600,
              fontSize: '12px'
            }}
          />
        )}
      </div>

      <div className="flex items-baseline gap-2 mb-2">
        <Typography 
          variant="h2" 
          className="font-bold"
          style={{ color: `${color}dd` }}
        >
          {typeof value === 'number' ? (
            <ReactAnimatedNumber
              value={value}
              formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
            />
          ) : (
            value
          )}
        </Typography>
        {trend && (
          <div className="flex items-center gap-1">
            {trend.icon && <trend.icon className={`text-${trend.type === 'up' ? 'green' : 'red'}-500`} fontSize="small" />}
            <Typography 
              variant="caption" 
              className={`font-medium text-${trend.type === 'up' ? 'green' : 'red'}-600`}
            >
              {trend.value}
            </Typography>
          </div>
        )}
      </div>

      {subtitle && (
        <Typography variant="caption" className="text-gray-600">
          {subtitle}
        </Typography>
      )}
    </div>
  );
}
