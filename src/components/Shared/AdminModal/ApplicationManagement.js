// React
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Mui
import {
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Chip,
  Avatar,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Edit,
  Add,
  Apps,
  Settings,
  CheckCircle,
  Cancel,
  Token,
  Search,
  Delete,
  ContentCopy
} from '@mui/icons-material';

// Components
import { TextInput, SelectInput } from '../CustomInput';
import { StatCard, AdminCard, AdminTable } from './components';
import UserPopup from '../UserPopup';
import ReactAnimatedNumber from '../ReactAnimatedNumber';

// Utils
import axios from '../../../utils/axios';
import { useSnackbar } from '../snackbar';
import { useAuthContext } from '../../../utils/auth/useAuthContext';
import { toUpperCaseFirstLetter } from '../../../utils/shared';
import { AUM_ENDPOINT } from '../../../utils/aum';
import { setIsLoading } from '../../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

export default function ApplicationManagement() {
  const { user } = useAuthContext();
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();

  // Table
  const [currentPage, setCurrentPage] = useState(0);
  const [itemsPerPage] = useState(15);
  const [tableData, setTableData] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');

  const filteredData = (() => {
    if (!searchQuery) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(searchQuery.toLowerCase()));
  })();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const INITIAL_DIALOG_DATA = {
    application_name: '',
    application_status: 'active',
    created_by: user?.staff_id,
  };

  const [dialogData, setDialogData] = useState(INITIAL_DIALOG_DATA);

  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const handleClickOpenDialog = (editMode, data = null) => {
    if (editMode && data) {
      setDialogData(data);
    } else {
      setDialogData(INITIAL_DIALOG_DATA);
    }
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };

  const handleDialogClose = async (action) => {
    if (action) {
      try {
        if (!dialogData.application_name) {
          enqueueSnackbar('Please fill in all required fields.', { variant: 'error' });
          return;
        }

        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${AUM_ENDPOINT}/application/v1`, dialogData);
            break;
          case 'put':
            response = await axios.put(
              `${AUM_ENDPOINT}/application/v1/status/${dialogData.id}/${dialogData.application_status}`
            );
            break;
          case 'delete':
            response = await axios.delete(`${AUM_ENDPOINT}/application/v1/${dialogData.id}`);
            break;
          default:
            break;
        }

        let statusVariant;
        let message;
        if (response?.data?.status === 'success') {
          statusVariant = 'success';
          message = response.data.data || 'Operation completed successfully';
        } else {
          statusVariant = 'error';
          message = 'Operation failed';
        }
        enqueueSnackbar(message, { variant: statusVariant });
      } catch (error) {
        enqueueSnackbar('Operation failed', { variant: 'error' });
      }
    }

    setDialogData(INITIAL_DIALOG_DATA);
    setDialogOpen(false);
    fetchData();
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${AUM_ENDPOINT}/application/v1/all`);
      if (response.data.data) {
        setTableData(response.data.data);
      }
    } catch (error) {
      enqueueSnackbar('Failed to fetch applications', { variant: 'error' });
    }
    dispatch(setIsLoading(false));
  };

  // Stats calculations
  const getStats = () => {
    const active = tableData.filter(app => app.application_status === 'active').length;
    const inactive = tableData.filter(app => app.application_status === 'inactive').length;
    const total = tableData.length;

    return { active, inactive, total };
  };

  const getStatusInfo = (status) => {
    switch (status) {
      case 'active':
        return { bg: '#10b98115', color: '#10b981', label: 'Active' };
      case 'inactive':
        return { bg: '#6b728015', color: '#6b7280', label: 'Inactive' };
      default:
        return { bg: '#6b728015', color: '#6b7280', label: status };
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (searchQuery) {
      setCurrentPage(0);
    }
  }, [searchQuery]);

  const stats = getStats();
  const paginatedData = filteredData.slice(
    currentPage * itemsPerPage,
    currentPage * itemsPerPage + itemsPerPage
  );

  const tableColumns = [
    { label: '#', width: '60px' },
    { label: 'Application', width: 'auto' },
    { label: 'Token', width: '200px' },
    { label: 'Status', width: '120px' },
    { label: 'Created By', width: '140px' },
    { label: 'Created At', width: '140px' },
    { label: 'Actions', width: '120px' }
  ];

  const handleCopyToken = async (token, appName) => {
    try {
      await navigator.clipboard.writeText(token);
      enqueueSnackbar(
        `Copied ${appName?.toUpperCase()} token to clipboard.`,
        { variant: 'success' }
      );
    } catch (error) {
      enqueueSnackbar('Failed to copy token', { variant: 'error' });
    }
  };

  const renderTableRow = (row, index) => {
    const statusInfo = getStatusInfo(row?.application_status);
    return (
      <>
        <td className="px-6 py-4">
          <Typography variant="body2" className="text-gray-600 font-medium">
            {index + 1 + itemsPerPage * currentPage}
          </Typography>
        </td>

        <td className="px-6 py-4">
          <div className="flex items-center gap-3">
            <Avatar
              sx={{
                width: 40,
                height: 40,
                backgroundColor: '#3b82f615',
                color: '#3b82f6',
                fontSize: '14px',
                fontWeight: 600
              }}
            >
              <Apps fontSize="small" />
            </Avatar>
            <div>
              <Typography variant="body2" className="font-semibold text-gray-900">
                {row?.application_name?.toUpperCase()}
              </Typography>
              <Typography variant="caption" className="text-gray-500">
                App ID: {row?.id}
              </Typography>
            </div>
          </div>
        </td>

        <td className="px-6 py-4">
          <div className="flex items-center gap-2">
            <Typography
              variant="body2"
              className="text-gray-700 font-mono text-xs max-w-[150px] truncate"
            >
              {row?.token}
            </Typography>
            <Tooltip title="Copy Token">
              <IconButton
                onClick={() => handleCopyToken(row?.token, row?.application_name)}
                sx={{
                  width: 28,
                  height: 28,
                  backgroundColor: '#3b82f615',
                  color: '#3b82f6',
                  '&:hover': {
                    backgroundColor: '#3b82f625',
                  }
                }}
              >
                <ContentCopy fontSize="small" />
              </IconButton>
            </Tooltip>
          </div>
        </td>

        <td className="px-6 py-4">
          <Chip
            label={statusInfo.label}
            size="small"
            sx={{
              backgroundColor: statusInfo.bg,
              color: statusInfo.color,
              fontWeight: 600,
              fontSize: '12px'
            }}
          />
        </td>

        <td className="px-6 py-4">
          <UserPopup
            label={row?.created_by?.toUpperCase()}
            staff_id={row?.created_by}
          />
        </td>

        <td className="px-6 py-4">
          <Typography variant="body2" className="text-gray-700">
            {row?.created_at}
          </Typography>
        </td>

        <td className="px-6 py-4">
          <Tooltip title="Edit Application">
            <IconButton
              onClick={() => handleClickOpenDialog(true, row)}
              sx={{
                width: 36,
                height: 36,
                backgroundColor: '#6366f115',
                color: '#6366f1',
                '&:hover': {
                  backgroundColor: '#6366f125',
                }
              }}
            >
              <Edit fontSize="small" />
            </IconButton>
          </Tooltip>
        </td>
      </>
    );
  };

  return (
    <>
      <div className="h-full">
        <div className="p-8 space-y-8">
          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <StatCard
              title="Active Applications"
              value={stats.active}
              subtitle={`${stats.total > 0 ? ((stats.active / stats.total) * 100).toFixed(1) : 0}% of total applications`}
              icon={CheckCircle}
              color="#10b981"
            />

            <StatCard
              title="Inactive Applications"
              value={stats.inactive}
              subtitle="Disabled applications"
              icon={Cancel}
              color="#6b7280"
            />

            <StatCard
              title="Total Applications"
              value={stats.total}
              subtitle="Registered applications"
              icon={Apps}
              color="#3b82f6"
            />
          </div>

          {/* Search and Actions */}
          <AdminCard
            title="Application Controls"
            subtitle="Search and manage system applications"
            icon={Settings}
            iconColor="#3b82f6"
          >
            <div className="flex flex-col lg:flex-row gap-4 justify-between">
              <div className="flex-1">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search applications by name, status, or creator..."
                  className="w-full h-10 px-4 text-sm border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all outline-none"
                />
              </div>

              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={() => handleClickOpenDialog(false)}
                sx={{
                  borderRadius: '12px',
                  textTransform: 'none',
                  fontWeight: 600,
                  px: 3,
                  py: 1.5,
                  backgroundColor: '#3b82f6',
                  '&:hover': {
                    backgroundColor: '#2563eb',
                  },
                  boxShadow: '0 4px 6px -1px rgba(59, 130, 246, 0.3)'
                }}
              >
                New Application
              </Button>
            </div>
          </AdminCard>

          {/* Enhanced Application Table */}
          <AdminTable
            title="Application Directory"
            subtitle={`${filteredData.length} applications found`}
            icon={Apps}
            iconColor="#3b82f6"
            columns={tableColumns}
            data={paginatedData}
            renderRow={renderTableRow}
            searchQuery={searchQuery}
            pagination={{
              currentPage,
              itemsPerPage,
              totalItems: filteredData.length,
              onPageChange: setCurrentPage
            }}
          />
        </div>
      </div>

      {/* Enhanced Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => handleDialogClose(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '16px',
            overflow: 'hidden'
          }
        }}
      >
        <DialogTitle sx={{
          background: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
          color: 'white',
          fontWeight: 700,
          fontSize: '18px',
          display: 'flex',
          alignItems: 'center',
          gap: 2
        }}>
          <Avatar sx={{ width: 32, height: 32, backgroundColor: 'rgba(255,255,255,0.2)' }}>
            {editModeDialog ? <Edit fontSize="small" /> : <Add fontSize="small" />}
          </Avatar>
          {editModeDialog ? 'Edit Application' : 'Add New Application'}
        </DialogTitle>

        <DialogContent sx={{ p: 4 }}>
          <div className="space-y-6 pt-2">
            {!editModeDialog && (
              <div>
                <Typography variant="body2" className="font-semibold text-gray-700 mb-2">
                  Application Name *
                </Typography>
                <TextInput
                  name="application_name"
                  value={dialogData?.application_name}
                  placeholder="Enter application name"
                  onChange={handleDialogDataChange}
                  fullWidth
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '12px',
                      backgroundColor: '#f9fafb',
                      '&:hover': {
                        backgroundColor: '#f8fafc',
                      },
                      '&.Mui-focused': {
                        backgroundColor: 'white',
                        boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)',
                      }
                    }
                  }}
                />
              </div>
            )}

            {editModeDialog && (
              <div>
                <Typography variant="body2" className="font-semibold text-gray-700 mb-2">
                  Application Status
                </Typography>
                <SelectInput
                  name="application_status"
                  value={dialogData?.application_status}
                  placeholder="Select status"
                  options={['active', 'inactive']}
                  onChange={handleDialogDataChange}
                  fullWidth
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '12px',
                      backgroundColor: '#f9fafb',
                      '&:hover': {
                        backgroundColor: '#f8fafc',
                      },
                      '&.Mui-focused': {
                        backgroundColor: 'white',
                        boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.1)',
                      }
                    }
                  }}
                />
              </div>
            )}
          </div>
        </DialogContent>

        <DialogActions sx={{ p: 4, pt: 0, gap: 2 }}>
          {editModeDialog && (
            <Button
              onClick={() => handleDialogClose('delete')}
              startIcon={<Delete />}
              sx={{
                borderRadius: '8px',
                textTransform: 'none',
                fontWeight: 600,
                color: '#ef4444',
                backgroundColor: '#ef444415',
                '&:hover': {
                  backgroundColor: '#ef444425',
                }
              }}
            >
              Delete
            </Button>
          )}

          <div className="flex-1" />

          <Button
            onClick={() => handleDialogClose(false)}
            sx={{
              borderRadius: '8px',
              textTransform: 'none',
              fontWeight: 600,
              color: '#6b7280'
            }}
          >
            Cancel
          </Button>

          <Button
            onClick={() => handleDialogClose(editModeDialog ? 'put' : 'post')}
            variant="contained"
            sx={{
              borderRadius: '8px',
              textTransform: 'none',
              fontWeight: 600,
              backgroundColor: '#3b82f6',
              '&:hover': {
                backgroundColor: '#2563eb',
              }
            }}
          >
            {editModeDialog ? 'Update Application' : 'Create Application'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
