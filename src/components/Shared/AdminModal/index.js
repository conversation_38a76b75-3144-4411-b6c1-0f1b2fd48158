// Next, React
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';

// Mui
import {
  Dialog,
  DialogContent,
  Tabs,
  Tab,
  Box,
  IconButton,
  Typography,
  Fade,
  Slide,
  Avatar,
  Chip,
  Tooltip,
  useMediaQuery,
  useTheme
} from '@mui/material';
import {
  Close as CloseIcon,
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Apps as AppsIcon,
  Airplay as AirplayIcon,
  AlternateEmail as EmailIcon,
  Email as BlastEmailIcon,
  CalendarMonth as CalendarIcon,
  Psychology as TanyaIcon,
  AccountTree as SessionIcon,
  Settings as SettingsIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Menu as MenuIcon
} from '@mui/icons-material';

// Components
import AdminDashboard from './AdminDashboard';
import UserManagement from './UserManagement';
import ModuleManagement from './ModuleManagement';
import ApplicationManagement from './ApplicationManagement';
import EmailDomainManagement from './EmailDomainManagement';
import BlastEmail from './BlastEmail';
import DateManagement from './DateManagement';
import GaishaAccessManagement from './GaishaAccessManagement';
import UserSessions from './UserSessions';

// Utils
import { useAuthContext } from '../../../utils/auth/useAuthContext';

// ----------------------------------------------------------------------

const ADMIN_TABS = [
  { 
    id: 'dashboard', 
    label: 'Dashboard', 
    icon: DashboardIcon,
    description: 'Overview & Analytics',
    color: '#6366f1'
  },
  { 
    id: 'user-management', 
    label: 'Users', 
    icon: PeopleIcon,
    description: 'User Management',
    color: '#10b981'
  },
  { 
    id: 'module-management', 
    label: 'Modules', 
    icon: AppsIcon,
    description: 'Module Management',
    color: '#f59e0b'
  },
  { 
    id: 'application-management', 
    label: 'Applications', 
    icon: AirplayIcon,
    description: 'App Management',
    color: '#8b5cf6'
  },
  { 
    id: 'email-domain-management', 
    label: 'Email Domains', 
    icon: EmailIcon,
    description: 'Domain Management',
    color: '#06b6d4'
  },
  { 
    id: 'blast-email', 
    label: 'Blast Email', 
    icon: BlastEmailIcon,
    description: 'Email Broadcasting',
    color: '#ef4444'
  },
  { 
    id: 'date-management', 
    label: 'Dates', 
    icon: CalendarIcon,
    description: 'Date Management',
    color: '#84cc16'
  },
  { 
    id: 'gaisha-access-management', 
    label: 'TANYA Access', 
    icon: TanyaIcon,
    description: 'AI Access Control',
    color: '#ec4899'
  },
  { 
    id: 'user-sessions', 
    label: 'Sessions', 
    icon: SessionIcon,
    description: 'Active Sessions',
    color: '#f97316'
  }
];

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`admin-tabpanel-${index}`}
      aria-labelledby={`admin-tab-${index}`}
      style={{ height: value === index ? 'auto' : 0, overflow: 'hidden' }}
      {...other}
    >
      {value === index && (
        <Box sx={{ height: '100%' }}>
          {children}
        </Box>
      )}
    </div>
  );
}

// ----------------------------------------------------------------------

export default function AdminModal({ open, onClose }) {
  const { user } = useAuthContext();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));

  const [activeTab, setActiveTab] = useState(0);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(isMobile);

  // Auto-collapse on mobile, expand on desktop
  React.useEffect(() => {
    setSidebarCollapsed(isMobile);
  }, [isMobile]);

  // Security check - only superadmin can access
  if (!user?.isSuperAdmin) {
    return null;
  }

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const renderTabContent = (tabId) => {
    switch (tabId) {
      case 'dashboard':
        return <AdminDashboard />;
      case 'user-management':
        return <UserManagement />;
      case 'module-management':
        return <ModuleManagement />;
      case 'application-management':
        return <ApplicationManagement />;
      case 'email-domain-management':
        return <EmailDomainManagement />;
      case 'blast-email':
        return <BlastEmail />;
      case 'date-management':
        return <DateManagement />;
      case 'gaisha-access-management':
        return <GaishaAccessManagement />;
      case 'user-sessions':
        return <UserSessions />;
      default:
        return <div>Tab content not found</div>;
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="xl"
      fullWidth
      TransitionComponent={Slide}
      TransitionProps={{ direction: 'up' }}
      PaperProps={{
        sx: { 
          height: '95vh',
          maxHeight: '95vh',
          borderRadius: '16px',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          background: '#fafafa',
          overflow: 'hidden',
          border: '1px solid #e5e7eb'
        }
      }}
    >
      <DialogContent sx={{ p: 0, height: '100%', display: 'flex' }}>
        {/* Sidebar Navigation */}
        <Box sx={{
          width: sidebarCollapsed ? (isMobile ? '60px' : '80px') : (isTablet ? '240px' : '280px'),
          minWidth: sidebarCollapsed ? (isMobile ? '60px' : '80px') : (isTablet ? '240px' : '280px'),
          backgroundColor: 'white',
          borderRight: '1px solid #f3f4f6',
          display: 'flex',
          flexDirection: 'column',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          boxShadow: sidebarCollapsed && isMobile ? '2px 0 8px rgba(0,0,0,0.1)' : 'none',
        }}>
          {/* Header */}
          <Box sx={{
            p: sidebarCollapsed ? 2 : 3,
            borderBottom: '1px solid #f3f4f6',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            minHeight: sidebarCollapsed ? '64px' : '80px',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
          }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: sidebarCollapsed ? 1 : 2,
              opacity: sidebarCollapsed ? 0 : 1,
              transition: 'opacity 0.2s',
              overflow: 'hidden'
            }}>
              <Avatar sx={{
                width: sidebarCollapsed ? 32 : 36,
                height: sidebarCollapsed ? 32 : 36,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                fontSize: sidebarCollapsed ? '16px' : '18px',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
              }}>
                <SettingsIcon />
              </Avatar>
              {!sidebarCollapsed && (
                <Box>
                  <Typography variant="h6" sx={{
                    fontWeight: 700,
                    color: '#111827',
                    fontSize: '16px',
                    lineHeight: 1.2
                  }}>
                    Admin Panel
                  </Typography>
                  <Typography variant="caption" sx={{
                    color: '#6b7280',
                    fontSize: '12px'
                  }}>
                    System Management
                  </Typography>
                </Box>
              )}
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Tooltip title={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}>
                <IconButton
                  onClick={toggleSidebar}
                  sx={{
                    width: 32,
                    height: 32,
                    color: '#6b7280',
                    '&:hover': {
                      backgroundColor: '#f3f4f6',
                      color: '#374151'
                    },
                    transition: 'all 0.2s'
                  }}
                >
                  {sidebarCollapsed ? <ChevronRightIcon fontSize="small" /> : <ChevronLeftIcon fontSize="small" />}
                </IconButton>
              </Tooltip>

              <Tooltip title="Close">
                <IconButton
                  onClick={onClose}
                  sx={{
                    width: 32,
                    height: 32,
                    color: '#6b7280',
                    '&:hover': {
                      backgroundColor: '#f3f4f6',
                      color: '#374151'
                    },
                    transition: 'all 0.2s'
                  }}
                >
                  <CloseIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* Navigation Items */}
          <Box sx={{
            flex: 1,
            p: sidebarCollapsed ? 1 : 2,
            overflow: 'auto',
            '&::-webkit-scrollbar': {
              width: '4px',
            },
            '&::-webkit-scrollbar-track': {
              background: '#f1f1f1',
              borderRadius: '2px',
            },
            '&::-webkit-scrollbar-thumb': {
              background: '#c1c1c1',
              borderRadius: '2px',
              '&:hover': {
                background: '#a8a8a8',
              },
            },
          }}>
            {ADMIN_TABS.map((tab, index) => {
              const IconComponent = tab.icon;
              const isActive = activeTab === index;

              const menuItem = (
                <Box
                  key={tab.id}
                  onClick={() => setActiveTab(index)}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: sidebarCollapsed ? 0 : 2,
                    p: sidebarCollapsed ? 1.5 : 1.5,
                    mb: sidebarCollapsed ? 1 : 0.5,
                    borderRadius: sidebarCollapsed ? '8px' : '10px',
                    cursor: 'pointer',
                    backgroundColor: isActive ? '#f8fafc' : 'transparent',
                    border: isActive ? '1px solid #e2e8f0' : '1px solid transparent',
                    boxShadow: isActive ? '0 1px 3px rgba(0, 0, 0, 0.1)' : 'none',
                    transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&:hover': {
                      backgroundColor: isActive ? '#f8fafc' : '#f9fafb',
                      transform: sidebarCollapsed ? 'scale(1.05)' : 'translateY(-1px)',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                    },
                    position: 'relative',
                    overflow: 'hidden',
                    justifyContent: sidebarCollapsed ? 'center' : 'flex-start',
                    minHeight: sidebarCollapsed ? '44px' : '56px'
                  }}
                >
                  {/* Active indicator */}
                  {isActive && !sidebarCollapsed && (
                    <Box sx={{
                      position: 'absolute',
                      left: 0,
                      top: 0,
                      bottom: 0,
                      width: '3px',
                      backgroundColor: tab.color,
                      borderRadius: '0 2px 2px 0'
                    }} />
                  )}

                  {/* Active indicator for collapsed state */}
                  {isActive && sidebarCollapsed && (
                    <Box sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: '3px',
                      backgroundColor: tab.color,
                      borderRadius: '2px 2px 0 0'
                    }} />
                  )}

                  <Box sx={{
                    minWidth: '20px',
                    color: isActive ? tab.color : '#6b7280',
                    transition: 'color 0.2s',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <IconComponent fontSize="small" />
                  </Box>

                  {!sidebarCollapsed && (
                    <Box sx={{
                      transition: 'opacity 0.2s',
                      overflow: 'hidden',
                      flex: 1
                    }}>
                      <Typography variant="body2" sx={{
                        fontWeight: isActive ? 600 : 500,
                        color: isActive ? '#111827' : '#374151',
                        fontSize: '13px',
                        lineHeight: 1.3,
                        mb: 0.25
                      }}>
                        {tab.label}
                      </Typography>
                      <Typography variant="caption" sx={{
                        color: '#9ca3af',
                        fontSize: '10px',
                        lineHeight: 1.2
                      }}>
                        {tab.description}
                      </Typography>
                    </Box>
                  )}
                </Box>
              );

              // Wrap with tooltip when collapsed
              if (sidebarCollapsed) {
                return (
                  <Tooltip
                    key={tab.id}
                    title={
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 600, mb: 0.5 }}>
                          {tab.label}
                        </Typography>
                        <Typography variant="caption" sx={{ opacity: 0.8 }}>
                          {tab.description}
                        </Typography>
                      </Box>
                    }
                    placement="right"
                    arrow
                  >
                    {menuItem}
                  </Tooltip>
                );
              }

              return menuItem;
            })}
          </Box>

          {/* User Info */}
          <Box sx={{
            p: sidebarCollapsed ? 1.5 : 3,
            borderTop: '1px solid #f3f4f6',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
          }}>
            {sidebarCollapsed ? (
              <Tooltip
                title={
                  <Box>
                    <Typography variant="body2" sx={{ fontWeight: 600, mb: 0.5 }}>
                      {user?.name || 'Admin'}
                    </Typography>
                    <Typography variant="caption" sx={{ opacity: 0.8 }}>
                      Super Admin
                    </Typography>
                  </Box>
                }
                placement="right"
                arrow
              >
                <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                  <Avatar sx={{
                    width: 32,
                    height: 32,
                    backgroundColor: '#10b981',
                    fontSize: '14px',
                    fontWeight: 600
                  }}>
                    {user?.name?.charAt(0)?.toUpperCase() || 'A'}
                  </Avatar>
                </Box>
              </Tooltip>
            ) : (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{
                  width: 32,
                  height: 32,
                  backgroundColor: '#10b981',
                  fontSize: '14px',
                  fontWeight: 600
                }}>
                  {user?.name?.charAt(0)?.toUpperCase() || 'A'}
                </Avatar>
                <Box sx={{ overflow: 'hidden' }}>
                  <Typography variant="body2" sx={{
                    fontWeight: 600,
                    color: '#111827',
                    fontSize: '13px',
                    lineHeight: 1.2,
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}>
                    {user?.name || 'Admin'}
                  </Typography>
                  <Chip
                    label="Super Admin"
                    size="small"
                    sx={{
                      height: '18px',
                      fontSize: '10px',
                      fontWeight: 600,
                      backgroundColor: '#dbeafe',
                      color: '#1d4ed8',
                      mt: 0.5
                    }}
                  />
                </Box>
              </Box>
            )}
          </Box>
        </Box>

        {/* Main Content Area */}
        <Box sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          backgroundColor: '#fafafa'
        }}>
          {/* Content Header */}
          <Box sx={{
            p: isMobile ? 2 : (isTablet ? 3 : 4),
            borderBottom: '1px solid #f3f4f6',
            backgroundColor: 'white',
            minHeight: isMobile ? '64px' : '80px',
            display: 'flex',
            alignItems: 'center'
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: isMobile ? 2 : 3 }}>
              <Box sx={{
                width: isMobile ? 40 : 48,
                height: isMobile ? 40 : 48,
                borderRadius: '12px',
                backgroundColor: `${ADMIN_TABS[activeTab]?.color}15`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: ADMIN_TABS[activeTab]?.color
              }}>
                {(() => {
                  const IconComponent = ADMIN_TABS[activeTab]?.icon;
                  return <IconComponent fontSize={isMobile ? 'medium' : 'large'} />;
                })()}
              </Box>
              <Box>
                <Typography variant="h5" sx={{
                  fontWeight: 700,
                  color: '#111827',
                  fontSize: isMobile ? '18px' : (isTablet ? '20px' : '24px'),
                  lineHeight: 1.2,
                  mb: 0.5
                }}>
                  {ADMIN_TABS[activeTab]?.label}
                </Typography>
                <Typography variant="body2" sx={{
                  color: '#6b7280',
                  fontSize: isMobile ? '12px' : '14px'
                }}>
                  {ADMIN_TABS[activeTab]?.description}
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Content Body */}
          <Box sx={{
            flex: 1,
            overflow: 'auto',
            p: isMobile ? 2 : (isTablet ? 3 : 4),
            '&::-webkit-scrollbar': {
              width: '6px',
            },
            '&::-webkit-scrollbar-track': {
              background: '#f1f1f1',
              borderRadius: '3px',
            },
            '&::-webkit-scrollbar-thumb': {
              background: '#c1c1c1',
              borderRadius: '3px',
              '&:hover': {
                background: '#a8a8a8',
              },
            },
          }}>
            {ADMIN_TABS.map((tab, index) => (
              <TabPanel key={tab.id} value={activeTab} index={index}>
                <Fade in={activeTab === index} timeout={200}>
                  <Box sx={{
                    backgroundColor: 'white',
                    borderRadius: isMobile ? '12px' : '16px',
                    border: '1px solid #f3f4f6',
                    minHeight: `calc(95vh - ${isMobile ? '180px' : '240px'})`,
                    overflow: 'hidden',
                    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
                  }}>
                    {renderTabContent(tab.id)}
                  </Box>
                </Fade>
              </TabPanel>
            ))}
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
}
