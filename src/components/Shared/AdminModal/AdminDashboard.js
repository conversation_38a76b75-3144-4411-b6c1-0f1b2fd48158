// React
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { twMerge } from 'tailwind-merge';

// Mui
import { Typography, Box, Chip, Avatar, LinearProgress } from '@mui/material';
import { 
  TrendingUp, 
  TrendingDown, 
  StarRate,
  People,
  Assessment,
  Timeline
} from '@mui/icons-material';

// Packages
import moment from 'moment';

// Components
import { SearchInput } from '../CustomInput';
import ReactAnimatedNumber from '../ReactAnimatedNumber';
import UserPopup from '../UserPopup';

// Utils
import axios from '../../../utils/axios';
import { AUM_ENDPOINT } from '../../../utils/aum';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { checkAndReplaceStringWithHyphen } from '../../../utils/shared';

// ----------------------------------------------------------------------

export default function AdminDashboard() {
  const dispatch = useDispatch();

  // Table
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(10);
  const [tableData, setTableData] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');

  const onChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const getTableData = () => {
    if (!searchQuery) {
      return tableData;
    }
    return tableData.filter((o) => JSON.stringify(o).toLowerCase().includes(searchQuery.toLowerCase()));
  };

  // NPS calculations
  const getNpsAverage = () => {
    const data = getTableData();
    if (data.length === 0) return 0;
    return data.reduce((prev, curr) => prev + curr?.rating, 0) / data.length;
  };

  const getNpsCategory = (score) => {
    if (score >= 9) return { label: 'Excellent', color: '#10b981', emoji: '🎉' };
    if (score >= 7) return { label: 'Good', color: '#f59e0b', emoji: '👍' };
    if (score >= 5) return { label: 'Fair', color: '#f97316', emoji: '😐' };
    return { label: 'Poor', color: '#ef4444', emoji: '😞' };
  };

  const getScoreDistribution = () => {
    const data = getTableData();
    const promoters = data.filter(d => d?.rating >= 9).length;
    const passives = data.filter(d => d?.rating >= 7 && d?.rating < 9).length;
    const detractors = data.filter(d => d?.rating < 7).length;
    const total = data.length;

    return {
      promoters: { count: promoters, percentage: total ? (promoters / total) * 100 : 0 },
      passives: { count: passives, percentage: total ? (passives / total) * 100 : 0 },
      detractors: { count: detractors, percentage: total ? (detractors / total) * 100 : 0 },
      total
    };
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${AUM_ENDPOINT}/nps/v1/all/keyword`);
      if (response.data.data) {
        response.data.data.reverse();
        setTableData(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching NPS data:', error);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (searchQuery) {
      setPage(0);
    }
  }, [searchQuery]);

  const npsScore = getNpsAverage();
  const npsCategory = getNpsCategory(npsScore);
  const distribution = getScoreDistribution();

  return (
    <div className="h-full">
      <div className="p-8 space-y-8">
        {/* Key Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Overall NPS Score */}
          <div className="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-xl bg-blue-100 flex items-center justify-center">
                  <StarRate className="text-blue-600" fontSize="small" />
                </div>
                <div>
                  <Typography variant="caption" className="text-gray-600 font-medium uppercase tracking-wide">
                    NPS Score
                  </Typography>
                </div>
              </div>
              <Chip 
                label={npsCategory.label}
                sx={{
                  backgroundColor: `${npsCategory.color}15`,
                  color: npsCategory.color,
                  fontWeight: 600,
                  fontSize: '12px'
                }}
                size="small"
              />
            </div>
            <div className="flex items-baseline gap-2 mb-2">
              <Typography variant="h3" className="font-bold text-gray-900">
                <ReactAnimatedNumber
                  value={npsScore}
                  formatValue={(n) => Number(n).toFixed(1)}
                />
              </Typography>
              <Typography variant="body2" className="text-gray-500">
                / 10
              </Typography>
              <span className="text-2xl">{npsCategory.emoji}</span>
            </div>
            <Typography variant="caption" className="text-gray-600">
              Based on {getTableData().length} responses
            </Typography>
          </div>

          {/* Total Responses */}
          <div className="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-xl bg-green-100 flex items-center justify-center">
                <People className="text-green-600" fontSize="small" />
              </div>
              <Typography variant="caption" className="text-gray-600 font-medium uppercase tracking-wide">
                Total Responses
              </Typography>
            </div>
            <Typography variant="h3" className="font-bold text-gray-900 mb-2">
              <ReactAnimatedNumber
                value={distribution.total}
                formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
              />
            </Typography>
            <div className="flex items-center gap-1">
              <TrendingUp className="text-green-500" fontSize="small" />
              <Typography variant="caption" className="text-green-600 font-medium">
                All time data
              </Typography>
            </div>
          </div>

          {/* Promoters */}
          <div className="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-xl bg-emerald-100 flex items-center justify-center">
                <Typography variant="body2" className="font-bold text-emerald-600">
                  9+
                </Typography>
              </div>
              <Typography variant="caption" className="text-gray-600 font-medium uppercase tracking-wide">
                Promoters
              </Typography>
            </div>
            <div className="flex items-baseline gap-2 mb-2">
              <Typography variant="h3" className="font-bold text-gray-900">
                {distribution.promoters.count}
              </Typography>
              <Typography variant="body2" className="text-emerald-600 font-semibold">
                {distribution.promoters.percentage.toFixed(1)}%
              </Typography>
            </div>
            <LinearProgress 
              variant="determinate" 
              value={distribution.promoters.percentage} 
              sx={{
                height: 4,
                borderRadius: 2,
                backgroundColor: '#d1fae5',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: '#10b981',
                  borderRadius: 2
                }
              }}
            />
          </div>

          {/* Detractors */}
          <div className="bg-gradient-to-br from-white to-gray-50 rounded-2xl p-6 border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-xl bg-red-100 flex items-center justify-center">
                <Typography variant="body2" className="font-bold text-red-600">
                  0-6
                </Typography>
              </div>
              <Typography variant="caption" className="text-gray-600 font-medium uppercase tracking-wide">
                Detractors
              </Typography>
            </div>
            <div className="flex items-baseline gap-2 mb-2">
              <Typography variant="h3" className="font-bold text-gray-900">
                {distribution.detractors.count}
              </Typography>
              <Typography variant="body2" className="text-red-600 font-semibold">
                {distribution.detractors.percentage.toFixed(1)}%
              </Typography>
            </div>
            <LinearProgress 
              variant="determinate" 
              value={distribution.detractors.percentage} 
              sx={{
                height: 4,
                borderRadius: 2,
                backgroundColor: '#fee2e2',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: '#ef4444',
                  borderRadius: 2
                }
              }}
            />
          </div>
        </div>

        {/* Search and Filter Section */}
        <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-xl bg-purple-100 flex items-center justify-center">
                <Assessment className="text-purple-600" fontSize="small" />
              </div>
              <div>
                <Typography variant="h6" className="font-semibold text-gray-900">
                  Feedback Explorer
                </Typography>
                <Typography variant="caption" className="text-gray-600">
                  Search and analyze user feedback
                </Typography>
              </div>
            </div>
          </div>
          
          <div className="w-full">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search feedback by message, path, or staff ID..."
              className="w-full h-10 px-4 text-sm border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all outline-none"
            />
          </div>
        </div>

        {/* Enhanced Feedback Table */}
        <div className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden">
          <div className="px-8 py-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-xl bg-indigo-100 flex items-center justify-center">
                  <Timeline className="text-indigo-600" fontSize="small" />
                </div>
                <div>
                  <Typography variant="h6" className="font-semibold text-gray-900">
                    Recent Feedback
                  </Typography>
                  <Typography variant="caption" className="text-gray-600">
                    {getTableData().length} feedback entries
                  </Typography>
                </div>
              </div>
              {searchQuery && (
                <Chip 
                  label={`${getTableData().length} results`}
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              )}
            </div>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-50">
                  {[
                    { label: '#', width: '60px' },
                    { label: 'Rating', width: '100px' },
                    { label: 'Feedback', width: 'auto' },
                    { label: 'Location', width: '200px' },
                    { label: 'User', width: '120px' },
                    { label: 'Date', width: '140px' }
                  ].map((col, i) => (
                    <th 
                      key={i} 
                      className="px-6 py-4 text-left"
                      style={{ width: col.width }}
                    >
                      <Typography variant="caption" className="text-gray-700 font-semibold uppercase tracking-wider">
                        {col.label}
                      </Typography>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-50">
                {getTableData()
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((row, i) => {
                    const scoreCategory = getNpsCategory(row?.rating);
                    return (
                      <tr key={i} className="hover:bg-gray-50/50 transition-colors duration-150">
                        <td className="px-6 py-4">
                          <Typography variant="body2" className="text-gray-600 font-medium">
                            {i + 1 + rowsPerPage * page}
                          </Typography>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center gap-2">
                            <Chip
                              label={`${row?.rating}/10`}
                              size="small"
                              sx={{
                                backgroundColor: `${scoreCategory.color}15`,
                                color: scoreCategory.color,
                                fontWeight: 600,
                                fontSize: '12px'
                              }}
                            />
                            <span className="text-sm">{scoreCategory.emoji}</span>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="max-w-xs">
                            {checkAndReplaceStringWithHyphen(row?.message) ? (
                              <Typography variant="body2" className="text-gray-900">
                                {checkAndReplaceStringWithHyphen(row?.message)}
                              </Typography>
                            ) : (
                              <Typography variant="body2" className="text-gray-400 italic">
                                No message provided
                              </Typography>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="bg-gray-100 rounded-lg px-3 py-1 max-w-fit">
                            <Typography variant="caption" className="text-gray-700 font-mono">
                              {checkAndReplaceStringWithHyphen(row?.path)}
                            </Typography>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <UserPopup
                            label={row?.created_by?.toUpperCase()}
                            staff_id={row?.created_by}
                          />
                        </td>
                        <td className="px-6 py-4">
                          <div>
                            <Typography variant="body2" className="text-gray-900 font-medium">
                              {moment(row?.created_at).format('MMM DD')}
                            </Typography>
                            <Typography variant="caption" className="text-gray-500">
                              {moment(row?.created_at).format('hh:mm A')}
                            </Typography>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
              </tbody>
            </table>
          </div>

          {/* Enhanced Pagination */}
          <div className="px-8 py-6 border-t border-gray-100 bg-gray-50/50">
            {getTableData().length > rowsPerPage ? (
              <div className="flex items-center justify-between">
                <Typography variant="body2" className="text-gray-600">
                  Showing {page * rowsPerPage + 1} to {Math.min((page + 1) * rowsPerPage, getTableData().length)} of {getTableData().length} entries
                </Typography>
                <div className="flex gap-2">
                  <button
                    onClick={() => setPage(Math.max(0, page - 1))}
                    disabled={page === 0}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-150"
                  >
                    Previous
                  </button>
                  <div className="px-4 py-2 text-sm font-semibold text-white bg-indigo-600 rounded-lg">
                    {page + 1}
                  </div>
                  <button
                    onClick={() => setPage(Math.min(Math.ceil(getTableData().length / rowsPerPage) - 1, page + 1))}
                    disabled={page >= Math.ceil(getTableData().length / rowsPerPage) - 1}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-150"
                  >
                    Next
                  </button>
                </div>
              </div>
            ) : (
              <div className="text-center">
                <Typography variant="body2" className="text-gray-600">
                  Showing all {getTableData().length} entries
                </Typography>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
