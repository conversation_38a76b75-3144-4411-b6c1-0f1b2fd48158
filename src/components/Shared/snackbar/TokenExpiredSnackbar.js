import PropTypes from 'prop-types';
import { useRouter } from 'next/router';
import { useEffect, useState, forwardRef } from 'react';

import { IconButton, Snackbar, Alert } from '@mui/material';
import { CheckCircle } from '@mui/icons-material';
import { TailSpin } from 'react-loader-spinner';

import { AUM_ENDPOINT } from '../../../utils/aum';
import { setSession } from '../../../utils/auth/utils';
import { useAuthContext } from '../../../utils/auth/useAuthContext';

// eslint-disable-next-line
const MuiAlert = forwardRef(function MuiAlert(props, ref) {
  return <Alert elevation={6} ref={ref} variant="filled" {...props} />;
});

// ----------------------------------------------------------------------

TokenExpiredSnackbar.propTypes = {
  children: PropTypes.node,
};

const jwtDecode = (token) => {
  // Check if token is defined and has the expected format
  if (!token || typeof token !== 'string' || !token.includes('.')) {
    return { exp: 0 }; // Return a default object with exp set to 0
  }

  const parts = token.split('.');
  if (parts.length < 2) {
    return { exp: 0 }; // Return a default object if token doesn't have enough parts
  }

  const base64Url = parts[1];
  if (!base64Url) {
    return { exp: 0 }; // Return a default object if the second part is missing
  }

  const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');

  try {
    const jsonPayload = decodeURIComponent(
      window
        .atob(base64)
        .split('')
        .map((c) => `%${`00${c.charCodeAt(0).toString(16)}`.slice(-2)}`)
        .join('')
    );

    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Error decoding JWT token:', error);
    return { exp: 0 }; // Return default object if decoding fails
  }
};

export default function TokenExpiredSnackbar({ children }) {
  const { logout, isAuthenticated } = useAuthContext();
  const { asPath } = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [refreshTokenExpiredTimerId, setRefreshTokenExpiredTimerId] = useState(null);
  const [accessTokenExpiredTimerId, setAccessTokenExpiredTimerId] = useState(null);

  const handleSessionRenew = async () => {
    setIsLoading(true);
    const response = await fetch(`${AUM_ENDPOINT}/authentication/v1/regenerate`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${localStorage.getItem('refreshToken')}`,
      },
      body: '',
    });

    const payload = await response.json();

    if (payload.status === 'success') {
      setSession(payload.token, payload.refresh_token);
      handleTokenExpired(jwtDecode(payload.token)?.exp, jwtDecode(payload.refresh_token)?.exp);
      // initialize();
      setSnackbarOpen(false);
    }
    setIsLoading(false);
    if (window) window.location.reload();
  };

  const handleTokenExpired = (accessTokenExp, refreshTokenExp) => {
    if (refreshTokenExpiredTimerId && accessTokenExpiredTimerId) {
      clearTimeout(refreshTokenExpiredTimerId);
      clearTimeout(accessTokenExpiredTimerId);
      setRefreshTokenExpiredTimerId(null);
      setAccessTokenExpiredTimerId(null);
    }

    const currentTime = Date.now();
    const refreshTokenTimeLeft = refreshTokenExp * 1000 - 90000 - currentTime;
    const accessTokenTimeLeft = accessTokenExp * 1000 - 90000 - currentTime;

    if (refreshTokenTimeLeft <= 0) {
      logout();
      return;
    }
    const refreshTokenExpiredTimer = setTimeout(() => {
      setSnackbarOpen(false);
      logout();
    }, refreshTokenTimeLeft);
    const accessTokenExpiredTimer = setTimeout(() => {
      setSnackbarOpen(true);
    }, accessTokenTimeLeft);
    setRefreshTokenExpiredTimerId(refreshTokenExpiredTimer);
    setAccessTokenExpiredTimerId(accessTokenExpiredTimer);
  };

  useEffect(() => {
    const accessToken = localStorage.getItem('accessToken');
    const refreshToken = localStorage.getItem('refreshToken');
    if (accessToken && refreshToken) {
      handleTokenExpired(jwtDecode(accessToken)?.exp, jwtDecode(refreshToken)?.exp);
    }
    // eslint-disable-next-line
  }, [isAuthenticated, asPath]);

  return (
    <>
      <Snackbar
        open={snackbarOpen}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <MuiAlert
          severity="info"
          action={
            <IconButton size="small" color="inherit" onClick={() => handleSessionRenew()}>
              {!isLoading ? (
                <CheckCircle fontSize="small" />
              ) : (
                <TailSpin height="25" width="25" radius="10" visible />
              )}
            </IconButton>
          }
        >
          Session expired, continue using?
        </MuiAlert>
      </Snackbar>
      {children}
    </>
  );
}
