// Next, React, Tw
import { useState, useRef } from 'react';
import { twMerge } from 'tailwind-merge';
import { useDispatch } from 'react-redux';

// Mui
import { Popover } from '@mui/material';
import { ImageOutlined } from '@mui/icons-material';

// Packages
import PropTypes from 'prop-types';

// Others
import { useSnackbar } from '../snackbar';
import { setIsLoading } from '../../../utils/store/loadingReducer';

const ImagePicker = ({ children, name, value, onChange, minDimension = 0 }) => {
  // Standard and Vars
  const fileInputRef = useRef(null);
  const { enqueueSnackbar } = useSnackbar();
  const [popOverPosition, setPopOverPosition] = useState(null);
  const dispatch = useDispatch();

  // Others
  const buttonStyle = 'text-sm hover:brightness-150 text-white font-semibold p-2';

  const image = (() => {
    if (![undefined, '', null]?.includes(value)) {
      return (
        <img
          src={value}
          alt="Attached"
          width={200}
          height={200}
          className="z-0 h-[200px] w-[200px] rounded-full"
        />
      );
    }
    return <ImageOutlined className="z-0 h-[150px] w-[150px]" />;
  })();

  const checkImageDimension = (file) =>
    new Promise((resolve, reject) => {
      const fileReader = new FileReader();
      fileReader.readAsDataURL(file);
      fileReader.onload = (e) => {
        const img = new Image();
        img.src = e.target.result;
        img.onload = () => {
          resolve(img);
        };
      };
      fileReader.onerror = (error) => {
        reject(error);
      };
    });

  const convertToBase64 = (file) =>
    new Promise((resolve, reject) => {
      const fileReader = new FileReader();
      fileReader.readAsDataURL(file);
      fileReader.onload = () => {
        resolve(fileReader.result);
      };
      fileReader.onerror = (error) => {
        reject(error);
      };
    });

  const compressImage = (image, maxFileSizeMB = 2) =>
    new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      // Set canvas dimensions to image dimensions
      canvas.width = image.width;
      canvas.height = image.height;
      
      // Draw image on canvas
      ctx.drawImage(image, 0, 0, canvas.width, canvas.height);
      
      // Start with high quality
      let quality = 0.9;
      let result = canvas.toDataURL('image/jpeg', quality);
      
      // Check if we need to compress (simulating file size check)
      const approximateSize = (result.length * 3) / 4 / (1024 * 1024);
      
      // If the image is already small enough, return it
      if (approximateSize <= maxFileSizeMB) {
        resolve(result);
        return;
      }
      
      // Compress until file size is acceptable (maximum 5 iterations)
      let iterations = 0;
      const maxIterations = 5;
      
      const compress = () => {
        iterations += 1;
        // Reduce quality by 0.2 each time
        quality -= 0.2;
        if (quality < 0.1) quality = 0.1;
        
        result = canvas.toDataURL('image/jpeg', quality);
        const newSize = (result.length * 3) / 4 / (1024 * 1024);
        
        if (newSize <= maxFileSizeMB || iterations >= maxIterations) {
          resolve(result);
        } else {
          compress();
        }
      };
      
      compress();
    });

  return (
    <div className="group relative flex h-[200px] w-[200px] items-center justify-center rounded-full border">
      {image}
      <button
        type="button"
        className="absolute left-0 top-0 z-10 hidden h-full w-full rounded-full bg-black bg-opacity-50 text-center text-white group-hover:block "
        onClick={(event) => {
          setPopOverPosition(popOverPosition === null ? event.currentTarget : null);
        }}
      >
        CHANGE <br /> IMAGE
      </button>
      <Popover
        open={popOverPosition !== null}
        anchorEl={popOverPosition}
        onClose={() => setPopOverPosition(null)}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
      >
        <div className="flex flex-col rounded-lg">
          <button
            type="button"
            className={twMerge(buttonStyle, 'bg-green-500')}
            onClick={() => {
              fileInputRef.current.click();
              setPopOverPosition(null);
            }}
          >
            Upload photo
          </button>
          {value !== '' && (
            <button
              type="button"
              className={twMerge(buttonStyle, 'bg-red-500')}
              onClick={() => {
                if (onChange) onChange({ target: { name, value: '' } });
                setPopOverPosition(null);
              }}
            >
              Remove photo
            </button>
          )}
        </div>
      </Popover>
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={async (event) => {
          const file = event.target.files[0];
          const fileExtension = file.type.split('/')[file.type.split('/').length - 1];
          const fileSizeInMB = file.size / 1024 / 1024;

          if (!['jpeg', 'jpg', 'png', 'webp'].includes(fileExtension)) {
            enqueueSnackbar('Please select an image with .jpeg, .jpg, or .png extension only.', {
              variant: 'error',
            });
            return;
          }
          if (fileSizeInMB > 50) {
            enqueueSnackbar('Please select a smaller image file size (max 50MB).', {
              variant: 'error',
            });
            return;
          }
          try {
            const imageObj = await checkImageDimension(file);
            if (imageObj.width < minDimension || imageObj.height < minDimension) {
              enqueueSnackbar(
                `Min dimesion of the image must be at least ${minDimension}x${minDimension}.`,
                {
                  variant: 'error',
                }
              );
              return;
            }
            
            dispatch(setIsLoading(true));
            
            // Use compression for large files
            let imageData;
            if (fileSizeInMB > 5) {
              // Compress the image
              imageData = await compressImage(imageObj);
              console.log('Image compressed successfully');
            } else {
              // Use original image for smaller files
              imageData = await convertToBase64(file);
            }
            
            if (onChange) onChange({ target: { name, value: String(imageData) } });
            
          } catch (error) {
            console.error('Image processing error:', error);
            enqueueSnackbar('Something went wrong processing the image.', {
              variant: 'error',
            });
          } finally {
            dispatch(setIsLoading(false));
          }
        }}
      />
    </div>
  );
};

ImagePicker.propTypes = {
  children: PropTypes.node,
  name: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func,
  minDimension: PropTypes.number,
};

export default ImagePicker;
