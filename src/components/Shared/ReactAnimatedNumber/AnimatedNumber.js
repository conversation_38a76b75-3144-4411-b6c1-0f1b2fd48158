import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';

const AnimatedNumber = ({
  value,
  formatValue,
  duration = 1000,
  delay = 0,
  stepPrecision = 0,
  style = {},
  frameStyle,
  className = '',
  ...props
}) => {
  const [currentValue, setCurrentValue] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const animationRef = useRef(null);
  const startTimeRef = useRef(null);
  const startValueRef = useRef(0);

  const animate = (timestamp) => {
    if (!startTimeRef.current) {
      startTimeRef.current = timestamp;
    }

    const elapsed = timestamp - startTimeRef.current;
    const progress = Math.min(elapsed / duration, 1);

    // Easing function (ease-out)
    const easeOut = 1 - Math.pow(1 - progress, 3);
    
    const newValue = startValueRef.current + (value - startValueRef.current) * easeOut;
    
    if (stepPrecision > 0) {
      setCurrentValue(Number(newValue.toFixed(stepPrecision)));
    } else {
      setCurrentValue(Math.round(newValue));
    }

    if (progress < 1) {
      animationRef.current = requestAnimationFrame(animate);
    } else {
      setCurrentValue(value);
      setIsAnimating(false);
      startTimeRef.current = null;
    }
  };

  const startAnimation = () => {
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
    
    startValueRef.current = currentValue;
    startTimeRef.current = null;
    setIsAnimating(true);
    
    if (delay > 0) {
      setTimeout(() => {
        animationRef.current = requestAnimationFrame(animate);
      }, delay);
    } else {
      animationRef.current = requestAnimationFrame(animate);
    }
  };

  useEffect(() => {
    if (value !== currentValue) {
      startAnimation();
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [value]);

  useEffect(() => {
    // Cleanup animation on unmount
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  const displayValue = formatValue ? formatValue(currentValue) : currentValue;

  return (
    <span
      className={className}
      style={frameStyle || style}
      {...props}
    >
      {displayValue}
    </span>
  );
};

AnimatedNumber.propTypes = {
  value: PropTypes.number.isRequired,
  formatValue: PropTypes.func,
  duration: PropTypes.number,
  delay: PropTypes.number,
  stepPrecision: PropTypes.number,
  style: PropTypes.object,
  frameStyle: PropTypes.object,
  className: PropTypes.string,
};

export default AnimatedNumber;
