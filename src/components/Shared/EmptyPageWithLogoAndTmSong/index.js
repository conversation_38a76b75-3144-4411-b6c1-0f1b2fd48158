// Next, React, Tw
import { useRouter } from 'next/router';
import Image from 'next/image';

// others
import { getModuleFromPath } from '../../../utils/shared';

const EmptyPageWithLogoAndTmSong = () => {
  // Standard and Vars
  const { asPath } = useRouter();
  return (
    <div className="flex h-full w-full items-center justify-center bg-white dark:bg-gray-800">
      <Image
        src={`/assets/moduleLogo/${getModuleFromPath(asPath)}.webp`}
        alt="Logo"
        width={100}
        height={100}
        loading="eager"
        priority
        className="rounded-xl shadow-dashboardCard dark:shadow-dashboardCardDark"
      />
    </div>
  );
};

export default EmptyPageWithLogoAndTmSong;
