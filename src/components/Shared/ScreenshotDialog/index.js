// Next, React, Tw
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';

// Packages
import PropTypes from 'prop-types';
import { Close } from '@mui/icons-material';
import { useSnackbar } from 'notistack';

// Others
import { useParamContext } from '../../../utils/auth/ParamProvider';
import axios from '../../../utils/axios';
import { SCRAPPER_ENDPOINT } from '../../../utils/scrapper';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { useSimiContext } from '../../../utils/simi';

const ScreenshotDialog = ({ children, fileName, isLandscape = false }) => {
  // Standard and Vars
  const { setParam } = useParamContext();
  const { query, asPath } = useRouter();
  const { screenshotDialogOpen, isTakingScreenshot } = query;
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const { moduleColorCode } = useSimiContext();

  return (
    <Dialog
      fullScreen
      open={screenshotDialogOpen === 'true'}
      onClose={() => setParam({ screenshotDialogOpen: 'false' })}
    >
      {[undefined, 'false', null]?.includes(isTakingScreenshot) && (
        <DialogTitle style={{ backgroundColor: moduleColorCode }}>
          <div className="flex w-full justify-end">
            <button type="button" onClick={() => setParam({ screenshotDialogOpen: 'false' })}>
              <Close className="text-white" />
            </button>
          </div>
        </DialogTitle>
      )}
      <DialogContent>{children}</DialogContent>
      {[undefined, 'false', null]?.includes(isTakingScreenshot) && (
        <DialogActions>
          <div className="flex w-full justify-end">
            <button
              type="button"
              className="cta-btn"
              style={{ backgroundColor: moduleColorCode }}
              onClick={async () => {
                dispatch(setIsLoading(true));
                try {
                  const updatedUrl = new URL(
                    `${process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT}${asPath}`
                  );
                  const response = await axios.get(
                    `${SCRAPPER_ENDPOINT}/screenshot?url=${encodeURIComponent(updatedUrl)}&isLandscape=${isLandscape}`,
                    {
                      responseType: 'blob',
                    }
                  );
                  const link = document.createElement('a');
                  link.href = URL.createObjectURL(response?.data);
                  link.download = fileName || 'export.pdf';
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                  URL.revokeObjectURL(link.href);
                } catch (error) {
                  enqueueSnackbar('Something went wrong', { variant: 'error' });
                }
                dispatch(setIsLoading(false));
              }}
            >
              Export PDF
            </button>
          </div>
        </DialogActions>
      )}
    </Dialog>
  );
};

ScreenshotDialog.propTypes = {
  children: PropTypes.node,
  fileName: PropTypes.string,
  isLandscape: PropTypes.bool,
};

export default ScreenshotDialog;
