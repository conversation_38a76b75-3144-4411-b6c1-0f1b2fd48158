// Next, React, Tw
import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';

// Packages
import PropTypes from 'prop-types';

// others
import { useSimiContext } from '../../../utils/simi';

const SelectRemoveStaffListContainer = ({
  CALLBACK_FUNCTION,
  INITIAL_STAFF_LIST,
  KEY,
  disabled = false,
  customColor,
}) => {
  // Standard and Vars

  const { moduleColorCode } = useSimiContext();

  const { allStaffs } = useSelector((state) => state.aum);
  const [searchStaffKeyword, setSearchStaffKeyword] = useState('');
  const [filteredStaffList, setFilteredStaffList] = useState([]);
  const [selectedStaffObjectList, setSelectedStaffObjectList] = useState([]);

  useEffect(() => {
    if (allStaffs?.length > 0 && INITIAL_STAFF_LIST?.length > 0) {
      setSelectedStaffObjectList(
        INITIAL_STAFF_LIST?.map((o) => allStaffs?.find((p) => p?.[KEY] === o))
      );
    }
  }, [allStaffs?.length, INITIAL_STAFF_LIST?.length]);

  // Use custom color if provided, otherwise use module color
  const tagColor = customColor || moduleColorCode;

  return (
    <div className="flex w-full flex-wrap items-center gap-1">
      {selectedStaffObjectList?.map((o, i) => (
        <div
          key={i}
          className="group relative flex items-center rounded-md px-2 py-1 text-[10px] text-white"
          style={{ backgroundColor: tagColor }}
        >
          <span className="peer whitespace-nowrap">{o?.name}</span>
          <button
            type="button"
            className="absolute right-0 top-0 -mx-1 -my-1 hidden h-[12px] w-[12px] items-center justify-center rounded-full bg-black text-[10px] text-white group-hover:flex"
            onClick={() =>
              setSelectedStaffObjectList((prev) => {
                const temp = [...prev];
                temp?.splice(i, 1);
                if (CALLBACK_FUNCTION) CALLBACK_FUNCTION(temp);
                return temp;
              })
            }
            disabled={disabled}
          >
            X
          </button>
        </div>
      ))}
      {!disabled && (
        <div className="relative">
          <input
            type="text"
            placeholder="Type a name"
            value={searchStaffKeyword}
            onChange={(e) => {
              const input = e.target.value;
              setSearchStaffKeyword(input);
              if (input.length > 0) {
                const filtered = allStaffs.filter((o) =>
                  o?.name?.toLowerCase().includes(input.toLowerCase())
                );
                setFilteredStaffList(filtered);
              } else {
                setFilteredStaffList([]);
              }
            }}
            className="h-[30px] w-[200px] rounded-lg  bg-white px-2 py-2 text-sm text-black focus:outline-none "
          />
          {filteredStaffList.length > 0 && (
            <ul className="absolute z-50 mt-2 w-full rounded border border-gray-300 bg-white shadow-lg">
              {filteredStaffList.map((o, index) => (
                // eslint-disable-next-line
                <li
                  key={index}
                  className="flex h-10 cursor-pointer flex-col justify-center p-1 text-xs hover:bg-indigo-200"
                  onClick={() => {
                    setSearchStaffKeyword('');
                    setFilteredStaffList([]);
                    const temp = [...selectedStaffObjectList, o];
                    setSelectedStaffObjectList(temp);
                    if (CALLBACK_FUNCTION) CALLBACK_FUNCTION(temp);
                  }}
                >
                  {o?.name}
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  );
};

SelectRemoveStaffListContainer.propTypes = {
  CALLBACK_FUNCTION: PropTypes.func,
  INITIAL_STAFF_LIST: PropTypes.array,
  KEY: PropTypes.string,
  disabled: PropTypes.bool,
  customColor: PropTypes.string,
};

export default SelectRemoveStaffListContainer;
