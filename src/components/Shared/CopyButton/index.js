// Mui
import { ContentCopy } from '@mui/icons-material';

// Packages
import PropTypes from 'prop-types';

// Components
import { useSnackbar } from 'notistack';

const CopyButton = ({ content }) => {
  // Standard and Vars
  const { enqueueSnackbar } = useSnackbar();

  return (
    <button
      type="button"
      onClick={async () => {
        try {
          await navigator.clipboard.writeText(content);
          enqueueSnackbar(`Copied.`, {
            variant: 'success',
          });
        } catch (error) {
          enqueueSnackbar(`Failed.`, {
            variant: 'error',
          });
        }
      }}
    >
      <ContentCopy className="text-sm" />
    </button>
  );
};

CopyButton.propTypes = {
  content: PropTypes.string || PropTypes.number,
};

export default CopyButton;
