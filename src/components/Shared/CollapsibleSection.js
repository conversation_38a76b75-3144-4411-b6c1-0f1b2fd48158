import { useState, useEffect } from 'react';
import { useMediaQuery } from '@mui/material';

const CollapsibleSection = ({
  id,
  title,
  icon,
  defaultOpen = false,
  alwaysOpenOnMobile = false,
  rightComponent,
  children,
  className = '',
  // New accordion props
  accordionMode = false,
  onToggle,
  isOpen: externalIsOpen,
}) => {
  // In accordion mode, use external state; otherwise use internal state
  const [internalIsOpen, setInternalIsOpen] = useState(() => {
    if (accordionMode) return false; // Accordion mode handles state externally
    
    if (typeof window === 'undefined') return defaultOpen;
    
    // Try to get saved state from localStorage
    const savedState = localStorage.getItem(`section-${id}`);
    if (savedState !== null) {
      return savedState === 'true';
    }
    
    return defaultOpen;
  });

  const isMobile = useMediaQuery('(max-width: 768px)');
  
  // Use external state if in accordion mode, otherwise use internal state
  const isOpen = accordionMode ? externalIsOpen : internalIsOpen;

  // Debug logging
  useEffect(() => {
    if (accordionMode) {
      console.log(`[Accordion Debug] Section ${id}: isOpen=${isOpen}, externalIsOpen=${externalIsOpen}`);
    }
  }, [accordionMode, id, isOpen, externalIsOpen]);

  // Handle auto-collapse on window resize (only for non-accordion mode)
  useEffect(() => {
    if (accordionMode) return;
    
    if (isMobile && !alwaysOpenOnMobile) {
      setInternalIsOpen(false);
    } else if (isMobile && alwaysOpenOnMobile) {
      setInternalIsOpen(defaultOpen);
    } else {
      // Restore from localStorage or use default
      const savedState = localStorage.getItem(`section-${id}`);
      if (savedState !== null) {
        setInternalIsOpen(savedState === 'true');
      } else {
        setInternalIsOpen(defaultOpen);
      }
    }
  }, [isMobile, alwaysOpenOnMobile, defaultOpen, id, accordionMode]);

  // Save state to localStorage when it changes (only for non-accordion mode)
  useEffect(() => {
    if (accordionMode) return;
    
    if (typeof window !== 'undefined') {
      localStorage.setItem(`section-${id}`, internalIsOpen.toString());
    }
  }, [internalIsOpen, id, accordionMode]);

  const handleToggle = () => {
    if (accordionMode) {
      // In accordion mode, notify parent
      console.log(`[Accordion Debug] Toggling section ${id}`);
      onToggle && onToggle(id);
    } else {
      // In normal mode, toggle internal state
      setInternalIsOpen(!internalIsOpen);
    }
  };

  return (
    <div id={id} className={`mb-6 rounded-lg bg-white shadow-sm dark:bg-gray-800 ${className}`}>
      <div className="flex w-full items-center justify-between border-b border-gray-200 bg-gray-50 px-6 py-4 dark:border-gray-700 dark:bg-gray-700">
        <button
          className="flex items-center gap-2 flex-grow"
          onClick={handleToggle}
          aria-expanded={isOpen}
        >
          {icon && <span className="text-primary">{icon}</span>}
          <h2 className="text-lg font-semibold">{title}</h2>
        </button>

        <div className="flex items-center gap-3">
          {rightComponent && (
            <div onClick={(e) => e.stopPropagation()}>
              {rightComponent}
            </div>
          )}
          <button
            onClick={handleToggle}
            aria-expanded={isOpen}
            className="p-1"
          >
            <svg
              className={`h-5 w-5 transition-transform duration-200 ${isOpen ? 'rotate-180 transform' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>
      </div>

      {/* Improved collapse animation with better CSS */}
      <div
        className={`transition-all duration-300 ease-in-out overflow-hidden ${
          isOpen 
            ? 'max-h-[5000px] opacity-100' 
            : 'max-h-0 opacity-0'
        }`}
      >
        {isOpen && <div className="p-6">{children}</div>}
      </div>
    </div>
  );
};

export default CollapsibleSection;
