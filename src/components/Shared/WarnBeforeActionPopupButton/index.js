// Next, React, Tw
import { useState } from 'react';
import { useRouter } from 'next/router';
import { twMerge } from 'tailwind-merge';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';

// Packages
import PropTypes from 'prop-types';

// Others
import { getModuleFromPath, getColorCode } from '../../../utils/shared';

const WarnBeforeActionPopupButton = ({
  onCancel,
  onApprove,
  children,
  disabled,
  positiveSentiment = false,
}) => {
  // Standard and Vars
  const { asPath } = useRouter();
  const module = getModuleFromPath(asPath);

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);

  return (
    <>
      <button
        type="button"
        className={twMerge(!children && 'cta-btn', !children && 'bg-red-500')}
        disabled={disabled}
        onClick={async () => setDialogOpen(true)}
      >
        {children && children}
        {!children && positiveSentiment && 'Save'}
        {!children && !positiveSentiment && 'Delete'}
      </button>
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)}>
        <DialogTitle
          className="text-center text-white"
          sx={{ backgroundColor: getColorCode(module) }}
        >
          {positiveSentiment && 'Proceed?'}
          {!positiveSentiment && 'Hold !!!'}
        </DialogTitle>
        <DialogContent>
          <div className="mt-8 flex w-full flex-col items-center gap-4 p-2 text-center text-xs md:w-[400px]">
            {!positiveSentiment && 'You are about to perform a dangerous action! '}
            Are you sure?
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full items-center justify-between gap-4">
            <button
              type="button"
              onClick={() => {
                if (onCancel) onCancel();
                setDialogOpen(false);
              }}
              className="cta-btn text-black"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={async () => {
                if (onApprove) onApprove();
                setDialogOpen(false);
              }}
              className="cta-btn"
              style={{ backgroundColor: getColorCode(module) }}
            >
              Yes
            </button>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

WarnBeforeActionPopupButton.propTypes = {
  children: PropTypes.any,
  onCancel: PropTypes.any,
  onApprove: PropTypes.any,
  disabled: PropTypes.bool,
  positiveSentiment: PropTypes.bool,
};

export default WarnBeforeActionPopupButton;
