// Next, React, Tw
import Image from 'next/image';
import { useState, useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Dialog, DialogTitle, Tooltip } from '@mui/material';
import { Upload, Delete } from '@mui/icons-material';

// Packages
import PropTypes from 'prop-types';
import DocViewer, { DocViewerRenderers } from '@cyntler/react-doc-viewer';
import '@cyntler/react-doc-viewer/dist/index.css';

// Others
import axios from '../../../utils/axios';
import { useSnackbar } from '../snackbar';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { useParamContext } from '../../../utils/auth/ParamProvider';
import { useSimiContext } from '../../../utils/simi';

const AttachmentBox = ({
  TITLE = 'Attachments',
  HEIGHT = '300px',
  GET_ALL_FILES_ENDPOINT,
  UPLOAD_CERTAIN_FILE_ENDPOINT,
  DOWNLOAD_CERTAIN_FILE_ENDPOINT,
  DELETE_CERTAIN_FILE_ENDPOINT,
  DISABLE_UPLOAD = false,
  CALLBACK_UPON_SUCCESSFUL_UPLOAD,
  CALLBACK_UPON_SUCCESSFUL_DELETE,
  exportMode = false
}) => {
  // Standard and Vars
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { viewAttachmentDialogOpen } = query;
  const { setParam } = useParamContext();
  const { moduleColorCode } = useSimiContext();

  const [fileList, setFileList] = useState([]);
  const fileInputRef = useRef(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [focusedFile, setFocusedFile] = useState({});
  const [pdfUrl, setPdfUrl] = useState(null);

  // Others
  const handleActionButtonClick = async (action, event) => {
    dispatch(setIsLoading(true));
    if (action) {
      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(
              `${UPLOAD_CERTAIN_FILE_ENDPOINT}`,
              { file: event.target.files[0] },
              {
                headers: {
                  'Content-Type': 'multipart/form-data',
                },
              }
            );
            break;
          default:
            break;
        }
        if (response.data.status === 'success') {
          switch (action) {
            case 'post':
              if (CALLBACK_UPON_SUCCESSFUL_UPLOAD) CALLBACK_UPON_SUCCESSFUL_UPLOAD();
              break;
            default:
              break;
          }
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success' || response?.status === 201) {
          statusVariant = 'success';
          message = 'Success';
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }

        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        enqueueSnackbar('Something went wrong', {
          variant: 'error',
        });
      }
    }
    dispatch(setIsLoading(false));
    fetchData();
  };

  const getIcon = (fileType) => {
    if (fileType === 'pdf') return 'pdf.svg';

    if (['png', 'jpg', 'jpeg', 'webp']?.includes(fileType)) return 'image.svg';

    if (['ppt', 'pptx']?.includes(fileType)) return 'ppt.svg';

    if (['docx']?.includes(fileType)) return 'word.svg';

    if (['csv', 'xlsx']?.includes(fileType)) return 'sheets.svg';

    return 'document.svg';
  };

  const downloadFile = async (file) => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${DOWNLOAD_CERTAIN_FILE_ENDPOINT}/${file?.id}`, {
        responseType: 'blob',
      });

      const type = file?.file_name.split('.').pop();

      const typeMimeDictionary = {
        pdf: 'application/pdf',
      };

      if (Object?.keys(typeMimeDictionary)?.includes(type)) {
        const blobUrl = URL.createObjectURL(
          new Blob([response?.data], { type: typeMimeDictionary[type] })
        );
        setPdfUrl(blobUrl);
        setParam({ viewAttachmentDialogOpen: 'true' });
      } else {
        const link = document.createElement('a');
        link.href = URL.createObjectURL(response?.data);
        link.download = file?.file_name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);
      }
    } catch {
      enqueueSnackbar('Something went wrong', { variant: 'error' });
    }
    dispatch(setIsLoading(false));
  };

  const handleDelete = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.delete(`${DELETE_CERTAIN_FILE_ENDPOINT}/${focusedFile?.id}`);

      let statusVariant = 'error';
      let message = 'Failed';
      if (response.data.status === 'success' || response?.status === 204) {
        statusVariant = 'success';
        message = 'Success';
      }

      enqueueSnackbar(message, {
        variant: statusVariant,
      });
    } catch {
      enqueueSnackbar('Something went wrong', { variant: 'error' });
    }
    setDialogOpen(false);
    fetchData();
    dispatch(setIsLoading(false));
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${GET_ALL_FILES_ENDPOINT}`);
      setFileList(response?.data?.data || []);
    } catch {
      setFileList([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div
        className={`flex w-full flex-col gap-4 overflow-y-scroll rounded-lg border border-slate-300 bg-white p-4 text-black scrollbar-none`}
        style={{ height: HEIGHT }}
      >
        <p className="text-center text-sm">{TITLE}</p>
        {!exportMode && !DISABLE_UPLOAD && (
          <div className="flex w-full justify-end">
            <button
              type="button"
              className="rounded-md border border-gray-300 px-1 text-sm font-semibold text-gray-500"
              onClick={() => fileInputRef.current.click()}
            >
              <div className="flex items-center gap-2">
                <span>
                  <Upload />
                </span>
                Upload
                <input
                  type="file"
                  ref={fileInputRef}
                  className="hidden"
                  onChange={(event) => handleActionButtonClick('post', event)}
                />
              </div>
            </button>
          </div>
        )}

        <div className="flex flex-col gap-2 p-4">
          {fileList.map((o, i) => (
            <div className="flex w-full justify-between gap-2" key={i}>
              <button
                type="button"
                onClick={() => downloadFile(o)}
                className="w-[4/5] overflow-hidden"
              >
                <div className="flex items-center gap-2">
                  <Image
                    src={`/assets/icons/${getIcon(o?.file_name.split('.')[1])}`}
                    alt="File Logo"
                    height={25}
                    width={25}
                  />
                  <div className="flex w-full  flex-col gap-1 whitespace-nowrap">
                    <Tooltip title={o?.file_name}>
                      <p className="text-sm">{o?.file_name}</p>
                    </Tooltip>
                  </div>
                </div>
              </button>
              {!DISABLE_UPLOAD && (
                <button
                  type="button"
                  onClick={() => {
                    setFocusedFile(o);
                    setDialogOpen(true);
                  }}
                >
                  <Delete className="hover:text-red-500" />
                </button>
              )}
            </div>
          ))}
        </div>
      </div>

      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)}>
        <DialogTitle
          className=" text-center text-white"
          style={{ backgroundColor: moduleColorCode }}
        >
          Delete Attachment?
        </DialogTitle>

        <div className="flex flex-col gap-4 p-4">
          <div className="flex items-center gap-4">
            <Image
              src={`/assets/icons/${getIcon(focusedFile?.file_name?.split('.')[1])}`}
              alt="File Logo"
              height={25}
              width={25}
            />
            <div className="flex flex-col gap-1 overflow-x-hidden">
              <p className="text-sm">{focusedFile?.file_name}</p>
            </div>
          </div>
        </div>

        <div className="flex justify-between gap-4 p-4">
          <button type="button" onClick={() => setDialogOpen(false)} className="p-2">
            Cancel
          </button>
          <button type="button" onClick={() => handleDelete()} className="cta-btn bg-red-500">
            Proceed
          </button>
        </div>
      </Dialog>
      <Dialog
        open={viewAttachmentDialogOpen === 'true'}
        fullScreen
        onClose={() => setParam({ viewAttachmentDialogOpen: 'false' })}
      >
        <DocViewer
          documents={[
            {
              uri: pdfUrl,
            },
          ]}
          config={{
            header: {
              disableHeader: true,
              disableFileName: true,
              retainURLParams: false,
            },
            pdfVerticalScrollByDefault: true,
          }}
          pluginRenderers={DocViewerRenderers}
        />
      </Dialog>
    </>
  );
};

AttachmentBox.propTypes = {
  TITLE: PropTypes.string,
  HEIGHT: PropTypes.string,
  GET_ALL_FILES_ENDPOINT: PropTypes.string,
  UPLOAD_CERTAIN_FILE_ENDPOINT: PropTypes.string,
  DOWNLOAD_CERTAIN_FILE_ENDPOINT: PropTypes.string,
  DELETE_CERTAIN_FILE_ENDPOINT: PropTypes.string,
  DISABLE_UPLOAD: PropTypes.bool,
  CALLBACK_UPON_SUCCESSFUL_UPLOAD: PropTypes.func,
  CALLBACK_UPON_SUCCESSFUL_DELETE: PropTypes.func,
  exportMode: PropTypes.bool,
};

export default AttachmentBox;
