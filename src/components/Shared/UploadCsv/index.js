// Next, React, Tw
import { useRef } from 'react';

// Packages
import PropTypes from 'prop-types';
import <PERSON> from 'papaparse';
import * as xlsx from 'xlsx';

// Components
import ExportExcelButton from '../ExportExcelButton';

// Others
import { useSnackbar } from '../snackbar';
import { useSimiContext } from '../../../utils/simi';

const UploadCsvButton = ({
  templateInJson,
  onUpload,
  children,
  onlyAccept = ['csv', 'xlsx'],
  headerIsAtRow = 0,
  disabled,
}) => {
  // Standard
  const { enqueueSnackbar } = useSnackbar();
  const { moduleColorCode } = useSimiContext();

  const fileInputRef = useRef(null);

  return (
    <div className="flex items-center gap-2">
      {templateInJson && (
        <ExportExcelButton data={[templateInJson]} filename="template.csv">
          <p className="text-blue-500 underline">CSV Template</p>
        </ExportExcelButton>
      )}
      {children && (
        <div onClick={() => fileInputRef?.current?.click()}>
          {children}
        </div>
      )}
      {!children && (
        <button
          type="button"
          className="cta-btn"
          style={{ backgroundColor: moduleColorCode }}
          onClick={() => fileInputRef?.current?.click()}
          disabled={disabled}
        >
          Upload {onlyAccept?.includes('xlsx') && 'Excel'}
          {onlyAccept?.length > 1 && '/'}
          {onlyAccept?.includes('csv') && 'CSV'}
        </button>
      )}

      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={(event) => {
          const file = event?.target?.files?.[0];
          if (!file) {
            enqueueSnackbar('No file selected.', { variant: 'error' });
            return;
          }
          const extension = file.name.split('.').pop()?.toLowerCase();

          if (!onlyAccept.includes(extension)) {
            enqueueSnackbar('Please upload a CSV or an XLSX file.', { variant: 'error' });
            return;
          }

          if (extension === 'csv') {
            Papa.parse(file, {
              header: true,
              complete: (results) => {
                if (onUpload) onUpload({ Sheet1: results?.data });
              },
              error: (error) => {
                enqueueSnackbar('Error parsing CSV file.', { variant: 'error' });
              },
            });
          }

          if (extension === 'xlsx') {
            const reader = new FileReader();
            reader.onload = (e) => {
              const data = new Uint8Array(e.target.result);
              const workbook = xlsx.read(data, { type: 'array' });

              const sheetsData = {};
              workbook.SheetNames.forEach((sheetName) => {
                const sheet = workbook.Sheets[sheetName];
                const sheetData = xlsx.utils.sheet_to_json(sheet, { header: 1 });
                const headerRowIndex = headerIsAtRow;
                const headers = sheetData[headerRowIndex];
                const jsonData = xlsx.utils.sheet_to_json(sheet, {
                  header: headers,
                  range: headerRowIndex + 1,
                });

                sheetsData[sheetName] = jsonData;
              });

              if (onUpload) onUpload(sheetsData);
            };
            reader.onerror = () => {
              enqueueSnackbar('Error reading XLSX file.', { variant: 'error' });
            };
            reader.readAsArrayBuffer(file);
          }
        }}
      />
    </div>
  );
};

UploadCsvButton.propTypes = {
  templateInJson: PropTypes.any,
  onUpload: PropTypes.any,
  children: PropTypes.node,
  headerIsAtRow: PropTypes.number,
  disabled: PropTypes.bool,
  onlyAccept: PropTypes.array,
};

export default UploadCsvButton;
