// Next, React, Tw
import { useState, useEffect, useRef } from 'react';
import { twMerge } from 'tailwind-merge';

// Mui
import { Stepper, Step, StepLabel } from '@mui/material';

// Packages
import PropTypes from 'prop-types';
import axios from '../../../utils/axios';

// Others
import { useSimiContext } from '../../../utils/simi';

const HistoryBox = ({ parentHiddenDivRef, GET_ALL_HISTORIES_ENDPOINT, isExporting = false }) => {
  // Standard and Vars
  const hiddenDivRef = useRef(null);
  const { moduleColorCode } = useSimiContext();

  const [historyList, setHistoryList] = useState([]);

  // Others

  const fetchData = async () => {
    try {
      const response = await axios.get(`${GET_ALL_HISTORIES_ENDPOINT}`);
      setHistoryList(response?.data?.data || []);
    } catch {
      setHistoryList([]);
    }
  };

  useEffect(() => {
    fetchData();
    hiddenDivRef.current?.scrollIntoView();
    if (parentHiddenDivRef) parentHiddenDivRef?.current?.scrollIntoView();
    else document.getElementById('scroll-container').scrollTo(0, 0);
  }, []);

  // Add event listener for history refresh
  useEffect(() => {
    if (parentHiddenDivRef && parentHiddenDivRef.current) {
      const refreshHistoryHandler = () => {
        console.log('History refresh event received');
        fetchData();
      };
      
      // Add event listener
      parentHiddenDivRef.current.addEventListener('refreshHistory', refreshHistoryHandler);
      
      // Clean up
      return () => {
        if (parentHiddenDivRef.current) {
          parentHiddenDivRef.current.removeEventListener('refreshHistory', refreshHistoryHandler);
        }
      };
    }
  }, [parentHiddenDivRef]);

  return (
    <div
      className={twMerge(
        'flex w-full flex-col gap-4 overflow-y-scroll rounded-lg border border-slate-300 bg-white p-4 text-black scrollbar-none',
        !isExporting ? 'h-[300px]' : 'min-h-[300px]'
      )}
    >
      <div
        className={twMerge(
          'flex flex-grow flex-col ',
          !isExporting && 'overflow-y-scroll scrollbar-none'
        )}
      >
        <p className="w-full text-center text-sm">History</p>
        {historyList?.length > 0 && (
          <Stepper
            activeStep={historyList.length - 1}
            orientation="vertical"
            className="w-full p-4"
          >
            {historyList.map((history, i) => (
              <Step
                key={i}
                sx={{
                  '& .MuiStepLabel-root .Mui-completed': {
                    color: moduleColorCode,
                  },
                  '& .MuiStepLabel-root .Mui-active': {
                    color: moduleColorCode,
                  },
                }}
              >
                <StepLabel className="p-0">
                  <div className="flex flex-col gap-[2px] px-2">
                    <p className="text-[10px] text-gray-500">{history?.action_by}</p>
                    <p className="text-black">{history?.description}</p>
                    <p className="text-[8px] text-gray-500">{history?.created_at}</p>
                  </div>
                </StepLabel>
              </Step>
            ))}
            <div ref={hiddenDivRef} />
          </Stepper>
        )}
      </div>
    </div>
  );
};

HistoryBox.propTypes = {
  parentHiddenDivRef: PropTypes.any,
  GET_ALL_HISTORIES_ENDPOINT: PropTypes.string,
  isExporting: PropTypes.bool,
};

export default HistoryBox;
