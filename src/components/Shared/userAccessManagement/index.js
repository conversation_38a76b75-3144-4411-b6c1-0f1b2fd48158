// Next, React, Tailwind
import { useEffect, useState, useMemo } from 'react';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';

// Mui
import { Divider } from '@mui/material';

// Packages
import PropTypes from 'prop-types';

// Components
import { TablePaginationCustom } from '../table';
import UserAccessManagementRow from './UserAccessManagementRow';
import { AutoCompleteTextInput, SearchInput } from '../CustomInput';

// Others
import axios from '../../../utils/axios';
import { AUM_ENDPOINT } from '../../../utils/aum';
import { useAuthContext } from '../../../utils/auth/useAuthContext';
import { useSnackbar } from '../snackbar';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { getModuleFromPath, getColorCode } from '../../../utils/shared';
import ExportExcelButton from '../ExportExcelButton';

const AccessManagementTemplate = ({ roleArray = ['pending', 'user', 'admin'] }) => {
  // Standard and Vars
  const { asPath, query, push } = useRouter();
  const { q, division } = query;
  const { user } = useAuthContext();
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const module = getModuleFromPath(asPath);
  const { page, rowsPerPage } = useSelector((state) => state.simi);
  const { allStaffs } = useSelector((state) => state.aum);
  const [toBeManuallyAssignedStaffId, setToBeManuallyAssignedStaffId] = useState(null);
  const [selectedTag, setSelectedTag] = useState('');
  const workgroupOptions = module === 'smart-tool' ? ['MC1', 'FCR', 'GLOBAL'] : [];
  const [activeFilter, setActiveFilter] = useState('');

  // Sorting state
  const [sortConfig, setSortConfig] = useState({
    key: 'name', // Default sort by name
    direction: 'asc',
  });

  // Table
  const [tableData, setTableData] = useState([]);

  // Get the filtered data before sorting
  const getFilteredData = () => {
    let temp = tableData;
    if (division !== 'all') temp = temp.filter((o) => o?.division === division);
    if ([undefined, '']?.includes(q)) {
      if (activeFilter) {
        return temp.filter(
          (user) => user?.modules?.[0]?.role?.toLowerCase() === activeFilter.toLowerCase()
        );
      }
      return temp;
    }
    return temp.filter((o) => JSON.stringify(o).toLowerCase().includes(q.toLowerCase()));
  };

  // Sort function
  const sortedData = useMemo(() => {
    let sortableItems = [...getFilteredData()];
    if (sortConfig.key) {
      sortableItems.sort((a, b) => {
        let aValue, bValue;

        // Extract the correct values based on the sort key
        if (sortConfig.key === 'role') {
          aValue = a?.modules?.[0]?.role || '';
          bValue = b?.modules?.[0]?.role || '';
        } else if (sortConfig.key === 'tag') {
          aValue = a?.modules?.[0]?.tag || '';
          bValue = b?.modules?.[0]?.tag || '';
        } else {
          aValue = a[sortConfig.key] || '';
          bValue = b[sortConfig.key] || '';
        }

        // Handle string comparison
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          if (sortConfig.direction === 'asc') {
            return aValue.localeCompare(bValue);
          } else {
            return bValue.localeCompare(aValue);
          }
        }

        // Handle number comparison
        if (sortConfig.direction === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
    }
    return sortableItems;
  }, [tableData, sortConfig, activeFilter, q, division]);

  // Handle sort request
  const requestSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Toggle filter function
  const toggleFilter = (filterName) => {
    if (activeFilter === filterName) {
      setActiveFilter('');
    } else {
      setActiveFilter(filterName);
    }
  };

  // Form
  const handleManualAssign = async (event) => {
    event?.preventDefault();
    try {
      const response = await axios.post(
        `${AUM_ENDPOINT}/user/v1/module/${toBeManuallyAssignedStaffId}`,
        {
          module,
          role: 'pending',
          tag: module === 'smart-tool' ? selectedTag : '',
        }
      );

      if (response.data.status === 'success') {
        enqueueSnackbar(`${response.data.data}`, { variant: 'success' });
        // Clear the form after successful submission
        setToBeManuallyAssignedStaffId(null);
        setSelectedTag('');
      }
    } catch (error) {
      // Check if error message contains indication that the user already has access
      const errorMessage = error?.response?.data?.message || error?.message || 'An error occurred';
      
      if (errorMessage.toLowerCase().includes('already exist') || 
          errorMessage.toLowerCase().includes('already assigned') ||
          errorMessage.toLowerCase().includes('already has access')) {
        // If user already has access, show a warning notification
        const selectedUserName = allStaffs?.find(s => s.staff_id === toBeManuallyAssignedStaffId)?.name;
        enqueueSnackbar(`${selectedUserName || 'User'} already has access to this module`, { 
          variant: 'warning'
        });
        
        // Clear the form even in this case
        setToBeManuallyAssignedStaffId(null);
        setSelectedTag('');
      } else {
        // For other errors, show error notification and keep form values
        enqueueSnackbar(errorMessage, { variant: 'error' });
      }
    }
    fetchData();
  };

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${AUM_ENDPOINT}/user/v1/module/${module}/all`);
      setTableData(response?.data?.data || []);
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="container mx-auto px-4 py-4">
      {/* Superadmin area in separate card */}
      {user?.isSuperAdmin && (
        <div className="mb-4 rounded-lg bg-white shadow-sm dark:bg-gray-800">
          <div className="border-b border-gray-200 bg-white px-4 py-3">
            <h2 className="text-lg font-medium text-gray-800">Superadmin Area</h2>
          </div>
          <div className="bg-white px-4 py-4">
            <form className="flex flex-wrap items-center gap-3" onSubmit={handleManualAssign}>
              <div className="w-[250px]">
                <AutoCompleteTextInput
                  value={toBeManuallyAssignedStaffId}
                  placeholder=""
                  options={allStaffs?.map((o) => ({
                    label: o?.name,
                    value: o?.staff_id,
                  }))}
                  onChange={(selectedStaffId) => {
                    setToBeManuallyAssignedStaffId(selectedStaffId);
                  }}
                  className="h-9 shadow-sm border border-gray-200 rounded-md focus:ring-0 focus:border-gray-300"
                  hideExternalLabel={true}
                />
              </div>
              {module === 'smart-tool' && (
                <div className="w-[120px]">
                  <select
                    className="focus:ring-primary w-full rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-transparent focus:ring-2"
                    value={selectedTag}
                    onChange={(e) => setSelectedTag(e.target.value)}
                  >
                    <option value="">Select Workgroup</option>
                    {workgroupOptions.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                </div>
              )}
              <button
                type="submit"
                disabled={!toBeManuallyAssignedStaffId || (module === 'smart-tool' && !selectedTag)}
                className="bg-primary hover:bg-primary/90 rounded-lg px-5 py-2 text-sm font-medium text-white shadow-sm transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Assign
              </button>
              <button
                type="button"
                onClick={() => {
                  setToBeManuallyAssignedStaffId(null);
                  setSelectedTag('');
                }}
                className="rounded-lg px-5 py-2 text-sm font-medium text-gray-700 shadow-sm transition-all duration-200 border border-gray-300 hover:bg-gray-50"
              >
                Clear
              </button>
            </form>
          </div>
        </div>
      )}

      {/* Main access management card */}
      <div className="overflow-hidden rounded-lg bg-white shadow-sm dark:bg-gray-800">
        {/* Header with title and search/export */}
        <div className="flex flex-col items-center justify-between gap-3 border-b border-gray-200 bg-white px-4 py-3 sm:flex-row">
          <div className="flex items-center">
            <h2 className="text-lg font-medium text-gray-800">Access Management</h2>
          </div>

          <div className="flex items-center gap-4">
            {/* Legend moved to right side */}
            <div className="mr-0 hidden items-center gap-3 md:flex">
              <div
                className={`flex cursor-pointer items-center gap-1.5 rounded px-2 py-1 transition-colors ${
                  activeFilter === 'admin'
                    ? 'bg-purple-100 ring-1 ring-purple-400'
                    : 'hover:bg-gray-100'
                }`}
                onClick={() => toggleFilter('admin')}
              >
                <span className="h-2.5 w-2.5 rounded-full bg-purple-500" />
                <span className="text-xs text-gray-600">Admin</span>
              </div>
              <div
                className={`flex cursor-pointer items-center gap-1.5 rounded px-2 py-1 transition-colors ${
                  activeFilter === 'user'
                    ? 'bg-green-100 ring-1 ring-green-400'
                    : 'hover:bg-gray-100'
                }`}
                onClick={() => toggleFilter('user')}
              >
                <span className="h-2.5 w-2.5 rounded-full bg-green-500" />
                <span className="text-xs text-gray-600">User</span>
              </div>
              <div
                className={`flex cursor-pointer items-center gap-1.5 rounded px-2 py-1 transition-colors ${
                  activeFilter === 'pending'
                    ? 'bg-amber-100 ring-1 ring-amber-400'
                    : 'hover:bg-gray-100'
                }`}
                onClick={() => toggleFilter('pending')}
              >
                <span className="h-2.5 w-2.5 rounded-full bg-amber-500" />
                <span className="text-xs text-gray-600">Pending</span>
              </div>
              {activeFilter && (
                <div
                  className="flex cursor-pointer items-center gap-1.5 rounded px-2 py-1 hover:bg-gray-100"
                  onClick={() => setActiveFilter('')}
                >
                  <span className="text-xs font-medium text-blue-600">Clear filter</span>
                </div>
              )}
            </div>

            <div className="w-full min-w-[90px] md:w-auto">
              <SearchInput 
                placeholder="Search users..." 
                className="w-full lg:w-[160px]" 
              />
            </div>

            <ExportExcelButton
              data={sortedData}
              header={[
                { label: 'Name', key: 'name' },
                { label: 'Staff ID', key: 'staff_id' },
                { label: 'Division', key: 'division' },
                { label: 'Role', key: 'modules.0.role' },
                { label: 'Tag', key: 'modules.0.tag' },
              ]}
              filename={`${module}-access-management`}
              className="bg-primary hover:bg-primary/90 rounded-lg px-5 py-2 text-sm font-medium text-white shadow-sm transition-all duration-200"
            />
          </div>
        </div>

        {/* Mobile-only legend */}
        <div className="flex flex-wrap items-center justify-center gap-2 border-b border-gray-200 bg-white px-4 py-2 md:hidden">
          <div
            className={`flex cursor-pointer items-center gap-1.5 rounded px-2 py-1 transition-colors ${
              activeFilter === 'admin' ? 'bg-purple-100 ring-1 ring-purple-400' : 'hover:bg-gray-100'
            }`}
            onClick={() => toggleFilter('admin')}
          >
            <span className="h-2.5 w-2.5 rounded-full bg-purple-500" />
            <span className="text-xs text-gray-600">Admin</span>
          </div>
          <div
            className={`flex cursor-pointer items-center gap-1.5 rounded px-2 py-1 transition-colors ${
              activeFilter === 'user' ? 'bg-green-100 ring-1 ring-green-400' : 'hover:bg-gray-100'
            }`}
            onClick={() => toggleFilter('user')}
          >
            <span className="h-2.5 w-2.5 rounded-full bg-green-500" />
            <span className="text-xs text-gray-600">User</span>
          </div>
          <div
            className={`flex cursor-pointer items-center gap-1.5 rounded px-2 py-1 transition-colors ${
              activeFilter === 'pending' ? 'bg-amber-100 ring-1 ring-amber-400' : 'hover:bg-gray-100'
            }`}
            onClick={() => toggleFilter('pending')}
          >
            <span className="h-2.5 w-2.5 rounded-full bg-amber-500" />
            <span className="text-xs text-gray-600">Pending</span>
          </div>
          {activeFilter && (
            <div
              className="flex cursor-pointer items-center gap-1.5 rounded px-2 py-1 hover:bg-gray-100"
              onClick={() => setActiveFilter('')}
            >
              <span className="text-xs font-medium text-blue-600">Clear filter</span>
            </div>
          )}
        </div>

        {/* Show active filter indicator if filtering */}
        {activeFilter && (
          <div className="flex items-center justify-between border-b border-blue-200 bg-blue-50 px-4 py-2 text-sm">
            <div className="flex items-center">
              <span className="text-blue-700">Filtered by:</span>
              <span
                className={`ml-2 rounded-full px-2 py-0.5 text-xs font-medium ${
                  activeFilter === 'admin'
                    ? 'bg-purple-100 text-purple-800'
                    : activeFilter === 'user'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-amber-100 text-amber-800'
                }`}
              >
                {activeFilter.toUpperCase()}
              </span>
            </div>
            <button
              className="text-xs font-medium text-blue-600 hover:text-blue-800"
              onClick={() => setActiveFilter('')}
            >
              Clear Filter
            </button>
          </div>
        )}

        {/* Table section */}
        <div className="overflow-x-auto rounded-b-xl bg-white dark:bg-gray-800">
          {sortedData.length > 0 ? (
            <div className="min-w-full overflow-x-auto">
              <table className="min-w-full table-fixed border-collapse">
                <thead className="sticky top-0 z-10 bg-gray-50">
                  <tr className="border-b border-gray-200">
                    {[
                      {
                        label: 'Name',
                        width: '200px',
                        minWidth: '180px',
                        mobileHide: false,
                        isSticky: true,
                        key: 'name',
                      },
                      {
                        label: 'Staff ID',
                        width: '120px',
                        minWidth: '120px',
                        mobileHide: true,
                        key: 'staff_id',
                      },
                      {
                        label: 'Division',
                        width: '120px',
                        minWidth: '120px',
                        mobileHide: true,
                        key: 'division',
                      },
                      {
                        label: 'Role',
                        width: '120px',
                        minWidth: '100px',
                        mobileHide: false,
                        key: 'role',
                      },
                      ...(module === 'smart-tool'
                        ? [
                            {
                              label: 'Workgroup',
                              width: '120px',
                              minWidth: '120px',
                              mobileHide: true,
                              key: 'tag',
                            },
                          ]
                        : []),
                      { label: 'Actions', width: '100px', minWidth: '100px', mobileHide: false, key: null },
                    ].map((col, i) => (
                      <th
                        key={i}
                        scope="col"
                        className={`border-b-2 border-gray-300 px-3 py-3 text-left text-sm font-semibold uppercase text-gray-800 ${col.mobileHide ? 'hidden md:table-cell' : ''} ${
                          col.isSticky ? 'sticky left-0 z-20 bg-gray-50' : ''
                        }`}
                        style={{ width: col.width, minWidth: col.minWidth }}
                      >
                        <div className="flex items-center">
                          {col.label}
                          {col.key && (
                            <button
                              className="ml-1 text-gray-500 hover:text-gray-700"
                              onClick={() => requestSort(col.key)}
                            >
                              <svg
                                className="h-3.5 w-3.5"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                {sortConfig.key === col.key ? (
                                  sortConfig.direction === 'asc' ? (
                                    // Up arrow for ascending sort
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="2"
                                      d="M5 15l7-7 7 7"
                                    />
                                  ) : (
                                    // Down arrow for descending sort
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth="2"
                                      d="M19 9l-7 7-7-7"
                                    />
                                  )
                                ) : (
                                  // Filter icon for unsorted
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
                                  />
                                )}
                              </svg>
                            </button>
                          )}
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {sortedData
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((row, i) => (
                    <UserAccessManagementRow 
                      key={`${row?.staff_id}-${i}`} 
                      index={i + page * rowsPerPage}
                      data={row} 
                      module={module} 
                      onRefresh={fetchData} 
                      roleArray={roleArray} 
                      workgroupOptions={workgroupOptions}
                      activeFilter={activeFilter}
                    />
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center px-4 py-8 text-center sm:py-16">
              <div className="mb-3 text-gray-300 dark:text-gray-600">
                <svg
                  className="mx-auto h-12 w-12 sm:h-16 sm:w-16"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="1.5"
                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                  ></path>
                </svg>
              </div>
              <p className="text-base font-medium text-gray-700 dark:text-gray-300">
                No users found
              </p>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Try changing your search criteria or assign a new user
              </p>
            </div>
          )}
          
          {sortedData.length > 0 && (
            <div className="border-t border-gray-200 px-4 py-2">
              <TablePaginationCustom count={sortedData.length} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

AccessManagementTemplate.propTypes = {
  roleArray: PropTypes.array,
};

export default AccessManagementTemplate;
