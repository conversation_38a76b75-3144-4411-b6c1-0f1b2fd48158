// Next, React, Tw
import { useState, Fragment } from 'react';
import { twMerge } from 'tailwind-merge';
import { useRouter } from 'next/router';
import { useSelector } from 'react-redux';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions, Popover } from '@mui/material';
import { Edit } from '@mui/icons-material';

// Packages
import PropTypes from 'prop-types';

// Components
import UserPopup from '../UserPopup';

// Others
import { AUM_ENDPOINT } from '../../../utils/aum';
import axios from '../../../utils/axios';
import { useSnackbar } from '../snackbar';
import { useAuthContext } from '../../../utils/auth/useAuthContext';
import { isCertainModuleRole, getColorCode, getModuleFromPath } from '../../../utils/shared';
import { useModuleRoleContext } from '../../../utils/auth/ModuleRoleProvider';

const UserAccessManagementRow = ({ data, index, module, onRefresh, roleArray, workgroupOptions, activeFilter }) => {
  // Standard and Vars
  const { user } = useAuthContext();
  const { enqueueSnackbar } = useSnackbar();
  const { isAdmin } = useModuleRoleContext();
  const { page, rowsPerPage } = useSelector((state) => state.simi);

  const [selectedRole, setSelectedRole] = useState('');
  const [selectedTag, setSelectedTag] = useState('');
  const [localData, setLocalData] = useState(data);
  const [anchorEl, setAnchorEl] = useState(null);

  // Dialog
  const [dialogTitle, setDialogTitle] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [tagDialogOpen, setTagDialogOpen] = useState(false);

  const role = localData?.modules?.[0]?.role?.toLowerCase() || '';
  
  // Determine row background based on role
  let rowBgClass = '';
  if (role === 'admin') {
    rowBgClass = 'bg-purple-50';
  } else if (role === 'user') {
    rowBgClass = 'bg-green-50/50';
  } else if (role === 'pending') {
    rowBgClass = 'bg-amber-50/50';
  } else {
    rowBgClass = 'bg-white';
  }

  // Handle Actions
  const handleActionButtonClick = (role, promotion = true) => {
    setSelectedRole(role);

    let title = `Demote to ${role?.toUpperCase()}?`;
    if (promotion) title = `Promote to ${role?.toUpperCase()}?`;

    setDialogTitle(title);
    setDialogOpen(true);
  };

  const handleTagButtonClick = () => {
    setSelectedTag(localData?.modules?.[0]?.tag || '');
    setTagDialogOpen(true);
  };

  const handleDialogClose = async (approved) => {
    if (!approved) {
      setDialogOpen(false);
      return;
    }
    
    try {
      let response = '';
      if (selectedRole === 'delete') {
        response = await axios.delete(
          `${AUM_ENDPOINT}/user/v1/module/${module}/${localData?.staff_id}`
        );
      } else {
        response = await axios.put(`${AUM_ENDPOINT}/user/v1/module/${localData?.staff_id}`, {
          module,
          role: selectedRole,
          tag: localData?.modules?.[0]?.tag || '',
        });
      }

      if (response.data.status === 'success') {
        enqueueSnackbar(`${response.data.data}`, {});
      }
    } catch {
      enqueueSnackbar('Something went wrong', { variant: 'error' });
    }
    
    if (onRefresh) onRefresh();
    setDialogOpen(false);
  };

  const handleTagDialogClose = async (approved) => {
    if (!approved) {
      setTagDialogOpen(false);
      return;
    }
    
    try {
      const response = await axios.put(
        `${AUM_ENDPOINT}/user/v1/module/${localData?.staff_id}`,
        {
          module,
          role: localData?.modules?.[0]?.role || 'pending',
          tag: selectedTag,
        }
      );

      if (response.data.status === 'success') {
        enqueueSnackbar(`${response.data.data}`, {});
      }
    } catch {
      enqueueSnackbar('Something went wrong', { variant: 'error' });
    }
    
    if (onRefresh) onRefresh();
    setTagDialogOpen(false);
  };

  return (
    <>
      <tr
        key={`${index}-${localData?.staff_id}`}
        className={twMerge(
          'cursor-pointer border-b border-gray-200 transition-colors duration-150 hover:bg-gray-100',
          rowBgClass
        )}
      >
        {/* Name - Sticky Column */}
        <td className={`px-3 py-2.5 text-sm font-medium text-gray-900 sticky left-0 z-10 ${rowBgClass}`}>
          <div className="max-w-[180px] truncate sm:max-w-xs">
            {localData?.name || '-'}
          </div>
          {/* Mobile-only status and staff ID display */}
          <div className="mt-1 flex items-center gap-2 md:hidden">
            <span className="text-xs text-gray-500">
              {localData?.staff_id?.toUpperCase() || '-'}
            </span>
            <span
              className={`inline-block rounded-full px-2 py-0.5 text-xs font-medium ${
                role === 'admin'
                  ? 'bg-purple-100 text-purple-800'
                  : role === 'user'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-amber-100 text-amber-800'
              }`}
            >
              {(localData?.modules?.[0]?.role || 'pending').toUpperCase()}
            </span>
          </div>
        </td>
        
        {/* Staff ID */}
        <td className="hidden px-3 py-2.5 text-sm text-gray-600 md:table-cell">
          <UserPopup
            label={localData?.staff_id?.toUpperCase() || '-'}
            staff_id={localData?.staff_id}
          />
        </td>
        
        {/* Division */}
        <td className="hidden px-3 py-2.5 text-sm text-gray-600 md:table-cell">
          {localData?.division || '-'}
        </td>
        
        {/* Role */}
        <td className="hidden px-3 py-2.5 text-sm md:table-cell">
          <span
            className={`inline-block rounded-full px-2 py-0.5 text-xs font-medium ${
              role === 'admin'
                ? 'bg-purple-100 text-purple-800'
                : role === 'user'
                  ? 'bg-green-100 text-green-800'
                  : 'bg-amber-100 text-amber-800'
            }`}
          >
            {(localData?.modules?.[0]?.role || 'PENDING').toUpperCase()}
          </span>
        </td>
        
        {/* Tag - Only for smart-tool */}
        {module === 'smart-tool' && (
          <td className="hidden px-3 py-2.5 text-sm text-gray-600 md:table-cell">
            {localData?.modules?.[0]?.tag || '-'}
          </td>
        )}
        
        {/* Actions */}
        <td className="px-3 py-2.5 text-center text-sm">
          {isAdmin && localData.staff_id !== user.staff_id && (
            <div className="flex items-center justify-center">
              <button
                type="button"
                className="hover:text-primary text-gray-500 transition-colors"
                onClick={(event) => {
                  event.stopPropagation();
                  setAnchorEl(anchorEl === null ? event.currentTarget : null);
                }}
              >
                <Edit className="h-4 w-4" />
              </button>
              
              <Popover
                open={Boolean(anchorEl)}
                anchorEl={anchorEl}
                onClose={() => {
                  setAnchorEl(null);
                }}
                anchorOrigin={{
                  vertical: 'top',
                  horizontal: 'left',
                }}
                transformOrigin={{
                  vertical: 'bottom',
                  horizontal: 'right',
                }}
                PaperProps={{
                  sx: {
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
                    borderRadius: '0.5rem',
                    overflow: 'hidden',
                  },
                }}
              >
                <div className="flex flex-col overflow-hidden rounded-lg">
                  {roleArray?.map((role, i) => (
                    <Fragment key={i}>
                      {isCertainModuleRole(localData, module, role) && (
                        <>
                          {i !== 0 && (user?.isSuperAdmin || role !== 'admin') && (
                            <button
                              type="button"
                              className="bg-red-500 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-red-600"
                              onClick={() => handleActionButtonClick(roleArray[i - 1], false)}
                            >
                              Demote to {roleArray[i - 1].toUpperCase()}
                            </button>
                          )}
                          {i !== roleArray.length - 1 && (
                            <button
                              type="button"
                              className="bg-green-500 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-green-600"
                              onClick={() => handleActionButtonClick(roleArray[i + 1])}
                            >
                              Promote to {roleArray[i + 1].toUpperCase()}
                            </button>
                          )}
                          {module === 'smart-tool' && (
                            <button
                              type="button"
                              className="bg-blue-500 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-blue-600"
                              onClick={handleTagButtonClick}
                            >
                              Edit Workgroup
                            </button>
                          )}
                        </>
                      )}
                    </Fragment>
                  ))}
                </div>
              </Popover>
            </div>
          )}
        </td>
      </tr>

      {/* Dialogs */}
      <Dialog
        open={dialogOpen}
        onClose={() => handleDialogClose(false)}
        PaperProps={{
          sx: {
            padding: 0,
            borderRadius: '1rem',
            backgroundColor: '#fff',
            boxShadow: '0 4px 24px rgba(0, 0, 0, 0.1)',
            maxWidth: '400px',
            width: '100%',
          },
        }}
      >
        <div className="overflow-hidden rounded-xl">
          <div className="bg-primary px-6 py-5 text-white">
            <h2 className="text-xl font-medium">{dialogTitle}</h2>
          </div>
          <div className="bg-white p-6 text-center">
            <p className="text-gray-700">Are you sure you want to proceed with this action?</p>
          </div>
          <div className="flex items-center justify-end gap-4 border-t border-gray-200 bg-gray-50 px-6 py-4">
            <button
              type="button"
              onClick={() => handleDialogClose(false)}
              className="rounded-lg border border-gray-300 bg-white px-5 py-2 text-sm font-medium text-gray-700 shadow-sm transition-all duration-200 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={() => handleDialogClose(true)}
              className="bg-primary hover:bg-primary/90 rounded-lg px-5 py-2 text-sm font-medium text-white shadow-sm transition-all duration-200"
            >
              Proceed
            </button>
          </div>
        </div>
      </Dialog>

      {/* Tag Dialog */}
      <Dialog
        open={tagDialogOpen}
        onClose={() => handleTagDialogClose(false)}
        PaperProps={{
          sx: {
            padding: 0,
            borderRadius: '1rem',
            backgroundColor: '#fff',
            boxShadow: '0 4px 24px rgba(0, 0, 0, 0.1)',
            maxWidth: '400px',
            width: '100%',
          },
        }}
      >
        <div className="overflow-hidden rounded-xl">
          <div className="bg-primary px-6 py-5 text-white">
            <h2 className="text-xl font-medium">Edit Workgroup</h2>
          </div>
          <div className="bg-white p-6">
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700">Select Workgroup</label>
              <select
                className="mt-1 block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                value={selectedTag}
                onChange={(e) => setSelectedTag(e.target.value)}
              >
                <option value="">None</option>
                {workgroupOptions.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>
            </div>
          </div>
          <div className="flex items-center justify-end gap-4 border-t border-gray-200 bg-gray-50 px-6 py-4">
            <button
              type="button"
              onClick={() => handleTagDialogClose(false)}
              className="rounded-lg border border-gray-300 bg-white px-5 py-2 text-sm font-medium text-gray-700 shadow-sm transition-all duration-200 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={() => handleTagDialogClose(true)}
              className="bg-primary hover:bg-primary/90 rounded-lg px-5 py-2 text-sm font-medium text-white shadow-sm transition-all duration-200"
            >
              Save
            </button>
          </div>
        </div>
      </Dialog>
    </>
  );
};

UserAccessManagementRow.propTypes = {
  data: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  module: PropTypes.string.isRequired,
  onRefresh: PropTypes.func.isRequired,
  roleArray: PropTypes.array.isRequired,
  workgroupOptions: PropTypes.array,
  activeFilter: PropTypes.string
};

export default UserAccessManagementRow;
