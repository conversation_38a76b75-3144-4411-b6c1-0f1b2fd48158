// Next, React, Tw
import { useState, useEffect } from 'react';
import { twMerge } from 'tailwind-merge';
import { useRouter } from 'next/router';

// Mui
import { Close } from '@mui/icons-material';

// Packages
import * as yup from 'yup';
import moment from 'moment';

// Others
import axios from '../../../utils/axios';
import { useSnackbar } from '../snackbar';
import { AUM_ENDPOINT } from '../../../utils/aum';
import { useAuthContext } from '../../../utils/auth/useAuthContext';
import { localStorageAvailable } from '../../../utils/shared';

const NpsPopup = () => {
  // Standard and Vars
  const { user } = useAuthContext();
  const { enqueueSnackbar } = useSnackbar();
  const storageAvailable = localStorageAvailable();
  const { asPath } = useRouter();

  const [showComponent, setShowComponent] = useState(false);

  // Form
  const MESSAGE = 'Please provide';
  const schema = yup.object().shape({
    rating: yup.number().required(`${MESSAGE} rating`),
    message: yup.string(),
    path: yup.string().required(`${MESSAGE} path`).default(asPath),
    created_by: yup.string().required(`${MESSAGE} year`).default(user?.staff_id),
  });

  const [formData, setFormData] = useState({});
  const handleFormDataChange = (event) => {
    const { name, value } = event.target;
    setFormData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const handleFormDataSubmit = async () => {
    let payload;
    try {
      payload = await schema.validate(formData, {
        abortEarly: false,
      });
    } catch (error) {
      return;
    }

    try {
      const response = await axios.post(`${AUM_ENDPOINT}/nps/v1`, payload);

      let statusVariant;
      let message;
      if (response?.data?.status === 'success') {
        statusVariant = 'success';
        message = 'Submitted, thanks for the feedback!';
      } else {
        statusVariant = 'error';
        message = 'Failed';
      }
      enqueueSnackbar(message, {
        variant: statusVariant,
      });
    } catch (error) {
      enqueueSnackbar(error?.message, {
        variant: 'error',
      });
    }
    setFormData({});
    setShowComponent(false);
  };

  // Others

  useEffect(() => {
    if (!storageAvailable) return;

    if (user?.isSuperAdmin) return;

    if (!localStorage?.getItem('lastNpsPopupUnixTime')) {
      localStorage.setItem('lastNpsPopupUnixTime', moment().unix());
      return;
    }

    const lastNpsPopupUnixTime = Number(localStorage.getItem('lastNpsPopupUnixTime'));
    const oneWeekAgo = moment().subtract(30, 'days').unix();
    if (lastNpsPopupUnixTime > oneWeekAgo) return;

    const timeout = setTimeout(() => {
      setShowComponent(true);
      localStorage.setItem('lastNpsPopupUnixTime', moment().unix());
    }, 5000);

    // eslint-disable-next-line consistent-return
    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, []);

  return (
    <>
      {showComponent && (
        <form
          className="bg-primary relative flex flex-col items-center gap-2 rounded-lg p-2 text-white md:w-[500px]"
          onSubmit={(event) => {
            event.preventDefault();
            handleFormDataSubmit();
          }}
        >
          <p className="md:text-md text-sm">How do you rate SIMI?</p>
          <div className="grid grid-cols-10">
            {Array.from({ length: 10 }, (_, i) => i + 1)?.map((o) => (
              <button
                key={o}
                type="button"
                onClick={() => setFormData({ ...formData, rating: o })}
                className={twMerge(
                  'hover:bg-secondary border border-gray-500 bg-white p-1 text-sm text-black hover:text-white  md:text-lg',
                  `${formData?.rating === o ? 'bg-secondary text-white' : ''}`
                )}
              >
                {o}
              </button>
            ))}
          </div>
          {formData?.rating && (
            <>
              <textarea
                className="w-full border border-gray-500 p-1  text-gray-500 focus:outline-none"
                name="message"
                value={formData?.message}
                rows={5}
                onChange={handleFormDataChange}
                placeholder="Tell us why you rated SIMI as such? (Optional)"
              />
              <button type="submit" className="bg-secondary px-2 py-1 text-white">
                Submit
              </button>
            </>
          )}

          <button
            type="button"
            className="absolute right-0 top-0 m-1"
            onClick={() => setShowComponent(false)}
          >
            <Close className="text-md" />
          </button>
        </form>
      )}
    </>
  );
};

export default NpsPopup;
