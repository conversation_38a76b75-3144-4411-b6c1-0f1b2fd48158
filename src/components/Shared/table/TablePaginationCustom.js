// Next, React, Tw
import { useSelector, useDispatch } from 'react-redux';

// Mui
import { TablePagination } from '@mui/material';

// Packages
import PropTypes from 'prop-types';

// Others
import { setPage } from '../../../utils/store/simiReducer';

// ----------------------------------------------------------------------

TablePaginationCustom.propTypes = {
  onPageChange: PropTypes.func,
};

export default function TablePaginationCustom({ onPageChange, ...other }) {
  // Standard and Vars
  const { page } = useSelector((state) => state.simi);
  const dispatch = useDispatch();

  return (
    <TablePagination
      rowsPerPageOptions={[]}
      page={Number(page)}
      rowsPerPage={20}
      component="div"
      onPageChange={(_, newPage) => {
        if (onPageChange) onPageChange(_, newPage);
        dispatch(setPage(newPage));
      }}
      {...other}
      className="border-0 text-black dark:text-white"
    />
  );
}
