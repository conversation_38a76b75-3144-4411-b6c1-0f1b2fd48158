// Next, React, Tw
import { useRouter } from 'next/router';
import { useEffect } from 'react';

// Packages
import PropTypes from 'prop-types';
import moment from 'moment';

// Components
import { DateInput } from '../CustomInput';

// Others
import { useParamContext } from '../../../utils/auth/ParamProvider';

const FilterTableDataByDateRange = ({
  defaultFrom = moment()?.subtract(1, 'month').unix(),
  defaultTo = moment().unix(),
}) => {
  // Standard and Vars
  const { query } = useRouter();
  const { tableDataFrom, tableDataTo } = query;
  const { setParam, replaceParam } = useParamContext();

  // Others

  useEffect(() => {
    if (tableDataFrom && tableDataTo) return;
    replaceParam({
      tableDataFrom: defaultFrom,
      tableDataTo: defaultTo,
    });
  }, []);

  return (
    <div className="flex flex-col items-center gap-2 md:flex-row">
      <DateInput
        views={['year', 'month', 'day']}
        value={tableDataFrom}
        returnedFormat="unix"
        placeholder="From"
        onChange={(event) => {
          setParam({ tableDataFrom: event?.target?.value });
        }}
      />
      <DateInput
        views={['year', 'month', 'day']}
        value={tableDataTo}
        returnedFormat="unix"
        placeholder="To"
        onChange={(event) => {
          setParam({ tableDataTo: event?.target?.value });
        }}
      />
    </div>
  );
};

FilterTableDataByDateRange.propTypes = {
  defaultFrom: PropTypes.number,
  defaultTo: PropTypes.number,
};

export default FilterTableDataByDateRange;
