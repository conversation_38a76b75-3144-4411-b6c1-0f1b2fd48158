// Next, React, Tw
import { useRouter } from 'next/router';

// Others
import { useParamContext } from '../../../utils/auth/ParamProvider';

const FilterRoleButtonGroup = () => {
  // Standard and Vars
  const { query } = useRouter();
  const { dataRole } = query;
  const { setParam } = useParamContext();

  // Others
  const getButtonClass = (role) =>
    `py-2 px-4 border text-black text-xs ${dataRole === role ? 'bg-payme text-white' : 'bg-white'}`;

  return (
    <div className="flex">
      <button
        type="button"
        className={`rounded-l-[4px] ${getButtonClass('all')}`}
        onClick={() => setParam({ dataRole: 'all' })}
      >
        All
      </button>
      <button
        type="button"
        className={`${getButtonClass('gm-vp')} whitespace-nowrap`}
        onClick={() => setParam({ dataRole: 'gm-vp' })}
      >
        VP & GM
      </button>
      <button
        type="button"
        className={`${getButtonClass('agm')}`}
        onClick={() => setParam({ dataRole: 'agm' })}
      >
        AGM
      </button>
      <button
        type="button"
        className={`${getButtonClass('m')}`}
        onClick={() => setParam({ dataRole: 'm' })}
      >
        MGR
      </button>
      <button
        type="button"
        className={`${getButtonClass('am')}`}
        onClick={() => setParam({ dataRole: 'am' })}
      >
        AM
      </button>
      <button
        type="button"
        className={`rounded-r-[4px] ${getButtonClass('ne')}`}
        onClick={() => setParam({ dataRole: 'ne' })}
      >
        NE
      </button>
    </div>
  );
};

export default FilterRoleButtonGroup;
