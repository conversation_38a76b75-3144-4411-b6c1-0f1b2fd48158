// Next, React, Tw
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import Image from 'next/image';

// Mui
import Carousel from 'react-material-ui-carousel';
import { useMediaQuery } from '@mui/material';

// Packages
import { TypeAnimation } from 'react-type-animation';
import moment from 'moment';

// Others
import { EVENT_ENDPOINT } from '../../../utils/event';
import { CORS_PROXY_ENDPOINT } from '../../../utils/shared';
import { getRandomQuote } from '../../../utils/quotes';
import { useSnackbar } from '../snackbar';

const LeftHandSide = () => {
  // Standard and Vars
  const { query, replace } = useRouter();
  const { layout } = query;
  const { isSmallScreen } = useMediaQuery('(max-width:768px)');
  const { enqueueSnackbar } = useSnackbar();

  const [quoteData, setQuoteData] = useState({});
  const [eventData, setEventData] = useState([]);

  // Others

  const fetchQuote = async () => {
    try {
      // Alternative APIs to try (in order of preference)
      const apiEndpoints = [
        // Try different quote APIs that might work better in your network
        {
          name: 'QuotGarden',
          url: 'https://quotegarden.herokuapp.com/api/v3/quotes/random',
          parser: (data) => ({
            quote: `"${data?.data?.quoteText}"`,
            author: `-${data?.data?.quoteAuthor}-`
          })
        },
        {
          name: 'Quotable via CORS Proxy',
          url: `https://api.allorigins.win/get?url=${encodeURIComponent('https://api.quotable.io/random')}`,
          parser: (data) => {
            const parsed = JSON.parse(data.contents);
            return {
              quote: `"${parsed?.content}"`,
              author: `-${parsed?.author}-`
            };
          }
        },
        {
          name: 'ZenQuotes via CORS Proxy',
          url: `https://api.allorigins.win/get?url=${encodeURIComponent('https://zenquotes.io/api/random')}`,
          parser: (data) => {
            const parsed = JSON.parse(data.contents);
            return {
              quote: `"${parsed?.[0]?.q}"`,
              author: `-${parsed?.[0]?.a}-`
            };
          }
        },
        {
          name: 'Alternative CORS Proxy (corsproxy.io)',
          url: `https://corsproxy.io/?https://api.quotable.io/random`,
          parser: (data) => ({
            quote: `"${data?.content}"`,
            author: `-${data?.author}-`
          })
        }
      ];

      // Try each endpoint until one works
      for (const endpoint of apiEndpoints) {
        try {
          console.log(`Trying ${endpoint.name}...`);
          
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout
          
          const response = await fetch(endpoint.url, {
            signal: controller.signal,
            headers: {
              'Accept': 'application/json',
              'User-Agent': 'Mozilla/5.0 (compatible; SimiDigital/1.0)'
            },
            mode: 'cors'
          });
          
          clearTimeout(timeoutId);
          
          if (response.ok) {
            const data = await response.json();
            const parsed = endpoint.parser(data);
            
            if (parsed.quote && parsed.author && parsed.quote !== '""' && parsed.author !== '--') {
              console.log(`✅ ${endpoint.name} API succeeded`);
              setQuoteData(parsed);
              return;
            }
          }
          
        } catch (apiError) {
          console.log(`❌ ${endpoint.name} failed:`, apiError.message);
          continue; // Try next endpoint
        }
      }

      // If all APIs fail, fall back to local quotes
      console.log('🔄 All external APIs failed, using local quotes as fallback');
      setQuoteData(getRandomQuote());
      
    } catch (error) {
      console.log('🚨 Unexpected error in fetchQuote:', error.message);
      setQuoteData(getRandomQuote());
    }
  };

  const fetchEventData = async () => {
    try {
      const response = await fetch(`${EVENT_ENDPOINT}/latest/keyword`);
      const data = await response.json();
      setEventData(data?.data || []);
    } catch (error) {
      setEventData([]);
    }
  };

  useEffect(() => {
    let timer;
    if (!layout && !isSmallScreen) {
      fetchQuote();
      timer = setInterval(async () => {
        fetchQuote();
      }, 10000);
    }
    return () => timer && clearInterval(timer);
  }, []);

  useEffect(() => {
    if (!isSmallScreen) fetchEventData();
  }, []);

  if (!layout) {
    return (
      <>
        {eventData?.length === 0 && (
          <>
            <p className="text-center text-sm font-semibold text-[#2c2d86]">
              <TypeAnimation
                key={quoteData?.quote}
                sequence={[quoteData?.quote]}
                wrapper="i"
                speed={100}
                cursor={false}
              />
            </p>
            <p className="text-center text-xs text-[#2c2d86]">{quoteData?.author}</p>
          </>
        )}
        {eventData?.length > 0 && (
          <Carousel indicators={false} className="-mt-14  w-[500px] ">
            {eventData?.map((o, i) => (
              <button
                key={i}
                type="button"
                className=" flex h-full w-full flex-col items-center  gap-1 p-8 hover:scale-125"
                onClick={() => {
                  enqueueSnackbar('Log in to view the event details');
                  replace(`/event/view-event/${o?.event_id}`);
                }}
              >
                {o?.image !== '' && <img src={o?.image} alt="Event" height={500} width={500} />}
                <p className="text-md font-semibold">{o.name}</p>
                <p className="text-sm">
                  {moment.unix(o?.start_date).format('DD MMM YYYY')}
                  {o?.start_date !== o?.end_date && (
                    <> - {moment.unix(o?.end_date).format('DD MMM YYYY')}</>
                  )}
                  &emsp;<strong>|</strong>&emsp;
                  {o.location}
                </p>
              </button>
            ))}
          </Carousel>
        )}
      </>
    );
  }

  const componentMapping = {
    'lrp-2024': (
      <Image
        src="/events/lrp-2024/logo.png"
        width={936}
        height={905}
        className="h-[500px] w-[500px]"
      />
    ),
  };

  return componentMapping[layout];
};

export default LeftHandSide;
