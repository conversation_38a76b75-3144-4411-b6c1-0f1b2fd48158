// Next, React, Tw
import Image from 'next/image';
import { useRouter } from 'next/router';
import { useState, useEffect, useRef } from 'react';

// Packages
import { m } from 'framer-motion';
import { ThreeDots } from 'react-loader-spinner';
import moment from 'moment';

// Others
import { AUM_ENDPOINT } from '../../../utils/aum';
import { useAuthContext } from '../../../utils/auth/useAuthContext';
import { useSnackbar } from '../snackbar';
import { useParamContext } from '../../../utils/auth/ParamProvider';

const RightHandSide = () => {
  // Standard and Vars
  const { query } = useRouter();
  const { enqueueSnackbar } = useSnackbar();
  const { login } = useAuthContext();
  const { setParam } = useParamContext();

  const pinRef1 = useRef(null);
  const pinRef2 = useRef(null);
  const pinRef3 = useRef(null);
  const pinRef4 = useRef(null);
  const pinRef5 = useRef(null);
  const pinRef6 = useRef(null);
  const [submitStaffIdDisabled, setSubmitStaffIdDisabled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const { layout, mode } = query;
  const [loginForm, setLoginForm] = useState({
    staff_id: null,
  });
  const REGISTER_FORM = {
    staff_id: null,
    alt_email: '',
  };
  const [registerform, setRegisterForm] = useState(REGISTER_FORM);
  const [newPinCodeTime, setNewPinCodeTime] = useState(null);
  const [newPinCodeTimer, setNewPinCodeTimer] = useState(null);
  const [newPincodeButtonVisible, setNewPincodeButtonVisible] = useState(false);

  // Form
  const handleLoginFormChange = (event) => {
    const { name, value } = event.target;
    setLoginForm((prevValues) => ({
      ...prevValues,
      [name]: value.trim(),
    }));
  };
  const handleRegisterFormChange = (event) => {
    const { name, value } = event.target;
    setRegisterForm((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const handleSubmit = async (data) => {
    if (Object.values(loginForm).includes(null) || Object.values(loginForm).includes('')) {
      enqueueSnackbar('Please input Staff ID', {
        variant: 'error',
        anchorOrigin: {
          vertical: 'top',
          horizontal: 'right',
        },
      });
      return;
    }

    setSubmitStaffIdDisabled(true);
    setIsLoading(true);
    try {
      const response = await fetch(`${AUM_ENDPOINT}/authentication/v1/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(loginForm),
      });

      const payload = await response.json();

      if (payload.status === 'success') {
        setIsVerifying(true);
        localStorage.setItem('verifyToken', payload.token);
        enqueueSnackbar(
          <span className="text-[11px]">Please check your TM e-mail or SMS.</span>,
          {
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'right',
            },
          }
        );
        setNewPinCodeTime(moment().add(2, 'minutes'));
      } else {
        enqueueSnackbar(<span className="text-[11px]">{payload.message}</span>, {
          variant: 'error',
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'right',
          },
        });
      }
    } catch {
      enqueueSnackbar(
        <span className="text-[11px]">Something went wrong. Please try again later.</span>,
        {
          variant: 'error',
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'right',
          },
        }
      );
    }
    setIsLoading(false);
    setSubmitStaffIdDisabled(false);
  };

  const handleBackspaceButtonPushed = (event) => {
    if (event?.key !== 'Backspace') {
      return;
    }
    pinRef1.current.value = '';
    pinRef2.current.value = '';
    pinRef3.current.value = '';
    pinRef4.current.value = '';
    pinRef5.current.value = '';
    pinRef6.current.value = '';

    pinRef1.current.focus();
  };

  const handlePinInputComplete = async () => {
    const value = `${pinRef1.current.value}${pinRef2.current.value}${pinRef3.current.value}${pinRef4.current.value}${pinRef5.current.value}${pinRef6.current.value}`;
    let level;
    let message;
    if (await login(value)) {
      level = 'success';
      message = <span className="text-[11px]">Successfully logged in.</span>;
    } else {
      level = 'error';
      message = <span className="text-[11px]">Failed to log in.</span>;
      pinRef1.current.value = '';
      pinRef2.current.value = '';
      pinRef3.current.value = '';
      pinRef4.current.value = '';
      pinRef5.current.value = '';
      pinRef6.current.value = '';
      pinRef1?.current?.focus();
    }

    enqueueSnackbar(message, {
      variant: level,
      anchorOrigin: {
        vertical: 'top',
        horizontal: 'right',
      },
    });
  };

  const handleSubmitRegister = async () => {
    if (Object.values(registerform).includes(null)) {
      enqueueSnackbar(<span className="text-[11px]">Please complete the form.</span>, {
        variant: 'error',
        anchorOrigin: {
          vertical: 'top',
          horizontal: 'right',
        },
      });
      return;
    }
    registerform.staff_id = registerform.staff_id.toLowerCase();
    if (registerform?.alt_email === '') {
      delete registerform?.alt_email;
    } else {
      registerform.alt_email = registerform?.alt_email?.toLowerCase()?.trim();
    }

    try {
      const response = await fetch(`${AUM_ENDPOINT}/user/v1`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(registerform),
      });

      const payload = await response.json();
      if (!response.ok || payload.status !== 'success') {
        enqueueSnackbar(payload.data, {
          variant: 'error',
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'right',
          },
        });
        return;
      }

      enqueueSnackbar(payload.data, {
        variant: 'success',
        anchorOrigin: {
          vertical: 'top',
          horizontal: 'right',
        },
      });
      await new Promise((r) => setTimeout(r, 2000));
      setParam({ mode: 'login' });
    } catch {
      /* empty */
    }
  };

  const isNotTmTech = (() => {
    if ([null, '']?.includes(registerform?.staff_id)) return false;
    let numberPosition;
    const staffId = registerform?.staff_id?.replaceAll('0', '');
    for (let i = 0; i < staffId.length; i += 1) {
      if (Number(staffId[i])) {
        numberPosition = i;
        break;
      }
    }
    if (
      numberPosition &&
      ['tmhk', 'tmsg', 'tmuk', 'tmus'].includes(staffId.slice(0, numberPosition)?.toLowerCase())
    )
      return true;
    return false;
  })();

  useEffect(() => {
    pinRef1?.current?.focus();
  }, [isVerifying]);

  useEffect(() => {
    if (newPinCodeTime === null) {
      return;
    }
    const timer = setInterval(async () => {
      setNewPinCodeTimer(newPinCodeTime.diff(moment(), 'seconds'));
      if (newPinCodeTime.diff(moment(), 'seconds') <= 0) {
        clearInterval(timer);
        setNewPincodeButtonVisible(true);
      }
    }, 1000);
    // eslint-disable-next-line
    return () => {
      clearInterval(timer);
    };
  }, [newPinCodeTime]);

  useEffect(() => {
    setRegisterForm(REGISTER_FORM);
  }, [mode]);

  if (!layout) {
    return (
      <div className="w-[220px]">
        <img
          src="/assets/logo/New-SIMI-Logo.svg"
          alt="Bottom Right"
          className="mx-auto h-[130px]"
        />
        <p className="-mt-6 mb-4 w-full text-center font-sans text-3xl font-extrabold text-[#2c2d86]">
          Welcome To
          <br />
          Simi Digital
        </p>
        {mode !== 'register' && (
          <>
            {!isVerifying && (
              <form
                onSubmit={(event) => {
                  event.preventDefault();
                  handleSubmit();
                }}
              >
                <input
                  type="text"
                  name="staff_id"
                  placeholder="Staff ID"
                  onChange={handleLoginFormChange}
                  className="-mt-1 mb-2 w-full border border-slate-300 bg-[#FBFBFB] p-2 text-center text-xs focus:border-[#1800e7] focus:outline-none focus:ring-1 focus:ring-[#1800e7]"
                />
                <button
                  type="submit"
                  disabled={submitStaffIdDisabled}
                  className={` bg-40 h-8 w-full bg-[#fc6604] ${
                    !isLoading ? 'bg-[url(/assets/icons/tm-right-arrow.svg)]' : 'bg-none'
                  }  bg-right bg-no-repeat p-1 text-xs font-extrabold text-white hover:bg-[#1800e7]`}
                >
                  {!isLoading ? (
                    'LOGIN'
                  ) : (
                    <div className="flex w-full justify-center">
                      <ThreeDots height="30" width="30" radius="10" color="#FBFBFB" visible />
                    </div>
                  )}
                </button>
              </form>
            )}

            {isVerifying && (
              <div className="mb-4 flex gap-1">
                <input
                  ref={pinRef1}
                  type="number"
                  min="0"
                  max="9"
                  onKeyDown={handleBackspaceButtonPushed}
                  onChange={(event) => {
                    event.target.value = event.target.value.slice(0, 1);
                    if (event?.target?.value !== '') {
                      pinRef2?.current?.focus();
                    }
                  }}
                  className="mt-[2px] h-[35px] w-[35px] rounded-[8px] border border-blue-800 bg-transparent text-center text-black focus:border focus:border-red-500 focus:outline-none"
                />
                <input
                  ref={pinRef2}
                  type="number"
                  min="0"
                  max="9"
                  onKeyDown={handleBackspaceButtonPushed}
                  onChange={(event) => {
                    event.target.value = event.target.value.slice(0, 1);
                    if (event?.target?.value !== '') {
                      pinRef3?.current?.focus();
                    }
                  }}
                  className="mt-[2px] h-[35px] w-[35px] rounded-[8px] border border-blue-800 bg-transparent text-center text-black focus:border focus:border-red-500 focus:outline-none"
                />

                <input
                  ref={pinRef3}
                  type="number"
                  min="0"
                  max="9"
                  onKeyDown={handleBackspaceButtonPushed}
                  onChange={(event) => {
                    event.target.value = event.target.value.slice(0, 1);
                    if (event?.target?.value !== '') {
                      pinRef4?.current?.focus();
                    }
                  }}
                  className="mt-[2px] h-[35px] w-[35px] rounded-[8px] border border-blue-800 bg-transparent text-center text-black focus:border focus:border-red-500 focus:outline-none"
                />
                <input
                  ref={pinRef4}
                  type="number"
                  min="0"
                  max="9"
                  onKeyDown={handleBackspaceButtonPushed}
                  onChange={(event) => {
                    event.target.value = event.target.value.slice(0, 1);
                    if (event?.target?.value !== '') {
                      pinRef5?.current?.focus();
                    }
                  }}
                  className="mt-[2px] h-[35px] w-[35px] rounded-[8px] border border-blue-800 bg-transparent text-center text-black focus:border focus:border-red-500 focus:outline-none"
                />
                <input
                  ref={pinRef5}
                  type="number"
                  min="0"
                  max="9"
                  onKeyDown={handleBackspaceButtonPushed}
                  onChange={(event) => {
                    event.target.value = event.target.value.slice(0, 1);
                    if (event?.target?.value !== '') {
                      pinRef6?.current?.focus();
                    }
                  }}
                  className="mt-[2px] h-[35px] w-[35px] rounded-[8px] border border-blue-800 bg-transparent text-center text-black focus:border focus:border-red-500 focus:outline-none"
                />
                <input
                  ref={pinRef6}
                  type="number"
                  min="0"
                  max="9"
                  onKeyDown={handleBackspaceButtonPushed}
                  onChange={(event) => {
                    event.target.value = event.target.value.slice(0, 1);
                    handlePinInputComplete();
                  }}
                  className="mt-[2px] h-[35px] w-[35px] rounded-[8px] border border-blue-800 bg-transparent text-center text-black focus:border focus:border-red-500 focus:outline-none"
                />
              </div>
            )}
            {isVerifying && !newPincodeButtonVisible && (
              <p className="-mt-4 font-sans text-[12px]">
                Wait for {newPinCodeTimer} seconds for new pincode.
              </p>
            )}

            {isVerifying && newPincodeButtonVisible && (
              <button
                type="button"
                className="w-full"
                onClick={() => {
                  setNewPincodeButtonVisible(false);
                  handleSubmit();
                }}
              >
                <p className="-mt-4 font-sans text-[14px] hover:underline">New Pin Please!</p>
              </button>
            )}

            <div className="mt-3 flex justify-center gap-1 font-sans text-[11px]">
              <p>New user?</p>
              <button
                type="button"
                onClick={() => setParam({ mode: 'register' })}
                className="font-bold text-[#1800e7] hover:underline"
              >
                Ask for Access
              </button>
            </div>
          </>
        )}
        {mode === 'register' && (
          <>
            <form
              onSubmit={(event) => {
                event.preventDefault();
                handleSubmitRegister();
              }}
            >
              <input
                type="text"
                name="staff_id"
                placeholder="Staff ID"
                onChange={handleRegisterFormChange}
                className="-mt-1 mb-2 w-full border border-slate-300 bg-[#FBFBFB] p-2 text-center text-xs focus:border-[#1800e7] focus:outline-none focus:ring-1 focus:ring-[#1800e7]"
              />
              {isNotTmTech && (
                <m.input
                  initial={{ x: +1000 }}
                  animate={{ x: 0 }}
                  transition={{
                    duration: 0.5,
                    type: 'spring',
                    stiffness: 200,
                    damping: 20,
                  }}
                  type="text"
                  name="alt_email"
                  placeholder="Alternate E-Mail for Pincode"
                  onChange={handleRegisterFormChange}
                  className="-mt-1 mb-2 w-full border border-slate-300 bg-[#FBFBFB] p-2 text-center text-xs focus:border-[#1800e7] focus:outline-none focus:ring-1 focus:ring-[#1800e7]"
                />
              )}

              <button
                type="submit"
                className="bg-40 h-8 w-full bg-[#fc6604] bg-[url(/assets/icons/tm-right-arrow.svg)] bg-right bg-no-repeat p-1 text-xs font-extrabold text-white hover:bg-[#1800e7]"
              >
                REGISTER
              </button>
            </form>

            <p className="text-[11px]">
              Already registered? Click{' '}
              <button
                type="button"
                onClick={() => setParam({ mode: 'login' })}
                className="font-bold text-[#1800e7] hover:underline"
              >
                here
              </button>{' '}
              to login!
            </p>
          </>
        )}
      </div>
    );
  }
  const componentMapping = {
    'lrp-2024': (
      <div className="z-10 mx-auto  flex w-4/5 flex-col gap-4 md:w-[20%]">
        <div className="flex w-full justify-center md:hidden">
          <Image
            src="/events/lrp-2024/logo.png"
            width={936}
            height={905}
            className="h-[250px] w-[250px] "
          />
        </div>

        <div className="relative h-[350px] text-white">
          <div className="absolute left-0 top-0 h-full w-full rounded-3xl bg-white opacity-20" />
          <div className="absolute left-0 top-0 flex h-full w-full flex-col items-center justify-center  gap-4 px-4 py-8">
            <p className="w-full text-center font-sans text-2xl font-extrabold ">
              Welcome To
              <br />
              TM Global LRP 2024
              <br />
              Kick-Off
            </p>
            {mode !== 'register' && (
              <>
                {!isVerifying && (
                  <form
                    onSubmit={(event) => {
                      event.preventDefault();
                      handleSubmit();
                    }}
                  >
                    <input
                      type="text"
                      name="staff_id"
                      placeholder="Staff ID"
                      onChange={handleLoginFormChange}
                      autoComplete="off"
                      className=" -mt-1 mb-2 w-full border border-slate-300 bg-[#dce8ff] p-2 text-center text-xs text-black focus:border-[#1800e7] focus:outline-none focus:ring-1 focus:ring-[#1800e7]"
                    />
                    <button
                      type="submit"
                      disabled={submitStaffIdDisabled}
                      className={` bg-40 h-8 w-full bg-[#0d43aa] p-1 text-xs font-extrabold text-white hover:bg-[#1800e7]`}
                    >
                      {!isLoading ? (
                        'LOGIN'
                      ) : (
                        <div className="flex w-full justify-center">
                          <ThreeDots height="30" width="30" radius="10" color="#FBFBFB" visible />
                        </div>
                      )}
                    </button>
                  </form>
                )}

                {isVerifying && (
                  <div className="mb-4 flex gap-1">
                    <input
                      ref={pinRef1}
                      type="number"
                      maxLength={1}
                      onChange={() => {
                        pinRef2?.current?.focus();
                      }}
                      className="mt-[2px] h-[35px] w-[35px] rounded-[8px] border border-white bg-transparent text-center font-semibold text-white focus:border focus:border-red-500 focus:outline-none"
                    />
                    <input
                      ref={pinRef2}
                      type="number"
                      maxLength={1}
                      onChange={() => {
                        pinRef3?.current?.focus();
                      }}
                      className="mt-[2px] h-[35px] w-[35px] rounded-[8px] border border-white bg-transparent text-center font-semibold text-white focus:border focus:border-red-500 focus:outline-none"
                    />

                    <input
                      ref={pinRef3}
                      type="number"
                      maxLength={1}
                      onChange={() => {
                        pinRef4?.current?.focus();
                      }}
                      className="mt-[2px] h-[35px] w-[35px] rounded-[8px] border border-white bg-transparent text-center font-semibold text-white focus:border focus:border-red-500 focus:outline-none"
                    />
                    <input
                      ref={pinRef4}
                      type="number"
                      maxLength={1}
                      onChange={() => {
                        pinRef5?.current?.focus();
                      }}
                      className="mt-[2px] h-[35px] w-[35px] rounded-[8px] border border-white bg-transparent text-center font-semibold text-white focus:border focus:border-red-500 focus:outline-none"
                    />
                    <input
                      ref={pinRef5}
                      type="number"
                      maxLength={1}
                      onChange={() => {
                        pinRef6?.current?.focus();
                      }}
                      className="mt-[2px] h-[35px] w-[35px] rounded-[8px] border border-white bg-transparent text-center font-semibold text-white focus:border focus:border-red-500 focus:outline-none"
                    />
                    <input
                      ref={pinRef6}
                      type="number"
                      maxLength={1}
                      onChange={() => {
                        handlePinInputComplete();
                      }}
                      className="mt-[2px] h-[35px] w-[35px] rounded-[8px] border border-white bg-transparent text-center font-semibold text-white focus:border focus:border-red-500 focus:outline-none"
                    />
                  </div>
                )}
                {isVerifying && !newPincodeButtonVisible && (
                  <p className="-mt-4 font-sans text-[14px]">
                    Wait for {newPinCodeTimer} seconds for new pincode.
                  </p>
                )}

                {isVerifying && newPincodeButtonVisible && (
                  <button
                    type="button"
                    onClick={() => {
                      setNewPincodeButtonVisible(false);
                      handleSubmit();
                    }}
                  >
                    <p className="-mt-4 font-sans text-[14px] hover:underline">New Pin Please!</p>
                  </button>
                )}

                <div className="-mt-1 flex gap-1 font-sans text-[11px]">
                  <p>New user?</p>
                  <button
                    type="button"
                    onClick={() => setParam({ mode: 'register' })}
                    className="font-bold hover:underline"
                  >
                    Ask for Access
                  </button>
                </div>
              </>
            )}
            {mode === 'register' && (
              <>
                <form
                  onSubmit={(event) => {
                    event.preventDefault();
                    handleSubmitRegister();
                  }}
                >
                  <input
                    type="text"
                    name="staff_id"
                    placeholder="Staff ID"
                    onChange={handleRegisterFormChange}
                    className="-mt-1 mb-2 w-full border border-slate-300 bg-[#FBFBFB] p-2 text-center text-xs focus:border-[#1800e7] focus:outline-none focus:ring-1 focus:ring-[#1800e7]"
                  />
                  {isNotTmTech && (
                    <m.input
                      initial={{ x: +1000 }}
                      animate={{ x: 0 }}
                      transition={{
                        duration: 0.5,
                        type: 'spring',
                        stiffness: 200,
                        damping: 20,
                      }}
                      type="text"
                      name="alt_email"
                      placeholder="Alternate E-Mail for Pincode"
                      onChange={handleRegisterFormChange}
                      className="-mt-1 mb-2 w-full border border-slate-300 bg-[#FBFBFB] p-2 text-center text-xs focus:border-[#1800e7] focus:outline-none focus:ring-1 focus:ring-[#1800e7]"
                    />
                  )}

                  <button
                    type="submit"
                    className="bg-40 h-8 w-full bg-[#fc6604] bg-[url(/assets/icons/tm-right-arrow.svg)] bg-right bg-no-repeat p-1 text-xs font-extrabold text-white hover:bg-[#1800e7]"
                  >
                    REGISTER
                  </button>
                </form>

                <p className="text-[11px]">
                  Already registered? Click{' '}
                  <button
                    type="button"
                    onClick={() => setParam({ mode: 'login' })}
                    className="font-bold text-[#1800e7] hover:underline"
                  >
                    here
                  </button>{' '}
                  to login!
                </p>
              </>
            )}
          </div>
        </div>
      </div>
    ),
  };
  return componentMapping[layout];
};

export default RightHandSide;
