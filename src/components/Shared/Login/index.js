// Next, React, Tw
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { twMerge } from 'tailwind-merge';

// Mui
import { Tooltip, Dialog, DialogTitle, DialogContent, IconButton } from '@mui/material';
import { Info, Close } from '@mui/icons-material';

// Components
import LeftHandSide from './LeftHandSide';
import RightHandSide from './RightHandSide';

// ----------------------------------------------------------------------

const Login = () => {
  // Standard and Vars
  const { query } = useRouter();
  const { layout, mode } = query;
  const [openVideoDialog, setOpenVideoDialog] = useState(false);

  // Others

  const handleCloseDialog = () => {
    setOpenVideoDialog(false);
  };

  const bg = (() => {
    if (!layout) {
      return 'bg-[url(/login/bg.webp)] bg-cover bg-center bg-no-repeat';
    }
    const bgMapping = {
      'lrp-2024': 'bg-[url(/events/lrp-2024/bg.png)] bg-cover bg-center bg-no-repeat',
    };
    return bgMapping[layout];
  })();

  const blurIsOn = mode === 'register' ? 'blur-md' : '';

  return (
    <div className="flex h-full w-full items-center">
      <Dialog
        open={openVideoDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth={false}
        PaperProps={{
          style: {
            backgroundColor: 'white',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
            borderRadius: '8px',
            overflow: 'hidden',
            position: 'relative',
            width: '1000px',
            maxWidth: '1000px',
          },
        }}
      >
        <DialogTitle
          style={{
            textAlign: 'center',
            fontWeight: 'bold',
            padding: '16px',
            backgroundColor: '#f5f5f5',
          }}
        >
          Aidilfitri Greetings Message 2025 - Product & Marketing, TM Global
          <IconButton
            aria-label="close"
            onClick={handleCloseDialog}
            style={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: 'grey',
            }}
          >
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent style={{ padding: '0', display: 'flex', justifyContent: 'center' }}>
          <div
            style={{ width: '100%', height: '0', paddingBottom: '56.25%', position: 'relative' }}
          >
            <iframe
              title="Aidilfitri Greetings Message 2025 - Product & Marketing, TM Global"
              src="https://player.vimeo.com/video/1069831006?h=defb69fb66&autoplay=1&title=0&byline=0&portrait=0"
              style={{
                position: 'absolute',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                border: 'none',
              }}
              frameBorder="0"
              allowFullScreen
              allow="autoplay; fullscreen; picture-in-picture"
            />
          </div>
        </DialogContent>
      </Dialog>
      <div className="flex h-full w-full">
        <div className="hidden h-full w-1/2 flex-col items-center justify-center bg-white md:flex">
          <div
            className={twMerge(
              'z-10 mx-auto -mt-12 hidden w-2/5 flex-col items-center justify-center gap-2 md:flex',
              blurIsOn
            )}
          >
            {!layout && (
              <>
                <img
                  src="/login/top-left.webp"
                  alt="Top Left"
                  className={twMerge('absolute left-0 top-0 mx-auto w-1/2 md:w-[15%]', blurIsOn)}
                />
                <img
                  src="/login/bottom-left.webp"
                  alt="Bottom Left"
                  className={twMerge('absolute bottom-0 left-0 m-4 w-1/4 md:block', blurIsOn)}
                />
                <img
                  src="/login/tmglobal with tagline.webp"
                  alt="Bottom Right"
                  className={twMerge('absolute bottom-0 right-0 m-4 h-[40px]', blurIsOn)}
                />
                <Link
                  href="https://tm365-my.sharepoint.com/:p:/g/personal/kamil_fauzi_tm_com_my/ETunzSG0nolDhHqL0o8AtIkBFiTHLLDU38YPocixDpqvZQ?e=AHZ3IQ"
                  target="_blank"
                >
                  <Tooltip title="Portal guide.">
                    <Info
                      className={twMerge('absolute right-0 top-0 mx-4 my-2 h-[40px]', blurIsOn)}
                    />
                  </Tooltip>
                </Link>
              </>
            )}
            <div className="flex h-full w-full flex-col items-center justify-center">
              {' '}
              <LeftHandSide />
            </div>
          </div>
        </div>

        <div className="relative flex h-full w-full items-center justify-center md:w-1/2">
          <div className={twMerge('absolute left-0 top-0 z-0 h-full w-full', bg, blurIsOn)} />
          {!layout && (
            <>
              <img
                src="/login/tmglobal with tagline.webp"
                alt="Bottom Right"
                className={twMerge('absolute bottom-0 right-0 m-4 h-[40px]', blurIsOn)}
              />
              <Link
                href="https://tm365-my.sharepoint.com/:p:/g/personal/kamil_fauzi_tm_com_my/ETunzSG0nolDhHqL0o8AtIkBFiTHLLDU38YPocixDpqvZQ?e=AHZ3IQ"
                target="_blank"
              >
                <Tooltip title="Portal guide.">
                  <Info
                    className={twMerge('absolute right-0 top-0 mx-4 my-2 h-[40px]', blurIsOn)}
                  />
                </Tooltip>
              </Link>
            </>
          )}
          <div className="relative z-10 mx-auto flex w-3/5 flex-col items-center justify-center gap-4 md:w-[20%]">
            <RightHandSide />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
