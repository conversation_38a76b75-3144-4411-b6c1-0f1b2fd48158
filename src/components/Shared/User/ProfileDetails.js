// Mui
import { FontDownload, Shield, Email, Work, LocalPhone, Escalator } from '@mui/icons-material';

// Packages
import PropTypes from 'prop-types';

// Others
import { getEmail, getPhone, getDesignation } from '../../../utils/dashboard';

const ProfileDetails = ({ userObject }) => (
  <>
    <div className="flex items-center gap-4">
      <FontDownload className="text-primary" />
      <p className="text-sm">{`${userObject?.name} ${
        userObject?.nick_name !== '' ? `(${userObject?.nick_name})` : ''
      }`}</p>
    </div>
    <div className="flex items-center gap-4">
      <Shield className="text-primary" />
      <p className="text-sm">{`${userObject?.staff_id?.toUpperCase()}`}</p>
    </div>
    <div className="flex items-center gap-4">
      <Work className="text-primary" />
      <p className="text-sm">{`${userObject?.division}`}</p>
    </div>
    <div className="flex items-center gap-4">
      <Email className="text-primary" />
      <p className="text-sm">{`${getEmail(userObject)}`}</p>
    </div>
    <div className="flex items-center gap-4">
      <Escalator className="text-primary" />
      <p className="text-sm">{`${getDesignation(userObject)}`}</p>
    </div>
    <div className="flex items-center gap-4">
      <LocalPhone className="text-primary" />
      <p className="text-sm">{`${getPhone(userObject)}`}</p>
    </div>
  </>
);

ProfileDetails.propTypes = {
  userObject: PropTypes.object,
};

export default ProfileDetails;
