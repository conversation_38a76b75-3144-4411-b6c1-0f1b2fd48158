// Next, React, Tailwind
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useState, useRef } from 'react';
import { twMerge } from 'tailwind-merge';
import { useDispatch } from 'react-redux';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions, Popover, Tooltip } from '@mui/material';
import { Person, Info } from '@mui/icons-material';

// Packages
import PropTypes from 'prop-types';
import <PERSON>ropper from 'react-easy-crop';

// Others
import axios from '../../../utils/axios';
import { useSnackbar } from '../snackbar';
import { useAuthContext } from '../../../utils/auth/useAuthContext';
import { AUM_ENDPOINT } from '../../../utils/aum';
import { setIsLoading } from '../../../utils/store/loadingReducer';

const ProfilePicture = ({ userObject, userId, fetchData }) => {
  // Standard and Vars
  const { query, asPath } = useRouter();
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuthContext();
  const dispatch = useDispatch();

  const { staffId } = query;
  const fileInputRef = useRef(null);
  const [croppedImage, setCroppedImage] = useState(null);
  const [popOverPosition, setPopOverPosition] = useState(null);

  // Dialog
  const [dialog2Open, setDialog2Open] = useState(false);
  const [dialogData, setDialogData] = useState({});

  const handleDialogClose = async (action) => {
    if (action) {
      dispatch(setIsLoading(true));
      try {
        let payload = dialogData;
        if (croppedImage) {
          payload = {
            ...userObject,
            profile_picture: croppedImage,
          };
        }
        const response = await axios.put(`${AUM_ENDPOINT}/profile/v1/${userId}`, payload);
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch (error) {
        enqueueSnackbar('Something went wrong', {
          variant: 'error',
        });
      }
      dispatch(setIsLoading(false));
    }
    setDialog2Open(false);
    setDialogData({});
    fetchData();
  };

  // Others
  const buttonStyle = 'text-sm hover:brightness-150 text-white font-semibold p-2';

  const profilePhoto = (() => {
    if (userObject?.profile_picture !== '') {
      return (
        <img
          src={userObject?.profile_picture}
          alt="Profile"
          width={200}
          height={200}
          className="z-0 h-[200px] w-[200px] rounded-full"
        />
      );
    }
    return <Person className="z-0 h-[200px] w-[200px]" />;
  })();

  const [crop, setCrop] = useState({ x: 0, y: 0 });

  return (
    <>
      <div className="group relative flex h-[200px] w-[200px] items-center justify-center rounded-full border">
        {profilePhoto}
        {(staffId === user?.staff_id || user?.isSuperAdmin) &&
          asPath?.includes('/dashboard/view-user') && (
            <>
              <button
                type="button"
                className="absolute left-0 top-0 z-10 hidden h-full w-full rounded-full bg-black bg-opacity-50 text-center text-white group-hover:block "
                onClick={(event) => {
                  setPopOverPosition(popOverPosition === null ? event.currentTarget : null);
                }}
              >
                CHANGE <br /> PROFILE <br /> PHOTO
              </button>
              <Popover
                open={popOverPosition !== null}
                anchorEl={popOverPosition}
                onClose={() => setPopOverPosition(null)}
                anchorOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
                transformOrigin={{
                  vertical: 'bottom',
                  horizontal: 'right',
                }}
              >
                <div className="flex flex-col rounded-lg">
                  <button
                    type="button"
                    className={twMerge(buttonStyle, 'bg-green-500')}
                    onClick={() => {
                      fileInputRef.current.click();
                      setPopOverPosition(null);
                    }}
                  >
                    Upload photo
                  </button>
                  {userObject?.profile_picture !== '' && (
                    <button
                      type="button"
                      className={twMerge(buttonStyle, 'bg-red-500')}
                      onClick={() => {
                        setDialogData({
                          ...userObject,
                          profile_picture: '',
                        });
                        setPopOverPosition(null);
                        setDialog2Open(true);
                      }}
                    >
                      Remove photo
                    </button>
                  )}
                </div>
              </Popover>
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                onChange={async (event) => {
                  const file = event.target.files[0];
                  const fileExtension = file.type.split('/')[file.type.split('/').length - 1];
                  const fileSizeInMB = file.size / 1024 / 1024;

                  if (!['jpeg', 'jpg', 'png'].includes(fileExtension)) {
                    enqueueSnackbar(
                      'Please select an image with .jpeg, .jpg, or .png extension only.',
                      {
                        variant: 'error',
                        anchorOrigin: {
                          vertical: 'top',
                          horizontal: 'center',
                        },
                      }
                    );
                    return;
                  }
                  if (fileSizeInMB > 10) {
                    enqueueSnackbar('Please select a smaller image file size.', {
                      variant: 'error',
                      anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'center',
                      },
                    });
                    return;
                  }
                  try {
                    const { width, height } = await checkImageDimension(file);
                    if (width < 500 || height < 500) {
                      enqueueSnackbar('Min dimesion of the image must be 500x500.', {
                        variant: 'error',
                        anchorOrigin: {
                          vertical: 'top',
                          horizontal: 'center',
                        },
                      });
                      return;
                    }
                  } catch (error) {
                    enqueueSnackbar('Something went wrong.', {
                      variant: 'error',
                      anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'center',
                      },
                    });
                    return;
                  }
                  try {
                    const base64 = await convertToBase64(file);
                    setDialogData((prevValues) => ({
                      ...userObject,
                      profile_picture: String(base64),
                    }));
                  } catch (error) {
                    enqueueSnackbar('Something went wrong.', {
                      variant: 'error',
                      anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'center',
                      },
                    });
                  }
                }}
              />
            </>
          )}
      </div>
      {dialogData?.profile_picture && (
        <div className="fixed left-0 top-0 z-50 h-screen w-screen">
          <Cropper
            image={dialogData?.profile_picture}
            crop={crop}
            zoom={1}
            aspect={1}
            onCropChange={(temp) => setCrop(temp)}
            onCropComplete={async (croppedArea, croppedAreaPixels) => {
              const newlyCroppedImage = await getCroppedImg(
                dialogData?.profile_picture,
                croppedAreaPixels
              );
              setCroppedImage(newlyCroppedImage);
            }}
            cropSize={{ width: 500, height: 500 }}
          />
          <Link
            href="/profile/Profile Photo Guideline.pdf"
            target="__blank"
            className="absolute right-0 top-0 "
          >
            <Tooltip title="Profile photo guidelines.">
              <Info className="m-4 rounded-full bg-white" />
            </Tooltip>
          </Link>
          <div className="absolute bottom-0 left-0 flex w-full justify-end bg-white px-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="m-2 p-2">
              Cancel
            </button>
            <button
              type="button"
              onClick={() => handleDialogClose('put')}
              className="m-2 bg-[#5e7ce0] p-2 text-white"
            >
              Save
            </button>
          </div>
        </div>
      )}

      <Dialog open={dialog2Open} onClose={() => setDialog2Open(false)}>
        <DialogTitle className="text-center">Profile Photo</DialogTitle>

        <DialogContent>
          <div className="min-w-[300px] text-center">Remove profile photo?</div>
        </DialogContent>

        <DialogActions>
          <div className="flex w-full justify-between">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
              No
            </button>
            <button
              type="button"
              onClick={() => handleDialogClose('put')}
              className="bg-red-500 p-2 text-white"
            >
              Yes
            </button>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

ProfilePicture.propTypes = {
  userObject: PropTypes.object,
  userId: PropTypes.string,
  fetchData: PropTypes.func,
};

export default ProfilePicture;

/**
 * Functions
 */

const checkImageDimension = (file) =>
  new Promise((resolve, reject) => {
    const fileReader = new FileReader();
    fileReader.readAsDataURL(file);
    fileReader.onload = (e) => {
      const img = new Image();
      img.src = e.target.result;
      img.onload = () => {
        resolve(img);
      };
    };
    fileReader.onerror = (error) => {
      reject(error);
    };
  });

const convertToBase64 = (file) =>
  new Promise((resolve, reject) => {
    const fileReader = new FileReader();
    fileReader.readAsDataURL(file);
    fileReader.onload = () => {
      resolve(fileReader.result);
    };
    fileReader.onerror = (error) => {
      reject(error);
    };
  });

const createImage = (url) =>
  new Promise((resolve, reject) => {
    const image = new Image();
    image.addEventListener('load', () => resolve(image));
    image.addEventListener('error', (error) => reject(error));
    image.src = url;
  });

const getRadianAngle = (degreeValue) => (degreeValue * Math.PI) / 180;

const rotateSize = (width, height, rotation) => {
  const rotRad = getRadianAngle(rotation);
  return {
    width: Math.abs(Math.cos(rotRad) * width) + Math.abs(Math.sin(rotRad) * height),
    height: Math.abs(Math.sin(rotRad) * width) + Math.abs(Math.cos(rotRad) * height),
  };
};

const getCroppedImg = async (
  imageSrc,
  pixelCrop,
  rotation = 0,
  flip = { horizontal: false, vertical: false }
) => {
  const image = await createImage(imageSrc);
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    return null;
  }

  const rotRad = getRadianAngle(rotation);

  // calculate bounding box of the rotated image
  const { width: bBoxWidth, height: bBoxHeight } = rotateSize(image.width, image.height, rotation);

  // set canvas size to match the bounding box
  canvas.width = bBoxWidth;
  canvas.height = bBoxHeight;

  // translate canvas context to a central location to allow rotating and flipping around the center
  ctx.translate(bBoxWidth / 2, bBoxHeight / 2);
  ctx.rotate(rotRad);
  ctx.scale(flip.horizontal ? -1 : 1, flip.vertical ? -1 : 1);
  ctx.translate(-image.width / 2, -image.height / 2);

  // draw rotated image
  ctx.drawImage(image, 0, 0);

  const croppedCanvas = document.createElement('canvas');

  const croppedCtx = croppedCanvas.getContext('2d');

  if (!croppedCtx) {
    return null;
  }

  // Set the size of the cropped canvas
  croppedCanvas.width = pixelCrop.width;
  croppedCanvas.height = pixelCrop.height;

  // Draw the cropped image onto the new canvas
  croppedCtx.drawImage(
    canvas,
    pixelCrop.x,
    pixelCrop.y,
    pixelCrop.width,
    pixelCrop.height,
    0,
    0,
    pixelCrop.width,
    pixelCrop.height
  );

  // As Base64 string
  return croppedCanvas.toDataURL('image/jpeg');
};
