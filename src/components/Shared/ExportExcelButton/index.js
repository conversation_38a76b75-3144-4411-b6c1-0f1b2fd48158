// Packages
import PropTypes from 'prop-types';
import * as xlsx from 'xlsx';

// Others
import { useSimiContext } from '../../../utils/simi';

const ExportExcelButton = ({
  children,
  data,
  headers = [],
  filename = 'export.xlsx',
  buttonText = 'Export',
  buttonClassname = '',
  ...others
}) => {
  // Standard and Vars
  const { moduleColorCode } = useSimiContext();

  return (
    <button
      type="button"
      data={data}
      {...others}
      className={buttonClassname || ''}
      target="_blank"
      onClick={() => {
        let filteredData = data;
        if (headers?.length !== 0) {
          filteredData = data?.map((o) =>
            headers?.reduce((temp, p) => {
              temp[p.label] = o[p.key];
              return temp;
            }, {})
          );
        }
        const worksheet = xlsx.utils.json_to_sheet(filteredData);
        const workbook = xlsx.utils.book_new();
        xlsx.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
        xlsx.writeFile(workbook, filename);
      }}
    >
      {children ||
        (buttonClassname ? (
          buttonText
        ) : (
          <div style={{ backgroundColor: moduleColorCode }} className="cta-btn">
            {buttonText}
          </div>
        ))}
    </button>
  );
};

ExportExcelButton.propTypes = {
  children: PropTypes.any,
  data: PropTypes.array,
  headers: PropTypes.array,
  filename: PropTypes.string,
  buttonText: PropTypes.string,
  buttonClassname: PropTypes.string,
};

export default ExportExcelButton;
