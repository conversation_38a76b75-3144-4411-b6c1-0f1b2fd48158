// React, Next, Tw
import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';

// Packages
import { MapContainer, TileLayer, Popup, Polyline } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';

// Components
import OnMapDiv from './OnMapDiv';

// Others
import { setIsLoading } from '../../utils/store/loadingReducer';

const OpenStreetMap = () => {
  // Standard and Vars
  const mapRef = useRef();
  const { query } = useRouter();
  const { cableCode } = query;
  const dispatch = useDispatch();

  const [allCableData, setAllCableData] = useState([]);

  // Others

  const filteredCableData = (() => {
    if ([undefined, '']?.includes(cableCode)) return allCableData;
    return allCableData.filter((o) => o?.id === cableCode);
  })();

  const fetchAllCableData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await fetch(`/capry/cable-geo.json`);
      if (response?.status === 200) {
        const data = await response.json();
        const temp = data?.features?.map((o) => {
          for (let i = 0; i < o?.geometry?.coordinates?.length; i += 1) {
            for (let j = 0; j < o?.geometry?.coordinates[i]?.length; j += 1) {
              o.geometry.coordinates[i][j].reverse();
            }
          }
          return {
            ...o?.properties,
            ...o?.geometry,
          };
        });
        setAllCableData(temp);
      }
    } catch {
      setAllCableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchAllCableData();
  }, []);

  useEffect(() => {
    if (filteredCableData?.length === 1 && mapRef)
      mapRef?.current?.fitBounds(filteredCableData?.[0]?.coordinates);
  }, [filteredCableData?.length]);

  return (
    <div className="container">
      <div className="row">
        <div className="col">
          <div className="container">
            <div className="relative h-[45vh] md:h-[75vh]">
              <MapContainer
                center={[0, 0]}
                zoom={2}
                minZoom={2}
                zoomControl={false}
                ref={mapRef}
                style={{
                  height: '100%',
                  width: '100%',
                  borderRadius: '16px',
                  overflow: 'hidden',
                  zIndex: '10',
                }}
              >
                <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
                {filteredCableData?.map((o, i) => (
                  <Polyline
                    key={i}
                    pathOptions={{ color: o?.color }}
                    positions={o?.coordinates}
                    eventHandlers={{
                      mouseover: (event) => event.target.openPopup(),
                      mouseout: (event) => event.target.closePopup(),
                    }}
                    className="cursor-pointer"
                  >
                    <Popup>
                      <p>{o?.name}</p>
                    </Popup>
                  </Polyline>
                ))}
              </MapContainer>
              <div className="absolute right-0 top-0 z-50 m-4 hidden overflow-x-auto  scrollbar md:block">
                <OnMapDiv />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OpenStreetMap;
