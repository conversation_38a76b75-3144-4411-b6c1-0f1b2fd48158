// Next, React, Tw
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';

// Components
import { SearchInput } from '../../Shared/CustomInput';
import ExportExcelButton from '../../Shared/ExportExcelButton';

// Others
import axios from '../../../utils/axios';
import { useParamContext } from '../../../utils/auth/ParamProvider';
import { CAPRY_ENDPOINT } from '../../../utils/capry';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { sortArrayOfObjectsByCertainKeyAlphabetically } from '../../../utils/shared';

const CableSystemView = () => {
  // Standard
  const { setParam } = useParamContext();
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { q } = query;

  // Table
  const [cableSystemList, setCableSystemList] = useState([]);

  const bodyCellStyle = 'text-center py-1 text-sm font-semibold';

  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return cableSystemList;
    }
    return cableSystemList.filter((o) =>
      JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase())
    );
  })();

  // Others

  const headers = (() => {
    const temp = filteredTableData?.[0];
    if (!temp) return [];
    const temp2 = Object?.keys(temp)?.filter(
      (o) => !['id', 'cable_id', 'bearer_id', 'submarine_cable_map_website_code'].includes(o)
    );
    return temp2?.map((o) => ({ label: o, key: o }));
  })();

  const fetchAllCableSystem = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/offnet/cable/all/keyword`);

      if (response?.data?.data) {
        setCableSystemList(
          sortArrayOfObjectsByCertainKeyAlphabetically(response?.data?.data, 'name')
        );
      }
    } catch {
      setCableSystemList([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchAllCableSystem();
  }, []);

  return (
    <div className="p-6">
      {/* Header Section */}
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Cable Systems</h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Select a cable system to view its bearers and paths
            </p>
          </div>
          <ExportExcelButton
            key={filteredTableData?.length}
            headers={headers}
            data={filteredTableData}
            buttonClassname="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export
          </ExportExcelButton>
        </div>

        {/* Search Bar */}
        <div className="max-w-md">
          <SearchInput placeholder="Search cable systems..." className="w-full" />
        </div>
      </div>

      {/* Table Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-20">
                  #
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Cable System
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-16">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredTableData.map((row, i) => (
                <tr
                  key={i}
                  className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors group"
                  onClick={() => setParam({ cableId: row?.id })}
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {i + 1}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      {row?.name}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right">
                    <svg className="w-4 h-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Empty State */}
        {filteredTableData.length === 0 && (
          <div className="text-center py-12">
            <div className="w-12 h-12 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-1">No cable systems found</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">Try adjusting your search criteria</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CableSystemView;
