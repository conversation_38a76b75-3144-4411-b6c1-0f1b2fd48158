// Next, React, Tw
import { useRef } from 'react';
import { useDispatch } from 'react-redux';

// Packages
import Papa from 'papaparse';
import * as R from 'ramda';
import * as yup from 'yup';
// import moment from 'moment'; // Removed - no longer needed for date validation

// Others
import axios from '../../../utils/axios';
import { CAPRY_ENDPOINT } from '../../../utils/capry';
import { useSnackbar } from '../../Shared/snackbar';
import { setIsLoading } from '../../../utils/store/loadingReducer';

const UploadBulkButton = () => {
  // Standard
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();

  const fileInputRef = useRef(null);

  // Others

  const schema = yup.object({
    name: yup.string().required('Please provide cable name.'),
  });
  const schema2 = yup.object({
    cable_id: yup.string().required('Please provide cable ID.'),
    bearer_name: yup.string().required('Please provide bearer name.'),
    rb_ref: yup.string().required('Please provide RB Ref.'),
  });
  const schema3 = yup.object({
    cable_id: yup.string().required('Please provide cable ID.'),
    bearer_id: yup.string().required('Please provide bearer ID.'),
    status: yup.string().required('Please provide status.'),
    total_capacity: yup.number().required('Please provide total capacity.'),
  });

  // Date validation helper functions - REMOVED
  // const validateDateFormat = (dateString, fieldName) => {
  //   if (!dateString || dateString.trim() === '') {
  //     return { isValid: true, error: null }; // Empty dates are allowed
  //   }

  //   const trimmedDate = dateString.trim();

  //   // Check for common date formats: YYYY-MM-DD, DD/MM/YYYY, DD-MM-YYYY
  //   const dateFormats = ['YYYY-MM-DD', 'DD/MM/YYYY', 'DD-MM-YYYY', 'MM/DD/YYYY'];
  //   let parsedDate = null;

  //   for (const format of dateFormats) {
  //     const testDate = moment(trimmedDate, format, true);
  //     if (testDate.isValid()) {
  //       parsedDate = testDate;
  //       break;
  //     }
  //   }

  //   if (!parsedDate || !parsedDate.isValid()) {
  //     return {
  //       isValid: false,
  //       error: `${fieldName} has invalid date format. Please use YYYY-MM-DD, DD/MM/YYYY, or DD-MM-YYYY format.`
  //     };
  //   }

  //   return { isValid: true, error: null, parsedDate };
  // };

  // const validateServiceDates = (startDate, endDate, rowIndex) => {
  //   const errors = [];

  //   // Validate start date format
  //   const startValidation = validateDateFormat(startDate, 'Service Start Date');
  //   if (!startValidation.isValid) {
  //     errors.push(`Row ${rowIndex + 1}: ${startValidation.error}`);
  //   }

  //   // Validate end date format
  //   const endValidation = validateDateFormat(endDate, 'Service End Date');
  //   if (!endValidation.isValid) {
  //     errors.push(`Row ${rowIndex + 1}: ${endValidation.error}`);
  //   }

  //   // If both dates are valid and present, check logical validation
  //   if (startValidation.isValid && endValidation.isValid &&
  //       startValidation.parsedDate && endValidation.parsedDate) {
  //     if (startValidation.parsedDate.isAfter(endValidation.parsedDate)) {
  //       errors.push(`Row ${rowIndex + 1}: Service Start Date cannot be after Service End Date.`);
  //     }
  //   }

  //   return errors;
  // };

  // const formatDateForDatabase = (dateString) => {
  //   if (!dateString || dateString.trim() === '') {
  //     return null;
  //   }

  //   const validation = validateDateFormat(dateString, 'Date');
  //   if (validation.isValid && validation.parsedDate) {
  //     return validation.parsedDate.format('YYYY-MM-DD');
  //   }

  //   return null;
  // };

  const fetchAllCable = async () => {
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/offnet/cable/all/keyword`);

      if (response?.data?.data) {
        return response?.data?.data;
      }
    } catch {
      /* empty */
    }
    return [];
  };

  const fetchAllBearer = async () => {
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/offnet/bearer/all/keyword`);

      if (response?.data?.data) {
        return response?.data?.data;
      }
    } catch {
      /* empty */
    }
    return [];
  };

  const saveNewCable = async (data) => {
    let payload;
    try {
      payload = await schema.validate(data, { abortEarly: false });
    } catch (error) {
      enqueueSnackbar(error.errors, {
        variant: 'error',
      });
      return;
    }
    dispatch(setIsLoading(true));
    try {
      await axios.post(`${CAPRY_ENDPOINT}/offnet/cable`, payload);
    } catch {
      /* empty */
    }
    dispatch(setIsLoading(false));
  };

  const saveNewBearer = async (data) => {
    let payload;
    try {
      payload = await schema2.validate(data, { abortEarly: false });
    } catch (error) {
      enqueueSnackbar(error.errors, {
        variant: 'error',
      });
      return;
    }

    dispatch(setIsLoading(true));
    try {
      await axios.post(`${CAPRY_ENDPOINT}/offnet/bearer`, payload);
    } catch {
      /* empty */
    }
    dispatch(setIsLoading(false));
  };

  const saveNewPath = async (data, rowIndex = null) => {
    let payload;
    try {
      // Log the data before validation
      console.log(`Row ${rowIndex + 1} pre-validation data:`, data);
      
      payload = await schema3.validate(data, { abortEarly: false });
      
      // Log the validated payload
      console.log(`Row ${rowIndex + 1} validated payload:`, payload);
    } catch (error) {
      const errorMessages = Array.isArray(error.errors) ? error.errors : [error.message];
      const rowPrefix = rowIndex !== null ? `Row ${rowIndex + 1}: ` : '';

      errorMessages.forEach(msg => {
        enqueueSnackbar(`${rowPrefix}${msg}`, {
          variant: 'error',
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
      });
      return { success: false, errors: errorMessages };
    }

    dispatch(setIsLoading(true));
    try {
      // Log the exact payload being sent to the API
      console.log(`Row ${rowIndex + 1} API request payload:`, JSON.stringify(payload));
      
      const response = await axios.post(`${CAPRY_ENDPOINT}/offnet/path`, payload);
      dispatch(setIsLoading(false));
      
      // Log successful response
      console.log(`Row ${rowIndex + 1} API response:`, response.data);
      
      return { success: true };
    } catch (error) {
      dispatch(setIsLoading(false));
      
      // Enhanced error logging
      const errorMessage = error?.response?.data?.message || 'Failed to save path data';
      const errorDetails = error?.response?.data?.details || {};
      const statusCode = error?.response?.status;
      
      console.error(`Row ${rowIndex + 1} API Error (${statusCode}):`, {
        message: errorMessage,
        details: errorDetails,
        payload: payload,
        error: error.toString(),
        response: error?.response?.data
      });

      const rowPrefix = rowIndex !== null ? `Row ${rowIndex + 1}: ` : '';
      enqueueSnackbar(`${rowPrefix}${errorMessage}`, {
        variant: 'error',
        anchorOrigin: {
          vertical: 'top',
          horizontal: 'center',
        },
      });

      return { success: false, errors: [errorMessage], details: errorDetails };
    }
  };

  const manipulateData = (data) =>
    data?.map((o) => ({
      ...o,
      'Parent Capacity': `${o?.['Parent Capacity']}, ${o?.['RB Ref']}`,
    }));

  const processData = async (data) => {
    // Pre-validation: Check for service date validation errors - REMOVED
    // const dateValidationErrors = [];

    // data.forEach((row, index) => {
    //   const serviceStartDate = row['Service Start Date'];
    //   const serviceEndDate = row['Service End Date'];

    //   const errors = validateServiceDates(serviceStartDate, serviceEndDate, index);
    //   dateValidationErrors.push(...errors);
    // });

    // // If there are date validation errors, show them and stop processing
    // if (dateValidationErrors.length > 0) {
    //   dateValidationErrors.forEach(error => {
    //     enqueueSnackbar(error, {
    //       variant: 'error',
    //       anchorOrigin: {
    //         vertical: 'top',
    //         horizontal: 'center',
    //       },
    //     });
    //   });

    //   enqueueSnackbar(`Found ${dateValidationErrors.length} date validation error(s). Please fix them and try again.`, {
    //     variant: 'error',
    //     anchorOrigin: {
    //       vertical: 'top',
    //       horizontal: 'center',
    //     },
    //   });
    //   return;
    // }

    let uniqueUploadedCable = R.compose(R.uniq, R.pluck('Cable'))(data);
    let cableList = await fetchAllCable();
    uniqueUploadedCable = uniqueUploadedCable?.filter(
      (o) => !cableList?.find((p) => p?.name === o)
    );

    // Save New Cable
    for (let i = 0; i < uniqueUploadedCable.length; i += 1) {
      await saveNewCable({
        internal_code: '',
        name: uniqueUploadedCable[i],
        submarine_cable_map_website_code: '',
        type: uniqueUploadedCable[i]?.toLowerCase()?.includes('bh') ? 'backhaul' : 'submarine',
      });
    }

    // Fetch New Data After Uploaded New Cable
    cableList = await fetchAllCable();

    const uniqueUploadedBearer = R.compose(R.uniq, R.pluck('Parent Capacity'))(data);
    // Save New Bearer
    for (let i = 0; i < uniqueUploadedBearer.length; i += 1) {
      const temp2 = data?.find((o) => o['Parent Capacity'] === uniqueUploadedBearer[i]);
      const temp = cableList?.find((o) => o?.name === temp2.Cable);
      await saveNewBearer({
        cable_id: temp?.id,
        bearer_name: temp2['Parent Capacity'],
        rb_ref: temp2['RB Ref'],
        partner: temp2?.Partner,
      });
    }

    // Fetch New Data After Uploaded New Bearer
    const bearerList = await fetchAllBearer();

    // Upload New Path with fail-fast error handling
    let successCount = 0;
    const errorLog = [];

    console.log(`Starting bulk upload process for ${data?.length} rows...`);

    for (let i = 0; i < data?.length; i += 1) {
      try {
        console.log(`Processing row ${i + 1}/${data?.length}...`);

        const temp = cableList?.find((o) => o?.name === data[i].Cable);
        const temp2 = bearerList?.find((o) => o?.bearer_name === data[i]['Parent Capacity']);
        const landingPointA = data[i]?.Route?.split('-')[0]?.trim();
        const landingPointB = data[i]?.Route?.split('-')[1]?.trim();

        // Debug capacity processing
        const rawCapacity = data[i]['Capacity (Gbps)'];
        const processedCapacity = parseFloat((rawCapacity?.toString() || '0').replace(/G/gi, ''));
        console.log(`Row ${i+1}: Raw capacity value: "${rawCapacity}", Processed: ${processedCapacity}, Type: ${typeof processedCapacity}`);

        // Log the payload being sent
        const payload = {
          // Basic
          cable_id: temp?.id,
          bearer_id: temp2?.id,
          path_name: data[i]['Child Capacity'],
          salesforce_id: data[i]['SFA ID'],
          sofca_id: data[i]['SOFCA Ref No'],
          service_no: data[i]['Service No']?.trim(),
          status: data[i]['Status utilization (reserved/provisioning/activated)']
            ?.trim()
            ?.toLowerCase(),
          landing_point_a: landingPointA,
          landing_point_b: `${landingPointB || landingPointA}`,
          total_capacity: processedCapacity,
          type: data[i]['Execute Asset/Stock/JOP']?.trim()?.toLowerCase(),
          // General
          ocm_boc: data[i]['OCM/BOC'],
          date: data[i].Date,
          // Service Dates (passed as-is from CSV)
          service_start_date: data[i]['Service Start Date'],
          service_end_date: data[i]['Service End Date'],
          // Contract
          contract_term_years: data[i]['Contract Term (Years)'],
          currency: data[i].Curr,
          iru_otc: data[i]['IRU OTC'],
          otc: data[i].OTC,
          om_percentage: data[i]['O&M %'],
          annual_recurring_charge: data[i]['Annual Recurring Charge'],
          total_contract: data[i]['Total Contract'],
          sales_pic: data[i]['Sales PIC'],
          // Partner
          tm_po_ref_no: data[i]['TM PO Ref no'],
          prime_ref_id: data[i]['PRIME ref ID'],
          partner_service_ref_no: data[i]['Partner Service Ref No'],
          scn_handover_ref_no: data[i]['SCN/Handover ref no'],
          partner_circuit_id: data[i]['Partner Circuit ID'],
          invoice_no: data[i]['Invoice No'],
          po_date: data[i]['PO Date'],
          handover_date: data[i]['Handover Date'],
          bill_start_date: data[i]['Bill Start Date'],
          invoice_date: data[i]['Invoice Date'],
          // Customer
          customer_name: data[i]['Confirm Customer'],
          product_type: data[i]['Product Type'],
          account_no: data[i]['Account No'],
          // Finance
          asset_no: data[i].Asset,
          original_asset: data[i]['Original asset'],
          project_definition: data[i]['Project Definition'],
          first_acquisition_on: data[i]['First Acq. on'],
          capitalized_on: data[i]['Capitalized Date / Stock-in Date'],
        };

        console.log(`Row ${i+1} payload:`, payload);

        const result = await saveNewPath(payload, i);

        if (result.success) {
          successCount++;
          console.log(`Row ${i + 1} processed successfully`);
        } else {
          // FAIL-FAST: Stop processing on first error
          const errorDetails = {
            rowNumber: i + 1,
            rowData: data[i],
            payload: payload,
            errors: result.errors || [],
            details: result.details || {},
            timestamp: new Date().toISOString()
          };

          errorLog.push(errorDetails);

          console.error('BULK UPLOAD STOPPED - Error encountered:', errorDetails);

          // Show detailed error information
          enqueueSnackbar(`Upload stopped at row ${i + 1} due to error. Check console for detailed error log.`, {
            variant: 'error',
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'center',
            },
            autoHideDuration: 8000,
          });

          // Log detailed error information to console
          console.group(`🚨 BULK UPLOAD ERROR LOG - Row ${i + 1}`);
          console.error('Row Data:', data[i]);
          console.error('Processed Payload:', payload);
          console.error('Error Messages:', result.errors);
          console.error('Error Details:', result.details);
          console.error('Full Error Log:', errorLog);
          console.groupEnd();

          // Stop processing immediately
          return;
        }
      } catch (error) {
        // FAIL-FAST: Stop processing on unexpected error
        const errorDetails = {
          rowNumber: i + 1,
          rowData: data[i],
          error: {
            message: error.message,
            stack: error.stack,
            name: error.name
          },
          timestamp: new Date().toISOString()
        };

        errorLog.push(errorDetails);

        console.error('BULK UPLOAD STOPPED - Unexpected error:', errorDetails);

        const errorMessage = `Upload stopped at row ${i + 1}: ${error.message}`;
        enqueueSnackbar(errorMessage, {
          variant: 'error',
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
          autoHideDuration: 8000,
        });

        // Log detailed error information to console
        console.group(`🚨 BULK UPLOAD UNEXPECTED ERROR LOG - Row ${i + 1}`);
        console.error('Row Data:', data[i]);
        console.error('Error:', error);
        console.error('Full Error Log:', errorLog);
        console.groupEnd();

        // Stop processing immediately
        return;
      }
    }

    // Show success summary only if all rows processed successfully
    console.log(`Bulk upload completed successfully. Processed ${successCount}/${data?.length} rows.`);
    enqueueSnackbar(`Successfully processed all ${successCount} row(s).`, {
      variant: 'success',
      anchorOrigin: {
        vertical: 'top',
        horizontal: 'center',
      },
    });

    if (window) window.location.reload();
  };

  return (
    <>
      <button
        type="button"
        className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 border border-transparent rounded-lg shadow-sm transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        onClick={() => fileInputRef?.current?.click()}
      >
        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
        </svg>
        Upload Bulk
      </button>
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept=".csv"
        onChange={(event) => {
          const file = event?.target?.files?.[0];

          if (!file) {
            enqueueSnackbar('No file selected. Please select a CSV file.', {
              variant: 'error',
              anchorOrigin: {
                vertical: 'top',
                horizontal: 'center',
              },
            });
            return;
          }

          const extension = file.name.split('.').pop()?.toLowerCase();

          if (extension !== 'csv') {
            enqueueSnackbar('Invalid file format. Please upload a CSV file only.', {
              variant: 'error',
              anchorOrigin: {
                vertical: 'top',
                horizontal: 'center',
              },
            });
            return;
          }

          // Show processing message
          enqueueSnackbar('Processing CSV file...', {
            variant: 'info',
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'center',
            },
          });

          Papa.parse(file, {
            header: true,
            skipEmptyLines: true,
            complete: (results) => {
              if (results.errors && results.errors.length > 0) {
                enqueueSnackbar('Error parsing CSV file. Please check the file format.', {
                  variant: 'error',
                  anchorOrigin: {
                    vertical: 'top',
                    horizontal: 'center',
                  },
                });
                return;
              }

              if (!results.data || results.data.length === 0) {
                enqueueSnackbar('CSV file is empty or contains no valid data.', {
                  variant: 'error',
                  anchorOrigin: {
                    vertical: 'top',
                    horizontal: 'center',
                  },
                });
                return;
              }

              processData(manipulateData(results.data));
            },
            error: (error) => {
              enqueueSnackbar(`Failed to parse CSV file: ${error.message}`, {
                variant: 'error',
                anchorOrigin: {
                  vertical: 'top',
                  horizontal: 'center',
                },
              });
            }
          });

          // Reset file input
          event.target.value = '';
        }}
      />
    </>
  );
};

export default UploadBulkButton;
