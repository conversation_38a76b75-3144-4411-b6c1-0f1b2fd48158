// Next, React, Tw
import { useEffect, useState, useMemo } from 'react';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';
import { twMerge } from 'tailwind-merge';
import { useSnackbar } from 'notistack';

// Packages
import * as R from 'ramda';

// Components
import { SearchInput, SelectInput } from '../../Shared/CustomInput';
import { TablePaginationCustom } from '../../Shared/table';
import ExportExcelButton from '../../Shared/ExportExcelButton';

// Others
import axios from '../../../utils/axios';
import { CAPRY_ENDPOINT, getStatusCircle, getTypeComponent } from '../../../utils/capry';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import {
  checkAndReplaceStringWithHyphen,
  sortArrayOfObjectsByCertainKeyAlphabetically,
} from '../../../utils/shared';
import { useParamContext } from '../../../utils/auth/ParamProvider';
import { useModuleRoleContext } from '../../../utils/auth/ModuleRoleProvider';

const AllPathsView = () => {
  // Standard
  const dispatch = useDispatch();
  const { push, query } = useRouter();
  const { replaceParam, setParam } = useParamContext();
  const { q, status, type } = query;
  const { page, rowsPerPage } = useSelector((state) => state.simi);
  const { isAdmin } = useModuleRoleContext();
  const { enqueueSnackbar } = useSnackbar();

  const [cableData, setCableData] = useState([]);
  const [bearerData, setBearerData] = useState([]);
  const [pathData, setPathData] = useState([]);
  const [isLoading, setIsLoadingLocal] = useState(false);

  // Table

  const getBodyCellStyle = () => 'text-center py-1 text-xs';

  const memoizedCombinedPathData = useMemo(
    () =>
      sortArrayOfObjectsByCertainKeyAlphabetically(
        pathData?.map((o) => ({
          ...cableData?.find((c) => c?.id === o?.cable_id),
          ...bearerData?.find((b) => b?.id === o?.bearer_id),
          ...o,
        })),
        'name'
      ),
    [cableData?.length, bearerData?.length, pathData?.length]
  );

  const filteredTableData = (() => {
    let temp = memoizedCombinedPathData;
    if (status !== 'all') temp = temp?.filter((o) => o?.status === status);
    if (type !== 'all') temp = temp?.filter((o) => o?.type === type);
    if ([undefined, '']?.includes(q)) {
      return temp;
    }
    return temp.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  // Others

  const headers = (() => {
    const temp = filteredTableData?.[0];
    if (!temp) return [];
    const temp2 = Object?.keys(temp)?.filter(
      (o) => !['id', 'cable_id', 'bearer_id', 'submarine_cable_map_website_code'].includes(o)
    );
    return temp2?.map((o) => ({ label: o, key: o }));
  })();

  const options = (() => R.uniq(R.pluck('status', memoizedCombinedPathData)))();

  const types = (() => R.uniq(R.pluck('type', memoizedCombinedPathData)))();

  const fetchCableData = async () => {
    setIsLoadingLocal(true);
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/offnet/cable/all/keyword`);

      if (response?.data?.data) {
        setCableData(response?.data?.data);
      }
    } catch (error) {
      //   console.log(error);
    }
    dispatch(setIsLoading(false));
    setIsLoadingLocal(false);
  };

  const fetchBearerData = async () => {
    setIsLoadingLocal(true);
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/offnet/bearer/all/keyword`);

      if (response?.data?.data) {
        setBearerData(response?.data?.data);
      }
    } catch (error) {
      //   console.log(error);
    }
    dispatch(setIsLoading(false));
    setIsLoadingLocal(false);
  };

  const fetchPathData = async () => {
    setIsLoadingLocal(true);
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/offnet/path/all/keyword`);
      setPathData(response?.data?.data || []);
    } catch (error) {
      //   console.log(error);
    }
    dispatch(setIsLoading(false));
    setIsLoadingLocal(false);
  };

  useEffect(() => {
    fetchCableData();
    fetchBearerData();
    fetchPathData();
  }, []);

  useEffect(() => {
    if (!status) {
      replaceParam({ status: 'all', type: 'all' });
    }
  }, [status]);

  // Click to copy functionality
  const handleCopyToClipboard = async (text, label) => {
    try {
      await navigator.clipboard.writeText(text);
      enqueueSnackbar(`${label} copied to clipboard`, { variant: 'success' });
    } catch (err) {
      enqueueSnackbar('Failed to copy to clipboard', { variant: 'error' });
    }
  };

  return (
    <div className="p-6">
      {/* Header Section */}
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Network Paths</h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              View and manage all network capacity paths and their utilization
            </p>
          </div>
          <ExportExcelButton
            key={filteredTableData?.length}
            headers={headers}
            data={filteredTableData}
            buttonClassname="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export
          </ExportExcelButton>
        </div>

        {/* Search and Filter Controls */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="flex flex-col gap-3 md:flex-row md:items-center">
            <div className="w-full md:w-48">
              <SearchInput placeholder="Search paths..." className="w-full" />
            </div>
            <div className="flex gap-3">
              <div className="w-full md:w-40">
                <SelectInput
                  value={status}
                  placeholder="Status"
                  options={['all', ...options]}
                  onChange={(event) => setParam({ status: event?.target?.value })}
                />
              </div>
              <div className="w-full md:w-40">
                <SelectInput
                  value={type}
                  placeholder="Type"
                  options={['all', ...types]}
                  onChange={(event) => setParam({ type: event?.target?.value })}
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Table Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                {[
                  { label: '#', width: 'w-16', align: 'text-center' },
                  { label: 'Cable System', width: '', align: 'text-center' },
                  { label: 'Bearer', width: '', align: 'text-left' },
                  { label: 'Service No.', width: '', align: 'text-center' },
                  { label: 'Route', width: '', align: 'text-center' },
                  { label: 'Capacity (Gbps)', width: 'w-32', align: 'text-center' },
                  { label: 'Type', width: 'w-24', align: 'text-center' },
                  { label: 'Status', width: 'w-28', align: 'text-center' },
                ].map((header, i) => (
                  <th
                    key={i}
                    className={`px-6 py-3 ${header.align} text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider ${header.width}`}
                  >
                    {header.label}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {isLoading ? (
                // Skeleton loading rows
                Array.from({ length: 5 }).map((_, i) => (
                  <tr key={`skeleton-${i}`} className="animate-pulse">
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-8 mx-auto"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-32 mx-auto"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20 mx-auto"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-40 mx-auto"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16 mx-auto"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16 mx-auto"></div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-20 mx-auto"></div>
                    </td>
                  </tr>
                ))
              ) : filteredTableData.length === 0 ? (
                // Empty state
                <tr>
                  <td colSpan="8" className="px-6 py-12 text-center">
                    <div className="flex flex-col items-center gap-4">
                      <div className="w-12 h-12 text-gray-400">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <div className="text-gray-500 dark:text-gray-400 text-sm">No network paths found</div>
                      <div className="text-gray-400 dark:text-gray-500 text-xs">Try adjusting your search or filter criteria</div>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredTableData
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((row, i) => (
                  <tr
                    key={i}
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 group hover:shadow-sm"
                    style={{ transform: 'translateY(0px)', transition: 'transform 0.2s ease-in-out' }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-1px)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0px)';
                    }}
                    onClick={() =>
                      push(
                        `/capry/inventory?category=offnet&cableId=${row?.cable_id}&bearerId=${row?.bearer_id}&pathId=${row?.id}`
                      )
                    }
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 text-center">
                      {i + 1 + rowsPerPage * page}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div
                        className="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCopyToClipboard(row?.name, 'Cable name');
                        }}
                        title="Click to copy cable name"
                      >
                        {checkAndReplaceStringWithHyphen(row?.name)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div
                        className="text-sm text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCopyToClipboard(row?.bearer_name, 'Bearer name');
                        }}
                        title="Click to copy bearer name"
                      >
                        {checkAndReplaceStringWithHyphen(row?.bearer_name)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div
                        className="text-sm text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCopyToClipboard(row?.service_no, 'Service number');
                        }}
                        title="Click to copy service number"
                      >
                        {checkAndReplaceStringWithHyphen(row?.service_no)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div
                        className="text-sm text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCopyToClipboard(`${row?.landing_point_a} - ${row?.landing_point_b}`, 'Route');
                        }}
                        title="Click to copy route"
                      >
                        {`${row?.landing_point_a} - ${row?.landing_point_b}`}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div
                        className="text-sm font-medium text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleCopyToClipboard(row?.total_capacity, 'Capacity');
                        }}
                        title="Click to copy capacity"
                      >
                        {row?.total_capacity}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      {getTypeComponent(row?.type)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      {getStatusCircle(row?.status)}
                    </td>
                  </tr>
                  ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="mt-6">
        <TablePaginationCustom count={filteredTableData.length} />
      </div>
    </div>
  );
};

export default AllPathsView;
