// Next, React, Tw
import { Fragment, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';
import { twMerge } from 'tailwind-merge';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { Edit, ArrowForwardIos } from '@mui/icons-material';

// Packages
import * as yup from 'yup';

// Components
import PathRow from './PathRow';
import { TextInput, SelectInput, SearchInput } from '../../Shared/CustomInput';
import ExportExcelButton from '../../Shared/ExportExcelButton';

// Others
import axios from '../../../utils/axios';
import { useParamContext } from '../../../utils/auth/ParamProvider';
import { useModuleRoleContext } from '../../../utils/auth/ModuleRoleProvider';
import { CAPRY_ENDPOINT } from '../../../utils/capry';
import { useSnackbar } from '../../Shared/snackbar';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { useAuthContext } from '../../../utils/auth/useAuthContext';

const BearerView = () => {
  // Standard and Vars
  const { setParam } = useParamContext();
  const { query, push } = useRouter();
  const { q, cableId, bearerId } = query;
  const { enqueueSnackbar } = useSnackbar();
  const { isAdmin } = useModuleRoleContext();
  const { user } = useAuthContext();
  const dispatch = useDispatch();

  const [cableList, setCableList] = useState([]);

  // Table
  const [tableData, setTableData] = useState([]);

  const bodyCellStyle = (() => 'text-center py-1 px-2 text-sm')();

  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) return tableData;
    return tableData.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);

  const [dialogData, setDialogData] = useState({});

  // Delete Cable Dialog States
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }
    setDialogOpen(true);
  };

  const schema = yup.object({
    bearer_name: yup.string().required('Please provide bearer name.'),
    rb_ref: yup.string().required('Please provide RB Ref.'),
    partner: yup.string().required('Please provide partner.'),
  });

  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }

      try {
        let response;
        switch (action) {
          case 'put':
            response = await axios.put(`${CAPRY_ENDPOINT}/offnet/bearer/${payload?.id}`, payload);
            break;
          default:
            break;
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    setDialogData({});
    setDialogOpen(false);
    fetchData();
  };

  // Delete Cable Handler
  const handleDeleteCable = async () => {
    if (!cableId) {
      enqueueSnackbar('Cable ID not found', { variant: 'error' });
      return;
    }

    setDeleteLoading(true);
    try {
      const response = await axios.delete(`https://simidigital.tm.com.my/api/capry/v1/offnet/cable/${cableId}`);

      if (response?.data?.status === 'success') {
        enqueueSnackbar('Cable deleted successfully', { variant: 'success' });

        // Create history entry
        await handleCreateHistory('Cable deleted.');

        // Redirect to parent page after successful deletion
        push('/capry/inventory');
      } else {
        enqueueSnackbar(response?.data?.message || 'Failed to delete cable', { variant: 'error' });
      }
    } catch (error) {
      console.error('Delete cable error:', error);

      // Enhanced error handling with specific error messages
      let errorMessage = 'Failed to delete cable';
      if (error.response?.status === 404) {
        errorMessage = 'Cable not found';
      } else if (error.response?.status === 403) {
        errorMessage = 'Permission denied - insufficient privileges';
      } else if (error.response?.status >= 500) {
        errorMessage = 'Server error occurred - please try again later';
      } else if (!error.response) {
        errorMessage = 'Network connection error - please check your connection';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }

      enqueueSnackbar(errorMessage, { variant: 'error' });
    } finally {
      setDeleteLoading(false);
      setDeleteDialogOpen(false);
    }
  };

  // Create history entry helper function
  const handleCreateHistory = async (description) => {
    try {
      await axios.post(`${CAPRY_ENDPOINT}/history`, {
        cable_id: cableId,
        description,
        action_by: user?.name,
      });
    } catch (error) {
      // console.log(error);
    }
  };

  // Others

  const headers = (() => {
    const temp = filteredTableData?.[0];
    if (!temp) return [];
    const temp2 = Object?.keys(temp)?.filter(
      (o) => !['id', 'cable_id', 'bearer_id', 'submarine_cable_map_website_code'].includes(o)
    );
    return temp2?.map((o) => ({ label: o, key: o }));
  })();

  const fetchCableList = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/offnet/cable/all/keyword`);

      if (response?.data?.data) {
        setCableList(response?.data?.data);
      }
    } catch (error) {
      //   console.log(error);
    }
    dispatch(setIsLoading(false));
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/offnet/bearer/cable_id/${cableId}`);
      setTableData(response?.data?.data || []);
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (cableId) {
      fetchData();
      fetchCableList();
    }
  }, [cableId]);

  return (
    <>
      <div className="p-6">
        {/* Header Section */}
        <div className="flex flex-col gap-4 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                {cableList?.find((o) => o?.id === cableId)?.name}
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Manage bearers and their associated paths
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3">
              <ExportExcelButton
                key={filteredTableData?.length}
                headers={headers}
                data={filteredTableData}
                buttonClassname="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Export
              </ExportExcelButton>

              {isAdmin && cableId && (
                <button
                  type="button"
                  className="inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 hover:-translate-y-0.5 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 shadow-sm hover:shadow-md"
                  onClick={() => setDeleteDialogOpen(true)}
                  disabled={deleteLoading}
                >
                  {deleteLoading ? (
                    <>
                      <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Deleting...
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                      Delete Cable
                    </>
                  )}
                </button>
              )}
            </div>
          </div>

          {/* Search Bar */}
          <div className="max-w-md">
            <SearchInput placeholder="Search bearers..." className="w-full" />
          </div>
        </div>

        {/* Table Section */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-12">

                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Bearer Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    RB Ref
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Partner
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-16">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredTableData.map((row, i) => (
                  <Fragment key={i}>
                    <tr
                      className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors group"
                      onClick={() => {
                        if (bearerId === row?.id) {
                          setParam({ bearerId: '' });
                        } else {
                          setParam({ bearerId: row?.id });
                        }
                      }}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <ArrowForwardIos
                          className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${
                            bearerId === row?.id ? 'rotate-90' : ''
                          }`}
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                          {row?.bearer_name}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {row?.rb_ref}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {row?.partner}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">
                        {isAdmin && (
                          <button
                            type="button"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleClickOpenDialog(true, row);
                            }}
                            className="inline-flex items-center p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                        )}
                      </td>
                    </tr>
                    {bearerId === row?.id && (
                      <tr>
                        <td colSpan={5} className="p-0 bg-gray-50 dark:bg-gray-700">
                          <div className="border-t border-gray-200 dark:border-gray-600">
                            <PathRow />
                          </div>
                        </td>
                      </tr>
                    )}
                  </Fragment>
                ))}
              </tbody>
            </table>
          </div>

          {/* Empty State */}
          {filteredTableData.length === 0 && (
            <div className="text-center py-12">
              <div className="w-12 h-12 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-1">No bearers found</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">Try adjusting your search criteria</p>
            </div>
          )}
        </div>
      </div>

      {/* Delete Cable Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => !deleteLoading && setDeleteDialogOpen(false)}
        maxWidth="sm"
        fullWidth
        disableEscapeKeyDown={deleteLoading}
        PaperProps={{
          className: "rounded-xl shadow-2xl border-0 overflow-hidden",
          style: {
            background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',
            padding: 0,
          }
        }}
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: '12px',
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          }
        }}
      >
        <DialogTitle className="p-0">
          <div className="flex items-center justify-between p-6 pb-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-white">Delete Cable</h2>
                <p className="text-red-100 text-sm mt-1">This action cannot be undone</p>
              </div>
            </div>
            <button
              onClick={() => !deleteLoading && setDeleteDialogOpen(false)}
              disabled={deleteLoading}
              className={`w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-200 backdrop-blur-sm ${
                deleteLoading
                  ? 'bg-white/10 cursor-not-allowed opacity-50'
                  : 'bg-white/20 hover:bg-white/30 hover:scale-105'
              }`}
              aria-label="Close dialog"
            >
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </DialogTitle>

        <DialogContent className="px-6 pb-4">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg className="w-4 h-4 text-yellow-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div className="flex-1">
                <p className="text-white font-medium text-sm mb-2">
                  Are you sure you want to delete this cable?
                </p>
                <div className="text-red-100 text-sm space-y-1">
                  <p><strong>Cable:</strong> {cableList?.find((o) => o?.id === cableId)?.name || 'Unknown'}</p>
                  <p><strong>Bearers:</strong> {filteredTableData?.length || 0} bearer(s) will be affected</p>
                </div>
                <div className="mt-3 p-3 bg-red-900/30 rounded-lg border border-red-500/30">
                  <p className="text-red-100 text-xs">
                    <strong>Warning:</strong> This will permanently delete the cable and all associated bearers, paths, and data.
                    This action cannot be undone and may affect related reservations.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>

        <DialogActions className="px-6 pb-6 pt-2">
          <div className="flex justify-end space-x-3 w-full">
            <button
              type="button"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={deleteLoading}
              className={`px-4 py-2.5 text-sm font-medium border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-white/50 transition-all duration-200 ${
                deleteLoading
                  ? 'text-white/50 cursor-not-allowed'
                  : 'text-white hover:bg-white/10 hover:border-white/50'
              }`}
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleDeleteCable}
              disabled={deleteLoading}
              className={`px-6 py-2.5 text-sm font-medium text-white border border-transparent rounded-lg focus:outline-none focus:ring-2 focus:ring-red-300 focus:ring-offset-2 transition-all duration-200 flex items-center space-x-2 ${
                deleteLoading
                  ? 'bg-red-800 cursor-not-allowed opacity-75'
                  : 'bg-red-700 hover:bg-red-800 hover:shadow-lg hover:-translate-y-0.5'
              }`}
            >
              {deleteLoading ? (
                <>
                  <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Deleting...</span>
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                  <span>Delete Cable</span>
                </>
              )}
            </button>
          </div>
        </DialogActions>
      </Dialog>

      <Dialog
        open={dialogOpen}
        onClose={() => handleDialogClose(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          className: "rounded-lg"
        }}
      >
        <DialogTitle className="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
          <div className="flex items-center">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Edit Bearer</h3>
          </div>
        </DialogTitle>
        <DialogContent className="bg-white dark:bg-gray-800">
          <div className="py-6 space-y-4">
            <TextInput
              name="bearer_name"
              value={dialogData?.bearer_name}
              placeholder="Bearer Name"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="partner"
              value={dialogData?.partner}
              placeholder="Partner"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="rb_ref"
              value={dialogData?.rb_ref}
              placeholder="RB Ref"
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700 px-6 py-4">
          <div className="flex justify-end gap-3 w-full">
            <button
              type="button"
              onClick={() => handleDialogClose(false)}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={() => handleDialogClose('put')}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
            >
              Save Changes
            </button>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default BearerView;
