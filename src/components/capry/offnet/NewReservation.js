// Next, React, Tw
import { useState, useEffect } from 'react';
import { twMerge } from 'tailwind-merge';
import { useSelector, useDispatch } from 'react-redux';
import { useRouter } from 'next/router';
import moment from 'moment';
import { useAuthContext } from '../../../utils/auth/useAuthContext';

// Mui
import { Search } from '@mui/icons-material';

// Others
import { CAPRY_ENDPOINT } from '../../../utils/capry';
import axios from '../../../utils/axios';
import { setReservationData } from '../../../utils/store/capryReducer';
import { setIsLoading } from '../../../utils/store/loadingReducer';

const NewReservation = () => {
  // Standard and Vars
  const dispatch = useDispatch();
  const { query, push } = useRouter();
  const { user } = useAuthContext();
  const { reservationData } = useSelector((state) => state.capry);

  // Table
  const bodyCellStyle = 'text-center py-1 px-4 text-xs';
  
  // Status validation
  const invalidStatuses = ['Dropped', 'Closed', 'Lost'];

  // Others
  const [sfaId, setSfaId] = useState(null);
  const [sfaData, setSfaData] = useState(null);

  // Set initial reservation data from URL parameters
  useEffect(() => {
    if (query?.pathId && query?.cableId && query?.bearerId && !reservationData?.path_id_array) {
      dispatch(
        setReservationData({
          path_id_array: [query.pathId],
          cable_id: query.cableId,
          bearer_id: query.bearerId
        })
      );
    }
  }, [query, reservationData?.path_id_array]);

  // Reset local state when reservationData changes or component mounts
  useEffect(() => {
    if (!reservationData?.salesforce_id) {
      setSfaId(null);
      setSfaData(null);
    }
  }, [reservationData]);

  const fetchSfaData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.post(
        `${CAPRY_ENDPOINT}/order_opportunity/opportunity_id/${sfaId}`
      );
      setSfaData(response?.data?.data?.[0] || null);
    } catch {
      setSfaData(null);
    }
    dispatch(setIsLoading(false));
  };

  return (
    <>
      <form
        className="flex items-center justify-center gap-2"
        onSubmit={(event) => {
          event?.preventDefault();
          fetchSfaData();
        }}
      >
        <div className="relative h-10 w-[400px]">
          <input
            className={twMerge(
              `peer h-full w-full rounded-lg border bg-transparent  bg-white px-3 py-2.5 text-xs outline outline-0 transition-all placeholder-shown:border focus:border-2 focus:border-[#ff7b7b] focus:border-t-transparent focus:outline-0 disabled:border-2 disabled:border-t-transparent disabled:outline-0`,
              `${!['', null]?.includes(sfaId) ? 'border-t-transparent' : ''}`
            )}
            value={sfaId}
            onChange={(event) => setSfaId(event?.target?.value)}
            placeholder=" "
            autoComplete="off"
          />
          <p
            className={`before:content[' '] after:content[' '] pointer-events-none absolute -top-1.5 left-0 flex h-full w-full select-none text-xs font-normal leading-tight transition-all before:pointer-events-none before:mr-1 before:mt-[6.5px] before:box-border before:block before:h-1.5 before:w-2.5 before:rounded-tl-md before:border-l before:border-t before:transition-all after:pointer-events-none after:ml-1 after:mt-[6.5px] after:box-border after:block after:h-1.5 after:w-2.5 after:flex-grow after:rounded-tr-md after:border-r after:border-t after:transition-all peer-placeholder-shown:text-sm peer-placeholder-shown:leading-[3.75] peer-placeholder-shown:before:border-transparent peer-placeholder-shown:after:border-transparent peer-focus:text-[11px] peer-focus:leading-tight peer-focus:text-[#ff7b7b] peer-focus:before:border-l-2 peer-focus:before:border-t-2 peer-focus:before:border-[#ff7b7b] peer-focus:after:border-r-2 peer-focus:after:border-t-2 peer-focus:after:border-[#ff7b7b]`}
          >
            SFA ID
          </p>
        </div>
        <button type="submit" className="rounded-lg bg-white px-3 py-2.5 text-xs">
          <Search className="h-[20px]" /> Search
        </button>
      </form>
      {sfaData && (
        <div
          style={{ boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)' }}
          className="flex flex-col gap-4 rounded-xl bg-white p-4 dark:bg-gray-600 dark:text-white"
        >
          <div className="flex w-full flex-col gap-2 items-end">
            {invalidStatuses.includes(sfaData?.status) && (
              <div className="text-red-500 text-sm font-medium">
                Cannot proceed with status "{sfaData?.status}". Only active opportunities can be reserved.
              </div>
            )}
            <button
              type="button"
              className={`rounded-lg px-4 py-2 text-white ${
                invalidStatuses.includes(sfaData?.status)
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-[#ff6602]'
              }`}
              disabled={invalidStatuses.includes(sfaData?.status)}
              onClick={() => {
                if (!invalidStatuses.includes(sfaData?.status)) {
                  // First ensure we have initial data
                  const existingData = {
                    ...reservationData,
                    order_type: 'offnet',
                    salesforce_id: sfaId,
                    path_id_array: reservationData?.path_id_array || (query?.pathId ? [query?.pathId] : []),
                    cable_id: reservationData?.cable_id || query?.cableId || null,
                    bearer_id: reservationData?.bearer_id || query?.bearerId || null,
                    reserved_by_staff_name: user?.name,
                    reserved_by_email: user?.email,
                    reserved_date: moment().format('YYYY-MM-DD'),
                    status: 'open',
                    renew_no: 0
                  };
                  dispatch(setReservationData(existingData));
                  
                  // Navigate after data is set
                  push('/capry/reservation/new');
                }
              }}
            >
              Continue Reservation
            </button>
          </div>
          <div className="overflow-x-auto scrollbar">
            <table className="min-w-full bg-white text-black">
              <thead>
                <tr>
                  {[
                    'SFA ID',
                    'Description',
                    'Customer',
                    'Sales PIC',
                    'PO Date',
                    'Sales Type',
                    'Status',
                  ].map((label, i) => (
                    <td key={i} className="bg-capry whitespace-nowrap px-4 text-center text-white">
                      {label}
                    </td>
                  ))}
                </tr>
              </thead>
              <tbody>
                <tr className="cursor-pointer border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8]">
                  {[
                    'opportunity_id',
                    'description',
                    'customer_name',
                    'owner',
                    'po_date',
                    'type',
                    'status',
                  ]?.map((o, i) => (
                    <td key={i} className={bodyCellStyle}>
                      {sfaData?.[o]}
                    </td>
                  ))}
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      )}
    </>
  );
};

export default NewReservation;
