// Next, React, Tw
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';
import { twMerge } from 'tailwind-merge';

// Packages
import * as R from 'ramda';

// Components
import { SearchInput, SelectInput } from '../../Shared/CustomInput';
import ExportExcelButton from '../../Shared/ExportExcelButton';

// Others
import axios from '../../../utils/axios';
import {
  CAPRY_ENDPOINT,
  getStatusStyle,
  getReservationAging,
  getReservationAgingFontColor,
} from '../../../utils/capry';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { useParamContext } from '../../../utils/auth/ParamProvider';

const AllReservationView = () => {
  // Standard
  const dispatch = useDispatch();
  const { push, query } = useRouter();
  const { q, category, status } = query;
  const { setParam, replaceParam } = useParamContext();

  // Table
  const [tableData, setTableData] = useState([]);

  const getBodyCellStyle = () => 'text-center py-1 text-xs';

  const filteredTableData = (() => {
    let temp = tableData;
    if (status !== 'all') temp = temp?.filter((o) => o?.status === status);
    if ([undefined, '']?.includes(q)) return temp;
    return temp.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  // Others

  const options = (() => R.uniq(R.pluck('status', tableData)))();

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/reservation/all/keyword`);
      if (response?.data?.data) {
        setTableData(response?.data?.data?.filter((o) => o?.order_type === category));
      }
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (!status) replaceParam({ status: 'all' });
  }, [status]);

  useEffect(() => {
    if (category) fetchData();
  }, [category]);

  return (
    <div className="flex flex-col gap-8">
      <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
        <div className="flex flex-col items-center gap-2 md:flex-row">
          <div className="w-[200px]">
            <SelectInput
              value={status}
              placeholder="Status"
              options={['all', ...options]}
              onChange={(event) => setParam({ status: event?.target?.value })}
            />
          </div>
          <SearchInput />
        </div>

        <ExportExcelButton key={filteredTableData?.length} data={filteredTableData} />
      </div>
      <div className="flex justify-end">
        <button
          type="button"
          className=" bg-capry cta-btn"
          onClick={() => push('/capry/reservation/new')}
        >
          New
        </button>
      </div>
      <div className="overflow-x-auto scrollbar">
        <table className="min-w-full bg-white text-black">
          <thead>
            <tr>
              {[
                'Salesforce ID',
                'Reserved By',
                'Reserved Date',
                'Aging',
                'Cycle No.',
                'Status',
              ].map((label, i) => (
                <td key={i} className="bg-capry whitespace-nowrap px-4 text-center text-white">
                  {label}
                </td>
              ))}
            </tr>
          </thead>
          <tbody>
            {filteredTableData.map((row, i) => (
              <tr
                key={i}
                className="cursor-pointer border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-200"
                onClick={() => push(`/capry/reservation/${row?.id}`)}
              >
                <td className={getBodyCellStyle()}>{row?.salesforce_id}</td>
                <td className={getBodyCellStyle()}>{row?.reserved_by_staff_name}</td>
                <td className={getBodyCellStyle()}>{row?.reserved_date}</td>
                <td
                  className={twMerge(
                    getBodyCellStyle(),
                    getReservationAgingFontColor(getReservationAging(row?.reserved_date))
                  )}
                >
                  {getReservationAging(row?.reserved_date)}
                </td>
                <td className={getBodyCellStyle()}>{row?.renew_no + 1}</td>
                <td className={getBodyCellStyle()}>{getStatusStyle(row?.status)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default AllReservationView;
