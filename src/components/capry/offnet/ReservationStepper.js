// Next, React, Tw
import { useRouter } from 'next/router';
import { useSelector } from 'react-redux';

// Mui
import { Stepper, Step, StepLabel } from '@mui/material';

const ReservationStepper = () => {
  // Standard
  const { query } = useRouter();
  const { reservationId } = query;
  const { reservationData } = useSelector((state) => state.capry);
  
  // Get status from Redux store instead of URL params for reliability
  const reservationStatus = reservationData?.status;

  // Others
  const stepNumber = (() => {
    if (reservationId === 'new') {
      if (['', undefined]?.includes(reservationData?.salesforce_id)) {
        return 0; // Select SFA ID
      }
      return 1; // Capacity Reservation
    }
    
    // Check the actual status from Redux store
    // Explicitly prioritize 'open' status regardless of path status
    if (reservationStatus === 'open' || reservationData?.status === 'open') {
      return 1; // Capacity Reservation (after creation) - using 1 for 0-based index
    }
    if (['provisioning', 'reserved']?.includes(reservationStatus)) {
      return 2; // Order Provisioning - using 2 for 0-based index
    }
    if (reservationStatus === 'completed') {
      return 3; // Activated - using 3 for 0-based index
    }
    
    // Default to Capacity Reservation if status is unknown
    return 1; // Default to Capacity Reservation - using 1 for 0-based index
  })();

  return (
    <Stepper activeStep={stepNumber} alternativeLabel>
      {['Select SFA ID', 'Capacity Reservation', 'Order Provisioning', 'Activated'].map(
        (label, i) => (
          <Step
            key={i}
            sx={{
              '& .MuiStepLabel-root .Mui-completed': {
                color: '#ff6602',
              },
              '& .MuiStepLabel-root .Mui-active': {
                color: '#ff6602',
              },
            }}
          >
            <StepLabel>
              <p className="font-semibold text-black">{label}</p>
            </StepLabel>
          </Step>
        )
      )}
    </Stepper>
  );
};

export default ReservationStepper;
