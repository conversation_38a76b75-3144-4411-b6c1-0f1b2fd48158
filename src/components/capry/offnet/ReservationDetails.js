// Next, React, Tw
import { Fragment, useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import { useSelector, useDispatch } from 'react-redux';
import { twMerge } from 'tailwind-merge';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions, Divider } from '@mui/material';
import { Remove } from '@mui/icons-material';

// Packages
import * as yup from 'yup';
import moment from 'moment';

// Components
import PathDetails from './PathDetails';
import AttachmentBox from '../../Shared/AttachmentBox';
import { SelectInput, TextAreaInput } from '../../Shared/CustomInput';
import HistoryBox from '../../Shared/HistoryBox';

// Others
import {
  CAPRY_ENDPOINT,
  getStatusStyle,
  getReservablePathStatusArray,
  getReservationAging,
  getReservationAgingFontColor,
} from '../../../utils/capry';
import axios from '../../../utils/axios';
import { useAuthContext } from '../../../utils/auth/useAuthContext';
import { useModuleRoleContext } from '../../../utils/auth/ModuleRoleProvider';
import { useSnackbar } from '../../Shared/snackbar';
import {
  checkAndReplaceNumberWithZero,
  checkAndReplaceStringWithHyphen,
} from '../../../utils/shared';
import { setReservationData } from '../../../utils/store/capryReducer';
import { setIsLoading } from '../../../utils/store/loadingReducer';

const ReservationDetails = () => {
  const { query, push } = useRouter();
  const { reservationId } = query;
  const { user } = useAuthContext();
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const { reservationData } = useSelector((state) => state.capry);
  const { isAdmin } = useModuleRoleContext();
  const hiddenDivRef = useRef(null);

  const [availablePathList, setAvailablePathList] = useState([]);
  const [attachmentList, setAttachmentList] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialog2Open, setDialog2Open] = useState(false);
  const [pathRefreshTrigger, setPathRefreshTrigger] = useState(0);

  const bodyCellStyle = 'text-center py-1 px-4 text-xs';

  const schema = yup.object({
    cancellation_reason: yup.string(),
    order_type: yup.string().required('Please provide order type.')?.default('offnet'),
    path_id_array: yup
      .array()
      .of(yup.string().required('Please provide a valid path ID.'))
      .required('Please provide month.'),
    reserved_by_staff_name: yup.string().required('Please provide name.')?.default(user?.name),
    reserved_by_email: yup.string().required('Please provide staff email.')?.default(user?.email),
    reserved_date: yup
      .string()
      .required('Please provide reserved date.')
      ?.default(moment().format('YYYY-MM-DD')),
    salesforce_id: yup.string().required('Please provide salesforce ID.'),
    status: yup.string().required('Please provide status.')?.default('open'),
    renew_no: yup?.number()?.required('Please provide renew no.')?.default(0),
  });

  const updatePath = async (newStatus = 'reserved') => {
    try {
      // First update all paths
      await Promise.all(
        reservationData?.path_id_array?.map(pathId =>
          axios.put(`${CAPRY_ENDPOINT}/offnet/path/reservation/${pathId}`, {
            status: newStatus,
            salesforce_id: newStatus === 'available' ? null : reservationData?.salesforce_id,
          })
        )
      );
      // Then trigger refresh
      setPathRefreshTrigger(prev => prev + 1);
    } catch (error) {
      // Handle error silently
    }
  };

  // Function to handle path status update when cancelling
  const updatePathStatusOnCancel = async () => {
    await updatePath('available');
  };

  const handleCreateHistory = async (reservation_id, description) => {
    try {
      await axios.post(`${CAPRY_ENDPOINT}/history`, {
        reservation_id,
        description,
      });
    } catch (error) {
      // console.log(error);
    }
  };

  const handleFormSubmit = async (action) => {
    if (action) {
      // Additional validation for new reservations
      if (action === 'post' && (!reservationData?.path_id_array || reservationData.path_id_array.length === 0)) {
        enqueueSnackbar('Please add at least one path to create a reservation.', {
          variant: 'error',
        });
        return;
      }

      let payload;
      try {
        payload = await schema.validate(reservationData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }
      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${CAPRY_ENDPOINT}/reservation`, payload);
            break;
          case 'put':
            response = await axios.put(`${CAPRY_ENDPOINT}/reservation/${reservationId}`, payload);
            break;
          default:
            break;
        }

        if (response?.data?.status === 'success') {
          const localReservationId = response.data.data?.split(' ')[2];
          
          // Handle path status updates based on action
          if (action === 'post') {
            // For new reservations - set to reserved
            await updatePath('reserved');
            await handleCreateHistory(localReservationId, 'Reservation created.');
            push(`/capry/reservation/${localReservationId}`);
          } else if (action === 'put') {
            if (reservationData?.status === 'cancelled') {
              // For cancellation - set to available
              await updatePathStatusOnCancel();
              await handleCreateHistory(localReservationId, 'Reservation cancelled. Paths set to available.');
              
              // Create a small delay to ensure the history API has updated
              setTimeout(() => {
                // Force history refresh by triggering a DOM event that HistoryBox can listen to
                if (hiddenDivRef.current) {
                  const refreshEvent = new CustomEvent('refreshHistory');
                  hiddenDivRef.current.dispatchEvent(refreshEvent);
                }
              }, 500);
              
              push(`/capry/reservation/${reservationId}`);
            } else if (reservationData?.status === 'open') {
              // For renewal - update reservation with new renewal info
              await handleCreateHistory(reservationId, 'Reservation renewed.');
              
              // Update current reservation with renewal info
              const renewalResponse = await axios.put(`${CAPRY_ENDPOINT}/reservation/${reservationId}`, {
                ...reservationData,
                reserved_date: moment().format('YYYY-MM-DD'),
                renew_no: (reservationData?.renew_no || 0) + 1,
                status: 'open'
              });

              if (renewalResponse?.data?.status === 'success') {
                // Refresh the page to show updated info
                fetchReservationData();
                
                // Create a small delay to ensure the history API has updated
                setTimeout(() => {
                  // Force history refresh by triggering a DOM event that HistoryBox can listen to
                  if (hiddenDivRef.current) {
                    const refreshEvent = new CustomEvent('refreshHistory');
                    hiddenDivRef.current.dispatchEvent(refreshEvent);
                  }
                }, 500);
                
                // Stay on the same page
                push(`/capry/reservation/${reservationId}`);
              }
            }
          }
        }

        enqueueSnackbar(response.data.status === 'success' ? response.data.data : 'Failed', {
          variant: response.data.status === 'success' ? 'success' : 'error',
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    fetchReservationData();
    setDialogOpen(false);
  };

  const fetchReservationData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/reservation/id/${reservationId}`);
      if (response?.data?.data) {
        dispatch(
          setReservationData({
            ...response?.data?.data[0],
            aging: getReservationAging(response?.data?.data[0]?.reserved_date)
          })
        );
      }
    } catch (error) {
      // Handle error silently
    }
    dispatch(setIsLoading(false));
  };

  const fetchAttachment = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/file/reservation/${reservationId}`);
      setAttachmentList(response?.data?.data || []);
    } catch {
      setAttachmentList([]);
    }
    dispatch(setIsLoading(false));
  };

  const fetchAvailablePath = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/offnet/path/all/keyword`);
      if (response?.data?.data) {
        const temp = response?.data?.data?.filter((o) =>
          getReservablePathStatusArray()?.includes(o?.status)
        );
        setAvailablePathList(temp);
      }
    } catch {
      /* empty */
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (!reservationId) return;
    if (reservationId === 'new') {
      fetchAvailablePath();
    } else {
      fetchReservationData();
      fetchAvailablePath();
      fetchAttachment();
    }
  }, [reservationId]);

  useEffect(() => {
    if (reservationId === 'new') {
      const initialData = {
        ...reservationData,
        order_type: 'offnet',
        reserved_by_staff_name: user?.name,
        reserved_by_email: user?.email,
        reserved_date: moment().format('YYYY-MM-DD'),
        status: 'open',
        renew_no: 0,
      };
      console.log('Setting initial data:', initialData);
      dispatch(setReservationData(initialData));
    }
  }, [reservationId, user]);

  return (
    <>
      <div ref={hiddenDivRef} />
      <div className="container mx-auto flex flex-col gap-4 p-1 md:p-4">
        <div className="flex w-full justify-end">
          <div className="flex flex-col items-end gap-2">
            {reservationId === 'new' && reservationData?.path_id_array?.length === 0 && (
              <p className="text-sm text-gray-500 italic">
                Add at least one path to enable reservation
              </p>
            )}
            <div className="flex gap-2">
              {reservationId === 'new' && reservationData?.path_id_array?.length > 0 && (
                <button
                  type="button"
                  className="rounded-lg bg-[#ff6602] px-4 py-2 text-white hover:bg-[#e55a02] transition-colors duration-200"
                  onClick={() => handleFormSubmit('post')}
                >
                  Reserve
                </button>
              )}
            {reservationId !== 'new' &&
              (isAdmin || reservationData?.reserved_by_staff_name === user?.name) &&
              reservationData?.status !== 'cancelled' && (
                <button
                  type="button"
                  className="rounded-lg bg-red-500 px-4 py-2 text-white"
                  onClick={() => {
                    dispatch(
                      setReservationData({
                        ...reservationData,
                        status: 'cancelled',
                      })
                    );
                    setDialogOpen(true);
                  }}
                >
                  Cancel Reservation
                </button>
              )}
            {reservationId !== 'new' &&
              reservationData?.reserved_by_staff_name === user?.name &&
              attachmentList?.length > 0 &&
              reservationData?.status !== 'cancelled' &&
              (reservationData?.renew_no || 0) < 1 && (
                <button
                  type="button"
                  className="rounded-lg bg-[#ff6602] px-4 py-2 text-white"
                  onClick={() => {
                    dispatch(
                      setReservationData({
                        ...reservationData,
                        status: 'open',
                      })
                    );
                    setDialogOpen(true);
                  }}
                >
                  Renew Reservation
                </button>
              )}
            </div>
          </div>
        </div>

        {reservationData && (
          <div
            style={{ boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.5)' }}
            className="flex flex-col gap-4 rounded-xl bg-white p-4 dark:bg-gray-600 dark:text-white"
          >
            {reservationData?.cancellation_reason && reservationData.cancellation_reason !== '' && (
              <p>Cancellation Reason : {reservationData?.cancellation_reason}</p>
            )}
            <div className="overflow-x-auto scrollbar">
              <table className="min-w-full bg-white text-black">
                <thead>
                  <tr>
                    {['Salesforce ID', 'Reserved By', 'Reserved Date', 'Aging', 'Status'].map(
                      (label, i) => (
                        <td
                          key={i}
                          className="bg-capry whitespace-nowrap px-4 text-center text-white"
                        >
                          {label}
                        </td>
                      )
                    )}
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8]">
                    <td className={bodyCellStyle}>{reservationData?.salesforce_id}</td>
                    <td className={bodyCellStyle}>{reservationData?.reserved_by_staff_name}</td>
                    <td className={bodyCellStyle}>
                      {checkAndReplaceStringWithHyphen(reservationData?.reserved_date)}
                    </td>
                    <td
                      className={twMerge(
                        bodyCellStyle,
                        getReservationAgingFontColor(reservationData?.aging)
                      )}
                    >
                      {checkAndReplaceNumberWithZero(reservationData?.aging)}
                    </td>
                    <td className={bodyCellStyle}>{getStatusStyle(reservationData?.status)}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        )}

        <Divider className="font-semibold">To-Be-Reserved Path</Divider>
        {reservationId === 'new' && (
          <div className="flex w-full justify-end">
            <button
              type="button"
              className="rounded-lg bg-[#ff6602] px-4 py-2 text-white"
              onClick={() => setDialog2Open(true)}
            >
              Add Other Path
            </button>
          </div>
        )}

        {reservationData?.path_id_array?.map((o, i) => (
          <Fragment key={i}>
            <p className="text-lg">Path {i + 1}</p>
            <PathDetails key={i} pathId={o} forceRefresh={pathRefreshTrigger} />
            {i !== reservationData.path_id_array.length - 1 && <Divider />}
          </Fragment>
        ))}

        {reservationId !== 'new' && (
          <div className="flex flex-col gap-4 md:flex-row">
            <div className="flex w-full flex-col gap-4 rounded-xl bg-white p-4 md:w-1/2">
              <p className="text-lg font-bold">Attachment</p>
              <AttachmentBox
                GET_ALL_FILES_ENDPOINT={`${CAPRY_ENDPOINT}/file/reservation/${reservationId}`}
                UPLOAD_CERTAIN_FILE_ENDPOINT={`${CAPRY_ENDPOINT}/file/upload/reservation/${reservationId}`}
                DOWNLOAD_CERTAIN_FILE_ENDPOINT={`${CAPRY_ENDPOINT}/file/download`}
                DELETE_CERTAIN_FILE_ENDPOINT={`${CAPRY_ENDPOINT}/file`}
                DISABLE_UPLOAD={reservationData?.reserved_by_staff_name !== user?.name}
                CALLBACK_UPON_SUCCESSFUL_UPLOAD={fetchAttachment}
              />
            </div>
            <div className="flex w-full flex-col gap-4 rounded-xl bg-white p-4 md:w-1/2">
              <HistoryBox
                key={reservationId}
                parentHiddenDivRef={hiddenDivRef}
                GET_ALL_HISTORIES_ENDPOINT={`${CAPRY_ENDPOINT}/history/reservation_id/${reservationId}`}
              />
            </div>
          </div>
        )}

        <Dialog open={dialogOpen} onClose={() => handleFormSubmit(false)}>
          <DialogTitle className="bg-capry text-center text-white">
            {reservationData?.status === 'cancelled' ? 'Cancel Reservation' : 'Renew Reservation'}
          </DialogTitle>
          <DialogContent>
            <div className="flex w-full flex-col gap-1 px-2 py-4 md:w-[400px]">
              {reservationData?.status === 'cancelled' && (
                <TextAreaInput
                  value={reservationData?.cancellation_reason}
                  placeholder="Cancellation Reason"
                  onChange={(event) =>
                    dispatch(
                      setReservationData({
                        ...reservationData,
                        cancellation_reason: event?.target?.value,
                      })
                    )
                  }
                />
              )}
              {reservationData?.status === 'open' && <>Proceed?</>}
            </div>
          </DialogContent>
          <DialogActions>
            <div className="flex w-full justify-between gap-4">
              <button type="button" onClick={() => handleFormSubmit(false)} className="p-2">
                Cancel
              </button>
              {((reservationData?.status === 'open') || 
                (reservationData?.status === 'cancelled' && Boolean(reservationData?.cancellation_reason))) && (
                <div className="flex gap-2">
                  <button
                    type="button"
                    onClick={() => handleFormSubmit('put')}
                    className="bg-capry rounded-[4px] p-2 font-semibold text-white"
                  >
                    Yes
                  </button>
                </div>
              )}
            </div>
          </DialogActions>
        </Dialog>

        <Dialog open={dialog2Open} onClose={() => setDialog2Open(false)}>
          <DialogTitle className="text-center">To-Be-Reserved Path</DialogTitle>
          <DialogContent>
            <div className="flex w-[400px] flex-col gap-4">
              <div>
                {reservationData?.path_id_array?.map((o, i) => (
                  <div key={i} className="flex items-center justify-end gap-4">
                    <p key={i} className="flex-grow">
                      {availablePathList?.find((p) => p?.id === o)?.path_name}
                    </p>
                    <button
                      type="button"
                      onClick={() => {
                        const temp = [...reservationData.path_id_array];
                        temp.splice(i, 1);
                        dispatch(
                          setReservationData({
                            ...reservationData,
                            path_id_array: temp,
                          })
                        );
                      }}
                    >
                      <Remove className="text-capry h-[20px]" />
                    </button>
                  </div>
                ))}
              </div>
              <Divider />
              <SelectInput
                onChange={(event) => {
                  if (reservationData?.path_id_array) {
                    dispatch(
                      setReservationData({
                        ...reservationData,
                        path_id_array: [...reservationData.path_id_array, event?.target?.value],
                      })
                    );
                    event.target.value = undefined;
                    return;
                  }
                  dispatch(
                    setReservationData({
                      ...reservationData,
                      path_id_array: [event?.target?.value],
                    })
                  );
                }}
                options={availablePathList?.map((o) => ({
                  label: o?.path_name,
                  value: o?.id,
                }))}
              />
            </div>
          </DialogContent>
          <DialogActions>
            <div className="flex w-full justify-between gap-4">
              <button type="button" onClick={() => setDialog2Open(false)} className="p-2">
                Cancel
              </button>
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={() => setDialog2Open(false)}
                  className="bg-capry rounded-[4px] p-2 font-semibold text-white"
                >
                  Close
                </button>
              </div>
            </div>
          </DialogActions>
        </Dialog>
      </div>
    </>
  );
};

export default ReservationDetails;
