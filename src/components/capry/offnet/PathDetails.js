// Next, React, Tw
import { useState, useEffect, useRef, useCallback } from 'react';
import { useRouter } from 'next/router';
import { twMerge } from 'tailwind-merge';
import { useSelector, useDispatch } from 'react-redux';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions, Divider } from '@mui/material';
import { ArrowForward, ArrowForwardIos } from '@mui/icons-material';

// Packages
import * as yup from 'yup';
import PropTypes from 'prop-types';
import moment from 'moment';

// Components
import AttachmentBox from '../../Shared/AttachmentBox';
import { TextInput, SelectInput, AutoCompleteTextInput } from '../../Shared/CustomInput';
import HistoryBox from '../../Shared/HistoryBox';
import UserPopup from '../../Shared/UserPopup';

// Others
import {
  CAPRY_ENDPOINT,
  getStatusCircle,
  getTypeComponent,
  getReservablePathStatusArray,
  getReserverRole,
} from '../../../utils/capry';
import axios from '../../../utils/axios';
import { useSnackbar } from '../../Shared/snackbar';
import { useModuleRoleContext } from '../../../utils/auth/ModuleRoleProvider';
import { checkAndReplaceStringWithHyphen } from '../../../utils/shared';
import { setReservationData } from '../../../utils/store/capryReducer';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { useAuthContext } from '../../../utils/auth/useAuthContext';

const PathDetails = ({ pathId, forceRefresh }) => {
  // Standard and Vars
  const { asPath, push } = useRouter();
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const { isAdmin, userModuleRole } = useModuleRoleContext();
  const { user } = useAuthContext();

  const hiddenDivRef = useRef(null);
  const { allStaffs } = useSelector((state) => state.aum);

  const [cableData, setCableData] = useState(null);
  const [bearerData, setBearerData] = useState(null);
  const [pathData, setPathData] = useState(null);
  const [sfaData, setSfaData] = useState(null);

  // Remove collapsible state - always show all sections for single-page layout
  // const [showGeneralSfaSection, setShowGeneralSfaSection] = useState(false);
  // const [showRmsStockFormSection, setShowRmsStockFormSection] = useState(false);
  // const [showPartnerCustomerSection, setShowPartnerCustomerSection] = useState(false);
  // const [showContractFinanceSection, setShowContractFinanceSection] = useState(false);
  // const [showHistorySection, setShowHistorySection] = useState(false);

  const [stockFormIsAvailable, setStockFormIsAvailable] = useState(false);

  const PATH_LABEL_KEY_LIST = [
    {
      label: 'Path name',
      key: 'path_name',
    },
    {
      label: 'SFA ID',
      key: 'salesforce_id',
    },
    {
      label: 'SOFCA ID',
      key: 'sofca_id',
    },
    {
      label: 'Service No.',
      key: 'service_no',
    },
    {
      label: 'Landing point A',
      key: 'landing_point_a',
    },
    {
      label: 'Landing point B',
      key: 'landing_point_b',
    },
    {
      label: 'Total Capacity',
      key: 'total_capacity',
    },
    {
      label: 'Total Available',
      key: 'total_available',
    },
  ];
  const GENERAL_LABEL_KEY_LIST = [
    {
      label: 'OCM / BOC',
      key: 'ocm_boc',
    },
    {
      label: 'Date',
      key: 'date',
    },
  ];

  // Helper function to format dates for display
  const formatDateForDisplay = (dateValue) => {
    if (!dateValue || dateValue === '' || dateValue === null || dateValue === undefined) {
      return '-';
    }

    try {
      // Handle different date formats that might come from API
      const date = moment(dateValue);
      if (!date.isValid()) {
        return '-';
      }
      return date.format('DD/MM/YYYY');
    } catch (error) {
      console.error('Error formatting date:', dateValue, error);
      return '-';
    }
  };
  const CONTRACT_LABEL_KEY_LIST = [
    {
      label: 'Contract Term (Years)',
      key: 'contract_term_years',
    },
    {
      label: 'Contract Remaining (Years)',
      key: 'contract_remaining_years',
    },
    {
      label: 'Currency',
      key: 'currency',
    },
    {
      label: 'IRU OTC',
      key: 'iru_otc',
    },
    {
      label: 'OTC',
      key: 'otc',
    },
    {
      label: 'O&M %',
      key: 'om_percentage',
    },
    {
      label: 'Annual Recurring Charge',
      key: 'annual_recurring_charge',
    },
    {
      label: 'Total Contract',
      key: 'total_contract',
    },
  ];
  const PARTNER_LABEL_KEY_LIST = [
    {
      label: 'TM PO Ref. No.',
      key: 'tm_po_ref_no',
    },
    {
      label: 'Prime Ref. ID',
      key: 'prime_ref_id',
    },
    {
      label: 'Partner Service Ref. No.',
      key: 'partner_service_ref_no',
    },

    {
      label: 'SCN/Handover Ref. No.',
      key: 'scn_handover_ref_no',
    },
    {
      label: 'Partner Circuit ID',
      key: 'partner_circuit_id',
    },
    {
      label: 'Invoice No.',
      key: 'invoice_no',
    },
    {
      label: 'PO Date',
      key: 'po_date',
    },

    {
      label: 'Handover Date',
      key: 'handover_date',
    },
    {
      label: 'Bill Start Date',
      key: 'bill_start_date',
    },
    {
      label: 'Invoice Date',
      key: 'invoice_date',
    },
  ];

  const CUSTOMER_LABEL_KEY_LIST = [
    {
      label: 'Customer Name',
      key: 'customer_name',
    },
    {
      label: 'Product Type',
      key: 'product_type',
    },
    {
      label: 'Account No.',
      key: 'account_no',
    },
  ];

  const FINANCE_LABEL_KEY_LIST = [
    {
      label: 'Asset No.',
      key: 'asset_no',
    },
    {
      label: 'Original Asset',
      key: 'original_asset',
    },
    {
      label: 'Project Definition',
      key: 'project_definition',
    },
    {
      label: 'First Acquisition On',
      key: 'first_acquisition_on',
    },
    {
      label: 'Capitalized On',
      key: 'capitalized_on',
    },
  ];

  const RMS_LABEL_KEY_LIST = [
    {
      label: 'Selling Price Currency',
      key: 'selling_price_currency',
    },
    {
      label: 'Selling Price',
      key: 'selling_price',
    },
  ];

  // Table
  const getBodyCellStyle = () => 'text-center py-1 px-4 text-xs';

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogLoading, setDialogLoading] = useState(false);

  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }
    setDialogOpen(true);
  };

  const schema = yup.object({
    // Basic
    cable_id: yup.string().required('Please provide Asset.'),
    bearer_id: yup.string().required('Please provide Asset.'),
    path_name: yup.string(),
    salesforce_id: yup.string(),
    sofca_id: yup.string(),
    service_no: yup.string(),
    status: yup.string(),
    landing_point_a: yup.string(),
    landing_point_b: yup.string(),
    total_capacity: yup.number().transform((value) => Number(value)),
    total_available: yup.number().transform((value) => Number(value)),
    type: yup.string(),
    // General
    ocm_boc: yup.string(),
    date: yup.string(),
    service_start_date: yup
      .string()
      .nullable(),
    service_end_date: yup
      .string()
      .nullable(),
    // Contract
    contract_term_years: yup.string(),
    currency: yup.string(),
    iru_otc: yup.string(),
    otc: yup.string(),
    om_percentage: yup.string(),
    annual_recurring_charge: yup.string(),
    total_contract: yup.string(),
    sales_pic: yup.string(),
    // Partner
    tm_po_ref_no: yup.string(),
    prime_ref_id: yup.string(),
    partner_service_ref_no: yup.string(),
    scn_handover_ref_no: yup.string(),
    partner_circuit_id: yup.string(),
    invoice_no: yup.string(),
    po_date: yup.string(),
    handover_date: yup.string(),
    bill_start_date: yup.string(),
    invoice_date: yup.string(),
    // Customer
    customer_name: yup.string(),
    product_type: yup.string(),
    account_no: yup.string(),
    // Finance
    asset_no: yup.string(),
    original_asset: yup.string(),
    project_definition: yup.string(),
    first_acquisition_on: yup.string(),
    capitalized_on: yup.string(),
    // RMS
    selling_price_currency: yup.string(),
    selling_price: yup.number().transform((value) => Number(value)),
  });

  const handleDialogClose = async (action) => {
    if (action) {
      setDialogLoading(true);
      let payload;
      try {
        payload = await schema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        setDialogLoading(false);
        // Enhanced error handling with specific field validation messages
        const errorMessages = Array.isArray(error.errors) ? error.errors : [error.message];
        errorMessages.forEach(msg => {
          enqueueSnackbar(msg, { variant: 'error' });
        });
        return;
      }

      // Ensure a valid SFA ID is input, upon changing status to booked
      if (payload?.status === 'booked' && (!payload?.salesforce_id || payload?.salesforce_id.trim() === '')) {
        setDialogLoading(false);
        enqueueSnackbar('Please input a valid SFA ID when changing status to booked.', {
          variant: 'error',
        });
        return;
      }

      try {
        let response;
        switch (action) {
          case 'put':
            response = await axios.put(`${CAPRY_ENDPOINT}/offnet/path/${payload?.id}`, payload);
            break;
          default:
            break;
        }

        // Next Actions on Success
        if (response?.data?.status === 'success') {
          switch (action) {
            case 'put':
              await handleCreateHistory('Path updated.');
              break;
            default:
              break;
          }
        }

        let statusVariant;
        let message;
        if (response?.data?.status === 'success') {
          statusVariant = 'success';
          message = response.data.data || 'Path updated successfully';
        } else {
          statusVariant = 'error';
          message = response?.data?.message || 'Update failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch (error) {
        console.error('Dialog save error:', error);

        // Enhanced error handling with network/server error distinction
        let errorMessage = 'Failed to update path';
        if (error.response?.status === 404) {
          errorMessage = 'Path not found';
        } else if (error.response?.status === 403) {
          errorMessage = 'Permission denied';
        } else if (error.response?.status >= 500) {
          errorMessage = 'Server error occurred';
        } else if (!error.response) {
          errorMessage = 'Network connection error';
        }

        enqueueSnackbar(errorMessage, {
          variant: 'error',
        });
      } finally {
        setDialogLoading(false);
      }
    }
    setDialogData({});
    setDialogOpen(false);
    fetchPathData();
  };

  const handleCreateHistory = async (description) => {
    try {
      await axios.post(`${CAPRY_ENDPOINT}/history`, {
        path_id: pathId,
        description,
        action_by: user?.name,
      });
    } catch (error) {
      // console.log(error);
    }
  };

  // Others

  const currentDirectory = asPath?.split('?')[0]?.split('/')[2];

  const fetchPathData = useCallback(async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/offnet/path/id/${pathId}`);

      if (response?.data?.data?.[0]) {
        const handoverDate = moment(response?.data?.data[0]?.handover_date);
        const termEndDate = handoverDate
          ?.clone()
          .add(Number(response?.data?.data[0]?.contract_term_years), 'years');
        const today = moment();

        // Setting Contract Remaining in Years
        response.data.data[0].contract_remaining_years = termEndDate
          .diff(today, 'years', true)
          .toFixed(1);

        // Setting Aging in Days
        response.data.data[0].aging = 0;
        if (response?.data?.data[0]?.status === 'available') {
          let temp = handoverDate;
          if (response.data.data[0]?.last_status_changed_date !== '') {
            temp = moment(response?.data?.data[0]?.last_status_changed_date);
          }
          response.data.data[0].aging = today.diff(temp, 'days', true).toFixed(0);
        }
        setPathData(response?.data?.data[0]);
      }
    } catch {
      setPathData(null);
    }
    dispatch(setIsLoading(false));
  }, [pathId, dispatch]);

  const fetchCableData = useCallback(async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/offnet/cable/id/${pathData?.cable_id}`);

      setCableData(response?.data?.data?.[0] || null);
    } catch (error) {
      setCableData(null);
    }
    dispatch(setIsLoading(false));
  }, [pathData?.cable_id, dispatch]);

  const fetchBearerData = useCallback(async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/offnet/bearer/id/${pathData?.bearer_id}`);

      setBearerData(response?.data?.data?.[0] || null);
    } catch {
      setBearerData(null);
    }
    dispatch(setIsLoading(false));
  }, [pathData?.bearer_id, dispatch]);

  const fetchSfaData = useCallback(async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.post(
        `${CAPRY_ENDPOINT}/order_opportunity/opportunity_id/${pathData?.salesforce_id?.trim()}`
      );
      setSfaData(response?.data?.data?.[0] || null);
    } catch (error) {
      setSfaData(null);
    }
    dispatch(setIsLoading(false));
  }, [pathData?.salesforce_id, dispatch]);

  const fetchStockFormData = useCallback(async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/file/stock-form/${pathId}`);

      if (response?.data?.data) {
        setStockFormIsAvailable(true);
      }
    } catch (error) {
      setStockFormIsAvailable(false);
    }
    dispatch(setIsLoading(false));
  }, [pathId, dispatch]);

  useEffect(() => {
    if (pathId) {
      fetchPathData();
      fetchStockFormData();
    }
  }, [pathId, forceRefresh, fetchPathData, fetchStockFormData]);

  // Force refresh path data when status changes
  useEffect(() => {
    if (pathData?.status === 'reserved') {
      fetchPathData();
    }
  }, [pathData?.status, fetchPathData]);

  useEffect(() => {
    if (pathData) {
      fetchCableData();
      fetchBearerData();
      fetchSfaData();
    }
  }, [pathData?.id, fetchCableData, fetchBearerData, fetchSfaData]);

  return (
    <>
      <div className="p-6">
        <div ref={hiddenDivRef} />

        {/* Header Section */}
        <div className="flex flex-col gap-4 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                {cableData?.name || 'Path Details'}
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Bearer: {bearerData?.bearer_name} • Path: {pathData?.path_name}
              </p>
              <div className="flex items-center text-gray-700 dark:text-gray-300 mt-2">
                <span className="font-medium">{pathData?.landing_point_a}</span>
                <ArrowForward className="mx-2 text-gray-400" fontSize="small" />
                <span className="font-medium">{pathData?.landing_point_b}</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3">
              {currentDirectory === 'inventory' && isAdmin && (
                <button
                  type="button"
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  onClick={() => handleClickOpenDialog(true, pathData)}
                >
                  Edit Path
                </button>
              )}
              {currentDirectory === 'inventory' &&
                getReservablePathStatusArray()?.includes(pathData?.status) &&
                getReserverRole()?.includes(userModuleRole) && (
                  <button
                    type="button"
                    className="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                    onClick={() => {
                      console.log('Selected path data:', pathData);
                      const reservationPayload = {
                        path_id_array: [pathData?.id],
                        cable_id: pathData?.cable_id,
                        bearer_id: pathData?.bearer_id,
                        salesforce_id: undefined,
                        order_type: 'offnet',
                        status: 'open',
                        renew_no: 0
                      };
                      console.log('Setting reservation data:', reservationPayload);
                      dispatch(setReservationData(reservationPayload));
                      push(`/capry/reservation/new?pathId=${pathData?.id}&cableId=${pathData?.cable_id}&bearerId=${pathData?.bearer_id}`);
                    }}
                  >
                    Reserve
                  </button>
                )}
            </div>
          </div>

          {/* Status Overview */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
            <div className="grid grid-cols-4 gap-4 text-center">
              <div className="space-y-1">
                <div className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Status</div>
                <div className="flex justify-center">
                  {getStatusCircle(pathData?.status)}
                </div>
              </div>
              <div className="space-y-1">
                <div className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Capacity</div>
                <div className="text-sm font-semibold text-gray-900 dark:text-white">{pathData?.total_capacity} Gbps</div>
              </div>
              <div className="space-y-1">
                <div className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Category</div>
                <div className="text-sm font-semibold text-gray-900 dark:text-white">OFFNET</div>
              </div>
              <div className="space-y-1">
                <div className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Type</div>
                <div className="flex justify-center">
                  {getTypeComponent(pathData?.type)}
                </div>
              </div>
            </div>
          </div>
        </div>
        {/* General Information */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
            General Information
          </h3>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead className="sr-only">
                <tr>
                  <th scope="col" className="text-left">Field</th>
                  <th scope="col" className="text-left">Value</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-100 dark:divide-gray-600">
                {[
                  { label: 'SERVICE NO', key: 'service_no' },
                  { label: 'SERVICE START DATE', key: 'service_start_date'},
                  { label: 'SERVICE END DATE', key: 'service_end_date'},
                  { label: 'OCM/BOC', key: 'ocm_boc' },
                  { label: 'Date', key: 'date' },
                ].map((item, index) => (
                  <tr key={index} className="group hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                    <td className="py-2 pr-4 text-sm font-semibold text-gray-700 dark:text-gray-300 align-top w-1/3 min-w-0">
                      <span className="block truncate" title={item.label}>
                        {item.label}
                      </span>
                    </td>
                    <td className="py-2 pl-4 w-2/3 min-w-0">
                      <span
                        className="inline-block text-sm text-gray-900 dark:text-white font-medium cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded-md transition-all duration-200 hover:shadow-sm text-left w-full"
                        onClick={() => {
                          const value = item.isDate
                            ? formatDateForDisplay(pathData?.[item.key])
                            : pathData?.[item.key];
                          if (value && value !== '-') {
                            navigator.clipboard.writeText(value);
                            enqueueSnackbar(`${item.label} copied to clipboard`, { variant: 'success' });
                          }
                        }}
                        title="Click to copy"
                        role="button"
                        tabIndex={0}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            const value = item.isDate
                              ? formatDateForDisplay(pathData?.[item.key])
                              : pathData?.[item.key];
                            if (value && value !== '-') {
                              navigator.clipboard.writeText(value);
                              enqueueSnackbar(`${item.label} copied to clipboard`, { variant: 'success' });
                            }
                          }
                        }}
                      >
                        {item.isDate
                          ? formatDateForDisplay(pathData?.[item.key])
                          : checkAndReplaceStringWithHyphen(pathData?.[item.key])
                        }
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* SFA Information */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
            SFA Information
          </h3>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead className="sr-only">
                <tr>
                  <th scope="col" className="text-left">Field</th>
                  <th scope="col" className="text-left">Value</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-100 dark:divide-gray-600">
                {[
                  { label: 'SFA ID', key: 'opportunity_id', dataSource: 'sfaData' },
                  { label: 'Description', key: 'description', dataSource: 'sfaData' },
                  { label: 'Customer', key: 'customer_name', dataSource: 'sfaData' },
                  { label: 'Sales PIC', key: 'owner', dataSource: 'sfaData' },
                  { label: 'PO Date', key: 'po_date', dataSource: 'sfaData' },
                  { label: 'Sales Type', key: 'type', dataSource: 'sfaData' },
                  { label: 'Status', key: 'status', dataSource: 'sfaData' },
                  { label: 'SOFCA ID', key: 'sofca_id', dataSource: 'pathData' },
                ].map((item, index) => (
                  <tr key={index} className="group hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                    <td className="py-2 pr-4 text-sm font-semibold text-gray-700 dark:text-gray-300 align-top w-1/3 min-w-0">
                      <span className="block truncate" title={item.label}>
                        {item.label}
                      </span>
                    </td>
                    <td className="py-2 pl-4 w-2/3 min-w-0">
                      <span
                        className="inline-block text-sm text-gray-900 dark:text-white font-medium cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded-md transition-all duration-200 hover:shadow-sm text-left w-full"
                        onClick={() => {
                          const value = checkAndReplaceStringWithHyphen(
                            item.dataSource === 'sfaData' ? sfaData?.[item.key] : pathData?.[item.key]
                          );
                          if (value && value !== '-') {
                            navigator.clipboard.writeText(value);
                            enqueueSnackbar(`${item.label} copied to clipboard`, { variant: 'success' });
                          }
                        }}
                        title="Click to copy"
                        role="button"
                        tabIndex={0}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            const value = checkAndReplaceStringWithHyphen(
                              item.dataSource === 'sfaData' ? sfaData?.[item.key] : pathData?.[item.key]
                            );
                            if (value && value !== '-') {
                              navigator.clipboard.writeText(value);
                              enqueueSnackbar(`${item.label} copied to clipboard`, { variant: 'success' });
                            }
                          }
                        }}
                      >
                        {checkAndReplaceStringWithHyphen(
                          item.dataSource === 'sfaData' ? sfaData?.[item.key] : pathData?.[item.key]
                        )}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Partner Information */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
            Partner Information
          </h3>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead className="sr-only">
                <tr>
                  <th scope="col" className="text-left">Field</th>
                  <th scope="col" className="text-left">Value</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-100 dark:divide-gray-600">
                {PARTNER_LABEL_KEY_LIST?.map((item, index) => (
                  <tr key={index} className="group hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                    <td className="py-2 pr-4 text-sm font-semibold text-gray-700 dark:text-gray-300 align-top w-1/3 min-w-0">
                      <span className="block truncate" title={item.label}>
                        {item.label}
                      </span>
                    </td>
                    <td className="py-2 pl-4 w-2/3 min-w-0">
                      <span
                        className="inline-block text-sm text-gray-900 dark:text-white font-medium cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded-md transition-all duration-200 hover:shadow-sm text-left w-full"
                        onClick={() => {
                          const value = checkAndReplaceStringWithHyphen(pathData?.[item.key]);
                          if (value && value !== '-') {
                            navigator.clipboard.writeText(value);
                            enqueueSnackbar(`${item.label} copied to clipboard`, { variant: 'success' });
                          }
                        }}
                        title="Click to copy"
                        role="button"
                        tabIndex={0}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            const value = checkAndReplaceStringWithHyphen(pathData?.[item.key]);
                            if (value && value !== '-') {
                              navigator.clipboard.writeText(value);
                              enqueueSnackbar(`${item.label} copied to clipboard`, { variant: 'success' });
                            }
                          }
                        }}
                      >
                        {checkAndReplaceStringWithHyphen(pathData?.[item.key])}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Customer Information */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
            Customer Information
          </h3>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead className="sr-only">
                <tr>
                  <th scope="col" className="text-left">Field</th>
                  <th scope="col" className="text-left">Value</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-100 dark:divide-gray-600">
                {CUSTOMER_LABEL_KEY_LIST?.map((item, index) => (
                  <tr key={index} className="group hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                    <td className="py-2 pr-4 text-sm font-semibold text-gray-700 dark:text-gray-300 align-top w-1/3 min-w-0">
                      <span className="block truncate" title={item.label}>
                        {item.label}
                      </span>
                    </td>
                    <td className="py-2 pl-4 w-2/3 min-w-0">
                      <span
                        className="inline-block text-sm text-gray-900 dark:text-white font-medium cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded-md transition-all duration-200 hover:shadow-sm text-left w-full"
                        onClick={() => {
                          const value = checkAndReplaceStringWithHyphen(pathData?.[item.key]);
                          if (value && value !== '-') {
                            navigator.clipboard.writeText(value);
                            enqueueSnackbar(`${item.label} copied to clipboard`, { variant: 'success' });
                          }
                        }}
                        title="Click to copy"
                        role="button"
                        tabIndex={0}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            const value = checkAndReplaceStringWithHyphen(pathData?.[item.key]);
                            if (value && value !== '-') {
                              navigator.clipboard.writeText(value);
                              enqueueSnackbar(`${item.label} copied to clipboard`, { variant: 'success' });
                            }
                          }
                        }}
                      >
                        {checkAndReplaceStringWithHyphen(pathData?.[item.key])}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Contract Information */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
            Contract Information
          </h3>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead className="sr-only">
                <tr>
                  <th scope="col" className="text-left">Field</th>
                  <th scope="col" className="text-left">Value</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-100 dark:divide-gray-600">
                {CONTRACT_LABEL_KEY_LIST?.map((item, index) => (
                  <tr key={index} className="group hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                    <td className="py-2 pr-4 text-sm font-semibold text-gray-700 dark:text-gray-300 align-top w-1/3 min-w-0">
                      <span className="block truncate" title={item.label}>
                        {item.label}
                      </span>
                    </td>
                    <td className="py-2 pl-4 w-2/3 min-w-0">
                      <span
                        className="inline-block text-sm text-gray-900 dark:text-white font-medium cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded-md transition-all duration-200 hover:shadow-sm text-left w-full"
                        onClick={() => {
                          const value = checkAndReplaceStringWithHyphen(pathData?.[item.key]);
                          if (value && value !== '-') {
                            navigator.clipboard.writeText(value);
                            enqueueSnackbar(`${item.label} copied to clipboard`, { variant: 'success' });
                          }
                        }}
                        title="Click to copy"
                        role="button"
                        tabIndex={0}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            const value = checkAndReplaceStringWithHyphen(pathData?.[item.key]);
                            if (value && value !== '-') {
                              navigator.clipboard.writeText(value);
                              enqueueSnackbar(`${item.label} copied to clipboard`, { variant: 'success' });
                            }
                          }
                        }}
                      >
                        {checkAndReplaceStringWithHyphen(pathData?.[item.key])}
                      </span>
                    </td>
                  </tr>
                ))}
                {/* Aging (Days) - Special handling */}
                <tr className="group hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                  <td className="py-2 pr-4 text-sm font-semibold text-gray-700 dark:text-gray-300 align-top w-1/3 min-w-0">
                    <span className="block truncate" title="Aging (Days)">
                      Aging (Days)
                    </span>
                  </td>
                  <td className="py-2 pl-4 w-2/3 min-w-0">
                    <span
                      className="inline-block text-sm text-gray-900 dark:text-white font-medium cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded-md transition-all duration-200 hover:shadow-sm text-left w-full"
                      onClick={() => {
                        const value = pathData?.aging;
                        if (value && value !== '-') {
                          navigator.clipboard.writeText(value.toString());
                          enqueueSnackbar('Aging (Days) copied to clipboard', { variant: 'success' });
                        }
                      }}
                      title="Click to copy"
                      role="button"
                      tabIndex={0}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          const value = pathData?.aging;
                          if (value && value !== '-') {
                            navigator.clipboard.writeText(value.toString());
                            enqueueSnackbar('Aging (Days) copied to clipboard', { variant: 'success' });
                          }
                        }
                      }}
                    >
                      {pathData?.aging}
                    </span>
                  </td>
                </tr>
                {/* Sales PIC - Special handling with UserPopup */}
                <tr className="group hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                  <td className="py-2 pr-4 text-sm font-semibold text-gray-700 dark:text-gray-300 align-top w-1/3 min-w-0">
                    <span className="block truncate" title="Sales PIC">
                      Sales PIC
                    </span>
                  </td>
                  <td className="py-2 pl-4 w-2/3 min-w-0">
                    <div className="text-sm text-gray-900 dark:text-white font-medium text-left">
                      <UserPopup
                        label={pathData?.sales_pic}
                        staff_id={allStaffs?.find((o) => o?.name === pathData?.sales_pic)?.staff_id}
                      />
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Finance Information */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
            Finance Information
          </h3>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead className="sr-only">
                <tr>
                  <th scope="col" className="text-left">Field</th>
                  <th scope="col" className="text-left">Value</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-100 dark:divide-gray-600">
                {FINANCE_LABEL_KEY_LIST?.map((item, index) => (
                  <tr key={index} className="group hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                    <td className="py-2 pr-4 text-sm font-semibold text-gray-700 dark:text-gray-300 align-top w-1/3 min-w-0">
                      <span className="block truncate" title={item.label}>
                        {item.label}
                      </span>
                    </td>
                    <td className="py-2 pl-4 w-2/3 min-w-0">
                      <span
                        className="inline-block text-sm text-gray-900 dark:text-white font-medium cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded-md transition-all duration-200 hover:shadow-sm text-left w-full"
                        onClick={() => {
                          const value = checkAndReplaceStringWithHyphen(pathData?.[item.key]);
                          if (value && value !== '-') {
                            navigator.clipboard.writeText(value);
                            enqueueSnackbar(`${item.label} copied to clipboard`, { variant: 'success' });
                          }
                        }}
                        title="Click to copy"
                        role="button"
                        tabIndex={0}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            const value = checkAndReplaceStringWithHyphen(pathData?.[item.key]);
                            if (value && value !== '-') {
                              navigator.clipboard.writeText(value);
                              enqueueSnackbar(`${item.label} copied to clipboard`, { variant: 'success' });
                            }
                          }
                        }}
                      >
                        {checkAndReplaceStringWithHyphen(pathData?.[item.key])}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* RMS Information */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
            RMS Information
          </h3>
          <div className="space-y-3">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead className="sr-only">
                  <tr>
                    <th scope="col" className="text-left">Field</th>
                    <th scope="col" className="text-left">Value</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100 dark:divide-gray-600">
                  {RMS_LABEL_KEY_LIST?.map((item, index) => (
                    <tr key={index} className="group hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                      <td className="py-2 pr-4 text-sm font-semibold text-gray-700 dark:text-gray-300 align-top w-1/3 min-w-0">
                        <span className="block truncate" title={item.label}>
                          {item.label}
                        </span>
                      </td>
                      <td className="py-2 pl-4 w-2/3 min-w-0">
                        <span
                          className="inline-block text-sm text-gray-900 dark:text-white font-medium cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded-md transition-all duration-200 hover:shadow-sm text-left w-full"
                          onClick={() => {
                            const value = checkAndReplaceStringWithHyphen(sfaData?.[item.key]);
                            if (value && value !== '-') {
                              navigator.clipboard.writeText(value);
                              enqueueSnackbar(`${item.label} copied to clipboard`, { variant: 'success' });
                            }
                          }}
                          title="Click to copy"
                          role="button"
                          tabIndex={0}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                              e.preventDefault();
                              const value = checkAndReplaceStringWithHyphen(sfaData?.[item.key]);
                              if (value && value !== '-') {
                                navigator.clipboard.writeText(value);
                                enqueueSnackbar(`${item.label} copied to clipboard`, { variant: 'success' });
                              }
                            }
                          }}
                        >
                          {checkAndReplaceStringWithHyphen(sfaData?.[item.key])}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">RMS Files</h4>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                <AttachmentBox
                  key={pathId}
                  GET_ALL_FILES_ENDPOINT={`${CAPRY_ENDPOINT}/file/rms/${pathId}`}
                  UPLOAD_CERTAIN_FILE_ENDPOINT={`${CAPRY_ENDPOINT}/file/upload/rms/${pathId}`}
                  DOWNLOAD_CERTAIN_FILE_ENDPOINT={`${CAPRY_ENDPOINT}/file/download`}
                  DELETE_CERTAIN_FILE_ENDPOINT={`${CAPRY_ENDPOINT}/file`}
                  DISABLE_UPLOAD={!isAdmin}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Stock Form Information */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
            Stock Form
          </h3>
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
            <AttachmentBox
              key={pathId}
              GET_ALL_FILES_ENDPOINT={`${CAPRY_ENDPOINT}/file/stock-form/${pathId}`}
              UPLOAD_CERTAIN_FILE_ENDPOINT={`${CAPRY_ENDPOINT}/file/upload/stock-form/${pathId}`}
              DOWNLOAD_CERTAIN_FILE_ENDPOINT={`${CAPRY_ENDPOINT}/file/download`}
              DELETE_CERTAIN_FILE_ENDPOINT={`${CAPRY_ENDPOINT}/file`}
              DISABLE_UPLOAD={!isAdmin}
              CALLBACK_UPON_SUCCESSFUL_UPLOAD={() => {
                if (window) window?.location?.reload();
              }}
              CALLBACK_UPON_SUCCESSFUL_DELETE={() => {
                if (window) window?.location?.reload();
              }}
            />
          </div>
        </div>

        {/* History Section */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
            History
          </h3>
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
            <HistoryBox
              key={pathId}
              parentHiddenDivRef={hiddenDivRef}
              GET_ALL_HISTORIES_ENDPOINT={`${CAPRY_ENDPOINT}/history/path_id/${pathId}`}
            />
          </div>
        </div>
      </div>

    {/* Enhanced Edit Dialog with Notion-like Design */}
    <Dialog
        open={dialogOpen}
        onClose={() => !dialogLoading && handleDialogClose(false)}
        maxWidth="lg"
        fullWidth
        disableEscapeKeyDown={dialogLoading}
        PaperProps={{
          className: "rounded-xl shadow-2xl border-0 overflow-hidden",
          style: {
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            padding: 0,
          }
        }}
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: '12px',
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          }
        }}
        aria-labelledby="edit-path-dialog-title"
        aria-describedby="edit-path-dialog-description"
      >
        {/* Enhanced Header with Gradient Background */}
        <DialogTitle
          id="edit-path-dialog-title"
          className="relative bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 text-white p-6 border-0"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-white">Edit Path Information</h2>
                <p id="edit-path-dialog-description" className="text-blue-100 text-sm mt-1">Update path details and configuration</p>
              </div>
            </div>
            <button
              onClick={() => !dialogLoading && handleDialogClose(false)}
              disabled={dialogLoading}
              className={`w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-200 backdrop-blur-sm ${
                dialogLoading
                  ? 'bg-white/10 cursor-not-allowed opacity-50'
                  : 'bg-white/20 hover:bg-white/30 hover:scale-105'
              }`}
              aria-label="Close dialog"
            >
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </DialogTitle>

        {/* Enhanced Content with Better Spacing and Layout */}
        <DialogContent className="bg-gray-50 dark:bg-gray-900 p-0">
          <div className="max-h-[70vh] overflow-y-auto">
            <div className="p-8 space-y-8">
              {/* Loading Overlay */}
              {dialogLoading && (
                <div className="absolute inset-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm z-50 flex items-center justify-center">
                  <div className="flex flex-col items-center space-y-4">
                    <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Saving changes...</p>
                  </div>
                </div>
              )}
              {/* Status Configuration Section - Only for draft/booked paths */}
              {['draft', 'booked']?.includes(pathData?.status) && (
                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300">
                  <div className="flex items-center space-x-3 mb-6">
                    <div className="w-8 h-8 bg-amber-100 dark:bg-amber-900/30 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Status Configuration</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Available for draft and booked paths only</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Path Status</label>
                      <SelectInput
                        name="status"
                        value={dialogData?.status}
                        placeholder="Status"
                        options={(() => {
                          const temp = ['draft', 'booked'];
                          if (stockFormIsAvailable) temp?.push('available');
                          return temp;
                        })()}
                        onChange={handleDialogDataChange}
                      />
                    </div>
                    {stockFormIsAvailable && (
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Path Type</label>
                        <SelectInput
                          name="type"
                          value={dialogData?.type}
                          placeholder="Type"
                          options={['asset', 'jop', 'stock']}
                          onChange={handleDialogDataChange}
                        />
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Basic Information Section */}
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Basic Information</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Core path identification and capacity details</p>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {PATH_LABEL_KEY_LIST?.map((item, index) => (
                    <div key={index} className="space-y-2">
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center space-x-1">
                        <span>{item?.label}</span>
                        {['path_name', 'total_capacity'].includes(item?.key) && (
                          <span className="text-red-500">*</span>
                        )}
                      </label>
                      <div className="relative group">
                        <TextInput
                          name={item?.key}
                          value={dialogData?.[item?.key]}
                          placeholder={item?.label}
                          onChange={handleDialogDataChange}
                          showRedAsteric={false}
                        />
                        {/* Click to copy functionality for key fields */}
                        {dialogData?.[item?.key] && ['path_name', 'service_no'].includes(item?.key) && (
                          <button
                            type="button"
                            onClick={() => {
                              navigator.clipboard.writeText(dialogData[item?.key]);
                              enqueueSnackbar(`${item?.label} copied to clipboard`, { variant: 'success' });
                            }}
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                            title="Click to copy"
                          >
                            <svg className="w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* General Information Section */}
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">General Information</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Additional operational details</p>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {GENERAL_LABEL_KEY_LIST?.map((item, index) => (
                    <div key={index} className="space-y-2">
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">{item?.label}</label>
                      <TextInput
                        name={item?.key}
                        value={dialogData?.[item?.key]}
                        placeholder={item?.label}
                        onChange={handleDialogDataChange}
                        showRedAsteric={false}
                      />
                    </div>
                  ))}
                  {/* Service Date Fields */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Service Start Date</label>
                    <TextInput
                      name="service_start_date"
                      value={dialogData?.service_start_date}
                      onChange={handleDialogDataChange}
                      showRedAsteric={false}
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Service End Date</label>
                    <TextInput
                      name="service_end_date"
                      value={dialogData?.service_end_date}
                      onChange={handleDialogDataChange}
                      showRedAsteric={false}
                    />
                  </div>
                </div>
              </div>

              {/* Contract Information Section */}
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Contract Information</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Financial terms and contract details</p>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {CONTRACT_LABEL_KEY_LIST?.map((item, index) => (
                    <div key={index} className="space-y-2">
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">{item?.label}</label>
                      <TextInput
                        name={item?.key}
                        value={dialogData?.[item?.key]}
                        placeholder={item?.label}
                        onChange={handleDialogDataChange}
                        showRedAsteric={false}
                      />
                    </div>
                  ))}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Sales PIC</label>
                    <AutoCompleteTextInput
                      name="sales_pic"
                      value={dialogData?.sales_pic}
                      placeholder="Sales PIC"
                      options={allStaffs?.map((o) => o?.name)}
                      onChange={handleDialogDataChange}
                      showRedAsteric={false}
                    />
                  </div>
                </div>
              </div>

              {/* Partner Information Section */}
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Partner Information</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Partner references and service details</p>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {PARTNER_LABEL_KEY_LIST?.map((item, index) => (
                    <div key={index} className="space-y-2">
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">{item?.label}</label>
                      <div className="relative group">
                        <TextInput
                          name={item?.key}
                          value={dialogData?.[item?.key]}
                          placeholder={item?.label}
                          onChange={handleDialogDataChange}
                          showRedAsteric={false}
                        />
                        {/* Click to copy for reference numbers */}
                        {dialogData?.[item?.key] && ['tm_po_ref_no', 'prime_ref_id', 'partner_service_ref_no', 'partner_circuit_id'].includes(item?.key) && (
                          <button
                            type="button"
                            onClick={() => {
                              navigator.clipboard.writeText(dialogData[item?.key]);
                              enqueueSnackbar(`${item?.label} copied to clipboard`, { variant: 'success' });
                            }}
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                            title="Click to copy"
                          >
                            <svg className="w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Customer Information Section */}
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-8 h-8 bg-teal-100 dark:bg-teal-900/30 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-teal-600 dark:text-teal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Customer Information</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Customer details and account information</p>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {CUSTOMER_LABEL_KEY_LIST?.map((item, index) => (
                    <div key={index} className="space-y-2">
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">{item?.label}</label>
                      <div className="relative group">
                        <TextInput
                          name={item?.key}
                          value={dialogData?.[item?.key]}
                          placeholder={item?.label}
                          onChange={handleDialogDataChange}
                          showRedAsteric={false}
                        />
                        {/* Click to copy for account number */}
                        {dialogData?.[item?.key] && item?.key === 'account_no' && (
                          <button
                            type="button"
                            onClick={() => {
                              navigator.clipboard.writeText(dialogData[item?.key]);
                              enqueueSnackbar(`${item?.label} copied to clipboard`, { variant: 'success' });
                            }}
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                            title="Click to copy"
                          >
                            <svg className="w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Finance Information Section */}
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-8 h-8 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-emerald-600 dark:text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Finance Information</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Asset and financial tracking details</p>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {FINANCE_LABEL_KEY_LIST?.map((item, index) => (
                    <div key={index} className="space-y-2">
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">{item?.label}</label>
                      <div className="relative group">
                        <TextInput
                          name={item?.key}
                          value={dialogData?.[item?.key]}
                          placeholder={item?.label}
                          onChange={handleDialogDataChange}
                          showRedAsteric={false}
                        />
                        {/* Click to copy for asset numbers */}
                        {dialogData?.[item?.key] && ['asset_no', 'original_asset'].includes(item?.key) && (
                          <button
                            type="button"
                            onClick={() => {
                              navigator.clipboard.writeText(dialogData[item?.key]);
                              enqueueSnackbar(`${item?.label} copied to clipboard`, { variant: 'success' });
                            }}
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                            title="Click to copy"
                          >
                            <svg className="w-3 h-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* RMS Information Section */}
              <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-300">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">RMS Information</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Revenue management and pricing details</p>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {RMS_LABEL_KEY_LIST?.map((item, index) => (
                    <div key={index} className="space-y-2">
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">{item?.label}</label>
                      <TextInput
                        name={item?.key}
                        value={dialogData?.[item?.key]}
                        placeholder={item?.label}
                        onChange={handleDialogDataChange}
                        showRedAsteric={false}
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Form Validation Notice */}
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4">
                <div className="flex items-start space-x-3">
                  <div className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">Important Notes</h4>
                    <ul className="mt-2 text-sm text-blue-800 dark:text-blue-200 space-y-1">
                      <li>• Required fields are marked with an asterisk (*)</li>
                      <li>• SFA ID is required when changing status to "booked"</li>
                      <li>• Click on reference numbers to copy them to clipboard</li>
                      <li>• All changes will be logged in the history section</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DialogContent>

        {/* Enhanced Dialog Actions with Better Styling */}
        <DialogActions className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-6">
          <div className="flex w-full justify-between items-center">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Changes will be saved and logged automatically
            </div>
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={() => handleDialogClose(false)}
                className="px-6 py-2.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 hover:shadow-sm hover:-translate-y-0.5 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={() => handleDialogClose('put')}
                disabled={dialogLoading}
                className={`px-6 py-2.5 text-sm font-medium text-white border border-transparent rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 flex items-center space-x-2 ${
                  dialogLoading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 hover:shadow-lg hover:-translate-y-0.5'
                }`}
              >
                {dialogLoading ? (
                  <>
                    <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>Saving...</span>
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>Save Changes</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

PathDetails.propTypes = {
  pathId: PropTypes.string,
  forceRefresh: PropTypes.any,
};

export default PathDetails;
