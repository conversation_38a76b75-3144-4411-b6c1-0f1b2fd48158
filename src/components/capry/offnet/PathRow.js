// Next, React, Tw
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { twMerge } from 'tailwind-merge';
import { useDispatch } from 'react-redux';

// Others
import axios from '../../../utils/axios';
import { CAPRY_ENDPOINT, getStatusCircle, getTypeComponent } from '../../../utils/capry';
import { useParamContext } from '../../../utils/auth/ParamProvider';
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { checkAndReplaceStringWithHyphen } from '../../../utils/shared';

const PathRow = () => {
  // Standard and Vars
  const { query } = useRouter();
  const { bearerId } = query;
  const { setParam } = useParamContext();
  const dispatch = useDispatch();

  // Table
  const [tableData, setTableData] = useState([]);
  const bodyCellStyle = 'text-center py-1 text-[10px]';

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/offnet/path/bearer_id/${bearerId}`);
      setTableData(response?.data?.data || []);
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (bearerId) fetchData();
  }, []);

  return (
    <div className="p-4 bg-gray-50 dark:bg-gray-700">
      <div className="mb-3">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Paths</h4>
      </div>

      {tableData.length > 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  {['Service No.', 'Route', 'Capacity (Gbps)', 'Type', 'Status'].map((label, i) => (
                    <th
                      key={i}
                      className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      {label}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                {tableData.map((row, i) => (
                  <tr
                    key={i}
                    className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors group"
                    onClick={() => setParam({ pathId: row?.id })}
                  >
                    <td className="px-4 py-3 whitespace-nowrap text-xs text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      {checkAndReplaceStringWithHyphen(row?.service_no)}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400">
                      {`${row?.landing_point_a} - ${row?.landing_point_b}`}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400">
                      {row?.total_capacity}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-xs">
                      {getTypeComponent(row?.type)}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-xs">
                      {getStatusCircle(row?.status)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <div className="text-center py-8">
          <div className="w-10 h-10 mx-auto mb-3 bg-gray-100 dark:bg-gray-600 rounded-full flex items-center justify-center">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400">No paths found for this bearer</p>
        </div>
      )}
    </div>
  );
};

export default PathRow;
