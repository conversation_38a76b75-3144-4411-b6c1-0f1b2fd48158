// Next, React, Tw
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';

// Components
import ReactAnimatedNumber from '../Shared/ReactAnimatedNumber';

// Others
import { useParamContext } from '../../utils/auth/ParamProvider';
import axios from '../../utils/axios';
import { CAPRY_ENDPOINT } from '../../utils/capry';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { SelectInput } from '../Shared/CustomInput';

const OnMapDiv = () => {
  // Standard and Vars
  const { setParam } = useParamContext();
  const { query, push } = useRouter();
  const { cableId, cableName } = query;
  const dispatch = useDispatch();

  const [cableSystemList, setCableSystemList] = useState([]);
  const [pathList, setPathList] = useState([]);

  // Others

  const fetchAllCableSystem = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/offnet/cable/all/keyword`);
      if (response?.data?.data) {
        setCableSystemList(response?.data?.data);
      }
    } catch {
      setCableSystemList([]);
    }
    dispatch(setIsLoading(false));
  };

  const fetchPathData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/offnet/path/cable_id/${cableId}`);

      if (response?.data?.data) {
        setPathList(response?.data?.data);
      } else {
        setPathList([]);
      }
    } catch {
      setPathList([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchAllCableSystem();
  }, []);

  useEffect(() => {
    if (cableName) fetchPathData();
  }, [cableName]);

  return (
    <div className="flex w-[300px] flex-col gap-4 rounded-md bg-transparent p-4">
      <div className="flex w-full flex-col items-center gap-2 rounded-md bg-white p-4 shadow-xl">
        <p className="text-center text-xs">
          Pick a Cable to View <br /> Estimated Capacity
        </p>
        <SelectInput
          value={cableName}
          placeholder="Cable"
          onChange={(event) => {
            const temp = cableSystemList?.find((o) => o?.name === event?.target?.value);
            setParam({
              cableName: temp?.name,
              cableCode: temp?.submarine_cable_map_website_code,
              cableId: temp?.id,
            });
          }}
          options={cableSystemList?.map((o) => o?.name)}
        />
      </div>
      {pathList?.length > 0 && (
        <div className="flex w-full flex-col items-center gap-2 rounded-md bg-white p-4 shadow-xl">
          <div className="flex w-full items-center justify-between">
            <p className="text-xs">Total Capacity : </p>
            <p className="font-bold">
              <ReactAnimatedNumber
                value={pathList.reduce(
                  (accumulator, currentValue) => accumulator + currentValue.total_capacity,
                  0
                )}
                formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
              />
              &nbsp; GB
            </p>
          </div>
          <div className="flex w-full items-center justify-between">
            <p className="text-xs">Total Available : </p>
            <p className="font-bold">
              <ReactAnimatedNumber
                value={pathList
                  ?.filter((o) => !['provisioning', 'activated']?.includes(o?.status))
                  ?.reduce(
                    (accumulator, currentValue) => accumulator + currentValue.total_capacity,
                    0
                  )}
                formatValue={(n) => Intl.NumberFormat('en-US').format(n)}
              />
              &nbsp; GB
            </p>
          </div>
          <div className="flex w-full justify-center">
            <button
              type="button"
              className="text-blue-500 hover:underline"
              onClick={() => push(`/capry/inventory?category=offnet&cableId=${cableId}`)}
            >
              View Cable
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default OnMapDiv;
