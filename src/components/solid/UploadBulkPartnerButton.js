// Next, React, Tw
import { useRef } from 'react';
import { useDispatch } from 'react-redux';

// Packages
import Papa from 'papapar<PERSON>';
import * as yup from 'yup';

// Others
import axios from '../../utils/axios';
import { CAPRY_ENDPOINT } from '../../utils/capry';
import { useSnackbar } from '../Shared/snackbar';
import { setIsLoading } from '../../utils/store/loadingReducer';

const UploadBulkPartnerButton = () => {
  // Standard and Vars
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();

  const fileInputRef = useRef(null);

  // Others
  const schema = yup.object({
    region: yup.string().required('Please provide region.'),
    country: yup.string().required('Please provide country.'),
    name: yup.string().required('Please provide name.'),
    email: yup.string(),
    pic_name: yup.string(),
    pic_job_title: yup.string(),
  });

  const saveNewPartner = async (data) => {
    let payload;
    try {
      payload = await schema.validate(data, { abortEarly: false });
    } catch (error) {
      enqueueSnackbar(error.errors, {
        variant: 'error',
        anchorOrigin: {
          vertical: 'top',
          horizontal: 'center',
        },
      });
      return;
    }

    dispatch(setIsLoading(true));
    try {
      await axios.post(`${CAPRY_ENDPOINT}/solid/partner`, payload);
    } catch {
      /* empty */
    }
    dispatch(setIsLoading(false));
  };

  const processData = async (data) => {
    // Save New Partner
    for (let i = 0; i < data.length; i += 1) {
      await saveNewPartner({
        region: data[i]?.Regions?.toLowerCase(),
        country: data[i]?.['Origin country'],
        name: data[i]?.["Partner' Name"],
        email: data[i]?.Email,
        pic_name: data[i]?.Name,
        pic_job_title: data[i]?.['Job Title'],
      });
    }
  };

  return (
    <>
      <button
        type="button"
        className="bg-solid rounded-md p-1 text-sm font-semibold text-white"
        onClick={() => fileInputRef?.current?.click()}
      >
        Upload Bulk
      </button>
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={(event) => {
          const extension =
            event?.target?.files[0]?.name?.split('.')[
              event.target.files[0].name.split('.').length - 1
            ];
          if (extension !== 'csv') {
            enqueueSnackbar('Please upload a CSV.', {
              variant: 'error',
              anchorOrigin: {
                vertical: 'top',
                horizontal: 'center',
              },
            });
            return;
          }
          Papa.parse(event?.target?.files[0], {
            header: true,
            complete: (results) => processData(results?.data),
          });
        }}
      />
    </>
  );
};

export default UploadBulkPartnerButton;
