// Next, React, Tw
import { useRef } from 'react';
import { useDispatch } from 'react-redux';

// Packages
import Papa from 'papaparse';
import * as yup from 'yup';

// Others
import axios from '../../utils/axios';
import { CAPRY_ENDPOINT } from '../../utils/capry';
import { useSnackbar } from '../Shared/snackbar';
import { setIsLoading } from '../../utils/store/loadingReducer';

const UploadBulkPopClsButton = () => {
  // Standard and Vars
  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();

  const fileInputRef = useRef(null);

  // Others
  const schema = yup.object({
    country: yup.string().required('Please provide country.'),
    address: yup.string().required('Please provide address.'),
    type: yup.string().required('Please provide type.'),
  });

  const saveNewPopCls = async (data) => {
    let payload;
    try {
      payload = await schema.validate(data, { abortEarly: false });
    } catch (error) {
      enqueueSnackbar(error.errors, {
        variant: 'error',
      });
      return;
    }

    dispatch(setIsLoading(true));
    try {
      await axios.post(`${CAPRY_ENDPOINT}/solid/pop_cls`, payload);
    } catch {
      /* empty */
    }
    dispatch(setIsLoading(false));
  };

  const processData = async (data) => {
    // Save New Partner
    for (let i = 0; i < data.length; i += 1) {
      await saveNewPopCls({
        country: data[i]?.Country,
        address: data[i]?.['Data Center'],
        type: 'border station',
      });
    }
  };

  return (
    <>
      <button
        type="button"
        className="text-md bg-solid rounded-lg p-1 text-sm font-semibold text-white"
        onClick={() => fileInputRef?.current?.click()}
      >
        Upload Bulk
      </button>
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={(event) => {
          const extension =
            event?.target?.files[0]?.name?.split('.')[
              event.target.files[0].name.split('.').length - 1
            ];
          if (extension !== 'csv') {
            enqueueSnackbar('Please upload a CSV.', {
              variant: 'error',
              anchorOrigin: {
                vertical: 'top',
                horizontal: 'center',
              },
            });
            return;
          }
          Papa.parse(event?.target?.files[0], {
            header: true,
            complete: (results) => processData(results?.data),
          });
        }}
      />
    </>
  );
};

export default UploadBulkPopClsButton;
