// Next, React, Tw
import { Fragment, useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';

// Packages
import PropTypes from 'prop-types';
import moment from 'moment';

// Components
import { SearchInput } from '../Shared/CustomInput';

// Others
import axios from '../../utils/axios';
import { CAPRY_ENDPOINT } from '../../utils/capry';
import { setIsLoading } from '../../utils/store/loadingReducer';

const QuotationFromSfaTable = ({ taskRmsId }) => {
  // Standard and Vars
  const { query } = useRouter();
  const { q } = query;
  const dispatch = useDispatch();

  // Table
  const [tableData, setTableData] = useState([]);

  const bodyCellStyle = 'text-center py-1 text-xs';

  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  // Others

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/rms_line_item/${taskRmsId}`);

      if (response?.data?.status === 'success' && response?.data?.data !== null) {
        response?.data?.data?.reverse();
        setTableData(response?.data?.data);
      } else {
        setTableData([]);
      }
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (taskRmsId) {
      fetchData();
    }
  }, [taskRmsId]);

  return (
    <>
      <div className="flex items-center justify-between">
        <SearchInput />
      </div>
      <div className="overflow-x-auto scrollbar">
        <table className="min-w-full bg-white text-black">
          <thead>
            <tr>
              {['Partner', 'Cable', 'Product', 'MRC (USD)', 'OTC (USD)', 'Modified Date'].map(
                (label, i) => (
                  <td key={i} className="bg-solid whitespace-nowrap px-4 text-center text-white">
                    {label}
                  </td>
                )
              )}
            </tr>
          </thead>
          <tbody>
            {filteredTableData.map((row, i) => (
              <tr key={i} className="border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8]">
                <td className={bodyCellStyle}>{row?.partner}</td>
                <td className={bodyCellStyle}>{row?.name}</td>
                <td className={bodyCellStyle}>{row?.item_type}</td>
                <td className={bodyCellStyle}>{row?.mrc}</td>
                <td className={bodyCellStyle}>{row?.otc}</td>
                <td className={bodyCellStyle}>
                  {moment(row?.last_modified_date)?.format('DD-MM-YYYY')}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </>
  );
};

QuotationFromSfaTable.propTypes = {
  taskRmsId: PropTypes.string,
};

export default QuotationFromSfaTable;
