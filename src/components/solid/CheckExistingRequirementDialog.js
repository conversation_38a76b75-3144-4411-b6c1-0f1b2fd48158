// Next, React, Tw
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions, Divider } from '@mui/material';

// Packages
import PropTypes from 'prop-types';
import moment from 'moment';
import * as yup from 'yup';

// Components
import { TextInput, SelectInput } from '../Shared/CustomInput';

// Others
import { setRequirementData } from '../../utils/store/capryReducer';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { CAPRY_ENDPOINT } from '../../utils/capry';
import { useSnackbar } from '../Shared/snackbar';
import axios from '../../utils/axios';
import { useAuthContext } from '../../utils/auth/useAuthContext';

const CheckExistingRequirementDialog = ({ dialogOpen, setDialogOpen }) => {
  // Standard and Vars
  const { push } = useRouter();
  const dispatch = useDispatch();
  const { requirementData } = useSelector((state) => state?.capry);
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuthContext();

  const [countryList, setCountryList] = useState([]);

  // Table
  const bodyCellStyle = 'text-center py-1 text-xs';
  const [existingRequirementListFromSimi, setExistingRequirementListFromSimi] = useState([]);
  const [existingRequirementListFromSfa, setExistingRequirementListFromSfa] = useState([]);

  // Form
  const handleFormDataChange = (event) => {
    const { name, value } = event.target;
    dispatch(setRequirementData({ ...requirementData, [name]: value }));
  };
  const checkExistingRequirementSchema = yup.object({
    a_end_country: yup.string().required('Please provide A-End Country.'),
    bandwidth_times_100_gbps: yup
      .number()
      .required('Please provide bandwidth.')
      .transform((value) => Number(value)),
    b_end_country: yup.string().required('Please provide Z-End Country.'),
  });
  const checkExistingRequirement = async () => {
    let payload;
    try {
      payload = await checkExistingRequirementSchema.validate(requirementData, {
        abortEarly: false,
        stripUnknown: true,
      });
    } catch (error) {
      enqueueSnackbar(error.errors, {
        variant: 'error',
      });
      return;
    }

    dispatch(setIsLoading(true));
    try {
      const response = await axios.post(`${CAPRY_ENDPOINT}/solid/requirement/check`, payload);
      if (response?.data?.fetchSfaData) {
        setExistingRequirementListFromSimi(response?.data?.data);
      }
    } catch {
      enqueueSnackbar('Data from SIMI not found', {
        variant: 'error',
      });
      setExistingRequirementListFromSimi([]);
    }
    try {
      const response = await axios.get(
        `${CAPRY_ENDPOINT}/task_rms/${payload?.a_end_country?.toLowerCase()}/${payload?.b_end_country?.toLowerCase()}`
      );
      if (response?.data?.data) {
        let temp = response?.data?.data?.map((o) => ({
          ...o,
          bandwidth_times_100_gbps:
            o?.bandwidth_unit?.toLowerCase() === 'mbps'
              ? o?.bandwidth_number / 100000
              : o?.bandwidth_number / 100,
        }));
        if (temp?.length > 30) {
          const threeMonthsAgo = moment().subtract(3, 'months');
          temp = temp?.filter((o) => moment(o?.last_modified_date).isSameOrAfter(threeMonthsAgo));
        }
        setExistingRequirementListFromSfa(temp);
      }
    } catch {
      enqueueSnackbar('Data from SFA not found', {
        variant: 'error',
      });
      setExistingRequirementListFromSimi([]);
    }
    dispatch(setIsLoading(false));
  };

  const handleSfaDataRowClick = async (row) => {
    dispatch(setIsLoading(true));

    const payload = {
      a_end_country: row?.leg_a_country?.toLowerCase(),
      a_end_location: row?.leg_a_address,
      a_end_pop_cls: '',
      account_name: row?.customer_name,
      b_end_country: row?.leg_b_country?.toLowerCase(),
      bandwidth_times_100_gbps: row?.bandwidth_times_100_gbps,
      created_by_staff_id: user?.staff_id,
      preferred_cable_name: row?.cable,
      region: '',
      salesforce_id: row?.opportunity_id,
      task_rms_id: row?.Id,
      term: row?.contract_type,
      term_period_years: row?.contract_term,
      z_end_location: row?.leg_b_address,
      z_end_pop_cls: '',
    };

    try {
      const response = await axios.post(`${CAPRY_ENDPOINT}/solid/requirement`, payload);
      if (response?.data?.status === 'success') {
        setDialogOpen(false);
        push(`/solid/requirement/${payload?.salesforce_id}`);
      }
      let statusVariant;
      let message;
      if (response.data.status === 'success') {
        statusVariant = 'success';
        message = response.data.data;
      } else {
        statusVariant = 'error';
        message = 'Failed';
      }
      enqueueSnackbar(message, {
        variant: statusVariant,
      });
    } catch {
      enqueueSnackbar('Failed', {
        variant: 'error',
      });
    }
    dispatch(setIsLoading(false));
  };

  // Data Fetching
  const fetchCountryList = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await fetch(`/capry/country-list.json`);
      if (response?.status === 200) {
        const data = await response.json();
        setCountryList(data?.ref_country_codes);
      }
    } catch {
      setCountryList([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchCountryList();
  }, []);

  return (
    <Dialog fullScreen open={dialogOpen}>
      <DialogTitle className="text-center">Cross Check Existing Requirement</DialogTitle>
      <DialogContent>
        <div className="flex w-full flex-col justify-center gap-4 py-4">
          <div className="mx-auto flex w-1/2 items-center justify-center gap-2">
            <div className="flex w-full flex-col items-center gap-2">
              <TextInput
                name="bandwidth_times_100_gbps"
                value={requirementData?.bandwidth_times_100_gbps}
                placeholder="Bandwidth (x100 Gbps)"
                onChange={handleFormDataChange}
              />
              <SelectInput
                name="a_end_country"
                value={requirementData?.a_end_country}
                placeholder="A-End Country"
                options={countryList?.map((o, i) => o?.country)}
                onChange={handleFormDataChange}
              />
              <SelectInput
                name="b_end_country"
                value={requirementData?.b_end_country}
                placeholder="B-End Country"
                options={countryList?.map((o, i) => o?.country)}
                onChange={handleFormDataChange}
              />
            </div>
            <button
              type="button"
              onClick={() => checkExistingRequirement()}
              className="bg-solid rounded-[4px] p-1 text-sm font-semibold text-white"
            >
              Check
            </button>
          </div>
          <Divider className="text-xs">Existing from SIMI</Divider>
          {existingRequirementListFromSimi?.length > 0 && (
            <div className="mx-auto w-3/4 ">
              <div className="overflow-x-auto scrollbar">
                <table
                  className="min-w-full bg-white text-black
                  "
                >
                  <thead>
                    <tr>
                      {[
                        'SFA ID',
                        'Customer',
                        'Bandwidth (x100 Gbps)',
                        'Created by',
                        'Created Date',
                        'Status',
                      ].map((label, i) => (
                        <td
                          key={i}
                          className="whitespace-nowrap bg-[#fcfcfd] px-4 text-center text-sm text-black"
                        >
                          {label}
                        </td>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {existingRequirementListFromSimi.map((row, i) => (
                      <tr
                        key={i}
                        className="cursor-pointer border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-200"
                        onClick={() => {
                          setDialogOpen(false);
                          push(`/solid/requirement/${row?.id}`);
                        }}
                      >
                        <td className={bodyCellStyle}>{row?.salesforce_id}</td>
                        <td className={bodyCellStyle}>{row?.account_name}</td>
                        <td className={bodyCellStyle}>{row?.bandwidth_times_100_gbps}</td>
                        <td className={bodyCellStyle}>{row?.created_by_staff_id?.toUpperCase()}</td>
                        <td className={bodyCellStyle}>{row?.created_date}</td>
                        <td className={bodyCellStyle}>{row?.status}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
          <Divider className="text-xs">Existing from SFA</Divider>
          {existingRequirementListFromSfa?.length > 0 && (
            <div className="mx-auto w-3/4 ">
              <div className="overflow-x-auto scrollbar">
                <table
                  className="min-w-full bg-white text-black
                  "
                >
                  <thead>
                    <tr>
                      {['SFA ID', 'Customer', 'Bandwidth (x100 Gbps)', 'Created Date'].map(
                        (label, i) => (
                          <td
                            key={i}
                            className="whitespace-nowrap bg-[#fcfcfd] px-4 text-center text-sm text-black"
                          >
                            {label}
                          </td>
                        )
                      )}
                    </tr>
                  </thead>
                  <tbody>
                    {existingRequirementListFromSfa.map((row, i) => (
                      <tr
                        key={i}
                        className="cursor-pointer border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-200"
                        onClick={() => handleSfaDataRowClick(row)}
                      >
                        <td className={bodyCellStyle}>{row?.opportunity_id}</td>
                        <td className={bodyCellStyle}>{row?.customer_name}</td>
                        <td className={bodyCellStyle}>{row?.bandwidth_times_100_gbps}</td>
                        <td className={bodyCellStyle}>{row?.last_modified_date}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
      <DialogActions>
        <div className="flex w-full justify-between gap-4">
          <button type="button" onClick={() => push('/solid/requirement')} className="p-1 text-sm">
            Cancel
          </button>
          <div className="flex gap-2">
            <button
              type="button"
              onClick={() => {
                setDialogOpen(false);
                dispatch(setRequirementData({}));
              }}
              className="bg-solid rounded-[4px] p-1 text-sm font-semibold text-white"
            >
              Proceed New
            </button>
          </div>
        </div>
      </DialogActions>
    </Dialog>
  );
};

CheckExistingRequirementDialog.propTypes = {
  dialogOpen: PropTypes.bool,
  setDialogOpen: PropTypes.func,
};

export default CheckExistingRequirementDialog;
