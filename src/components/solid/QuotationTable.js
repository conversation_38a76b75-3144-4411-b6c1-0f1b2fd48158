// Next, React, Tw
import { Fragment, useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSelector, useDispatch } from 'react-redux';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions, Divider } from '@mui/material';
import { ArrowForwardIos, Edit } from '@mui/icons-material';

// Packages
import * as R from 'ramda';
import * as yup from 'yup';
import PropTypes from 'prop-types';
import parse from 'html-react-parser';

// Components
import AttachmentBox from '../Shared/AttachmentBox';
import SelectRemoveStaffListContainer from '../Shared/SelectRemoveStaffListContainer';
import {
  SearchInput,
  TextInput,
  BinarySwitchInput,
  TextAreaInput,
  BinaryRadioInput,
  SelectInput,
} from '../Shared/CustomInput';
import UserPopup from '../Shared/UserPopup';

// Others
import axios from '../../utils/axios';
import { CAPRY_ENDPOINT } from '../../utils/capry';
import { NOTIFICATION_ENDPOINT } from '../../utils/notification';
import { useSnackbar } from '../Shared/snackbar';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { checkAndReplaceStringWithHyphen, toUpperCaseFirstLetter } from '../../utils/shared';
import { setIsLoading } from '../../utils/store/loadingReducer';

const QuotationTable = ({ requirementId }) => {
  // Standard and Vars
  const { query } = useRouter();
  const { q, quoteId, dialogOpen } = query;
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuthContext();

  const { setParam, replaceParam } = useParamContext();
  const { requirementData } = useSelector((state) => state.capry);
  const dispatch = useDispatch();

  const [partnerList, setPartnerList] = useState([]);
  const [emailCcList, setEmailCcList] = useState([user?.email]);
  const [selectedPartnerRegion, setSelectedPartnerRegion] = useState(null);
  const [selectedPartnerCountry, setSelectedPartnerCountry] = useState(null);

  // Table
  const [tableData, setTableData] = useState([]);

  const bodyCellStyle = 'text-center py-1 text-xs';

  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  // Dialog
  const [dialog2Open, setDialog2Open] = useState(false);

  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const quoteSchema = yup.object({
    created_by: yup.string().required('Please provide created by.')?.default(user?.staff_id),
    mrc_in_usd: yup.number().transform((value) => Number(value)),
    om_in_usd: yup.number().transform((value) => Number(value)),
    om_percentage: yup.number().transform((value) => Number(value)),
    otc_in_usd: yup.number().transform((value) => Number(value)),
    preferred_cable_is_available: yup
      .bool()
      .required('Please provide whether the preferred cable is available.')
      .default(false),
    remark: yup.string(),
    requirement_id: yup.string().required('Please provide requirement ID.')?.default(requirementId),
    status: yup.string().required('Please provide status.')?.default('pending'),
    partner_id: yup.string().required('Please provide partner ID.'),
  });

  const emailSchema = yup.object({
    body: yup.string().required(`Please provide body`).default('string'),
    html_body: yup.string().required(`Please provide html body`),
    recipients: yup.array().of(yup.string()).required(`Please provide recipient`),
    cc: yup.array().of(yup.string()).default([]),
    sender: yup.string().required(`Please provide sender`).default('<EMAIL>'),
    subject: yup.string().required(`Please provide subject`),
  });

  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await quoteSchema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
        return;
      }

      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${CAPRY_ENDPOINT}/solid/quote`, payload);
            break;
          case 'put':
            response = await axios.put(`${CAPRY_ENDPOINT}/solid/quote/${payload?.id}`, payload);
            break;
          default:
            break;
        }
        if (response.data.status === 'success') {
          switch (action) {
            case 'post':
              handleSendEmail(response?.data?.data?.split(' ')?.[2]);
              break;
            default:
              break;
          }
        }
        let statusVariant;
        let message;
        if (response.data.status === 'success') {
          statusVariant = 'success';
          message = response.data.data;
        } else {
          statusVariant = 'error';
          message = 'Failed';
        }
        enqueueSnackbar(message, {
          variant: statusVariant,
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    replaceParam({ quoteId: undefined, dialogOpen: false });
    setDialog2Open(false);
  };

  const partnerName = partnerList?.find((o) => o?.id === dialogData?.partner_id)?.name || '';

  const emailTitle = `TMREQ/${requirementData.bandwidth_times_100_gbps * 100}G [${
    requirementData?.preferred_cable_name
  }] [${requirementData?.term === 'lease' ? 'Lease' : 'IRU'}] [${
    requirementData?.a_end_country
  }] [${requirementData?.b_end_country}] / [${partnerName}]`;

  const emailHtmlBody = (() => {
    const TABLE_DATA = [
      {
        label: 'Service',
        key: 'service',
      },
      {
        label: 'A-end (CLS/POP)',
        key: 'a_end_pop_cls',
      },
      {
        label: '(CLS/POP)',
        key: 'z_end_pop_cls',
      },
      {
        label: 'Interface',
        key: 'z_end_interface',
      },
      {
        label: 'Protection',
        key: 'protection',
      },
      {
        label: 'Term (Years)',
        key: 'term_period_years',
      },
      {
        label: 'Bandwidth (n x 100G)',
        key: 'bandwidth_times_100_gbps',
      },
      {
        label: 'Cable',
        key: 'preferred_cable_name',
      },
      {
        label: 'Availability / RFS',
        key: 'availability',
      },
      {
        label: 'Cable Route',
        key: 'route',
      },
      {
        label: 'RTD (ms)',
        key: 'rtd_in_ms',
      },
      {
        label: 'Quantity',
        key: 'quantity',
      },
      {
        label: 'OTC (USD) / 100G',
        key: 'otc_in_usd',
      },
      {
        label: 'MRC (USD) / 100G',
        key: 'mrc_in_usd',
      },
      {
        label: 'O&M',
        key: 'om',
      },
      {
        label: 'Remark',
        key: 'remark',
      },
    ];
    return `
    <p data-ogsb="white">
      <span data-ogsc="rgb(36, 36, 36)">
        Dear ${partnerName},
        <br /><br />
        Greetings, hope this email finds you well.
        <br /><br />
        We are working on opportunities that require capacity as below. Kindly please
        provide us with your best quotation for the requested services in the table below.
        Please also specify the cable readiness, type of offering, and any relevant remarks
        regarding the service.
        <br /> <br />
      </span>
    </p>
    <table border="2" style="width: 100%; border-collapse: collapse; border-color: #000000; background-color: #ffffff;">
      <tbody>
          ${TABLE_DATA?.reduce(
            (prev, curr) =>
              `${prev}<tr><td style="width: 200px; text-align: center;">${
                curr?.label
              }</td><td style="width: 200px; text-align: center;">${
                requirementData?.[curr?.key] || ''
              }</td> </tr>`,
            ''
          )}
      </tbody>
    </table>
    <p data-ogsb="white">
      <span data-ogsc="rgb(36, 36, 36)">
        <br /> <br />
        Please let us know if you need any additional information. We look forward to
        hearing from you. Thanks and best regards,
        <br /><br />
        ${user?.name}
        <br />
        TM Global
      </span>
    </p>`;
  })();

  const handleSendEmail = async (quoteId_) => {
    const getCode = () => quoteId_;

    let payload = {
      recipients: [partnerList?.find((o) => o?.id === dialogData?.partner_id)?.email],
      cc: emailCcList,
      subject: emailTitle,
      html_body: `<!DOCTYPE html>\n<html lang="en">\n<head>\n<meta charset="UTF-8">\n<meta name="viewport" content="width=device-width, initial-scale=1.0">\n<title>Your Page Title</title>\n</head>\n<body>\n
      ${emailHtmlBody}
      <br />
      <p>Note to requestor: Please use below code to input quotation to-be-received.
      <br /><br />
      <strong>${getCode()}</strong>
      </p>
      \n</body>\n</html>`,
    };
    try {
      payload = await emailSchema.validate(payload, { abortEarly: false });
    } catch (error) {
      enqueueSnackbar(error.errors, {
        variant: 'error',
      });
      return;
    }
    dispatch(setIsLoading(true));
    try {
      await axios.post(`${NOTIFICATION_ENDPOINT}/send_email_v2`, payload);
      enqueueSnackbar('RFQ sent successfully.', {
        variant: 'success',
      });
    } catch {
      enqueueSnackbar('Error', {
        variant: 'error',
      });
    }
    dispatch(setIsLoading(false));
  };

  // Others

  const handleOnClick = (row) => {
    if (quoteId === row?.id) {
      setParam({ quoteId: '' });
      return;
    }
    setParam({ quoteId: row?.id });
  };

  const partnerRegionList = R.uniq(R.pluck('region', partnerList));
  const partnerCountryList = R.uniq(
    R.pluck(
      'country',
      partnerList?.filter((o) => o?.region === selectedPartnerRegion)
    )
  );
  const partnerlist = partnerList?.filter(
    (o) => o?.region === selectedPartnerRegion && o?.country === selectedPartnerCountry
  );

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    let endpoint = `requirement_id/${requirementId}`;
    if (requirementId === 'all') {
      endpoint = `all/keyword`;
    }
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/solid/quote/${endpoint}`);

      if (response?.data?.data) {
        response?.data?.data?.reverse();
        setTableData(response?.data?.data);
      } else {
        setTableData([]);
      }
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  const fetchAllPartners = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${CAPRY_ENDPOINT}/solid/partner/all/keyword`);
      setPartnerList(response?.data?.data || []);
    } catch (error) {
      setPartnerList([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (requirementId !== 'new') {
      fetchData();
      fetchAllPartners();
    }
  }, [requirementId]);

  useEffect(() => {
    if (quoteId && tableData?.length > 0) setDialogData(tableData?.find((o) => o?.id === quoteId));
  }, [quoteId, tableData?.length]);

  return (
    <>
      <div className="flex items-center justify-between">
        <SearchInput />
        {requirementId !== 'all' && (
          <div className="flex gap-2">
            <button
              type="button"
              className=" bg-solid rounded-md p-1 text-white"
              onClick={() => setDialog2Open(true)}
            >
              Request New Quotation
            </button>
          </div>
        )}
      </div>
      <div className="overflow-x-auto scrollbar">
        <table className="min-w-full bg-white text-black">
          <thead>
            <tr>
              {[
                '',
                'MRC (USD)',
                'O&M (USD)',
                'O&M (%)',
                'OTC (USD)',
                'Cable Availability',
                'Status',
                'Created by',
                'Created Date',
                '',
              ].map((label, i) => (
                <td key={i} className="bg-solid whitespace-nowrap px-4 text-center text-white">
                  {label}
                </td>
              ))}
            </tr>
          </thead>
          <tbody>
            {filteredTableData.map((row, i) => (
              <Fragment key={i}>
                <tr className="cursor-pointer border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-200">
                  <td className={bodyCellStyle} onClick={() => handleOnClick(row)}>
                    <ArrowForwardIos className={`${quoteId !== row?.id ? '' : 'rotate-90'} h-3`} />
                  </td>
                  <td className={bodyCellStyle} onClick={() => handleOnClick(row)}>
                    {row?.mrc_in_usd}
                  </td>
                  <td className={bodyCellStyle} onClick={() => handleOnClick(row)}>
                    {row?.om_in_usd}
                  </td>
                  <td className={bodyCellStyle} onClick={() => handleOnClick(row)}>
                    {row?.om_percentage}
                  </td>
                  <td className={bodyCellStyle} onClick={() => handleOnClick(row)}>
                    {row?.otc_in_usd}
                  </td>
                  <td className={bodyCellStyle} onClick={() => handleOnClick(row)}>{`${
                    row?.preferred_cable_is_available ? 'Yes' : 'No'
                  }`}</td>
                  <td className={bodyCellStyle} onClick={() => handleOnClick(row)}>
                    {row?.status}
                  </td>
                  <td className={bodyCellStyle}>
                    <UserPopup label={row?.created_by?.toUpperCase()} staff_id={row?.created_by} />
                  </td>
                  <td className={bodyCellStyle} onClick={() => handleOnClick(row)}>
                    {row?.created_date}
                  </td>
                  <td />
                </tr>
                {row?.id === quoteId && (
                  <tr className="border-b border-[#fcfcfd] bg-white">
                    <td colSpan={9} className={bodyCellStyle}>
                      <div className="flex w-full items-center gap-2 p-4">
                        <div className="flex w-1/2 flex-col gap-2">
                          <Divider>Partner Info</Divider>
                          <div className="mx-auto w-full overflow-x-auto scrollbar">
                            <table className="min-w-full bg-white text-black">
                              <tbody>
                                {[
                                  {
                                    label: 'Region',
                                    key: 'region',
                                  },
                                  {
                                    label: 'Country',
                                    key: 'country',
                                  },
                                  {
                                    label: 'Name',
                                    key: 'name',
                                  },
                                  {
                                    label: 'E-Mail',
                                    key: 'email',
                                  },
                                  {
                                    label: 'PIC Name',
                                    key: 'pic_name',
                                  },
                                  {
                                    label: 'PIC Name',
                                    key: 'pic_job_title',
                                  },
                                ]?.map((o, j) => (
                                  <tr
                                    key={j}
                                    className="border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8]"
                                  >
                                    <td className={bodyCellStyle}>{o?.label}</td>
                                    <td className={bodyCellStyle}>:</td>
                                    <td className={bodyCellStyle}>
                                      {checkAndReplaceStringWithHyphen(
                                        partnerList?.find(
                                          (p) => p?.id === dialogData?.partner_id
                                        )?.[o?.key]
                                      )}
                                    </td>
                                  </tr>
                                ))}
                              </tbody>
                            </table>
                          </div>
                        </div>

                        <div className="w-1/2">
                          <AttachmentBox
                            GET_ALL_FILES_ENDPOINT={`${CAPRY_ENDPOINT}/file/quote/${row?.id}`}
                            UPLOAD_CERTAIN_FILE_ENDPOINT={`${CAPRY_ENDPOINT}/file/upload/quote/${row?.id}`}
                            DOWNLOAD_CERTAIN_FILE_ENDPOINT={`${CAPRY_ENDPOINT}/file/download`}
                            DELETE_CERTAIN_FILE_ENDPOINT={`${CAPRY_ENDPOINT}/file`}
                          />
                        </div>
                      </div>
                    </td>
                    <td className={bodyCellStyle}>
                      <div className="flex gap-2">
                        <button type="button" onClick={() => setParam({ dialogOpen: 'true' })}>
                          <Edit className="text-solid" />
                        </button>
                      </div>
                    </td>
                  </tr>
                )}
              </Fragment>
            ))}
          </tbody>
        </table>
      </div>
      <Dialog open={dialogOpen === 'true'} onClose={() => replaceParam({ dialogOpen: 'false' })}>
        <DialogTitle className="bg-solid text-center text-white">Edit Quotation</DialogTitle>
        <DialogContent>
          <div className="mt-2 flex w-full flex-col gap-2 p-2 md:w-[400px]">
            <TextInput
              name="mrc_in_usd"
              value={dialogData?.mrc_in_usd}
              onChange={handleDialogDataChange}
              placeholder="MRC (USD)"
            />
            <TextInput
              name="om_in_usd"
              value={dialogData?.om_in_usd}
              onChange={handleDialogDataChange}
              placeholder="O&M (USD)"
            />
            <TextInput
              name="om_percentage"
              value={dialogData?.om_percentage}
              onChange={handleDialogDataChange}
              placeholder="O&M (%)"
            />
            <TextInput
              name="otc_in_usd"
              value={dialogData?.otc_in_usd}
              onChange={handleDialogDataChange}
              placeholder="OTC (USD)"
            />
            <TextAreaInput
              name="remark"
              value={dialogData?.remark}
              onChange={handleDialogDataChange}
              placeholder="Remark"
            />
            <BinarySwitchInput
              name="preferred_cable_is_available"
              value={dialogData?.preferred_cable_is_available}
              onChange={handleDialogDataChange}
              placeholder="Cable is Available"
            />
            <p className="text-center">Status</p>
            <div>
              {['active', 'expired']?.map((o, i) => (
                <BinaryRadioInput
                  key={i}
                  name="status"
                  value={dialogData?.status === o}
                  onChange={() =>
                    setDialogData((prevValues) => ({
                      ...prevValues,
                      status: o,
                    }))
                  }
                  showRedAsteric={false}
                  placeholder={toUpperCaseFirstLetter(o)}
                />
              ))}
            </div>
            <AttachmentBox
              GET_ALL_FILES_ENDPOINT={`${CAPRY_ENDPOINT}/file/quote/${dialogData?.id}`}
              UPLOAD_CERTAIN_FILE_ENDPOINT={`${CAPRY_ENDPOINT}/file/upload/quote/${dialogData?.id}`}
              DOWNLOAD_CERTAIN_FILE_ENDPOINT={`${CAPRY_ENDPOINT}/file/download`}
              DELETE_CERTAIN_FILE_ENDPOINT={`${CAPRY_ENDPOINT}/file`}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-1 text-sm">
              Cancel
            </button>
            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => handleDialogClose('put')}
                className="bg-solid rounded-[4px] px-1 text-sm font-semibold text-white"
              >
                Edit
              </button>
            </div>
          </div>
        </DialogActions>
      </Dialog>
      <Dialog fullScreen open={dialog2Open} onClose={() => setDialog2Open(false)}>
        <DialogTitle className=" text-solid text-center">Request for Quotation</DialogTitle>
        <DialogContent>
          <div className="my-2 flex w-full gap-2">
            <div className="w-[200px]">
              <SelectInput
                name=""
                value={selectedPartnerRegion}
                options={partnerRegionList}
                onChange={(event) => setSelectedPartnerRegion(event.target.value)}
                placeholder=""
                defaultLabel="Select Partner Region"
              />
            </div>

            {selectedPartnerRegion && (
              <div className="w-[200px]">
                <SelectInput
                  name=""
                  value={selectedPartnerCountry}
                  onChange={(event) => setSelectedPartnerCountry(event.target.value)}
                  placeholder=""
                  options={partnerCountryList}
                  defaultLabel="Select Partner Country"
                />
              </div>
            )}
            {selectedPartnerRegion && selectedPartnerCountry && (
              <div className="w-[200px]">
                <SelectInput
                  name="partner_id"
                  value={dialogData?.partner_id}
                  onChange={handleDialogDataChange}
                  placeholder=""
                  options={partnerlist?.map((o) => ({
                    label: `${o?.name} - ${o?.pic_name}`,
                    value: o?.id,
                  }))}
                  defaultLabel="Select Partner"
                />
              </div>
            )}
          </div>
          <Divider />
          <div className="flex w-full items-center justify-center gap-1 p-2">
            <div className="mx-auto flex w-2/3 flex-col gap-4 rounded-lg border border-gray-400 p-2">
              <div className="flex items-center gap-2">
                <p className="text-xs">CC :</p>
                <div className="flex-grow ">
                  <SelectRemoveStaffListContainer
                    INITIAL_STAFF_LIST={emailCcList}
                    KEY="email"
                    CALLBACK_FUNCTION={(selectedStaffList) => {
                      setEmailCcList(R.pluck('email')(selectedStaffList));
                    }}
                  />
                </div>
              </div>
              <Divider />
              <strong>{emailTitle}</strong>
              <Divider />
              {parse(emailHtmlBody)}
            </div>
          </div>
        </DialogContent>
        <DialogActions className="bg-white">
          <div className="flex w-full justify-between gap-4">
            <button
              type="button"
              onClick={() => setDialog2Open(false)}
              className="p-1 text-sm text-black"
            >
              Cancel
            </button>
            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => handleDialogClose('post')}
                className="bg-solid rounded-[4px] p-1 text-sm font-semibold text-white"
              >
                Send RFQ
              </button>
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

QuotationTable.propTypes = {
  requirementId: PropTypes.string,
};

export default QuotationTable;
