// Next, React, Tw
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { Done, Close } from '@mui/icons-material';

// Packages
import * as yup from 'yup';

// Components
import {
  TextInput,
  SelectInput,
  BinarySwitchInput,
  AutoCompleteTextInput,
} from '../Shared/CustomInput';
import UploadCsvButton from '../Shared/UploadCsv';
import { TablePaginationCustom } from '../Shared/table';

// Others
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { ITAF_ENDPOINT } from '../../utils/itaf';
import { useSnackbar } from '../Shared/snackbar';
import {
  checkAndReplaceStringWithHyphen,
  checkAndReplaceNumberWithZero,
  toUpperCaseFirstLetter,
  TM_GLOBAL_REGIONS,
} from '../../utils/shared';

const AllFunnels = () => {
  // Standard
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { travelId } = query;
  const { travelData } = useSelector((state) => state.itaf);
  const { allStaffs } = useSelector((state) => state.aum);
  const { enqueueSnackbar } = useSnackbar();

  // Table
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(10);
  const onChangePage = (event, newPage) => {
    setPage(newPage);
  };
  const [tableData, setTableData] = useState([]);
  const bodyCellStyle = 'text-center py-1 text-sm whitespace-nowrap px-2';
  const filteredTableData = (() => tableData)();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);

  const [dialogData, setDialogData] = useState({});
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) setDialogData(data);
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const schema = yup.object({
    customer: yup.string(),
    forecasted: yup.boolean().required('Please provide customer.'),
    mrc: yup.number().required('Please provide customer.'),
    otc: yup.number().required('Please provide customer.'),
    potential_sales_deal: yup.string().required('Please provide ptential sales deal.'),
    probability_in_percentage: yup.number().required('Please provide customer.'),
    region: yup.string().required('Please provide region.'),
    revenue_to_be_recognized_in_current_year: yup.number().required('Please provide customer.'),
    rfs: yup.string().required('Please provide RFS.'),
    sales_pic_name: yup.string().required('Please provide sales PIC.'),
    status: yup.string().required('Please provide status.'),
    tcv: yup.number().required('Please provide customer.'),
    travel_id: yup.string().required('Please provide travel ID.')?.default(travelId),
    within_current_year_aop: yup.boolean().required('Please provide customer.'),
  });

  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }
      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${ITAF_ENDPOINT}/funnels`, payload);
            break;
          case 'put':
            response = await axios.put(`${ITAF_ENDPOINT}/funnels/${payload?.id}`, payload);
            break;
          case 'delete':
            response = await axios.delete(`${ITAF_ENDPOINT}/funnels/${payload?.id}`);
            break;
          default:
            break;
        }

        let variant = 'error';
        if ([200, 201]?.includes(response?.status)) {
          variant = 'success';
          switch (action) {
            default:
              break;
          }
        }
        enqueueSnackbar(toUpperCaseFirstLetter(variant), {
          variant,
        });
      } catch (error) {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    setDialogData({});
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const processData = async (data) => {
    for (let i = 0; i < data?.length; i += 1) {
      try {
        data[i] = await schema.validate(data?.[i], { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }
    }

    for (let i = 0; i < data?.length; i += 1) {
      try {
        console.log(data?.[i]);

        await axios.post(`${ITAF_ENDPOINT}/funnels`, data?.[i]);
      } catch (error) {
        enqueueSnackbar(`Error uploading data of row ${i + 1}`, {
          variant: 'error',
        });
      }
    }
    const variant = 'success';
    enqueueSnackbar(toUpperCaseFirstLetter(variant), {
      variant,
    });
    fetchData();
  };

  const getIcon = (boolean) => {
    if (boolean) return <Done className="text-green-500" />;
    return <Close className="text-red-500" />;
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${ITAF_ENDPOINT}/funnels/travel_id/${travelId}`);

      if (response?.data?.data) {
        setTableData(response?.data?.data);
      }
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (!travelId) return;
    fetchData();
  }, [travelId]);

  return (
    <>
      <div className="flex flex-col rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
        <div className="mb-8 flex items-center justify-between">
          <p className="text-lg font-bold">Funnels</p>
          {!travelData?.IS_DISABLED && (
            <div className="flex items-center gap-2">
              <UploadCsvButton
                templateInJson={{
                  customer: 'string',
                  mrc: 0,
                  otc: 0,
                  potential_sales_deal: 'string',
                  probability_in_percentage: 0,
                  region: 'string',
                  revenue_to_be_recognized_in_current_year: 0,
                  rfs: 'string',
                  sales_pic_name: 'string',
                  status: 'string',
                  tcv: 0,
                  within_current_year_aop: true,
                }}
                onUpload={(data) => processData(data)}
              >
                <button type="button" className="bg-itaf cta-btn">
                  UPLOAD CSV
                </button>
              </UploadCsvButton>
              <button
                type="button"
                className="bg-itaf cta-btn"
                onClick={() => handleClickOpenDialog(false)}
              >
                NEW FUNNEL
              </button>
            </div>
          )}
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white text-black">
            <thead>
              <tr>
                {[
                  'No.',
                  'Region/Division',
                  'Customer',
                  'Potential Sales Deal',
                  'OTC (MYR)',
                  'MRC (MYR)',
                  'TCV (MYR)',
                  'RFS',
                  'Sales PIC',
                  'Status',
                  'Revenue To Be Recognized in Current Year (MYR)',
                  'Probability',
                  'Within Current Year AOP',
                  'Forecasted',
                ].map((label, i) => (
                  <td key={i} className="bg-itaf whitespace-nowrap px-4 text-center text-white">
                    {label}
                  </td>
                ))}
              </tr>
            </thead>
            <tbody>
              {filteredTableData
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((row, i) => (
                  <tr
                    key={i}
                    className="cursor-pointer border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-200"
                    onClick={() => {
                      if (travelData?.IS_DISABLED) return;
                      handleClickOpenDialog(true, row);
                    }}
                  >
                    <td className={bodyCellStyle}>{i + 1}</td>
                    <td className={bodyCellStyle}>
                      {checkAndReplaceStringWithHyphen(row?.region?.toUpperCase())}
                    </td>
                    <td className={bodyCellStyle}>
                      {checkAndReplaceStringWithHyphen(row?.customer)}
                    </td>
                    <td className={bodyCellStyle}>
                      {checkAndReplaceStringWithHyphen(row?.potential_sales_deal)}
                    </td>
                    <td className={bodyCellStyle}>{checkAndReplaceNumberWithZero(row?.otc, 2)}</td>
                    <td className={bodyCellStyle}>{checkAndReplaceNumberWithZero(row?.mrc, 2)}</td>
                    <td className={bodyCellStyle}>{checkAndReplaceNumberWithZero(row?.tcv, 2)}</td>
                    <td className={bodyCellStyle}>{checkAndReplaceStringWithHyphen(row?.rfs)}</td>
                    <td className={bodyCellStyle}>
                      {checkAndReplaceStringWithHyphen(row?.sales_pic_name)}
                    </td>
                    <td className={bodyCellStyle}>
                      {checkAndReplaceStringWithHyphen(row?.status)}
                    </td>
                    <td className={bodyCellStyle}>
                      {checkAndReplaceNumberWithZero(
                        row?.revenue_to_be_recognized_in_current_year,
                        2
                      )}
                    </td>
                    <td className={bodyCellStyle}>
                      {checkAndReplaceNumberWithZero(row?.probability_in_percentage)} %
                    </td>
                    <td className={bodyCellStyle}>{getIcon(row?.within_current_year_aop)}</td>
                    <td className={bodyCellStyle}>{getIcon(row?.forecasted)}</td>
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
        <TablePaginationCustom
          count={filteredTableData.length}
          page={page}
          rowsPerPage={rowsPerPage}
          onPageChange={onChangePage}
        />
      </div>
      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-itaf text-center text-white">New Travel</DialogTitle>
        <DialogContent>
          <div className="my-8 flex w-full flex-col gap-4 p-2 md:w-[400px]">
            <SelectInput
              name="region"
              value={dialogData?.region}
              placeholder="Region"
              options={TM_GLOBAL_REGIONS}
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="customer"
              value={dialogData?.customer}
              placeholder="Customer"
              onChange={handleDialogDataChange}
              showRedAsteric={false}
            />
            <TextInput
              name="potential_sales_deal"
              value={dialogData?.potential_sales_deal}
              placeholder="Potential Product/Services Deal"
              onChange={handleDialogDataChange}
            />
            <TextInput
              type="number"
              name="otc"
              value={dialogData?.otc}
              placeholder="OTC (MYR)"
              onChange={handleDialogDataChange}
            />
            <TextInput
              type="number"
              name="mrc"
              value={dialogData?.mrc}
              placeholder="MRC (MYR)"
              onChange={handleDialogDataChange}
            />
            <TextInput
              type="number"
              name="tcv"
              value={dialogData?.tcv}
              placeholder="TCV (MYR)"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="rfs"
              value={dialogData?.rfs}
              placeholder="RFS (Quarter and Year)"
              onChange={handleDialogDataChange}
            />
            <AutoCompleteTextInput
              name="sales_pic_name"
              value={dialogData?.sales_pic_name}
              placeholder="Sales PIC"
              options={allStaffs?.map((o) => o?.name)}
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="status"
              value={dialogData?.status}
              placeholder="Status"
              onChange={handleDialogDataChange}
            />
            <TextInput
              type="number"
              name="revenue_to_be_recognized_in_current_year"
              value={dialogData?.revenue_to_be_recognized_in_current_year}
              placeholder="Revenue To Be Recognized in Current Year (MYR)"
              onChange={handleDialogDataChange}
            />
            <TextInput
              type="number"
              name="probability_in_percentage"
              value={dialogData?.probability_in_percentage}
              placeholder="Probability (%)"
              onChange={handleDialogDataChange}
            />
            <BinarySwitchInput
              name="within_current_year_aop"
              value={dialogData?.within_current_year_aop}
              placeholder="Within Current Year AOP"
              onChange={handleDialogDataChange}
            />
            <BinarySwitchInput
              name="forecasted"
              value={dialogData?.forecasted}
              placeholder="Forecasted"
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
              Cancel
            </button>
            <div className="flex gap-2">
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-itaf rounded-[4px] p-2 font-semibold text-white"
                >
                  Create
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('delete')}
                  className="rounded-[4px] bg-red-500 p-2 font-semibold text-white"
                >
                  Delete
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-itaf rounded-[4px] p-2 font-semibold text-white"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default AllFunnels;
