// Next, React, Tw
import { useState, useEffect, Fragment } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions, Divider } from '@mui/material';
import { Edit } from '@mui/icons-material';

// Packages
import * as yup from 'yup';

// Components
import { SearchInput, TextAreaInput, TextInput, SelectInput } from '../Shared/CustomInput';
import AttachmentBox from '../Shared/AttachmentBox';

// Others
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { ITAF_ENDPOINT } from '../../utils/itaf';
import { useSnackbar } from '../Shared/snackbar';
import {
  checkAndReplaceStringWithHyphen,
  toUpperCaseFirstLetter,
  checkAndReplaceNumberWithZero,
} from '../../utils/shared';
import { useParamContext } from '../../utils/auth/ParamProvider';

const ParticipantDetails = () => {
  // Standard
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { travelId, participantId, costId, q } = query;
  const { enqueueSnackbar } = useSnackbar();
  const { travelData } = useSelector((state) => state.itaf);
  const { setParam } = useParamContext();

  const [participantData, setParticipantData] = useState({});

  // Table
  const [tableData, setTableData] = useState([]);
  const bodyCellStyle = 'text-center py-1 text-sm';
  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialog2Open, setDialog2Open] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const [editModeDialog2, setEditModeDialog2] = useState(false);
  const [dialogData, setDialogData] = useState({});
  const [dialogData2, setDialogData2] = useState({});
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) setDialogData(data);
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  const handleClickOpenDialog2 = (editMode, data) => {
    if (editMode) setDialogData2(data);
    setEditModeDialog2(editMode);
    setDialog2Open(true);
  };

  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const handleDialog2DataChange = (event) => {
    const { name, value } = event.target;
    setDialogData2((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const schema = yup.object({
    justification: yup.string().required('Please provide justification.'),
  });

  const schema2 = yup.object({
    currency: yup.string().required('Please provide currency.'),
    participant_id: yup.string().required('Please provide participant ID.')?.default(participantId),
    value: yup.number().required('Please provide value.'),
    travel_id: yup.string().required('Please provide travel ID.')?.default(travelId),
    type: yup.string().required('Please provide type.'),
    remarks: yup.string(),
  });

  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
        return;
      }

      try {
        let response;
        switch (action) {
          case 'put':
            response = await axios.put(`${ITAF_ENDPOINT}/participants/${participantId}`, payload);
            break;
          default:
            break;
        }

        let variant = 'error';
        if ([200, 201, 202, 203, 204, 205]?.includes(response?.status)) {
          variant = 'success';
          switch (action) {
            default:
              break;
          }
        }
        enqueueSnackbar(toUpperCaseFirstLetter(variant), {
          variant,
        });
      } catch (error) {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    setDialogData({});
    setDialogOpen(false);
    fetchParticipantData();
  };

  const handleDialog2Close = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema2.validate(dialogData2, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }

      if (travelData?.exchange_rate === 0) {
        enqueueSnackbar('Please update exchange rate to continue', {
          variant: 'error',
        });
        return;
      }

      if (payload?.currency === 'usd') {
        payload.value *= travelData?.exchange_rate;
      }

      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${ITAF_ENDPOINT}/costs`, payload);
            break;
          case 'put':
            response = await axios.put(`${ITAF_ENDPOINT}/costs/${dialogData2?.id}`, payload);
            break;
          default:
            break;
        }

        let variant = 'error';
        if ([200, 201, 202, 203, 204, 205]?.includes(response?.status)) {
          variant = 'success';
          switch (action) {
            default:
              break;
          }
        }

        enqueueSnackbar(toUpperCaseFirstLetter(variant), {
          variant,
        });
      } catch (error) {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    setDialogData2({});
    setDialog2Open(false);
    fetchData();
  };

  // Others

  const handleRowClick = (id) => {
    setParam({ costId: !costId ? id : undefined });
  };

  const fetchParticipantData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${ITAF_ENDPOINT}/participants/id/${participantId}`);
      setParticipantData(response?.data?.data?.[0] || {});
    } catch {
      setParticipantData({});
    }
    dispatch(setIsLoading(false));
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${ITAF_ENDPOINT}/costs/participant_id/${participantId}`);
      setTableData(response?.data?.data || []);
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchParticipantData();
    fetchData();
  }, []);

  return (
    <>
      <div className="container mx-auto p-1 md:p-4">
        <div className="flex flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
          <div className="flex flex-col items-center justify-between md:flex-row">
            <div className="flex flex-col gap-2">
              <p className="font-bold">{participantData?.name}</p>
              <p>{participantData?.justification}</p>
            </div>
            <div className="flex flex-col">
              {!travelData?.IS_DISABLED && (
                <button
                  type="button"
                  className="bg-itaf cta-btn"
                  onClick={() => handleClickOpenDialog(true, participantData)}
                >
                  EDIT JUSTIFICATION
                </button>
              )}
            </div>
          </div>
          <Divider />
          <div className="flex flex-col items-center justify-end md:flex-row">
            <div className="flex flex-col items-center gap-2 md:flex-row">
              <SearchInput />
              {!travelData?.IS_DISABLED && (
                <button
                  type="button"
                  className="bg-itaf cta-btn"
                  onClick={() => handleClickOpenDialog2(false)}
                >
                  NEW COST
                </button>
              )}
            </div>
          </div>
          <div className="overflow-x-auto scrollbar">
            <table className="min-w-full bg-white text-black">
              <thead>
                <tr>
                  {['No.', 'Type', 'Remarks', 'Value (MYR)', ''].map((label, i) => (
                    <td key={i} className="bg-itaf whitespace-nowrap px-4 text-center text-white">
                      {label}
                    </td>
                  ))}
                </tr>
              </thead>
              <tbody>
                {filteredTableData.map((row, i) => (
                  <Fragment key={i}>
                    <tr className="cursor-pointer border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-200">
                      <td className={bodyCellStyle} onClick={() => handleRowClick(row?.id)}>
                        {i + 1}
                      </td>
                      <td className={bodyCellStyle} onClick={() => handleRowClick(row?.id)}>
                        {checkAndReplaceStringWithHyphen(row?.type?.toUpperCase())}
                      </td>
                      <td className={bodyCellStyle} onClick={() => handleRowClick(row?.id)}>
                        {checkAndReplaceStringWithHyphen(row?.remarks)}
                      </td>
                      <td className={bodyCellStyle} onClick={() => handleRowClick(row?.id)}>
                        {checkAndReplaceNumberWithZero(row?.value, 2)}
                      </td>
                      <td className={bodyCellStyle}>
                        {!travelData?.IS_DISABLED && (
                          <button
                            type="button"
                            className="cursor-pointer rounded-xl border border-gray-300 p-1"
                            onClick={() => {
                              handleClickOpenDialog2(true, row);
                            }}
                          >
                            <Edit className="text-itaf" />
                          </button>
                        )}
                      </td>
                    </tr>
                    {costId === row?.id && (
                      <tr className="border-b border-[#fcfcfd] odd:bg-[#f8f8f8] even:bg-white">
                        <td colSpan={6}>
                          <div className="mx-auto w-1/3">
                            <AttachmentBox
                              key={costId}
                              GET_ALL_FILES_ENDPOINT={`${ITAF_ENDPOINT}/files/cost/${costId}`}
                              UPLOAD_CERTAIN_FILE_ENDPOINT={`${ITAF_ENDPOINT}/files/upload/cost/${costId}`}
                              DOWNLOAD_CERTAIN_FILE_ENDPOINT={`${ITAF_ENDPOINT}/files/download`}
                              DELETE_CERTAIN_FILE_ENDPOINT={`${ITAF_ENDPOINT}/files`}
                              DISABLE_UPLOAD={travelData?.IS_DISABLED}
                            />
                          </div>
                        </td>
                      </tr>
                    )}
                  </Fragment>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-itaf text-center text-white">Edit Participant</DialogTitle>
        <DialogContent>
          <div className="my-8 flex w-full flex-col gap-2 p-2 md:w-[400px]">
            <TextAreaInput
              name="justification"
              value={dialogData?.justification}
              placeholder="Justification"
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
              Cancel
            </button>
            <div className="flex gap-2">
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-itaf rounded-[4px] p-2 font-semibold text-white"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>

      <Dialog open={dialog2Open} onClose={() => handleDialog2Close(false)}>
        <DialogTitle className="bg-itaf text-center text-white">
          {!editModeDialog ? 'New' : 'Edit'} Cost
        </DialogTitle>
        <DialogContent>
          <div className="my-8 flex w-full flex-col gap-4 p-2 md:w-[400px]">
            <SelectInput
              name="type"
              value={dialogData2?.type}
              placeholder="Type"
              options={['meal', 'accomodation', 'flight', 'visa', 'other']}
              onChange={handleDialog2DataChange}
            />
            <TextAreaInput
              name="remarks"
              value={dialogData2?.remarks}
              placeholder="Remarks"
              onChange={handleDialog2DataChange}
              showRedAsteric={false}
            />
            <SelectInput
              name="currency"
              value={dialogData2?.currency}
              placeholder="Currency"
              options={['myr', 'usd']}
              onChange={handleDialog2DataChange}
            />
            <TextInput
              name="value"
              value={dialogData2?.value}
              placeholder="Value"
              onChange={handleDialog2DataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <button type="button" onClick={() => handleDialog2Close(false)} className="p-2">
              Cancel
            </button>
            <div className="flex gap-2">
              {!editModeDialog2 && (
                <button
                  type="button"
                  onClick={() => handleDialog2Close('post')}
                  className="bg-itaf rounded-[4px] p-2 font-semibold text-white"
                >
                  Create
                </button>
              )}
              {editModeDialog2 && (
                <button
                  type="button"
                  onClick={() => handleDialog2Close('put')}
                  className="bg-itaf rounded-[4px] p-2 font-semibold text-white"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ParticipantDetails;
