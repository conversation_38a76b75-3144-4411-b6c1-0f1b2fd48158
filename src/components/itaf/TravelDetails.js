// Next, React, Tw
import { useSelector, useDispatch } from 'react-redux';

// Packages
import moment from 'moment';
import * as yup from 'yup';

// Components
import { TextInput, DateInput, TextAreaInput, BinarySwitchInput } from '../Shared/CustomInput';
import FetchUsdToMyrExchangeRateButton from '../Shared/FetchUsdToMyrExchangeRateButton';

// Others
import { useSnackbar } from '../Shared/snackbar';
import axios from '../../utils/axios';
import { ITAF_ENDPOINT } from '../../utils/itaf';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { setTravelData } from '../../utils/store/itafReducer';
import { toUpperCaseFirstLetter } from '../../utils/shared';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

const TravelDetails = () => {
  // Standard
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();
  const { isAdmin } = useModuleRoleContext();

  const { travelData } = useSelector((state) => state.itaf);

  const IS_DISABLED = travelData?.IS_DISABLED;

  // Form
  const handleFormDataChange = (event) => {
    const { name, value } = event.target;
    dispatch(setTravelData({ ...travelData, [name]: value }));
  };

  const schema = yup.object({
    available_budget: yup.number(),
    destination: yup.string().required('Please provide destination.'),
    end_date: yup.number().required('Please provide end date.'),
    justification: yup.string().required('Please provide justification.'),
    remarks: yup.string(),
    start_date: yup.number().required('Please provide start date.'),
    exchange_rate: yup.number().required('Please provide exchange rate.'),
    exchange_rate_as_of_date: yup.string(),
    urgent: yup.boolean()?.default(false),
  });

  const handleFormSubmit = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(travelData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }
      dispatch(setIsLoading(true));
      try {
        let response;
        switch (action) {
          case 'put':
            response = await axios.put(`${ITAF_ENDPOINT}/${travelData?.id}`, payload);
            break;
          default:
            break;
        }
        let variant = 'error';
        if ([200, 201, 202, 203, 204, 205]?.includes(response?.status)) {
          variant = 'success';
          switch (action) {
            default:
              break;
          }
        }

        enqueueSnackbar(toUpperCaseFirstLetter(variant), {
          variant,
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
      dispatch(setIsLoading(false));
    }
    if (window) window.location.reload();
  };

  return (
    <div className="flex flex-col gap-2 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
      <div className="flex items-center justify-between">
        <p className="text-lg font-bold">{travelData?.travel_id}</p>
      </div>
      <div className="flex w-full flex-col gap-2">
        <TextInput
          name="destination"
          value={travelData?.destination}
          placeholder="Destination"
          onChange={handleFormDataChange}
          disabled={IS_DISABLED}
        />
        <TextAreaInput
          name="justification"
          value={travelData?.justification}
          placeholder="Justification"
          onChange={handleFormDataChange}
          disabled={IS_DISABLED}
        />
        <TextAreaInput
          rows={3}
          name="remarks"
          value={travelData?.remarks}
          placeholder="Remarks"
          onChange={handleFormDataChange}
          disabled={IS_DISABLED}
          showRedAsteric={false}
        />
        <DateInput
          name="start_date"
          value={travelData?.start_date}
          placeholder="Start Date"
          returnedFormat="unix"
          onChange={handleFormDataChange}
          disabled={IS_DISABLED}
        />
        <DateInput
          name="end_date"
          value={travelData?.end_date}
          placeholder="End Date"
          returnedFormat="unix"
          onChange={handleFormDataChange}
          disabled={IS_DISABLED}
        />
        <div className="flex items-center justify-end gap-2">
          <div className="flex-grow">
            <TextInput
              type="number"
              name="exchange_rate"
              value={travelData?.exchange_rate}
              placeholder={`Exchange Rate - ${travelData?.exchange_rate_as_of_date}`}
              disabled={IS_DISABLED}
              onChange={(event) => {
                dispatch(
                  setTravelData({
                    ...travelData,
                    exchange_rate: event?.target?.value,
                    exchange_rate_as_of_date: moment().format('DD-MM-YYYY'),
                  })
                );
              }}
            />
          </div>
          <FetchUsdToMyrExchangeRateButton
            disabled={IS_DISABLED}
            onChange={(data) =>
              dispatch(
                setTravelData({
                  ...travelData,
                  exchange_rate: data,
                  exchange_rate_as_of_date: moment().format('DD-MM-YYYY'),
                })
              )
            }
          />
        </div>
        <BinarySwitchInput
          name="urgent"
          value={travelData?.urgent}
          placeholder="Urgent"
          onChange={handleFormDataChange}
          disabled={IS_DISABLED}
        />
        {travelData?.urgent && (
          <TextAreaInput
            rows={3}
            name="urgency_justification"
            value={travelData?.urgency_justification}
            placeholder="Urgency Justificaton"
            onChange={handleFormDataChange}
            disabled={IS_DISABLED}
          />
        )}
        <TextInput
          type="number"
          name="available_budget"
          value={travelData?.available_budget}
          placeholder="Available Budget"
          onChange={handleFormDataChange}
          disabled={!isAdmin}
        />
      </div>
      <div className="flex justify-end">
        {!IS_DISABLED && (
          <div className="flex items-center gap-2">
            <buton
              type="button"
              className="bg-itaf cursor-pointer rounded-lg px-2 py-1 text-sm text-white"
              onClick={() => handleFormSubmit('put')}
            >
              SAVE
            </buton>
          </div>
        )}
      </div>
    </div>
  );
};

export default TravelDetails;
