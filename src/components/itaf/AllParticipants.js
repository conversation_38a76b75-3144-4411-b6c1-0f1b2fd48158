// Next, React, Tw
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useDispatch, useSelector } from 'react-redux';
import { twMerge } from 'tailwind-merge';

// Mui
import { Dialog, DialogTitle } from '@mui/material';
import { Visibility } from '@mui/icons-material';

// Packages
import * as yup from 'yup';

// Components
import { AutoCompleteTextInput } from '../Shared/CustomInput';

// Others
import { useSnackbar } from '../Shared/snackbar';
import axios from '../../utils/axios';
import { ITAF_ENDPOINT } from '../../utils/itaf';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { useParamContext } from '../../utils/auth/ParamProvider';
import { toUpperCaseFirstLetter, checkAndReplaceStringWithHyphen } from '../../utils/shared';

const AllParticipants = () => {
  // Standard
  const { enqueueSnackbar } = useSnackbar();
  const { query } = useRouter();
  const { travelId } = query;
  const dispatch = useDispatch();
  const { travelData } = useSelector((state) => state?.itaf);
  const { allStaffs } = useSelector((state) => state?.aum);
  const { setParam } = useParamContext();

  const IS_DISABLED = travelData?.IS_DISABLED;

  // Table
  const [tableData, setTableData] = useState([]);

  const bodyCellStyle = 'text-center py-1 text-xs';

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);
  const [dialogData, setDialogData] = useState({});
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) {
      setDialogData(data);
    }

    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  const schema = yup.object({
    name: yup.string().required('Please provide name.'),
    travel_id: yup.string().required('Please provide travel ID.')?.default(travelId),
  });

  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
          anchorOrigin: {
            vertical: 'top',
            horizontal: 'center',
          },
        });
        return;
      }
      dispatch(setIsLoading(true));
      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${ITAF_ENDPOINT}/participants`, payload);
            break;
          default:
            break;
        }

        let variant = 'error';
        if ([200, 201, 202, 203, 204, 205]?.includes(response?.status)) {
          variant = 'success';
          switch (action) {
            default:
              break;
          }
        }
        enqueueSnackbar(toUpperCaseFirstLetter(variant), {
          variant,
        });
      } catch {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
      dispatch(setIsLoading(false));
    }
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const handleClickRow = (id) => setParam({ participantId: id });

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${ITAF_ENDPOINT}/participants/travel_id/${travelId}`);

      if (response?.data?.data) {
        setTableData(response?.data?.data);
      }
    } catch {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div className="flex h-full flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
        <div className="flex items-center justify-between">
          <p className="text-lg font-bold">Participants</p>
          {!IS_DISABLED && (
            <buton
              type="button"
              className="bg-itaf cta-btn"
              onClick={() => handleClickOpenDialog(false)}
            >
              ADD
            </buton>
          )}
        </div>
        <div className="flex-grow overflow-x-auto scrollbar">
          <table className="min-w-full bg-white text-black">
            <thead>
              <tr>
                {['No.', 'Staff ID', 'Justification', ''].map((label, i) => (
                  <td key={i} className="bg-itaf whitespace-nowrap px-4 text-center text-white">
                    {label}
                  </td>
                ))}
              </tr>
            </thead>
            <tbody>
              {tableData.map((row, i) => (
                <tr
                  key={i}
                  className="cursor-pointer border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-200"
                  onClick={() => handleClickRow(row?.id)}
                >
                  <td className={bodyCellStyle}>{i + 1}</td>
                  <td className={bodyCellStyle}>{row?.name}</td>
                  <td className={twMerge(bodyCellStyle, 'overflow-hidden whitespace-nowrap')}>
                    {checkAndReplaceStringWithHyphen(row?.justification)}
                  </td>
                  <td>
                    <Visibility className="text-itaf" />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-itaf text-center text-white">Add Participant</DialogTitle>
        <div className="my-8 flex w-full flex-col gap-2 p-4 md:w-[400px]">
          <AutoCompleteTextInput
            name="name"
            value={dialogData?.name}
            placeholder="Name"
            options={allStaffs?.map((o) => o?.name)}
            onChange={handleDialogDataChange}
          />
        </div>
        <div className="flex w-full justify-between gap-4 p-4">
          <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
            Cancel
          </button>
          <div className="flex gap-2">
            {!editModeDialog && (
              <button
                type="button"
                onClick={() => handleDialogClose('post')}
                className="bg-itaf rounded-[4px] p-2 font-semibold text-white"
              >
                Add
              </button>
            )}
          </div>
        </div>
      </Dialog>
    </>
  );
};

export default AllParticipants;
