// Next, React, Tw
import { useSelector, useDispatch } from 'react-redux';
import { useRouter } from 'next/router';

// Components
import ApproveRejectButtonGroup from '../Shared/Approval/ApproveRejectButtonGroup';

// Others
import { setIsLoading } from '../../utils/store/loadingReducer';
import { ITAF_ENDPOINT } from '../../utils/itaf';
import axios from '../../utils/axios';
import { useAuthContext } from '../../utils/auth/useAuthContext';

const ActionButtonGroup = () => {
  // Standard and Vars
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { travelId } = query;
  const { user } = useAuthContext();

  const { travelData } = useSelector((state) => state.itaf);

  const handleCreateHistory = async (description) => {
    try {
      await axios.post(`${ITAF_ENDPOINT}/historys`, {
        travel_id: travelData?.id,
        description,
        action_by: user?.name,
      });
    } catch {
      // console.log;
    }
  };

  const handleRevertProject = async () => {
    dispatch(setIsLoading(true));
    try {
      await axios.put(`${ITAF_ENDPOINT}/${travelData?.id}`, {
        ...travelData,
        status: 'draft',
      });
    } catch {
      // console.log;
    }
    dispatch(setIsLoading(false));
  };

  const handleSubmitApproveProject = async (status) => {
    dispatch(setIsLoading(true));
    try {
      await axios.put(`${ITAF_ENDPOINT}/${travelData?.id}`, {
        ...travelData,
        status,
      });
    } catch {
      // console.log;
    }
    dispatch(setIsLoading(false));
  };

  return (
    <div className="flex w-full flex-col md:flex-row  md:justify-end">
      <ApproveRejectButtonGroup
        key={travelData?.id}
        NAME={travelId}
        GET_ALL_APPROVALS_ENDPOINT={`${ITAF_ENDPOINT}/approvals/travel_id/${travelData?.id}`}
        UPDATE_CERTAIN_APPROVAL_ENDPOINT={`${ITAF_ENDPOINT}/approvals`}
        REVERT_CALLBACK={async () => {
          await handleRevertProject();
          await handleCreateHistory('Project was reverted.');
          if (window) window.location.reload();
        }}
        SUBMIT_CALLBACK={async (status) => {
          await handleSubmitApproveProject(status);
          await handleCreateHistory('Project was submitted for approval.');
          if (window) window.location.reload();
        }}
        APPROVE_CALLBACK={async (status) => {
          await handleSubmitApproveProject(status);
          await handleCreateHistory('Project was approved.');
          if (window) window.location.reload();
        }}
        documentName={`Project ${travelData?.travel_id}`}
      />
    </div>
  );
};

export default ActionButtonGroup;
