// Next, React, Tw
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';
import { twMerge } from 'tailwind-merge';

// Others
import { setIsLoading } from '../../utils/store/loadingReducer';
import axios from '../../utils/axios';
import { ITAF_ENDPOINT } from '../../utils/itaf';
import { checkAndReplaceNumberWithZero } from '../../utils/shared';

const ExecutiveSummary = () => {
  // Standard and Vars
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { travelId } = query;

  const [funnelsData, setFunnelsData] = useState([]);
  const [costsData, setCostsData] = useState([]);

  // Table
  const bodyCellStyle = 'text-center py-1 text-sm whitespace-nowrap px-2';

  // Others
  const fetchFunnelsData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${ITAF_ENDPOINT}/funnels/travel_id/${travelId}`);
      setFunnelsData(response?.data?.data || []);
    } catch {
      setFunnelsData([]);
    }
    dispatch(setIsLoading(false));
  };

  const fetchCostsData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${ITAF_ENDPOINT}/costs/travel_id/${travelId}`);
      setCostsData(response?.data?.data || []);
    } catch {
      setCostsData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (!travelId) return;

    fetchFunnelsData();
    fetchCostsData();
  }, [travelId]);

  return (
    <div className="flex flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
      <p className="text-lg font-bold">Executive Summary</p>
      <div className="overflow-x-auto scrollbar">
        <table className="min-w-full bg-white text-black">
          <tbody>
            <tr className="border-b border-[#fcfcfd] bg-gray-800 text-lg text-white ">
              <td colSpan={3} className={twMerge(bodyCellStyle, 'text-center font-bold')}>
                Costs
              </td>
            </tr>
            {[
              {
                label: 'Meal',
                value: costsData
                  ?.filter((o) => o?.type === 'meal')
                  ?.reduce((sum, curr) => sum + curr?.value, 0),
              },
              {
                label: 'Accomodation',
                value: costsData
                  ?.filter((o) => o?.type === 'accomodation')
                  ?.reduce((sum, curr) => sum + curr?.value, 0),
              },
              {
                label: 'Flight',
                value: costsData
                  ?.filter((o) => o?.type === 'flight')
                  ?.reduce((sum, curr) => sum + curr?.value, 0),
              },
              {
                label: 'Visa',
                value: costsData
                  ?.filter((o) => o?.type === 'visa')
                  ?.reduce((sum, curr) => sum + curr?.value, 0),
              },
              {
                label: 'Others',
                value: costsData
                  ?.filter((o) => o?.type === 'other')
                  ?.reduce((sum, curr) => sum + curr?.value, 0),
              },
            ]?.map((row, i) => (
              <tr key={i} className=" border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] ">
                <td className={bodyCellStyle}>{row?.label}</td>
                <td className={bodyCellStyle}>:</td>
                <td className={bodyCellStyle}>
                  MYR {checkAndReplaceNumberWithZero(row?.value, 2)}
                </td>
              </tr>
            ))}
            <tr className="border-b border-[#fcfcfd] bg-gray-800 text-lg text-white ">
              <td colSpan={3} className={twMerge(bodyCellStyle, 'text-center font-bold')}>
                Funnels
              </td>
            </tr>
            {[
              { label: 'MRC', value: funnelsData?.reduce((sum, curr) => sum + curr?.mrc, 0) },
              { label: 'OTC', value: funnelsData?.reduce((sum, curr) => sum + curr?.otc, 0) },
              { label: 'TCV', value: funnelsData?.reduce((sum, curr) => sum + curr?.tcv, 0) },
              {
                label: 'Revenue To Be Recognized Current Year',
                value: funnelsData?.reduce(
                  (sum, curr) => sum + curr?.revenue_to_be_recognized_in_current_year,
                  0
                ),
              },
            ]?.map((row, i) => (
              <tr key={i} className=" border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] ">
                <td className={bodyCellStyle}>{row?.label}</td>
                <td className={bodyCellStyle}>:</td>
                <td className={bodyCellStyle}>
                  MYR {checkAndReplaceNumberWithZero(row?.value, 2)}
                </td>
              </tr>
            ))}
            <tr className="border-b border-[#fcfcfd] bg-gray-800 text-lg text-white ">
              <td colSpan={3} className={twMerge(bodyCellStyle, 'text-center font-bold')}>
                Costs / Revenue
              </td>
            </tr>
            <tr className="border-b border-[#fcfcfd] ">
              <td colSpan={3} className={twMerge(bodyCellStyle, 'text-center')}>
                {checkAndReplaceNumberWithZero(
                  costsData?.reduce((sum, curr) => sum + curr?.value, 0) /
                    funnelsData?.reduce((sum, curr) => sum + curr?.tcv, 0),
                  6
                )}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ExecutiveSummary;
