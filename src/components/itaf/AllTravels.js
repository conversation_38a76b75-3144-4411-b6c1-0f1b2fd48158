// Next, React, Tw
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';

// Packages
import * as yup from 'yup';
import moment from 'moment';

// Components
import { SearchInput } from '../Shared/CustomInput';
import UserPopup from '../Shared/UserPopup';

// Others
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { ITAF_ENDPOINT } from '../../utils/itaf';
import { useSnackbar } from '../Shared/snackbar';
import { checkAndReplaceStringWithHyphen, toUpperCaseFirstLetter } from '../../utils/shared';
import { useAuthContext } from '../../utils/auth/useAuthContext';

const AllTravels = () => {
  // Standard
  const dispatch = useDispatch();
  const { push, query } = useRouter();
  const { q } = query;
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuthContext();

  // Table
  const [tableData, setTableData] = useState([]);
  const bodyCellStyle = 'text-center py-1 text-sm';
  const filteredTableData = (() => {
    if ([undefined, '']?.includes(q)) {
      return tableData;
    }
    return tableData.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);

  const [dialogData, setDialogData] = useState({});
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) setDialogData(data);
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };

  const schema = yup.object({
    requestor_staff_id: yup.string().required('Please provide staff ID.')?.default(user?.staff_id),
    status: yup.string().required('Please provide status.')?.default('draft'),
  });

  const handleCreateApproval = async (travelId) => {
    const temp = [
      {
        type: 'preparer',
        user_name_array: [user?.name],
        is_disabled: true,
      },
      {
        type: 'business finance',
        user_name_array: [''],
        is_disabled: false,
      },
      {
        type: 'gm finance',
        user_name_array: ['Eliza Binti Abdul Rahman'],
        is_disabled: true,
      },
      {
        type: 'evp',
        user_name_array: ['Khairul Liza Binti Ibrahim'],
        is_disabled: true,
      },
    ];

    try {
      for (let i = 0; i < temp.length; i += 1) {
        await axios.post(`${ITAF_ENDPOINT}/approvals`, {
          travel_id: travelId,
          status: 'pending',
          ...temp?.[i],
        });
      }
    } catch {
      /* empty */
    }
  };

  const handleCreateHistory = async (travelId) => {
    try {
      await axios.post(`${ITAF_ENDPOINT}/historys`, {
        travel_id: travelId,
        description: 'Travel is created.',
        action_by: user?.name,
      });
    } catch {
      /* empty */
    }
  };

  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }

      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${ITAF_ENDPOINT}`, payload);
            break;
          default:
            break;
        }

        let variant = 'error';
        if ([201]?.includes(response?.status)) {
          variant = 'success';
          switch (action) {
            case 'post':
              await handleCreateApproval(response.data.data?.id);
              await handleCreateHistory(response.data.data?.id);
              break;
            default:
              break;
          }
        }

        enqueueSnackbar(toUpperCaseFirstLetter(variant), {
          variant,
        });

        push(`/itaf/travels/${response.data.data?.travel_id}`);
      } catch (error) {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    setDialogData({});
    setDialogOpen(false);
    fetchData();
  };

  // Others
  const handleClickRow = (id) => push(`/itaf/travels/${id}`);

  const getFormattedDate = (dateInUnix) => {
    if (dateInUnix === 0) return '';
    return moment.unix(dateInUnix).format('DD/MM/YYYY');
  };

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${ITAF_ENDPOINT}/all/keyword`);

      if (response?.data?.data) {
        response.data.data.reverse();
        setTableData(response?.data?.data);
      }
    } catch (error) {
      setTableData([]);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <div className="container mx-auto p-1 md:p-4">
        <div className="flex flex-col gap-8 rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
          <div className="flex flex-col justify-end md:flex-row">
            <div className="flex flex-col items-center gap-2 md:flex-row">
              <SearchInput />
              <button
                type="button"
                className="bg-itaf cta-btn"
                onClick={() => handleClickOpenDialog(false)}
              >
                NEW TRAVEL
              </button>
            </div>
          </div>
          <div className="overflow-x-auto scrollbar">
            <table className="min-w-full bg-white text-black">
              <thead>
                <tr>
                  {['No.', 'Travel ID', 'Requestor', 'Date', 'Status'].map((label, i) => (
                    <td key={i} className="bg-itaf whitespace-nowrap px-4 text-center text-white">
                      {label}
                    </td>
                  ))}
                </tr>
              </thead>
              <tbody>
                {filteredTableData.map((row, i) => (
                  <tr
                    key={i}
                    className="cursor-pointer border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-200"
                  >
                    <td className={bodyCellStyle} onClick={() => handleClickRow(row?.travel_id)}>
                      {i + 1}
                    </td>
                    <td className={bodyCellStyle} onClick={() => handleClickRow(row?.travel_id)}>
                      {checkAndReplaceStringWithHyphen(row?.travel_id)}
                    </td>
                    <td className={bodyCellStyle}>
                      <UserPopup
                        label={row?.requestor_staff_id?.toUpperCase()}
                        staff_id={row?.requestor_staff_id}
                      />
                    </td>
                    <td className={bodyCellStyle} onClick={() => handleClickRow(row?.travel_id)}>
                      {getFormattedDate(row?.start_date)} - {getFormattedDate(row?.end_date)}
                    </td>
                    <td className={bodyCellStyle} onClick={() => handleClickRow(row?.travel_id)}>
                      {checkAndReplaceStringWithHyphen(row?.status)?.toUpperCase()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-itaf text-center text-white">New Travel</DialogTitle>
        <DialogContent>
          <div className="mt-8 flex w-full flex-col gap-4 p-2 md:w-[400px]">
            <p className="text-center">Create?</p>
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
              Cancel
            </button>
            <div className="flex gap-2">
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-itaf rounded-[4px] p-2 font-semibold text-white"
                >
                  Create
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default AllTravels;
