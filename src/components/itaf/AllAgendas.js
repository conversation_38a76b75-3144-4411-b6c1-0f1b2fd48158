// Next, React, Tw
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Mui
import { Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';

// Packages
import * as yup from 'yup';
import moment from 'moment';

// Components
import { TextInput, TimeInput, AutoCompleteTextInput } from '../Shared/CustomInput';
import { TablePaginationCustom } from '../Shared/table';

// Others
import axios from '../../utils/axios';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { ITAF_ENDPOINT } from '../../utils/itaf';
import { useSnackbar } from '../Shared/snackbar';
import { checkAndReplaceStringWithHyphen, toUpperCaseFirstLetter } from '../../utils/shared';

const AllAgendas = () => {
  // Standard
  const dispatch = useDispatch();
  const { query } = useRouter();
  const { travelId } = query;
  const { travelData } = useSelector((state) => state.itaf);
  const { allStaffs } = useSelector((state) => state.aum);
  const { enqueueSnackbar } = useSnackbar();

  // Table
  const [page, setPage] = useState(0);
  const [rowsPerPage] = useState(10);
  const onChangePage = (event, newPage) => {
    setPage(newPage);
  };
  const [tableData, setTableData] = useState([]);
  const bodyCellStyle = 'text-center py-1 text-sm whitespace-nowrap px-2';
  const filteredTableData = (() => tableData)();

  // Dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editModeDialog, setEditModeDialog] = useState(false);

  const [dialogData, setDialogData] = useState({});
  const handleClickOpenDialog = (editMode, data) => {
    if (editMode) setDialogData(data);
    setEditModeDialog(editMode);
    setDialogOpen(true);
  };
  const handleDialogDataChange = (event) => {
    const { name, value } = event.target;
    setDialogData((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  };

  const schema = yup.object({
    activity: yup.string().required('Please provide activity.'),
    end_time: yup.number().required('Please provide end time.'),
    pic: yup.string().required('Please provide PIC.'),
    start_time: yup.number().required('Please provide start time.'),
    travel_id: yup.string().required('Please provide travel ID.')?.default(travelId),
  });

  const handleDialogClose = async (action) => {
    if (action) {
      let payload;
      try {
        payload = await schema.validate(dialogData, { abortEarly: false });
      } catch (error) {
        enqueueSnackbar(error.errors, {
          variant: 'error',
        });
        return;
      }
      try {
        let response;
        switch (action) {
          case 'post':
            response = await axios.post(`${ITAF_ENDPOINT}/agendas`, payload);
            break;
          case 'put':
            response = await axios.put(`${ITAF_ENDPOINT}/agendas/${payload?.id}`, payload);
            break;
          case 'delete':
            response = await axios.delete(`${ITAF_ENDPOINT}/agendas/${payload?.id}`);
            break;
          default:
            break;
        }

        let variant = 'error';
        if ([200, 201]?.includes(response?.status)) {
          variant = 'success';
          switch (action) {
            default:
              break;
          }
        }
        enqueueSnackbar(toUpperCaseFirstLetter(variant), {
          variant,
        });
      } catch (error) {
        enqueueSnackbar('Failed', {
          variant: 'error',
        });
      }
    }
    setDialogData({});
    setDialogOpen(false);
    fetchData();
  };

  // Others

  const getTimeFormat = (time) => moment.unix(time)?.format('hh:mm A');

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    try {
      const response = await axios.get(`${ITAF_ENDPOINT}/agendas/travel_id/${travelId}`);

      if (response?.data?.data) {
        setTableData(response?.data?.data);
      }
    } catch (error) {
      // console.log(error);
    }
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (!travelId) return;
    fetchData();
  }, [travelId]);

  return (
    <>
      <div className="flex flex-col rounded-xl bg-white p-4 shadow-md dark:bg-gray-600 dark:text-white">
        <div className="mb-8 flex items-center justify-between">
          <p className="text-lg font-bold">Agendas</p>
          <div className="flex items-center gap-2">
            {!travelData?.IS_DISABLED && (
              <button
                type="button"
                className="bg-itaf cta-btn"
                onClick={() => handleClickOpenDialog(false)}
              >
                NEW AGENDA
              </button>
            )}
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white text-black">
            <thead>
              <tr>
                {['Date', 'Start Time', 'End Time', 'Activity', 'PIC'].map((label, i) => (
                  <td key={i} className="bg-itaf whitespace-nowrap px-4 text-center text-white">
                    {label}
                  </td>
                ))}
              </tr>
            </thead>
            <tbody>
              {filteredTableData
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((row, i) => (
                  <tr
                    key={i}
                    className="cursor-pointer border-b border-[#fcfcfd] odd:bg-white even:bg-[#f8f8f8] hover:bg-gray-200"
                    onClick={() => {
                      if (travelData?.IS_DISABLED) return;
                      handleClickOpenDialog(true, row);
                    }}
                  >
                    <td className={bodyCellStyle}>
                      {moment.unix(row?.start_time)?.format('DD/MM/YYYY')}
                    </td>
                    <td className={bodyCellStyle}>
                      {checkAndReplaceStringWithHyphen(getTimeFormat(row?.start_time))}
                    </td>
                    <td className={bodyCellStyle}>
                      {checkAndReplaceStringWithHyphen(getTimeFormat(row?.end_time))}
                    </td>
                    <td className={bodyCellStyle}>
                      {checkAndReplaceStringWithHyphen(row?.activity)}
                    </td>
                    <td className={bodyCellStyle}>{checkAndReplaceStringWithHyphen(row?.pic)}</td>
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
        <TablePaginationCustom
          count={filteredTableData.length}
          page={page}
          rowsPerPage={rowsPerPage}
          onPageChange={onChangePage}
        />
      </div>
      <Dialog open={dialogOpen} onClose={() => handleDialogClose(false)}>
        <DialogTitle className="bg-itaf text-center text-white">New Travel</DialogTitle>
        <DialogContent>
          <div className="my-8 flex w-full flex-col gap-4 p-2 md:w-[400px]">
            <TimeInput
              name="start_time"
              value={dialogData?.start_time}
              placeholder="Start Time"
              returnedFormat="unix"
              onChange={handleDialogDataChange}
            />
            <TimeInput
              name="end_time"
              value={dialogData?.end_time}
              placeholder="End Time"
              returnedFormat="unix"
              onChange={handleDialogDataChange}
            />
            <TextInput
              name="activity"
              value={dialogData?.activity}
              placeholder="Activity"
              onChange={handleDialogDataChange}
            />

            <AutoCompleteTextInput
              name="pic"
              value={dialogData?.pic}
              placeholder="PIC"
              options={allStaffs?.map((o) => o?.name)}
              onChange={handleDialogDataChange}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <div className="flex w-full justify-between gap-4">
            <button type="button" onClick={() => handleDialogClose(false)} className="p-2">
              Cancel
            </button>
            <div className="flex gap-2">
              {!editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('post')}
                  className="bg-itaf cta-btn"
                >
                  Create
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('delete')}
                  className="cta-btn bg-red-500"
                >
                  Delete
                </button>
              )}
              {editModeDialog && (
                <button
                  type="button"
                  onClick={() => handleDialogClose('put')}
                  className="bg-itaf cta-btn"
                >
                  Edit
                </button>
              )}
            </div>
          </div>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default AllAgendas;
