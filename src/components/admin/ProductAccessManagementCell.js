// Next, React, Tw
import { useState } from 'react';

// Packages
import PropTypes from 'prop-types';

// Components
import UserPopup from '../Shared/UserPopup';

// Others
import { useSnackbar } from '../Shared/snackbar';
import axios from '../../utils/axios';
import { toUpperCaseFirstLetter, isCertainModuleSubModuleRole } from '../../utils/shared';
import { AUM_ENDPOINT } from '../../utils/aum';

const PRODUCTS = ['simi', 'gsoc', 'sharepoint'];

const ProductAccessManagementCell = ({ rowData, i, rowsPerPage, page, module }) => {
  // Standard
  const { enqueueSnackbar } = useSnackbar();

  // Others
  const handleActionButtonClick = async (submodule, role) => {
    const temp = row?.modules[0]?.submodules.find((o) => o?.submodule === submodule);
    if (!temp) {
      try {
        const response = await axios.post(
          `${AUM_ENDPOINT}/user/v1/submodule/${module}/${row?.staff_id}`,
          {
            role,
            submodule,
            tag: '',
          }
        );
        if (response.data.status === 'success') {
          enqueueSnackbar(`${response.data.data}`);
        }
      } catch {
        enqueueSnackbar('Failed to update', {
          variant: 'error',
        });
      }
    } else {
      try {
        const response = await axios.put(`${AUM_ENDPOINT}/user/v1/submodule/${temp?.id}`, {
          role,
          submodule,
          tag: 'string',
        });
        if (response.data.status === 'success') {
          enqueueSnackbar(`${response.data?.data}`);
        }
      } catch {
        enqueueSnackbar('Failed to update', {
          variant: 'error',
        });
      }
    }

    try {
      await axios.put(`${AUM_ENDPOINT}/user/v1/module/${row?.staff_id}`, {
        module,
        role: 'user',
        tag: '',
      });
    } catch {
      /* empty */
    }

    fetchData();
  };

  // Others
  const [row, setRow] = useState(rowData);
  const getBodyCellStyle = () => 'text-center py-2.5 text-sm transition-colors duration-150';

  // Click to copy functionality
  const handleCopyToClipboard = async (text, label) => {
    try {
      await navigator.clipboard.writeText(text);
      enqueueSnackbar(`${label} copied to clipboard`, { variant: 'success' });
    } catch (err) {
      enqueueSnackbar('Failed to copy to clipboard', { variant: 'error' });
    }
  };

  const fetchData = async () => {
    try {
      const response = await axios.get(`${AUM_ENDPOINT}/user/v1/module/${module}/${row?.staff_id}`);
      setRow(response?.data?.data[0]);
    } catch {
      /* empty */
    }
  };

  return (
    <>
      {PRODUCTS.map((product, index) => (
        <tr
          key={index}
          className={`cursor-pointer border-b border-gray-200 transition-all duration-200 hover:bg-gray-50 hover:-translate-y-0.5 ${
            index === PRODUCTS.length - 1 && 'border-b-2 border-gray-300'
          }`}
        >
          {index === 0 && (
            <>
              <td className={`${getBodyCellStyle()} font-medium text-gray-900`} rowSpan={PRODUCTS.length}>
                <div className="flex items-center justify-center">
                  {i + 1 + rowsPerPage * page}
                </div>
              </td>
              <td
                className={`${getBodyCellStyle()} font-medium text-gray-900 sticky left-0 z-10 bg-white hover:bg-gray-50 cursor-pointer`}
                rowSpan={PRODUCTS.length}
                onClick={() => handleCopyToClipboard(row.name, 'Name')}
                title="Click to copy name"
              >
                <div className="max-w-[180px] truncate sm:max-w-xs transition-colors duration-200 hover:text-blue-600">
                  {row.name?.toUpperCase() || '-'}
                </div>
                {/* Mobile-only staff ID display */}
                <div className="mt-1 flex items-center gap-2 md:hidden">
                  <span
                    className="text-xs text-gray-500 cursor-pointer hover:text-blue-600 transition-colors duration-200"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCopyToClipboard(row.staff_id, 'Staff ID');
                    }}
                    title="Click to copy staff ID"
                  >
                    {row.staff_id?.toUpperCase() || '-'}
                  </span>
                </div>
              </td>
              <td
                className={`${getBodyCellStyle()} hidden md:table-cell cursor-pointer hover:text-blue-600`}
                rowSpan={PRODUCTS.length}
                onClick={() => handleCopyToClipboard(row.staff_id, 'Staff ID')}
                title="Click to copy staff ID"
              >
                <div className="transition-colors duration-200">
                  <UserPopup label={row.staff_id?.toUpperCase() || '-'} staff_id={row.staff_id} />
                </div>
              </td>
            </>
          )}
          <td className={`${getBodyCellStyle()} font-medium`}>
            <div className="flex items-center justify-center">
              <span className="inline-block rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-800 transition-all duration-200 hover:bg-blue-100">
                {toUpperCaseFirstLetter(product?.toUpperCase()?.replace('-', ' '))}
              </span>
            </div>
          </td>
          <td className={getBodyCellStyle()}>
            <div className="flex items-center justify-center">
              <span
                className={`inline-block rounded-full px-2 py-0.5 text-xs font-medium transition-all duration-200 ${
                  isCertainModuleSubModuleRole(row, module, product, 'user')
                    ? 'bg-green-100 text-green-800 hover:bg-green-200'
                    : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                }`}
              >
                {isCertainModuleSubModuleRole(row, module, product, 'user') ? 'Active' : 'Inactive'}
              </span>
            </div>
          </td>
          <td className={getBodyCellStyle()}>
            <div className="flex items-center justify-center">
              {isCertainModuleSubModuleRole(row, module, product, 'user') ? (
                <button
                  type="button"
                  className="rounded-lg bg-red-500 px-3 py-1.5 text-xs font-medium text-white shadow-sm transition-all duration-200 hover:bg-red-600 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                  onClick={(event) => handleActionButtonClick(product, 'pending')}
                >
                  Revoke Access
                </button>
              ) : (
                <button
                  type="button"
                  className="rounded-lg bg-green-500 px-3 py-1.5 text-xs font-medium text-white shadow-sm transition-all duration-200 hover:bg-green-600 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                  onClick={(event) => handleActionButtonClick(product, 'user')}
                >
                  Allow Access
                </button>
              )}
            </div>
          </td>
        </tr>
      ))}
    </>
  );
};

ProductAccessManagementCell.propTypes = {
  rowData: PropTypes.object,
  i: PropTypes.number,
  rowsPerPage: PropTypes.number,
  page: PropTypes.number,
  module: PropTypes.string,
};

export default ProductAccessManagementCell;
