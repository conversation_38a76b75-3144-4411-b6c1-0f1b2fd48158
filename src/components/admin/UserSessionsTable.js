import { useState } from 'react';
import PropTypes from 'prop-types';
import * as XLSX from 'xlsx';

// Components
import { TablePaginationCustom } from '../Shared/table';
import { SelectInput, SearchInput } from '../Shared/CustomInput';

UserSessionsTable.propTypes = {
  sessions: PropTypes.arrayOf(
    PropTypes.shape({
      staff_id: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired,
      module: PropTypes.string.isRequired,
      module_role: PropTypes.string.isRequired,
      session_type: PropTypes.string.isRequired,
      last_active: PropTypes.number.isRequired,
      last_active_str: PropTypes.string.isRequired,
      submodules: PropTypes.arrayOf(PropTypes.string),
    })
  ),
  filters: PropTypes.shape({
    module: PropTypes.string,
    sessionType: PropTypes.string,
    role: PropTypes.string,
    search: PropTypes.string,
  }),
  onFilterChange: PropTypes.func.isRequired,
};

UserSessionsTable.defaultProps = {
  sessions: [],
  filters: {
    module: 'all',
    sessionType: 'all',
    role: 'all',
    search: '',
  },
};

export default function UserSessionsTable({ sessions, filters, onFilterChange }) {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const bodyCellStyle = 'text-center py-1 text-xs';

  const filteredSessions = sessions.filter((session) => {
    if (filters.module !== 'all' && session.module !== filters.module) return false;
    if (filters.sessionType !== 'all' && session.session_type !== filters.sessionType) return false;
    if (filters.role !== 'all' && session.module_role !== filters.role) return false;
    if (filters.search) {
      const searchStr = filters.search.toLowerCase();
      return (
        session.staff_id.toLowerCase().includes(searchStr) ||
        session.name.toLowerCase().includes(searchStr)
      );
    }
    return true;
  });

  const handleExportToExcel = () => {
    const headers = [
      'Staff ID',
      'Name',
      'Module',
      'Role',
      'Session Type',
      'Last Active',
      'Submodules',
    ];
    const data = filteredSessions.map((session) => [
      session.staff_id,
      session.name,
      session.module,
      session.module_role,
      session.session_type,
      session.last_active_str,
      session.submodules?.join(', ') || '',
    ]);

    const ws = XLSX.utils.aoa_to_sheet([headers, ...data]);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sessions');
    XLSX.writeFile(wb, `user_sessions_${new Date().toISOString().split('T')[0]}.xlsx`);
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col items-center justify-between gap-4 px-4 md:flex-row">
        <div className="flex gap-4">
          <div className="w-[200px]">
            <SelectInput
              value={filters.module || 'all'}
              placeholder="Filter by Module"
              options={['all', 'hsba']} // Add more modules as needed
              onChange={(e) => onFilterChange('module', e.target.value)}
            />
          </div>
          <div className="w-[200px]">
            <SelectInput
              value={filters.sessionType || 'all'}
              placeholder="Filter by Type"
              options={['all', 'web', 'phone']}
              onChange={(e) => onFilterChange('sessionType', e.target.value)}
            />
          </div>
          <div className="w-[200px]">
            <SelectInput
              value={filters.role || 'all'}
              placeholder="Filter by Role"
              options={['all', 'admin', 'user', 'pending']}
              onChange={(e) => onFilterChange('role', e.target.value)}
            />
          </div>
          <SearchInput
            value={filters.search || ''}
            onChange={(e) => onFilterChange('search', e.target.value)}
          />
        </div>
        <div>
          <button
            type="button"
            onClick={handleExportToExcel}
            className="rounded-lg bg-green-500 px-4 py-2 text-white transition-colors hover:bg-green-600"
          >
            Export to Excel
          </button>
        </div>
      </div>

      <div className="mt-4 overflow-x-auto scrollbar">
        <table className="min-w-full bg-white text-black">
          <thead>
            <tr>
              {[
                'No.',
                'Staff ID',
                'Name',
                'Module',
                'Role',
                'Session Type',
                'Last Active',
                'Submodules',
              ].map((label, i) => (
                <td key={i} className="whitespace-nowrap bg-[#408ed0] px-4 text-center text-white">
                  {label}
                </td>
              ))}
            </tr>
          </thead>
          <tbody>
            {filteredSessions
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((session, i) => (
                <tr
                  key={`${i}-${session.staff_id}`}
                  className="border-b border-gray-400 last:border-b-0"
                >
                  <td className={bodyCellStyle}>{i + 1 + rowsPerPage * page}</td>
                  <td className={bodyCellStyle}>{session.staff_id.toUpperCase()}</td>
                  <td className={bodyCellStyle}>{session.name}</td>
                  <td className={bodyCellStyle}>{session.module}</td>
                  <td className={bodyCellStyle}>{session.module_role}</td>
                  <td className={bodyCellStyle}>{session.session_type}</td>
                  <td className={bodyCellStyle}>{session.last_active_str}</td>
                  <td className={bodyCellStyle}>
                    {session.submodules?.length > 0 ? (
                      <div className="flex flex-wrap justify-center gap-1">
                        {session.submodules.map((submodule, index) => (
                          <span key={index} className="rounded bg-gray-200 px-2 py-0.5 text-xs">
                            {submodule}
                          </span>
                        ))}
                      </div>
                    ) : (
                      '-'
                    )}
                  </td>
                </tr>
              ))}
          </tbody>
        </table>
      </div>
      <TablePaginationCustom
        count={filteredSessions.length}
        page={page}
        rowsPerPage={rowsPerPage}
        onPageChange={(_, newPage) => setPage(newPage)}
        onRowsPerPageChange={(e) => {
          setRowsPerPage(parseInt(e.target.value, 10));
          setPage(0);
        }}
      />
    </div>
  );
}
