import { useState, useMemo } from 'react';
import PropTypes from 'prop-types';
import { IconButton, Drawer, Box, Button, TableContainer, Tooltip } from '@mui/material';
import FilterListIcon from '@mui/icons-material/FilterList';
import CloseIcon from '@mui/icons-material/Close';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';
import { useSnackbar } from 'notistack';
import { jsPDF } from 'jspdf';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'; // Import Info icon
import { setIsLoading } from '../../../utils/store/loadingReducer';
import { SelectInput } from '../../Shared/CustomInput';
import SortButton from '../../Shared/SortButton';

// Returns Tailwind CSS classes for score cells based on value
const getScoreClasses = (score) => {
  if (score === null || score === undefined || Number.isNaN(score)) {
    return {
      bgClass: 'bg-gray-100 dark:bg-gray-700',
      textClass: 'text-gray-500 dark:text-gray-400',
    };
  }

  if (score >= 90)
    return {
      bgClass: 'bg-green-100 dark:bg-green-900',
      textClass: 'text-green-800 dark:text-green-200',
    }; // Excellent
  if (score >= 80)
    return {
      bgClass: 'bg-blue-100 dark:bg-blue-900',
      textClass: 'text-blue-800 dark:text-blue-200',
    }; // Good
  if (score >= 70)
    return {
      bgClass: 'bg-yellow-100 dark:bg-yellow-900',
      textClass: 'text-yellow-800 dark:text-yellow-200',
    }; // Acceptable
  if (score >= 40)
    return {
      bgClass: 'bg-orange-100 dark:bg-orange-900',
      textClass: 'text-orange-800 dark:text-orange-200',
    }; // Need Improvement
  return { bgClass: 'bg-red-100 dark:bg-red-900', textClass: 'text-red-800 dark:text-red-200' }; // Poor
};

// Helper to format score for display in the table
const formatScoreDisplay = (score) => {
  if (score === null || score === undefined || Number.isNaN(score)) return 'N/A';
  return `${score.toFixed(1)}%`; // Use 1 decimal place for cleaner look
};

// Note: Original formatScore function moved inside exportToPdf as it's only used there.

export default function DashboardOverview({ data = { data: [] } }) {
  // Get unique years from data
  const yearOptions = useMemo(() => {
    if (!data || !data.data) return [];
    const years = [...new Set(data.data.map((item) => item.month_year.split('-')[0]))];
    return years
      .map((year) => ({
        value: year,
        label: year,
      }))
      .sort((a, b) => b.value - a.value); // Sort years descending
  }, [data]);

  const [selectedPartner, setSelectedPartner] = useState('All');
  const [selectedYear, setSelectedYear] = useState(
    yearOptions[0]?.value || new Date().getFullYear().toString()
  );
  const [drawerOpen, setDrawerOpen] = useState(false);
  const { query } = useRouter();
  const { sortedByKey, sortInAscendingOrder } = query;
  const dispatch = useDispatch();
  const { enqueueSnackbar } = useSnackbar();

  // Process data to organize by partner and month
  const processedData = useMemo(() => {
    if (!data || !data.data) return [];

    const partnerMap = new Map();

    // Initialize all partners with empty month data
    data?.data.forEach((item) => {
      if (!partnerMap.has(item.partner_name)) {
        partnerMap.set(item.partner_name, {
          partner_name: item.partner_name,
          months: {
            '01': null,
            '02': null,
            '03': null,
            '04': null,
            '05': null,
            '06': null,
            '07': null,
            '08': null,
            '09': null,
            10: null,
            11: null,
            12: null,
          },
          scores: [],
        });
      }
    });

    // Fill in the scores for each month
    data?.data.forEach((item) => {
      const [, month] = item.month_year.split('-');
      const partner = partnerMap.get(item.partner_name);

      if (partner) {
        partner.months[month] = item.overall_score;
        partner.scores.push(item.overall_score);
      }
    });

    // Calculate average score for each partner
    partnerMap.forEach((partner) => {
      const validScores = partner.scores.filter(
        (score) => score !== null && score !== undefined && !Number.isNaN(score)
      );

      partner.averageScore =
        validScores.length > 0
          ? validScores.reduce((sum, score) => sum + score, 0) / validScores.length
          : null;
    });

    return Array.from(partnerMap.values());
  }, [data]);

  // Get unique partner names for dropdown with proper format for SelectInput
  const partnerOptions = useMemo(() => {
    const partners = processedData.map((item) => ({
      value: item.partner_name,
      label: item.partner_name,
    }));
    return [{ value: 'All', label: 'All' }, ...partners];
  }, [processedData]);

  // Filter data based on selected partner and year
  const filteredData = useMemo(() => {
    let filtered = processedData;

    // Filter by year first
    filtered = filtered.map((partner) => {
      const yearData = data.data.filter(
        (d) => d.partner_name === partner.partner_name && d.month_year.startsWith(selectedYear)
      );

      const monthScores = {};
      yearData.forEach((d) => {
        const [, month] = d.month_year.split('-');
        monthScores[month] = d.overall_score;
      });

      return {
        ...partner,
        months: {
          '01': monthScores['01'] || null,
          '02': monthScores['02'] || null,
          '03': monthScores['03'] || null,
          '04': monthScores['04'] || null,
          '05': monthScores['05'] || null,
          '06': monthScores['06'] || null,
          '07': monthScores['07'] || null,
          '08': monthScores['08'] || null,
          '09': monthScores['09'] || null,
          10: monthScores['10'] || null,
          11: monthScores['11'] || null,
          12: monthScores['12'] || null,
        },
        averageScore:
          yearData.length > 0
            ? yearData.reduce((sum, d) => sum + d.overall_score, 0) / yearData.length
            : null,
      };
    });

    // Then filter by partner if not "All"
    if (selectedPartner !== 'All') {
      filtered = filtered.filter((item) => item.partner_name === selectedPartner);
    }

    // Apply sorting if sortedByKey is 'partner_name'
    if (sortedByKey === 'partner_name') {
      filtered = [...filtered].sort((a, b) => {
        const aValue = a.partner_name || '';
        const bValue = b.partner_name || '';

        if (sortInAscendingOrder === 'true') {
          return aValue.localeCompare(bValue);
        }
        return bValue.localeCompare(aValue);
      });
    }

    return filtered;
  }, [processedData, selectedPartner, selectedYear, data, sortedByKey, sortInAscendingOrder]);

  const handlePartnerChange = (event) => {
    setSelectedPartner(event.target.value);
    setDrawerOpen(false);
  };

  const handleYearChange = (event) => {
    setSelectedYear(event.target.value);
  };

  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen);
  };

  const exportToPdf = () => {
    try {
      dispatch(setIsLoading(true));

      // PDF formatting function (moved inside as it's only used here)
      const formatScore = (score) => {
        if (score === null || score === undefined || Number.isNaN(score)) return 'NaN';
        return `${score.toFixed(2)}%`;
      };

      // Create a more basic PDF without using autoTable plugin
      // Create PDF with landscape orientation
      // eslint-disable-next-line new-cap
      const doc = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4',
      });

      // Title
      doc.setFontSize(16);
      doc.setTextColor(46, 125, 50); // Scorecard green
      doc.text(
        `Partner Scorecard: Overall Score (${selectedYear})`,
        doc.internal.pageSize.width / 2,
        15,
        { align: 'center' }
      );

      // Define table dimensions
      const margin = 10;
      const pageWidth = doc.internal.pageSize.width;
      const pageHeight = doc.internal.pageSize.height;
      const tableWidth = pageWidth - margin * 2;
      const tableX = margin;

      // Add legend below title - centered
      const legendY = 22;
      const boxSize = 4;
      const itemWidth = 44; // Width of each legend item
      const legendItems = [
        { color: [76, 175, 80], text: 'Excellent (90-100%)' },
        { color: [33, 150, 243], text: 'Good (80-89%)' },
        { color: [255, 235, 59], text: 'Acceptable (70-79%)' },
        { color: [255, 167, 38], text: 'Need Improvement (40-69%)' },
        { color: [244, 67, 54], text: 'Poor (0-39%)' },
      ];

      // Calculate total width of legend and starting position to center it
      const totalLegendWidth = itemWidth * legendItems.length;
      const legendStartX = (pageWidth - totalLegendWidth) / 2;

      // Draw legend items
      doc.setFont(undefined, 'normal');
      doc.setFontSize(7);
      doc.setTextColor(0);

      legendItems.forEach((item, index) => {
        const itemX = legendStartX + index * itemWidth;

        // Draw color box
        doc.setFillColor(item.color[0], item.color[1], item.color[2]);
        doc.rect(itemX, legendY, boxSize, boxSize, 'F');

        // Draw label text
        doc.text(item.text, itemX + boxSize + 2, legendY + 3);
      });

      // Start table below the legend
      let tableY = 30;

      // Calculate column widths
      const partnerColWidth = 60; // Partner name column is wider
      const numCols = 14; // Partner + 12 months + Avg
      const scoreColWidth = (tableWidth - partnerColWidth) / (numCols - 1);

      // Define header row
      const headers = [
        'Partner',
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
        'Avg',
      ];

      // Calculate if multiple pages are needed
      const rowHeight = 7;
      const tableHeaderHeight = 8;
      const pageContentHeight = pageHeight - 40; // Accounting for margins
      const rowsPerPage = Math.floor((pageContentHeight - tableHeaderHeight) / rowHeight);
      const totalPages = Math.ceil(filteredData.length / rowsPerPage);

      // Function to draw header row on each page
      const drawTableHeader = (y) => {
        // Draw header background
        doc.setFillColor(77, 93, 142); // bg-sdp color
        doc.setDrawColor(200, 200, 200);
        doc.rect(tableX, y, tableWidth, tableHeaderHeight, 'FD');

        // Draw header text
        doc.setTextColor(255);
        doc.setFontSize(10);
        doc.setFont(undefined, 'bold');

        headers.forEach((header, i) => {
          const colWidth = i === 0 ? partnerColWidth : scoreColWidth;
          const colX = tableX + (i === 0 ? 0 : partnerColWidth + (i - 1) * scoreColWidth);
          doc.text(header, colX + colWidth / 2, y + 5, { align: 'center' });
        });

        return y + tableHeaderHeight;
      };

      // Function to draw a data row
      const drawDataRow = (row, y) => {
        // Draw partner cell
        doc.setDrawColor(200, 200, 200);
        doc.setFillColor(255, 255, 255);
        doc.rect(tableX, y, partnerColWidth, rowHeight, 'FD');

        // Partner name
        doc.setTextColor(0);
        doc.setFontSize(8);
        doc.setFont(undefined, 'bold');
        doc.text(
          row.partner_name.length > 30
            ? `${row.partner_name.substring(0, 28)}...`
            : row.partner_name,
          tableX + 2,
          y + 5,
          { align: 'left' }
        );

        // Draw score cells
        const scores = [
          row.months['01'],
          row.months['02'],
          row.months['03'],
          row.months['04'],
          row.months['05'],
          row.months['06'],
          row.months['07'],
          row.months['08'],
          row.months['09'],
          row.months['10'],
          row.months['11'],
          row.months['12'],
          row.averageScore,
        ];

        scores.forEach((score, i) => {
          const colX = tableX + partnerColWidth + i * scoreColWidth;

          // Set cell background color based on score
          if (score === null || score === undefined || Number.isNaN(score)) {
            doc.setFillColor(240, 240, 240); // Gray for NaN
          } else if (score >= 90) {
            doc.setFillColor(76, 175, 80); // Green
          } else if (score >= 80) {
            doc.setFillColor(33, 150, 243); // Blue
          } else if (score >= 70) {
            doc.setFillColor(255, 235, 59); // Yellow
          } else if (score >= 40) {
            doc.setFillColor(255, 167, 38); // Orange
          } else {
            doc.setFillColor(244, 67, 54); // Red
          }

          // Draw cell background
          doc.rect(colX, y, scoreColWidth, rowHeight, 'F');
          doc.setDrawColor(200, 200, 200);
          doc.rect(colX, y, scoreColWidth, rowHeight, 'S');

          // Set text color based on background for readability
          if (score === null || score === undefined || Number.isNaN(score)) {
            doc.setTextColor(100);
          } else if (score >= 70 && score < 80) {
            doc.setTextColor(0); // Black text on yellow
          } else if (score >= 40 && score < 70) {
            doc.setTextColor(0); // Black text on orange
          } else {
            doc.setTextColor(255); // White text on dark backgrounds
          }

          // Draw score text
          doc.setFont(undefined, 'normal');
          doc.text(formatScore(score), colX + scoreColWidth / 2, y + 5, { align: 'center' });
        });

        return y + rowHeight;
      };

      // Loop through pages and draw content
      for (let pageNum = 0; pageNum < totalPages; pageNum += 1) {
        // Add new page if not the first page
        if (pageNum > 0) {
          doc.addPage();
          // Draw title on subsequent pages
          doc.setFontSize(16);
          doc.setTextColor(46, 125, 50); // Scorecard green
          doc.text(
            `Partner Scorecard: Overall Score (${selectedYear}) - Page ${pageNum + 1}`,
            doc.internal.pageSize.width / 2,
            15,
            { align: 'center' }
          );
          tableY = 25; // Start table higher on subsequent pages (no legend needed)
        }

        // Draw header for this page
        let currentY = drawTableHeader(tableY);

        // Calculate which rows to display on this page
        const startRowIndex = pageNum * rowsPerPage;
        const endRowIndex = Math.min(startRowIndex + rowsPerPage, filteredData.length);

        // Draw data rows for this page
        for (let i = startRowIndex; i < endRowIndex; i += 1) {
          currentY = drawDataRow(filteredData[i], currentY);
        }

        // Add page number at bottom
        doc.setFontSize(8);
        doc.setTextColor(100);
        doc.text(`Page ${pageNum + 1} of ${totalPages}`, pageWidth / 2, pageHeight - 10, {
          align: 'center',
        });
      }

      // No need for legend at the end since we already added it below the title

      // Save PDF
      doc.save(`partner_scorecard_${selectedYear}.pdf`);
      enqueueSnackbar('PDF exported successfully', { variant: 'success' });
    } catch (error) {
      console.error('PDF export error:', error);
      enqueueSnackbar(`Error exporting PDF: ${error.message}`, { variant: 'error' });
    } finally {
      dispatch(setIsLoading(false));
    }
  };

  // Tooltip content for score legend
  const scoreLegendTooltip = (
    <div className="flex flex-col gap-1 p-1 text-xs">
      <div className="flex items-center gap-1.5">
        <span className="h-2.5 w-2.5 rounded-full bg-green-500" /> Excellent (90%+)
      </div>
      <div className="flex items-center gap-1.5">
        <span className="h-2.5 w-2.5 rounded-full bg-blue-500" /> Good (80-89%)
      </div>
      <div className="flex items-center gap-1.5">
        <span className="h-2.5 w-2.5 rounded-full bg-yellow-500" /> Acceptable (70-79%)
      </div>
      <div className="flex items-center gap-1.5">
        <span className="h-2.5 w-2.5 rounded-full bg-orange-500" /> Need Improvement (40-69%)
      </div>
      <div className="flex items-center gap-1.5">
        <span className="h-2.5 w-2.5 rounded-full bg-red-500" /> Poor ({'<'}40%)
      </div>
      <div className="flex items-center gap-1.5">
        <span className="h-2.5 w-2.5 rounded-full bg-gray-400" /> N/A
      </div>
    </div>
  );

  return (
    <div className="flex flex-col gap-6">
      {' '}
      {/* Reduced gap */}
      {/* Combined Header (Desktop & Mobile) */}
      <div className="flex flex-col gap-4 rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-800">
        {/* Top Row: Title, Filters (Mobile Trigger), Export */}
        <div className="flex items-center justify-between gap-4">
          <p className="text-md font-bold text-gray-700 dark:text-gray-200 md:text-lg">
            Partner Scorecard: Overall Score
          </p>
          <div className="flex items-center gap-2">
            {/* Mobile Filter Trigger */}
            <Tooltip title="Filter Data">
              <IconButton
                onClick={toggleDrawer}
                className="text-gray-600 hover:bg-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 md:hidden"
                size="small"
              >
                <FilterListIcon />
              </IconButton>
            </Tooltip>
            {/* Export Button */}
            <Tooltip title="Export as PDF">
              <IconButton
                onClick={exportToPdf}
                className="text-gray-600 hover:bg-gray-200 dark:text-gray-400 dark:hover:bg-gray-700"
                size="small"
              >
                <PictureAsPdfIcon />
              </IconButton>
            </Tooltip>
            {/* Score Legend Info Button - Mobile Only */}
            <div className="md:hidden">
              <Tooltip
                title={scoreLegendTooltip}
                placement="bottom"
                arrow
                PopperProps={{
                  modifiers: [
                    {
                      name: 'offset',
                      options: {
                        offset: [0, 8],
                      },
                    },
                  ],
                }}
              >
                <IconButton
                  className="text-gray-600 hover:bg-gray-200 dark:text-gray-400 dark:hover:bg-gray-700"
                  size="small"
                >
                  <InfoOutlinedIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </div>
          </div>
        </div>

        {/* Bottom Row: Filters and Legend (Desktop) */}
        <div className="hidden items-center justify-between md:flex">
          <div className="flex items-center gap-3">
            <div className="w-[250px]">
              <SelectInput
                name="partner"
                value={selectedPartner}
                placeholder="All Partners"
                onChange={handlePartnerChange}
                options={partnerOptions}
                size="small"
              />
            </div>
            <div className="w-[150px]">
              <SelectInput
                name="year"
                value={selectedYear}
                placeholder="Year"
                onChange={handleYearChange}
                options={yearOptions}
                size="small"
              />
            </div>
          </div>

          {/* Legend */}
          <div className="flex flex-wrap items-center gap-3">
            <div className="flex items-center gap-1.5">
              <span className="h-2.5 w-2.5 rounded-full bg-green-500" />
              <span className="text-xs text-gray-600 dark:text-gray-300">90%+</span>
            </div>
            <div className="flex items-center gap-1.5">
              <span className="h-2.5 w-2.5 rounded-full bg-blue-500" />
              <span className="text-xs text-gray-600 dark:text-gray-300">80-89%</span>
            </div>
            <div className="flex items-center gap-1.5">
              <span className="h-2.5 w-2.5 rounded-full bg-yellow-500" />
              <span className="text-xs text-gray-600 dark:text-gray-300">70-79%</span>
            </div>
            <div className="flex items-center gap-1.5">
              <span className="h-2.5 w-2.5 rounded-full bg-orange-500" />
              <span className="text-xs text-gray-600 dark:text-gray-300">40-69%</span>
            </div>
            <div className="flex items-center gap-1.5">
              <span className="h-2.5 w-2.5 rounded-full bg-red-500" />
              <span className="text-xs text-gray-600 dark:text-gray-300">{'<'}40%</span>
            </div>
            <div className="flex items-center gap-1.5">
              <span className="h-2.5 w-2.5 rounded-full bg-gray-400" />
              <span className="text-xs text-gray-600 dark:text-gray-300">N/A</span>
            </div>
          </div>
        </div>
      </div>
      {/* Mobile Filter Drawer (remains largely the same, triggered by header button now) */}
      <Drawer
        anchor="bottom"
        open={drawerOpen}
        onClose={toggleDrawer}
        sx={{ display: { md: 'none' } }} // Only show on mobile
      >
        <Box sx={{ p: 2, pb: 4 }}>
          {' '}
          {/* Reduced bottom padding */}
          <div className="mb-3 flex items-center justify-between">
            <h3 className="font-semibold text-gray-700">Filter Options</h3>
            <IconButton onClick={toggleDrawer} size="small">
              <CloseIcon />
            </IconButton>
          </div>
          <div className="mb-3 flex flex-col gap-3">
            <SelectInput
              name="partner"
              value={selectedPartner}
              placeholder="All Partners"
              onChange={handlePartnerChange}
              options={partnerOptions}
              size="small"
            />
            <SelectInput
              name="year"
              value={selectedYear}
              placeholder="Year"
              onChange={handleYearChange}
              options={yearOptions}
              size="small"
            />
          </div>
          <Button
            onClick={toggleDrawer}
            variant="contained"
            fullWidth
            className="bg-sdp hover:bg-sdp-dark"
            size="medium"
          >
            Apply Filters
          </Button>
        </Box>
      </Drawer>
      {/* Table Section */}
      {data && (
        // Removed extra wrapping div, apply shadow/rounding directly to TableContainer parent
        <div className="overflow-hidden rounded-lg border border-gray-200 shadow-sm dark:border-gray-700">
          <TableContainer className="max-h-[65vh] overflow-auto">
            {' '}
            {/* Adjusted max height */}
            <table className="w-full min-w-full table-fixed border-collapse">
              {/* Table Head */}
              <thead className="sticky top-0 z-10">
                <tr>
                  {/* Partner Column Header */}
                  <th className="sticky left-0 z-20 w-[180px] border-b border-r border-gray-200 bg-gray-100 px-3 py-2.5 text-left text-xs font-semibold uppercase tracking-wider text-gray-600 dark:border-gray-700 dark:bg-gray-700 dark:text-gray-300 md:w-[280px]">
                    <div className="flex items-center gap-1">
                      Partner
                      <SortButton objectKey="partner_name" />
                    </div>
                  </th>
                  {/* Month/Avg Column Headers */}
                  {[
                    'Jan',
                    'Feb',
                    'Mar',
                    'Apr',
                    'May',
                    'Jun',
                    'Jul',
                    'Aug',
                    'Sep',
                    'Oct',
                    'Nov',
                    'Dec',
                    'Avg',
                  ].map((header) => (
                    <th
                      key={header}
                      className={`w-[60px] border-b border-r border-gray-200 bg-gray-100 px-2 py-2.5 text-center text-xs font-semibold uppercase tracking-wider text-gray-600 dark:border-gray-700 dark:bg-gray-700 dark:text-gray-300 md:w-[70px] ${header === 'Avg' ? 'border-r-0' : ''}`} // No right border on last header
                    >
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              {/* Table Body */}
              <tbody className="divide-y divide-gray-100 dark:divide-gray-700">
                {filteredData.map((row) => {
                  const avgScoreClasses = getScoreClasses(row.averageScore);
                  return (
                    <tr
                      key={row.partner_name}
                      className="hover:bg-gray-50 dark:hover:bg-gray-700/50"
                    >
                      {/* Partner Name Cell */}
                      <td className="sticky left-0 z-[1] w-[180px] truncate border-r border-gray-200 bg-white px-3 py-2 text-sm font-medium text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200 md:w-[280px]">
                        {row.partner_name}
                      </td>
                      {/* Monthly Score Cells */}
                      {['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'].map(
                        (month) => {
                          const score = row.months[month];
                          const { bgClass, textClass } = getScoreClasses(score);
                          return (
                            <td
                              key={month}
                              className={`w-[60px] border-r border-gray-200 px-2 py-2 text-center text-sm font-medium dark:border-gray-700 md:w-[70px] ${bgClass} ${textClass}`}
                            >
                              {formatScoreDisplay(score)}
                            </td>
                          );
                        }
                      )}
                      {/* Average Score Cell */}
                      <td
                        className={`w-[60px] px-2 py-2 text-center text-sm font-semibold md:w-[70px] ${avgScoreClasses.bgClass} ${avgScoreClasses.textClass}`}
                      >
                        {formatScoreDisplay(row.averageScore)}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </TableContainer>
        </div>
      )}
    </div>
  );
}

DashboardOverview.propTypes = {
  data: PropTypes.shape({
    data: PropTypes.array,
  }),
};

// Using function parameter default instead of defaultProps
// data = { data: [] }
