import PropTypes from 'prop-types';
import Header from './Header';
// auth
import AuthGuard from '../utils/auth/AuthGuard';

// ----------------------------------------------------------------------

Layout.propTypes = {
  children: PropTypes.node,
};

export default function Layout({ children, ...other }) {
  return (
    <AuthGuard>
      {' '}
      <div className="flex h-full w-full flex-col">
        <div className="w-full">
          <Header />
        </div>
        <div className="relative w-full flex-grow bg-[#f5f5f5] dark:bg-gray-800">
          <div className="absolute left-0 top-0 h-full w-[100%] overflow-y-auto scrollbar scrollbar-none">
            <div className="flex h-full w-full items-center justify-center dark:text-white">
              {children}
            </div>
          </div>
        </div>
      </div>
    </AuthGuard>
  );
}
