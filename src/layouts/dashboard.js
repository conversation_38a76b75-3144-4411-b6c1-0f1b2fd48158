// Next, React, Tail<PERSON>
import { useRef, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSelector } from 'react-redux';

// Mui
import { useMediaQuery } from '@mui/material';

// Packages
import PropTypes from 'prop-types';
import { m } from 'framer-motion';

// Components
import Header from './Header';
import LoadingScreen from '../components/Shared/loading-screen';
import NpsPopup from '../components/Shared/NpsPopup';

// Others
import { GAISHA_ENDPOINT } from '../utils/gaisha';
import AuthGuard from '../utils/auth/AuthGuard';

// ----------------------------------------------------------------------

Layout.propTypes = {
  children: PropTypes.node,
};

// ----------------------------------------------------------------------

export default function Layout({ children }) {
  // Standard
  const { query } = useRouter();
  const { showGaisha } = query;
  const { isLoading } = useSelector((state) => state.loading);
  const isSmallScreen = useMediaQuery('(max-width: 768px)');

  // Others
  const pageContainerRef = useRef();
  const hideHeaderAndSideMenu = () => pageContainerRef.current.requestFullscreen();

  const getTransitionPeriod = () => (!isSmallScreen ? 1 : 0);

  const sendMessageToGaisha = (payload) => {
    const gaishaIframe = document.getElementById('gaisha');
    if (gaishaIframe?.contentWindow && GAISHA_ENDPOINT) {
      try {
        // Extract origin from the full URL
        const targetOrigin = new URL(GAISHA_ENDPOINT).origin;
        gaishaIframe.contentWindow.postMessage(payload, targetOrigin);
      } catch (error) {
        console.log('PostMessage error:', error);
        // Fallback to wildcard (less secure but functional)
        gaishaIframe.contentWindow.postMessage(payload, '*');
      }
    }
  };

  const handleGaishaLoading = (event) => {
    if (!event?.data?.gaishaLoaded) {
      return;
    }
    const localStorageKeys = ['accessToken', 'theme'];
    for (let i = 0; i < localStorageKeys.length; i += 1) {
      sendMessageToGaisha({
        key: localStorageKeys[i],
        value: localStorage?.getItem(localStorageKeys[i]),
      });
    }
  };

  const handleStorageChange = (event) => {
    if (event?.key && ['accessToken', 'theme']?.includes(event?.key) && event?.newValue) {
      sendMessageToGaisha({
        key: event.key,
        value: event.newValue,
      });
    }
  };

  useEffect(() => {
    if (showGaisha === 'true') {
      window.addEventListener('message', handleGaishaLoading, false);
      window.addEventListener('storage', handleStorageChange);
    }
    return () => {
      window.removeEventListener('storage', () => {});
      window.removeEventListener('message', () => {});
    };
  }, [showGaisha]);

  return (
    <AuthGuard>
      {' '}
      <div className="flex h-full w-full flex-col">
        <div className="w-full">
          <Header hideHeaderAndSideMenu={hideHeaderAndSideMenu} />
        </div>
        <div
          ref={pageContainerRef}
          className="relative w-full flex-grow bg-[#f5f5f5] dark:bg-gray-800"
        >
          <div className="absolute left-0 top-0 h-full w-[100%] overflow-y-auto">{children}</div>

          {showGaisha === 'true' && (
            <m.div
              className="relative h-full w-full bg-white dark:bg-gray-800"
              initial={{
                top: '100%',
                left: '100%',
                width: '0px',
                height: '0px',
              }}
              animate={{
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
              }}
              transition={{
                duration: getTransitionPeriod(),
              }}
            >
              <iframe
                id="gaisha"
                width="100%"
                height="100%"
                src={`${GAISHA_ENDPOINT}?showToolbar=false`}
                title="TANYA"
                className="absolute left-0 top-0 h-full w-full"
              />
            </m.div>
          )}
        </div>
        
        {/* NPS Popup */}
        <div className="fixed bottom-4 right-4 z-50">
          <NpsPopup />
        </div>
      </div>
      {isLoading && <LoadingScreen />}
    </AuthGuard>
  );
}
