// Next, React, Tw
import Image from 'next/image';
import { twMerge } from 'tailwind-merge';
import { useRouter } from 'next/router';
import { useTheme } from 'next-themes';

// Mui
import {
  Home,
  Groups,
  LockPerson,
  AdminPanelSettings,
  Inventory,
  Assessment,
  Apps,
  Airplay,
  AlternateEmail,
  LocationOn,
  Timeline,
  AssignmentReturn,
  OndemandVideo,
  SouthAmerica,
  Cable,
  BarChart,
  StackedBarChart,
  Assignment,
  Create,
  Escalator,
  Collections,
  AttachMoney,
  Done,
  Person,
  ToggleOn,
  BookOnline,
  Tune,
  HowToVote,
  Email,
  Moving,
  ImportExport,
  TableChart,
  Bookmark,
  Paid,
  Settings,
  LocalOffer,
  Diversity3,
  List,
  GpsFixed,
  Percent,
  AssessmentSharp,
  CalendarMonth,
  Article,
  ShoppingCart,
  Close,
  Payment,
  People,
  PieChart,
  Dashboard,
  FormatListBulleted,
} from '@mui/icons-material';

// Packages
import PropTypes from 'prop-types';

// Others
import { getColorCode, getModuleFromPath } from '../utils/shared';

// ----------------------------------------------------------------------

Tab.propTypes = {
  functionToExecuteOnTabClick: PropTypes.func,
  icon: PropTypes.string,
  redirectTo: PropTypes.string,
  label: PropTypes.string,
};

// ----------------------------------------------------------------------

export default function Tab({ label, redirectTo, icon, functionToExecuteOnTabClick }) {
  // Standard and Vars
  const { push, asPath } = useRouter();
  const { theme } = useTheme();

  // Others
  const getIcon = () => {
    const temp = asPath?.split('?')[0];
    const module = getModuleFromPath(asPath);

    const style = {
      // eslint-disable-next-line no-nested-ternary
      color: temp === redirectTo ? getColorCode(module) : theme === 'light' ? '#5D5D5D' : '#fff',
      fontSize: '23px',
    };
    const ICON_MAPPER = {
      Home: <Home style={style} />,
      Inventory: <Inventory style={style} />,
      Groups: <Groups style={style} />,
      AdminPanelSettings: <AdminPanelSettings style={style} />,
      LockPerson: <LockPerson style={style} />,
      Assessment: <Assessment style={style} />,
      Apps: <Apps style={style} />,
      Airplay: <Airplay style={style} />,
      AlternateEmail: <AlternateEmail style={style} />,
      LocationOn: <LocationOn style={style} />,
      Timeline: <Timeline style={style} />,
      AssignmentReturn: <AssignmentReturn style={style} />,
      OndemandVideo: <OndemandVideo style={style} />,
      SouthAmerica: <SouthAmerica style={style} />,
      Cable: <Cable style={style} />,
      BarChart: <BarChart style={style} />,
      StackedBarChart: <StackedBarChart style={style} />,
      Assignment: <Assignment style={style} />,
      Create: <Create style={style} />,
      Escalator: <Escalator style={style} />,
      Collections: <Collections style={style} />,
      AttachMoney: <AttachMoney style={style} />,
      Done: <Done style={style} />,
      Person: <Person style={style} />,
      ToggleOn: <ToggleOn style={style} />,
      BookOnline: <BookOnline style={style} />,
      Tune: <Tune style={style} />,
      HowToVote: <HowToVote style={style} />,
      Email: <Email style={style} />,
      Moving: <Moving style={style} />,
      ImportExport: <ImportExport style={style} />,
      TableChart: <TableChart style={style} />,
      Bookmark: <Bookmark style={style} />,
      Paid: <Paid style={style} />,
      Settings: <Settings style={style} />,
      LocalOffer: <LocalOffer style={style} />,
      Diversity3: <Diversity3 style={style} />,
      List: <List style={style} />,
      GpsFixed: <GpsFixed style={style} />,
      Percent: <Percent style={style} />,
      AssessmentSharp: <AssessmentSharp style={style} />,
      CalendarMonth: <CalendarMonth style={style} />,
      Article: <Article style={style} />,
      ShoppingCart: <ShoppingCart style={style} />,
      Close: <Close style={style} />,
      Payment: <Payment style={style} />,
      People: <People style={style} />,
      PieChart: <PieChart style={style} />,
      Dashboard: <Dashboard style={style} />,
      FormatListBulleted: <FormatListBulleted style={style} />,
      Gaisha: (
        <Image
          src="/gaisha/logo.svg"
          alt="Flappy Bird Icon"
          width={23}
          height={23}
          className="h-[23px] w-[23px]"
          style={{ width: "auto", height: "23px" }}
        />
      ),
    };
    return (
      ICON_MAPPER?.[icon] || (
        <div
          className={twMerge('flex w-full justify-center text-[11px] font-semibold')}
          style={style}
        >
          <p>{label}</p>
        </div>
      )
    );
  };

  return (
    <button
      type="button"
      className="group relative flex w-full items-center pb-3 md:justify-center"
      onClick={() => {
        if (functionToExecuteOnTabClick) {
          functionToExecuteOnTabClick();
          return;
        }
        push(redirectTo);
      }}
    >
      {getIcon()}
      <div className="left-[30px] top-0 z-10 block whitespace-nowrap bg-white px-4 py-2 text-xs font-bold group-hover:block md:absolute md:hidden">
        {label}
      </div>
    </button>
  );
}
