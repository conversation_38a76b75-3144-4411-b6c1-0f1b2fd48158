// Next, React, Tailwind
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useSelector, useDispatch } from 'react-redux';

// Packages
import PropTypes from 'prop-types';

// Components
import StandardLayout from '../standard';
import Tab from '../Shared';
import UnauthorizedPage from '../unauthorized';
import ErrorBoundary from '../../components/fa/ErrorBoundary';

// Others
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { fetchProjectData } from '../../utils/store/faReducer';
import { FA_ENDPOINT } from '../../utils/fa';
import axios from '../../utils/axios';
import { setBreadCrumbsList } from '../../utils/store/simiReducer';

// ----------------------------------------------------------------------

Layout.propTypes = {
  children: PropTypes.node,
};

export default function Layout({ children }) {
  // Standard and Vars
  const { asPath, query } = useRouter();
  const { projectId } = query;
  const { userModuleRole, isAdmin } = useModuleRoleContext();
  const { user, isAuthenticated } = useAuthContext();
  const dispatch = useDispatch();
  const { projectData } = useSelector((state) => state.fa);

  const [approvalList, setApprovalList] = useState([]);

  const tabList = [
    {
      label: 'Dashboard',
      redirectTo: '/fa',
      icon: 'Home',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Rate Management',
      redirectTo: '/fa/rate-management',
      icon: 'Percent',
      authorized: ['admin']?.includes(userModuleRole),
    },
    {
      label: 'Guidelines',
      redirectTo: '/fa/guidelines',
      icon: 'Article',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Access Management',
      redirectTo: '/fa/access-management',
      icon: 'AdminPanelSettings',
      authorized: ['admin']?.includes(userModuleRole),
    },
  ];

  const tabs = (
    <div className="flex w-full flex-col items-center">
      {tabList?.map(
        (o, i) =>
          o?.authorized && (
            <Tab key={i} label={o?.label} redirectTo={o?.redirectTo} icon={o?.icon} />
          )
      )}
    </div>
  );

  let authorized = tabList?.find((o) =>
    asPath?.split('?')?.[0]?.includes(o?.redirectTo)
  )?.authorized;

  const pattern = /\/fa\/project\/[0-9a-fA-F]{24}/;

  if (
    pattern.test(asPath) &&
    !isAdmin &&
    !projectData?.viewer_staff_id_list?.includes(user?.staff_id) &&
    !approvalList?.includes(user?.name)
  )
    authorized = false;

  const fetchData = async () => {
    try {
      const response = await axios.get(`${FA_ENDPOINT}/approval/project_id/${projectId}`);
      if (response.data.data) {
        const temp = [];
        for (let i = 0; i < response.data.data?.length; i += 1) {
          temp.push(...response.data.data[i]?.user_name_array);
        }
        setApprovalList(temp);
      }
    } catch (error) {
      // console.log(error);
      setApprovalList([]);
    }
  };

  useEffect(() => {
    if (projectId) fetchData();
  }, [projectId]);

  useEffect(() => {
    // Only fetch project data if we don't have it yet or if projectId changed
    if (pattern.test(asPath) && projectId && (!projectData || projectData.id !== projectId)) {
      dispatch(fetchProjectData(projectId));
    }
  }, [projectId, isAuthenticated]);

  // Clear breadcrumbs when leaving FA module or going to FA dashboard
  useEffect(() => {
    // If we're on the FA dashboard (not in a project), clear breadcrumbs
    if (asPath === '/fa' || asPath.startsWith('/fa?')) {
      dispatch(setBreadCrumbsList([]));
    }
  }, [asPath, dispatch]);

  // Cleanup breadcrumbs when component unmounts
  useEffect(
    () => () => {
      dispatch(setBreadCrumbsList([]));
    },
    []
  );

  if (authorized) {
    return (
      <StandardLayout tabs={tabs}>
        <ErrorBoundary>
          {children}
        </ErrorBoundary>
      </StandardLayout>
    );
  }
  return <UnauthorizedPage />;
}
