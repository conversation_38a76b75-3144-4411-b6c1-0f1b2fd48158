// Next, React, Tailwind
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';
import { useEffect } from 'react';

// Packages
import PropTypes from 'prop-types';

// Components
import StandardLayout from '../standard';
import Tab from '../Shared';
import UnauthorizedPage from '../unauthorized';

// Others
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { fetchOrdersData } from '../../utils/store/sdpReducer';
import { setIsLoading } from '../../utils/store/loadingReducer';

// ----------------------------------------------------------------------

Layout.propTypes = {
  children: PropTypes.node,
};

export default function Layout({ children }) {
  // Standard and Vars

  const dispatch = useDispatch();
  const { asPath } = useRouter();
  const { userModuleRole } = useModuleRoleContext();

  const tabList = [
    {
      label: 'Dashboard',
      redirectTo: '/sdp',
      icon: 'Home',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Delivery Performance',
      redirectTo: '/sdp/delivery-performance',
      icon: 'Moving',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Order Details',
      redirectTo: '/sdp/order-details',
      icon: 'ShoppingCart',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'No RFS Date Orders',
      redirectTo: '/sdp/no-rfs-date-orders',
      icon: 'Close',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Access Management',
      redirectTo: '/sdp/access-management',
      icon: 'AdminPanelSettings',
      authorized: true,
    },
  ];

  const tabs = (
    <div className="flex w-full flex-col items-center">
      {tabList?.map(
        (o, i) =>
          o?.authorized && (
            <Tab key={i} label={o?.label} redirectTo={o?.redirectTo} icon={o?.icon} />
          )
      )}
    </div>
  );

  const authorized = tabList?.find((o) =>
    asPath?.split('?')?.[0]?.includes(o?.redirectTo)
  )?.authorized;

  const fetchData = async () => {
    try {
      dispatch(setIsLoading(true));
      await dispatch(fetchOrdersData());
      dispatch(setIsLoading(false));
    } catch {
      /* empty */
    }
  };

  useEffect(() => {
    if (['admin', 'user']?.includes(userModuleRole)) fetchData();
  }, [userModuleRole]);

  if (authorized) {
    return <StandardLayout tabs={tabs}>{children}</StandardLayout>;
  }
  return <UnauthorizedPage />;
}
