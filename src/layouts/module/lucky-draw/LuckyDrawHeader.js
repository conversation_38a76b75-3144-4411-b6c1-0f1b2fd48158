// Next, React, Tw
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useTheme } from 'next-themes';

// Mui
import { Tooltip } from '@mui/material';
import { DarkMode, LightMode, Refresh, FitScreen } from '@mui/icons-material';

// Packages
import moment from 'moment';
import PropTypes from 'prop-types';

// ----------------------------------------------------------------------

const LuckyDrawHeader = ({ hideHeaderAndSideMenu }) => {
  // Standard
  const { push } = useRouter();
  const { theme, setTheme } = useTheme();

  // Others
  const [displayClock, setDisplayClock] = useState(false);
  const [time, setTime] = useState(moment().format('hh:mm:ss A'));
  const [refreshButtonPulsing, setRefreshButtonPulsing] = useState(false);

  useEffect(() => {
    let timer;
    if (!/Android|webOS|iPhone|iPad|iPod|Opera Mini/i.test(navigator.userAgent)) {
      setDisplayClock(true);
      timer = setInterval(() => {
        setTime(moment().format('hh:mm:ss A'));
      }, 1000);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, []);

  return (
    <div className="bg-primary flex w-full flex-row items-center justify-between px-8 py-2 dark:bg-[#1c2237]">
      <button
        type="button"
        onClick={() => {
          push('/dashboard');
        }}
      >
        <img src="/assets/logo/New-SIMI-Logo-White.svg" alt="SIMI Logo" className="w-[45px]" />
      </button>
      
      <div className="flex items-center justify-center gap-2 text-xs text-white">
        {displayClock && <p className="whitespace-nowrap">{time}</p>}
        <button
          type="button"
          onClick={() => {
            if (hideHeaderAndSideMenu) hideHeaderAndSideMenu();
          }}
          className="hidden rounded-lg hover:bg-gray-600 md:block"
        >
          <Tooltip title="Hide header and side menu">
            <FitScreen className="h-[20px] w-[20px]" />
          </Tooltip>
        </button>
        <button
          type="button"
          className="rounded-lg transition-all duration-100 hover:bg-gray-600"
          onClick={() => {
            let newTheme = 'dark';
            if (['system', 'dark']?.includes(theme)) newTheme = 'light';
            setTheme(newTheme);
            window?.dispatchEvent(
              new StorageEvent('storage', {
                key: 'theme',
                newValue: newTheme,
              })
            );
          }}
        >
          {theme === 'light' ? (
            <DarkMode className="h-[20px] w-[20px]" />
          ) : (
            <LightMode className="h-[20px] w-[20px]" />
          )}
        </button>
        <button
          type="button"
          onClick={async () => {
            setRefreshButtonPulsing(true);
            await new Promise((r) => setTimeout(r, 500));
            setRefreshButtonPulsing(false);
            window.location.reload();
          }}
          className={`${refreshButtonPulsing && 'animate-pulse'}`}
        >
          <Tooltip title="Refresh">
            <Refresh className="h-[20px] w-[20px]" />
          </Tooltip>
        </button>
      </div>
    </div>
  );
};

LuckyDrawHeader.propTypes = {
  hideHeaderAndSideMenu: PropTypes.func,
};

export default LuckyDrawHeader; 