// Packages
import PropTypes from 'prop-types';
import { useRef } from 'react';

// Components
import Lucky<PERSON><PERSON>Header from './lucky-draw/LuckyDrawHeader';

// ----------------------------------------------------------------------

Layout.propTypes = {
  children: PropTypes.node,
};

export default function Layout({ children }) {
  const pageContainerRef = useRef();

  const hideHeaderAndSideMenu = () => {
    if (pageContainerRef.current) {
      pageContainerRef.current.requestFullscreen();
    }
  };

  // A direct layout without AuthGuard and without sidebar
  return (
    <div className="flex h-full w-full flex-col">
      <div className="w-full">
        <LuckyDrawHeader hideHeaderAndSideMenu={hideHeaderAndSideMenu} />
      </div>
      <div className="relative w-full flex-grow">
        <div 
          ref={pageContainerRef}
          className="absolute left-0 top-0 h-full w-full overflow-y-auto bg-[#f5f5f5] scrollbar scrollbar-none dark:bg-gray-800">
          {children}
        </div>
      </div>
    </div>
  );
}
