// Next, React, Tailwind
import { useRouter } from 'next/router';

// Packages
import PropTypes from 'prop-types';

// Components
import UnauthorizedPage from '../unauthorized';
import StandardLayout from '../standard';
import Tab from '../Shared';

// ----------------------------------------------------------------------

Layout.propTypes = {
  children: PropTypes.node,
};

export default function Layout({ children }) {
  // Standard
  const { asPath } = useRouter();

  const tabList = [
    {
      label: 'Dashboard',
      redirectTo: '/support',
      icon: 'Home',
      authorized: true,
    },
  ];

  const tabs = (
    <div className="flex w-full flex-col items-center">
      {tabList?.map(
        (o, i) =>
          o?.authorized && (
            <Tab key={i} label={o?.label} redirectTo={o?.redirectTo} icon={o?.icon} />
          )
      )}
    </div>
  );

  const authorized = tabList?.find((o) =>
    asPath?.split('?')?.[0]?.includes(o?.redirectTo)
  )?.authorized;
  if (authorized) {
    return <StandardLayout tabs={tabs}>{children}</StandardLayout>;
  }
  return <UnauthorizedPage />;
}
