// Next, React, Tailwind
import { useDispatch } from 'react-redux';

// Packages
import PropTypes from 'prop-types';

// Components
import StandardLayout from '../standard';
import Tab from '../Shared';

// Others
import { setGoToCertainPageDialogOpen } from '../../utils/store/quranReducer';

// ----------------------------------------------------------------------

Layout.propTypes = {
  children: PropTypes.node,
};

export default function Layout({ children }) {
  // Standard
  const dispatch = useDispatch();

  const tabList = [
    {
      label: 'Reader',
      redirectTo: '/quran',
      icon: 'Home',
      authorized: true,
    },
    {
      label: 'Navigate',
      redirectTo: '',
      icon: 'Moving',
      authorized: true,
      functionToExecuteOnTabClick: () => dispatch(setGoToCertainPageDialogOpen(true)),
    },
  ];

  const tabs = (
    <div className="flex w-full flex-col items-center">
      {tabList?.map(
        (o) =>
          o?.authorized && (
            <Tab
              key={o.label}
              label={o?.label}
              redirectTo={o?.redirectTo}
              icon={o?.icon}
              functionToExecuteOnTabClick={o?.functionToExecuteOnTabClick}
            />
          )
      )}
    </div>
  );

  return (
    <StandardLayout
      tabs={tabs}
      title="Al-Quran"
      moduleClassName="bg-gradient-to-br from-sky-400 to-indigo-600 dark:from-sky-700 dark:to-indigo-900"
    >
      {children}
    </StandardLayout>
  );
}
