// Next, React, Tailwind
import { useRouter, push } from 'next/router';
import { useEffect } from 'react';

// Packages
import PropTypes from 'prop-types';

// Components
import StandardLayout from '../standard';
import Tab from '../Shared';
import UnauthorizedPage from '../unauthorized';

// Others
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

// ----------------------------------------------------------------------

Layout.propTypes = {
  children: PropTypes.node,
};

export default function Layout({ children }) {
  const { asPath } = useRouter();
  const { isAdmin, userModuleRole } = useModuleRoleContext();

  useEffect(() => {
    if (userModuleRole === 'admin') {
      push('/payme');
    } else {
      push('/payme/payment');
    }
  }, [userModuleRole, push]);

  let tabList = [
    {
      label: 'Payment',
      redirectTo: '/payme/payment',
      icon: 'Payment',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
  ];

  if (isAdmin)
    tabList = [
      {
        label: 'Dashboard',
        redirectTo: '/payme',
        icon: 'Home',
        authorized: ['admin', 'user']?.includes(userModuleRole),
      },
      ...tabList,
      {
        label: 'Collection',
        redirectTo: '/payme/collection',
        icon: 'Collections',
        authorized: ['admin']?.includes(userModuleRole),
      },
      {
        label: 'Expenses',
        redirectTo: '/payme/expenses',
        icon: 'AttachMoney',
        authorized: ['admin']?.includes(userModuleRole),
      },
      {
        label: 'Pending Verification',
        redirectTo: '/payme/pending-verification',
        icon: 'Done',
        authorized: ['admin']?.includes(userModuleRole),
      },
      {
        label: 'User',
        redirectTo: '/payme/user',
        icon: 'Person',
        authorized: ['admin']?.includes(userModuleRole),
      },
      {
        label: 'Year Config',
        redirectTo: '/payme/year-config',
        icon: 'ToggleOn',
        authorized: ['admin']?.includes(userModuleRole),
      },
      {
        label: 'Access Management',
        redirectTo: '/payme/access-management',
        icon: 'AdminPanelSettings',
        authorized: ['admin']?.includes(userModuleRole),
      },
    ];

  const tabs = (
    <div className="flex w-full flex-col items-center">
      {tabList?.map(
        (o, i) =>
          o?.authorized && (
            <Tab key={i} label={o?.label} redirectTo={o?.redirectTo} icon={o?.icon} />
          )
      )}
    </div>
  );

  const authorized = tabList?.find((o) => {
    const pathMatch = asPath?.split('?')?.[0]?.includes(o?.redirectTo);
    return pathMatch;
  })?.authorized;

  if (authorized) {
    return <StandardLayout tabs={tabs}>{children}</StandardLayout>;
  }
  return <UnauthorizedPage />;
}
