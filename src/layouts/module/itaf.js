// Next, React, Tailwind
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';
import { useEffect } from 'react';

// Packages
import PropTypes from 'prop-types';

// Components
import StandardLayout from '../standard';
import Tab from '../Shared';
import UnauthorizedPage from '../unauthorized';

// Others
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { fetchTravelData } from '../../utils/store/itafReducer';

// ----------------------------------------------------------------------

Layout.propTypes = {
  children: PropTypes.node,
};

export default function Layout({ children }) {
  // Standard and Vars

  const dispatch = useDispatch();
  const { asPath, query } = useRouter();
  const { travelId } = query;
  const { userModuleRole } = useModuleRoleContext();

  const tabList = [
    {
      label: 'Dashboard',
      redirectTo: '/itaf',
      icon: 'Home',
      authorized: true,
    },
    {
      label: 'Travel',
      redirectTo: '/itaf/travels',
      icon: 'List',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Guidelines',
      redirectTo: '/itaf/guidelines',
      icon: 'Article',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Access Management',
      redirectTo: '/itaf/access-management',
      icon: 'AdminPanelSettings',
      authorized: ['admin']?.includes(userModuleRole),
    },
  ];

  const tabs = (
    <div className="flex w-full flex-col items-center">
      {tabList?.map(
        (o, i) =>
          o?.authorized && (
            <Tab key={i} label={o?.label} redirectTo={o?.redirectTo} icon={o?.icon} />
          )
      )}
    </div>
  );

  const authorized = tabList?.find((o) =>
    asPath?.split('?')?.[0]?.includes(o?.redirectTo)
  )?.authorized;

  const pattern = /^\/itaf\/travels\/ITAF-\d{8}-\d{6}$/;

  useEffect(() => {
    if (pattern.test(asPath) && travelId) dispatch(fetchTravelData(travelId));
  }, [travelId]);

  if (authorized) {
    return <StandardLayout tabs={tabs}>{children}</StandardLayout>;
  }
  return <UnauthorizedPage />;
}
