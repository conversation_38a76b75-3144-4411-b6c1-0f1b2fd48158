// Next, React, Tailwind
import { useRouter } from 'next/router';

// Packages
import PropTypes from 'prop-types';

// Components
import StandardLayout from '../standard';
import Tab from '../Shared';
import UnauthorizedPage from '../unauthorized';

// Others
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

// ----------------------------------------------------------------------

Layout.propTypes = {
  children: PropTypes.node,
};

export default function Layout({ children }) {
  const { asPath } = useRouter();
  const { userModuleRole } = useModuleRoleContext();

  const tabList = [
    {
      label: 'Dashboard',
      redirectTo: '/solid',
      icon: 'Home',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Requirement',
      redirectTo: '/solid/requirement',
      icon: 'Bookmark',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Quotation',
      redirectTo: '/solid/quotation',
      icon: 'LocalOffer',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'PoP/CLS Management',
      redirectTo: '/solid/pop-cls-management',
      icon: 'GpsFixed',
      authorized: ['admin']?.includes(userModuleRole),
    },
    {
      label: 'Partner Management',
      redirectTo: '/solid/partner-management',
      icon: 'Diversity3',
      authorized: ['admin']?.includes(userModuleRole),
    },
    {
      label: 'Access Management',
      redirectTo: '/solid/access-management',
      icon: 'AdminPanelSettings',
      authorized: ['admin']?.includes(userModuleRole),
    },
  ];

  const tabs = (
    <div className="flex w-full flex-col items-center">
      {tabList?.map(
        (o, i) =>
          o?.authorized && (
            <Tab key={i} label={o?.label} redirectTo={o?.redirectTo} icon={o?.icon} />
          )
      )}
    </div>
  );

  const authorized = tabList?.find((o) =>
    asPath?.split('?')?.[0]?.includes(o?.redirectTo)
  )?.authorized;
  if (authorized) {
    return <StandardLayout tabs={tabs}>{children}</StandardLayout>;
  }
  return <UnauthorizedPage />;
}
