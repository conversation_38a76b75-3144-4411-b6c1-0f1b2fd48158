// Next, React, Tailwind
import { useRouter } from 'next/router';
import { useSelector } from 'react-redux';

// Packages
import PropTypes from 'prop-types';

// Components
import StandardLayout from '../standard';
import Tab from '../Shared';
import UnauthorizedPage from '../unauthorized';

// Others
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

// ----------------------------------------------------------------------

Layout.propTypes = {
  children: PropTypes.node,
};

export default function Layout({ children }) {
  const { asPath } = useRouter();
  const { userModuleRole } = useModuleRoleContext();
  const { userSubModules } = useSelector((state) => state.aum);

  const tabList = [
    {
      label: 'Dashboard',
      redirectTo: '/hsba',
      icon: 'Home',
      iconIsNotShown: true,
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Management View',
      redirectTo: '/hsba/management-view',
      icon: 'BarChart',
      authorized: ['admin']?.includes(userModuleRole) || userSubModules.includes('management-view'),
    },
    {
      label: 'CX Overview',
      redirectTo: '/hsba/cx-overview',
      icon: 'StackedBarChart',
      authorized: ['admin']?.includes(userModuleRole) || userSubModules.includes('cx-overview'),
    },
    {
      label: 'BTU Stock Utilization',
      redirectTo: '/hsba/btu-stock-utilization',
      icon: 'ImportExport',
      authorized:
        ['admin']?.includes(userModuleRole) || userSubModules.includes('btu-stock-utilization'),
    },
    {
      label: 'Distribution',
      redirectTo: '/hsba/distribution',
      icon: 'Timeline',
      authorized: ['admin']?.includes(userModuleRole) || userSubModules.includes('distribution'),
    },
    {
      label: 'Return Order',
      redirectTo: '/hsba/return-order',
      icon: 'AssignmentReturn',
      authorized: ['admin']?.includes(userModuleRole) || userSubModules.includes('return-order'),
    },
    {
      label: 'Demand List',
      redirectTo: '/hsba/demand-list',
      icon: 'OndemandVideo',
      authorized: ['admin']?.includes(userModuleRole) || userSubModules.includes('demand-list'),
    },
    {
      label: 'Inter Region',
      redirectTo: '/hsba/inter-region',
      icon: 'SouthAmerica',
      authorized: ['admin']?.includes(userModuleRole) || userSubModules.includes('inter-region'),
    },
    {
      label: 'Cable Report',
      redirectTo: '/hsba/cable-report',
      icon: 'Cable',
      authorized: ['admin']?.includes(userModuleRole) || userSubModules.includes('cable-report'),
    },
    {
      label: 'Configuration',
      redirectTo: '/hsba/configuration',
      icon: 'Tune',
      authorized: ['admin']?.includes(userModuleRole),
    },
    {
      label: 'Product Access Management',
      redirectTo: '/hsba/product-access-management',
      icon: 'LockPerson',
      authorized: ['admin']?.includes(userModuleRole),
    },
    {
      label: 'Access Management',
      redirectTo: '/hsba/access-management',
      icon: 'AdminPanelSettings',
      authorized: ['admin']?.includes(userModuleRole),
    },
  ];

  const tabs = (
    <div className="flex w-full flex-col items-center">
      {tabList?.map(
        (o, i) =>
          o?.authorized &&
          !o?.iconIsNotShown && (
            <Tab key={i} label={o?.label} redirectTo={o?.redirectTo} icon={o?.icon} />
          )
      )}
    </div>
  );

  const authorized = tabList?.find((o) =>
    asPath?.split('?')?.[0]?.includes(o?.redirectTo)
  )?.authorized;
  if (authorized) {
    return <StandardLayout tabs={tabs}>{children}</StandardLayout>;
  }
  return <UnauthorizedPage />;
}
