// Next, React, Tailwind
import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch } from 'react-redux';

// Packages
import PropTypes from 'prop-types';

// Components
import StandardLayout from '../standard';
import Tab from '../Shared';
import UnauthorizedPage from '../unauthorized';

// Others
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { setIsLoading } from '../../utils/store/loadingReducer';
import { fetchSourcesData } from '../../utils/store/finsightReducer';

// ----------------------------------------------------------------------

Layout.propTypes = {
  children: PropTypes.node,
};

export default function Layout({ children }) {
  // Standard and Vars
  const { asPath } = useRouter();
  const dispatch = useDispatch();

  const { userModuleRole } = useModuleRoleContext();

  const tabList = [
    {
      label: 'Dashboard',
      redirectTo: '/finsight',
      icon: 'Home',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Forecast Performance',
      redirectTo: '/finsight/forecast-performance',
      icon: 'Timeline',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Monthly Breakdown',
      redirectTo: '/finsight/monthly-breakdown',
      icon: 'PieChart',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Guidelines',
      redirectTo: '/finsight/guidelines',
      icon: 'Article',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Access Management',
      redirectTo: '/finsight/access-management',
      icon: 'AdminPanelSettings',
      authorized: ['admin']?.includes(userModuleRole),
    },
  ];

  const tabs = (
    <div className="flex w-full flex-col items-center">
      {tabList?.map(
        (o, i) =>
          o?.authorized && (
            <Tab key={i} label={o?.label} redirectTo={o?.redirectTo} icon={o?.icon} />
          )
      )}
    </div>
  );

  const authorized = tabList?.find((o) => {
    const pathMatch = asPath?.split('?')?.[0]?.includes(o?.redirectTo);
    return pathMatch;
  })?.authorized;

  const fetchData = async () => {
    dispatch(setIsLoading(true));
    await dispatch(fetchSourcesData());
    dispatch(setIsLoading(false));
  };

  useEffect(() => {
    if (!authorized) return;
    fetchData();
  }, [authorized]);

  if (authorized) {
    return <StandardLayout tabs={tabs}>{children}</StandardLayout>;
  }
  return <UnauthorizedPage />;
}
