// Next, React, Tailwind
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';

// Mui
import { Divider } from '@mui/material';

// Packages
import PropTypes from 'prop-types';

// Components
import StandardLayout from '../standard';
import Tab from '../Shared';
import UnauthorizedPage from '../unauthorized';

// Others
import axios from '../../utils/axios';
import { useAuthContext } from '../../utils/auth/useAuthContext';
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { METRIC_ENDPOINT } from '../../utils/metric';

// ----------------------------------------------------------------------

Layout.propTypes = {
  children: PropTypes.node,
};

export default function Layout({ children }) {
  const { asPath } = useRouter();
  const { user, isAuthenticated } = useAuthContext();
  const { userModuleRole, isAdmin } = useModuleRoleContext();
  const [divisionList, setDivisionList] = useState([]);

  const tabList = [
    {
      label: 'Dashboard',
      redirectTo: '/metric',
      icon: 'Home',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
  ];

  const tabList2 = [
    {
      label: 'Product Management',
      redirectTo: '/metric/product-management',
      icon: 'Inventory',
      authorized: user?.isSuperAdmin,
    },
    {
      label: 'Division Management',
      redirectTo: '/metric/division-management',
      icon: 'Groups',
      authorized: user?.isSuperAdmin,
    },
    {
      label: 'Product Access Management',
      redirectTo: '/metric/product-access-management',
      icon: 'LockPerson',
      authorized: ['admin']?.includes(userModuleRole),
    },
    {
      label: 'Access Management',
      redirectTo: '/metric/access-management',
      icon: 'AdminPanelSettings',
      authorized: ['admin']?.includes(userModuleRole),
    },
  ];

  const tabs = (
    <div className="flex w-full flex-col items-center">
      {tabList?.map(
        (o, i) =>
          o?.authorized && (
            <Tab key={i} label={o?.label} redirectTo={o?.redirectTo} icon={o?.icon} />
          )
      )}
      <Divider />
      {divisionList?.map((o, i) => (
        <Tab
          key={i}
          label={o?.division_abbreviation}
          redirectTo={`/metric/report-card?divisionId=${o?.id}&year=all&tab=0`}
          icon="Assessment"
        />
      ))}
      {isAdmin && <Divider />}
      {tabList2?.map(
        (o, i) =>
          o?.authorized && (
            <Tab key={i} label={o?.label} redirectTo={o?.redirectTo} icon={o?.icon} />
          )
      )}
    </div>
  );

  const fetchData = async () => {
    try {
      const response = await axios.get(`${METRIC_ENDPOINT}/division/all/keyword`);
      if (response?.data?.status === 'success' && response?.data?.data !== null) {
        setDivisionList(response?.data?.data);
      }
    } catch {
      setDivisionList([]);
    }
  };

  useEffect(() => {
    if (!isAuthenticated) return;
    fetchData();
  }, [isAuthenticated]);

  let authorized = [...tabList, ...tabList2]?.find(
    (o) => o?.redirectTo === asPath?.split('?')[0]
  )?.authorized;

  const pattern = /\/metric\/report-card/;

  if (pattern.test(asPath)) {
    authorized = true;
  }

  if (authorized) {
    return <StandardLayout tabs={tabs}>{children}</StandardLayout>;
  }
  return <UnauthorizedPage />;
}
