// Next, React, Tw
import { useRouter } from 'next/router';

// Packages
import PropTypes from 'prop-types';

// Components
import StandardLayout from '../standard';
import Tab from '../Shared';
import UnauthorizedPage from '../unauthorized';

// Others
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

// ----------------------------------------------------------------------

Layout.propTypes = {
  children: PropTypes.node,
};

export default function Layout({ children }) {
  const { asPath } = useRouter();
  const { userModuleRole } = useModuleRoleContext();

  const tabList = [
    {
      label: 'Dashboard',
      redirectTo: '/calendar',
      icon: 'Home',
      authorized: true,
    },
    {
      label: 'Group Management',
      redirectTo: '/calendar/public-holiday',
      icon: 'Groups',
      authorized: ['user', 'admin']?.includes(userModuleRole),
    },
    {
      label: 'Access Management',
      redirectTo: '/calendar/access-management',
      icon: 'AdminPanelSettings',
      authorized: ['admin']?.includes(userModuleRole),
    },
  ];

  const tabs = (
    <div className="flex w-full flex-col items-center">
      {tabList?.map(
        (o, i) =>
          o?.authorized && (
            <Tab key={i} label={o?.label} redirectTo={o?.redirectTo} icon={o?.icon} />
          )
      )}
    </div>
  );

  const authorized = tabList?.find((o) =>
    asPath?.split('?')?.[0]?.includes(o?.redirectTo)
  )?.authorized;

  if (authorized) {
    return <StandardLayout tabs={tabs}>{children}</StandardLayout>;
  }
  return <UnauthorizedPage />;
}
