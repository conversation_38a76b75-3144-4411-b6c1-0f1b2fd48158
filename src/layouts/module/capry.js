// Next, React, Tailwind
import { useRouter } from 'next/router';

// Packages
import PropTypes from 'prop-types';

// Components
import StandardLayout from '../standard';
import Tab from '../Shared';
import UnauthorizedPage from '../unauthorized';

// Others
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';

// ----------------------------------------------------------------------

Layout.propTypes = {
  children: PropTypes.node,
};

export default function Layout({ children }) {
  const { asPath } = useRouter();
  const { userModuleRole } = useModuleRoleContext();

  const tabList = [
    {
      label: 'Dashboard',
      redirectTo: '/capry',
      icon: 'Home',
      authorized: ['admin', 'reserver', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Report',
      redirectTo: '/capry/report',
      icon: 'AssessmentSharp',
      authorized: ['admin', 'reserver', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Inventory',
      redirectTo: '/capry/inventory',
      icon: 'Inventory',
      authorized: ['admin', 'reserver', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Capacity List',
      redirectTo: '/capry/capacity-list',
      icon: 'ImportExport',
      authorized: ['admin', 'reserver', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Reservation',
      redirectTo: '/capry/reservation',
      icon: 'BookOnline',
      authorized: ['admin', 'reserver', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Guidelines',
      redirectTo: '/capry/guidelines',
      icon: 'Article',
      authorized: ['admin', 'reserver', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Access Management',
      redirectTo: '/capry/access-management',
      icon: 'AdminPanelSettings',
      authorized: ['admin']?.includes(userModuleRole),
    },
  ];

  const tabs = (
    <div className="flex w-full flex-col items-center">
      {tabList?.map(
        (o, i) =>
          o?.authorized && (
            <Tab key={i} label={o?.label} redirectTo={o?.redirectTo} icon={o?.icon} />
          )
      )}
    </div>
  );

  const authorized = tabList?.find((o) =>
    asPath?.split('?')?.[0]?.includes(o?.redirectTo)
  )?.authorized;
  if (authorized) {
    return <StandardLayout tabs={tabs}>{children}</StandardLayout>;
  }
  return <UnauthorizedPage />;
}
