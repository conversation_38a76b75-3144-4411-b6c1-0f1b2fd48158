// Next, React, Tailwind
import { useRouter } from 'next/router';
import { useSelector } from 'react-redux';

// Packages
import PropTypes from 'prop-types';
import * as R from 'ramda';

// Components
import StandardLayout from '../standard';
import Tab from '../Shared';
import UnauthorizedPage from '../unauthorized';

// Others
import { useModuleRoleContext } from '../../utils/auth/ModuleRoleProvider';
import { toUpperCaseFirstLetter } from '../../utils/shared';

// ----------------------------------------------------------------------

Layout.propTypes = {
  children: PropTypes.node,
};

export default function Layout({ children }) {
  const { asPath } = useRouter();
  const { userModuleRole } = useModuleRoleContext();
  const { userSubModules } = useSelector((state) => state.aum);

  let tabList = [
    {
      label: 'Dashboard',
      redirectTo: '/oasys',
      icon: 'Home',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
  ];

  if (['admin']?.includes(userModuleRole)) {
    tabList?.push({
      label: 'Supervisor',
      redirectTo: '/oasys/executive',
      icon: 'Timeline',
      authorized: ['admin']?.includes(userModuleRole),
    });
  }

  userSubModules?.map((o) =>
    tabList?.push({
      label: toUpperCaseFirstLetter(o),
      redirectTo: `/oasys/${o}`,
      icon: 'Timeline',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    })
  );

  tabList = R.uniqBy(R.prop('redirectTo'), tabList);

  tabList = [
    ...tabList,
    {
      label: 'Guidelines',
      redirectTo: '/oasys/guidelines',
      icon: 'Article',
      authorized: ['admin', 'user']?.includes(userModuleRole),
    },
    {
      label: 'Role Management',
      redirectTo: '/oasys/role-management',
      icon: 'People',
      authorized: ['admin']?.includes(userModuleRole),
    },
    {
      label: 'Access Management',
      redirectTo: '/oasys/access-management',
      icon: 'AdminPanelSettings',
      authorized: ['admin']?.includes(userModuleRole),
    },
  ];

  const tabs = (
    <div className="flex w-full flex-col items-center">
      {tabList?.map(
        (o, i) =>
          o?.authorized && (
            <Tab key={i} label={o?.label} redirectTo={o?.redirectTo} icon={o?.icon} />
          )
      )}
    </div>
  );

  const authorized = tabList?.find((o) => {
    const pathMatch = asPath?.split('?')?.[0]?.includes(o?.redirectTo);
    return pathMatch;
  })?.authorized;

  if (authorized) {
    return <StandardLayout tabs={tabs}>{children}</StandardLayout>;
  }
  return <UnauthorizedPage />;
}
