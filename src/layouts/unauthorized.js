// Next, React, Tw
import Image from 'next/image';

// Components
import Layout from './error';

// ----------------------------------------------------------------------

const UnauthorizedPage = () => (
  <Layout>
    <div className="h-full w-full p-8">
      <div className="mx-auto flex h-full flex-col items-center justify-center gap-4">
        <p className="text-center">Ask your superior to approve your access request.</p>
        <Image
          src="/layout/pexels-introspectivedsgn-9703047.webp"
          alt="Unauthorized Image"
          width={3125}
          height={2331}
          className="h-[300px] w-[350px] rounded-md md:h-[700px] md:w-[750px]"
          priority
        />
      </div>
    </div>
  </Layout>
);

export default UnauthorizedPage;
