// Next, React, Tw
import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import { useTheme } from 'next-themes';
import { useSelector } from 'react-redux';

// Mui
import { Divider, Tooltip } from '@mui/material';
import { Person, Refresh, DarkMode, LightMode, FitScreen, Help } from '@mui/icons-material';

// Packages
import moment from 'moment';
import PropTypes from 'prop-types';

// Components
import { useAuthContext } from '../utils/auth/useAuthContext';
import MenuPopover from '../components/Shared/menu-popover';

// Others
import { useSnackbar } from '../components/Shared/snackbar';
import { localStorageAvailable, getModuleFromPath } from '../utils/shared';

// ----------------------------------------------------------------------

const Header = ({ hideHeaderAndSideMenu }) => {
  // Standard
  const { push, query, asPath } = useRouter();
  const { enqueueSnackbar } = useSnackbar();
  const { user, logout } = useAuthContext();
  const { theme, setTheme } = useTheme();
  const { layout } = query;
  const { isSaving } = useSelector((state) => state.loading);
  const firstRender = useRef(true);

  const [showSaved, setShowSaved] = useState(false);

  const module = getModuleFromPath(asPath);

  const showHeader = localStorageAvailable() && localStorage.getItem('showHeader');

  // Others
  const [displayClock, setDisplayClock] = useState(false);
  const [time, setTime] = useState(moment().format('hh:mm:ss A'));
  const [openPopover, setOpenPopover] = useState(null);
  const [refreshButtonPulsing, setRefreshButtonPulsing] = useState(false);

  const getLogo = () => {
    if (!layout) {
      return '/assets/logo/New-SIMI-Logo-White.svg';
    }
    const logoMapping = {
      'lrp-2024': '/events/lrp-2024/logo.png',
    };
    return logoMapping[layout];
  };

  useEffect(() => {
    let timer;
    if (!/Android|webOS|iPhone|iPad|iPod|Opera Mini/i.test(navigator.userAgent)) {
      setDisplayClock(true);
      timer = setInterval(() => {
        setTime(moment().format('hh:mm:ss A'));
      }, 1000);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, []);

  useEffect(() => {
    if (firstRender.current) {
      firstRender.current = false;
      return;
    }
    if (!isSaving) {
      setShowSaved(true);
      const timer = setTimeout(() => {
        setShowSaved(false);
      }, 3000);
      // eslint-disable-next-line consistent-return
      return () => clearTimeout(timer);
    }
  }, [isSaving]);

  if (showHeader === 'false') return '';

  return (
    <div className="bg-primary flex w-full flex-row items-center justify-between px-8 py-2 dark:bg-[#1c2237]">
      <button
        type="button"
        onClick={() => {
          if (asPath === '/dashboard') {
            window.location.reload();
            return;
          }
          push('/dashboard');
        }}
      >
        <img src={getLogo()} alt="SIMI Logo" className="w-[45px]" />
      </button>
      {isSaving && <p className="text-xs text-white">Saving changes...</p>}
      {!isSaving && showSaved && <p className="text-xs text-white">All changes are saved!</p>}

      <div className="relative">
        <button
          type="button"
          className="joyride-dashboard-step-5 flex items-center gap-2 rounded-lg text-xs "
          onClick={(event) => setOpenPopover(event.currentTarget)}
        >
          {user?.profile_picture !== '' ? (
            <img
              src={user?.profile_picture}
              alt="Profile"
              width={200}
              height={200}
              className="h-[30px] w-[30px] rounded-full"
            />
          ) : (
            <div className="flex h-[30px] w-[30px] items-center justify-center rounded-full border">
              <Person className="h-[25px] w-[25px]" />
            </div>
          )}
        </button>

        <MenuPopover
          open={openPopover}
          onClose={() => setOpenPopover(null)}
          sx={{ width: 200, p: 0 }}
        >
          <div className="flex flex-col p-1">
            {[
              {
                label: 'Profile',
                linkTo: `/dashboard/view-user/${user?.staff_id}`,
              },
            ].map((option) => (
              <button
                type="button"
                key={option.label}
                onClick={() => {
                  setOpenPopover(null);
                  push(option?.linkTo);
                }}
                className="flex justify-center px-4 py-2 text-xs hover:bg-gray-300 md:py-1"
              >
                <p>{option.label}</p>
              </button>
            ))}
            <Divider sx={{ borderStyle: 'dashed' }} />
            <button
              type="button"
              onClick={() => {
                try {
                  setOpenPopover(null);
                  logout();
                } catch (error) {
                  // console.error(error);
                  enqueueSnackbar('Unable to logout!', { variant: 'error' });
                }
              }}
              className="flex justify-center px-4 py-1 text-xs hover:bg-gray-300"
            >
              <p>Logout</p>
            </button>
          </div>
        </MenuPopover>
        <div className="absolute right-full top-1/2 mx-2 flex -translate-y-1/2 transform items-center justify-center gap-2 text-xs text-white">
          {displayClock && <p className="whitespace-nowrap">{time}</p>}
          <button
            type="button"
            onClick={() =>
              push(`/support${module !== 'dashboard' ? `?module=${module}&dialogOpen=true` : ''}`)
            }
            className="joyride-dashboard-step-9 hidden rounded-lg hover:bg-gray-600 md:block"
          >
            <Tooltip title="Get Support">
              <Help className="h-[20px] w-[20px]" />
            </Tooltip>
          </button>
          <button
            type="button"
            onClick={() => {
              if (hideHeaderAndSideMenu) hideHeaderAndSideMenu();
            }}
            className="hidden rounded-lg hover:bg-gray-600 md:block"
          >
            <Tooltip title="Hide header and side menu">
              <FitScreen className="h-[20px] w-[20px]" />
            </Tooltip>
          </button>
          <button
            type="button"
            className="joyride-dashboard-step-6 rounded-lg transition-all duration-100 hover:bg-gray-600"
            onClick={() => {
              let theme_ = 'dark';
              if (['system', 'dark']?.includes(theme)) theme_ = 'light';
              setTheme(theme_);
              window?.dispatchEvent(
                new StorageEvent('storage', {
                  key: 'theme',
                  newValue: theme_,
                })
              );
            }}
          >
            {theme === 'light' ? (
              <DarkMode className="h-[20px] w-[20px]" />
            ) : (
              <LightMode className="h-[20px] w-[20px]" />
            )}
          </button>
          <button
            type="button"
            onClick={async () => {
              setRefreshButtonPulsing(true);
              await new Promise((r) => setTimeout(r, 500));
              setRefreshButtonPulsing(false);
              window.location.reload();
            }}
            className={`${refreshButtonPulsing && 'animate-pulse'}`}
          >
            <Tooltip title="Refresh">
              <Refresh className="h-[20px] w-[20px]" />
            </Tooltip>
          </button>
        </div>
      </div>
    </div>
  );
};

Header.propTypes = {
  hideHeaderAndSideMenu: PropTypes.func,
};

export default Header;
