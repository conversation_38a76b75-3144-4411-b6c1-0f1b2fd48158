export const PAYME_ENDPOINT = `/qjaRXWLnPHQa4cRaRXnitt4FMiuf20`;

export const getRoleText = (role) => {
  role = role.toLowerCase();
  if (role.includes('penolong') || role.includes('kerani') || role.includes('secretary')) {
    return 'Non-Executive';
  }
  if (role.includes('exec') || role.includes('asst')) {
    return 'Assistant Manager';
  }
  if (role.includes('mgr') || role.includes('manager')) {
    return 'Manager';
  }
  if (role.includes('agm')) {
    return 'Assistant General Manager';
  }
  if (role.includes('head')) {
    return 'General Manager';
  }
  if (role.includes('vp')) {
    return 'Vice President';
  }
  if (role.includes('evp')) {
    return 'Executive Vice President';
  }
  return 'Unknown';
};

export const getStatusStyle = (status) => {
  switch (status) {
    case 'pending':
      return 'bg-[#fff1aa]';
    case 'verified':
      return 'bg-[#a9ffb7]';
    default:
      return 'bg-white';
  }
};
