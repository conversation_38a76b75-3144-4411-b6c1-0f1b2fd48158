// Next, React, Tw
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Packages
import * as yup from 'yup';
import { useSnackbar } from 'notistack';

// Others
import axios from './axios';
import { NOTIFICATION_ENDPOINT } from './notification';
import { setIsLoading } from './store/loadingReducer';
import { getColorCode, getModuleFromPath } from './shared';

export const useSimiContext = () => {
  // Standard and Vars

  const { enqueueSnackbar } = useSnackbar();
  const dispatch = useDispatch();
  const { asPath } = useRouter();

  const { allStaffs } = useSelector((state) => state.aum);

  // Notifications Section

  const emailSchema = yup.object({
    body: yup.string().required(`Please provide body`).default('string'),
    html_body: yup.string().required(`Please provide html body`),
    recipients: yup.array().of(yup.string()).required(`Please provide recipient`),
    cc: yup.array().of(yup.string()).default([]),
    sender: yup.string().required(`Please provide sender`).default('<EMAIL>'),
    subject: yup.string().required(`Please provide subject`),
  });

  const handleSendEmail = async (recipientsList, ccsList, subject, htmlBody) => {
    let payload = {
      recipients: recipientsList,
      cc: ccsList,
      subject,
      html_body: `<!DOCTYPE html>\n<html lang="en">\n<head>\n<meta charset="UTF-8">\n<meta name="viewport" content="width=device-width, initial-scale=1.0">\n<title>Your Page Title</title>\n</head>\n<body>\n</head>\n<body>
                  ${htmlBody?.trim()}
                  </body></html>`,
    };
    try {
      payload = await emailSchema.validate(payload, { abortEarly: false });
    } catch (error) {
      enqueueSnackbar(error.errors, {
        variant: 'error',
      });
      return;
    }
    dispatch(setIsLoading(true));
    try {
      await axios.post(`${NOTIFICATION_ENDPOINT}/send_email_v2`, payload);
      enqueueSnackbar('E-mail sent successfully.', {
        variant: 'success',
      });
    } catch {
      enqueueSnackbar('Error', {
        variant: 'error',
      });
    }
    dispatch(setIsLoading(false));
  };

  // Misc Sections
  const moduleColorCode = getColorCode(getModuleFromPath(asPath));

  const getCertainStaffInfoFromStaffId = (staffId, key) =>
    allStaffs?.find((o) => o?.staff_id === staffId)?.[key];

  const getCertainStaffInfoFromName = (name, key) =>
    allStaffs?.find((o) => o?.name === name)?.[key];

  return {
    handleSendEmail,
    moduleColorCode,
    getCertainStaffInfoFromStaffId,
    getCertainStaffInfoFromName,
  };
};
