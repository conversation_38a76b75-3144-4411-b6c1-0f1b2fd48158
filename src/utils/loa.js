// Others
import axios from './axios';
import { useAuthContext } from './auth/useAuthContext';

export const LOA_ENDPOINT = `/MU5aPGbPCnHgCQM6bESQkffbXN3uEE`;

export const loaIsEditable = (status, extraStatusToBeIncluded = []) => {
  if (
    [...extraStatusToBeIncluded, 'empty', 'draft', 'reverted'].includes(status?.toLowerCase()) ||
    status?.toLowerCase()?.includes('reviewer')
  ) {
    return true;
  }
  return false;
};

export const useLoaContext = () => {
  // Standard and Vars
  const { user } = useAuthContext();

  const handleCreateHistory = async (loaId, description) => {
    try {
      await axios.post(`${LOA_ENDPOINT}/historys`, {
        loa_id: loaId,
        description,
        action_by: user?.name,
      });
    } catch {
      // console.log;
    }
  };

  return {
    handleCreateHistory,
  };
};
