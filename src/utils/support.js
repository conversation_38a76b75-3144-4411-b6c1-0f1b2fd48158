// Others
import axios from './axios';
import { useAuthContext } from './auth/useAuthContext';

export const RM_ENDPOINT = `/juNTWTyZziMhf1T35Tz3hMY37UzkQQ`;

export const useSupportContext = () => {
  const { user } = useAuthContext();

  const handleCreateHistory = async (ticketId, description) => {
    try {
      await axios.post(`${RM_ENDPOINT}/histories`, {
        action_by: user?.name,
        ticket_id: ticketId,
        description,
      });
    } catch {
      /* empty */
    }
  };

  return { handleCreateHistory };
};
