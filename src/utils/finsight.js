// Next, React, Tw
import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Packages
import * as R from 'ramda';

// Others
import { checkAndReplaceNumberWithZero } from './shared';

export const FINSIGHT_ENDPOINT = `/yz31NfRDa0TqBwnCy1Yc3ch2P9Pg56`;

export const FIGURE_TYPES_COLUMN_MAPPING = [
  {
    type: 'budget',
    dcph: 'T',
    withDcph: 'F',
  },
  {
    type: 'actual',
    dcph: 'BJ',
    withDcph: 'AV',
  },
  {
    type: 'forecast',
    dcph: 'CZ',
    withDcph: 'CL',
  },
];
export const FIGURE_TYPES = FIGURE_TYPES_COLUMN_MAPPING?.map((o) => o?.type);
export const SEGMENTS = [
  'International (GB Net)',
  'DCPH',
  'Macasa',
  'Fiberail',
  'Fibrecomm',
  'Elimination',
];
export const MONTHS_ARRAY = Array.from({ length: 12 }, (_, j) => j);
export const QUARTERS_ARRAY = Array.from({ length: 4 }, (_, j) => j + 1);

export const tidyUpNumber = (num) => checkAndReplaceNumberWithZero(num, 1, (o) => o / 1000000);

const sumArray = (arr1, arr2) => arr1.map((n, i) => n + (arr2[i] ?? 0));
const EMPTY_SOURCE = {
  ...FIGURE_TYPES?.reduce(
    (a, b) => ({
      ...a,
      [b]: {
        dcph: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        with_dcph: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        without_dcph: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      },
    }),
    {}
  ),
  name: '',
  product_code: '',
  segment: '',
  tag: '',
  type: '',
  year: '',
};

export const reconstructSourcesArray = (array, page) => {
  const uniqueCategories = R.uniq(R.pluck('category', array));
  const uniqueTags = R.uniq(R.pluck('tag', array))?.filter((o) => o !== '');
  const categoriesSources = uniqueCategories?.map((category) =>
    array
      ?.filter((o) => o?.category === category)
      ?.reduce(
        (prev, curr) => ({
          ...FIGURE_TYPES?.reduce(
            (a, b) => ({
              ...a,
              [b]: {
                dcph: sumArray(prev?.[b]?.dcph, curr?.[b]?.dcph),
                with_dcph: sumArray(prev?.[b]?.with_dcph, curr?.[b]?.with_dcph),
                without_dcph: sumArray(prev?.[b]?.without_dcph, curr?.[b]?.without_dcph),
              },
            }),
            {}
          ),
          category: curr?.category,
          name: category,
          product_code: '',
          segment: curr?.segment,
          tag: '',
          type: curr?.type,
          year: curr?.year,
        }),
        EMPTY_SOURCE
      )
  );
  const tagsSources = uniqueTags?.map((tag) =>
    array
      ?.filter((o) => o?.tag === tag)
      ?.reduce(
        (prev, curr) => ({
          ...FIGURE_TYPES?.reduce(
            (a, b) => ({
              ...a,
              [b]: {
                dcph: sumArray(prev?.[b]?.dcph, curr?.[b]?.dcph),
                with_dcph: sumArray(prev?.[b]?.with_dcph, curr?.[b]?.with_dcph),
                without_dcph: sumArray(prev?.[b]?.without_dcph, curr?.[b]?.without_dcph),
              },
            }),
            {}
          ),
          category: curr?.category,
          name: tag,
          product_code: '',
          segment: curr?.segment,
          tag: '',
          type: curr?.type,
          year: curr?.year,
        }),
        EMPTY_SOURCE
      )
  );
  const reconstructedSourcesData = [];
  for (let i = 0; i < categoriesSources?.length; i += 1) {
    reconstructedSourcesData?.push(categoriesSources[i]);
    if (['dashboard']?.includes(page)) continue;
    for (let j = 0; j < tagsSources?.length; j += 1) {
      if (tagsSources[j]?.category !== categoriesSources[i]?.name) continue;
      reconstructedSourcesData?.push(tagsSources[j]);
      if (!['monthly-breakdown']?.includes(page)) continue;
      array
        ?.filter(
          (source) =>
            source?.category === categoriesSources[i]?.name && source?.tag === tagsSources[j]?.name
        )
        ?.map((source) => reconstructedSourcesData?.push(source));
    }
    if (!['forecast-performance', 'monthly-breakdown']?.includes(page)) continue;
    array
      ?.filter((source) => source?.category === categoriesSources[i]?.name && source?.tag === '')
      ?.map((source) => reconstructedSourcesData?.push(source));
  }
  return reconstructedSourcesData;
};

export const useFinsightContext = () => {
  // Standard and Vars
  const { sourcesData } = useSelector((state) => state.finsight);
  const { query } = useRouter();
  const { q } = query;

  const filteredSourcesData = (() => {
    if ([undefined, '']?.includes(q)) {
      return sourcesData;
    }
    return sourcesData.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  return { filteredSourcesData };
};
