// Others
import axios from './axios';
import { useAuthContext } from './auth/useAuthContext';

export const FA_ENDPOINT = `/rNEDFtjfnYqxXwD1r8bNh48NTgXzVe`;

export const useFaContext = () => {
  // Standard and Vars
  const { user } = useAuthContext();

  const handleCreateHistory = async (projectId, description) => {
    try {
      await axios.post(`${FA_ENDPOINT}/history`, {
        project_id: projectId,
        description,
        action_by: user?.name,
      });
    } catch {
      /* empty */
    }
  };

  return { handleCreateHistory };
};
