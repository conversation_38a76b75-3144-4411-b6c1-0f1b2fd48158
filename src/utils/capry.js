// Next, React, Tw
import { twMerge } from 'tailwind-merge';

// Packages
import moment from 'moment';

// Others
import { toUpperCaseFirstLetter } from './shared';

export const CAPRY_ENDPOINT = `/ET9pEuS10DWEiu5AY4BkLS8rvfkYQ6`;

export const getTypeComponent = (type) => {
  if (type === 'asset') {
    return (
      <p className="rounded-lg bg-[#c1d3ff] px-2 py-1 text-xs font-semibold text-black">ASSET</p>
    );
  }
  if (type === 'jop') {
    return (
      <p className="rounded-lg bg-purple-500 px-2 py-1 text-xs font-semibold text-white">JOP</p>
    );
  }
  if (type === 'stock') {
    return (
      <p className="rounded-lg bg-[#f7b859] px-2 py-1 text-xs font-semibold text-black">STOCK</p>
    );
  }
  return <p className="rounded-lg bg-black px-2 py-1 text-xs font-semibold text-white">UNKNOWN</p>;
};

export const statusAndColorMapping = {
  activated: '#b91c1b',
  reserved: '#f97315',
  draft: '#6b7280',
  booked: '#706060',
  provisioning: '#e9b309',
  available: '#21c55e',
  total: '#000000',
};

export const getStatusBg = (status) => {
  const temp = {
    activated: `bg-[#b91c1b]`,
    reserved: `bg-[#f97315]`,
    draft: `bg-[#6b7280]`,
    booked: `bg-[#706060]`,
    provisioning: `bg-[#e9b309]`,
    available: `bg-[#21c55e]`,
  };
  return temp[status] || 'bg-black';
};

export const getStatusCircle = (status) => {
  status = status?.toLowerCase();
  return (
    <p
      className={twMerge(
        'rounded-lg bg-red-700 px-2 py-1 text-xs font-semibold text-white',
        getStatusBg(status)
      )}
    >
      {toUpperCaseFirstLetter(status)}
    </p>
  );
};

export const getStatusStyle = (status) => {
  if (status === 'open') {
    return (
      <p className="text-md rounded-full bg-yellow-700 px-2 py-1 font-semibold text-white">Open</p>
    );
  }
  if (status === 'cancelled') {
    return (
      <p className="text-md text- rounded-full bg-red-500 px-2 py-1 font-semibold text-white">
        Cancelled
      </p>
    );
  }
  if (status === 'completed') {
    return (
      <p className="text-md rounded-full bg-green-500 px-2 py-1 font-semibold text-white">
        Completed
      </p>
    );
  }
  return '';
};

export const getReservablePathStatusArray = () => ['available'];

export const getReservationAging = (date) => {
  const reservedDate = moment(date, 'YYYY-MM-DD').startOf('day');
  const today = moment().startOf('day');
  return today.diff(reservedDate, 'days');
};

export const getReservationAgingFontColor = (aging) => {
  const AGING_ALERT_DAY_NO = 90 - 7; // 3 months minus 7 days
  return aging > AGING_ALERT_DAY_NO ? 'font-bold text-red-500' : '';
};

export const getReserverRole = () => ['reserver', 'admin'];
