// Redux
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Packages
import moment from 'moment';
import * as R from 'ramda';

// Others
import { FINSIGHT_ENDPOINT, FIGURE_TYPES } from '../finsight';
import axios from '../axios';

export const fetchSourcesData = createAsyncThunk('faReducer/fetchSourcesData', async (travelId) => {
  try {
    const response = await axios.get(`${FINSIGHT_ENDPOINT}/sources/year/${moment().year()}`);
    let temp = response?.data?.data || [];
    // if (process?.env?.NODE_ENV === 'development') temp = response?.data?.data?.slice(0, 10) || [];
    let highestTs = 0;
    temp = temp?.map((o) => {
      const modifiedAt = moment(o?.modified_at)?.unix();
      if (modifiedAt > highestTs) highestTs = modifiedAt;
      return {
        ...o,
        ...FIGURE_TYPES?.reduce(
          (a, b) => ({
            ...a,
            [b]: {
              ...o?.[b],
              without_dcph: R.zipWith(R.subtract)(o?.[b]?.with_dcph, o?.[b]?.dcph),
            },
          }),
          {}
        ),
      };
    });
    return { sourcesData: temp, lastUpdatedAtUnix: highestTs };
  } catch {
    return { sourcesData: [], lastUpdatedAtUnix: 0 };
  }
});

const initialState = {
  lastUpdatedAtUnix: 0,
  sourcesData: [],
};

const finsightReducer = createSlice({
  name: 'finsightReducer',
  initialState,
  reducers: {
    setSourcesData: (state, action) => {
      state.sourcesData = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchSourcesData.fulfilled, (state, action) => {
      const { sourcesData, lastUpdatedAtUnix } = action?.payload;
      state.sourcesData = sourcesData;
      state.lastUpdatedAtUnix = lastUpdatedAtUnix;
    });
  },
});

export const { setSourcesData } = finsightReducer.actions;

export default finsightReducer.reducer;
