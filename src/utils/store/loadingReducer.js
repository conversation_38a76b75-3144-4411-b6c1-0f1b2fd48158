import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  isLoading: false,
  isSaving: false,
};

const loadingReducer = createSlice({
  name: 'loadingReducer',
  initialState,
  reducers: {
    setIsLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setIsSaving: (state, action) => {
      state.isSaving = action.payload;
    },
  },
});

export const { setIsLoading, setIsSaving } = loadingReducer.actions;

export default loadingReducer.reducer;
