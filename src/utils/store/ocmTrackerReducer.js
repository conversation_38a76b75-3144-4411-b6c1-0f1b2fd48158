// Redux
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Packages
import { OCM_TRACKER_ENDPOINT } from '../ocm-tracker';

// Others
import axios from '../axios';

export const fetchOcmsData = createAsyncThunk(
  'ocmTrackerReducer/fetchOcmsData',
  async (endpoint) => {
    try {
      const response = await axios.get(`${OCM_TRACKER_ENDPOINT}/ocm${endpoint}`);
      const temp = response?.data?.data || [];
      temp.reverse();
      return temp;
    } catch {
      return [];
    }
  }
);

export const fetchPapersData = createAsyncThunk(
  'ocmTrackerReducer/fetchPapersData',
  async (endpoint) => {
    try {
      const response = await axios.get(`${OCM_TRACKER_ENDPOINT}/paper${endpoint}`);
      const temp = response?.data?.data || [];
      temp.reverse();
      return temp;
    } catch {
      return [];
    }
  }
);

const initialState = {
  ocmsList: [],
  papersList: [],
};

const ocmTrackerReducer = createSlice({
  name: 'ocmTrackerReducer',
  initialState,
  reducers: {
    setOcmsList: (state, action) => {
      state.ocmsList = action.payload;
    },
    setPapersList: (state, action) => {
      state.papersList = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchOcmsData.fulfilled, (state, action) => {
      state.ocmsList = action.payload;
    });
    builder.addCase(fetchPapersData.fulfilled, (state, action) => {
      state.papersList = action.payload;
    });
  },
});

export const { setOcmsList, setPapersList } = ocmTrackerReducer.actions;

export default ocmTrackerReducer.reducer;
