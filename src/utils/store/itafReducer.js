// Redux
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Others
import { ITAF_ENDPOINT } from '../itaf';
import axios from '../axios';

export const fetchTravelData = createAsyncThunk('faReducer/fetchTravelData', async (travelId) => {
  try {
    const response = await axios.get(`${ITAF_ENDPOINT}/travel_id/${travelId}`);
    if (response?.data?.data)
      return {
        ...response?.data?.data?.[0],
        IS_DISABLED: response?.data?.data?.[0]?.status !== 'draft',
      };
    return {};
  } catch (error) {
    // console.log(error);
    return {};
  }
});

const initialState = {
  travelData: {},
};

const itafReducer = createSlice({
  name: 'itafReducer',
  initialState,
  reducers: {
    setTravelData: (state, action) => {
      state.travelData = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchTravelData.fulfilled, (state, action) => {
      state.travelData = action.payload;
    });
  },
});

export const { setTravelData } = itafReducer.actions;

export default itafReducer.reducer;
