import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  goToCertainPageDialogOpen: false,
};

const quranReducer = createSlice({
  name: 'quranReducer',
  initialState,
  reducers: {
    setGoToCertainPageDialogOpen: (state, action) => {
      state.goToCertainPageDialogOpen = action.payload;
    },
  },
});

export const { setGoToCertainPageDialogOpen } = quranReducer.actions;

export default quranReducer.reducer;
