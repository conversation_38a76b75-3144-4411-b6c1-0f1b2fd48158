// Redux
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Others
import { AUM_ENDPOINT } from '../aum';
import axios from '../axios';

export const fetchUserModules = createAsyncThunk('aumReducer/fetchUserModules', async (staffId) => {
  try {
    const response = await axios.get(`${AUM_ENDPOINT}/user/v1/module/all/${staffId}`);
    if (response?.data?.data) return response?.data?.data[0]?.modules;
    return [];
  } catch {
    return [];
  }
});

export const fetchUserSubModules = createAsyncThunk(
  'aumReducer/fetchUserSubModules',
  async ({ module, staffId }) => {
    try {
      const response = await axios.get(`${AUM_ENDPOINT}/user/v1/module/${module}/${staffId}`);
      return (
        response?.data?.data?.[0]?.modules[0]?.submodules
          ?.filter((o) => o?.role !== 'pending')
          ?.map((o) => o?.submodule) || []
      );
    } catch {
      return [];
    }
  }
);

export const fetchAllStaffs = createAsyncThunk('aumReducer/fetchAllStaffs', async () => {
  try {
    const response = await axios.get(`${AUM_ENDPOINT}/user/v1/all`);
    if (response?.data?.data) {
      response.data.data.reverse();
      return response?.data?.data;
    }
    return [];
  } catch {
    return [];
  }
});

const initialState = {
  userModules: [],
  userSubModules: [],
  allStaffs: [],
};

const aumReducer = createSlice({
  name: 'aumReducer',
  initialState,
  reducers: {
    setUserModules: (state, action) => {
      state.userModules = action.payload;
    },
    setAllStaffs: (state, action) => {
      state.userModules = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchUserModules.fulfilled, (state, action) => {
      state.userModules = action.payload;
    });
    builder.addCase(fetchUserSubModules.fulfilled, (state, action) => {
      state.userSubModules = action.payload;
    });
    builder.addCase(fetchAllStaffs.fulfilled, (state, action) => {
      state.allStaffs = action.payload;
    });
  },
});

export const { setUserModules, setAllStaffs } = aumReducer.actions;

export default aumReducer.reducer;
