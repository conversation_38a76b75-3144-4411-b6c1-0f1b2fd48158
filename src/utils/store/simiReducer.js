import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  page: 0,
  rowsPerPage: 20,
  breadCrumbsList: [],
};

const simiReducer = createSlice({
  name: 'simiReducer',
  initialState,
  reducers: {
    setPage: (state, action) => {
      state.page = action.payload;
    },
    setBreadCrumbsList: (state, action) => {
      state.breadCrumbsList = action.payload;
    },
  },
});

export const { setPage, setBreadCrumbsList } = simiReducer.actions;

export default simiReducer.reducer;
