import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  reservationData: {},
  requirementData: {},
};

const capryReducer = createSlice({
  name: 'capryReducer',
  initialState,
  reducers: {
    setReservationData: (state, action) => {
      state.reservationData = action.payload;
    },
    setRequirementData: (state, action) => {
      state.requirementData = action.payload;
    },
  },
});

export const { setReservationData, setRequirementData } = capryReducer.actions;

export default capryReducer.reducer;
