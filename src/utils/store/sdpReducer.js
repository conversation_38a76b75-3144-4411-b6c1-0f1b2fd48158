// Redux
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Packages
import moment from 'moment';

// Others
import { METRIC_ENDPOINT } from '../metric';
import axios from '../axios';

export const fetchOrdersData = createAsyncThunk('faReducer/fetchOrdersData', async (travelId) => {
  try {
    const response = await axios.get(`${METRIC_ENDPOINT}/orders/year/${moment().format('YYYY')}`);
    if (response?.data?.data?.[0]) {
      const temp = response?.data?.data?.[0]?.data?.map((o) => ({
        ...o,
        nrc_total_net_price: Number(o?.nrc_total_net_price),
        mrc_total_net_price: Number(o?.mrc_total_net_price),
        completion_day_gap: moment(o?.rfs_target_date).diff(o?.rfs_date, 'days'),
      }));
      return { updated_at: response?.data?.data?.[0]?.updated_at, data: temp };
    }
    return { updated_at: '', data: [] };
  } catch (error) {
    // console.log(error);
    return { updated_at: '', data: [] };
  }
});

const initialState = {
  ordersDataUpdatedAt: '',
  ordersData: [],
};

const sdpReducer = createSlice({
  name: 'sdpReducer',
  initialState,
  reducers: {
    setOrdersData: (state, action) => {
      state.ordersData = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(fetchOrdersData.fulfilled, (state, action) => {
      state.ordersDataUpdatedAt = action.payload?.updated_at;
      state.ordersData = action.payload?.data;
    });
  },
});

export const { setOrdersData } = sdpReducer.actions;

export default sdpReducer.reducer;
