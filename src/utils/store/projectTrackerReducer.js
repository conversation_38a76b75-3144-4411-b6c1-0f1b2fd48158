// Redux
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Others
import { PROJECT_TRACKER_ENDPOINT } from '../project-tracker';
import axios from '../axios';

export const fetchAllProjects = createAsyncThunk(
  'projectTracker/fetchAllProjects',
  async (filters = {}, { rejectWithValue }) => {
    try {
      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          queryParams.append(key, value);
        }
      });

      const response = await axios.get(`${PROJECT_TRACKER_ENDPOINT}/projects?${queryParams}`);
      return response?.data?.data || [];
    } catch (error) {
      console.error('Failed to fetch projects:', error);
      return rejectWithValue(error.message || 'Failed to fetch projects');
    }
  }
);

export const fetchProjectDetails = createAsyncThunk(
  'projectTracker/fetchProjectDetails',
  async (projectId, { rejectWithValue }) => {
    try {
      const [projectRes, activitiesRes, milestonesRes, risksRes, teamRes] = await Promise.all([
        axios.get(`${PROJECT_TRACKER_ENDPOINT}/projects/${projectId}`),
        axios.get(`${PROJECT_TRACKER_ENDPOINT}/projects/${projectId}/activities`),
        axios.get(`${PROJECT_TRACKER_ENDPOINT}/projects/${projectId}/milestones`),
        axios.get(`${PROJECT_TRACKER_ENDPOINT}/projects/${projectId}/risks`),
        axios.get(`${PROJECT_TRACKER_ENDPOINT}/projects/${projectId}/team`)
      ]);

      return {
        project: projectRes?.data?.data || {},
        activities: activitiesRes?.data?.data || [],
        milestones: milestonesRes?.data?.data || [],
        risks: risksRes?.data?.data || [],
        team: teamRes?.data?.data || []
      };
    } catch (error) {
      console.error('Failed to fetch project details:', error);
      return rejectWithValue(error.message || 'Failed to fetch project details');
    }
  }
);

export const fetchDashboardStats = createAsyncThunk(
  'projectTracker/fetchDashboardStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${PROJECT_TRACKER_ENDPOINT}/dashboard/stats`);
      return response?.data?.data || {};
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error);
      return rejectWithValue(error.message || 'Failed to fetch dashboard stats');
    }
  }
);

export const createProject = createAsyncThunk(
  'projectTracker/createProject',
  async (projectData, { rejectWithValue }) => {
    try {
      const response = await axios.post(`${PROJECT_TRACKER_ENDPOINT}/projects`, projectData);
      return response?.data?.data || {};
    } catch (error) {
      console.error('Failed to create project:', error);
      return rejectWithValue(error.message || 'Failed to create project');
    }
  }
);

export const updateProject = createAsyncThunk(
  'projectTracker/updateProject',
  async ({ projectId, updates }, { rejectWithValue }) => {
    try {
      const response = await axios.patch(`${PROJECT_TRACKER_ENDPOINT}/projects/${projectId}`, updates);
      return response?.data?.data || {};
    } catch (error) {
      console.error('Failed to update project:', error);
      return rejectWithValue(error.message || 'Failed to update project');
    }
  }
);

export const createMilestone = createAsyncThunk(
  'projectTracker/createMilestone',
  async ({ projectId, milestoneData }, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `${PROJECT_TRACKER_ENDPOINT}/projects/${projectId}/milestones`, 
        milestoneData
      );
      return response?.data?.data || {};
    } catch (error) {
      console.error('Failed to create milestone:', error);
      return rejectWithValue(error.message || 'Failed to create milestone');
    }
  }
);

export const createRisk = createAsyncThunk(
  'projectTracker/createRisk',
  async ({ projectId, riskData }, { rejectWithValue }) => {
    try {
      const response = await axios.post(
        `${PROJECT_TRACKER_ENDPOINT}/projects/${projectId}/risks`, 
        riskData
      );
      return response?.data?.data || {};
    } catch (error) {
      console.error('Failed to create risk:', error);
      return rejectWithValue(error.message || 'Failed to create risk');
    }
  }
);

const initialState = {
  // Data
  projects: [],
  currentProject: {},
  activities: [],
  milestones: [],
  risks: [],
  team: [],
  dashboardStats: {},
  
  // UI State
  filters: {
    status: '',
    priority: '',
    health: '',
    owner: '',
    search: ''
  },
  
  // Loading States
  loading: {
    projects: false,
    projectDetails: false,
    dashboardStats: false,
    creating: false,
    updating: false
  },
  
  // Error States
  error: {
    projects: null,
    projectDetails: null,
    dashboardStats: null,
    creating: null,
    updating: null
  }
};

const projectTrackerReducer = createSlice({
  name: 'projectTracker',
  initialState,
  reducers: {
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearCurrentProject: (state) => {
      state.currentProject = {};
      state.activities = [];
      state.milestones = [];
      state.risks = [];
      state.team = [];
    },
    addActivity: (state, action) => {
      state.activities.unshift(action.payload);
    },
    updateProjectProgress: (state, action) => {
      const { projectId, progress } = action.payload;
      if (state.currentProject.id === projectId) {
        state.currentProject.progress_percentage = progress;
      }
      const projectIndex = state.projects.findIndex(p => p.id === projectId);
      if (projectIndex !== -1) {
        state.projects[projectIndex].progress_percentage = progress;
      }
    }
  },
  extraReducers: (builder) => {
    // Fetch All Projects
    builder
      .addCase(fetchAllProjects.pending, (state) => {
        state.loading.projects = true;
        state.error.projects = null;
      })
      .addCase(fetchAllProjects.fulfilled, (state, action) => {
        state.loading.projects = false;
        state.projects = action.payload;
        state.error.projects = null;
      })
      .addCase(fetchAllProjects.rejected, (state, action) => {
        state.loading.projects = false;
        state.error.projects = action.payload;
        state.projects = [];
      });

    // Fetch Project Details
    builder
      .addCase(fetchProjectDetails.pending, (state) => {
        state.loading.projectDetails = true;
        state.error.projectDetails = null;
      })
      .addCase(fetchProjectDetails.fulfilled, (state, action) => {
        state.loading.projectDetails = false;
        state.currentProject = action.payload.project;
        state.activities = action.payload.activities;
        state.milestones = action.payload.milestones;
        state.risks = action.payload.risks;
        state.team = action.payload.team;
        state.error.projectDetails = null;
      })
      .addCase(fetchProjectDetails.rejected, (state, action) => {
        state.loading.projectDetails = false;
        state.error.projectDetails = action.payload;
      });

    // Dashboard Stats
    builder
      .addCase(fetchDashboardStats.pending, (state) => {
        state.loading.dashboardStats = true;
        state.error.dashboardStats = null;
      })
      .addCase(fetchDashboardStats.fulfilled, (state, action) => {
        state.loading.dashboardStats = false;
        state.dashboardStats = action.payload;
        state.error.dashboardStats = null;
      })
      .addCase(fetchDashboardStats.rejected, (state, action) => {
        state.loading.dashboardStats = false;
        state.error.dashboardStats = action.payload;
      });

    // Create Project
    builder
      .addCase(createProject.pending, (state) => {
        state.loading.creating = true;
        state.error.creating = null;
      })
      .addCase(createProject.fulfilled, (state, action) => {
        state.loading.creating = false;
        state.projects.unshift(action.payload);
        state.error.creating = null;
      })
      .addCase(createProject.rejected, (state, action) => {
        state.loading.creating = false;
        state.error.creating = action.payload;
      });

    // Update Project
    builder
      .addCase(updateProject.pending, (state) => {
        state.loading.updating = true;
        state.error.updating = null;
      })
      .addCase(updateProject.fulfilled, (state, action) => {
        state.loading.updating = false;
        const updatedProject = action.payload;
        
        // Update current project if it matches
        if (state.currentProject.id === updatedProject.id) {
          state.currentProject = { ...state.currentProject, ...updatedProject };
        }
        
        // Update in projects list
        const projectIndex = state.projects.findIndex(p => p.id === updatedProject.id);
        if (projectIndex !== -1) {
          state.projects[projectIndex] = { ...state.projects[projectIndex], ...updatedProject };
        }
        
        state.error.updating = null;
      })
      .addCase(updateProject.rejected, (state, action) => {
        state.loading.updating = false;
        state.error.updating = action.payload;
      });

    // Create Milestone
    builder
      .addCase(createMilestone.fulfilled, (state, action) => {
        state.milestones.push(action.payload);
      });

    // Create Risk
    builder
      .addCase(createRisk.fulfilled, (state, action) => {
        state.risks.push(action.payload);
      });
  }
});

export const { 
  setFilters, 
  clearCurrentProject, 
  addActivity, 
  updateProjectProgress 
} = projectTrackerReducer.actions;

export default projectTrackerReducer.reducer;