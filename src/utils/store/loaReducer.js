import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  loaData: {},
  loaTcvData: {},
};

const loaReducer = createSlice({
  name: 'loaReducer',
  initialState,
  reducers: {
    setLoaData: (state, action) => {
      state.loaData = action.payload;
    },
    setLoaTcvData: (state, action) => {
      state.loaTcvData = action.payload;
    },
  },
});

export const { setLoaData, setLoaTcvData } = loaReducer.actions;

export default loaReducer.reducer;
