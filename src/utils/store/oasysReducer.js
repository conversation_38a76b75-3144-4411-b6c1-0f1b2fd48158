// Redux
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  role: null,
  dataArray: [],
  jobsTableData: [],
  tasksTableData: [],
  originalJobData: {},
  allProcessors: [],
};

const oasysReducer = createSlice({
  name: 'oasysReducer',
  initialState,
  reducers: {
    setRole: (state, action) => {
      state.role = action.payload;
    },
    setJobDataArray: (state, action) => {
      state.dataArray = action.payload;
    },
    setJobsTableData: (state, action) => {
      state.jobsTableData = action.payload;
    },
    setTasksTableData: (state, action) => {
      state.tasksTableData = action.payload;
    },
    setOriginalJobData: (state, action) => {
      state.originalJobData = action.payload;
    },
    setAllProcessors: (state, action) => {
      state.allProcessors = action.payload;
    },
  },
});

export const {
  setRole,
  setJobDataArray,
  setJobsTableData,
  setTasksTableData,
  setOriginalJobData,
  setAllProcessors,
} = oasysReducer.actions;

export default oasysReducer.reducer;
