import { configureStore } from '@reduxjs/toolkit';

// Reducers
import simiReducer from './simiReducer';
import aumReducer from './aumReducer';
import loaReducer from './loaReducer';
import quranReducer from './quranReducer';
import capryReducer from './capryReducer';
import loadingReducer from './loadingReducer';
import faReducer from './faReducer';
import luckyDrawReducer from './luckyDrawReducer';
import itafReducer from './itafReducer';
import sdpReducer from './sdpReducer';
import oasysReducer from './oasysReducer';
import finsightReducer from './finsightReducer';
import ocmTrackerReducer from './ocmTrackerReducer';
import projectTrackerReducer from './projectTrackerReducer';

const store = configureStore({
  reducer: {
    loading: loadingReducer,
    simi: simiReducer,
    aum: aumReducer,
    loa: loaReducer,
    quran: quranReducer,
    capry: capryReducer,
    fa: faReducer,
    luckyDraw: luckyDrawReducer,
    itaf: itafReducer,
    sdp: sdpReducer,
    oasys: oasysReducer,
    finsight: finsightReducer,
    ocmTracker: ocmTrackerReducer,
    projectTracker: projectTrackerReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export default store;
