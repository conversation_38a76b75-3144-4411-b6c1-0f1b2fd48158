// Redux
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Others
import { FA_ENDPOINT } from '../fa';
import axios from '../axios';

export const fetchProjectData = createAsyncThunk(
  'faReducer/fetchProjectData',
  async (projectId, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${FA_ENDPOINT}/project/id/${projectId}`);
      
      if (response?.data?.data?.[0]) {
        const projectData = response.data.data[0];
        
        return {
          ...projectData,
          IS_DISABLED: projectData?.status !== 'draft',
        };
      }
      throw new Error('Project not found');
    } catch (error) {
      console.error('Failed to fetch project data:', error);
      return rejectWithValue(error.message || 'Failed to fetch project');
    }
  }
);

export const fetchAllScenarios = createAsyncThunk(
  'faReducer/fetchAllScenarios',
  async (projectId, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${FA_ENDPOINT}/scenario/project_id/${projectId}`);
      const temp = response?.data?.data || [];
      return temp;
    } catch (error) {
      console.error('Failed to fetch scenarios:', error);
      return rejectWithValue(error.message || 'Failed to fetch scenarios');
    }
  }
);

export const fetchScenarioData = createAsyncThunk(
  'faReducer/fetchScenarioData',
  async (scenarioId, { rejectWithValue }) => {
    try {
      const response = await axios.get(`${FA_ENDPOINT}/scenario/id/${scenarioId}`);
      if (response?.data?.data) return response?.data?.data?.[0];
      throw new Error('Scenario not found');
    } catch (error) {
      console.error('Failed to fetch scenario data:', error);
      return rejectWithValue(error.message || 'Failed to fetch scenario');
    }
  }
);

const initialState = {
  projectData: {},
  allScenarios: [],
  scenarioData: {},
  loading: {
    projectData: false,
    allScenarios: false,
    scenarioData: false,
  },
  error: {
    projectData: null,
    allScenarios: null,
    scenarioData: null,
  },
};

const faReducer = createSlice({
  name: 'faReducer',
  initialState,
  reducers: {
    setProjectData: (state, action) => {
      state.projectData = action.payload;
    },
    setScenarioData: (state, action) => {
      state.scenarioData = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Project Data
    builder
      .addCase(fetchProjectData.pending, (state) => {
        state.loading.projectData = true;
        state.error.projectData = null;
      })
      .addCase(fetchProjectData.fulfilled, (state, action) => {
        state.loading.projectData = false;
        state.projectData = action.payload;
        state.error.projectData = null;
      })
      .addCase(fetchProjectData.rejected, (state, action) => {
        state.loading.projectData = false;
        state.error.projectData = action.payload;
        state.projectData = {};
      });

    // All Scenarios
    builder
      .addCase(fetchAllScenarios.pending, (state) => {
        state.loading.allScenarios = true;
        state.error.allScenarios = null;
      })
      .addCase(fetchAllScenarios.fulfilled, (state, action) => {
        state.loading.allScenarios = false;
        state.allScenarios = action.payload;
        state.error.allScenarios = null;
      })
      .addCase(fetchAllScenarios.rejected, (state, action) => {
        state.loading.allScenarios = false;
        state.error.allScenarios = action.payload;
        state.allScenarios = [];
      });

    // Scenario Data
    builder
      .addCase(fetchScenarioData.pending, (state) => {
        state.loading.scenarioData = true;
        state.error.scenarioData = null;
      })
      .addCase(fetchScenarioData.fulfilled, (state, action) => {
        state.loading.scenarioData = false;
        state.scenarioData = action.payload;
        state.error.scenarioData = null;
      })
      .addCase(fetchScenarioData.rejected, (state, action) => {
        state.loading.scenarioData = false;
        state.error.scenarioData = action.payload;
        state.scenarioData = {};
      });
  },
});

export const { setProjectData, setScenarioData } = faReducer.actions;

export default faReducer.reducer;
