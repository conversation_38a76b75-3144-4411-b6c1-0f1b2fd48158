// Simple but effective input sanitization utility
const sanitizeInput = (input) => {
  if (!input || typeof input !== 'string') return input;
  
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
};

const sanitizeNumber = (input) => {
  if (input === null || input === undefined) return 0;
  const num = Number(input);
  return Number.isFinite(num) ? num : 0;
};

const sanitizeObject = (obj) => {
  if (!obj || typeof obj !== 'object') return obj;
  
  const sanitized = {};
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'string') {
      sanitized[key] = sanitizeInput(value);
    } else if (typeof value === 'number') {
      sanitized[key] = sanitizeNumber(value);
    } else {
      sanitized[key] = value;
    }
  }
  return sanitized;
};

export { sanitizeInput, sanitizeNumber, sanitizeObject };