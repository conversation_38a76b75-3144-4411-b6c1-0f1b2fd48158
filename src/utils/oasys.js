// Next, React, Tw
import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Packages
import * as yup from 'yup';
import * as R from 'ramda';

// Others
import { useAuthContext } from './auth/useAuthContext';
import axios from './axios';

export const OASYS_ENDPOINT = `/vuPxkH6hpSHY3qKh0ZVgBVZMUrCRdv`;

export const useOasysContext = () => {
  // Standard and Vars
  const { user } = useAuthContext();
  const { jobsTableData, tasksTableData } = useSelector((state) => state.oasys);
  const { query } = useRouter();
  const { q, status, projectId, tableDataFrom, tableDataTo } = query;

  const jobScema = yup.object({
    status: yup.string(),
    on_hold_end_date: yup.number(),
    remarks: yup.string(),
    counters: yup.number().required(`Please provide number of tasks`),
    created_by_staff_id: yup.string().required(`Please provide created by`).default(user?.staff_id),
  });

  const sharedTaskSchema = {
    status: yup.string().required(`Please provide status`).default('pending review'),
    created_by_staff_id: yup.string().required(`Please provide created by`).default(user?.staff_id),
  };
  const newTaskSchema = yup.object(sharedTaskSchema);
  const saveTaskSchema = yup.object({
    processor_staff_id: yup.string().required(`Please provide processor`),
    ...sharedTaskSchema,
  });

  const jobStatusValueMap = [
    ...Object?.keys(JOB_STATUS_STYLE_MAP)?.map((o) => ({
      label: o,
      value: jobsTableData?.filter((p) => p?.status === o)?.length,
    })),
    { label: 'all', value: jobsTableData?.length },
  ];
  const taskStatusValueMap = [
    ...Object?.keys(TASK_STATUS_STYLE_MAP)?.map((o) => ({
      label: o,
      value: tasksTableData?.filter((p) => p?.status === o)?.length,
    })),
    { label: 'all', value: tasksTableData?.length },
  ];

  const filteredJobsTableData = (() => {
    let temp = jobsTableData;
    if (status && status !== 'all') temp = temp?.filter((o) => o?.status === status);
    if (projectId && projectId !== 'all') temp = temp?.filter((o) => o?.project_id === projectId);
    if (tableDataFrom && tableDataTo) {
      temp = temp?.filter((o) => {
        const date = o?.created_at;
        return date >= tableDataFrom && date <= tableDataTo;
      });
    }
    if ([undefined, '']?.includes(q)) {
      return temp;
    }
    return temp.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  const filteredTasksTableData = (() => {
    let temp = tasksTableData;
    if (status && status !== 'all') temp = temp?.filter((o) => o?.status === status);
    if (tableDataFrom && tableDataTo) {
      temp = temp?.filter((o) => {
        const date = o?.created_at;
        return date >= tableDataFrom && date <= tableDataTo;
      });
    }
    if ([undefined, '']?.includes(q)) {
      return temp;
    }
    return temp.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  const uniqueProcessors = R.uniq(R.pluck('processor_staff_id', jobsTableData))?.filter(
    (o) => o !== ''
  );

  const handleCreateHistory = async (taskId, description) => {
    try {
      await axios.post(`${OASYS_ENDPOINT}/historys`, {
        task_id: taskId,
        description,
        action_by: user?.name,
      });
    } catch {
      /* empty */
    }
  };

  return {
    jobScema,
    newTaskSchema,
    saveTaskSchema,
    handleCreateHistory,
    jobStatusValueMap,
    taskStatusValueMap,
    filteredJobsTableData,
    filteredTasksTableData,
    uniqueProcessors,
  };
};

export const JOB_STATUS_STYLE_MAP = {
  open: 'bg-yellow-500 text-white',
  'on hold': 'bg-purple-500 text-white',
  rejected: 'bg-gray-500 text-white',
  completed: 'bg-green-500 text-white',
};

export const TASK_STATUS_STYLE_MAP = {
  'pending review': 'bg-yellow-500 text-white',
  rejected: 'bg-gray-500 text-white',
  'in progress': 'bg-blue-500 text-white',
  blocked: 'bg-red-500 text-white',
  canceled: 'bg-gray-500 text-white',
  completed: 'bg-green-500 text-white',
};

export const getJobStatusStyle = (status) =>
  JOB_STATUS_STYLE_MAP?.[status] || 'bg-black text-white';

export const getTaskStatusStyle = (status) =>
  TASK_STATUS_STYLE_MAP?.[status] || 'bg-black text-white';

export const PROCESSOR_UPDATES_KEY_LABEL_MAP = [
  {
    label: 'Order No.',
    key: 'order_no',
  },
  {
    label: 'Service ID',
    key: 'service_id',
  },
  {
    label: 'Quote No.',
    key: 'quote_no',
  },
  {
    label: 'Billing No.',
    key: 'billing_no',
  },
  {
    label: 'Sphere ID',
    key: 'sphere_id',
  },
  {
    label: 'RFS Date',
    key: 'rfs_date',
  },
  {
    label: 'RFT Date',
    key: 'rft_date',
  },
];

export const JOB_TYPE_KEY_LABEL_MAP = {
  'NOVA New Install': [
    {
      label: 'Date',
      key: 'Date',
    },
    {
      label: 'Project ID',
      key: 'Project ID',
    },
    {
      label: 'Site ID/Site Name',
      key: 'Site ID/Site Name',
    },
    {
      label: 'Quote Name',
      key: 'Quote Name',
    },
    {
      label: 'Order Type',
      key: 'Order Type',
    },
    {
      label: 'Customer Name',
      key: 'Customer Name',
    },
    {
      label: 'Account No',
      key: 'Account No',
    },
    {
      label: 'Segment/Unit',
      key: 'Segment/Unit',
      options:
        'Celcom & Webe,Maxis Group,Digi & U Mobile,Edotco & YTL,Reseller 1,Reseller 2,Carrier Sales Central,Carrier Sales Eastern,Carrier Sales Northern,Carrier Sales Southern,Carrier Sales Sabah,Carrier Sales Sarawak',
    },
    {
      label: 'Product/Service',
      key: 'Product/Service',
    },
    {
      label: 'Speed',
      key: 'Speed',
    },
    {
      label: 'Billing Account No',
      key: 'Billing Account No',
    },
    {
      label: 'Leg A Price Per Leg Per Billing Frequency',
      key: 'Leg A Price Per Leg Per Billing Frequency',
    },
    {
      label: 'Leg A Billing Frequency',
      key: 'Leg A Billing Frequency',
      options: 'Monthly,Quarterly,Half-yearly,Yearly',
    },
    {
      label: 'Leg B Price Per Leg Per Billing Frequency',
      key: 'Leg B Price Per Leg Per Billing Frequency',
    },
    {
      label: 'Leg B Billing Frequency',
      key: 'Leg B Billing Frequency',
      options: 'Monthly,Quarterly,Half-yearly,Yearly',
    },
    {
      label: 'Contract Term',
      key: 'Contract Term',
    },
    {
      label: 'RFS Date',
      key: 'RFS Date',
    },
    {
      label: 'One Time Charge',
      key: 'One Time Charge',
    },
    {
      label: 'Sphere ID',
      key: 'Sphere ID',
    },
    {
      label: 'Additional Remark',
      key: 'Additional Remark',
    },
  ],
  'NOVA Modify': [
    {
      label: 'Date',
      key: 'Date',
    },
    {
      label: 'Customer Name',
      key: 'Customer Name',
    },
    {
      label: 'Segment/Unit',
      key: 'Segment/Unit',
      options:
        'Celcom & Webe,Maxis Group,Digi & U Mobile,Edotco & YTL,Reseller 1,Reseller 2,Carrier Sales Central,Carrier Sales Eastern,Carrier Sales Northern,Carrier Sales Southern,Carrier Sales Sabah,Carrier Sales Sarawak',
    },
    {
      label: 'Product/Service',
      key: 'Product/Service',
    },
    {
      label: 'Order Type',
      key: 'Order Type',
    },
    {
      label: 'Order Sub Type',
      key: 'Order Sub Type',
    },
    {
      label: 'Site ID/Site Name',
      key: 'Site ID/Site Name',
    },
    {
      label: 'Quote Name',
      key: 'Quote Name',
    },
    {
      label: 'Existing Service ID',
      key: 'Existing Service ID',
    },
    {
      label: 'Speed',
      key: 'Speed',
    },
    {
      label: 'Billing Account No',
      key: 'Billing Account No',
    },
    {
      label: 'New Leg A Price Per Leg Per Billing Frequency',
      key: 'New Leg A Price Per Leg Per Billing Frequency',
    },
    {
      label: 'New Leg A Billing Frequency',
      key: 'New Leg A Billing Frequency',
      options: 'Monthly,Quarterly,Half-yearly,Yearly',
    },
    {
      label: 'New Leg B Price Per Leg Per Billing Frequency',
      key: 'New Leg B Price Per Leg Per Billing Frequency',
    },
    {
      label: 'New Leg B Billing Frequency',
      key: 'New Leg B Billing Frequency',
      options: 'Monthly,Quarterly,Half-yearly,Yearly',
    },
    {
      label: 'RFS Date',
      key: 'RFS Date',
    },
    {
      label: 'One Time Charge',
      key: 'One Time Charge',
    },
    {
      label: 'Sphere ID',
      key: 'Sphere ID',
    },
    {
      label: 'Additional Remark',
      key: 'Additional Remark',
    },
  ],
  'NOVA Terminate': [
    {
      label: 'Date',
      key: 'Date',
    },
    {
      label: 'Customer Name',
      key: 'Customer Name',
    },
    {
      label: 'Segment/Unit',
      key: 'Segment/Unit',
      options:
        'Celcom & Webe,Maxis Group,Digi & U Mobile,Edotco & YTL,Reseller 1,Reseller 2,Carrier Sales Central,Carrier Sales Eastern,Carrier Sales Northern,Carrier Sales Southern,Carrier Sales Sabah,Carrier Sales Sarawak',
    },
    {
      label: 'Product/Service',
      key: 'Product/Service',
    },
    {
      label: 'Existing Service ID',
      key: 'Existing Service ID',
    },
    {
      label: 'Billing Account No',
      key: 'Billing Account No',
    },
    {
      label: 'Order Type',
      key: 'Order Type',
    },
    {
      label: 'Waive ETC (Yes = Waive / No = Charge ETC)',
      key: 'Waive ETC (Yes = Waive / No = Charge ETC)',
    },
    {
      label: 'Collect Equipment? (Yes/No)',
      key: 'Collect Equipment? (Yes/No)',
    },
    {
      label: 'RFT Date',
      key: 'RFT Date',
    },
    {
      label: 'Customer Contact Person',
      key: 'Customer Contact Person',
    },
    {
      label: 'Customer Contact No',
      key: 'Customer Contact No',
    },
    {
      label: 'Customer Contact Email',
      key: 'CustomerContactEmail',
    },
    {
      label: 'Additional Remark',
      key: 'Additional Remark',
    },
  ],
  'NOVA NI Infra': [
    {
      label: 'Date',
      key: 'Date',
    },
    {
      label: 'Customer Name',
      key: 'Customer Name',
    },
    {
      label: 'Segment/Unit',
      key: 'Segment/Unit',
      options:
        'Celcom & Webe,Maxis Group,Digi & U Mobile,Edotco & YTL,Reseller 1,Reseller 2,Carrier Sales Central,Carrier Sales Eastern,Carrier Sales Northern,Carrier Sales Southern,Carrier Sales Sabah,Carrier Sales Sarawak',
    },
    {
      label: 'Order Type',
      key: 'Order Type',
    },
    {
      label: 'Quote Name',
      key: 'Quote Name',
    },
    {
      label: 'RFS Date',
      key: 'RFS Date',
    },
    {
      label: 'Billing Account No',
      key: 'Billing Account No',
    },
    {
      label: 'TP Reference',
      key: 'TPReference',
    },
    {
      label: 'Work Permit No',
      key: 'WorkPermitNo',
    },
    {
      label: 'Tower Space',
      key: 'TowerSpace',
    },
    {
      label: 'Tower Type',
      key: 'TowerType',
    },
    {
      label: 'Price Tower Space',
      key: 'PriceTowerSpace',
    },
    {
      label: 'Additional Antenna',
      key: 'AdditionalAntenna',
    },
    {
      label: 'Price Additional Antenna',
      key: 'PriceAdditionalAntenna',
    },
    {
      label: 'Additional Dish',
      key: 'AdditionalDish',
    },
    {
      label: 'Price Additional Dish',
      key: 'PriceAdditionalDish',
    },
    {
      label: 'Land Space',
      key: 'LandSpace',
    },
    {
      label: 'Price Land Space',
      key: 'PriceLandSpace',
    },
    {
      label: 'Floor Space',
      key: 'FloorSpace',
    },
    {
      label: 'Price Floor Space',
      key: 'PriceFloorSpace',
    },
    {
      label: 'Electricity Genset',
      key: 'ElectricityGenset',
    },
    {
      label: 'Price Electricity Genset',
      key: 'PriceElectricityGenset',
    },
    {
      label: 'Total Charges in Nova',
      key: 'TotalChargesInNova',
    },
    {
      label: 'Contract Term',
      key: 'Contract Term',
    },
    {
      label: 'One Time Charge',
      key: 'One Time Charge',
    },
    {
      label: 'Ref No',
      key: 'Ref No',
    },
    {
      label: 'Installation Address',
      key: 'Installation Address',
    },
    {
      label: 'Billing Address',
      key: 'Billing Address',
    },
    {
      label: 'Customer Contact Person',
      key: 'Customer Contact Person',
    },
    {
      label: 'Customer Contact No',
      key: 'Customer Contact No',
    },
    {
      label: 'Customer Contact Email',
      key: 'CustomerContactEmail',
    },
    {
      label: 'Additional Remark',
      key: 'Additional Remark',
    },
    {
      label: 'Service ID',
      key: 'ServiceID',
    },
  ],
  'NOVA Modify Infra': [
    {
      label: 'Date',
      key: 'Date',
    },
    {
      label: 'Customer Name',
      key: 'Customer Name',
    },
    {
      label: 'Segment/Unit',
      key: 'Segment/Unit',
      options:
        'Celcom & Webe,Maxis Group,Digi & U Mobile,Edotco & YTL,Reseller 1,Reseller 2,Carrier Sales Central,Carrier Sales Eastern,Carrier Sales Northern,Carrier Sales Southern,Carrier Sales Sabah,Carrier Sales Sarawak',
    },
    {
      label: 'Order Type',
      key: 'Order Type',
    },
    {
      label: 'RFS Date',
      key: 'RFS Date',
    },
    {
      label: 'Quote Name',
      key: 'Quote Name',
    },
    {
      label: 'Billing Account No',
      key: 'Billing Account No',
    },
    {
      label: 'TP Reference',
      key: 'TPReference',
    },
    {
      label: 'Work Permit No',
      key: 'WorkPermitNo',
    },
    {
      label: 'Tower Space',
      key: 'TowerSpace',
    },
    {
      label: 'Tower Type',
      key: 'TowerType',
    },
    {
      label: 'Price Tower Space',
      key: 'PriceTowerSpace',
    },
    {
      label: 'Additional Antenna',
      key: 'AdditionalAntenna',
    },
    {
      label: 'Price Additional Antenna',
      key: 'PriceAdditionalAntenna',
    },
    {
      label: 'Additional Dish',
      key: 'AdditionalDish',
    },
    {
      label: 'Price Additional Dish',
      key: 'PriceAdditionalDish',
    },
    {
      label: 'Land Space',
      key: 'LandSpace',
    },
    {
      label: 'Price Land Space',
      key: 'PriceLandSpace',
    },
    {
      label: 'Floor Space',
      key: 'FloorSpace',
    },
    {
      label: 'Price Floor Space',
      key: 'PriceFloorSpace',
    },
    {
      label: 'Electricity Genset',
      key: 'ElectricityGenset',
    },
    {
      label: 'Price Electricity Genset',
      key: 'PriceElectricityGenset',
    },
    {
      label: 'Total Charges in Nova',
      key: 'TotalChargesInNova',
    },
    {
      label: 'Contract Term',
      key: 'Contract Term',
    },
    {
      label: 'One Time Charge',
      key: 'One Time Charge',
    },
    {
      label: 'Ref No',
      key: 'Ref No',
    },
    {
      label: 'Installation Address',
      key: 'Installation Address',
    },
    {
      label: 'Billing Address',
      key: 'Billing Address',
    },
    {
      label: 'Customer Contact Person',
      key: 'Customer Contact Person',
    },
    {
      label: 'Customer Contact No',
      key: 'Customer Contact No',
    },
    {
      label: 'Customer Contact Email',
      key: 'CustomerContactEmail',
    },
    {
      label: 'Additional Remark',
      key: 'Additional Remark',
    },
    {
      label: 'Service ID',
      key: 'ServiceID',
    },
  ],
  'NOVA Terminate Infra': [
    {
      label: 'Date',
      key: 'Date',
    },
    {
      label: 'Customer Name',
      key: 'Customer Name',
    },
    {
      label: 'Segment/Unit',
      key: 'Segment/Unit',
      options:
        'Celcom & Webe,Maxis Group,Digi & U Mobile,Edotco & YTL,Reseller 1,Reseller 2,Carrier Sales Central,Carrier Sales Eastern,Carrier Sales Northern,Carrier Sales Southern,Carrier Sales Sabah,Carrier Sales Sarawak',
    },
    {
      label: 'Waive ETC (Yes = Waive / No = Charge ETC)',
      key: 'Waive ETC (Yes = Waive / No = Charge ETC)',
    },
    {
      label: 'Order Type',
      key: 'Order Type',
    },
    {
      label: 'Quote Name',
      key: 'Quote Name',
    },
    {
      label: 'New Leg A Price Per Leg Per Month',
      key: 'New LegAPricePerLegPerMonth',
    },
    {
      label: 'New Leg B Price Per Leg Per Month',
      key: 'New LegBPricePerLegPerMonth',
    },
    {
      label: 'RFS Date',
      key: 'RFS Date',
    },
    {
      label: 'Billing Termination Date',
      key: 'BillingTerminationDate',
    },
    {
      label: 'One Time Charge',
      key: 'One Time Charge',
    },
    {
      label: 'Sphere ID',
      key: 'Sphere ID',
    },
    {
      label: 'Additional Remark',
      key: 'Additional Remark',
    },
    {
      label: 'Customer Contact Person',
      key: 'Customer Contact Person',
    },
    {
      label: 'Customer Contact No',
      key: 'Customer Contact No',
    },
    {
      label: 'Customer Contact Email',
      key: 'CustomerContactEmail',
    },
    {
      label: 'Service ID',
      key: 'ServiceID',
    },
  ],
  'NOVA Unifi New Install': [
    {
      label: 'Date',
      key: 'Date',
    },
    {
      label: 'Ref No',
      key: 'Ref No',
    },
    {
      label: 'Customer Name',
      key: 'Customer Name',
    },
    {
      label: 'BR No',
      key: 'BRNo',
    },
    {
      label: 'Login ID',
      key: 'Login ID',
    },
    {
      label: 'Segment/Unit',
      key: 'Segment/Unit',
      options:
        'Celcom & Webe,Maxis Group,Digi & U Mobile,Edotco & YTL,Reseller 1,Reseller 2,Carrier Sales Central,Carrier Sales Eastern,Carrier Sales Northern,Carrier Sales Southern,Carrier Sales Sabah,Carrier Sales Sarawak',
    },
    {
      label: 'Product/Service',
      key: 'Product/Service',
    },
    {
      label: 'Order Type',
      key: 'Order Type',
    },
    {
      label: 'Billing Account No',
      key: 'Billing Account No',
    },
    {
      label: 'Monthly Recurring Charge',
      key: 'Monthly Recurring Charge',
    },
    {
      label: 'Installation Address',
      key: 'Installation Address',
    },
    {
      label: 'Billing Address',
      key: 'Billing Address',
    },
    {
      label: 'Appointment Date',
      key: 'AppointmentDate',
    },
    {
      label: 'One Time Charge',
      key: 'One Time Charge',
    },
    {
      label: 'Additional Remark',
      key: 'Additional Remark',
    },
    {
      label: 'Customer Contact Person',
      key: 'Customer Contact Person',
    },
    {
      label: 'Customer Contact No',
      key: 'Customer Contact No',
    },
    {
      label: 'Contract Term',
      key: 'Contract Term',
    },
  ],
  'NOVA Unifi Modify': [
    {
      label: 'Date',
      key: 'Date',
    },
    {
      label: 'Ref No',
      key: 'Ref No',
    },
    {
      label: 'Service ID',
      key: 'ServiceID',
    },
    {
      label: 'Login ID',
      key: 'Login ID',
    },
    {
      label: 'Customer Name',
      key: 'Customer Name',
    },
    {
      label: 'Account No',
      key: 'Account No',
    },
    {
      label: 'Segment/Unit',
      key: 'Segment/Unit',
      options:
        'Celcom & Webe,Maxis Group,Digi & U Mobile,Edotco & YTL,Reseller 1,Reseller 2,Carrier Sales Central,Carrier Sales Eastern,Carrier Sales Northern,Carrier Sales Southern,Carrier Sales Sabah,Carrier Sales Sarawak',
    },
    {
      label: 'Product/Service',
      key: 'Product/Service',
    },
    {
      label: 'Order Type',
      key: 'Order Type',
    },
    {
      label: 'Order Sub Type',
      key: 'Order Sub Type',
    },
    {
      label: 'Monthly Recurring Charge',
      key: 'Monthly Recurring Charge',
    },
    {
      label: 'Billing Account No',
      key: 'Billing Account No',
    },
    {
      label: 'Billing Address',
      key: 'Billing Address',
    },
    {
      label: 'RFS Date',
      key: 'RFS Date',
    },
    {
      label: 'One Time Charge',
      key: 'One Time Charge',
    },
    {
      label: 'Customer Contact Person',
      key: 'Customer Contact Person',
    },
    {
      label: 'Customer Contact No',
      key: 'Customer Contact No',
    },
    {
      label: 'Installation Address',
      key: 'Installation Address',
    },
    {
      label: 'Additional Remark',
      key: 'Additional Remark',
    },
    {
      label: 'Contract Term',
      key: 'Contract Term',
    },
  ],
  'NOVA Unifi Terminate': [
    {
      label: 'Date',
      key: 'Date',
    },
    {
      label: 'Ref No',
      key: 'Ref No',
    },
    {
      label: 'Service ID',
      key: 'ServiceID',
    },
    {
      label: 'Login ID',
      key: 'Login ID',
    },
    {
      label: 'Customer Name',
      key: 'Customer Name',
    },
    {
      label: 'Customer Account Number',
      key: 'CustomerAccountNumber',
    },
    {
      label: 'Segment/Unit',
      key: 'Segment/Unit',
      options:
        'Celcom & Webe,Maxis Group,Digi & U Mobile,Edotco & YTL,Reseller 1,Reseller 2,Carrier Sales Central,Carrier Sales Eastern,Carrier Sales Northern,Carrier Sales Southern,Carrier Sales Sabah,Carrier Sales Sarawak',
    },
    {
      label: 'Product/Service',
      key: 'Product/Service',
    },
    {
      label: 'Waive ETC (Yes = Waive / No = Charge ETC)',
      key: 'Waive ETC (Yes = Waive / No = Charge ETC)',
    },
    {
      label: 'Collect Equipment? (Yes/No)',
      key: 'Collect Equipment? (Yes/No)',
    },
    {
      label: 'Order Sub Type',
      key: 'Order Sub Type',
    },
    {
      label: 'Billing Account No',
      key: 'Billing Account No',
    },
    {
      label: 'RFS Date',
      key: 'RFS Date',
    },
    {
      label: 'Customer Contact Person',
      key: 'Customer Contact Person',
    },
    {
      label: 'Customer Contact No',
      key: 'Customer Contact No',
    },
    {
      label: 'Additional Remark',
      key: 'Additional Remark',
    },
    {
      label: 'Contract Term',
      key: 'Contract Term',
    },
  ],
  'ICP New Install': [
    {
      label: 'Date',
      key: 'Date',
    },
    {
      label: 'Customer Name',
      key: 'Customer Name',
    },
    {
      label: 'Account No',
      key: 'Account No',
    },
    {
      label: 'Segment/Unit',
      key: 'Segment/Unit',
      options:
        'Celcom & Webe,Maxis Group,Digi & U Mobile,Edotco & YTL,Reseller 1,Reseller 2,Carrier Sales Central,Carrier Sales Eastern,Carrier Sales Northern,Carrier Sales Southern,Carrier Sales Sabah,Carrier Sales Sarawak',
    },
    {
      label: 'Order Type',
      key: 'Order Type',
    },
    {
      label: 'Product/Service',
      key: 'Product/Service',
    },
    {
      label: 'Login ID',
      key: 'Login ID',
    },
    {
      label: 'Billing Account No',
      key: 'Billing Account No',
    },
    {
      label: 'Monthly Recurring Charge',
      key: 'Monthly Recurring Charge',
    },
    {
      label: 'Billing Frequency',
      key: 'Billing Frequency',
    },
    {
      label: 'Contract Term',
      key: 'Contract Term',
    },
    {
      label: 'RFS Date',
      key: 'RFS Date',
    },
    {
      label: 'One Time Charge',
      key: 'One Time Charge',
    },
    {
      label: 'Sphere ID',
      key: 'Sphere ID',
    },
    {
      label: 'Ref No',
      key: 'Ref No',
    },
    {
      label: 'DAT Code',
      key: 'DATCode',
    },
    {
      label: 'Quantity',
      key: 'Quantity',
    },
    {
      label: 'Bandwidth Speed/Package',
      key: 'Bandwidth Speed Package',
    },
    {
      label: 'Contract ID',
      key: 'Contract ID',
    },
    {
      label: 'DP No.',
      key: 'DP No.',
    },
    {
      label: 'NIS Address ID',
      key: 'NIS Address ID',
    },
    {
      label: 'Installation Address',
      key: 'Installation Address',
    },
    {
      label: 'Billing Address',
      key: 'Billing Address',
    },
    {
      label: 'Customer Contact Person',
      key: 'Customer Contact Person',
    },
    {
      label: 'Customer Contact No',
      key: 'Customer Contact No',
    },
    {
      label: 'Additional Remark',
      key: 'Additional Remark',
    },
  ],
  'ICP Modify': [
    {
      label: 'Date',
      key: 'Date',
    },
    {
      label: 'Customer Name',
      key: 'Customer Name',
    },
    {
      label: 'Account No',
      key: 'Account No',
    },
    {
      label: 'Product/Service',
      key: 'Product/Service',
    },
    {
      label: 'Segment/Unit',
      key: 'Segment/Unit',
      options:
        'Celcom & Webe,Maxis Group,Digi & U Mobile,Edotco & YTL,Reseller 1,Reseller 2,Carrier Sales Central,Carrier Sales Eastern,Carrier Sales Northern,Carrier Sales Southern,Carrier Sales Sabah,Carrier Sales Sarawak',
    },
    {
      label: 'Order Type',
      key: 'Order Type',
    },
    {
      label: 'Order Sub Type',
      key: 'Order Sub Type',
    },
    {
      label: 'Monthly Recurring Charge',
      key: 'Monthly Recurring Charge',
    },
    {
      label: 'Contract Term',
      key: 'Contract Term',
    },
    {
      label: 'RFS Date',
      key: 'RFS Date',
    },
    {
      label: 'One Time Charge',
      key: 'One Time Charge',
    },
    {
      label: 'Ref No',
      key: 'Ref No',
    },
    {
      label: 'Billing Account No',
      key: 'Billing Account No',
    },
    {
      label: 'Existing Service ID',
      key: 'Existing Service ID',
    },
    {
      label: 'Login ID',
      key: 'Login ID',
    },
    {
      label: 'DP No',
      key: 'DP No.',
    },
    {
      label: 'NIS Address ID',
      key: 'NIS Address ID',
    },
    {
      label: 'Contract ID',
      key: 'Contract ID',
    },
    {
      label: 'Installation Address',
      key: 'Installation Address',
    },
    {
      label: 'Customer Contact Person',
      key: 'Customer Contact Person',
    },
    {
      label: 'Customer Contact No',
      key: 'Customer Contact No',
    },
    {
      label: 'Additional Remark',
      key: 'Additional Remark',
    },
  ],
  'ICP Terminate': [
    {
      label: 'Date',
      key: 'Date',
    },
    {
      label: 'Customer Name',
      key: 'Customer Name',
    },
    {
      label: 'Account No',
      key: 'Account No',
    },
    {
      label: 'Segment/Unit',
      key: 'Segment/Unit',
      options:
        'Celcom & Webe,Maxis Group,Digi & U Mobile,Edotco & YTL,Reseller 1,Reseller 2,Carrier Sales Central,Carrier Sales Eastern,Carrier Sales Northern,Carrier Sales Southern,Carrier Sales Sabah,Carrier Sales Sarawak',
    },
    {
      label: 'Waive ETC (Yes = Waive / No = Charge ETC)',
      key: 'Waive ETC (Yes = Waive / No = Charge ETC)',
    },
    {
      label: 'Collect Equipment (Y/N)',
      key: 'Collect Equipment? (Yes/No)',
    },
    {
      label: 'Order Type',
      key: 'Order Type',
    },
    {
      label: 'Product/Service',
      key: 'Product/Service',
    },
    {
      label: 'RFS Date',
      key: 'RFS Date',
    },
    {
      label: 'Ref No',
      key: 'Ref No',
    },
    {
      label: 'Billing Account No',
      key: 'Billing Account No',
    },
    {
      label: 'Contract ID',
      key: 'Contract ID',
    },
    {
      label: 'Existing Service ID',
      key: 'Existing Service ID',
    },
    {
      label: 'Login ID',
      key: 'Login ID',
    },
    {
      label: 'Installation Address',
      key: 'Installation Address',
    },
    {
      label: 'AE Name',
      key: 'AE Name',
    },
    {
      label: 'Customer Contact Person',
      key: 'Customer Contact Person',
    },
    {
      label: 'Customer Contact No',
      key: 'Customer Contact No',
    },
    {
      label: 'RFT Date',
      key: 'RFT Date',
    },
    {
      label: 'Additional Remark',
      key: 'Additional Remark',
    },
  ],
  'NOVA SMART C-RAN New Install': [
    {
      label: 'Date',
      key: 'Date',
    },
    {
      label: 'Project ID',
      key: 'Project ID',
    },
    {
      label: 'Site ID/Site Name',
      key: 'Site ID/Site Name',
    },
    {
      label: 'Quote Name',
      key: 'Quote Name',
    },
    {
      label: 'Customer Name',
      key: 'Customer Name',
    },
    {
      label: 'Account No',
      key: 'Account No',
    },
    {
      label: 'Segment/Unit',
      key: 'Segment/Unit',
      options:
        'Celcom & Webe,Maxis Group,Digi & U Mobile,Edotco & YTL,Reseller 1,Reseller 2,Carrier Sales Central,Carrier Sales Eastern,Carrier Sales Northern,Carrier Sales Southern,Carrier Sales Sabah,Carrier Sales Sarawak',
    },
    {
      label: 'Backhaul Service ID',
      key: 'Backhaul Service ID',
    },
    {
      label: 'CPRI',
      key: 'CPRI',
    },
    {
      label: 'Billing Account No',
      key: 'Billing Account No',
    },
    {
      label: 'Monthly Rental Charge Per Month',
      key: 'Monthly Rental Charge Per Monthly',
    },
    {
      label: 'Cell Site Utilities Charge Per Month',
      key: 'Cell Site Utilities Charge Per Month',
    },
    {
      label: 'BTS Utility Charge Per Month',
      key: 'BTS Utility Charge Per Month',
    },
    {
      label: 'Add On Charge (Space) Per Month',
      key: 'Add On Charge (Space) Per Month',
    },
    {
      label: 'Add On Charge (CPRI) Per Month',
      key: 'Add On Charge (CPRI) Per Month',
    },
    {
      label: 'Add On Charge (Additional Fronthaul) Per Month',
      key: 'Add On Charge (Additional Fronthaul) Per Month',
    },
    {
      label: 'Add On Charge (Breaker/Unit above 63Amp) Per Month',
      key: 'Add On Charge (Breaker/Unit above 63 Amp) Per Month',
    },
    {
      label: 'Add On Charge (4T4R) Per Month',
      key: 'Add On Charge (4T4R) Per Month',
    },
    {
      label: 'Add On Charge (BRS at Cell Site) Per Month',
      key: 'Add On Charge (BRS at Cell Site) Per Month',
    },
    {
      label: 'Add On Charge (Electricity) Per Month',
      key: 'Add On Charge (Electricity) Per Month',
    },
    {
      label: 'Generator Set Charge Per Month',
      key: 'Generator Set Charge Per Month',
    },
    {
      label: 'Contract Term',
      key: 'Contract Term',
    },
    {
      label: 'RFS Date',
      key: 'RFS Date',
    },
    {
      label: 'One Time Charge',
      key: 'One Time Charge',
    },
    {
      label: 'Sphere ID',
      key: 'Sphere ID',
    },
    {
      label: 'Additional Remark',
      key: 'Additional Remark',
    },
  ],
  'NOVA SMART C-RAN Modify': [
    {
      label: 'Date',
      key: 'Date',
    },
    {
      label: 'Customer Name',
      key: 'Customer Name',
    },
    {
      label: 'Segment/Unit',
      key: 'Segment/Unit',
      options:
        'Celcom & Webe,Maxis Group,Digi & U Mobile,Edotco & YTL,Reseller 1,Reseller 2,Carrier Sales Central,Carrier Sales Eastern,Carrier Sales Northern,Carrier Sales Southern,Carrier Sales Sabah,Carrier Sales Sarawak',
    },
    {
      label: 'Site ID/Site Name',
      key: 'Site ID/Site Name',
    },
    {
      label: 'Quote Name',
      key: 'Quote Name',
    },
    {
      label: 'Existing Service ID',
      key: 'Existing Service ID',
    },
    {
      label: 'New Total Mobile Fronthaul',
      key: 'New Total Mobile Fronthaul',
    },
    {
      label: 'New CPRI Rate',
      key: 'New CPRI Rate',
    },
    {
      label: 'Billing Account No',
      key: 'Billing Account No',
    },
    {
      label: 'RFS Date',
      key: 'RFS Date',
    },
    {
      label: 'One Time Charge',
      key: 'One Time Charge',
    },
    {
      label: 'Additional Remark',
      key: 'Additional Remark',
    },
  ],
  'NOVA IBC New Install': [
    {
      label: 'Date',
      key: 'Date',
    },
    {
      label: 'Project ID',
      key: 'Project ID',
    },
    {
      label: 'Site ID/Site Name',
      key: 'Site ID/Site Name',
    },
    {
      label: 'Quote Name',
      key: 'Quote Name',
    },
    {
      label: 'Customer Name',
      key: 'Customer Name',
    },
    {
      label: 'Account No',
      key: 'Account No',
    },
    {
      label: 'Segment/Unit',
      key: 'Segment/Unit',
      options:
        'Celcom & Webe,Maxis Group,Digi & U Mobile,Edotco & YTL,Reseller 1,Reseller 2,Carrier Sales Central,Carrier Sales Eastern,Carrier Sales Northern,Carrier Sales Southern,Carrier Sales Sabah,Carrier Sales Sarawak',
    },
    {
      label: 'Billing Account No',
      key: 'Billing Account No',
    },
    {
      label: 'Total# Antenna Subscription',
      key: 'Total# Antenna Subscription',
    },
    {
      label: 'IBC DAS Rental Charge (Antenna) Per Month',
      key: 'IBC DAS Rental Charge (Antenna) Per Month',
    },
    {
      label: 'IBC Electricity Charge Per Month',
      key: 'IBC Electricity Charge Per Month',
    },
    {
      label: 'Contract Term',
      key: 'Contract Term',
    },
    {
      label: 'RFS Date',
      key: 'RFS Date',
    },
    {
      label: 'One Time Charge',
      key: 'One Time Charge',
    },
    {
      label: 'Additional Remark',
      key: 'Additional Remark',
    },
  ],
  MCC: [
    {
      label: 'Date',
      key: 'Date',
    },
    {
      label: 'Customer Name',
      key: 'Customer Name',
    },
    {
      label: 'Account No',
      key: 'Account No',
    },
    {
      label: 'Segment/Unit',
      key: 'Segment/Unit',
      options:
        'Celcom & Webe,Maxis Group,Digi & U Mobile,Edotco & YTL,Reseller 1,Reseller 2,Carrier Sales Central,Carrier Sales Eastern,Carrier Sales Northern,Carrier Sales Southern,Carrier Sales Sabah,Carrier Sales Sarawak',
    },
    {
      label: 'Network ID',
      key: 'Network ID',
    },
    {
      label: 'Leg A New Price Per Billing Cycle',
      key: 'Leg A New Price Per Billing Cycle',
    },
    {
      label: 'Leg B* New Price Per Billing Cycle',
      key: 'Leg B* New Price Per Billing Cycle',
    },
    {
      label: 'Commitment Period',
      key: 'Commitment Period',
    },
    {
      label: 'New Billing Cycle',
      key: 'New Billing Cycle',
    },
    {
      label: 'RFS',
      key: 'RFS',
    },
    {
      label: 'Additional Remark',
      key: 'Additional Remark',
    },
  ],
};

export const TASKS_TABLE_HEADERS_FOR_EXPORT = [
  {
    label: 'Job ID',
    key: 'job_id',
  },
  {
    label: 'Task ID',
    key: 'task_id',
  },
  {
    label: 'Processor',
    key: 'processor_staff_id',
  },
  {
    label: 'Customer Name',
    key: 'Customer Name',
  },
  {
    label: 'Order Type',
    key: 'Order Type',
  },
  {
    label: 'Ref Number',
    key: 'Ref No',
  },
  {
    label: 'Quote Number',
    key: 'quote_no',
  },
  {
    label: 'Quote Name',
    key: 'Quote Name',
  },
  {
    label: 'Order Number',
    key: 'order_no',
  },
  {
    label: 'Service ID',
    key: 'service_id',
  },
  {
    label: 'Site Name',
    key: 'Site ID/Site Name',
  },
  {
    label: 'Sphere ID',
    key: 'Sphere ID',
  },
  {
    label: 'RFS',
    key: 'rfs_date',
  },
  {
    label: 'RFT',
    key: 'rft_date',
  },
  {
    label: 'Billing Number',
    key: 'billing_no',
  },
  {
    label: 'Status',
    key: 'status',
  },
];
