export const ALL_TM_CUSTOMER_ACCOUNT_ENDPOINT = `https://selfcare.tmglobal.com.my/api/account/search_account/all`;
export const DEPOT_ENGINE_ENDPOINT = `/UWwE7uvzYUXb2r5mHGTqhgCy0HKzUE`;
export const CORS_PROXY_ENDPOINT = `${process.env.NEXT_PUBLIC_CORS_PROXY_ENDPOINT}/?`;

export const toUpperCaseFirstLetter = (text) => {
  if ([null, undefined]?.includes(text)) return text;
  const temp = text.split(' ');
  const temp2 = [];
  for (let i = 0; i < temp.length; i += 1) {
    temp2.push(temp[i].charAt(0).toUpperCase() + temp[i].slice(1));
  }
  let returnedText = '';
  for (let i = 0; i < temp2.length; i += 1) {
    returnedText = `${returnedText}${temp2[i]}`;
    if (i !== temp2.length - 1) returnedText = `${returnedText} `;
  }
  return returnedText;
};

export const isCertainModuleRole = (user, module, role) => {
  if (user?.isSuperAdmin) return true;

  return user?.modules?.find((m) => m.module === module)?.role === role;
};

export const isCertainModuleSubModuleRole = (user, module, submodule, role) => {
  if (user?.isSuperAdmin) {
    return true;
  }

  return (
    user?.modules
      ?.find((m) => m.module === module)
      ?.submodules?.find((s) => s.submodule === submodule)?.role === role
  );
};

export const localStorageAvailable = () => {
  try {
    // Incognito mode might reject access to the localStorage for security reasons.
    // window isn't defined on Node.js
    // https://stackoverflow.com/questions/16427636/check-if-localstorage-is-available
    const key = '__some_random_key_you_are_not_going_to_use__';
    window.localStorage.setItem(key, key);
    window.localStorage.removeItem(key);

    return true;
  } catch (err) {
    return false;
  }
};

export const checkAndReplaceStringWithHyphen = (text) => {
  if (['', null, undefined, 'string']?.includes(text)) return '-';
  return String(text);
};

export const checkAndReplaceNumberWithZero = (number, roundedToDecimalPlace = 0, fn = (o) => o) => {
  if (!number || Number.isNaN(number)) return 0;
  number = Number(number);
  number = fn(number);
  return Intl.NumberFormat('en-US').format(number?.toFixed(roundedToDecimalPlace));
};

export const sortArrayOfObjectsByCertainKeyAlphabetically = (array, key) =>
  array.sort((a, b) => {
    if (a[key] < b[key]) {
      return -1;
    }
    if (a[key] > b[key]) {
      return 1;
    }
    return 0;
  });

export const sortArrayOfObjectsByCertainKeyNumerically = (array, key, inAscendingOrder = true) => {
  if (inAscendingOrder) {
    return array.sort((a, b) => a[key] - b[key]);
  }
  return array.sort((a, b) => b[key] - a[key]);
};

export const REQUIRE_PERMISSION_MODULES = [
  {
    module: 'metric',
    title: 'METRIC',
    description: 'Product performance metrics portal.',
    category: 'Performance & Reporting',
  },
  {
    module: 'hsba',
    title: 'HSBA',
    description: 'Various reporting tools for HSBA.',
    category: 'Performance & Reporting',
  },
  {
    module: 'payme',
    title: 'PayMe',
    description: 'Portal to track monthly transactions by TM Global staff.',
    category: 'Finance & Transactions',
  },
  {
    module: 'edge-facility',
    title: 'Edge Facility',
    description: 'Portal to track our EF offerings.',
    category: 'Inventory & Solution Management',
  },
  {
    module: 'loa',
    title: 'LOA',
    description: "A portal to track letter of awards' progress.",
    category: 'Inventory & Solution Management',
  },
  {
    module: 'capry',
    title: 'CAPRY',
    description: 'Capacity inventory portal.',
    category: 'Inventory & Solution Management',
  },
  {
    module: 'solid',
    title: 'SOLID',
    description: 'Solution inventory dashboard.',
    category: 'Inventory & Solution Management',
  },
  {
    module: 'fa',
    title: 'Digital Financial Analysis (FA)',
    description: 'Complex analysis for TM Global finances.',
    category: 'Finance & Transactions',
  },
  {
    module: 'itaf',
    title: 'ITAF',
    description: 'International Travel Application Form.',
    category: 'Travel & Application Tools',
  },
  {
    module: 'sdp',
    title: 'SDP',
    description: 'Service Delivery Performance.',
    category: 'Performance & Reporting',
  },
  {
    module: 'pecs',
    title: 'PECS',
    description: 'Permit to Work.',
    category: 'Internal Operations',
  },
  {
    module: 'gaisha-dev',
    title: 'TANYA (Dev)',
    description: 'TANYA (dev) AI Chatbot for TMG',
    category: 'AI & Chatbot',
  },
  {
    module: 'gaisha-preprod',
    title: 'TANYA (PreProd)',
    description: 'TANYA (PreProd) AI Chatbot for TMG',
    category: 'AI & Chatbot',
  },
  {
    module: 'calendar',
    title: 'Calendar',
    description: 'Manage TM Global internal calendar.',
    category: 'Internal Operations',
  },
  {
    module: 'noc',
    title: 'NOC',
    description: 'Manage NOC team.',
    category: 'Internal Operations',
  },
  {
    module: 'oasys',
    title: 'O@SYS',
    description: 'Order tracking portal.',
    category: 'Ordering & Service Tools',
  },
  {
    module: 'smart-tool',
    title: 'Smart-Tool',
    description: 'Smart Tool portal.',
    category: 'Ordering & Service Tools',
  },
  {
    module: 'finsight',
    title: 'FinSight',
    description: 'Finance + Insight.',
    category: 'Finance & Transactions',
  },
  {
    module: 'scorecard',
    title: 'Scorecard',
    description: 'Partner performance metrics dashboard.',
    category: 'Performance & Reporting',
  },
  {
    module: 'reporting',
    title: 'Reporting',
    description: 'Project timeline reports and analytics.',
    category: 'Performance & Reporting',
  },
  {
    module: 'ocm-tracker',
    title: 'OCM Tracker',
    description: 'Register your request for an OCM slot here.',
    category: 'Internal Operations',
  },
  {
    module: 'project-tracker',
    title: 'Project Tracker',
    description: 'Comprehensive project management and tracking platform with real-time analytics.',
    category: 'Project Management',
  },
];

export const NO_PERMISSION_MODULES = [
  {
    module: 'digital-card',
    title: 'Digital Card',
    description: 'Yeah, everyone has a digital card.',
    category: 'Engagement & Events',
  },
  {
    module: 'event',
    title: 'Event',
    description: "To manage TM Global's internal events.",
    category: 'Engagement & Events',
  },
  {
    module: 'old-oasys',
    title: 'Classic O@SYS',
    description: 'Domestic Ordering tracking portal.',
    category: 'Legacy Tools',
  },
  {
    module: 'old-oasys-global',
    title: 'Classic O@SYS Global',
    description: 'Global Ordering tracking portal.',
    category: 'Legacy Tools',
  },
  {
    module: 'old-capry',
    title: 'Classic CAPRY',
    description: 'Old capacity inventory portal.',
    category: 'Legacy Tools',
  },
  {
    module: 'lucky-draw',
    title: 'Lucky Draw',
    description: 'Lucky draw portal.',
    category: 'Engagement & Events',
  },
  {
    module: 'voting',
    title: 'Vote',
    description: 'Vote on your favourite candidate.',
    category: 'Engagement & Events',
  },
  {
    module: 'salary-tool',
    title: 'Salary Tool',
    description: 'Salary tool portal.',
    category: 'Finance & Transactions',
  },
  {
    module: 'quran',
    title: 'Quran',
    description: 'Take a time off...',
    category: 'Personal & Well-being',
  },
  {
    module: 'pmcare',
    title: 'TM Panel Clinics',
    description: 'Search for nearest TM Panel Clinics.',
    category: 'Internal Operations',
  },
  {
    module: 'exchange-rate',
    title: 'Exchange Rate',
    description: 'Get latest currency exchange rate here.',
    category: 'Finance & Transactions',
  },
  {
    module: 'support',
    title: 'Support',
    description: 'File complaint, bug, enhancement request here.',
    category: 'Internal Operations',
  },
];

export const SUPERADMIN_ONLY_MODULES = [
  {
    module: 'admin',
    title: 'Admin',
    description: 'Admin portal.',
  },
];

export const ALL_MODULES = [
  ...REQUIRE_PERMISSION_MODULES,
  ...NO_PERMISSION_MODULES,
  ...SUPERADMIN_ONLY_MODULES,
];

export const getFilteredStaffData = (allData, dataRole, keyToCheck) => {
  if (dataRole === 'all') {
    return allData;
  }
  if (dataRole === 'ne') {
    return allData?.filter(
      (o) =>
        o?.[keyToCheck]?.toLowerCase()?.includes('penolong') ||
        o?.[keyToCheck]?.toLowerCase()?.includes('secretary') ||
        o?.[keyToCheck]?.toLowerCase()?.includes('kerani')
    );
  }
  allData = allData?.filter(
    (o) =>
      !o?.[keyToCheck]?.toLowerCase()?.includes('penolong') &&
      !o?.[keyToCheck]?.toLowerCase()?.includes('secretary') &&
      !o?.[keyToCheck]?.toLowerCase()?.includes('kerani')
  );
  if (dataRole === 'am') {
    return allData?.filter(
      (o) =>
        o?.[keyToCheck]?.toLowerCase()?.includes('asst') ||
        o?.[keyToCheck]?.toLowerCase()?.includes('exec')
    );
  }
  allData = allData.filter(
    (o) =>
      !o?.[keyToCheck]?.toLowerCase()?.includes('asst') &&
      !o?.[keyToCheck]?.toLowerCase()?.includes('exec')
  );
  if (dataRole === 'm') {
    return allData?.filter(
      (o) =>
        o?.[keyToCheck]?.toLowerCase()?.includes('mgr') ||
        o?.[keyToCheck]?.toLowerCase()?.includes('manager')
    );
  }
  allData = allData?.filter(
    (o) =>
      !o?.[keyToCheck]?.toLowerCase()?.includes('mgr') ||
      !o?.[keyToCheck]?.toLowerCase()?.includes('manager')
  );

  if (dataRole === 'agm') {
    return allData?.filter((o) => o?.[keyToCheck]?.toLowerCase()?.includes('agm'));
  }
  allData = allData?.filter((o) => !o?.[keyToCheck]?.toLowerCase()?.includes('agm'));
  if (dataRole === 'gm-vp') {
    return allData?.filter(
      (o) =>
        o?.[keyToCheck]?.toLowerCase()?.includes('head') ||
        o?.[keyToCheck]?.toLowerCase()?.includes('gm') ||
        o?.[keyToCheck]?.toLowerCase()?.includes('vp') ||
        o?.[keyToCheck]?.toLowerCase()?.includes('evp') ||
        o?.[keyToCheck]?.toLowerCase()?.includes('chief')
    );
  }
  return [];
};

export const TM_GLOBAL_REGIONS = ['anzp', 'mesca', 'tmhk', 'tmsg', 'tmus', 'eua'];

export const getModuleFromPath = (path) => path?.split('?')?.[0]?.split('/')[1];

export const getColorCode = (module) => {
  switch (module) {
    case 'admin':
      return process.env.NEXT_PUBLIC_ADMIN;
    case 'hsba':
      return process.env.NEXT_PUBLIC_HSBA;
    case 'payme':
      return process.env.NEXT_PUBLIC_PAYME;
    case 'edge-facility':
      return process.env.NEXT_PUBLIC_EF;
    case 'loa':
      return process.env.NEXT_PUBLIC_LOA;
    case 'capry':
      return process.env.NEXT_PUBLIC_CAPRY;
    case 'solid':
      return process.env.NEXT_PUBLIC_SOLID;
    case 'fa':
      return process.env.NEXT_PUBLIC_FA;
    case 'pmcare':
      return process.env.NEXT_PUBLIC_PMCARE;
    case 'event':
      return process.env.NEXT_PUBLIC_EVENT;
    case 'itaf':
      return process.env.NEXT_PUBLIC_ITAF;
    case 'voting':
      return process.env.NEXT_PUBLIC_VOTING;
    case 'sdp':
      return process.env.NEXT_PUBLIC_SDP;
    case 'calendar':
      return process.env.NEXT_PUBLIC_CALENDAR;
    case 'support':
      return process.env.NEXT_PUBLIC_SUPPORT;
    case 'noc':
      return process.env.NEXT_PUBLIC_NOC;
    case 'oasys':
      return process.env.NEXT_PUBLIC_OASYS;
    case 'finsight':
      return process.env.NEXT_PUBLIC_FINSIGHT;
    case 'reporting':
      return process.env.NEXT_PUBLIC_REPORTING;
    case 'scorecard':
      return process.env.NEXT_PUBLIC_SCORECARD;
    case 'ocm-tracker':
      return process.env.NEXT_PUBLIC_OCM_TRACKER;
    default:
      return process.env.NEXT_PUBLIC_PRIMARY;
  }
};
