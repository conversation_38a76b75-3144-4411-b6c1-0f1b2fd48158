// Packages
import { useSnackbar } from 'notistack';

// Others
import axios from './axios';
import { DEPOT_ENGINE_ENDPOINT } from './shared';

export const HSBA_ENDPOINT = `/RmSBpPB7NThAqktPKAQxzyE38DExDB`;
export const UNUTILIZED_PORT_ENDPOINT = `/jwHSz4vcgLGz6zQXQKSBnQ13i57ETx`;

export const getLogo = (name) => {
  if (name.toLowerCase().includes('maxis')) {
    return '/assets/companyLogo/maxis-logo.png';
  }

  if (name.toLowerCase().includes('viewqwest') || name.toLowerCase().includes('ohana')) {
    return '/assets/companyLogo/viewqwest-logo.png';
  }

  if (name.toLowerCase().includes('digi')) {
    return '/assets/companyLogo/digi-logo.png';
  }

  if (name.toLowerCase().includes('redtone')) {
    return '/assets/companyLogo/redtone-logo.jpg';
  }

  if (name.toLowerCase().includes('celcom')) {
    return '/assets/companyLogo/celcom-logo.svg';
  }

  if (name.toLowerCase().includes('measat')) {
    return '/assets/companyLogo/measat-logo.png';
  }

  if (name.toLowerCase().includes('webe') || name.toLowerCase().includes('unifi')) {
    return '/assets/companyLogo/unifi-logo.svg';
  }

  return '/assets/companyLogo/tm-global.svg';
};

export const useHsbaContext = () => {
  // Standard and Vars
  const { enqueueSnackbar } = useSnackbar();

  // Others
  const triggerEngine = async (process, payload = {}) => {
    try {
      const response = await axios.post(`${DEPOT_ENGINE_ENDPOINT}/run_engine/${process}`, payload);
      enqueueSnackbar(response.data?.message || 'Engine triggered successfully');
    } catch {
      enqueueSnackbar('Error triggering engine', {
        variant: 'error',
      });
    }
  };

  return {
    triggerEngine,
  };
};
