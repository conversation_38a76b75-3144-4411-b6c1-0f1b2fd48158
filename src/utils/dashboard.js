export const getEmail = (user) => {
  if (user?.use_alt_email) {
    return user?.alt_email;
  }
  return user?.email;
};

export const getPhone = (user) => {
  let temp;
  if (user?.use_alt_phone) {
    temp = user?.alt_phone;
  } else {
    temp = user?.phone;
  }

  if (temp?.trim()[0] !== '+') {
    temp = `+${temp?.trim()}`;
  }

  return temp;
};

export const getDesignation = (user) => {
  if (user?.use_alt_designation) {
    return user?.alt_designation;
  }
  return user?.position;
};
