// Next, React, Tw
import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';

// Packages
import * as yup from 'yup';

// Others
import { useAuthContext } from './auth/useAuthContext';

export const OCM_TRACKER_ENDPOINT = `/pkkF0q8g1VYebHRT2MDzU6NUQKpUan`;

export const useOcmTrackerContext = () => {
  // Standard and Vars
  const { ocmsList } = useSelector((state) => state.ocmTracker);
  const { query } = useRouter();
  const { q } = query;

  const { user } = useAuthContext();

  const filteredOcmsList = (() => {
    const temp = ocmsList;
    if ([undefined, '']?.includes(q)) {
      return temp;
    }
    return temp.filter((o) => JSON?.stringify(o)?.toLowerCase().includes(q.toLowerCase()));
  })();

  const paperSchema = yup.object({
    category: yup.string().required('Please provide category.'),
    created_by_staff_id: yup
      .string()
      .required('Please provide created by staff id.')
      ?.default(user?.staff_id),
    ocm_id: yup.string(),
    status: yup.string().required('Please provide status.')?.default('pending'),
    title: yup.string().required('Please provide title.'),
    type: yup.string().required('Please provide type.'),
  });

  return {
    filteredOcmsList,
    paperSchema,
  };
};

const STATUS_COLOR_MAPPING = {
  pending: 'bg-yellow-500',
  queried: 'bg-purple-500',
  rejected: 'bg-red-500',
  assigned: 'bg-green-500',
};

export const STATUSES = Object.keys(STATUS_COLOR_MAPPING);

export const getStatusColor = (status) =>
  `${STATUS_COLOR_MAPPING[status?.toLowerCase()] || 'bg-black'} text-white`;

export const STRATEGY_TEAM_EMAILS = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];
