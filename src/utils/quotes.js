export const inspirationalQuotes = [
  {
    quote: "The way to get started is to quit talking and begin doing.",
    author: "<PERSON>"
  },
  {
    quote: "Innovation distinguishes between a leader and a follower.",
    author: "<PERSON>"
  },
  {
    quote: "The future belongs to those who believe in the beauty of their dreams.",
    author: "<PERSON>"
  },
  {
    quote: "Excellence is not a skill, it is an attitude.",
    author: "<PERSON>"
  },
  {
    quote: "Success is not final, failure is not fatal: it is the courage to continue that counts.",
    author: "<PERSON>"
  },
  {
    quote: "The only way to do great work is to love what you do.",
    author: "<PERSON>"
  },
  {
    quote: "Life is what happens to you while you're busy making other plans.",
    author: "<PERSON>"
  },
  {
    quote: "The future depends on what you do today.",
    author: "<PERSON><PERSON><PERSON>"
  },
  {
    quote: "It is during our darkest moments that we must focus to see the light.",
    author: "<PERSON>"
  },
  {
    quote: "In the middle of difficulty lies opportunity.",
    author: "<PERSON>"
  },
  {
    quote: "Believe you can and you're halfway there.",
    author: "<PERSON>"
  },
  {
    quote: "The only impossible journey is the one you never begin.",
    author: "<PERSON>"
  }
];

export const getRandomQuote = () => {
  const randomIndex = Math.floor(Math.random() * inspirationalQuotes.length);
  const quote = inspirationalQuotes[randomIndex];
  return {
    quote: `"${quote.quote}"`,
    author: `-${quote.author}-`
  };
}; 