export const GAISHA_ENDPOINT = process.env.NEXT_PUBLIC_GAISHA_ENDPOINT;
export const GAISHA_DEV_ENDPOINT = process.env.NEXT_PUBLIC_GAISHA_DEV_ENDPOINT;
export const GAISHA_PREPROD_ENDPOINT = process.env.NEXT_PUBLIC_GAISHA_PREPROD_ENDPOINT;

// Utility function for safe postMessage to Gaisha iframes
export const sendMessageToGaishaIframe = (payload, endpoint, iframeId = 'gaisha') => {
  const gaishaIframe = document.getElementById(iframeId);
  if (gaishaIframe?.contentWindow && endpoint) {
    try {
      // Extract origin from the full URL
      const targetOrigin = new URL(endpoint).origin;
      gaishaIframe.contentWindow.postMessage(payload, targetOrigin);
    } catch (error) {
      console.log('PostMessage error:', error);
      // Fallback to wildcard (less secure but functional)
      gaishaIframe.contentWindow.postMessage(payload, '*');
    }
  }
};
