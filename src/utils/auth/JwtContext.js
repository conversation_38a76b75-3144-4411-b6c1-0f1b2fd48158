// Next, React, Tw
import { useRouter } from 'next/router';
import { createContext, useEffect, useReducer, useCallback, useMemo } from 'react';

// Packages
import PropTypes from 'prop-types';
import { useTheme } from 'next-themes';

// Others
import axios from '../axios';
import { localStorageAvailable } from '../shared';
import { isValidToken, setSession } from './utils';
import { AUM_ENDPOINT } from '../aum';

const initialState = {
  isInitialized: false,
  isAuthenticated: false,
  user: null,
};

const reducer = (state, action) => {
  if (action.type === 'INITIAL') {
    return {
      isInitialized: true,
      isAuthenticated: action.payload.isAuthenticated,
      user: action.payload.user,
    };
  }
  if (action.type === 'LOGIN') {
    return {
      ...state,
      isAuthenticated: true,
      user: action.payload.user,
    };
  }
  if (action.type === 'LOGOUT') {
    return {
      ...state,
      isAuthenticated: false,
      user: null,
    };
  }

  return state;
};

// ----------------------------------------------------------------------

export const AuthContext = createContext(null);

// ----------------------------------------------------------------------

AuthProvider.propTypes = {
  children: PropTypes.node,
};

export function AuthProvider({ children }) {
  // Standard and Vars
  const [state, dispatch] = useReducer(reducer, initialState);
  const { asPath } = useRouter();
  const storageAvailable = localStorageAvailable();
  const { theme, setTheme } = useTheme();

  // Others

  const getUserDetails = async () => {
    try {
      const response = await axios.get(`${AUM_ENDPOINT}/user/v1/me`);
      const user = response.data.data[0];
      user.isSuperAdmin = user?.tag === 'superadmin';
      return user;
    } catch (error) {
      return error;
    }
  };

  const initialize = useCallback(async () => {
    try {
      const accessToken = storageAvailable ? localStorage.getItem('accessToken') : '';
      const refreshToken = storageAvailable ? localStorage.getItem('refreshToken') : '';

      if (accessToken && refreshToken && isValidToken(accessToken) && isValidToken(refreshToken)) {
        setSession(accessToken, refreshToken);
        const user = await getUserDetails();

        if (user?.status === 'failed') {
          setSession(null);
          dispatch({
            type: 'LOGOUT',
          });
          return;
        }

        dispatch({
          type: 'INITIAL',
          payload: {
            isAuthenticated: true,
            user,
          },
        });
      } else {
        dispatch({
          type: 'INITIAL',
          payload: {
            isAuthenticated: false,
            user: null,
          },
        });
      }
    } catch {
      dispatch({
        type: 'INITIAL',
        payload: {
          isAuthenticated: false,
          user: null,
        },
      });
    }
  }, []);

  useEffect(() => {
    initialize();
  }, [asPath?.split('?')[0]]);

  useEffect(() => {
    if (!['dark', 'light']?.includes(theme)) setTheme('light');
  }, []);

  // LOGIN
  const login = useCallback(async (pin_code) => {
    const response = await fetch(`${AUM_ENDPOINT}/authentication/v1/verify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('verifyToken')}`,
      },
      body: JSON.stringify({ pin_code: Number(pin_code) }),
    });

    const payload = await response.json();

    if (payload.status === 'success') {
      localStorage.removeItem('verifyToken');
      setSession(payload.token, payload.refresh_token);
      const user = await getUserDetails();
      dispatch({
        type: 'LOGIN',
        payload: {
          user,
        },
      });
      return true;
    }
    return false;
  }, []);

  // LOGOUT
  const logout = useCallback(() => {
    setSession(null, null);
    dispatch({
      type: 'LOGOUT',
    });
  }, []);

  const memoizedValue = useMemo(
    () => ({
      isInitialized: state.isInitialized,
      isAuthenticated: state.isAuthenticated,
      user: state.user,
      method: 'jwt',
      initialize,
      login,
      logout,
    }),
    [state.isAuthenticated, state.isInitialized, state.user, login, logout, initialize]
  );

  return <AuthContext.Provider value={memoizedValue}>{children}</AuthContext.Provider>;
}
