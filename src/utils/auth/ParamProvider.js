import PropTypes from 'prop-types';
import { useRouter } from 'next/router';
import { createContext, useCallback, useMemo, useContext } from 'react';

export const ParamContext = createContext(null);

const ParamProvider = ({ children }) => {
  const router = useRouter();

  const setParam = useCallback(
    (paramToset) => {
      router.query = {
        ...router.query,
        ...paramToset,
      };
      router?.push(router);
    },
    [router]
  );

  const replaceParam = useCallback(
    (paramToset) => {
      router.query = {
        ...router.query,
        ...paramToset,
      };
      router?.replace(router);
    },
    [router]
  );

  const memoizedValue = useMemo(
    () => ({
      setParam,
      replaceParam,
    }),
    [setParam, replaceParam]
  );

  return <ParamContext.Provider value={memoizedValue}>{children}</ParamContext.Provider>;
};

ParamProvider.propTypes = {
  children: PropTypes.node,
};

export default ParamProvider;

export const useParamContext = () => {
  const context = useContext(ParamContext);

  if (!context) throw new Error('useAuthContext context must be use inside AuthProvider');

  return context;
};
