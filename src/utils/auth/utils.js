// utils
import axios from '../axios';

// ----------------------------------------------------------------------

function jwtDecode(token) {
  const base64Url = token.split('.')[1];
  const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
  const jsonPayload = decodeURIComponent(
    window
      .atob(base64)
      .split('')
      .map((c) => `%${`00${c.charCodeAt(0).toString(16)}`.slice(-2)}`)
      .join('')
  );

  return JSON.parse(jsonPayload);
}

// ----------------------------------------------------------------------

export const isValidToken = (accessToken) => {
  if (!accessToken) {
    return false;
  }

  const decoded = jwtDecode(accessToken);

  const currentTime = Date.now() / 1000;

  return decoded.exp > currentTime;
};

// ----------------------------------------------------------------------

export const setSession = (accessToken, refreshToken) => {
  if (accessToken || refreshToken) {
    // Store in localStorage for client-side access
    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('refreshToken', refreshToken);
    
    // Store in cookies for server-side middleware access
    setCookie('accessToken', accessToken);
    setCookie('refreshToken', refreshToken);
    
    axios.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
    window?.dispatchEvent(
      new StorageEvent('storage', {
        key: 'accessToken',
        newValue: accessToken,
      })
    );
  } else {
    // Remove from localStorage
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    
    // Remove from cookies
    setCookie('accessToken', '', -1);
    setCookie('refreshToken', '', -1);
    
    delete axios.defaults.headers.common.Authorization;
  }
};

// Helper function to set cookies
const setCookie = (name, value, days = 7) => {
  if (typeof document === 'undefined') return; // Server-side safety
  
  let expires = '';
  if (days) {
    const date = new Date();
    if (days > 0) {
      date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
    } else {
      date.setTime(date.getTime() - (24 * 60 * 60 * 1000)); // Delete cookie
    }
    expires = `; expires=${date.toUTCString()}`;
  }
  
  // Set cookie with security flags
  document.cookie = `${name}=${value || ''}${expires}; path=/; SameSite=Strict; Secure=${location.protocol === 'https:'}`;
};
