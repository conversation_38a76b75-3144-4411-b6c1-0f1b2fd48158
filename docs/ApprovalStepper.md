# ApprovalStepper

The `ApprovalStepper` component is designed to visually represent the steps in an approval process. It fetches approval data from a specified endpoint and displays each step with the status of the approval.

## Props
- `GET_ALL_APPROVALS_ENDPOINT` (string): The API endpoint to fetch approval data.

## Usage
```jsx
<ApprovalStepper GET_ALL_APPROVALS_ENDPOINT="/api/approvals" />
```

## Additional Components
- **ApprovalStepLabel**: A sub-component used within `ApprovalStepper` to display individual step labels and user information.

## Notes
- The component uses a `UserPopup` to display user details when a step label is clicked.
- It manages loading state using a Redux action `setIsLoading`.
