# ApproveRejectButtonGroup

The `ApproveRejectButtonGroup` component is designed to manage approval actions within a workflow. It allows users to submit, approve, or revert approval stages and sends notifications via email.

## Props
- `GET_ALL_APPROVALS_ENDPOINT` (string): The API endpoint to fetch approval data.
- `UPDATE_CERTAIN_APPROVAL_ENDPOINT` (string): The API endpoint to update approval data.
- `REVERT_CALLBACK` (function): Callback function to execute after a revert action.
- `SUBMIT_CALLBACK` (function): Callback function to execute after a submit action.
- `APPROVE_CALLBACK` (function): Callback function to execute after an approve action.
- `NAME` (string): The name of the approval process.

## Usage
```jsx
<ApproveRejectButtonGroup
  GET_ALL_APPROVALS_ENDPOINT="/api/approvals"
  UPDATE_CERTAIN_APPROVAL_ENDPOINT="/api/approvals/update"
  REVERT_CALLBACK={() => console.log('Reverted')}
  SUBMIT_CALLBACK={() => console.log('Submitted')}
  APPROVE_CALLBACK={() => console.log('Approved')}
  NAME="Approval Process"
/>
```

## Notes
- The component uses a `useSnackbar` hook to display notifications.
- It validates email payloads using `yup`.
