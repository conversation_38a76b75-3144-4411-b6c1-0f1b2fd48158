name: Build, Push, and Deploy

on:
  push:
    branches:
      - production

env:
  REGISTRY_NAME: simi
  REPO_NAME: portal
  TAG_NAME: v${{github.run_number}}
  K8S_NAMESPACE: portal
  DEPLOYMENT_NAME: simi-portal

jobs:
  build-push-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository 🛎️
        uses: actions/checkout@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: InstallAlibabaCloudCLI
        run: |
          curl -o aliyun-cli-linux-latest-amd64.tgz https://aliyuncli.alicdn.com/aliyun-cli-linux-latest-amd64.tgz         
          tar zxvf aliyun-cli-linux-latest-amd64.tgz         
          sudo mv aliyun /usr/local/bin/         
          aliyun version

      - name: Log in to Alibaba Cloud
        run: |
          aliyun configure set --profile default --region ap-southeast-3
          aliyun configure set --profile default --access-key-id ${{ secrets.ACCESS_KEY_ID }}
          aliyun configure set --profile default --access-key-secret ${{ secrets.ACCESS_KEY_SECRET }}

      - name: Log in to ACR EE
        run: |
          docker login simi-digital-registry-registry.ap-southeast-3.cr.aliyuncs.com -u ${{ secrets.ACR_USER }} -p ${{ secrets.ACR_PASSWORD }}

      - name: Build and Push Docker Image
        run: |
          docker build -t simi-digital-registry-registry.ap-southeast-3.cr.aliyuncs.com/$REGISTRY_NAME/$REPO_NAME:$TAG_NAME .
          docker push simi-digital-registry-registry.ap-southeast-3.cr.aliyuncs.com/$REGISTRY_NAME/$REPO_NAME:$TAG_NAME

      - name: Update deployment file
        run: |
          sed -i "s|<IMAGE>|$REPO_NAME:$TAG_NAME|" $GITHUB_WORKSPACE/k8s/deployment_alibaba.yml

      - name: Set ACK context
        uses: aliyun/ack-set-context@v1
        with:
          access-key-id: ${{ secrets.ACCESS_KEY_ID }}
          access-key-secret: ${{ secrets.ACCESS_KEY_SECRET }}
          cluster-id: ${{ secrets.ACK_CLUSTER_ID }}

      - name: Deploy to ACK
        run: |
          kubectl apply -f k8s/deployment_alibaba.yml
          kubectl rollout status deployment/$DEPLOYMENT_NAME -n $K8S_NAMESPACE
