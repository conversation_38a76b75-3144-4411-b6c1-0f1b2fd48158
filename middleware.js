import { NextResponse } from 'next/server';

export function middleware(request) {
  const { pathname } = request.nextUrl;
  
  // Admin functionality has been moved to modal-based interface
  // No admin paths need protection anymore
  
  // Allow all requests to continue
  return NextResponse.next();
}

// Basic token format validation (JWT-like structure)
function isValidTokenFormat(token) {
  if (!token || typeof token !== 'string') return false;
  
  // Basic JWT format check (3 parts separated by dots)
  const parts = token.split('.');
  if (parts.length !== 3) return false;
  
  // Check if each part is base64-like
  return parts.every(part => /^[A-Za-z0-9_-]+$/.test(part));
}

// Decode JWT payload (basic implementation)
function decodeJWT(token) {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) throw new Error('Invalid JWT format');
    
    const payload = parts[1];
    // Add padding if needed
    const paddedPayload = payload + '='.repeat((4 - payload.length % 4) % 4);
    
    // Decode base64url
    const decoded = atob(paddedPayload.replace(/-/g, '+').replace(/_/g, '/'));
    return JSON.parse(decoded);
  } catch (error) {
    throw new Error('Failed to decode JWT');
  }
}

// Configure which paths this middleware should run on
export const config = {
  matcher: [
    // No paths need middleware protection anymore
    // Admin functionality is now modal-based
  ]
};
