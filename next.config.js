const BACKEND_ENDPOINT = `${process.env.NEXT_PUBLIC_FRONTEND_BASE_ENDPOINT}/api`;

/** @type {import('next').NextConfig} */
const nextConfig = {
  swcMinify: true,
  env: {},
  images: {
    domains: [],
  },
  async headers() {
    if (process?.env?.NODE_ENV !== 'development') {
      return [
        {
          source: '/(.*)',
          headers: [
            {
              key: 'Content-Security-Policy',
              value: `default-src 'self'; frame-src 'self' https://player.vimeo.com ${
                process.env.NEXT_PUBLIC_GAISHA_ENDPOINT?.split('/g-aisha')?.[0]
              } ${process.env.NEXT_PUBLIC_GAISHA_DEV_ENDPOINT?.split('/aisha/g-aisha')?.[0]} ${
                process.env.NEXT_PUBLIC_GAISHA_PREPROD_ENDPOINT?.split('/aisha/g-aisha')?.[0]
              }; style-src 'self' 'unsafe-inline'; img-src 'self' data: https://trusted.cdn.com https://*.tile.openstreetmap.org https://*.openstreetmap.org https://*.basemaps.cartocdn.com; script-src 'self' https://trusted.cdn.com blob: https://unpkg.com 'sha256-eMuh8xiwcX72rRYNAGENurQBAcH7kLlAUQcoOri3BIo='; object-src 'self'; connect-src 'self' https://api.simidigital.app/ https://pydyfgbm33.execute-api.us-east-1.amazonaws.com https://dlqawfi443.execute-api.ap-southeast-1.amazonaws.com https://37a0gi9tyj.execute-api.ap-southeast-1.amazonaws.com https://api.quotable.io/ https://zenquotes.io/ https://api.allorigins.win/ https://quotegarden.herokuapp.com/ https://corsproxy.io/ https://geolocation-db.com/json/ https://api.iconify.design/eva.json https://api.unisvg.com/eva.json https://api.simplesvg.com/eva.json https://api.freecurrencyapi.com/v1/latest https://ia902709.us.archive.org https://api.quran.com/api/v4/chapters ${
                process.env.NEXT_PUBLIC_CORS_PROXY_ENDPOINT
              } https://selfcare.tmglobal.com.my https://apps.pmcare.my/api/ThirdParty/PmcareTMPanelProviders blob:`,
            },
            {
              key: 'X-Content-Type-Options',
              value: 'nosniff',
            },
            {
              key: 'X-Frame-Options',
              value: 'SAMEORIGIN',
            },
            {
              key: 'X-XSS-Protection',
              value: '1; mode=block',
            },
          ],
        },
      ];
    }
    return [];
  },
  async rewrites() {
    return [
      {
        source: `/J9SqvDa2kTBubgqiA5rqxKjDediFqR/:path*`,
        destination: `${BACKEND_ENDPOINT}/aum/:path*`,
      },
      {
        source: `/qy36KCpHwc2Az7JqKJH9UrW9NqNDyy/:path*`,
        destination: `${BACKEND_ENDPOINT}/brief/:path*`,
      },
      {
        source: `/ET9pEuS10DWEiu5AY4BkLS8rvfkYQ6/:path*`,
        destination: `${BACKEND_ENDPOINT}/capry/v1/:path*`,
      },
      {
        source: `/wUTdEGZqbbbnziiWeCid6FcHNZtZGZ/:path*`,
        destination: `${BACKEND_ENDPOINT}/ef/v1/:path*`,
      },
      {
        source: `/RmSBpPB7NThAqktPKAQxzyE38DExDB/:path*`,
        destination: `${BACKEND_ENDPOINT}/hsba/v1/:path*`,
      },
      {
        source: `/xu8CxJ2VjGvFiEGCR88e5EtehkLhzf/:path*`,
        destination: `${BACKEND_ENDPOINT}/metric/v1/:path*`,
      },
      {
        source: `/MU5aPGbPCnHgCQM6bESQkffbXN3uEE/:path*`,
        destination: `${BACKEND_ENDPOINT}/loa/v1/:path*`,
      },
      {
        source: `/7adNpbFmC8UHntZWgvaWqmYEHbSbL8/:path*`,
        destination: `${BACKEND_ENDPOINT}/notification/v1/:path*`,
      },
      {
        source: `/qjaRXWLnPHQa4cRaRXnitt4FMiuf20/:path*`,
        destination: `${BACKEND_ENDPOINT}/payme/v1/:path*`,
      },
      {
        source: `/juNTWTyZziMhf1T35Tz3hMY37UzkQQ/:path*`,
        destination: `${BACKEND_ENDPOINT}/rm/v1/:path*`,
      },
      {
        source: `/P6p9SBUiLXv0Pv2F5gMhcSG4ZTNEyb/:path*`,
        destination: `${BACKEND_ENDPOINT}/lrp/v1/:path*`,
      },
      {
        source: `/UCwrvNm8Hp18PCDZriYb5XxwPD85r5/:path*`,
        destination: `${BACKEND_ENDPOINT}/cdn-demo/v1/:path*`,
      },
      {
        source: `/XR4Tw9qjmg99JXE52wujJcMCUkCznw/:path*`,
        destination: `${BACKEND_ENDPOINT}/cloudrift/application/:path*`,
      },
      {
        source: `/rNEDFtjfnYqxXwD1r8bNh48NTgXzVe/:path*`,
        destination: `${BACKEND_ENDPOINT}/fa/v1/:path*`,
      },
      {
        source: `/1TUAAAm1CxTjqpJDuYkFZaTARfSHPf/:path*`,
        destination: `${BACKEND_ENDPOINT}/event/v1/:path*`,
      },
      {
        source: `/eREXCnmxV0F6Qp2uS0rmTedDfc9Ler/:path*`,
        destination: `${BACKEND_ENDPOINT}/itaf/v1/:path*`,
      },
      {
        source: `/vuPxkH6hpSHY3qKh0ZVgBVZMUrCRdv/:path*`,
        destination: `${BACKEND_ENDPOINT}/oasys/v1/:path*`,
      },
      {
        source: `/4qN0uKnnfjp8ttSKwZRRrNpx7h4pvW/:path*`,
        destination: `${BACKEND_ENDPOINT}/scrapper/v1/:path*`,
      },
      {
        source: `/FCLDVJa8EinGvC8nTr2Rac7ik0pCbN/:path*`,
        destination: `${BACKEND_ENDPOINT}/smart-tool/:path*`,
      },
      {
        source: `/yz31NfRDa0TqBwnCy1Yc3ch2P9Pg56/:path*`,
        destination: `${BACKEND_ENDPOINT}/finsight/v1/:path*`,
      },
      {
        source: `/pkkF0q8g1VYebHRT2MDzU6NUQKpUan/:path*`,
        destination: `${BACKEND_ENDPOINT}/loa/v1/ocm-tracker/:path*`,
      },
      {
        source: `/jwHSz4vcgLGz6zQXQKSBnQ13i57ETx/:path*`,
        destination: `https://selfcare.tmglobal.com.my/api/unutilized_port/:path*`,
      },
      {
        source: `/UWwE7uvzYUXb2r5mHGTqhgCy0HKzUE/:path*`,
        destination: `https://selfcare.tmglobal.com.my/api/depot_engine/:path*`,
      },
    ];
  },
  async redirects() {
    return [
      {
        source: '/old-oasys',
        destination: '/old-oasys/index.html',
        permanent: true,
      },
      {
        source: '/old-oasys-global',
        destination: '/old-oasys-global/index.html',
        permanent: true,
      },
      {
        source: '/old-capry',
        destination: '/old-capry/index.html',
        permanent: true,
      },
    ];
  },
};

module.exports = nextConfig;
