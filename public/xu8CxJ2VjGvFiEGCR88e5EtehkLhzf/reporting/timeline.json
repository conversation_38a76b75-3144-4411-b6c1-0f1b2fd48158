{"success": true, "data": {"projects": [{"id": 1, "initiative": "GPUaaS", "projectName": "GPU as a Service Platform", "projectOwner": "<PERSON>", "projectSponsor": "<PERSON>", "status": "On Track", "completion": 45, "startDate": "01-Jan-23", "endDate": "15-Dec-23", "description": "Develop and deploy GPU as a Service platform to provide on-demand computing resources for AI/ML workloads", "ocm": {"startPlanDate": "10-Jan-23", "planDate": "25-Jan-23", "startActualDate": "12-Jan-23", "actualDate": "28-Jan-23", "planPercentage": 5.0, "actualPercentage": 5.0}, "pcm_concept": {"startPlanDate": "26-Jan-23", "planDate": "15-Feb-23", "startActualDate": "29-Jan-23", "actualDate": "18-Feb-23", "planPercentage": 10.0, "actualPercentage": 10.0}, "poc": {"startPlanDate": "16-Feb-23", "planDate": "15-Mar-23", "startActualDate": "19-Feb-23", "actualDate": "20-Mar-23", "planPercentage": 15.0, "actualPercentage": 15.0}, "be": {"startPlanDate": "16-Mar-23", "planDate": "15-Apr-23", "startActualDate": "21-Mar-23", "actualDate": "18-Apr-23", "planPercentage": 10.0, "actualPercentage": 10.0}, "ia": {"startPlanDate": "16-Apr-23", "planDate": "15-May-23", "startActualDate": "19-Apr-23", "actualDate": "20-May-23", "planPercentage": 10.0, "actualPercentage": 5.0}, "pcm_biz": {"startPlanDate": "16-May-23", "planDate": "31-May-23", "startActualDate": "21-May-23", "actualDate": "05-Jun-23", "planPercentage": 5.0, "actualPercentage": 5.0}, "dev": {"startPlanDate": "01-Jun-23", "planDate": "31-Aug-23", "startActualDate": "06-Jun-23", "actualDate": "Date?", "planPercentage": 20.0, "actualPercentage": 15.0}, "proc": {"startPlanDate": "01-Aug-23", "planDate": "15-Sep-23", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "launch_checkpoint": {"startPlanDate": "16-Sep-23", "planDate": "30-Sep-23", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "launch_rfs": {"startPlanDate": "01-Oct-23", "planDate": "31-Oct-23", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "pir": {"startPlanDate": "01-Dec-23", "planDate": "15-Dec-23", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "total": {"plan": 100, "actual": 45}, "achievement": 45.0, "planRFS": "31-Oct-23", "revisedRFS": "15-Nov-23", "actualRFS": "Date?", "delay": {"vsPlanned": "-", "vsRevised": "-"}, "timelyCompletion": {"vsPlanned": "-", "vsRevised": "-"}, "highlight": "Successfully completed POC phase with major cloud providers. Currently implementing the core platform infrastructure. Integration with authentication system taking longer than expected.", "support": "Need additional resources for API development. Security team availability for penetration testing."}, {"id": 2, "initiative": "GPUaaS", "projectName": "GPU Provisioning Automation", "projectOwner": "<PERSON>", "projectSponsor": "<PERSON>", "status": "Delayed", "completion": 30, "startDate": "15-Feb-23", "endDate": "30-Nov-23", "description": "Implement automation systems for GPU resource provisioning and scaling", "ocm": {"startPlanDate": "15-Feb-23", "planDate": "28-Feb-23", "startActualDate": "15-Feb-23", "actualDate": "28-Feb-23", "planPercentage": 5.0, "actualPercentage": 5.0}, "pcm_concept": {"startPlanDate": "01-Mar-23", "planDate": "15-Mar-23", "startActualDate": "01-Mar-23", "actualDate": "20-Mar-23", "planPercentage": 10.0, "actualPercentage": 10.0}, "poc": {"startPlanDate": "16-Mar-23", "planDate": "15-Apr-23", "startActualDate": "21-Mar-23", "actualDate": "25-Apr-23", "planPercentage": 15.0, "actualPercentage": 15.0}, "be": {"startPlanDate": "16-Apr-23", "planDate": "30-Apr-23", "startActualDate": "26-Apr-23", "actualDate": "15-May-23", "planPercentage": 10.0, "actualPercentage": 0.0}, "ia": {"startPlanDate": "01-May-23", "planDate": "15-May-23", "startActualDate": "16-May-23", "actualDate": "05-Jun-23", "planPercentage": 10.0, "actualPercentage": 0.0}, "pcm_biz": {"startPlanDate": "16-May-23", "planDate": "31-May-23", "startActualDate": "06-Jun-23", "actualDate": "20-Jun-23", "planPercentage": 5.0, "actualPercentage": 0.0}, "dev": {"startPlanDate": "01-Jun-23", "planDate": "15-Aug-23", "startActualDate": "21-Jun-23", "actualDate": "Date?", "planPercentage": 20.0, "actualPercentage": 0.0}, "proc": {"startPlanDate": "16-Aug-23", "planDate": "15-Sep-23", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "launch_checkpoint": {"startPlanDate": "16-Sep-23", "planDate": "30-Sep-23", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "launch_rfs": {"startPlanDate": "01-Oct-23", "planDate": "31-Oct-23", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "pir": {"startPlanDate": "01-Nov-23", "planDate": "30-Nov-23", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "total": {"plan": 100, "actual": 30}, "achievement": 30.0, "planRFS": "31-Oct-23", "revisedRFS": "15-Dec-23", "actualRFS": "Date?", "delay": {"vsPlanned": "1.5", "vsRevised": "-"}, "timelyCompletion": {"vsPlanned": "At Risk", "vsRevised": "-"}, "highlight": "POC phase completed with delays due to compatibility issues with existing infrastructure. Backend performance optimization in progress. Resource constraints affecting development timeline.", "support": "Need additional DevOps engineers familiar with Kubernetes and GPU orchestration. Assistance with vendor negotiations for hardware procurement."}, {"id": 3, "initiative": "GPUaaS", "projectName": "GPUaaS Billing & Metering", "projectOwner": "<PERSON>", "projectSponsor": "<PERSON>", "status": "On Track", "completion": 60, "startDate": "01-Mar-23", "endDate": "31-Dec-23", "description": "Develop billing and metering system for GPU as a Service platform usage", "ocm": {"startPlanDate": "01-Mar-23", "planDate": "15-Mar-23", "startActualDate": "01-Mar-23", "actualDate": "15-Mar-23", "planPercentage": 5.0, "actualPercentage": 5.0}, "pcm_concept": {"startPlanDate": "16-Mar-23", "planDate": "31-Mar-23", "startActualDate": "16-Mar-23", "actualDate": "31-Mar-23", "planPercentage": 10.0, "actualPercentage": 10.0}, "poc": {"startPlanDate": "01-Apr-23", "planDate": "30-Apr-23", "startActualDate": "01-Apr-23", "actualDate": "30-Apr-23", "planPercentage": 15.0, "actualPercentage": 15.0}, "be": {"startPlanDate": "01-May-23", "planDate": "31-May-23", "startActualDate": "01-May-23", "actualDate": "31-May-23", "planPercentage": 10.0, "actualPercentage": 10.0}, "ia": {"startPlanDate": "01-Jun-23", "planDate": "15-Jun-23", "startActualDate": "01-Jun-23", "actualDate": "20-Jun-23", "planPercentage": 10.0, "actualPercentage": 10.0}, "pcm_biz": {"startPlanDate": "16-Jun-23", "planDate": "30-Jun-23", "startActualDate": "21-Jun-23", "actualDate": "05-Jul-23", "planPercentage": 5.0, "actualPercentage": 5.0}, "dev": {"startPlanDate": "01-Jul-23", "planDate": "15-Sep-23", "startActualDate": "06-Jul-23", "actualDate": "15-Sep-23", "planPercentage": 20.0, "actualPercentage": 5.0}, "proc": {"startPlanDate": "16-Sep-23", "planDate": "15-Oct-23", "startActualDate": "16-Sep-23", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "launch_checkpoint": {"startPlanDate": "16-Oct-23", "planDate": "31-Oct-23", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "launch_rfs": {"startPlanDate": "01-Nov-23", "planDate": "30-Nov-23", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 10.0, "actualPercentage": 0.0}, "pir": {"startPlanDate": "01-Dec-23", "planDate": "31-Dec-23", "startActualDate": "Date?", "actualDate": "Date?", "planPercentage": 5.0, "actualPercentage": 0.0}, "total": {"plan": 100, "actual": 60}, "achievement": 60.0, "planRFS": "30-Nov-23", "revisedRFS": "30-Nov-23", "actualRFS": "Date?", "delay": {"vsPlanned": "-", "vsRevised": "-"}, "timelyCompletion": {"vsPlanned": "On Track", "vsRevised": "On Track"}, "highlight": "Successfully completed core metering components. Integration with existing billing system proceeding as scheduled. Unit testing showing high accuracy in resource usage tracking.", "support": "Need finance team involvement for final billing formula validation. Coordination needed with the main GPUaaS platform team for API integration."}, {"id": 4, "initiative": "GPUaaS", "projectName": "ML Workflow Templates", "projectOwner": "<PERSON>", "projectSponsor": "<PERSON>", "status": "Completed", "completion": 100, "startDate": "01-Apr-23", "endDate": "31-Aug-23", "description": "Create reusable ML workflow templates for the GPUaaS platform", "ocm": {"startPlanDate": "01-Apr-23", "planDate": "15-Apr-23", "startActualDate": "01-Apr-23", "actualDate": "12-Apr-23", "planPercentage": 5.0, "actualPercentage": 5.0}, "pcm_concept": {"startPlanDate": "16-Apr-23", "planDate": "30-Apr-23", "startActualDate": "13-Apr-23", "actualDate": "28-Apr-23", "planPercentage": 10.0, "actualPercentage": 10.0}, "poc": {"startPlanDate": "01-May-23", "planDate": "31-May-23", "startActualDate": "29-Apr-23", "actualDate": "25-May-23", "planPercentage": 15.0, "actualPercentage": 15.0}, "be": {"startPlanDate": "01-Jun-23", "planDate": "15-Jun-23", "startActualDate": "26-May-23", "actualDate": "12-Jun-23", "planPercentage": 10.0, "actualPercentage": 10.0}, "ia": {"startPlanDate": "16-Jun-23", "planDate": "30-Jun-23", "startActualDate": "13-Jun-23", "actualDate": "25-Jun-23", "planPercentage": 10.0, "actualPercentage": 10.0}, "pcm_biz": {"startPlanDate": "01-Jul-23", "planDate": "15-Jul-23", "startActualDate": "26-Jun-23", "actualDate": "10-Jul-23", "planPercentage": 5.0, "actualPercentage": 5.0}, "dev": {"startPlanDate": "16-Jul-23", "planDate": "15-Aug-23", "startActualDate": "11-Jul-23", "actualDate": "10-Aug-23", "planPercentage": 20.0, "actualPercentage": 20.0}, "proc": {"startPlanDate": "16-Aug-23", "planDate": "25-Aug-23", "startActualDate": "11-Aug-23", "actualDate": "20-Aug-23", "planPercentage": 5.0, "actualPercentage": 5.0}, "launch_checkpoint": {"startPlanDate": "26-Aug-23", "planDate": "28-Aug-23", "startActualDate": "21-Aug-23", "actualDate": "25-Aug-23", "planPercentage": 5.0, "actualPercentage": 5.0}, "launch_rfs": {"startPlanDate": "29-Aug-23", "planDate": "31-Aug-23", "startActualDate": "26-Aug-23", "actualDate": "31-Aug-23", "planPercentage": 10.0, "actualPercentage": 10.0}, "pir": {"startPlanDate": "15-Sep-23", "planDate": "30-Sep-23", "startActualDate": "15-Sep-23", "actualDate": "28-Sep-23", "planPercentage": 5.0, "actualPercentage": 5.0}, "total": {"plan": 100, "actual": 100}, "achievement": 100.0, "planRFS": "31-Aug-23", "revisedRFS": "31-Aug-23", "actualRFS": "31-Aug-23", "delay": {"vsPlanned": "0", "vsRevised": "0"}, "timelyCompletion": {"vsPlanned": "On Time", "vsRevised": "On Time"}, "highlight": "Successfully delivered all planned ML workflow templates ahead of schedule. Templates include computer vision, NLP, and general ML processing workflows. User testing gave positive feedback with 95% satisfaction rate.", "support": "Documentation team involvement for final user guides. Integration with GPUaaS main platform needed."}]}}