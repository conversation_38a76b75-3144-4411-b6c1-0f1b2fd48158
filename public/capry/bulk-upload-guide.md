# Capry Offnet Inventory Bulk Upload Guide

## Overview
This guide explains how to use the bulk upload functionality for Capry offnet inventories, with special focus on the new service date fields.

## CSV Template
Download the CSV template from: `/capry/template.csv`

## Required Fields
The following fields are required for successful upload:
- **Cable**: Name of the cable
- **Parent Capacity**: Bearer capacity information
- **Child Capacity**: Path capacity information
- **Route**: Landing points (format: "Point A - Point B")
- **Capacity (Gbps)**: Numeric value (e.g., "10G" or "10")
- **Status utilization**: One of: reserved, provisioning, activated

## Service Date Fields (New)
Two new date fields have been added to support service tracking:

### Service Start Date
- **Field Name**: `Service Start Date`
- **Format**: YYYY-MM-DD, DD/MM/YYYY, or DD-MM-YYYY
- **Examples**: 
  - `2024-01-15`
  - `15/01/2024`
  - `15-01-2024`
- **Optional**: Can be left empty
- **Validation**: Must be a valid date format

### Service End Date
- **Field Name**: `Service End Date`
- **Format**: YYYY-MM-DD, DD/MM/YYYY, or DD-MM-YYYY
- **Examples**: 
  - `2024-12-31`
  - `31/12/2024`
  - `31-12-2024`
- **Optional**: Can be left empty
- **Validation**: Must be a valid date format and cannot be before Service Start Date

## Date Validation Rules
1. **Format Validation**: Dates must be in one of the supported formats
2. **Logical Validation**: Service Start Date must be before or equal to Service End Date
3. **Empty Values**: Both date fields can be left empty (optional)
4. **Invalid Dates**: Will show specific error messages with row numbers

## Error Handling
The system provides comprehensive error reporting:
- **Row-specific errors**: Each error message includes the row number
- **Date format errors**: Clear messages about invalid date formats
- **Logical errors**: Warnings when start date is after end date
- **Processing summary**: Shows count of successful and failed rows

## Upload Process
1. Prepare your CSV file with the required columns
2. Ensure service dates follow the supported formats
3. Click "Upload Bulk" button
4. Select your CSV file
5. Review any error messages and fix issues
6. Re-upload if necessary

## Tips for Success
- Use the provided CSV template as a starting point
- Keep date formats consistent throughout your file
- Check for typos in date values
- Ensure Service Start Date ≤ Service End Date when both are provided
- Review error messages carefully - they include specific row numbers

## Support
For technical issues or questions about the bulk upload functionality, please contact the system administrator.
