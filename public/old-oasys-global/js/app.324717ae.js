(function(t){function e(e){for(var a,o,r=e[0],l=e[1],c=e[2],u=0,f=[];u<r.length;u++)o=r[u],Object.prototype.hasOwnProperty.call(i,o)&&i[o]&&f.push(i[o][0]),i[o]=0;for(a in l)Object.prototype.hasOwnProperty.call(l,a)&&(t[a]=l[a]);d&&d(e);while(f.length)f.shift()();return n.push.apply(n,c||[]),s()}function s(){for(var t,e=0;e<n.length;e++){for(var s=n[e],a=!0,r=1;r<s.length;r++){var l=s[r];0!==i[l]&&(a=!1)}a&&(n.splice(e--,1),t=o(o.s=s[0]))}return t}var a={},i={app:0},n=[];function o(e){if(a[e])return a[e].exports;var s=a[e]={i:e,l:!1,exports:{}};return t[e].call(s.exports,s,s.exports,o),s.l=!0,s.exports}o.m=t,o.c=a,o.d=function(t,e,s){o.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:s})},o.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},o.t=function(t,e){if(1&e&&(t=o(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var s=Object.create(null);if(o.r(s),Object.defineProperty(s,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var a in t)o.d(s,a,function(e){return t[e]}.bind(null,a));return s},o.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return o.d(e,"a",e),e},o.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},o.p="/";var r=window["webpackJsonp"]=window["webpackJsonp"]||[],l=r.push.bind(r);r.push=e,r=r.slice();for(var c=0;c<r.length;c++)e(r[c]);var d=l;n.push(["7dbe","chunk-vendors"]),s()})({0:function(t,e){},"00d3":function(t,e,s){"use strict";var a=s("22ad"),i=s.n(a);i.a},"0134":function(t,e,s){"use strict";s.r(e);var a=s("b172"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"03f0":function(t,e,s){"use strict";var a=s("dbce");s("c975"),s("b0c0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("b17e")),n={data:function(){return{filterProject:""}},computed:{projects:function(){var t=this;return i.filter((function(e){return e.name.toLowerCase().indexOf(t.filterProject.toLowerCase())>-1}),this.$store.state.om.projects)},assignedProjects:function(){return this.$store.getters.om_assignedProjects}},methods:{isLoading:function(t){return this.$store.state.om.loadings[t]},hasProject:function(t){return this.assignedProjects[t]}}};e["default"]=n,t.exports=e.default,t.exports.default=e.default},"03f5":function(t,e,s){"use strict";s.r(e);var a=s("7c3e"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"043a":function(t,e,s){"use strict";s.r(e);var a=s("a18f"),i=s("82ef");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"0525":function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");s("c975"),s("d81d"),s("b0c0"),s("ac1f"),s("5319"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("4ac2")),r={beforeRouteEnter:function(t,e,s){s((function(t){t.$store.getters.om_role(["executives","project_manager","supervisors"])||t.$router.replace("/")}))},components:{NewProjectForm:o["default"]},data:function(){return{processor_filter:"",projectForm:!1,filterProject:""}},computed:{headerheight:function(){return console.log(this.$refs.sectionheader),"Foo"},range:function(){return new Array(50).map((function(){return"fooo"}))},projects:function(){var t=this;return n.filter((function(e){return e.name.toLowerCase().indexOf(t.filterProject.toLowerCase())>-1}),this.$store.state.om.projects)},processors:function(){var t=this.$store.state.om.processors;return t},filtered_processors:function(){var t=this,e=100;return this.processor_filter.length>0?n.pipe(n.filter((function(e){return e.name.toLowerCase().indexOf(t.processor_filter.toLowerCase())>-1})),n.take(e))(this.processors):n.take(e,this.processors)}},methods:{onscroll:function(t){this.$store.dispatch("scrollY",t.target.scrollTop)},isLoading:function(t){return this.$store.state.om.loadings[t]},handleCreateProject:function(t){var e=this;this.$store.dispatch("om.project.create",t).then((function(t){e.$store.dispatch("om.project.load",t.PK).then((function(){e.$router.push("/jobs/projects/".concat(t.PK))}))}))}}};e["default"]=r,t.exports=e.default,t.exports.default=e.default},"05cf":function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");s("4de4"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=(a(s("c1df")),a(s("36dc"))),r={components:{TableJobsheets:o["default"]},data:function(){return{displayedJobsheets:[],jobsheetOwner:"me"}},mounted:function(){this.loadData()},methods:{loadData:function(){this.$store.dispatch("om.jobs.load"),this.$store.dispatch("om.dropfile.all"),this.$store.dispatch("om.stats.load")},showJobsheet:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"me";this.displayedJobsheets="me"==t?this.myJobsheets:this.othersJobsheets}},computed:{stats:function(){return Object.assign({},this.$store.state.om.stats,{completed:(this.jobsheets.COMPLETED||[]).length})},user:function(){return this.$store.state.login.user},jobsheets:function(){var t=this,e=function(e){return e.CreatedBy.staff_id==t.user.staff_id},s=function(e){return e.CreatedBy.staff_id!=t.user.staff_id};return(this.jobsheetOwner="me")?n.pipe(n.values,n.filter(e))(this.$store.state.om.jobs||{}):n.pipe(n.values,n.filter(s))(this.$store.state.om.jobs||{})},myJobsheets:function(){var t=this,e=function(e){return e.CreatedBy.staff_id==t.user.staff_id};return n.pipe(n.values,n.filter(e))(this.$store.state.om.jobs||{})},othersJobsheets:function(){var t=this,e=function(e){return e.CreatedBy.staff_id!=t.user.staff_id};return n.pipe(n.values,n.filter(e))(this.$store.state.om.jobs||{})},dropfiles:function(){return n.values(this.$store.state.om.dropfile.files).filter((function(t){return"OPEN"==t.Status}))},hold_dropfiles:function(){return n.values(this.$store.state.om.dropfile.files).filter((function(t){return"ON_HOLD"==t.Status}))}}};e["default"]=r,t.exports=e.default,t.exports.default=e.default},"05d1":function(t,e,s){"use strict";var a=s("4ea4");s("99af"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("c1df")),n={props:["showForm","resource"],data:function(){return{form:{message:""},comments:[],loading:!1}},computed:{user:function(){return this.$store.state.login.user},commentID:function(){return"".concat(this.resource.PK,".").concat(this.resource.SK)}},mounted:function(){this.loadComments()},watch:{resource:function(){this.loadComments()},showForm:function(t){var e=this;t&&(this.form.message="",this.$nextTick((function(){e.$refs.focus.focus()})))}},methods:{isBlocked:function(t){return t.details.comment_type&&"notes"!=t.details.comment_type},loadComments:function(){var t=this;console.log("load comments"),this.$store.dispatch("om.comments.get",this.commentID).then((function(e){t.loading=!1,t.comments=e}))},handleSubmit:function(){var t=this;this.$validator.validateAll().then((function(e){e&&(t.$store.dispatch("om.comments.post",{PK:"".concat(t.resource.PK,".").concat(t.resource.SK),message:t.form.message}).then((function(){t.loadComments(!0)})),t.$emit("close"))}))}},filters:{toTime:function(t){return t?(0,i["default"])(t).fromNow():"just now..."}}};e["default"]=n,t.exports=e.default,t.exports.default=e.default},"05e1":function(t,e,s){"use strict";s.r(e);var a=s("ab66"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"081d":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("div",{staticClass:"box is-fullwidth",staticStyle:{overflow:"hidden"}},[s("span",{staticClass:"heading",staticStyle:{"margin-bottom":"20px"}},[t._v("Network Design")]),t.uploadedFile?s("div",{staticClass:"animated zoomIn faster"},[s("a",{staticClass:"box",on:{click:t.downloadFile}},[s("div",{staticClass:"level"},[s("div",{staticClass:"level-item"},[s("div",[s("svg",{staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 492.308 492.308",width:"64"}},[s("path",{attrs:{d:"M0 0v492.308h492.308V0H0zm472.615 472.615H19.692V19.692h452.923v452.923z"}}),s("path",{attrs:{d:"M260.837 59.587v344.851h187.99V59.587h-187.99zm168.298 325.158H280.529V79.279h148.606v305.466zM50.462 59.587v69.933h187.99V59.587H50.462zm168.298 50.24H70.154V79.279H218.76v30.548zM50.462 162.337v69.938h187.99v-69.938H50.462zm168.298 50.245H70.154v-30.553H218.76v30.553zM50.462 265.091v69.933h187.99v-69.933H50.462zm168.298 50.241H70.154v-30.548H218.76v30.548z"}}),s("path",{attrs:{d:"M298.535 324.352h112.502v19.692H298.535zM298.535 256.197h112.502v19.692H298.535zM298.535 188.062h112.502v19.692H298.535zM298.535 119.975h112.502v19.692H298.535z"}})])]),s("div",{staticStyle:{padding:"5px 20px",overflow:"hidden"}},[s("p",[s("span",[t._v("Download Network Diagram")]),s("br"),s("span",{staticClass:"is-size-7 has-text-grey long-text break-word"},[t._v(t._s(t.uploadedFile.details.filename))]),s("br")])])])]),s("div",{staticClass:"notification is-size-7"},[t._v("\n                    Uploaded by "),s("span",[t._v(t._s(t.uploadedFile.CreatedBy.name))]),t._v(" "+t._s(t._f("diffNow")(t.uploadedFile.createdAt))+"\n                ")])]),s("div",{staticClass:"level"},[s("div",{staticClass:"level-left"},[s("span",{staticClass:"is-size-7"},[t._v("or")]),s("a",{staticClass:"button is-small is-text",on:{click:t.reUpload}},[t._v("Upload new file")])])])]):t._e(),t.uploadedFile?t._e():s("div",{staticClass:"field"},["checking"==t.isUploading?s("div",{staticClass:"has-text-centered"},[s("span",{staticClass:"button is-fullwidth is-loading is-text"}),s("span",[t._v("Checking...")])]):s("div",{staticClass:"file is-centered is-boxed has-name is-fullwidth",class:{"dropzone is-info":t.inDropzone,"is-success":"uploaded"==t.isUploading,"is-danger":"error"==t.isUploading},on:{dragover:function(e){return e.preventDefault(),t.handleDropover(e)},drop:function(e){return e.preventDefault(),t.handleFileChange(e)},dragleave:t.handleDropleaves}},[s("label",{staticClass:"file-label has-text-grey"},[s("input",{staticClass:"file-inputx",attrs:{type:"file",name:"resume"},on:{change:t.handleFileChange}}),s("span",{staticClass:"file-cta"},[t.isUploading?s("span",["uploading"==t.isUploading?s("span",{staticClass:"button is-loading is-fullwidth is-text"}):t._e(),"error"==t.isUploading?s("span",[s("svg",{staticClass:"animated rollIn",staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"64"}},[s("circle",{attrs:{cx:"15.5",cy:"9.5",r:"1.5"}}),s("circle",{attrs:{cx:"8.5",cy:"9.5",r:"1.5"}}),s("path",{attrs:{d:"M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm0-6c-2.33 0-4.32 1.45-5.12 3.5h1.67c.69-1.19 1.97-2 3.45-2s2.75.81 3.45 2h1.67c-.8-2.05-2.79-3.5-5.12-3.5z"}})])]):t._e(),"uploaded"==t.isUploading?s("span",[s("svg",{staticClass:"animated bounceIn faster",staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"64"}},[s("path",{attrs:{d:"M14.72 8.79l-4.29 4.3-1.65-1.65a1 1 0 1 0-1.41 1.41l2.35 2.36a1 1 0 0 0 .71.29 1 1 0 0 0 .7-.29l5-5a1 1 0 0 0 0-1.42 1 1 0 0 0-1.41 0zM12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm0 18a8 8 0 1 1 8-8 8 8 0 0 1-8 8z"}})])]):t._e()]):s("span",{staticClass:"file-icons"},[t.inDropzone?s("svg",{staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 492.308 492.308",width:"64"}},[s("path",{attrs:{d:"M332.298 65.827L215.106 184.332 160 128.615l-14 13.847 69.106 69.87L346.298 79.673z"}}),s("path",{attrs:{d:"M400.808 0H91.5L.567 257.365 0 258.952v233.356h492.308V258.957L400.808 0zM105.423 19.692h281.462l81.656 231.101H304.442v9.846c0 32.558-26.144 59.048-58.288 59.048s-58.288-26.49-58.288-59.048v-9.846H23.769l81.654-231.101zm367.192 452.923H19.692v-39.413h452.923v39.413zm0-59.105H19.692V270.486h149.087c4.817 38.788 37.673 68.894 77.375 68.894s72.558-30.106 77.375-68.894h149.087V413.51z"}})]):s("svg",{staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 492.308 492.308",width:"64"}},[s("path",{attrs:{d:"M246.154 161.913L142.885 273.308l14.442 13.384 78.981-85.196V411.5H256V201.495l78.986 85.197 14.442-13.384z"}}),s("path",{attrs:{d:"M242.168 63.726L181.447 0H0v492.308h492.308V63.726h-250.14zm230.447 408.889H19.692V19.692h153.322l60.721 63.726h238.88v389.197z"}})])]),t.isUploading?s("span",{staticClass:"file-label has-text-centered"},["uploaded"!=t.isUploading?s("span",[t._v("Uploading...")]):s("span",[t._v("Uploaded!")])]):s("span",{staticClass:"file-label has-text-centered"},[t.inDropzone?s("span",[t._v("Drop file here")]):s("span",[t._v("Upload Network Design")])])]),s("span",{staticClass:"file-name"},[t._v("\n                    "+t._s(t.filename)+"\n                ")])])])])])])},i=[]},"0916":function(t,e,s){"use strict";s.r(e);var a=s("e429"),i=s("fd73");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"0948":function(t,e,s){"use strict";s.r(e);var a=s("d42e"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"0993":function(t,e){var s={order:function(t){}};t.exports=function(t){var e=s[t.type];return e?e(t):null}},"09a6":function(t,e,s){"use strict";s.r(e);var a=s("05cf"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"09bf":function(t,e,s){},"0a5d":function(t,e,s){"use strict";s.r(e);var a=s("ea37"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"0a62":function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var a=s("479b"),i={components:{FunctionalCalendar:a.FunctionalCalendar},props:["file"],data:function(){return{showModal:!1,reason:"",calendarData:"",showCal:!1}},methods:{toggleModal:function(){this.showModal=!0,this.calendarData=""},handleReject:function(){var t=this;this.$validator.validateAll().then((function(e){e&&(t.$emit("onHold",{drop_id:t.file.drop_id,reason:t.reason,duedate:t.calendarData.selectedDate}),t.showModal=!1)}))}}};e["default"]=i,t.exports=e.default,t.exports.default=e.default},"0a65":function(t,e,s){"use strict";s.r(e);var a=s("6154"),i=s("67c1");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"0b0a":function(t,e,s){"use strict";s.r(e);var a=s("84df"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"0b16":function(t,e,s){"use strict";s.r(e);var a=s("96b0"),i=s("0a5d");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"0c65":function(t,e,s){"use strict";var a=s("dbce"),i=s("4ea4");s("99af"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("bc3a")),o=i(s("7f45"));a(s("b17e")),s("58b7");o["default"].tz.setDefault("Asia/Kuala_Lumpur");var r="staging",l=function(t){return"production"==r?"".concat("https://dlqawfi443.execute-api.ap-southeast-1.amazonaws.com/production").concat(t):"".concat("https://pydyfgbm33.execute-api.us-east-1.amazonaws.com/staging").concat(t)},c={state:{users:[]},getters:{},mutations:{SET_USERS:function(t,e){t.users=e}},actions:{"user-management.load":function(t){n["default"].get(l("/user-management/users")).then((function(e){t.commit("SET_USERS",e.data.users)}))}}};e["default"]=c,t.exports=e.default,t.exports.default=e.default},"0c91":function(t,e,s){"use strict";s.r(e);var a=s("682d"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"0c96":function(t,e,s){"use strict";s.r(e);var a=s("4eb5"),i=s("143d");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);s("fade");var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"0e95":function(t,e,s){"use strict";s.r(e);var a=s("7fa7"),i=s("bc3d");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"0ee1":function(t,e,s){"use strict";s.r(e);var a=s("9691"),i=s("b5ac");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},1:function(t,e){},"100d":function(t,e,s){"use strict";s.r(e);var a=s("6c76"),i=s("8d5c");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);s("50e4");var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"105f":function(t,e,s){"use strict";var a=s("dbce"),i=s("4ea4");s("4de4"),s("c975"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("c1df")),o=a(s("b17e")),r=i(s("43ce")),l=i(s("fe45")),c=i(s("7499")),d=i(s("2948")),u={components:{ExportExcel:r["default"],AttachmentTag:l["default"],TaskStatus:c["default"],Sorter:d["default"]},props:["filteredTasks","getProjectName","isViewed","selectedSessionTasks","getProcessorName","viewTask","defaultType"],data:function(){return{filterKeyword:"",sortedTasks:[],filterType:"all",filterDiff:0}},mounted:function(){this.defaultType&&(this.filterType=this.defaultType)},computed:{filteredTasksByKeyword:function(){var t=this;return this.filteredTasks.filter((function(e){return"all"==t.filterType||t.dateRange[0].isSameOrBefore(e.updatedAt,"day")&&t.dateRange[1].isAfter(e.updatedAt,"day")})).filter((function(e){return JSON.stringify(e).toLowerCase().indexOf(t.filterKeyword)>-1}))},dateRange:function(){return[(0,n["default"])().startOf(this.filterType).add(this.filterDiff,this.filterType),(0,n["default"])().startOf(this.filterType).add(this.filterDiff+1,this.filterType)]},dateRangeDis:function(){switch(this.filterType){case"day":return this.dateRange[0].format("DD MMM YYYY");case"week":return o.pipe(o.map((function(t){return t.format("DD MMM YYYY")})),o.join(" → "))(this.dateRange);case"month":return this.dateRange[0].format("MMMM YYYY");case"all":return"All"}}},watch:{filterType:function(){this.filterDiff=0}},methods:{odNumber:function(t){return t.feedback?t.feedback["OD Number"]:t.details["OD Number"]?t.details["OD Number"]:"Not Specified"},odLink:function(t){return t.feedback?t.feedback["OD Link"]:t.details["OD Link"]?t.details["OD Link"]:null},updateDiff:function(t){1==t&&this.filterDiff<0?this.filterDiff++:-1==t&&this.filterDiff--},onSorted:function(t){this.sortedTasks=t}},filters:{diffNow:function(t){return(0,n["default"])(t).fromNow()},shortKey:function(t){return o.take(12,t)},formatDate:function(t){return(0,n["default"])(t).format("DD MMM YYYY, hh:mm A")},formatTime:function(t){return(0,n["default"])(t).format("hh:mm A")}}};e["default"]=u,t.exports=e.default,t.exports.default=e.default},1078:function(t,e,s){"use strict";s.r(e);var a=s("a7bf"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"10cb":function(t,e,s){"use strict";var a=s("2c97"),i=s.n(a);i.a},"11aa":function(t,e,s){"use strict";s.r(e);var a=s("c10f"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"123d":function(t,e,s){"use strict";var a=s("4ea4");s("99af"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("26d3")),n={props:["selectedTask","task"],data:function(){return{isLoading:!1,cancellation:{remarks:"",cancellation_type:"Sales request",type:""}}},computed:{hasOrder:function(){return"SUBMITTED"==this.task.Status||"RERAISED"==this.task.Status},cancelWhat:function(){return this.hasOrder?"ORDER CANCELLATION SUBMITTED":"CANCEL THIS TASK"}},methods:{submitcancellation:function(){var t=this;this.cancellation.type="SUBMITTED"==this.task.Status||"RERAISED"==this.task.Status||"ASSIGNED_RERAISE"==this.task.Status?"cancel_order":"cancellation",this.$validator.validateAll().then((function(e){if(e){t.isLoading=!0;var s="cancellation"==t.cancellation.type?"CANCELLED":"CANCEL_SUBMITTED";(0,i["default"])(t.selectedTask).canTransTo(s)?t.$store.dispatch("om.tasks.cancel",{task:t.selectedTask,cancellation:t.cancellation}).then((function(e){t.isLoading=!1,t.$emit("cancelled"),t.$emit("close")}))["catch"]((function(){t.isLoading=!1})):alert("".concat(t.selectedTask.Status," task cannot be ").concat(s))}}))}}};e["default"]=n,t.exports=e.default,t.exports.default=e.default},"143d":function(t,e,s){"use strict";s.r(e);var a=s("8a7d"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},1601:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("a",{staticClass:"button is-small tooltip",attrs:{"data-tooltip":"Download as excel"},on:{click:t.handleExport}},[s("svg",{staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",width:"16",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M20 8.94a1.31 1.31 0 0 0-.06-.27v-.09a1.07 1.07 0 0 0-.19-.28l-6-6a1.07 1.07 0 0 0-.28-.19h-.1a1.1 1.1 0 0 0-.31-.11H7a3 3 0 0 0-3 3v14a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V9v-.06zm-6-3.53L16.59 8H15a1 1 0 0 1-1-1zM18 19a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h5v3a3 3 0 0 0 3 3h3zm-4.71-4.71l-.29.3V12a1 1 0 0 0-2 0v2.59l-.29-.3a1 1 0 0 0-1.42 1.42l2 2a1 1 0 0 0 .*********** 0 0 0 .76 0 1 1 0 0 0 .33-.21l2-2a1 1 0 0 0-1.42-1.42z"}})])])])},i=[]},"165b":function(t,e,s){"use strict";var a=s("dbce"),i=s("4ea4");s("b0c0"),s("d3b7"),s("4795"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0,s("96cf");var n=i(s("1da1")),o=a(s("b17e")),r=i(s("c1df")),l=function(t){return new Promise((function(e){setTimeout(e,t)}))},c={props:["resource"],data:function(){return{inDropzone:!1,filename:"",file:!1,isUploading:"checking",uploadedFile:!1}},mounted:function(){this.reset(),this.getAttachment()},watch:{resource:function(){this.reset(),this.getAttachment()}},methods:{reset:function(){this.uploadedFile=!1,this.inDropzone=!1,this.filename="",this.file=!1,this.isUploading="checking"},downloadFile:function(){var t=this;return(0,n["default"])(regeneratorRuntime.mark((function e(){var s;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$store.dispatch("om.attachments.download",t.uploadedFile);case 2:s=e.sent,window.open(s,"_BLANK");case 4:case"end":return e.stop()}}),e)})))()},getAttachment:function(){var t=this;this.resource&&this.$store.dispatch("om.attachments.get",{resource:this.resource,attachment_type:"network-diagram"}).then((function(e){t.uploadedFile=o.head(e),t.isUploading=!1}))},getUrl:function(t){var e=this;return(0,n["default"])(regeneratorRuntime.mark((function s(){var a;return regeneratorRuntime.wrap((function(s){while(1)switch(s.prev=s.next){case 0:return s.next=2,e.$store.dispatch("om.attachments.generate",{resource:e.resource,contentType:t});case 2:return a=s.sent,e.signed_url=a,s.abrupt("return",a);case 5:case"end":return s.stop()}}),s)})))()},reUpload:function(){this.uploadedFile=!1},handleFileChange:function(t){var e=this;return(0,n["default"])(regeneratorRuntime.mark((function s(){return regeneratorRuntime.wrap((function(s){while(1)switch(s.prev=s.next){case 0:t.stopPropagation(),t.preventDefault(),t.dataTransfer&&t.dataTransfer.files&&(e.file=o.head(t.dataTransfer.files),e.filename=e.file.name),!t.dataTransfer&&t.target&&t.target.files&&(e.file=o.head(t.target.files),e.filename=e.file.name),e.inDropzone=!1,e.isUploading="uploading",e.$store.dispatch("om.attachments.upload",{file:e.file,resource:e.resource,attachment_type:"network-diagram"}).then((function(t){return e.isUploading="uploaded",l(1e3).then((function(){e.uploadedFile=t}))}))["catch"]((function(t){return e.isUploading="error",e.uploadedFile=!1,e.$store.dispatch("showError","Failed to upload file"),l(2e3).then((function(){e.isUploading=!1}))}));case 7:case"end":return s.stop()}}),s)})))()},handleDropover:function(t){this.inDropzone=!0},handleDropleaves:function(t){this.inDropzone=!1}},filters:{diffNow:function(t){return(0,r["default"])(t).fromNow()}}};e["default"]=c,t.exports=e.default,t.exports.default=e.default},"169b":function(t,e,s){"use strict";var a=s("4ea4");Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("7d20")),n={components:{TemplateTabs:i["default"]},computed:{templates:function(){return this.$store.getters.om_requestTypes},columns:function(){}}};e["default"]=n,t.exports=e.default,t.exports.default=e.default},"16d3":function(t,e,s){"use strict";s.r(e);var a=s("da46"),i=s("09a6");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"17b2":function(t,e,s){"use strict";s.r(e);var a=s("e4d2"),i=s("2780");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},1905:function(t,e,s){"use strict";s.r(e);var a=s("ccdd"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"19d3":function(t,e,s){},"19eb":function(t,e,s){},"1b3e":function(t,e,s){"use strict";s.r(e);var a=s("5693"),i=s("d689");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);s("6b01");var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"1b52":function(t,e,s){"use strict";s.r(e);var a=s("169b"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"1c73":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("div",{staticClass:"timeline "},[t._m(0),t._l(t.steps,(function(e,a){var i,n;return s("div",{key:a},[e.isTag?s("header",{staticClass:"timeline-header",class:{"tooltip is-tooltip-right":e.tooltip},attrs:{"data-tooltip":e.tooltip}},[s("div",{staticClass:"timeline-marker"}),s("span",{staticClass:"tag is-small",class:(n={},n[e.class||"is-success"]=e.time,n)},[t._v(t._s(e.label))])]):s("div",{staticClass:"timeline-item"},[s("div",{staticClass:"timeline-marker",class:(i={},i[e.class||"is-success"]=e.time,i)}),s("div",{staticClass:"timeline-content"},[s("p",{class:{"tooltip is-tooltip-right":e.tooltip},attrs:{"data-tooltip":e.tooltip}},[t._v(t._s(e.label))]),e.time?s("p",{staticClass:"help tooltip is-tooltip-right",attrs:{"data-tooltip":t._f("formatDate")(e.time)}},[t._v(t._s(t._f("fromNow")(e.time)))]):s("p",{staticClass:"help"},[t._v("In progress")])])])])}))],2)])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("header",{staticClass:"timeline-header"},[s("div",{staticClass:"timeline-marker"}),s("span",{staticClass:"tag is-small is-success"},[t._v("Start")])])}]},"1d02":function(t,e,s){"use strict";var a=s("4ea4");Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("1321")),n={props:["title","series"],components:{apexchart:i["default"]},data:function(){return{chartOptions:{labels:[" < 1 day"," < 3 days"," > 3 days"],colors:["#64CC6D","#4B9CE7","#EB4C64"],legend:{show:!1},plotOptions:{pie:{donut:{labels:{show:!0,name:{offsetY:5},value:{show:!0,offsetY:35,fontSize:"41px"},total:{show:!0,label:"Total orders"}}}}}}}}};e["default"]=n,t.exports=e.default,t.exports.default=e.default},"1d43":function(t,e,s){"use strict";s.r(e);var a=s("f488"),i=s("e9a4");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"1e3a":function(t,e,s){"use strict";var a=s("dbce"),i=s("4ea4");Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0,s("96cf");var n=i(s("1da1")),o=i(s("ade3")),r=i(s("be94")),l=i(s("bc3a")),c=a(s("b17e")),d=i(s("c1df")),u=i(s("2a17")),f={namespace:!1,state:{files:{},teamfiles:{},lastKey:null},getters:{om_filedrops:function(t){return t.files}},mutations:{SET_DROPFILES:function(t,e){t.files=(0,r["default"])({},t.files,{},c.pipe(c.map((function(t){return"ON_HOLD"==t.Status&&(0,d["default"])(t.details.on_hold.duedate,"DD/MM/YYYY").isBefore()&&(t.details.on_hold.isOverdue=!0),t})))(e))},SET_DROPFILESTEAM:function(t,e){t.teamfiles=(0,r["default"])({},t.teamfiles,{},c.pipe(c.map((function(t){return"ON_HOLD"==t.Status&&(0,d["default"])(t.details.on_hold.duedate,"DD/MM/YYYY").isBefore()&&(t.details.on_hold.isOverdue=!0),t})))(e))},SET_LASTKEY:function(t,e){t.lastKey=e||null},removeDropfile:function(t,e){t.files=c.omit([e],t.files)}},actions:{"om.dropfile.find":function(t,e){return l["default"].get(t.getters.api("/obase/dropfiles/".concat(e))).then((function(e){var s=c.mergeAll([t.state.files,(0,o["default"])({},e.data.dropfile.PK,e.data.dropfile)]);t.commit("SET_DROPFILES",s)}))},"om.dropfile.list":function(t){var e=t.getters.api("/obase/dropfiles/my?limit=50&lastKey=".concat(encodeURI(JSON.stringify(t.state.lastKey))));return l["default"].get(e).then((function(e){t.commit("SET_LASTKEY",e.data.lastKey),t.commit("SET_DROPFILES",c.indexBy(c.prop("drop_id"),e.data.dropfiles))}))},"om.dropfile.listteam":function(t,e){var s=t.commit,a="https://api.simidigital.app/fastroll/oasys/dropfile/"+e;return l["default"].get(a).then((function(t){s("SET_DROPFILESTEAM",c.indexBy(c.prop("drop_id"),t.data.dropfile))}))},"om.dropfile.resetKey":function(t){t.commit("SET_LASTKEY",null)},"om.dropfile.loadMore":function(t){return t.dispatch("om.dropfile.all")},"om.dropfile.all":function(t){return l["default"].get(t.getters.api("/obase/dropfiles?limit=50&lastKey=".concat(encodeURI(JSON.stringify(t.state.lastKey))))).then((function(e){t.commit("SET_LASTKEY",e.data.lastKey),t.commit("SET_DROPFILES",c.indexBy(c.prop("drop_id"),e.data.dropfiles))}))},"om.dropfile.drop":function(t,e){var s=e.file;return(0,n["default"])(regeneratorRuntime.mark((function e(){var a,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,u["default"].validate(s);case 2:return a=e.sent,e.next=5,l["default"].post(t.getters.api("/obase/dropfiles"),a).then((function(t){return t.data.dropfile}));case 5:return i=e.sent,console.log("DROPREQUEST",i),e.next=9,t.dispatch("om.attachments.upload",{file:s,resource:i,attachment_type:"dropfile"});case 9:return e.sent,e.abrupt("return",{file:s});case 11:case"end":return e.stop()}}),e)})))()},"om.dropfile.remove":function(t,e){var s=e.drop_id;return(0,n["default"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,l["default"]["delete"](t.getters.api("/obase/dropfiles/".concat(s))).then((function(){t.commit("removeDropfile",s)}));case 2:case"end":return e.stop()}}),e)})))()},"om.dropfile.reject":function(t,e){var s=e.drop_id,a=e.reason;return l["default"].post(t.getters.api("/obase/dropfiles/reject"),{drop_id:s,reason:a}).then((function(e){var a=e.data.dropfile,i=(0,o["default"])({},s,a);return t.commit("SET_DROPFILES",i),a}))},"on.dropfile.hold":function(t,e){var s=e.drop_id,a=e.reason,i=e.duedate;return l["default"].post(t.getters.api("/obase/dropfiles/hold"),{drop_id:s,reason:a,duedate:i}).then((function(e){var a=e.data.dropfile,i=(0,o["default"])({},s,a);return t.commit("SET_DROPFILES",i),a}))},"om.dropfile.release":function(t,e){var s=e.drop_id;return l["default"].post(t.getters.api("/obase/dropfiles/holdRelease"),{drop_id:s}).then((function(e){var s=e.data.dropfile,a=(0,o["default"])({},s.drop_id,s);return t.commit("SET_DROPFILES",a),s}))},"om.dropfile.onholds":function(t){return l["default"].get(t.getters.api("/obase/dropfiles/onhold")).then((function(t){return c.indexBy(c.prop("drop_id"),t.data.dropfiles)})).then((function(e){return t.commit("SET_DROPFILES",e),e}))},"om.dropfile.download":function(t,e){var s=e.drop_id;return l["default"].get(t.getters.api("/obase/dropfiles/".concat(s,"?download=true"))).then((function(t){return t.data.signed_url}))},"om.dropfile.getJobsheet":function(t,e){return l["default"].get(t.getters.api("/obase/dropfiles/".concat(e))).then((function(t){return t.data.dropfile}))}}},v=f;e["default"]=v,t.exports=e.default,t.exports.default=e.default},2:function(t,e){},2065:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[t.showForm?s("div",{staticClass:"modal is-active"},[s("div",{staticClass:"modal-background"}),s("div",{staticClass:"modal-content"},[s("div",{staticClass:"card"},[t._m(0),s("form",{staticClass:"form",on:{submit:function(e){return e.preventDefault(),t.handleSubmit(e)}}},[s("div",{staticClass:"card-content"},[s("div",{staticClass:"field"},[s("label",{staticClass:"label"},[t._v("message")]),s("div",{staticClass:"control "},[s("textarea",{directives:[{name:"validate",rawName:"v-validate",value:{required:!0},expression:"{required: true}"},{name:"model",rawName:"v-model",value:t.form.message,expression:"form.message"}],ref:"focus",staticClass:"textarea",class:{"is-danger animated flash faster":t.errors.has("message")},attrs:{name:"message",cols:"30",placeholder:"Your message..."},domProps:{value:t.form.message},on:{input:function(e){e.target.composing||t.$set(t.form,"message",e.target.value)}}})])])]),s("div",{staticClass:"card-content has-background-light"},[s("div",{staticClass:"level"},[s("div"),s("div",{staticClass:"buttons"},[s("a",{staticClass:"button is-text",on:{click:function(e){return t.$emit("close")}}},[t._v("Cancel")]),s("button",{staticClass:"button is-primary",attrs:{type:"submit"}},[t._v("Post comment")])])])])])])]),s("button",{staticClass:"modal-close is-large",attrs:{"aria-label":"close"},on:{click:function(e){return t.$emit("close")}}})]):t._e(),t._l(t.comments,(function(e,a){return s("div",{key:e.SK,staticClass:"card animated fadeInRight faster has-ribbon",staticStyle:{"margin-bottom":"10px"},style:{"animation-delay":.1*a+"s"}},[t.isBlocked(e)?s("div",{staticClass:"ribbon is-danger is-small"},[t._v(t._s(e.details.comment_type))]):t._e(),s("div",{staticClass:"card-content"},[s("div",{staticClass:"content"},[s("p",{staticClass:"pre-formatted"},[t._v(t._s(e.details.message))])])]),s("div",{staticClass:"has-background-light",staticStyle:{padding:"10px"}},[s("p",{staticClass:"is-size-7 has-text-grey-light"},[t._v("Posted by "),s("b",[t._v(t._s(e.CreatedBy.name))]),t._v(" "+t._s(t._f("toTime")(e.createdAt)))])])])})),0==t.comments.length?s("div",{staticClass:"notification has-text-centered"},[s("h1",{staticClass:"subtitle has-text-grey-light"},[t._v("NO COMMENT YET")])]):t._e()],2)},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card-header"},[s("p",{staticClass:"card-header-title"},[t._v("Add comment")])])}]},"210b":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"tags is-right"},[s("span",{staticClass:"tag",class:{"is-info":"ASSIGNED"==t.task.Status||"ASSIGNED_RERAISE"==t.task.Status,"is-warning":"UNASSIGNED"==t.task.Status,"is-danger":"BLOCKED"==t.task.Status,"is-dark":"CANCELLED"==t.task.Status||"CANCEL_SUBMITTED"==t.task.Status,"is-success":"SUBMITTED"==t.task.Status||"COMPLETED"==t.task.Status||"RERAISED"==t.task.Status}},[t._v(t._s(t.task.Status))]),s("attachment-task",{attrs:{task:t.task}}),t.checkHash(t.task)?t._e():s("span",{},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"19",height:"19",viewBox:"0 0 24 24",fill:"none",stroke:"#d0021b","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"arcs"}},[s("path",{attrs:{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}}),s("line",{attrs:{x1:"12",y1:"9",x2:"12",y2:"13"}}),s("line",{attrs:{x1:"12",y1:"17",x2:"12",y2:"17"}})])])],1)},i=[]},"222f":function(t,e,s){},2273:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("div",{staticClass:"card"},[s("div",{staticClass:"card-content"},[s("h2",{staticClass:"title is-4"},[t._v("2019 Monthly Performance")]),s("apexchart",{attrs:{type:"bar",height:"350",options:t.chartOptions,series:t.series}})],1),s("div",{staticClass:"card-footer"},[s("div",{staticClass:"card-footer-item"},[s("div",{staticClass:"has-text-centered"},[s("span",{staticClass:"heading has-text-success"},[t._v(" < 1 day")]),s("span",{staticClass:"title is-2 has-text-success"},[t._v(t._s(t.perf[0])+"%")])])]),s("div",{staticClass:"card-footer-item"},[s("div",{staticClass:"has-text-centered"},[s("span",{staticClass:"heading has-text-info"},[t._v(" < 3 days")]),s("span",{staticClass:"title is-2 has-text-info"},[t._v(t._s(t.perf[1])+"%")])])]),s("div",{staticClass:"card-footer-item"},[s("div",{staticClass:"has-text-centered"},[s("span",{staticClass:"heading has-text-danger"},[t._v(" > 3 days")]),s("span",{staticClass:"title is-2 has-text-danger"},[t._v(t._s(t.perf[2])+"%")])])])])])])},i=[]},"22ad":function(t,e,s){},"232d":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("div",{staticClass:"card"},[s("div",{staticClass:"column"},[s("div",{staticClass:"level"},[t._m(0),s("div",{staticClass:"level-end"},[s("a",{staticClass:"button is-small",on:{click:function(e){t.asc=!t.asc}}},[t.asc?s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#000000","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}},[s("path",{attrs:{d:"M6 9l6 6 6-6"}})]):s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"#000000","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}},[s("path",{attrs:{d:"M18 15l-6-6-6 6"}})])])])])]),0==t.processors.length?s("div",{staticClass:"hero"},[t._m(1)]):s("table",{staticClass:"table is-fullwidth"},[s("tbody",t._l(t.sortedWorkload,(function(e){return s("tr",{key:e.staff_id},[s("td",[s("router-link",{staticClass:"has-text-weight-bold",attrs:{to:"/jobs/processors/"+e.staff_id}},[t._v(t._s(e.name))]),s("span",{staticClass:"heading"},[t._v(t._s(e.staff_id))])],1),s("td",[e.loaded?s("div",{staticClass:"level"},[s("div",{staticClass:"level-item"},[s("div",{staticClass:"has-text-centered"},[s("span",{staticClass:"has-text-info heading"},[t._v("Assg.")]),s("span",{staticClass:"has-text-info has-text-weight-bold is-size-5"},[t._v(t._s(e.assigned))])])]),s("div",{staticClass:"level-item"},[s("div",{staticClass:"has-text-centered"},[s("span",{staticClass:"has-text-danger heading"},[t._v("Blck.")]),s("span",{staticClass:"has-text-danger has-text-weight-bold is-size-5"},[t._v(t._s(e.blocked))])])])]):s("div",[s("div",{staticClass:"button is-loading is-white is-fullwidth"})])])])})),0)])])])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"level-start"},[s("h1",{staticClass:"title is-5"},[t._v("Workload Distribution")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"hero-body is-fullwidth"},[s("span",{staticClass:"button is-loading is-fullwidth is-white is-large"})])}]},2347:function(t,e,s){t.exports=[{path:"/",redirect:"/dashboard"},{path:"/masterlist",component:s("4ea0")["default"],children:[{path:"/",component:s("b2b5")["default"]},{meta:{blank:!0},path:"sheet",component:s("883d")["default"]}]},{path:"/dashboard",component:s("0e95")["default"]},{path:"/dropfiles/:PK",component:s("cb96")["default"]},{path:"/reports",component:s("0a65")["default"]},{path:"/search",component:s("8c59")["default"]},{path:"/jobs",component:s("9318")["default"],children:[{path:"",redirect:"/jobs/unassigned"},{path:"new",component:s("eb4f")["default"]},{path:"unassigned",component:s("c076")["default"]},{path:"recent",component:s("5d47")["default"]},{path:"mytasks",component:s("ac33")["default"]},{path:"projects/:project_id",component:s("fca5")["default"]},{path:"projects/:project_id/upload",component:s("eb4f")["default"]},{path:"projects/:project_id/:jobsheet_id",component:s("425f")["default"]},{path:"processors/:staff_id",component:s("bf97")["default"]}]},{path:"/dropfiles",component:s("0b16")["default"]},{path:"/tasks",component:s("95e8")["default"],children:[{path:"",redirect:"open"},{path:"open",component:s("ac33")["default"],props:{status:"Open"}},{path:"blocked",component:s("ac33")["default"],props:{status:"Blocked"}},{path:"recent",component:s("e25b")["default"]},{path:":task_id",component:s("ac33")["default"]},{path:"projects/:project_id",component:s("bdb0")["default"]},{path:"projects/:project_id/:jobsheet_id",component:s("da6b")["default"]}]},{path:"/settings",component:s("d974")["default"],children:[{path:"",redirect:"account"},{path:"account",component:s("77e3")["default"]},{path:"config",component:s("54e1")["default"]}]}]},2460:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"modal is-active"},[s("div",{staticClass:"modal-background"}),s("div",{staticClass:"modal-content"},[s("div",{staticClass:"modal-body"},[s("div",{staticClass:"box"},[s("h2",{staticClass:"title is-4"},[t._v("Patch feedback")]),s("div",{staticClass:"field"},[s("label",{staticClass:"label"},[t._v("Order no.")]),s("div",{staticClass:"control"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.form.order_no,expression:"form.order_no"}],staticClass:"input",attrs:{type:"text"},domProps:{value:t.form.order_no},on:{input:function(e){e.target.composing||t.$set(t.form,"order_no",e.target.value)}}})])]),s("div",{staticClass:"field"},[s("label",{staticClass:"label"},[t._v("Service ID")]),s("div",{staticClass:"control"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.form.service_id,expression:"form.service_id"}],staticClass:"input",attrs:{type:"text"},domProps:{value:t.form.service_id},on:{input:function(e){e.target.composing||t.$set(t.form,"service_id",e.target.value)}}})])]),s("div",{staticClass:"buttons"},[s("button",{staticClass:"button is-primary",on:{click:t.handlePatch}},[t._v("Patch")]),s("button",{staticClass:"button is-text",on:{click:function(e){return t.$emit("close")}}},[t._v("Cancel")])])])])])])},i=[]},"26ae":function(t,e,s){"use strict";s.r(e);var a=s("848b"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"26d3":function(t,e,s){s("c975");var a={UNASSIGNED:["ASSIGNED","CANCELLED","BLOCKED"],ASSIGNED:["ASSIGNED","SUBMITTED","CANCELLED","BLOCKED"],BLOCKED:["SUBMITTED","CANCELLED","ASSIGNED","BLOCKED"],CANCELLED:[],SUBMITTED:["CANCEL_SUBMITTED"],CANCEL_SUBMITTED:["RERAISED","ASSIGNED_RERAISE"],RERAISED:["CANCEL_SUBMITTED"],ASSIGNED_RERAISE:["RERAISED","CANCEL_SUBMITTED"]};t.exports=function(t){var e=a[t.Status]||["ASSIGNED"];return{canTransTo:function(t){return e.indexOf(t)>-1},isIn:function(e){return t.Status}}}},2780:function(t,e,s){"use strict";s.r(e);var a=s("8fc3"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},2912:function(t,e,s){"use strict";s.r(e);var a=s("b4cd"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},2948:function(t,e,s){"use strict";s.r(e);var a=s("5b0c"),i=s("4d57");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"2a17":function(t,e,s){"use strict";var a=s("4ea4");s("99af"),s("c975"),s("b0c0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0,s("96cf");var i=a(s("1da1")),n=a(s("58b7")),o=a(s("c687")),r=a(s("c1df")),l=function(){var t=(0,i["default"])(regeneratorRuntime.mark((function t(e){var s,a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(-1!=e.type.indexOf("spreadsheetml")){t.next=2;break}throw"Invalid file format";case 2:return s=JSON.stringify([o["default"].read(e),e.name,(0,r["default"])().unix()]),a="".concat((0,n["default"])(s),".").concat((0,r["default"])().unix()),console.log(a),t.abrupt("return",{filename:e.name,dropId:a,status:"open"});case 6:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),c={validate:l};e["default"]=c,t.exports=e.default,t.exports.default=e.default},"2b9a":function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("26d3")),r={props:["selectedTask"],data:function(){return{blockage:{blockage_type:"",remarks:"",type:"blockage"}}},methods:{submitBlockage:function(){var t=this;this.$validator.validateAll().then((function(e){e&&((0,o["default"])(t.selectedTask).canTransTo("BLOCKED")?t.$store.dispatch("om.tasks.blockage",{task:t.selectedTask,blockage:t.blockage}).then((function(e){t.$emit("blocked"),t.$emit("close")})):alert("".concat(t.selectedTask.Status," task cannot be block")))}))}},computed:{blockages:function(){return n.values(this.$store.state.om.configs.blockages)}}};e["default"]=r,t.exports=e.default,t.exports.default=e.default},"2c2b":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[t._m(0),s("div",{staticClass:"column"},[s("div",{staticClass:"columns"},[s("div",{staticClass:"column fullheight-scroller has-background-white-bis is-paddingless is-hidden-mobile is-2",staticStyle:{"border-right":"solid 1px hsl(0, 0%, 86%)"}},[s("div",{staticClass:"column"},[s("aside",{staticClass:"menu",staticStyle:{"padding-bottom":"40px"}},[s("p",{staticClass:"menu-label"},[t._v("\n                        Tasks\n                    ")]),s("ul",{staticClass:"menu-list"},[s("li",[s("router-link",{attrs:{to:"/tasks/open"}},[t._v("Open tasks")])],1),s("li",[s("router-link",{attrs:{to:"/tasks/blocked"}},[t._v("Blocked tasks")])],1),s("li",[s("router-link",{attrs:{to:"/tasks/recent"}},[t._v("Recent tasks")])],1)]),t._m(1),s("ul",{staticClass:"menu-list"},[s("li",{staticStyle:{"margin-bottom":"10px"}},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.filterProject,expression:"filterProject"}],staticClass:"input is-rounded is-small",attrs:{type:"text",placeholder:"filter"},domProps:{value:t.filterProject},on:{input:function(e){e.target.composing||(t.filterProject=e.target.value)}}})]),t._l(t.projects,(function(e){return s("li",{key:e.PK},[s("router-link",{attrs:{to:"/tasks/projects/"+e.PK}},[t._v(" "+t._s(e.name)+" "),t.hasProject(e.PK)?s("span",{staticClass:"has-text-success"},[t._v("•")]):t._e()])],1)})),t.isLoading("projects")?s("li",[s("span",{staticClass:"button is-text is-loading is-fullwidth"})]):t._e()],2)])])]),s("div",{staticClass:"column fullheight-scroller-2  "},[s("router-view")],1)])])])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"sectionheader"},[s("div",{staticClass:"card-content has-background-light"},[s("div",{staticClass:"level"},[s("div",[s("span",[s("span",{staticClass:"title is-5"},[t._v("Task Management")])])])])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"menu-label"},[s("div",{staticClass:"level"},[s("div",{staticClass:"badge"},[t._v("Projects")])])])}]},"2c97":function(t,e,s){},"2d96":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("nav",{staticClass:"navbar is-fixed-top",staticStyle:{"border-bottom":"solid 1px #d3d3d3","border-top":"solid 4px #003399"},attrs:{role:"navigation","aria-label":"main navigation"}},[s("div",{staticClass:"navbar-brand"},[s("a",{staticClass:"navbar-item",attrs:{href:"/"}},[s("svg",{attrs:{height:"32",viewBox:"0 0 115 43",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[s("rect",{attrs:{width:"115",height:"43",fill:"white"}}),s("path",{attrs:{d:"M38.3828 21.1406C38.3828 22.8125 38.1719 24.4219 37.75 25.9688C37.3281 27.5 36.7266 28.9375 35.9453 30.2812C35.1797 31.625 34.2578 32.8516 33.1797 33.9609C32.1016 35.0703 30.8984 36.0234 29.5703 36.8203C28.2578 37.6016 26.8438 38.2109 25.3281 38.6484C23.8281 39.0859 22.2656 39.3047 20.6406 39.3047C19 39.3047 17.4219 39.0859 15.9062 38.6484C14.4062 38.2109 13 37.6016 11.6875 36.8203C10.375 36.0234 9.17969 35.0703 8.10156 33.9609C7.02344 32.8516 6.10156 31.625 5.33594 30.2812C4.57031 28.9375 3.97656 27.5 3.55469 25.9688C3.13281 24.4219 2.92188 22.8125 2.92188 21.1406C2.92188 19.4688 3.13281 17.8594 3.55469 16.3125C3.97656 14.7656 4.57031 13.3203 5.33594 11.9766C6.10156 10.6328 7.02344 9.41406 8.10156 8.32031C9.17969 7.21094 10.375 6.26562 11.6875 5.48438C13 4.6875 14.4141 4.07812 15.9297 3.65625C17.4453 3.21875 19.0156 3 20.6406 3C22.2656 3 23.8281 3.21875 25.3281 3.65625C26.8438 4.07812 28.2578 4.6875 29.5703 5.48438C30.8984 6.26562 32.1016 7.21094 33.1797 8.32031C34.2578 9.41406 35.1797 10.6328 35.9453 11.9766C36.7266 13.3203 37.3281 14.7656 37.75 16.3125C38.1719 17.8594 38.3828 19.4688 38.3828 21.1406ZM33.7422 21.1406C33.7422 19.9062 33.5859 18.7188 33.2734 17.5781C32.9609 16.4219 32.5156 15.3438 31.9375 14.3438C31.375 13.3438 30.6953 12.4375 29.8984 11.625C29.1016 10.7969 28.2188 10.0938 27.25 9.51562C26.2812 8.92188 25.2344 8.46875 24.1094 8.15625C23 7.82812 21.8438 7.66406 20.6406 7.66406C18.8281 7.66406 17.1328 8.02344 15.5547 8.74219C13.9766 9.44531 12.5938 10.4062 11.4062 11.625C10.2188 12.8438 9.28125 14.2734 8.59375 15.9141C7.92188 17.5391 7.58594 19.2812 7.58594 21.1406C7.58594 23 7.92188 24.75 8.59375 26.3906C9.28125 28.0312 10.2188 29.4609 11.4062 30.6797C12.5938 31.8984 13.9766 32.8672 15.5547 33.5859C17.1328 34.2891 18.8281 34.6406 20.6406 34.6406C21.8438 34.6406 23 34.4844 24.1094 34.1719C25.2344 33.8438 26.2812 33.3906 27.25 32.8125C28.2188 32.2188 29.1016 31.5156 29.8984 30.7031C30.6953 29.875 31.375 28.9609 31.9375 27.9609C32.5156 26.9609 32.9609 25.8906 33.2734 24.75C33.5859 23.5938 33.7422 22.3906 33.7422 21.1406Z",fill:"#003399"}}),s("path",{attrs:{d:"M35.42 19.8009C35.42 20.7409 35.3 21.6909 35.06 22.6509C34.84 23.6109 34.49 24.4809 34.01 25.2609C33.55 26.0409 32.95 26.6809 32.21 27.1809C31.49 27.6809 30.63 27.9309 29.63 27.9309C28.99 27.9309 28.43 27.7509 27.95 27.3909C27.47 27.0309 27.13 26.5409 26.93 25.9209C26.51 26.4809 25.95 26.9509 25.25 27.3309C24.57 27.6909 23.89 27.8709 23.21 27.8709C22.43 27.8709 21.74 27.7309 21.14 27.4509C20.56 27.1509 20.06 26.7609 19.64 26.2809C19.24 25.7809 18.94 25.2109 18.74 24.5709C18.54 23.9309 18.44 23.2509 18.44 22.5309C18.44 21.7109 18.56 20.9109 18.8 20.1309C19.06 19.3309 19.43 18.6309 19.91 18.0309C20.39 17.4109 20.98 16.9109 21.68 16.5309C22.38 16.1509 23.18 15.9609 24.08 15.9609C24.7 15.9609 25.29 16.0909 25.85 16.3509C26.41 16.5909 26.86 16.9709 27.2 17.4909L27.5 16.2009H28.97L28.37 24.1209V24.3909C28.37 24.9509 28.49 25.4009 28.73 25.7409C28.97 26.0809 29.32 26.2509 29.78 26.2509C30.4 26.2509 30.95 26.0709 31.43 25.7109C31.91 25.3309 32.31 24.8409 32.63 24.2409C32.97 23.6209 33.22 22.9309 33.38 22.1709C33.54 21.3909 33.62 20.6009 33.62 19.8009C33.62 18.4609 33.38 17.2709 32.9 16.2309C32.42 15.1909 31.76 14.3109 30.92 13.5909C30.1 12.8709 29.13 12.3309 28.01 11.9709C26.91 11.5909 25.72 11.4009 24.44 11.4009C22.86 11.4009 21.43 11.6609 20.15 12.1809C18.89 12.7009 17.81 13.4309 16.91 14.3709C16.01 15.3109 15.32 16.4309 14.84 17.7309C14.36 19.0109 14.12 20.4309 14.12 21.9909C14.12 23.3709 14.35 24.6709 14.81 25.8909C15.27 27.0909 15.92 28.1509 16.76 29.0709C17.6 29.9709 18.6 30.6809 19.76 31.2009C20.92 31.7209 22.21 31.9809 23.63 31.9809C24.93 31.9809 26.2 31.8609 27.44 31.6209C28.7 31.3809 29.96 30.9909 31.22 30.4509V32.2509C29.9 32.8109 28.63 33.1909 27.41 33.3909C26.21 33.6109 24.93 33.7209 23.57 33.7209C21.89 33.7209 20.35 33.4109 18.95 32.7909C17.57 32.1909 16.38 31.3709 15.38 30.3309C14.4 29.2709 13.64 28.0309 13.1 26.6109C12.56 25.1709 12.29 23.6309 12.29 21.9909C12.29 20.1709 12.58 18.5109 13.16 17.0109C13.74 15.4909 14.56 14.1909 15.62 13.1109C16.68 12.0109 17.95 11.1609 19.43 10.5609C20.93 9.96089 22.6 9.66089 24.44 9.66089C25.96 9.66089 27.39 9.89089 28.73 10.3509C30.07 10.8109 31.23 11.4809 32.21 12.3609C33.21 13.2209 33.99 14.2809 34.55 15.5409C35.13 16.8009 35.42 18.2209 35.42 19.8009ZM26.84 21.3009C26.84 20.8209 26.79 20.3609 26.69 19.9209C26.59 19.4809 26.42 19.0909 26.18 18.7509C25.94 18.3909 25.63 18.1109 25.25 17.9109C24.87 17.7109 24.41 17.6109 23.87 17.6109C23.29 17.6109 22.77 17.7609 22.31 18.0609C21.87 18.3609 21.5 18.7509 21.2 19.2309C20.9 19.6909 20.67 20.2109 20.51 20.7909C20.35 21.3709 20.27 21.9509 20.27 22.5309C20.27 23.5709 20.51 24.4509 20.99 25.1709C21.49 25.8709 22.25 26.2209 23.27 26.2209C23.91 26.2209 24.45 26.0709 24.89 25.7709C25.35 25.4509 25.72 25.0509 26 24.5709C26.3 24.0909 26.51 23.5609 26.63 22.9809C26.77 22.4009 26.84 21.8409 26.84 21.3009Z",fill:"#003399"}}),s("path",{attrs:{d:"M58.2852 21.8125C58.2852 24.5547 57.5898 26.7109 56.1992 28.2812C54.8164 29.8516 52.8789 30.6367 50.3867 30.6367C47.8633 30.6367 45.9102 29.8672 44.5273 28.3281C43.1523 26.7812 42.4648 24.6016 42.4648 21.7891C42.4648 19 43.1484 16.8398 44.5156 15.3086C45.8906 13.7695 47.8555 13 50.4102 13C52.8945 13 54.8281 13.7812 56.2109 15.3438C57.5938 16.8984 58.2852 19.0547 58.2852 21.8125ZM44.7383 21.8125C44.7383 24.0781 45.2188 25.8047 46.1797 26.9922C47.1406 28.1797 48.543 28.7734 50.3867 28.7734C52.2227 28.7734 53.6172 28.1875 54.5703 27.0156C55.5234 25.8438 56 24.1094 56 21.8125C56 19.5234 55.5273 17.8008 54.582 16.6445C53.6367 15.4805 52.2461 14.8984 50.4102 14.8984C48.5508 14.8984 47.1406 15.4883 46.1797 16.668C45.2188 17.8398 44.7383 19.5547 44.7383 21.8125Z",fill:"#003399"}}),s("path",{attrs:{d:"M76.5664 21.2637C76.5664 22.1751 76.4199 23.015 76.127 23.7832C75.8405 24.5514 75.4368 25.1504 74.916 25.5801C74.3952 26.0098 73.793 26.2246 73.1094 26.2246C72.556 26.2246 72.0905 26.0651 71.7129 25.7461C71.3353 25.4206 71.1009 25.0007 71.0098 24.4863H70.9121C70.6582 25.0267 70.2871 25.4531 69.7988 25.7656C69.3105 26.0716 68.7409 26.2246 68.0898 26.2246C67.1003 26.2246 66.3223 25.8958 65.7559 25.2383C65.1895 24.5742 64.9062 23.6628 64.9062 22.5039C64.9062 21.638 65.0788 20.8633 65.4238 20.1797C65.7754 19.4896 66.2767 18.9557 66.9277 18.5781C67.5788 18.194 68.321 18.002 69.1543 18.002C69.6035 18.002 70.1146 18.0443 70.6875 18.1289C71.2669 18.207 71.7747 18.3242 72.2109 18.4805L71.9863 23.0508V23.2656C71.9863 24.4115 72.3997 24.9844 73.2266 24.9844C73.806 24.9844 74.2747 24.6426 74.6328 23.959C74.9909 23.2754 75.1699 22.3704 75.1699 21.2441C75.1699 20.0788 74.929 19.0566 74.4473 18.1777C73.972 17.2988 73.2917 16.625 72.4062 16.1562C71.5273 15.681 70.5215 15.4434 69.3887 15.4434C67.9564 15.4434 66.7064 15.7428 65.6387 16.3418C64.5775 16.9342 63.7637 17.7839 63.1973 18.8906C62.6309 19.9909 62.3477 21.2734 62.3477 22.7383C62.3477 24.7044 62.8652 26.2181 63.9004 27.2793C64.9355 28.334 66.4395 28.8613 68.4121 28.8613C69.7923 28.8613 71.2246 28.5749 72.709 28.002V29.3594C71.4069 29.9062 69.9746 30.1797 68.4121 30.1797C66.0423 30.1797 64.1999 29.5319 62.8848 28.2363C61.5697 26.9342 60.9121 25.1243 60.9121 22.8066C60.9121 21.1204 61.2604 19.6165 61.957 18.2949C62.6602 16.9668 63.6562 15.9414 64.9453 15.2188C66.2344 14.4896 67.7155 14.125 69.3887 14.125C70.7884 14.125 72.0352 14.4212 73.1289 15.0137C74.2292 15.6061 75.0755 16.446 75.668 17.5332C76.2669 18.6204 76.5664 19.8639 76.5664 21.2637ZM66.4688 22.543C66.4688 24.1706 67.097 24.9844 68.3535 24.9844C69.6882 24.9844 70.4141 23.9785 70.5312 21.9668L70.6484 19.4668C70.1732 19.3301 69.6816 19.2617 69.1738 19.2617C68.334 19.2617 67.6732 19.5514 67.1914 20.1309C66.7096 20.7103 66.4688 21.5143 66.4688 22.543Z",fill:"#003399"}}),s("path",{attrs:{d:"M87.7773 24.584C87.7773 25.8275 87.3281 26.8073 86.4297 27.5234C85.5312 28.2396 84.2812 28.5977 82.6797 28.5977C80.987 28.5977 79.6784 28.373 78.7539 27.9238V26.2051C79.3594 26.4655 80.0202 26.6706 80.7363 26.8203C81.459 26.9701 82.1328 27.0449 82.7578 27.0449C83.8255 27.0449 84.6296 26.8398 85.1699 26.4297C85.7103 26.0195 85.9805 25.4564 85.9805 24.7402C85.9805 24.265 85.8828 23.8743 85.6875 23.5684C85.4922 23.2624 85.1634 22.9759 84.7012 22.709C84.2454 22.4421 83.5521 22.1393 82.6211 21.8008C81.2995 21.319 80.3555 20.7526 79.7891 20.1016C79.2227 19.444 78.9395 18.5977 78.9395 17.5625C78.9395 16.4492 79.3561 15.5638 80.1895 14.9062C81.0293 14.2487 82.1328 13.9199 83.5 13.9199C84.9258 13.9199 86.2376 14.1868 87.4355 14.7207L86.8789 16.2637C85.6549 15.7559 84.5156 15.502 83.4609 15.502C82.6146 15.502 81.9505 15.6842 81.4688 16.0488C80.9935 16.4134 80.7559 16.9245 80.7559 17.582C80.7559 18.0508 80.847 18.4414 81.0293 18.7539C81.2181 19.0599 81.5176 19.3398 81.9277 19.5938C82.3444 19.8477 82.9954 20.1374 83.8809 20.4629C84.9421 20.8535 85.7331 21.2344 86.2539 21.6055C86.7747 21.9701 87.1589 22.3932 87.4062 22.875C87.6536 23.3503 87.7773 23.9199 87.7773 24.584Z",fill:"#003399"}}),s("path",{attrs:{d:"M94.3789 21.1465L98.0996 14.125H100.033L95.2773 22.8652V28.4023H93.4707V22.9434L88.7148 14.125H90.6777L94.3789 21.1465Z",fill:"#003399"}}),s("path",{attrs:{d:"M110.082 24.584C110.082 25.8275 109.633 26.8073 108.734 27.5234C107.836 28.2396 106.586 28.5977 104.984 28.5977C103.292 28.5977 101.983 28.373 101.059 27.9238V26.2051C101.664 26.4655 102.325 26.6706 103.041 26.8203C103.764 26.9701 104.438 27.0449 105.062 27.0449C106.13 27.0449 106.934 26.8398 107.475 26.4297C108.015 26.0195 108.285 25.4564 108.285 24.7402C108.285 24.265 108.188 23.8743 107.992 23.5684C107.797 23.2624 107.468 22.9759 107.006 22.709C106.55 22.4421 105.857 22.1393 104.926 21.8008C103.604 21.319 102.66 20.7526 102.094 20.1016C101.527 19.444 101.244 18.5977 101.244 17.5625C101.244 16.4492 101.661 15.5638 102.494 14.9062C103.334 14.2487 104.438 13.9199 105.805 13.9199C107.23 13.9199 108.542 14.1868 109.74 14.7207L109.184 16.2637C107.96 15.7559 106.82 15.502 105.766 15.502C104.919 15.502 104.255 15.6842 103.773 16.0488C103.298 16.4134 103.061 16.9245 103.061 17.582C103.061 18.0508 103.152 18.4414 103.334 18.7539C103.523 19.0599 103.822 19.3398 104.232 19.5938C104.649 19.8477 105.3 20.1374 106.186 20.4629C107.247 20.8535 108.038 21.2344 108.559 21.6055C109.079 21.9701 109.464 22.3932 109.711 22.875C109.958 23.3503 110.082 23.9199 110.082 24.584Z",fill:"#003399"}})])]),t._m(0),s("div",{staticClass:"navbar-item"},[s("form",{on:{submit:function(e){return e.preventDefault(),t.handleSearch(e)}}},[s("div",{staticClass:"field is-grouped "},[s("div",{staticClass:"control "},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.search,expression:"search"}],staticClass:"input is-rounded is-small ",attrs:{type:"text",placeholder:"Search"},domProps:{value:t.search},on:{input:function(e){e.target.composing||(t.search=e.target.value)}}})]),s("div",{staticClass:"control"},[s("label",{staticClass:"label tooltip is-tooltip-right has-text-grey-light",staticStyle:{"margin-left":"-10px","margin-top":"5px"},attrs:{"data-tooltip":"Order no, Service ID or Task ID"}},[s("svg",{staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",width:"18",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M11 18h2v-2h-2v2zm1-16C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-2.21 0-4 1.79-4 4h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4z"}})])])])])])]),t._m(1)]),s("div",{staticClass:"navbar-menu",attrs:{id:"navbarBasicExample"}},[s("div",{staticClass:"navbar-end"},[s("div",{staticClass:"navbar-item"},[s("span",{key:t.currentActiveRole,staticClass:"tag is-info is-capitalized animated bounceIn"},[t._v(t._s(t._f("formatLabel")(t.currentActiveRole)))])]),s("div",{staticClass:"navbar-item"},[s("span",[t._v("Welcome  \n          "),s("b",[t._v(t._s(t.user))])])]),s("div",{staticClass:"navbar-item"},[s("div",{staticClass:"navbar-item has-dropdown is-hoverable"},[s("span",{staticClass:"button is-small"},[t._v("...")]),s("div",{staticClass:"navbar-dropdown is-boxed is-right"},[t.userRoles.length>1?s("div",[s("a",{staticClass:"navbar-item",on:{click:function(e){t.showRoleSwitched=!0}}},[s("span",[t._v("Switch role")])])]):t._e(),s("a",{staticClass:"navbar-item",attrs:{href:"javascript:void(0)"},on:{click:function(e){return e.preventDefault(),t.$emit("logout")}}},[t._v("Logout")])])])])])]),t.showRoleSwitched?s("role-switcher",{on:{close:function(e){t.showRoleSwitched=!1},switchto:t.switchRole}}):t._e()],1)},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"navbar-item"},[s("div",{staticClass:"control"},[s("div",{staticClass:"tags has-addons"},[s("span",{staticClass:"tag is-primary"},[t._v("Global")])])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("a",{staticClass:"navbar-burger burger",attrs:{role:"button","aria-label":"menu","aria-expanded":"false","data-target":"navbarBasicExample"}},[s("span",{attrs:{"aria-hidden":"true"}}),s("span",{attrs:{"aria-hidden":"true"}}),s("span",{attrs:{"aria-hidden":"true"}})])}]},"2fc9":function(t,e,s){"use strict";s.r(e);var a=s("a15a"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"30e7":function(t,e,s){},"31c1":function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var a={mounted:function(){this.$store.dispatch("isLoaded")}};e["default"]=a,t.exports=e.default,t.exports.default=e.default},"339f":function(t,e,s){"use strict";var a=s("dbce"),i=s("4ea4");s("c975"),s("b0c0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0,s("96cf");var n=i(s("1da1")),o=a(s("b17e")),r=i(s("c687")),l={data:function(){return{version:"",filename:"",file:!1,headers:!1,currentFile:!1,isSaving:!1}},computed:{tpl:function(){return this.$store.state.om.configs.template?{version:this.$store.state.om.configs.template.version,file:{SK:"attachment.template.template.config",details:{filename:this.$store.state.om.configs["attachment.template.template.config"].filename}}}:{version:0,file:{details:{}}}}},methods:{downloadTemplate:function(){var t=this;return(0,n["default"])(regeneratorRuntime.mark((function e(){var s;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$store.dispatch("om.attachments.download",t.tpl.file);case 2:s=e.sent,window.open(s,"_BLANK");case 4:case"end":return e.stop()}}),e)})))()},handleSubmit:function(){var t=this;this.$validator.validateAll().then((function(e){e&&(t.isSaving=!0,t.uploadFile().then((function(){t.$store.dispatch("om.config.update",{type:"template",values:{version:t.version}}).then((function(){t.isSaving=!1})),t.$store.dispatch("om.config.update",{type:"template.columns.".concat(t.version),values:t.headers}).then((function(){t.isSaving=!1,t.$store.dispatch("om.config.load",!0),alert("New template uploaded!")}))})))}))},uploadFile:function(){var t=this;return this.$store.dispatch("om.attachments.upload",{file:this.file,resource:{PK:"config",SK:"template"},attachment_type:"template"}).then((function(e){t.currentFile=e}))["catch"]((function(e){t.$store.dispatch("showError","Failed to upload file")}))},analyzeFile:function(t){var e=this;t.stopPropagation(),t.preventDefault(),this.file=o.head(t.target.files),this.filename=this.file.name;var s=function(t){return t&&!t.Sheet1||alert("Invalid template uploaded"),t},a=function(t){return o.pipe(o.keys,o.filter((function(t){return 0==t.indexOf("_")})),(function(e){return o.omit(e,t)}))(t)},i=function(t){return e.headers=t};r["default"].headers(this.file).then(s).then(a).then(i)}}};e["default"]=l,t.exports=e.default,t.exports.default=e.default},3463:function(t,e,s){"use strict";s.r(e);var a=s("2460"),i=s("03f5");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"350b":function(t,e,s){"use strict";s.r(e);var a=s("8d85"),i=s("0134");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);s("ff13");var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"35ca":function(t,e,s){"use strict";s.r(e);var a=s("1d02"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"36dc":function(t,e,s){"use strict";s.r(e);var a=s("46f0"),i=s("f00e");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},3749:function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");s("99af"),s("b0c0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("c687")),r=a(s("6ab2")),l={components:{SheetReview:r["default"]},mounted:function(){this.$store.dispatch("isLoaded")},data:function(){return{file:!1,filename:"",records:[],processing:!1,step:1,jobsheet_id:!1}},computed:{project_id:function(){return this.$route.params.project_id}},methods:{handleClose:function(){this.$router.push("/jobs")},handleFileChange:function(t){t.stopPropagation(),t.preventDefault(),this.file=n.head(t.target.files),this.filename=this.file.name},processJobSheet:function(){var t=this;this.processing=!0,o["default"].read(this.file).then((function(e){t.processing=!1,t.$store.dispatch("om.jobsheet.checkDuplicate",{records:e}).then((function(s){t.jobsheet_id=s,t.records=e,t.step=2}))["catch"]((function(t){console.log(t),alert(t)}))}))},acceptJobSheet:function(t){var e=this,s=this.$store.state.om.dropfile.files[t];this.$store.dispatch("om.jobsheet.accept",{drop_id:t,file:this.file,records:this.records,project_id:this.project_id,dropfile:s.details}).then((function(t){e.$router.push("/jobs/projects/".concat(e.project_id,"/").concat(t.PK))}))}}};e["default"]=l,t.exports=e.default,t.exports.default=e.default},3883:function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");s("4de4"),s("4160"),s("b0c0"),s("d3b7"),s("3ca3"),s("159b"),s("ddb0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("c1df")),r=a(s("f137")),l=a(s("350b")),c=a(s("26d3")),d=a(s("d459")),u={components:{TableTasks:r["default"],QuickviewTask:l["default"],TaskAssignment:d["default"]},data:function(){return{tab:"tasks",task:!1,assignment_mode:!1,selected_tasks:{},processor:!1,isLoading:!1,selectedDistribution:!1,everyone:!1,showBlocked:!1}},computed:{me:function(){return this.$store.state.login.user},tasks:function(){return this.$store.state.om.unassigned},filteredTasks:function(){var t=this;return this.tasks.filter((function(e){return!!t.everyone||e.CreatedBy.staff_id==t.me.staff_id})).filter((function(e){return!!t.showBlocked||"UNASSIGNED"==e.Status}))},total_selected:function(){return n.pipe(n.values,n.sum)(this.selected_tasks)},processors:function(){return this.$store.state.om.processors},processorsMapping:function(){return n.indexBy(n.prop("staff_id"),this.processors)}},methods:{getProcessorName:function(t){return(this.processorsMapping[t]||{name:""}).name},handleAssign:function(t){var e=this;if(!this.isLoading){this.isLoading=!0;var s=this.tasks.filter((function(t){return e.selected_tasks[t.TaskID]}));this.$store.dispatch("om.processor.assign",{processor:t,tasks:s}).then((function(a){s.forEach((function(e){e.Status="UNASSIGNED"==e.Status?"ASSIGNED":e.Status,e.GS1=t.staff_id})),e.isLoading=!1,e.selected_tasks={},e.processor=""}))}},selectAll:function(){this.$set(this,"selected_tasks",n.pipe(n.indexBy(n.prop("TaskID")),n.map((function(){return!0})))(this.filteredTasks))},clearSelection:function(){this.selected_tasks={}},isSelected:function(t){return this.assignment_mode&&this.selected_tasks[t.TaskID]},loadData:function(){return Promise.all([this.$store.dispatch("om.jobs.allunassigned"),this.$store.dispatch("om.processors.list")])},canAssign:function(t){return(0,c["default"])(t).canTransTo("ASSIGNED")||(0,c["default"])(t).canTransTo("ASSIGNED_RERAISE")},viewTask:function(t){if(this.assignment_mode){if(!this.canAssign(t))return void alert("You cannot assign this task");this.$set(this.selected_tasks,t.TaskID,!this.selected_tasks[t.TaskID])}else this.task=t}},mounted:function(){var t=this;this.loadData().then((function(){t.$store.dispatch("isLoaded"),t.everyone=t.$route.query.everyone}))},filters:{shortKey:function(t){return n.take(12,t)},formatDate:function(t){return(0,o["default"])(t).format("DD MMM YYYY")}}};e["default"]=u,t.exports=e.default,t.exports.default=e.default},"38d6":function(t,e,s){"use strict";s.r(e);var a=s("3883"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"39c7":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("div",{ref:"spreadsheet"})])},i=[]},"3a46":function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");s("4795"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("c1df")),r=a(s("d460")),l={components:{Comments:r["default"]},props:["selectedTask"],data:function(){return{currentForm:{qoute_no:"",order_no:""},submitting:!1,showCommentForm:!1,copyStates:{}}},computed:{details:function(){return n.omit(["TaskID"],this.$store.getters.om_taskDetails(this.selectedTask.details))}},methods:{copyToCb:function(t,e){var s=this;s.$set(s.copyStates,e,"loading"),this.$copyText(t).then((function(t){setTimeout((function(){s.$set(s.copyStates,e,"copied"),setTimeout((function(){s.$set(s.copyStates,e,!1)}),500)}),300)}),(function(t){alert("Can not copy"),console.log(t)}))},copyClass:function(t){return{"is-loading":"loading"==this.copyStates[t],"is-success":"copied"==this.copyStates[t]}}},filters:{diffNow:function(t){return(0,o["default"])(t).fromNow()}}};e["default"]=l,t.exports=e.default,t.exports.default=e.default},"3b93":function(t,e,s){"use strict";s.r(e);var a=s("fd5b"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"3e0f":function(t,e,s){"use strict";s.r(e);var a=s("6c6f"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"3f9b":function(t,e,s){"use strict";s.r(e);var a=s("9572"),i=s("44e7");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"425f":function(t,e,s){"use strict";s.r(e);var a=s("5c85"),i=s("c192");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"43ce":function(t,e,s){"use strict";s.r(e);var a=s("1601"),i=s("a60e");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"44e7":function(t,e,s){"use strict";s.r(e);var a=s("2b9a"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},4562:function(t,e,s){"use strict";var a=s("91e5"),i=s.n(a);i.a},4678:function(t,e,s){var a={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"2554","./bs.js":"2554","./ca":"d716","./ca.js":"d716","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-SG":"cdab","./en-SG.js":"cdab","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3","./en-ie.js":"e1d3","./en-il":"7333","./en-il.js":"7333","./en-nz":"6f50","./en-nz.js":"6f50","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df4","./fa.js":"8df4","./fi":"81e9","./fi.js":"81e9","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b4","./gd.js":"f6b4","./gl":"8840","./gl.js":"8840","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-tw":"90ea","./zh-tw.js":"90ea"};function i(t){var e=n(t);return s(e)}function n(t){if(!s.o(a,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return a[t]}i.keys=function(){return Object.keys(a)},i.resolve=n,t.exports=i,i.id="4678"},4688:function(t,e,s){"use strict";var a=s("dbce"),i=s("4ea4");s("99af"),s("4de4"),s("c975"),s("b0c0"),s("d3b7"),s("4795"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0,s("96cf");var n=i(s("1da1")),o=a(s("b17e")),r=i(s("c1df")),l=i(s("e2e8")),c=i(s("0c96")),d=function(t){return new Promise((function(e){return setTimeout(e,t)}))},u={components:{OrderRequestUploader:l["default"],DropStatus:c["default"]},data:function(){return{filesTeam:[],isLoading:!1,filterKeyword:"",sortKey:"createdAt",sortDescend:!0,form:{},templateFilename:"om_submission_template.xlsx",tab:"my"}},mounted:function(){var t=this;return(0,n["default"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$store.dispatch("om.init"),t.$store.dispatch("om.dropfile.resetKey"),t.loadDropfiles();case 3:case"end":return e.stop()}}),e)})))()},methods:{downloadTemplate:function(){var t=this;return(0,n["default"])(regeneratorRuntime.mark((function e(){var s;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$store.dispatch("om.attachments.download",t.tpl.file);case 2:s=e.sent,window.open(s,"_BLANK");case 4:case"end":return e.stop()}}),e)})))()},hack:function(){this.$router.push("/dropfiles")},sortTable:function(t){this.sortKey==t?this.sortDescend=!this.sortDescend:this.sortKey=t},downloadFile:function(t){var e=this;return(0,n["default"])(regeneratorRuntime.mark((function s(){var a;return regeneratorRuntime.wrap((function(s){while(1)switch(s.prev=s.next){case 0:return s.next=2,e.$store.dispatch("om.dropfile.download",{drop_id:t.drop_id});case 2:a=s.sent,window.open(a,"_BLANK");case 4:case"end":return s.stop()}}),s)})))()},loadDropfiles:function(){var t=this;return(0,n["default"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$store.dispatch("om.dropfile.list"),t.$store.dispatch("om.dropfile.listteam",t.user.staff_id);case 2:case"end":return e.stop()}}),e)})))()},handleUpload:function(t){this.loadDropfiles()},removeFile:function(t){var e=this;confirm("Confirm to delete the file?")&&this.$store.dispatch("om.dropfile.remove",{drop_id:t}).then((function(){e.loadDropfiles()}))},copyData:function(t,e){this.$copyText(o.take(12,t.PK)),e.toElement.dataset.tooltip="Copied",d(1e3).then((function(){e.toElement.dataset.tooltip="Copy"}))},copyText:function(t,e){this.$copyText(t),e.toElement.dataset.tooltip="Copied",d(1e3).then((function(){e.toElement.dataset.tooltip="Copy"}))},getRejectionReason:function(t){return"REJECTED"!=t.Status?"":"".concat(t.details.rejection.reason,"\n\n-").concat(t.details.rejection.rejectedBy.name,"-")}},computed:{sortDirection:function(){return this.sortDescend?"descend":"ascend"},sortIcon:function(){return this.sortDescend?'<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-sort-descending" width="44" height="44" viewBox="0 0 24 24" stroke-width="1.5" stroke="#2c3e50" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>\n                            <line x1="4" y1="6" x2="13" y2="6" />\n                            <line x1="4" y1="12" x2="11" y2="12" />\n                            <line x1="4" y1="18" x2="11" y2="18" />\n                            <polyline points="15 15 18 18 21 15" />\n                            <line x1="18" y1="6" x2="18" y2="18" />\n                        </svg>':'<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-sort-ascending" width="44" height="44" viewBox="0 0 24 24" stroke-width="1.5" stroke="#2c3e50" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>\n                        <line x1="4" y1="6" x2="11" y2="6" />\n                        <line x1="4" y1="12" x2="11" y2="12" />\n                        <line x1="4" y1="18" x2="13" y2="18" />\n                        <polyline points="15 9 18 6 21 9" />\n                        <line x1="18" y1="6" x2="18" y2="18" />\n                    </svg>'},lastKey:function(){return this.$store.state.om.dropfile.lastKey},user:function(){return this.$store.state.login.user},filteredFiles:function(){var t=this,e=this.files.filter((function(e){return JSON.stringify(e).toLowerCase().indexOf(t.filterKeyword)>-1&&e.CreatedBy.name==t.user.name}));return o.sort(o[this.sortDirection](o.prop(this.sortKey)),e)},filteredFilesTeam:function(){var t=this,e=this.teamfiles.filter((function(e){return JSON.stringify(e).toLowerCase().indexOf(t.filterKeyword)>-1}));return e},files:function(){return o.pipe(o.values)(this.$store.state.om.dropfile.files)},teamfiles:function(){return o.pipe(o.values)(this.$store.state.om.dropfile.teamfiles)},nofile:function(){return 0==o.keys(this.files).length},tpl:function(){return{file:{SK:"attachment.template.template.form",details:{filename:this.templateFilename}}}}},filters:{fromNow:function(t){return(0,r["default"])(t).fromNow()},formatDate:function(t){return(0,r["default"])(t).format("DD MMM YYYY, hh:mm A")},shortKey:function(t){return o.take(12,t)}}};e["default"]=u,t.exports=e.default,t.exports.default=e.default},"46f0":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("div",{staticClass:"column",staticStyle:{"border-bottom":"solid 1px #ccc"}},[s("div",{staticClass:"level"},[s("div",{staticClass:"level-start"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.filterKeyword,expression:"filterKeyword"}],staticClass:"input is-rounded",attrs:{type:"text",placeholder:"filter"},domProps:{value:t.filterKeyword},on:{input:function(e){e.target.composing||(t.filterKeyword=e.target.value)}}})])])]),s("table",{staticClass:"table is-fullwidth is-hoverable"},[s("thead",[s("tr",[s("th",[t._v("Jobsheet ID")]),s("th",[t._v("Uploaded By")]),s("th",{staticClass:"is-clickable",on:{click:function(e){return t.sortTable("createdAt")}}},["createdAt"==t.sortKey?s("span",{staticClass:"icon is-pulled-right",domProps:{innerHTML:t._s(t.sortIcon)}}):t._e(),s("abbr",{attrs:{title:"Sort by this"}},[t._v("Created At")])]),s("th",{staticClass:"is-clickable",on:{click:function(e){return t.sortTable("TaskCounter")}}},["TaskCounter"==t.sortKey?s("span",{staticClass:"icon is-pulled-right",domProps:{innerHTML:t._s(t.sortIcon)}}):t._e(),s("abbr",{attrs:{title:"Sort by this"}},[t._v("Task Counter")])]),s("th",{staticClass:"is-clickable",on:{click:function(e){return t.sortTable("Status")}}},["Status"==t.sortKey?s("span",{staticClass:"icon is-pulled-right",domProps:{innerHTML:t._s(t.sortIcon)}}):t._e(),s("abbr",{attrs:{title:"Sort by this"}},[t._v("Status")])])])]),s("tbody",t._l(t.filteredJobsheets,(function(e){return s("tr",{key:e.PK,staticClass:"is-clickable",on:{click:function(s){return t.navigateTo(e)}}},[s("td",[s("span",{staticClass:"tooltip is-tooltip-right",attrs:{"data-tooltip":e.PK}},[t._v(t._s(t._f("shortKey")(e.PK)))]),s("span",{staticClass:"heading"},[t._v(t._s(e.GS2))])]),s("td",[t._v("\n                    "+t._s(e.CreatedBy.name)+"\n                    "),s("span",{staticClass:"heading"},[t._v(t._s(e.CreatedBy.staff_id))])]),s("td",[t._v("\n                    "+t._s(t._f("formatDate")(e.createdAt))+"\n                    "),s("br"),s("span",{staticClass:"help"},[t._v(t._s(t._f("formatTime")(e.createdAt)))])]),s("td",[t._v(t._s(e.TaskCounter))]),s("td",[s("span",{staticClass:"tag",class:{"is-info":"OPEN"==e.Status,"is-success":"COMPLETED"==e.Status}},[t._v(t._s(e.Status))])])])})),0)])])},i=[]},4887:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("feature-flag",{attrs:{scope:"developer"}},[s("div",{staticClass:"is-pulled-right"},[s("div",{staticClass:"popover is-popover-top ",class:{"is-popover-active":t.show}},[s("span",{staticClass:"is-small is-text",on:{click:function(e){t.show=!t.show}}},[s("i",[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"18",viewBox:"0 0 24 24",fill:"none",stroke:"#000000","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}},[s("circle",{attrs:{cx:"12",cy:"12",r:"10"}}),s("line",{attrs:{x1:"12",y1:"16",x2:"12",y2:"12"}}),s("line",{attrs:{x1:"12",y1:"8",x2:"12",y2:"8"}})])])]),s("div",{staticClass:"popover-content"},[s("pre",[t._v(t._s(this.content))])])])])])],1)},i=[]},4998:function(t,e,s){"use strict";var a=s("d03b"),i=s.n(a);i.a},"4ac2":function(t,e,s){"use strict";s.r(e);var a=s("e757"),i=s("fba7");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"4b10":function(t,e,s){"use strict";var a=s("09bf"),i=s.n(a);i.a},"4b62":function(t,e,s){"use strict";var a=s("769b"),i=s.n(a);i.a},"4be0":function(t,e,s){"use strict";var a=s("4ea4");Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("fe45")),n={props:["task"],components:{AttachmentTask:i["default"]},methods:{checkHash:function(t){return this.$store.getters.om_taskCheck(t)}}};e["default"]=n,t.exports=e.default,t.exports.default=e.default},"4d57":function(t,e,s){"use strict";s.r(e);var a=s("f1c3"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"4ea0":function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("router-view")],1)},i=[],n=s("2877"),o={},r=Object(n["a"])(o,a,i,!1,null,null,null);e["default"]=r.exports},"4eb5":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("div",{staticClass:"popover is-popover-top is-popover-active",on:{mouseover:function(e){t.showPopover=!0},mouseleave:function(e){t.showPopover=!1}}},[s("span",{staticClass:"tag popover-trigger",class:{"is-dark":"OPEN"==t.file.Status,"is-info":"UPLOADED"==t.file.Status,"is-danger":"REJECTED"==t.file.Status||"ON_HOLD"==t.file.Status&&t.file.details.on_hold.isOverdue,"is-success":"TASK_COMPLETED"==t.file.Status,"is-warning":"ON_HOLD"==t.file.Status&&!t.file.details.on_hold.isOverdue}},[t._v(t._s(t.file.Status))]),"ON_HOLD"==t.file.Status?s("span",{staticClass:"help"},[s("p",[t._v("Due on "+t._s(t.file.details.on_hold.duedate))]),t._v(" Hold by "+t._s(t.file.details.on_hold.holdBy.name)+" ")]):t._e(),t.showPopover&&t.file.details.rejection?s("div",{staticClass:"popover-content"},[s("div",{staticClass:"content pre-formatted"},[s("p",[s("strong",[t._v("REASON:")]),t._v("\n"+t._s(t.file.details.rejection.reason))]),s("p",[t._v("-"+t._s(t.file.details.rejection.rejectedBy.name))])])]):t.showPopover&&t.file.details.on_hold?s("div",{staticClass:"popover-content"},[s("div",{staticClass:"content pre-formatted"},[s("p",[s("strong",[t._v("REASON:")]),t._v("\n"+t._s(t.file.details.on_hold.reason))])])]):t.showPopover&&"OPEN"!=t.file.Status?s("div",{staticClass:"popover-content"},[t.tasks?s("table",{staticClass:"table is-fullwidth"},[s("tbody",t._l(t.tasks,(function(e,a){return s("tr",{key:a},[s("th",[t._v(t._s(a))]),s("td",[t._v(t._s(e))])])})),0)]):s("span",{staticClass:"button is-text is-loading"})]):t._e()])])},i=[]},"4fa2":function(t,e,s){"use strict";var a=s("dbce"),i=s("4ea4");s("4de4"),s("c975"),s("b0c0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("c1df")),o=a(s("b17e")),r=i(s("fe45")),l=i(s("7499")),c=i(s("43ce")),d=i(s("26d3")),u=i(s("2948")),f=(s("58b7"),{components:{AttachmentTask:r["default"],ExportExcel:c["default"],TaskStatus:l["default"],Sorter:u["default"]},props:["tasks","isSelected","processors","task","defaultFilter","defaultFilterType"],data:function(){return{filterKeyword:this.defaultFilter||"",sortedTasks:[],filterType:this.defaultFilterType||"all",filterDiff:0}},computed:{jobsheets:function(){return this.$store.state.om},filteredTasksByKeyword:function(){var t=this;return this.tasks.filter((function(e){return"all"==t.filterType||t.dateRange[0].isSameOrBefore(e.updatedAt,"day")&&t.dateRange[1].isAfter(e.updatedAt,"day")})).filter((function(e){return JSON.stringify(o.omit(["details"],e)).toLowerCase().indexOf(t.filterKeyword.toLowerCase())>-1}))},dateRange:function(){return[(0,n["default"])().startOf(this.filterType).add(this.filterDiff,this.filterType),(0,n["default"])().startOf(this.filterType).add(this.filterDiff+1,this.filterType)]},dateRangeDis:function(){switch(this.filterType){case"day":return this.dateRange[0].format("DD MMM YYYY");case"week":return o.pipe(o.map((function(t){return t.format("DD MMM YYYY")})),o.join(" → "))(this.dateRange);case"month":return this.dateRange[0].format("MMMM YYYY");case"all":return"All"}}},mounted:function(){},methods:{odNumber:function(t){return t.feedback?t.feedback["OD Number"]:t.details["OD Number"]?t.details["OD Number"]:"Not Specified"},odLink:function(t){return t.feedback?t.feedback["OD Link"]:t.details["OD Link"]?t.details["OD Link"]:null},onSorted:function(t){this.sortedTasks=t},canAssign:function(t){return(0,d["default"])(t).canTransTo("ASSIGNED")||(0,d["default"])(t).canTransTo("ASSIGNED_RERAISE")},viewTask:function(t){this.$emit("taskSelected",t)},getProcessorName:function(t){return(this.processors[t]||{name:""}).name},getProjectName:function(t){return(this.$store.state.om.projects[t]||{name:t}).name},updateDiff:function(t){1==t&&this.filterDiff<0?this.filterDiff++:-1==t&&this.filterDiff--}},filters:{diffNow:function(t){return(0,n["default"])(t).fromNow()},shortKey:function(t){return o.take(12,t)},formatDate:function(t){return(0,n["default"])(t).format("DD MMM YYYY, hh:mm A")},fromNow:function(t){return(0,n["default"])(t).fromNow()}}});e["default"]=f,t.exports=e.default,t.exports.default=e.default},"501c":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card-content"},[s("quickview-task",{attrs:{task:t.task},on:{close:function(e){t.task=!1},blocked:t.loadData}}),s("div",{staticClass:"card"},[s("table-task",{attrs:{filteredTasks:t.tasks,getProjectName:t.getProjectName,isViewed:t.isViewed,selectedSessionTasks:t.noop,getProcessorName:t.getProcessorName,viewTask:t.viewTask}})],1)],1)},i=[]},"506f":function(t,e,s){"use strict";s.r(e);var a=s("83c3"),i=s("3e0f");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"50e4":function(t,e,s){"use strict";var a=s("8f18"),i=s.n(a);i.a},"51ad":function(t,e,s){"use strict";s.r(e);var a=s("e7f7"),i=s("6452");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"520a":function(t,e,s){"use strict";s("4795"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var a={methods:{switchTo:function(t){var e=this;this.$emit("switchto",t),setTimeout((function(){return e.$emit("close")}),500)},hasRole:function(t){return this.$store.getters.om_has_role(t)},isActive:function(t){return this.$store.getters.om_role(t)}}};e["default"]=a,t.exports=e.default,t.exports.default=e.default},5276:function(t,e,s){"use strict";var a=s("4ea4");Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("b474")),n={props:["content"],components:{FeatureFlag:i["default"]},data:function(){return{show:!1}}};e["default"]=n,t.exports=e.default,t.exports.default=e.default},"53c1":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{ref:"mainwin"},[t.showFloat?s("div",{staticClass:"tags has-addons is-marginless is-paddingless animated fadeInUp faster",attrs:{id:"review-box"}},[s("div",{staticClass:"tag is-dark"},[t._v("\n            Record\n        ")]),s("div",{staticClass:"tag is-info"},[t._v("\n            "+t._s(t.rid+1)+" /  "+t._s(t.records.length)+"\n        ")])]):t._e(),s("div",{staticClass:"columns"},[s("div",{staticClass:"column is-3"},[s("h1",{staticClass:"title is-6"},[t._v("SHEET SUMMARY")]),s("div",{staticClass:"content"},[s("p",[t._v("\n                    Total Records: "+t._s(t.records.length)+" "),s("br"),t._v("\n                    Reviewed: "+t._s(t.total_reviewed)+"\n                ")])]),s("SelectDropfile",{attrs:{jobsheet_id:t.jobsheet_id},on:{selected:t.dropfileSelected}}),s("a",{staticClass:"button is-primary is-fullwidth",class:{"is-loading":t.isLoading},attrs:{disabled:t.total_reviewed!=t.records.length&&!t.bypass||!t.drop_id},on:{click:t.handleAccept}},[t._v("\n                ACCEPT JOB SHEET\n            ")]),s("label",[s("input",{directives:[{name:"model",rawName:"v-model",value:t.bypass,expression:"bypass"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(t.bypass)?t._i(t.bypass,null)>-1:t.bypass},on:{change:function(e){var s=t.bypass,a=e.target,i=!!a.checked;if(Array.isArray(s)){var n=null,o=t._i(s,n);a.checked?o<0&&(t.bypass=s.concat([n])):o>-1&&(t.bypass=s.slice(0,o).concat(s.slice(o+1)))}else t.bypass=i}}}),t._v(" I have review and verified all records are correct")]),s("div",{staticStyle:{"margin-top":"20px"}},[s("button",{staticClass:"button is-small is-fullwidth",on:{click:function(e){return t.$emit("reject")}}},[t._v("Re-upload Job Sheet")])])],1),s("div",{staticClass:"column"},[s("nav",{staticClass:"pagination is-small",attrs:{role:"navigation","aria-label":"pagination"}},[s("ul",{staticClass:"pagination-list"},[s("li",[s("a",{staticClass:"pagination-link",on:{click:t.prevRecord}},[t._v("«")])]),t._l(t.records,(function(e,a){return s("li",{key:a},[s("a",{staticClass:"pagination-link",class:{"is-current":a==t.rid,"has-background-warning":!t.checkHash(e),"has-background-success":t.isVerified[a]},on:{click:function(e){t.rid=a}}},[t._v(t._s(a+1))])])})),s("li",[s("a",{staticClass:"pagination-link",on:{click:t.nextRecord}},[t._v("»")])])],2)]),s("hr"),s("div",{staticClass:"level"},[s("div",{staticClass:"level-start"},[s("div",{staticClass:"level-item"},[s("h2",{staticClass:"title is-6"},[t._v("RECORD #"+t._s(t.rid+1))])])]),s("div",{staticClass:"level-end"},[s("div",{staticClass:"level-item"},[s("div",{staticClass:"field"},[s("div",{staticClass:"control"},[s("label",{staticClass:"button is-rounded",class:{"is-success":t.isVerified[t.rid]}},[s("input",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"},{name:"model",rawName:"v-model",value:t.isVerified[t.rid],expression:"isVerified[rid]"}],attrs:{type:"checkbox"},domProps:{checked:Array.isArray(t.isVerified[t.rid])?t._i(t.isVerified[t.rid],null)>-1:t.isVerified[t.rid]},on:{change:function(e){var s=t.isVerified[t.rid],a=e.target,i=!!a.checked;if(Array.isArray(s)){var n=null,o=t._i(s,n);a.checked?o<0&&t.$set(t.isVerified,t.rid,s.concat([n])):o>-1&&t.$set(t.isVerified,t.rid,s.slice(0,o).concat(s.slice(o+1)))}else t.$set(t.isVerified,t.rid,i)}}}),s("span",{staticClass:"icon is-small"},[t.isVerified[t.rid]?s("svg",{staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",width:"18",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"}})]):t._e()]),s("span",[t._v("Record Verified ")])])])])])])]),s("div",{staticClass:"card"},[s("table",{staticClass:"table is-fullwidth"},[s("tbody",t._l(t.displayedRecord,(function(e,a){return s("tr",{key:a,class:t.invalidRow(a,e)},[s("th",{attrs:{width:"120"}},[t._v(t._s(a))]),s("td",[t._v(t._s(e))])])})),0)])])])])])},i=[]},"545b":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card-content"},[s("div",[s("div",{staticClass:"level",class:{"is-quickview":t.task}},[t._m(0),s("div",[s("div",{staticClass:"field is-grouped"},[s("div",{staticClass:"control"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.everyone,expression:"everyone"}],staticClass:"switch is-rounded  is-rtl",attrs:{id:"switchRoundedDefault2",type:"checkbox",name:"switchRoundedDefault2"},domProps:{checked:Array.isArray(t.everyone)?t._i(t.everyone,null)>-1:t.everyone},on:{change:function(e){var s=t.everyone,a=e.target,i=!!a.checked;if(Array.isArray(s)){var n=null,o=t._i(s,n);a.checked?o<0&&(t.everyone=s.concat([n])):o>-1&&(t.everyone=s.slice(0,o).concat(s.slice(o+1)))}else t.everyone=i}}}),s("label",{staticClass:"is-size-6",attrs:{for:"switchRoundedDefault2"}},[t._v("Everyone task")])]),s("div",{staticClass:"control"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.assignment_mode,expression:"assignment_mode"}],staticClass:"switch is-rounded  is-rtl",attrs:{id:"switchRoundedDefault",type:"checkbox",name:"switchRoundedDefault"},domProps:{checked:Array.isArray(t.assignment_mode)?t._i(t.assignment_mode,null)>-1:t.assignment_mode},on:{change:function(e){var s=t.assignment_mode,a=e.target,i=!!a.checked;if(Array.isArray(s)){var n=null,o=t._i(s,n);a.checked?o<0&&(t.assignment_mode=s.concat([n])):o>-1&&(t.assignment_mode=s.slice(0,o).concat(s.slice(o+1)))}else t.assignment_mode=i}}}),s("label",{staticClass:"is-size-6",attrs:{for:"switchRoundedDefault"}},[t._v("Assign task")])])])])]),s("div",{staticClass:"columns",class:{"is-quickview":t.task}},[s("div",{staticClass:"column"},[t.filteredTasks.length>0?s("div",{staticClass:"card"},[s("table-tasks",{attrs:{defaultFilterType:t.defaultFilterType,defaultFilter:t.defaultFilter,tasks:t.filteredTasks,isSelected:t.isSelected,processors:t.processorsMapping,task:t.task},on:{taskSelected:t.viewTask}})],1):s("div",{staticClass:"columns is-centered"},[s("div",{staticClass:"column is-6 has-text-centered has-text-grey-lighter content ",staticStyle:{padding:"40px"}},[s("p",{staticClass:"animated zoomIn faster"},[s("svg",{staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 503.635 503.635",width:"160"}},[s("path",{attrs:{d:"M503.635 6.284l-150.846-.635-70.939 150.108c-9.755-1.774-19.744-2.858-29.994-2.858-10.273 0-20.286 1.087-30.062 2.868L150.837 5.649 0 6.284l107.797 228.103c-16.284 26.469-25.855 57.615-25.855 91.06 0 95.135 76.221 172.538 169.913 172.538s169.914-77.404 169.914-172.538c0-33.478-9.59-64.654-25.904-91.139L503.635 6.284zM365.231 25.399l107.385.442L382.873 215.7c-20.981-25.786-49.202-45.257-81.453-55.267l63.811-135.034zm-334.212.442l107.375-.442 63.825 135.052c-32.249 10.025-60.464 29.513-81.436 55.311L31.019 25.841zm371.058 299.606c0 84.279-67.385 152.846-150.221 152.846s-150.221-68.567-150.221-152.846c0-84.288 67.385-152.856 150.221-152.856s150.221 68.568 150.221 152.856z"}}),s("path",{attrs:{d:"M359.904 294.659l-72.519-13.827-35.529-66.173-35.519 66.173-72.529 13.827 50.663 54.49-9.5 74.587 66.885-32.24 66.885 32.24-9.5-74.587 50.663-54.49zM251.856 369.63l-42.769 20.625 6.077-47.76-32.683-35.164 46.76-8.923 22.615-42.115 22.625 42.115 46.75 8.923-32.683 35.164 6.077 47.76-42.769-20.625z"}})])]),s("p",{staticClass:"subtitle has-text-grey-light is-3 animated fadeInUp faster",staticStyle:{"animation-delay":"0.2s"}},[t._v("No pending task for you :)")])])])]),s("quickview-task",{attrs:{task:t.task},on:{close:function(e){t.task=!1},blocked:t.loadData}})],1)]),t.assignment_mode?s("div",{staticStyle:{height:"150px"}},[s("task-assignment",{attrs:{processors:t.processors,isLoading:t.isLoading,total_selected:t.total_selected,selectAll:t.selectAll,clearSelection:t.clearSelection},on:{assign:t.handleAssign,close:function(e){t.assignment_mode=!1}}})],1):t._e()])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("h1",{staticClass:"title is-5 has-text-grey"},[t._v("Recent Tasks")])])}]},"548a":function(t,e,s){"use strict";s.r(e);var a=s("dbc6"),i=s("3b93");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);s("59be");var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"54e1":function(t,e,s){"use strict";s.r(e);var a=s("aab4"),i=s("82d3");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);s("8a62");var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},5522:function(t,e,s){"use strict";s.r(e);var a=s("03f0"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"55d3":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"section"},[1==t.step?s("div",{staticClass:"columns"},[s("div",{staticClass:"column is-5 is-offset-3"},[s("div",{staticClass:"card"},[t._m(0),s("div",{staticClass:"card-content"},[s("label",{staticClass:"label"},[t._v("Job sheet")]),s("div",{staticClass:"file has-name is-boxed"},[s("label",{staticClass:"file-label"},[s("input",{ref:"jobsheet",staticClass:"file-input",attrs:{type:"file",name:"jobsheet"},on:{change:t.handleFileChange}}),t._m(1),s("span",{staticClass:"file-name"},[t._v("\n                                "+t._s(t.filename)+"\n                            ")])])])]),s("div",{staticClass:"card-footer"},[s("a",{staticClass:"card-footer-item button is-radiusless is-primary",on:{click:t.processJobSheet}},[t._v("\n                        NEXT\n                    ")])])])])]):t._e(),2==t.step?s("div",{staticClass:"columns"},[s("div",{staticClass:"column is-12"},[s("div",{staticClass:"card"},[t._m(2),s("div",{staticClass:"card-content"},[s("sheet-review",{attrs:{jobsheet_id:t.jobsheet_id,records:t.records},on:{reject:function(e){t.step=1},accept:t.acceptJobSheet}})],1)])])]):t._e()])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card-header"},[s("div",{staticClass:"card-header-title"},[s("h1",{staticClass:"title is-6"},[t._v("New Job")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("span",{staticClass:"file-cta"},[s("span",{staticClass:"file-icon"},[s("i",{staticClass:"fas fa-upload"})]),s("span",{staticClass:"file-label"},[t._v("\n                                Select Job Sheet\n                            ")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card-header"},[s("div",{staticClass:"card-header-title"},[t._v("\n                        REVIEW RECORDS\n                    ")])])}]},5661:function(t,e,s){"use strict";s.r(e);var a=s("9b92"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},5693:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{attrs:{id:"om-app"}},[t.isOffline&&!t.requireLogin?s("div",{staticClass:"navbar is-fixed-top has-background-danger animated slideInDown faster",staticStyle:{"z-index":"9999"}},[s("div",{staticClass:"navbar-start"},[s("div",{staticClass:"navbar-item has-text-white"},[1==t.$store.state.isOffline?s("span",[t._v("Offline. You have no internet connection "),s("a",{staticClass:"button is-small is-white is-outlined",staticStyle:{"margin-left":"10px"},on:{click:t.refresh}},[t._v("Refresh")])]):s("span",[t._v(t._s(t.$store.state.isOffline))])])]),s("div",{staticClass:"navbar-end"},[s("div",{staticClass:"navbar-item"},[s("a",{staticClass:"delete is-pulled-right",on:{click:function(e){return t.$store.dispatch("dismissError")}}})])])]):t._e(),t.requireLogin?s("login"):s("div",[s("div",{staticClass:"modal",class:{"is-active":t.isLoading}},[s("div",{staticClass:"modal-background has-background-white",staticStyle:{opacity:"0.9"}}),s("div",{staticClass:"modal-content is-centered has-text-centered"},[s("span",{staticClass:"title button is-loading is-text"}),s("br"),s("span",{staticClass:"has-text-grey-light",staticStyle:{position:"relative",top:"-20px"}},[t._v("Loading...")])])]),t.ready?s("layout",{on:{logout:t.handleLogout}},[s("router-view",{attrs:{store:t.$store}})],1):t._e()],1)],1)},i=[]},"56da":function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");s("d81d"),s("d3b7"),s("3ca3"),s("ddb0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("c1df")),r=a(s("36dc")),l={components:{TableJobsheets:r["default"]},computed:{project_id:function(){return this.$route.params.project_id},project:function(){return this.$store.state.om.projects[this.project_id]},jobsheets:function(){var t=this;return(this.project.jobsheets||[]).map((function(e){return Object.assign({},e,{TotalAssigned:(t.taskAssigned[e.PK]||[]).length})}))},taskAssigned:function(){return n.pipe(n.values,n.groupBy((function(t){return t.PK})))(this.$store.state.om.mytasks||[])}},methods:{loadData:function(){var t=this;return Promise.all([this.$store.dispatch("om.project.load",this.project_id),this.$store.dispatch("om.processors.mytasks",this.project_id)]).then((function(){t.$store.dispatch("isLoaded")}))}},mounted:function(){this.loadData()},watch:{project_id:function(t){this.loadData()}},filters:{shortKey:function(t){return n.take(12,t)},formatDate:function(t){return(0,o["default"])(t).format("DD MMM YYYY")},formatTime:function(t){return(0,o["default"])(t).format("hh:mm A")}}};e["default"]=l,t.exports=e.default,t.exports.default=e.default},"57f0":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card-content"},[t.project?s("div",[s("div",{staticClass:"level"},[s("div",{staticClass:"level-left"},[s("div",{staticClass:"level-item"},[t.isEditTitle?s("div",{staticClass:"field has-addons"},[s("div",{staticClass:"control"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.newTitle,expression:"newTitle"}],staticClass:"input",staticStyle:{"min-width":"30vw"},attrs:{type:"text"},domProps:{value:t.newTitle},on:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.saveNewTitle(e)},input:function(e){e.target.composing||(t.newTitle=e.target.value)}}})]),s("div",{staticClass:"control"},[s("a",{staticClass:"button",class:{"is-loading":t.isLoading},on:{click:t.saveNewTitle}},[t._v("Save")]),s("a",{staticClass:"button is-text",class:{"is-loading":t.isLoading},on:{click:t.cancelEdit}},[t._v("Cancel")])])]):s("h1",{staticClass:"title is-5 has-text-grey",class:{"is-clickable tooltip":t.isEditable},attrs:{"data-tooltip":"Double click to edit"},on:{dblclick:t.editTitle}},[t._v("\n                "+t._s(t.project.name)+"\n                "),t.isEditable?s("span",{staticClass:"is-parent-hovered"},[s("svg",{staticStyle:{width:"16px"},attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M22 7.24a1 1 0 0 0-.29-.71l-4.24-4.24a1 1 0 0 0-.71-.29 1 1 0 0 0-.71.29l-2.83 2.83L2.29 16.05a1 1 0 0 0-.29.71V21a1 1 0 0 0 1 1h4.24a1 1 0 0 0 .76-.29l10.87-10.93L21.71 8a1.19 1.19 0 0 0 .22-.33 1 1 0 0 0 0-.24.7.7 0 0 0 0-.14zM6.83 20H4v-2.83l9.93-9.93 2.83 2.83zM18.17 8.66l-2.83-2.83 1.42-1.41 2.82 2.82z"}})])]):t._e()])])]),s("div",{staticClass:"level-right"},[s("router-link",{staticClass:"button is-rounded is-small is-primary",attrs:{to:"/jobs/projects/"+t.project_id+"/upload"}},[t._v("Upload Jobsheet")])],1)]),s("div",{staticClass:"columns"},[s("div",{staticClass:"column"},[t.jobsheets&&t.jobsheets.length>0?s("div",{staticClass:"card"},[s("table-jobsheets",{attrs:{jobsheets:t.jobsheets}})],1):s("div",{staticClass:"columns is-centered"},[s("div",{staticClass:"column is-6 has-text-centered has-text-grey-lighter content",staticStyle:{padding:"40px"}},[s("p",[s("svg",{staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 492.308 492.308",width:"160"}},[s("path",{attrs:{d:"M316.038 254.298l-14.442-13.385-55.437 59.799-55.438-59.799-14.442 13.385 56.454 60.894-56.454 60.895 14.442 13.384 55.438-59.798 55.437 59.798 14.442-13.384-56.454-60.895z"}}),s("path",{attrs:{d:"M242.173 63.731L181.452 0H0v492.308h492.308V63.731H242.173zm230.442 408.884H19.692v-312.23h452.923v312.23zm0-331.923H19.692v-121H173.01l60.721 63.731h238.885v57.269z"}})])]),s("p",{staticClass:"subtitle has-text-grey-light is-3"},[t._v("No jobsheet uploaded yet in this project")]),s("p",[s("router-link",{staticClass:"button is-rounded is-medium is-primary",attrs:{to:"/jobs/projects/"+t.project_id+"/upload"}},[t._v("Upload Jobsheet")])],1),t.isEditable?s("p",[t._v("\n                        or "),s("br"),s("a",{staticClass:"button is-text is-small",class:{"is-loading":t.isLoading},on:{click:t.deleteProject}},[t._v("DELETE THIS PROJECT")])]):t._e()])])])])]):t._e()])},i=[]},"582f":function(t,e,s){"use strict";s.r(e);var a=s("612d"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"58fa":function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("c1df")),r={props:["jobsheet_id"],data:function(){return{showModal:!1,label:"Select dropfile"}},computed:{dropfiles:function(){var t=n.filter((function(t){return"OPEN"==t.Status}));return t(this.$store.state.om.dropfile.files)}},methods:{selectDropfile:function(t){this.$emit("selected",t?t.PK:this.jobsheet_id),this.label=t?n.take(12,t.PK):"No associated dropfile",this.showModal=!1}},filters:{shortKey:function(t){return n.take(12,t)},fromNow:function(t){return(0,o["default"])(t).fromNow()}}};e["default"]=r,t.exports=e.default,t.exports.default=e.default},"59be":function(t,e,s){"use strict";var a=s("93ef"),i=s.n(a);i.a},"5a8b":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"container"},[s("div",{staticClass:"columns"},[s("div",{staticClass:"column is-12"},[s("h1",{staticClass:"title is-6"},[t._v("Instructions")]),s("article",{staticClass:"message is-link"},[s("div",{staticClass:"message-body"},[s("div",{staticClass:"columns"},[s("div",{staticClass:"column content"},[s("ul",[s("li",[t._v("Fill up the submission form. Download here "),s("a",{on:{click:t.downloadTemplate}},[t._v(t._s(t.tpl.file.details.filename))])]),t._m(0),s("li",[t._v("Include "),s("em",[t._v("SIMI's")]),t._v(" email address "),s("a",{staticClass:"tag tooltip has-text-weight-medium",attrs:{"data-tooltip":"Copy"},on:{click:function(e){return t.copyText("<EMAIL>",e)}}},[t._v("<EMAIL>")])]),s("li",[t._v("Wait for acknowledgment/rejection email")])])]),t._m(1)])])])])]),s("div",{staticClass:"tabs is-boxed"},[s("ul",[s("li",{class:{"is-active":"my"==t.tab},on:{click:function(e){t.tab="my"}}},[s("a",[t._v("My Request")])]),s("li",{class:{"is-active":"my-region"==t.tab},on:{click:function(e){t.tab="my-region"}}},[t._m(2)])])]),s("div",{staticClass:"columns"},["my"==t.tab?s("div",{staticClass:"column is-12"},[t.nofile?s("article",{staticClass:"message"},[s("div",{staticClass:"message-body"},[t._v("\n                    You have no order request yet\n\n                    "),s("p",{on:{click:t.hack}},[t._v("...")])])]):t._e(),t.nofile?t._e():s("div",[s("div",{staticClass:"level"},[s("div",{staticClass:"level-start"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.filterKeyword,expression:"filterKeyword"}],staticClass:"input is-rounded",attrs:{type:"text",placeholder:"filter"},domProps:{value:t.filterKeyword},on:{input:function(e){e.target.composing||(t.filterKeyword=e.target.value)}}})])])]),t.nofile?t._e():s("table",{staticClass:"table is-fullwidth is-hoverable"},[s("thead",[s("tr",[s("th",[t._v("#")]),s("th",[t._v("Drop ID")]),s("th",[t._v("Details")]),s("th",[t._v("Region")]),s("th",[t._v("Request Type")]),s("th",[t._v("Ref #")]),s("th",{staticClass:"is-clickable",on:{click:function(e){return t.sortTable("createdAt")}}},["createdAt"==t.sortKey?s("span",{staticClass:"icon is-pulled-right",domProps:{innerHTML:t._s(t.sortIcon)}}):t._e(),s("abbr",{attrs:{title:"Sort by this"}},[t._v("Requested At")])]),s("th",{staticClass:"is-clickable",on:{click:function(e){return t.sortTable("Status")}}},["Status"==t.sortKey?s("span",{staticClass:"icon is-pulled-right",domProps:{innerHTML:t._s(t.sortIcon)}}):t._e(),s("abbr",{attrs:{title:"Sort by this"}},[t._v("Status")])]),s("th")])]),s("tbody",t._l(t.filteredFiles,(function(e,a){return s("tr",{key:a},[s("td",{staticClass:"has-text-grey-lighter",staticStyle:{"padding-right":"0"},attrs:{width:"5"}},[t._v("\n                            "+t._s(a+1)+"\n                        ")]),s("td",[s("a",{staticClass:"tag tooltip",attrs:{"data-tooltip":"Copy"},on:{click:function(s){return t.copyData(e,s)}}},[t._v(t._s(t._f("shortKey")(e.drop_id)))])]),s("td",[s("div",[s("a",{staticStyle:{display:"block",width:"400px"},on:{click:function(s){return t.downloadFile(e)}}},[t._v(t._s(e.details.subject)+" ")]),s("span",{staticClass:"heading"},[t._v("Subject")])]),e.details["Short Description on Request"]?s("div",[s("span",[t._v(t._s(e.details["Short Description on Request"]))]),s("span",{staticClass:"heading"},[t._v("Description")])]):t._e()]),s("td",[t._v("\n                            "+t._s(e.details.Region)+"\n                        ")]),s("td",[t._v("\n                            "+t._s(e.details["Request Type"])+"\n                        ")]),s("td",[e.details["Sales Ref Number"]?s("div",{staticClass:"headings"},[s("span",[t._v(t._s(e.details["Sales Ref Number"]))]),s("span",{staticClass:"heading"},[t._v("Sales Ref No.")])]):t._e(),e.details["BCS Req ID (if applicable)"]?s("div",{staticClass:"headings"},[s("span",[t._v(t._s(e.details["BCS Req ID (if applicable)"]))]),s("span",{staticClass:"heading"},[t._v("BCS Request No.")])]):t._e()]),s("td",[s("span",{staticClass:"tooltip",attrs:{"data-tooltip":t._f("formatDate")(e.createdAt)}},[t._v(t._s(t._f("fromNow")(e.createdAt)))]),s("span",{staticClass:"heading"},[t._v("By "+t._s(e.CreatedBy.name))])]),s("td",[s("drop-status",{attrs:{file:e}})],1),s("td",[s("router-link",{attrs:{to:"/dropfiles/"+e.PK}},[t._v("View")])],1)])})),0)]),t.lastKey?s("div",[s("a",{staticClass:"button is-fullwidth",class:{"is-loading":t.isLoading},on:{click:t.loadDropfiles}},[t._v("Load More")])]):t._e()]):t._e(),"my-region"==t.tab?s("div",{staticClass:"column is-12"},[t.nofile?s("article",{staticClass:"message"},[s("div",{staticClass:"message-body"},[t._v("\n                    You have no order request yet\n\n                    "),s("p",{on:{click:t.hack}},[t._v("...")])])]):t._e(),t.nofile?t._e():s("div",[s("div",{staticClass:"level"},[s("div",{staticClass:"level-start"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.filterKeyword,expression:"filterKeyword"}],staticClass:"input is-rounded",attrs:{type:"text",placeholder:"filter"},domProps:{value:t.filterKeyword},on:{input:function(e){e.target.composing||(t.filterKeyword=e.target.value)}}})])])]),t.nofile?t._e():s("table",{staticClass:"table is-fullwidth is-hoverable"},[s("thead",[s("tr",[s("th",[t._v("#")]),s("th",[t._v("Drop ID")]),s("th",[t._v("Details")]),s("th",[t._v("Region")]),s("th",[t._v("Request Type")]),s("th",[t._v("Ref #")]),s("th",{staticClass:"is-clickable",on:{click:function(e){return t.sortTable("createdAt")}}},["createdAt"==t.sortKey?s("span",{staticClass:"icon is-pulled-right",domProps:{innerHTML:t._s(t.sortIcon)}}):t._e(),s("abbr",{attrs:{title:"Sort by this"}},[t._v("Requested At")])]),s("th",{staticClass:"is-clickable",on:{click:function(e){return t.sortTable("Status")}}},["Status"==t.sortKey?s("span",{staticClass:"icon is-pulled-right",domProps:{innerHTML:t._s(t.sortIcon)}}):t._e(),s("abbr",{attrs:{title:"Sort by this"}},[t._v("Status")])]),s("th")])]),s("tbody",t._l(t.filteredFilesTeam,(function(e,a){return s("tr",{key:a},[s("td",{staticClass:"has-text-grey-lighter",staticStyle:{"padding-right":"0"},attrs:{width:"5"}},[t._v("\n                            "+t._s(a+1)+"\n                        ")]),s("td",[s("a",{staticClass:"tag tooltip",attrs:{"data-tooltip":"Copy"},on:{click:function(s){return t.copyData(e,s)}}},[t._v(t._s(t._f("shortKey")(e.drop_id)))])]),s("td",[s("div",[s("a",{staticStyle:{display:"block",width:"400px"},on:{click:function(s){return t.downloadFile(e)}}},[t._v(t._s(e.details.subject)+" ")]),s("span",{staticClass:"heading"},[t._v("Subject")])]),e.details["Short Description on Request"]?s("div",[s("span",[t._v(t._s(e.details["Short Description on Request"]))]),s("span",{staticClass:"heading"},[t._v("Description")])]):t._e()]),s("td",[t._v("\n                            "+t._s(e.details.Region)+"\n                        ")]),s("td",[t._v("\n                            "+t._s(e.details["Request Type"])+"\n                        ")]),s("td",[e.details["Sales Ref Number"]?s("div",{staticClass:"headings"},[s("span",[t._v(t._s(e.details["Sales Ref Number"]))]),s("span",{staticClass:"heading"},[t._v("Sales Ref No.")])]):t._e(),e.details["BCS Req ID (if applicable)"]?s("div",{staticClass:"headings"},[s("span",[t._v(t._s(e.details["BCS Req ID (if applicable)"]))]),s("span",{staticClass:"heading"},[t._v("BCS Request No.")])]):t._e()]),s("td",[s("span",{staticClass:"tooltip",attrs:{"data-tooltip":t._f("formatDate")(e.createdAt)}},[t._v(t._s(t._f("fromNow")(e.createdAt)))]),s("span",{staticClass:"heading"},[t._v("By "+t._s(e.CreatedBy.name))])]),s("td",[s("drop-status",{attrs:{file:e}})],1),s("td",[s("router-link",{attrs:{to:"/dropfiles/"+e.PK}},[t._v("View")])],1)])})),0)]),t.lastKey?s("div",[s("a",{staticClass:"button is-fullwidth",class:{"is-loading":t.isLoading},on:{click:t.loadDropfiles}},[t._v("Load More")])]):t._e()]):t._e()])])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("li",[t._v("Attached the form in your communication with "),s("strong",[t._v("ODM Team")]),t._v(". Please ensure the filename starts with "),s("em",[t._v("om_submission")])])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"column is-narrow"},[a("figure",{staticClass:"image"},[a("img",{attrs:{src:s("87f9"),alt:"Outlook snapshot"}})])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("a",[t._v("My Team Request "),s("span",{staticClass:"tag is-warning",staticStyle:{"margin-left":"1rem"}},[t._v("New !")])])}]},"5b0c":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"field has-addons "},[t._m(0),s("div",{staticClass:"control"},[s("div",{staticClass:"select is-small"},[s("select",{directives:[{name:"model",rawName:"v-model",value:t.sortKey,expression:"sortKey"}],on:{change:function(e){var s=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.sortKey=e.target.multiple?s:s[0]}}},t._l(t.sortfields,(function(e,a){return s("option",{key:a,domProps:{value:a}},[t._v(t._s(e))])})),0)])]),s("div",{staticClass:"control"},["ascend"==t.sortBy?s("a",{staticClass:"button is-small",on:{click:t.sort}},[s("span",{staticClass:"icon"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"19",height:"19",viewBox:"0 0 24 24",fill:"none",stroke:"#000000","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}},[s("path",{attrs:{d:"M17 11l-5-5-5 5M17 18l-5-5-5 5"}})])])]):t._e(),"descend"==t.sortBy?s("a",{staticClass:"button is-small",on:{click:t.sort}},[s("span",{staticClass:"icon"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"19",height:"19",viewBox:"0 0 24 24",fill:"none",stroke:"#000000","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}},[s("path",{attrs:{d:"M7 13l5 5 5-5M7 6l5 5 5-5"}})])])]):t._e()])])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"control"},[s("span",{staticClass:"button is-static is-small"},[t._v("Sort ")])])}]},"5c5d":function(t,e,s){"use strict";s.r(e);var a=s("d766"),i=s("35ca");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"5c85":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card-content"},[t.jobsheet&&t.project?s("div",[s("div",{staticClass:"level",class:{"is-quickview":t.task}},[s("div",[s("router-link",{staticClass:"heading",attrs:{to:"/jobs/projects/"+t.project.PK}},[t._v(" « "+t._s(t.project.name)+" Project")]),s("h1",{staticClass:"title is-5 has-text-grey"},[t._v(t._s(t._f("shortKey")(t.jobsheet.PK)))])],1),s("div",[s("div",{staticClass:"field"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.assignment_mode,expression:"assignment_mode"}],staticClass:"switch is-rounded  is-rtl",attrs:{id:"switchRoundedDefault",type:"checkbox",name:"switchRoundedDefault"},domProps:{checked:Array.isArray(t.assignment_mode)?t._i(t.assignment_mode,null)>-1:t.assignment_mode},on:{change:function(e){var s=t.assignment_mode,a=e.target,i=!!a.checked;if(Array.isArray(s)){var n=null,o=t._i(s,n);a.checked?o<0&&(t.assignment_mode=s.concat([n])):o>-1&&(t.assignment_mode=s.slice(0,o).concat(s.slice(o+1)))}else t.assignment_mode=i}}}),s("label",{staticClass:"is-size-6",attrs:{for:"switchRoundedDefault"}},[t._v("Assign task")])])])]),s("div",{staticClass:"tabs is-boxed"},[s("ul",[s("li",{class:{"is-active":"tasks"==t.tab}},[s("a",{on:{click:function(e){t.tab="tasks"}}},[s("span",[t._v("Tasks")])])]),s("li",{class:{"is-active":"distributions"==t.tab}},[s("a",{on:{click:function(e){t.tab="distributions"}}},[s("span",[t._v("Distributions")])])]),s("li",{class:{"is-active":"addTask"==t.tab}},[s("a",{staticClass:"has-text-link",on:{click:function(e){t.tab="addTask"}}},[s("span",{staticClass:"icon"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"100%",height:"100%",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",fill:"none","stroke-linecap":"round","stroke-linejoin":"round"}},[s("path",{attrs:{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}}),s("rect",{attrs:{x:"4",y:"4",width:"16",height:"16",rx:"2"}}),s("line",{attrs:{x1:"9",y1:"12",x2:"15",y2:"12"}}),s("line",{attrs:{x1:"12",y1:"9",x2:"12",y2:"15"}})])]),s("span",{staticStyle:{"padding-right":"1rem"}},[t._v("Add Task")]),s("span",{staticClass:"tag is-warning"},[t._v("ALPHA")])])]),t.canDeleteJobsheet?s("li",{class:{"is-active":"delete"==t.tab}},[s("a",{staticClass:"has-text-danger",on:{click:function(e){t.tab="delete"}}},[s("span",{staticClass:"icon is-small"},[s("i",{staticClass:"fas fa-image",attrs:{"aria-hidden":"true"}},[s("svg",{staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zm2.46-7.12l1.41-1.41L12 12.59l2.12-2.12 1.41 1.41L13.41 14l2.12 2.12-1.41 1.41L12 15.41l-2.12 2.12-1.41-1.41L10.59 14l-2.13-2.12zM15.5 4l-1-1h-5l-1 1H5v2h14V4z"}})])])]),s("span",[t._v("Delete Jobsheet")])])]):t._e()])]),s("div",{staticClass:"columns",class:{"is-quickview":t.task}},["delete"!=t.tab?s("div",{staticClass:"column"},["distributions"==t.tab?s("div",[s("div",{staticClass:"card",staticStyle:{"margin-bottom":"50px"}},[s("table",{staticClass:"table is-fullwidth is-hoverable"},[t._m(0),s("tbody",[t._l(t.distributions,(function(e,a){return s("tr",{key:a,class:{"is-selected":t.selectedDistribution==a},on:{click:function(e){t.selectedDistribution=a}}},[s("td",[t._v(t._s(a))]),s("td",[t._v(t._s(t.getProcessorName(a)))]),s("td",[t._v(t._s((e||[]).length))])])})),t.noDistributions?s("tr",[s("td",{attrs:{colspan:"3"}},[t._v("\n                                        NO DISTRIBUTION YET\n                                    ")])]):t._e()],2)])])]):t._e(),"addTask"==t.tab?s("div",[s("p",{staticClass:"title"},[t._v("Select Request Type")]),s("div",{staticClass:"tabs is-toggle is-toggle-rounded"},[s("ul",t._l(t.formTypes,(function(e){return s("li",{key:e,class:{"is-active":t.requestType==e},on:{click:function(s){return t.changeAddTaskForm(e)}}},[s("a",[t._v(t._s(e))])])})),0)]),t._l(t.currentRecordTemplate,(function(e){return[s("div",{key:e,staticClass:"field"},[s("label",{staticClass:"label"},[t._v(t._s(e))]),s("div",{staticClass:"control"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.form[e],expression:"form[template]"}],staticClass:"input",attrs:{type:"text"},domProps:{value:t.form[e]},on:{input:function(s){s.target.composing||t.$set(t.form,e,s.target.value)}}})])])]}))],2):t._e(),t.filteredTasks.length>0?s("div",{staticClass:"card"},[s("table-tasks",{attrs:{tasks:t.filteredTasks,isSelected:t.isSelected,processors:t.processorsMapping},on:{taskSelected:t.viewTask}})],1):t._e()]):s("div",{staticClass:"column"},[s("div",{},[t._m(1),s("div",{staticClass:"buttons"},[s("a",{staticClass:"button is-danger",class:{"is-loading":t.isLoading},on:{click:t.handleDelete}},[t._v("Confirm delete")]),s("a",{staticClass:"button is-text",on:{click:function(e){t.tab="tasks"}}},[t._v("Cancel")])])])]),s("quickview-task",{attrs:{task:t.task},on:{close:function(e){t.task=!1},blocked:t.loadData}})],1)]):t._e(),t.assignment_mode?s("div",{staticStyle:{height:"150px"}},[s("task-assignment",{attrs:{processors:t.processors,isLoading:t.isLoading,total_selected:t.total_selected,selectAll:t.selectAll,clearSelection:t.clearSelection},on:{assign:t.handleAssign,close:function(e){t.assignment_mode=!1}}})],1):t._e()])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("thead",[s("tr",[s("th",[t._v("Staff ID")]),s("th",[t._v("Name")]),s("th",[t._v("Total tasks")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"notification is-warning"},[s("h2",{staticClass:"title is-4"},[t._v("Delete Jobsheet Confirmation")]),s("p",[t._v("Are you sure want to delete the jobsheet? This action will remove all records related to this jobsheet.")])])}]},"5d47":function(t,e,s){"use strict";s.r(e);var a=s("545b"),i=s("8b61");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"5d68":function(t,e,s){"use strict";s.r(e);var a=s("ddca"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"5d84":function(t,e,s){"use strict";var a=s("dbce");s("99af"),s("4160"),s("c975"),s("b0c0"),s("159b"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("b17e")),n={data:function(){return{filterKeyword:"",workload:{}}},computed:{processors:function(){return this.$store.state.om.processors},sortedWorkload:function(){var t=this;return i.pipe(i.map((function(e){var s=(t.workload[e.staff_id]||{}).BLOCKED||0,a=(t.workload[e.staff_id]||{}).ASSIGNED||0;return{name:e.name,staff_id:e.staff_id,blocked:s,assigned:a,total:s+a}})),i.sort(i.ascend(i.prop("assigned"))),i.filter((function(e){return JSON.stringify(e).indexOf(t.filterKeyword)>-1})))(this.processors)}},mounted:function(){this.loadData()},methods:{getTooltip:function(t){return"Assigned: ".concat(t.assigned," | Blocked: ").concat(t.blocked)},getWorkload:function(t){return this.workload[t]||{}},loadData:function(){var t=this;return this.$store.dispatch("om.processors.list").then((function(){t.processors.forEach((function(e){t.$store.dispatch("om.processors.workload",e.staff_id).then((function(s){var a=i.pipe(i.groupBy(i.prop("Status")),i.map((function(t){return t.length})))(s);t.$set(t.workload,e.staff_id,a)}))}))}))}}};e["default"]=n,t.exports=e.default,t.exports.default=e.default},"5d92":function(t,e,s){"use strict";s.r(e);var a=s("4fa2"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"5fb3":function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("100d")),r=a(s("de99")),l=a(s("548a"));s("77ed"),s("19d3");var c={components:{Login:l["default"],Layout:o["default"]},data:function(){return{ready:!1}},created:function(){var t=this;this.$store.dispatch("requiresAuth",!0),this.$store.dispatch("login.init"),this.$store.dispatch("om.init"),this.$store.dispatch("om.roles.currentUser",(this.$store.state.login.user||{}).staff_id).then((function(){t.loadLocalStorage(),t.ready=!0}))},watch:{currentRole:function(t){r["default"].set("om.current_role",t)}},computed:{currentRole:function(){return this.$store.state.om.currentUserActiveRole},isOffline:function(){return this.$store.state.isOffline},requireLogin:function(){return this.$store.getters.show_login},isLoading:function(){return this.$store.state.isLoading}},methods:{loadLocalStorage:function(){this.$store.dispatch("om.roles.switch",r["default"].get.item("om.current_role",n.head(this.$store.state.om.currentUserRoles)))},handleLogout:function(){this.logout()},logout:function(){this.$store.dispatch("login.logout")},refresh:function(){window.location.reload(!0)}}};e["default"]=c,t.exports=e.default,t.exports.default=e.default},"612d":function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var a={props:["task","selectedTasks","getStaffName","moreInfo"],methods:{handleUnassigned:function(t){confirm("Unassigned this task?")&&alert("released")}}};e["default"]=a,t.exports=e.default,t.exports.default=e.default},6153:function(t,e,s){"use strict";var a=s("4ea4");Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("ba2a")),n=a(s("ba14")),o={components:{Navbar:i["default"],SidebarMenu:n["default"]}};e["default"]=o,t.exports=e.default,t.exports.default=e.default},6154:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("div",{staticClass:"section"},[s("div",{staticClass:"columns is-centered"},[s("div",{staticClass:"column is-2"},[s("div",{staticClass:"box has-text-centered"},[t._v("\n                    DOWNLOAD DATA "),s("export-excel",{attrs:{raw:t.records}})],1)])])])])},i=[]},6416:function(t,e,s){"use strict";s.r(e);var a=s("b85e"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},6452:function(t,e,s){"use strict";s.r(e);var a=s("3a46"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},6608:function(t,e,s){"use strict";s.r(e);var a=s("863e"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},6616:function(t,e,s){"use strict";s.r(e);var a=s("8d78"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"66fe":function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");s("4de4"),s("b0c0"),s("d3b7"),s("3ca3"),s("ddb0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("c1df")),r=a(s("f137")),l=a(s("350b")),c=a(s("26d3")),d=a(s("d459")),u={components:{TableTasks:r["default"],QuickviewTask:l["default"],TaskAssignment:d["default"]},data:function(){return{tab:"tasks",task:!1,assignment_mode:!1,selected_tasks:{},processor:!1,isLoading:!1,selectedDistribution:!1,requestType:"All",form:{}}},computed:{formTypes:function(){return n.keys(this.$store.getters.om_requestTypes)},currentRecordTemplate:function(){var t=this.$store.getters.om_taskTemplate(this.requestType);return t},noDistributions:function(){return 0==n.keys(this.distributions).length},project_id:function(){return this.$route.params.project_id},project:function(){return this.$store.state.om.projects[this.project_id]},jobsheet_id:function(){return this.$route.params.jobsheet_id},jobsheets:function(){return this.$store.state.om.jobs},jobsheet:function(){return this.jobsheets[this.jobsheet_id]},filteredTasks:function(){return this.selectedDistribution&&"distributions"==this.tab?this.distributions[this.selectedDistribution]||[]:"tasks"==this.tab?n.pipe(n.sortBy(n.prop("TaskID")))(this.jobsheet.tasks||[]):[]},total_selected:function(){return n.pipe(n.values,n.sum)(this.selected_tasks)},processors:function(){return this.$store.state.om.processors},processorsMapping:function(){return n.indexBy(n.prop("staff_id"),this.processors)},distributions:function(){return n.pipe(n.filter((function(t){return"UNASSIGNED"!=t.Status})),n.groupBy(n.prop("GS1")))(this.jobsheet.tasks)},canDeleteJobsheet:function(){return this.jobsheet.CreatedBy.staff_id==this.$store.state.login.user.staff_id||this.$store.getters.userScope("developer")}},methods:{changeAddTaskForm:function(t){this.requestType=t},handleDelete:function(){var t=this;this.isLoading=!0,this.$store.dispatch("om.jobsheet.delete",this.jobsheet_id).then((function(){t.isLoading=!1,alert("Jobsheet deleted!"),t.$router.push("/jobs/projects/".concat(t.project_id))}))},getProcessorName:function(t){return(this.processorsMapping[t]||{name:""}).name},handleAssign:function(t){var e=this;this.isLoading||(this.isLoading=!0,this.$store.dispatch("om.processor.assign",{processor:t,tasks:this.jobsheet.tasks.filter((function(t){return e.selected_tasks[t.TaskID]}))}).then((function(){e.isLoading=!1,e.loadData(),e.selected_tasks={},e.processor=""}))["catch"]((function(t){e.isLoading=!1})))},selectAll:function(){this.$set(this,"selected_tasks",n.pipe(n.indexBy(n.prop("TaskID")),n.map((function(){return!0})))(this.filteredTasks))},clearSelection:function(){this.selected_tasks={}},isSelected:function(t){return this.assignment_mode&&this.selected_tasks[t.TaskID]},loadData:function(){return Promise.all([this.$store.dispatch("om.processors.list"),this.$store.dispatch("om.project.load",this.project_id),this.$store.dispatch("om.jobsheet.load",this.jobsheet_id)])},canAssign:function(t){return(0,c["default"])(t).canTransTo("ASSIGNED")||(0,c["default"])(t).canTransTo("ASSIGNED_RERAISE")},viewTask:function(t){if(this.assignment_mode){if(!this.canAssign(t))return void alert("You cannot assign this task");this.$set(this.selected_tasks,t.TaskID,!this.selected_tasks[t.TaskID])}else this.task=t}},mounted:function(){var t=this;this.loadData().then((function(){t.$store.dispatch("isLoaded")}))},filters:{shortKey:function(t){return n.take(12,t)},formatDate:function(t){return(0,o["default"])(t).format("DD MMM YYYY")}}};e["default"]=u,t.exports=e.default,t.exports.default=e.default},"672a":function(t,e,s){"use strict";var a=s("dbce"),i=s("4ea4");s("d81d"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("c1df")),o=a(s("b17e")),r={mounted:function(){this.loadData()},methods:{loadData:function(){this.$store.dispatch("om.processors.mytasks")},getOpenTotal:function(t){var e=["ASSIGNED","BLOCKED","ASSIGNED_RERAISE"].map((function(e){return t[e]||[]}));return o.pipe(o.flatten,o.length)(e)},getTodayOrders:function(t){var e=["RERAISED","SUBMITTED"].map((function(e){return t[e]||[]})),s=function(t){return(0,n["default"])(t.updatedAt).isSame((0,n["default"])(),"day")};return o.pipe(o.flatten,o.filter(s),o.length)(e)}},computed:{mytasks:function(){return this.$store.state.om.mytasks||[]},stats:function(){if(!this.mytasks.length)return{};var t=o.pipe(o.groupBy(o.prop("Status")))(this.mytasks);return{open:this.getOpenTotal(t),todayOrder:this.getTodayOrders(t),blocked:(t.BLOCKED||[]).length}}}};e["default"]=r,t.exports=e.default,t.exports.default=e.default},"67c1":function(t,e,s){"use strict";s.r(e);var a=s("7bcf"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"682d":function(t,e,s){"use strict";var a=s("dbce");s("4160"),s("159b"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("b17e")),n={props:["monitor","selectedTask","loading"],data:function(){return{form:{order_no:"",service_id:"",qoute_no:"",billing_no:"","Sphere ID":"","RFS Date":"","RFT Date":"","OD Number":"","OD Link":"",logs:[]}}},mounted:function(){this.resetForm()},watch:{selectedTask:function(t){console.log("change task"),this.resetForm()}},methods:{resetForm:function(){var t=this;this.form={order_no:this.selectedTask.details["Order Number"]||"",service_id:"",qoute_no:"",billing_no:"","Sphere ID":this.selectedTask.details["Sphere ID"]||"","RFS Date":this.selectedTask.details["RFS Date"]||"","RFT Date":this.selectedTask.details["RFT Date"]||"","OD Number":this.selectedTask.details["OD Number"]||"","OD Link":this.selectedTask.details["OD Link"]||"",logs:[]},this.selectedTask.feedback&&(i.keys(this.form).forEach((function(e){t.form[e]=t.selectedTask.feedback[e]?t.selectedTask.feedback[e]:t.form[e]})),this.$set(this.form,"old_order_no",this.selectedTask.feedback.order_no),this.$set(this.form,"old_service_id",this.selectedTask.feedback.service_id),this.form.logs.push(i.mergeAll([this.selectedTask.feedback,{date:this.selectedTask.updatedAt}])))},handleComplete:function(){var t=this;this.$validator.validateAll().then((function(e){if(e){var s=["logs"];t.$emit("completeTask",i.omit(s,t.form))}}))}}};e["default"]=n,t.exports=e.default,t.exports.default=e.default},"684e":function(t,e,s){"use strict";s.r(e);var a=s("0a62"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"68d7":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card-content"},[t._m(0),s("div",{staticClass:"columns",class:{"is-quickview":t.task}},[t.selectedTask?t._e():s("div",{staticClass:"column"},[t.filteredTasks.length>0?s("div",{staticClass:"card"},[s("table-task",{attrs:{defaultType:"week",filteredTasks:t.filteredTasks,getProjectName:t.getProjectName,isViewed:t.noop,selectedSessionTasks:t.noop,getProcessorName:t.getProcessorName,viewTask:t.viewTask}})],1):s("div",{staticClass:"columns is-centered"},[s("div",{staticClass:"column is-6 has-text-centered has-text-grey-lighter content ",staticStyle:{padding:"40px"}},[s("p",{staticClass:"animated zoomIn faster"},[s("svg",{staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 503.635 503.635",width:"160"}},[s("path",{attrs:{d:"M503.635 6.284l-150.846-.635-70.939 150.108c-9.755-1.774-19.744-2.858-29.994-2.858-10.273 0-20.286 1.087-30.062 2.868L150.837 5.649 0 6.284l107.797 228.103c-16.284 26.469-25.855 57.615-25.855 91.06 0 95.135 76.221 172.538 169.913 172.538s169.914-77.404 169.914-172.538c0-33.478-9.59-64.654-25.904-91.139L503.635 6.284zM365.231 25.399l107.385.442L382.873 215.7c-20.981-25.786-49.202-45.257-81.453-55.267l63.811-135.034zm-334.212.442l107.375-.442 63.825 135.052c-32.249 10.025-60.464 29.513-81.436 55.311L31.019 25.841zm371.058 299.606c0 84.279-67.385 152.846-150.221 152.846s-150.221-68.567-150.221-152.846c0-84.288 67.385-152.856 150.221-152.856s150.221 68.568 150.221 152.856z"}}),s("path",{attrs:{d:"M359.904 294.659l-72.519-13.827-35.529-66.173-35.519 66.173-72.529 13.827 50.663 54.49-9.5 74.587 66.885-32.24 66.885 32.24-9.5-74.587 50.663-54.49zM251.856 369.63l-42.769 20.625 6.077-47.76-32.683-35.164 46.76-8.923 22.615-42.115 22.625 42.115 46.75 8.923-32.683 35.164 6.077 47.76-42.769-20.625z"}})])]),s("p",{staticClass:"subtitle has-text-grey-light is-3 animated fadeInUp faster",staticStyle:{"animation-delay":"0.2s"}},[t._v("No pending task for you :)")])])])]),t.selectedTask?s("div",{staticClass:"column"},[s("div",{staticClass:"columns"},[s("div",{staticClass:"column"},[s("task-details",{key:t.selectedTask.TaskID,staticClass:"animated fadeInUp faster",attrs:{selectedTask:t.selectedTask},on:{onUpdate:t.handleUpdate}}),s("task-card-float",{attrs:{loading:t.isLoading,monitor:t.monitor,selectedTask:t.selectedTask},on:{completeTask:t.completeTask,blocked:t.blocked,nextTask:t.nextTask,prevTask:t.prevTask,close:function(e){t.selectedTask=!1}}}),t.showBlock?s("div",{staticClass:"modal is-active"},[s("div",{staticClass:"modal-background"}),s("div",{staticClass:"modal-content"},[s("div",{staticClass:"card"},[s("div",{staticClass:"card-content"},[s("div",{staticClass:"field"},[s("div",{staticClass:"control"},[s("label",{staticClass:"label"},[t._v("Blockage type")]),s("input",{directives:[{name:"model",rawName:"v-model",value:t.blockage.type,expression:"blockage.type"},{name:"validate",rawName:"v-validate",value:{required:!0},expression:"{required:true}"}],attrs:{type:"hidden",name:"blockage_type"},domProps:{value:t.blockage.type},on:{input:function(e){e.target.composing||t.$set(t.blockage,"type",e.target.value)}}}),s("div",{staticClass:"select",class:{"is-danger":t.errors.has("blockage_type")}},[s("select",{directives:[{name:"model",rawName:"v-model",value:t.blockage.type,expression:"blockage.type"}],attrs:{placeholder:"select type"},on:{change:function(e){var s=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.$set(t.blockage,"type",e.target.multiple?s:s[0])}}},[s("option",{attrs:{value:"",di:""}},[t._v("Select")]),t._l(t.config.blockages,(function(e,a){return s("option",{key:a,domProps:{value:e}},[t._v(t._s(e))])}))],2)])]),s("div",{staticClass:"control"},[s("label",{staticClass:"label"},[t._v("Remarks")]),s("textarea",{directives:[{name:"validate",rawName:"v-validate",value:{required:!0},expression:"{required:true}"},{name:"model",rawName:"v-model",value:t.blockage.remarks,expression:"blockage.remarks"}],staticClass:"textarea",class:{"is-danger":t.errors.has("remarks")},attrs:{name:"remarks",placeholder:"Describe the blockage"},domProps:{value:t.blockage.remarks},on:{input:function(e){e.target.composing||t.$set(t.blockage,"remarks",e.target.value)}}}),s("span",{staticClass:"help"},[t._v("Good description will help future you and others understand the reason")])])])]),s("div",{staticClass:"card-content has-background-light"},[s("div",{staticClass:"level"},[s("div",{staticClass:"level-left"}),s("div",{staticClass:"level-right"},[s("div",{staticClass:"buttons"},[s("a",{staticClass:"button is-text ",on:{click:function(e){t.showBlock=!1}}},[t._v("Cancel")]),s("a",{staticClass:"button is-warning",on:{click:t.submitBlockage}},[t._v("UPDATE BLOCKAGE")])])])])])])])]):t._e()],1),s("div",{staticClass:"column is-4 "},[s("div",{staticClass:"level"},[t._m(1),s("div",{},[s("a",{staticClass:"button is-small is-text has-text-grey",on:{click:function(e){t.showCommentForm=!0}}},[t._v("Post comment")])])]),s("comments",{attrs:{showForm:t.showCommentForm,resource_type:"task",resource_id:t.selectedTask.TaskID},on:{close:function(e){t.showCommentForm=!1}}})],1)])]):t._e()]),s("quickview-task",{attrs:{task:t.task},on:{close:function(e){t.task=!1},blocked:t.loadData}})],1)},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"level"},[s("div",{staticClass:"level-left"},[s("h1",{staticClass:"subtitle is-5 has-text-grey"},[t._v("Recent tasks")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{},[s("h3",{staticClass:"subtitle has-text-grey is-5"},[t._v("Comments")])])}]},"697d":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("div",{staticClass:"column",staticStyle:{"border-bottom":"solid 1px #ccc"}},[s("div",{staticClass:"level"},[s("div",{staticClass:"level-start"},[s("div",{staticClass:"level-item"},[s("div",{staticClass:"field is-grouped"},[s("div",{staticClass:"control"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.filterKeyword,expression:"filterKeyword"}],staticClass:"input is-rounded",attrs:{type:"text",placeholder:"Filter"},domProps:{value:t.filterKeyword},on:{input:function(e){e.target.composing||(t.filterKeyword=e.target.value)}}})]),s("div",{staticClass:"control"},[s("span",{staticClass:"help"},[t._v("Showing "),s("strong",[t._v(t._s(t.filteredTasksByKeyword.length))]),t._v(" records")])])])])]),s("div",{staticClass:"level-end"},[s("div",{staticClass:"field is-grouped"},[s("div",{staticClass:"control"},[s("div",{staticClass:"field has-addons"},[s("div",{staticClass:"control"},[s("a",{staticClass:"button is-small",on:{click:function(e){return t.updateDiff(-1)}}},[t._v("«")])]),s("div",{staticClass:"control"},[s("div",{staticClass:"button is-small is-static"},[t._v(t._s(t.dateRangeDis))])]),s("div",{staticClass:"control"},[s("div",{staticClass:"select is-small"},[s("select",{directives:[{name:"model",rawName:"v-model",value:t.filterType,expression:"filterType"}],on:{change:function(e){var s=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.filterType=e.target.multiple?s:s[0]}}},[s("option",{attrs:{value:"all"}},[t._v("All")]),s("option",{attrs:{value:"day"}},[t._v("Daily")]),s("option",{attrs:{value:"week"}},[t._v("Week")]),s("option",{attrs:{value:"month"}},[t._v("Month")])])])]),s("div",{staticClass:"control"},[s("a",{staticClass:"button is-small",on:{click:function(e){t.filterDiff=0}}},[t._v("Reset")])]),s("div",{staticClass:"control"},[s("a",{staticClass:"button is-small",attrs:{disabled:t.filterDiff>=0},on:{click:function(e){return t.updateDiff(1)}}},[t._v("»")])])])]),s("div",{staticClass:"control"},[s("sorter",{attrs:{data:t.filteredTasksByKeyword,sortfields:{updatedAt:"Updated At","details.TaskID":"Task ID","details.project.id":"Project","details.Customer Name":"Customer Name","details.dropfile.Region":"Region","CreatedBy.name":"Created by",PK:"Jobsheet",GS1:"Processor",Status:"Status"}},on:{sorted:t.onSorted}})],1),s("div",{staticClass:"control"},[s("export-excel",{attrs:{data:t.sortedTasks}})],1)])])])]),s("table",{staticClass:"table is-fullwidth is-hoverable"},[t._m(0),s("tbody",t._l(t.sortedTasks,(function(e,a){return s("tr",{key:a,class:{"is-selected":t.isSelected(e)},on:{click:function(s){return t.viewTask(e)}}},[s("td",[s("span",{staticClass:"has-text-grey-lighter"},[t._v(t._s(a+1))])]),s("td",[s("p",{staticClass:"has-text-weight-semibold"},[t._v(t._s(t._f("shortKey")(e.TaskID)))]),s("span",{staticClass:"tag is-info"},[t._v(t._s(e.details.dropfile["Request Type"]))])]),s("td",[t._v("\n                    "+t._s(t.getProjectName(e.details.project.id))+"\n                ")]),s("td",[s("div",[s("span",{staticClass:"has-text-weight-semibold"},[t._v(t._s(e.details["Customer Name"]))]),s("br"),s("span",{staticClass:"heading"},[t._v("Name")])]),e.details.dropfile.Region?s("div",{staticClass:"headings"},[s("span",[t._v(t._s(e.details.dropfile.Region))]),s("span",{staticClass:"heading"},[t._v("Region")])]):t._e()]),s("td",[e.details.dropfile["Short Description on Request"]?s("div",[s("span",[t._v(t._s(e.details.dropfile["Short Description on Request"]))]),s("span",{staticClass:"heading"},[t._v("Description")])]):t._e(),e.details["Product/Requirement"]?s("div",{staticClass:"headings"},[s("span",[t._v(t._s(e.details["Product/Requirement"]))]),s("span",{staticClass:"heading"},[t._v("Product/Requirement")])]):t._e(),e.details["Request Type"]?s("div",{staticClass:"headings"},[s("span",[t._v(t._s(e.details["Request Type"]))]),s("span",{staticClass:"heading"},[t._v("Request Type")])]):t._e(),e.details["Sub-Order Type (if any)"]?s("div",{staticClass:"headings"},[s("span",[t._v(t._s(e.details["Sub-Order Type (if any)"]))]),s("span",{staticClass:"heading"},[t._v("Sub-Order Type")])]):t._e()]),s("td",[e.details.dropfile["Sales Ref Number"]?s("div",{staticClass:"headings"},[s("span",[t._v(t._s(e.details.dropfile["Sales Ref Number"]))]),s("span",{staticClass:"heading"},[t._v("Sales Ref No.")])]):t._e(),e.details.dropfile["BCS Req ID (if applicable)"]?s("div",{staticClass:"headings"},[s("span",[t._v(t._s(e.details.dropfile["BCS Req ID (if applicable)"]))]),s("span",{staticClass:"heading"},[t._v("BCS Request No.")])]):t._e(),t.odNumber(e)?s("div",[s("a",{attrs:{href:t.odLink(e),target:"_blank",rel:"noopener"}},[t._v(t._s(t.odNumber(e)))]),s("span",{staticClass:"heading"},[t._v("OD No.")])]):t._e()]),s("td",[s("div",{staticClass:"headings"},[s("span",[t._v(t._s(t._f("shortKey")(e.PK)))]),s("span",{staticClass:"heading"},[t._v("Jobsheet ID")])]),s("div",{staticClass:"headings"},[s("span",[t._v(t._s(e.CreatedBy.name))]),s("span",{staticClass:"heading"},[t._v("Created By")])]),s("div",{staticClass:"headings"},[s("span",[t._v(t._s(t._f("diffNow")(e.createdAt)))]),s("span",{staticClass:"heading"},[t._v(t._s(t._f("formatDate")(e.createdAt)))])])]),s("td",[s("task-status",{attrs:{task:e}}),t.getProcessorName(e.GS1)?s("div",[s("span",{staticClass:"help"},[t._v(t._s(t.getProcessorName(e.GS1)))]),s("span",{staticClass:"heading"},[t._v("Assigned To")])]):t._e()],1)])})),0)])])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("thead",[s("tr",[s("th",{attrs:{width:"2"}},[t._v("#")]),s("th",[t._v("Task")]),s("th",[t._v("Project")]),s("th",[t._v("Customer")]),s("th",[t._v("Request")]),s("th",[t._v("Ref")]),s("th",[t._v("Jobsheet")]),s("th",[t._v("Status")])])])}]},"6ab2":function(t,e,s){"use strict";s.r(e);var a=s("53c1"),i=s("ea75");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);s("f28e");var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"d86f8c12",null);e["default"]=r.exports},"6b01":function(t,e,s){"use strict";var a=s("77c0"),i=s.n(a);i.a},"6b1e":function(t,e,s){},"6c6f":function(t,e,s){"use strict";s("4795"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var a={data:function(){return{timer:!1}},computed:{login_status:function(){return this.$store.getters.login_status}},watch:{login_status:function(t){"login"===t&&this.$store.dispatch("hideLogin")}},methods:{handleLogout:function(){this.$emit("reset")},doCheck:function(){var t=this;this.$store.dispatch("login.check").then((function(e){e?"pending"!==e.status?t.$emit("success"):t.check(2e3):t.$store.dispatch("login.logout")}))["catch"]((function(e){t.$emit("error",(e.response||{}).message||e.message)}))},check:function(t){this.timer&&"pending"==this.login_status&&setTimeout(this.doCheck,t)}},mounted:function(){this.timer=!0,this.check(0)},destroyed:function(){this.timer=!1}};e["default"]=a,t.exports=e.default,t.exports.default=e.default},"6c76":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[t.$route.meta.blank?s("div",[t._t("default")],2):s("div",[s("div",{staticClass:"hero is-fullheight is-dark ",staticStyle:{position:"fixed",width:"50px","z-index":"2",background:"#003399"}},[s("sidebar-menu")],1),s("div",{staticStyle:{"padding-left":"50px","padding-top":"50px"}},[s("div",{staticClass:"columns is-gapless"},[s("div",{staticClass:"column"},[s("navbar",{on:{logout:function(e){return t.$emit("logout")}}}),s("div",[t._t("default")],2)],1)])])])])},i=[]},"6d6b":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("div",{staticStyle:{"padding-bottom":"280px"}},[s("div",{staticClass:"navbar is-fixed-bottom animated fadeInUp faster "},[s("div",{staticClass:"card is-fullwidth",staticStyle:{width:"100%"}},[s("div",{staticClass:"card-header"},[s("div",{staticClass:"card-header-title"},[s("div",{staticClass:"field has-addons"},[s("p",{staticClass:"control"},[s("a",{staticClass:"button",on:{click:function(e){return t.$emit("prev-task")}}},[s("span",{staticClass:"icon is-small"},[t._v("\n                            «\n                        ")])])]),s("p",{staticClass:"control"},[s("a",{staticClass:"button",on:{click:function(e){return t.$emit("next-task")}}},[s("span",{staticClass:"icon is-small"},[t._v("\n                            »\n                        ")])])])])]),s("span",{staticClass:"card-header-icon",attrs:{href:"#","aria-label":"more options"}},[s("a",{staticClass:"delete",on:{click:function(e){return t.$emit("close")}}})])]),s("div",{staticClass:"columns is-gapless has-background-light"},[s("div",{staticClass:"column is-2"},[s("div",{staticClass:"card-content "},[s("div",[s("span",{staticClass:"heading"},[t._v("PROGRESS")]),s("p",{key:t.monitor.totalCompleted,staticClass:"title is-1 has-text-centered animated zoomIn faster"},[t._v(t._s(t.monitor.totalCompleted)+" "),s("span",{staticClass:"is-size-7"},[t._v("/ "+t._s(t.monitor.initialTotal))])])])])]),s("div",{staticClass:"column has-background-white"},[s("div",{staticClass:"card-content"},[s("div",{staticClass:"columns"},[s("div",{staticClass:"column"},[s("div",{staticClass:"card-contents"},[s("div",{staticClass:"columns is-multiline"},[s("div",{staticClass:"field column"},[s("div",{staticClass:"control"},[s("label",{staticClass:"label"},[t._v("Order No.")]),s("input",{directives:[{name:"model",rawName:"v-model",value:t.form.order_no,expression:"form.order_no"}],staticClass:"input",class:{"is-danger animated shake faster":t.errors.has("order_no")},attrs:{type:"text",name:"order_no"},domProps:{value:t.form.order_no},on:{input:function(e){e.target.composing||t.$set(t.form,"order_no",e.target.value)}}})])]),s("div",{staticClass:"field column"},[s("div",{staticClass:"control"},[s("label",{staticClass:"label"},[t._v("Service ID")]),s("input",{directives:[{name:"model",rawName:"v-model",value:t.form.service_id,expression:"form.service_id"}],staticClass:"input",class:{"is-danger animated shake faster":t.errors.has("service_id")},attrs:{type:"text",name:"service_id"},domProps:{value:t.form.service_id},on:{input:function(e){e.target.composing||t.$set(t.form,"service_id",e.target.value)}}})])]),s("div",{staticClass:"field column"},[s("div",{staticClass:"control"},[s("label",{staticClass:"label"},[t._v("Qoute No.")]),s("input",{directives:[{name:"model",rawName:"v-model",value:t.form.qoute_no,expression:"form.qoute_no"}],staticClass:"input",class:{"is-danger animated shake faster":t.errors.has("qoute_no")},attrs:{type:"text",name:"qoute_no"},domProps:{value:t.form.qoute_no},on:{input:function(e){e.target.composing||t.$set(t.form,"qoute_no",e.target.value)}}})])]),s("div",{staticClass:"field column"},[s("div",{staticClass:"control"},[s("label",{staticClass:"label"},[t._v("Billing No.")]),s("input",{directives:[{name:"model",rawName:"v-model",value:t.form.billing_no,expression:"form.billing_no"}],staticClass:"input",class:{"is-danger animated shake faster":t.errors.has("billing_no")},attrs:{type:"text",name:"billing_no"},domProps:{value:t.form.billing_no},on:{input:function(e){e.target.composing||t.$set(t.form,"billing_no",e.target.value)}}})])]),s("div",{staticClass:"field column"},[s("div",{staticClass:"control"},[s("label",{staticClass:"label"},[t._v("Sphere ID")]),s("input",{directives:[{name:"model",rawName:"v-model",value:t.form["Sphere ID"],expression:"form['Sphere ID']"}],staticClass:"input",class:{"is-danger animated shake faster":t.errors.has("sphere_id")},attrs:{type:"text",name:"sphere_id"},domProps:{value:t.form["Sphere ID"]},on:{input:function(e){e.target.composing||t.$set(t.form,"Sphere ID",e.target.value)}}})])]),s("div",{staticClass:"field column"},[s("div",{staticClass:"control"},[s("label",{staticClass:"label"},[t._v("RFS Date")]),s("input",{directives:[{name:"model",rawName:"v-model",value:t.form["RFS Date"],expression:"form['RFS Date']"}],staticClass:"input",class:{"is-danger animated shake faster":t.errors.has("rfs_date")},attrs:{type:"text",name:"rfs_date"},domProps:{value:t.form["RFS Date"]},on:{input:function(e){e.target.composing||t.$set(t.form,"RFS Date",e.target.value)}}})])]),s("div",{staticClass:"field column"},[s("div",{staticClass:"control"},[s("label",{staticClass:"label"},[t._v("RFT Date")]),s("input",{directives:[{name:"model",rawName:"v-model",value:t.form["RFT Date"],expression:"form['RFT Date']"}],staticClass:"input",class:{"is-danger animated shake faster":t.errors.has("rft_date")},attrs:{type:"text",name:"rft_date"},domProps:{value:t.form["RFT Date"]},on:{input:function(e){e.target.composing||t.$set(t.form,"RFT Date",e.target.value)}}})])]),s("div",{staticClass:"field column"},[s("div",{staticClass:"control"},[s("label",{staticClass:"label"},[t._v("OD Number")]),s("input",{directives:[{name:"model",rawName:"v-model",value:t.form["OD Number"],expression:"form['OD Number']"}],staticClass:"input",class:{"is-danger animated shake faster":t.errors.has("od_number")},attrs:{type:"text",name:"od_number"},domProps:{value:t.form["OD Number"]},on:{input:function(e){e.target.composing||t.$set(t.form,"OD Number",e.target.value)}}})])]),s("div",{staticClass:"field column"},[s("div",{staticClass:"control"},[s("label",{staticClass:"label"},[t._v("OD Link")]),s("input",{directives:[{name:"model",rawName:"v-model",value:t.form["OD Link"],expression:"form['OD Link']"}],staticClass:"input",class:{"is-danger animated shake faster":t.errors.has("od_link")},attrs:{type:"text",name:"od_link"},domProps:{value:t.form["OD Link"]},on:{input:function(e){e.target.composing||t.$set(t.form,"OD Link",e.target.value)}}})])])])])]),s("div",{staticClass:"column is-2"},[s("div",{staticClass:"card-contents"},[s("div",{staticClass:"buttons"},[s("a",{staticClass:"button is-primary  is-fullwidth",class:{"is-loading":t.loading},on:{click:t.handleComplete}},[t._v("\n                                    COMPLETE\n                                ")]),s("a",{staticClass:"button is-text is-fullwidth has-text-danger",on:{click:function(e){return t.$emit("blocked")}}},[t._v("\n                                    Mark as blocked\n                                ")])])])])])])])])])])])])},i=[]},"6d92":function(t,e,s){"use strict";s.r(e);var a=s("d1fd"),i=s("0948");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"6e86":function(t,e,s){"use strict";s.r(e);var a=s("3749"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"6fe9":function(t,e,s){"use strict";s.r(e);var a=s("94fc"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"70df":function(t,e,s){"use strict";s.r(e);var a=s("99bc"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"739c":function(t,e,s){"use strict";var a=s("dbce"),i=s("4ea4");s("99af"),s("4de4"),s("c975"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("c1df")),o=a(s("b17e")),r={props:["jobsheets","routeTo"],data:function(){return{filterKeyword:"",sortKey:"createdAt",sortDescend:!0}},computed:{sortDirection:function(){return this.sortDescend?"descend":"ascend"},sortIcon:function(){return this.sortDescend?'<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-sort-descending" width="44" height="44" viewBox="0 0 24 24" stroke-width="1.5" stroke="#2c3e50" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>\n                            <line x1="4" y1="6" x2="13" y2="6" />\n                            <line x1="4" y1="12" x2="11" y2="12" />\n                            <line x1="4" y1="18" x2="11" y2="18" />\n                            <polyline points="15 15 18 18 21 15" />\n                            <line x1="18" y1="6" x2="18" y2="18" />\n                        </svg>':'<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-sort-ascending" width="44" height="44" viewBox="0 0 24 24" stroke-width="1.5" stroke="#2c3e50" fill="none" stroke-linecap="round" stroke-linejoin="round">\n                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>\n                        <line x1="4" y1="6" x2="11" y2="6" />\n                        <line x1="4" y1="12" x2="11" y2="12" />\n                        <line x1="4" y1="18" x2="13" y2="18" />\n                        <polyline points="15 9 18 6 21 9" />\n                        <line x1="18" y1="6" x2="18" y2="18" />\n                    </svg>'},filteredJobsheets:function(){var t=this,e=this.jobsheets.filter((function(e){return JSON.stringify(e).toLowerCase().indexOf(t.filterKeyword)>-1}));return o.sort(o[this.sortDirection](o.prop(this.sortKey)),e)}},methods:{sortTable:function(t){this.sortKey==t?this.sortDescend=!this.sortDescend:this.sortKey=t},navigateTo:function(t){this.$store.getters.om_role("processors")?this.$router.push("/tasks/projects/".concat(t.GS2,"/").concat(t.PK)):this.$router.push("/jobs/projects/".concat(t.GS2,"/").concat(t.PK))}},filters:{shortKey:function(t){return o.take(12,t)},formatDate:function(t){return(0,n["default"])(t).format("DD MMM YYYY")},formatTime:function(t){return(0,n["default"])(t).format("hh:mm A")}}};e["default"]=r,t.exports=e.default,t.exports.default=e.default},7499:function(t,e,s){"use strict";s.r(e);var a=s("210b"),i=s("da5a");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},7517:function(t,e,s){"use strict";s.r(e);var a=s("85aa"),i=s("5d68");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);s("10cb");var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},7530:function(t,e,s){"use strict";s.r(e);var a=s("ac74"),i=s("684e");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);s("f45e");var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"33cd97da",null);e["default"]=r.exports},"769b":function(t,e,s){},"77c0":function(t,e,s){},"77e3":function(t,e,s){"use strict";s.r(e);var a=s("fe95"),i=s("8ba9");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"782a":function(t,e,s){"use strict";s.r(e);var a=s("58fa"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"786a":function(t,e,s){"use strict";s.r(e);var a=s("165b"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"793c":function(t,e,s){"use strict";s.r(e);var a=s("c4ea"),i=s("782a");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"7bcf":function(t,e,s){"use strict";var a=s("4ea4");Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("43ce")),n={components:{ExportExcel:i["default"]},data:function(){return{records:[]}},mounted:function(){this.$store.dispatch("isLoaded"),this.loadData()},methods:{loadData:function(){var t=this;return this.$store.dispatch("om.reports.load").then((function(e){t.records=e}))}}};e["default"]=n,t.exports=e.default,t.exports.default=e.default},"7c3e":function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var a={props:["task"],data:function(){return{form:{order_no:"",service_id:""}}},computed:{},mounted:function(){this.copyData()},methods:{copyData:function(){this.form.order_no=this.task.feedback.order_no,this.form.service_id=this.task.feedback.service_id},noChanges:function(){return this.task.feedback.order_no==this.form.order_no&&this.task.feedback.service_id==this.form.service_id},handlePatch:function(){var t=this;this.noChanges()?alert("No changes made. Nothing to save"):this.$store.dispatch("om.tasks.patch",{task:this.task,data:this.form}).then((function(e){console.log(e),t.$emit("onpatched",e)}))}}};e["default"]=a,t.exports=e.default,t.exports.default=e.default},"7c89":function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var a={props:["scope","visibility","noDev"],computed:{show:function(){return this.hasScope(this.scope)||"public"==this.visibility||!1}},methods:{hasScope:function(t){return this.$store.getters.userScope(t)}},mounted:function(){this.$store.state.login.user||this.$store.dispatch("login.init")},render:function(t){if(this.show)return this.$scopedSlots["default"]()}};e["default"]=a,t.exports=e.default,t.exports.default=e.default},"7d20":function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t.$createElement;t._self._c;return t._m(0)},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"tabs is-boxed is-marginless"},[s("ul",[s("li",{staticClass:"is-active"},[s("a",{attrs:{href:"#"}},[t._v("Template name")])]),s("li",[s("a",{attrs:{href:"#"}},[t._v("Template name")])]),s("li",[s("a",{attrs:{href:"#"}},[t._v("Template name")])])])])}],n=s("2877"),o={},r=Object(n["a"])(o,a,i,!1,null,null,null);e["default"]=r.exports},"7dbe":function(t,e,s){"use strict";var a=s("4ea4");s("4795");var i=a(s("2b0e")),n=a(s("2f62")),o=a(s("8c4f")),r=a(s("1b3e")),l=a(s("bc3a")),c=a(s("7bb1")),d=a(s("4eb50")),u=a(s("bfa9")),f=a(s("b474")),v=a(s("83ed")),h=a(s("eb90"));i["default"].use(n["default"]),i["default"].use(o["default"]),i["default"].use(c["default"],{events:"blur"}),i["default"].use(d["default"]),i["default"].component("FeatureFlag",f["default"]);var p=s("2347"),m="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************.DH_okdCeVkXRDAMxLWMDzg6nKA3af1R2L3NUR1b8fws";l["default"].defaults.headers.common["Authorization"]="Bearer ".concat(m),l["default"].interceptors.response.use((function(t){return t}),(function(t){throw t}));var _=new u["default"]({key:"oasys",modules:["om","user-management"]}),g=new n["default"].Store({state:{isLoading:!1,isOffline:!1,scrollY:0},mutations:{RESTORE_MUTATION:_.RESTORE_MUTATION,SET_ISLOADING:function(t,e){t.isLoading=e},SET_OFFLINE:function(t,e){t.isOffline=e,t.isLoading=!1},SET_SCROLLY:function(t,e){t.scrollY=e}},actions:{scrollY:function(t,e){t.commit("SET_SCROLLY",e)},dismissError:function(t){t.commit("SET_OFFLINE",!1)},showError:function(t,e){t.commit("SET_OFFLINE",e)},loading:function(t){t.commit("SET_OFFLINE",!1);var e=setTimeout((function(){t.state.isLoading&&t.commit("SET_OFFLINE",!0),t.dispatch("isLoaded")}),3e4);return t.commit("SET_ISLOADING",e),{done:function(){t.dispatch("isLoaded")}}},isLoaded:function(t){t.state.isLoading&&clearTimeout(t.state.isLoading),setTimeout((function(){t.commit("SET_ISLOADING",!1)}),500)}},modules:{login:v["default"],om:h["default"],"user-management":s("0c65")["default"]}}),b=new o["default"]({routes:p});b.beforeEach((function(t,e,s){g.dispatch("loading"),s()})),b.afterEach((function(t,e){g.dispatch("isLoaded")})),i["default"].config.productionTip=!1,new i["default"]({store:g,router:b,render:function(t){return t(r["default"])}}).$mount("#app")},"7fa7":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"section has-background-",style:t.secureBg},[t.canViewDashboard("supervisors")?s("supervisor-dashboard"):t.canViewDashboard("sales")?s("sales-dashboard"):t.canViewDashboard("processors")?s("personal-dashboard"):t.canViewDashboard("executives")?s("executive-dashboard"):s("div",[s("div",{staticClass:"container"},[s("div",{staticClass:"hero is-large"},[s("div",{staticClass:"hero-body"},[s("div",{staticClass:"has-text-grey content"},[s("h1",{staticClass:"is-size-1"},[t._v("Hi, "),s("span",{staticClass:"has-text-grey-dark is-capitalized"},[t._v(t._s(t.user.name))])]),s("p",[t._v("It seems like you don't have any permission for this site. Please contact "),s("span",{staticClass:"has-text-weight-bold"},[t._v("Order Management Team")]),t._v(" below for futher assistance.")]),s("ul",[s("li",[t._v("Muhammad Fazli Bin Ismail")]),s("li",[t._v("Sharifah Salwa Binti Syed Nasharudin")])])])])])])])],1)},i=[]},"81d3":function(t,e,s){"use strict";s.r(e);var a=s("947f"),i=s("582f");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"82a6":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("div",{staticClass:"level"},[t._m(0),s("div",{staticClass:"level-end"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.filterKeyword,expression:"filterKeyword"}],staticClass:"input",attrs:{type:"text",placeholder:"filter"},domProps:{value:t.filterKeyword},on:{input:function(e){e.target.composing||(t.filterKeyword=e.target.value)}}})])]),t._l(t.sortedWorkload,(function(e){return s("div",{key:e.staff_id,staticClass:"card"},[s("div",{},[s("div",{staticClass:"level"},[s("div",{staticClass:"level-left"},[s("div",{staticClass:"column"},[s("a",{staticClass:"tooltip",attrs:{"data-tooltip":"Select this processor"},on:{click:function(s){return t.$emit("selected",e)}}},[t._v(t._s(e.name))]),s("span",{staticClass:"heading"},[t._v(t._s(e.staff_id))])])]),s("div",{staticClass:"level-right"},[s("div",{staticClass:"level-item"},[s("div",{staticClass:"column has-text-centered"},[s("span",{staticClass:"heading"},[t._v("Assigned")]),s("span",{staticClass:"title is-4"},[t._v(t._s(e.assigned))])])]),s("div",{staticClass:"level-item"},[s("div",{staticClass:"column has-text-centered"},[s("span",{staticClass:"heading"},[t._v("Blocked")]),s("span",{staticClass:"title is-4"},[t._v(t._s(e.blocked))])])])])])])])}))],2)},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"level-start"},[s("span",{staticClass:"title is-5"},[t._v("Workload")])])}]},"82cf":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"section"},[t.dropfile?s("div",{},[s("div",{staticClass:"columns"},[s("div",{staticClass:"column is-offset-2"},[s("div",{staticClass:"level"},[s("div",{staticClass:"level-left"},[s("h1",{staticClass:"title is-4"},[t._v(t._s(t.dropfile.TaskID))])]),s("div",{staticClass:"level-end"},[s("span",{staticClass:"tag",class:{"is-dark":"OPEN"==t.dropfile.Status,"is-info":"UPLOADED"==t.dropfile.Status,"is-danger tooltip is-tooltip-multiline is-preline":"REJECTED"==t.dropfile.Status,"is-success":"TASK_COMPLETED"==t.dropfile.Status},attrs:{"data-tooltip":t.getRejectionReason(t.dropfile)}},[t._v(t._s(t.dropfile.Status))])])])])]),s("div",{staticClass:"columns"},[s("div",{staticClass:"column is-2"},[t.dropfile?s("div",{staticClass:"cards"},[s("div",{staticClass:"card-content"},[s("request-progress",{attrs:{dropfile:t.dropfile,jobsheet:t.jobsheet}}),s("hr"),"UPLOADED"!=t.dropfile.Status&&"TASK_COMPLETED"!=t.dropfile.Status?s("div",{staticClass:"button is-danger is-fullwidth",on:{click:function(e){return t.removeFile(t.drop_id)}}},[t._v("\n                            Delete\n                        ")]):t._e()],1)]):t._e()]),s("div",{staticClass:"column is-10"},["REJECTED"!=t.dropfile.Status?s("div",{staticClass:"card"},[s("table-task",{attrs:{filteredTasks:t.tasks,getProjectName:t.getProjectName,isViewed:t.isViewed,selectedSessionTasks:t.noop,getProcessorName:t.getProcessorName,viewTask:t.viewTask}})],1):s("div",{},[s("div",{staticClass:"notification"},[s("span",{staticClass:"title is-4"},[t._v("REJECTED")]),s("p",[t._v("Reason:")]),s("p",{staticClass:"pre-formatted"},[t._v(t._s(t.dropfile.details.rejection.reason))])])])])])]):t._e(),s("quickview-task",{attrs:{task:t.task},on:{close:function(e){t.task=!1}}})],1)},i=[]},"82d3":function(t,e,s){"use strict";s.r(e);var a=s("a5c1"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"82ef":function(t,e,s){"use strict";s.r(e);var a=s("520a"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"83c3":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card animated faster zoomIn"},[s("div",{staticClass:"card-content has-text-white"},[s("div",{staticClass:"content"},[s("p",{staticClass:"is-6 has-text-grey"},[t._v('Please check your email and click "verify" to authenticate')]),s("p",{staticClass:"has-text-centered"},[s("span",{staticClass:"tag is-dark"},[t._v("\n                "+t._s(t.$store.state.login.phrase)+"\n                ")])])])]),s("div",{staticClass:"card-footer"},[s("span",{staticClass:"card-footer-item"},[s("p",[t._v("Didn't get the email?")]),t._v("   \n            "),s("a",{staticClass:"button ",attrs:{href:"javascript:void()"},on:{click:function(e){return e.preventDefault(),t.handleLogout(e)}}},[t._v("\n                Try again\n            ")])])])])},i=[]},"83ed":function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");s("99af"),s("c975"),s("0d03"),s("d3b7"),s("ac1f"),s("25f0"),s("5319"),s("4795"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;i(s("b17e"));var n=a(s("de99")),o=s("bc3a"),r=s("04e1"),l="https://pydyfgbm33.execute-api.us-east-1.amazonaws.com/staging",c="logout",d="login",u="pending",f={state:{temp_token:null,jwt:null,user:null,phrase:"",requiresAuth:!1,pwa_installed:!1},mutations:{REMOVE_JWT_TOKEN:function(t){n["default"].remove("simi.jwt_token"),o.defaults.headers.common["Authorization"]=null,t.jwt=null},UPDATE_JWT_TOKEN:function(t,e){e&&(t.jwt=e,o.defaults.headers.common["Authorization"]="Bearer ".concat(e),t.user=r(e),n["default"].set("simi.jwt_token",e))},REMOVE_TEMP_TOKEN:function(t){n["default"].remove("simi.temp_token"),t.temp_token=null},UPDATE_TEMP_TOKEN:function(t,e){t.temp_token=e,n["default"].set("simi.temp_token",e),t.phrase=e?e.phrase:""},REQUIRES_AUTH:function(t,e){t.requiresAuth=e},SET_INSTALLED:function(t,e){t.pwa_installed=e}},getters:{secureBg:function(t){if(t.user){var e=s("be4e"),a=new e({content:t.user.staff_id,padding:1,width:50,height:50,color:"#fcfcfc",background:"#ffffff",ecl:"M"}).svg().toString().replace(/[\n\r]+/g,"");return{background:"url('data:image/svg+xml;utf8,".concat(a,"') repeat")}}},isLoggedIn:function(t){return!!t.jwt},login_status:function(t){return t.jwt||t.temp_token?t.jwt?t.jwt?d:void 0:u:c},show_login:function(t){return t.requiresAuth&&!t.jwt},userScope:function(t){return function(e){return!!t.user&&(t.user.scope||[]).indexOf(e)>-1}}},actions:{requiresAuth:function(t,e){t.commit("REQUIRES_AUTH",e)},"installed.init":function(t){t.commit("SET_INSTALLED",n["default"].get.item("simi.pwa.isInstalled"))},"installed.update":function(t,e){n["default"].set("simi.pwa.isInstalled",e),t.dispatch("installed.init")},"login.init":function(t){t.commit("UPDATE_TEMP_TOKEN",n["default"].get.item("simi.temp_token")),t.commit("UPDATE_JWT_TOKEN",n["default"].get.item("simi.jwt_token")),setTimeout((function(){o.get("".concat(l,"/authentication/renew")).then((function(e){var s=e.data.jwt,a=t.state.user;if(t.commit("UPDATE_JWT_TOKEN",s),!t.state.user.staff_id)return t.dispatch("login.logout");JSON.stringify(t.state.user.scope)!=JSON.stringify(a.scope)&&location.reload()}))}),1e3)},"login.attempt":function(t,e){return console.log("Login attempt",l),o.post("".concat(l,"/authentication/request"),e).then((function(e){var s=e.data,a=s.token,i=s.phrase;return t.commit("UPDATE_TEMP_TOKEN",{token:a,phrase:i}),t.commit("REMOVE_JWT_TOKEN"),e.data}))["catch"]((function(t){throw console.log("ERROR",JSON.stringify(t)),t}))},"login.check":function(t){var e=t.state.temp_token.token;if(e)return o.get("".concat(l,"/authentication/check/").concat(e)).then((function(e){var s=e.data,a=s.jwt,i=s.status;return"verified"===i&&(t.commit("UPDATE_JWT_TOKEN",a),t.commit("REMOVE_TEMP_TOKEN")),{status:i,message:e.message}}))["catch"]((function(t){console.log(t)}))},"login.logout":function(t){t.commit("REMOVE_JWT_TOKEN"),t.commit("REMOVE_TEMP_TOKEN")}}};e["default"]=f,t.exports=e.default,t.exports.default=e.default},"848b":function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");s("4de4"),s("b0c0"),s("d3b7"),s("3ca3"),s("ddb0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("c1df")),r=a(s("350b")),l=a(s("fe45")),c=a(s("0ee1")),d={components:{QuickviewTask:r["default"],AttachmentTag:l["default"],TableTask:c["default"]},data:function(){return{tab:"tasks",task:!1,show_all:!1,selected_tasks:{},processor:!1,isLoading:!1,selectedDistribution:!1}},computed:{me:function(){return this.$store.state.login.user},project_id:function(){return this.$route.params.project_id},project:function(){return this.$store.state.om.projects[this.project_id]},jobsheet_id:function(){return this.$route.params.jobsheet_id},jobsheets:function(){return this.$store.state.om.jobs},jobsheet:function(){return this.jobsheets[this.jobsheet_id]},totalTasks:function(){return this.jobsheet.tasks.length},filteredTasks:function(){var t=this;if(this.selectedDistribution&&"distributions"==this.tab)return this.distributions[this.selectedDistribution];var e=this.show_all?n.identity:n.filter((function(e){return e.GS1==t.me.staff_id}));return"tasks"==this.tab?n.pipe(n.sortBy(n.prop("TaskID")),e)(this.jobsheet.tasks||[]):[]},total_selected:function(){return n.pipe(n.values,n.sum)(this.selected_tasks)},processors:function(){return this.$store.state.om.processors},processorsMapping:function(){return n.indexBy(n.prop("staff_id"),this.processors)},distributions:function(){return n.pipe(n.filter((function(t){return"UNASSIGNED"!=t.Status})),n.groupBy(n.prop("GS1")))(this.jobsheet.tasks)}},methods:{noop:function(){},getProjectName:function(t){return this.$store.state.om.projects[t].name},getProcessorName:function(t){return(this.processorsMapping[t]||{name:""}).name},handleAssign:function(){var t=this;this.isLoading||(this.isLoading=!0,this.$store.dispatch("om.processor.assign",{processor:this.processor,tasks:this.jobsheet.tasks.filter((function(e){return t.selected_tasks[e.TaskID]}))}).then((function(){t.isLoading=!1,t.loadData(),t.selected_tasks={},t.processor=""})))},selectAll:function(){this.$set(this,"selected_tasks",n.pipe(n.indexBy(n.prop("TaskID")),n.map((function(){return!0})))(this.filteredTasks))},clearSelection:function(){this.selected_tasks={}},isSelected:function(t){return this.show_all&&this.selected_tasks[t.TaskID]},loadData:function(){var t=this;return Promise.all([this.$store.dispatch("om.project.load",this.project_id),this.$store.dispatch("om.jobsheet.load",this.jobsheet_id)]).then((function(){t.$store.dispatch("isLoaded")}))},viewTask:function(t){this.task=t}},mounted:function(){this.loadData()},filters:{shortKey:function(t){return n.take(12,t)},formatDate:function(t){return(0,o["default"])(t).format("DD MMM YYYY")}}};e["default"]=d,t.exports=e.default,t.exports.default=e.default},"84df":function(t,e,s){"use strict";var a=s("dbce"),i=s("4ea4");s("4de4"),s("7db0"),s("c975"),s("b0c0"),s("ac1f"),s("5319"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("7f45")),o=i(s("81d3")),r=i(s("ed06")),l=i(s("51ad")),c=a(s("b17e")),d=i(s("d460")),u=i(s("350b")),f=i(s("cbc3")),v=i(s("fe45")),h=i(s("43ce")),p=i(s("0ee1")),m=i(s("3f9b"));n["default"].tz.setDefault("Asia/Kuala_Lumpur");var _={props:["status"],beforeRouteEnter:function(t,e,s){s((function(t){t.$store.getters.om_role(["processors"])||t.$router.replace("/")}))},components:{Comments:d["default"],TaskCard:o["default"],TaskDetails:l["default"],TaskCardFloat:r["default"],QuickviewTask:u["default"],NetworkDiagramUpload:f["default"],AttachmentTag:v["default"],ExportExcel:h["default"],TableTask:p["default"],Blockage:m["default"]},data:function(){return{isLoading:!1,showCommentForm:!1,selectedTask:!1,currentForm:{qoute_no:"",order_no:"",service_no:"",billing_no:""},submitting:!1,users:[],monitor:{startTime:!1,initialTotal:!1,totalCompleted:0,idx:0},showBlock:!1,task:!1,selectionMode:!1,selectedSessionTasks:{}}},watch:{selectedTask:function(){this.currentForm={qoute_no:"",order_no:"",service_no:""},this.$store.dispatch("isLoaded")},TaskID:function(t){this.selectedTask=!!t&&this.tasks.find((function(e){return e.TaskID==t})),window.scrollTo(0,0)}},mounted:function(){this.loadData()},computed:{sessionTasks:function(){return c.values(this.selectedSessionTasks).filter((function(t){return t}))},totalSelected:function(){return this.sessionTasks.length},isQuickview:function(){return!1!==this.task},config:function(){return this.$store.state.om.configs},staff_id:function(){return this.$store.state.login.user.staff_id},tasks:function(){var t=this,e={Open:["ASSIGNED","ASSIGNED_RERAISE"],Blocked:["BLOCKED"]},s=e[this.status]||[];return c.filter((function(e){return s.indexOf(e.Status)>-1||e.TaskID==t.TaskID}),this.$store.state.om.mytasks)},filteredTasks:function(){return this.tasks},isCompleted:function(){return this.monitor.initialTotal==this.monitor.totalCompleted},TaskID:function(){return this.$route.params.task_id&&console.log("TASK ID ",this.$route.params.task_id),this.$route.params.task_id}},methods:{cancelSession:function(){this.selectionMode=!1,this.selectedSessionTasks={}},startSessionNow:function(){this.task=!1,this.selectionMode=!1,this.startTask(0)},selectAll:function(){this.selectedSessionTasks=c.pipe(c.indexBy(c.prop("task_id")))(this.tasks)},clearAll:function(){this.selectedSessionTasks={}},selectTasks:function(){this.selectionMode=!0},isViewed:function(t){return this.task==t},getProjectName:function(t){return this.$store.state.om.projects[t].name},viewTask:function(t){this.selectionMode&&this.$set(this.selectedSessionTasks,t.task_id,!this.selectedSessionTasks[t.task_id]&&t),this.task=t},blocked:function(){this.showBlock=!0},onblocked:function(){this.$store.dispatch("om.processors.mytasks"),this.monitor.totalCompleted++,this.isCompleted?this.$router.push("/tasks"):this.nextTask()},completeTask:function(t){var e=this;this.isLoading=!0,this.$validator.validateAll().then((function(s){s?e.$store.dispatch("om.tasks.update",{data:t,task_id:e.selectedTask.task_id}).then((function(t){e.$store.dispatch("om.processors.mytasks"),e.monitor.totalCompleted++,e.isCompleted?e.$router.push("/tasks"):e.nextTask()}))["catch"]((function(t){})).then((function(){e.isLoading=!1})):e.isLoading=!1}))},startTask:function(t){this.monitor.startTime=(0,n["default"])(),this.monitor.initialTotal=this.totalSelected,this.monitor.totalCompleted=0,this.monitor.idx=t,this.$router.push("/tasks/".concat(this.sessionTasks[t].TaskID))},stopTask:function(){this.selectedSessionTasks={},this.$router.push("/tasks")},nextTask:function(){this.monitor.idx++;var t=this.sessionTasks.length;this.monitor.idx>=t&&(this.monitor.idx=0);var e=this.sessionTasks[this.monitor.idx].TaskID;this.$router.push("/tasks/".concat(e))},prevTask:function(){this.monitor.idx--;var t=this.sessionTasks.length;this.monitor.idx<0&&(this.monitor.idx=t-1);var e=this.sessionTasks[this.monitor.idx].TaskID;this.$router.push("/tasks/".concat(e))},loadData:function(){var t=this;this.$store.dispatch("om.processors.mytasks").then((function(){t.$store.dispatch("isLoaded")})).then((function(){t.TaskID&&(t.selectedTask=c.find((function(e){return e.TaskID==t.TaskID}),t.tasks),t.$set(t.selectedSessionTasks,t.TaskID,t.selectedTask),t.startSessionNow())})),this.$store.dispatch("om.processors.list").then((function(e){t.users=e}))},getProcessorName:function(t){return c.pipe(c.find((function(e){return e.staff_id==t})),(function(t){return(t||{}).name}))(this.users)},moreInfo:function(t){this.$router.push("/tasks/".concat(t.task_id))},copyToCb:function(t){this.$copyText(t).then((function(t){alert("Copied"),console.log(t)}),(function(t){alert("Can not copy"),console.log(t)}))}},filters:{diffNow:function(t){return(0,n["default"])(t).fromNow()},shortKey:function(t){return c.take(12,t)},formatDate:function(t){return(0,n["default"])(t).format("DD MMM YYYY")},formatTime:function(t){return(0,n["default"])(t).format("hh:mm A")}}};e["default"]=_,t.exports=e.default,t.exports.default=e.default},"85aa":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[t.showInprogress?s("div",{staticClass:"modal is-active"},[s("div",{staticClass:"modal-background",on:{click:function(e){t.showInprogress=!1}}}),s("div",{staticClass:"modal-content",staticStyle:{width:"auto"}},[s("div",{staticClass:"box"},[s("table-jobsheets",{attrs:{jobsheets:t.jobsheetsInprogress}})],1)])]):t._e(),s("div",[s("div",{staticClass:"columns is-multiline"},[s("div",{staticClass:"column"},[s("router-link",{staticClass:"box",attrs:{to:"/dropfiles?status=OPEN"}},[s("div",{staticClass:"level is-mobile"},[s("div",{staticClass:"level-left"},[s("div",{staticClass:"pending-item-icon has-text-warning"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"42",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 14v-2.47l6.88-6.88c.2-.2.51-.2.71 0l1.77 1.77c.2.2.2.51 0 .71L8.47 14H6zm12 0h-7.5l2-2H18v2z"}})])]),s("div",[s("strong",[t._v("Pending Review")]),s("div",{staticClass:"help"},[t._v("Requests to be review")])])]),s("div",{staticClass:"level-right"},[s("span",{staticClass:"title is-3"},[t._v(t._s(t.stats.open))])])])])],1),s("div",{staticClass:"column"},[s("router-link",{staticClass:"box",attrs:{to:"/dropfiles?status=ON_HOLD"}},[s("div",{staticClass:"level is-mobile"},[s("div",{staticClass:"level-left"},[s("div",{staticClass:"pending-item-icon"},[t.stats.onHoldOverdue>0?s("span",{staticClass:"has-text-danger animated flash infinite"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"42",height:"42",viewBox:"0 0 24 24",fill:"none",stroke:"#FF6600","stroke-width":"3","stroke-linecap":"round","stroke-linejoin":"round"}},[s("polygon",{attrs:{fill:"#FF6600",points:"7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2"}})])]):s("span",{staticClass:"has-text-primary"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"42",height:"42",viewBox:"0 0 24 24",fill:"none",stroke:"#FF6600","stroke-width":"3","stroke-linecap":"round","stroke-linejoin":"round"}},[s("polygon",{attrs:{fill:"#FF6600",points:"7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2"}})])])]),s("div",[s("strong",[t._v("On hold Request")]),s("div",{staticClass:"help"},[t._v("Pending dropfile to be review")])])]),s("div",{staticClass:"level-right"},[s("span",{staticClass:"title is-3"},[t._v(t._s(t.stats.onHold))])])])])],1),s("div",{staticClass:"column"},[s("a",{staticClass:"box",on:{click:function(e){return t.showJobsheet("OPEN")}}},[s("div",{staticClass:"level is-mobile"},[s("div",{staticClass:"level-left"},[s("div",{staticClass:"pending-item-icon has-text-info"},[s("svg",{attrs:{"data-name":"Layer 1",xmlns:"http://www.w3.org/2000/svg",width:"50",height:"50",viewBox:"0 0 50 50"}},[s("title",[t._v("\n                      AWS-Systems-Manager_Automation_dark-bg\n                  ")]),s("path",{attrs:{d:"M29 48h-9a1 1 0 0 1-.73-.31 1 1 0 0 1-.27-.75l.17-2.89a20.54 20.54 0 0 1-4.77-2l-1.9 2.19a1 1 0 0 1-.72.33 1.08 1.08 0 0 1-.74-.29L4.72 38a1 1 0 0 1-.29-.74 1 1 0 0 1 .33-.72l2.17-1.93A20.09 20.09 0 0 1 5 29.8l-2.94.2A1 1 0 0 1 1 29v-9a1 1 0 0 1 .31-.73 1 1 0 0 1 .75-.27l2.94.2a20 20 0 0 1 2-4.76L4.76 12.5a1 1 0 0 1-.33-.72 1 1 0 0 1 .29-.78L11 4.72a1.1 1.1 0 0 1 .74-.29 1 1 0 0 1 .72.33l1.94 2.18A20 20 0 0 1 19.2 5L19 2.06a1 1 0 0 1 .27-.75A1 1 0 0 1 20 1h9a1 1 0 0 1 .73.31 1 1 0 0 1 .27.75L29.8 5a20.62 20.62 0 0 1 4.75 2l2-2.18a1 1 0 0 1 .72-.33 1 1 0 0 1 .74.29L44.28 11a1 1 0 0 1 .29.74 1 1 0 0 1-.33.72l-2.18 1.94a20.47 20.47 0 0 1 2 4.76l2.9-.17a1 1 0 0 1 .75.27A1 1 0 0 1 48 20v9a1 1 0 0 1-.31.73 1 1 0 0 1-.75.27L44 29.8a20.26 20.26 0 0 1-2 4.77l2.17 1.93a1 1 0 0 1 .33.72 1 1 0 0 1-.29.74L38 44.28a1.06 1.06 0 0 1-.74.29 1 1 0 0 1-.72-.33l-1.93-2.17a19.71 19.71 0 0 1-4.77 2l.16 2.87a1 1 0 0 1-.27.75A1 1 0 0 1 29 48zm-7.88-2h6.82l-.15-2.67a1 1 0 0 1 .78-1 17.87 17.87 0 0 0 5.69-2.36 1 1 0 0 1 1.28.18l1.78 2 4.83-4.83-2-1.78a1 1 0 0 1-.18-1.28 18.18 18.18 0 0 0 2.35-5.69 1 1 0 0 1 1-.78l2.68.15v-6.85l-2.68.15a1 1 0 0 1-1-.78 18 18 0 0 0-2.36-5.68 1 1 0 0 1 .18-1.28l2-1.79-4.85-4.83-1.79 2a1 1 0 0 1-1.29.18 18.31 18.31 0 0 0-5.67-2.35 1 1 0 0 1-.78-1L27.91 3h-6.82l.15 2.69a1 1 0 0 1-.78 1 17.8 17.8 0 0 0-5.68 2.35 1 1 0 0 1-1.28-.18l-1.79-2-4.83 4.85 2 1.79a1 1 0 0 1 .18 1.28 18.24 18.24 0 0 0-2.36 5.68 1 1 0 0 1-1 .78L3 21.09v6.82l2.67-.15a1 1 0 0 1 1 .78 17.85 17.85 0 0 0 2.35 5.69 1 1 0 0 1-.18 1.28l-2 1.78 4.83 4.83 1.78-2a1 1 0 0 1 1.28-.18 18.2 18.2 0 0 0 5.69 2.36 1 1 0 0 1 .78 1z",fill:"currentFill"}}),s("path",{attrs:{d:"M24.57 36.28a11.73 11.73 0 0 1-11.46-9l2-.46a9.78 9.78 0 0 0 19.09-.32l2 .4a11.81 11.81 0 0 1-11.63 9.38zM15 22.54l-2-.4a11.79 11.79 0 0 1 22.86-.94l-1.86.56a9.79 9.79 0 0 0-19 .78z",fill:"currentFill"}}),s("path",{attrs:{fill:"currentFill",d:"M30.73 20.12l.38-.73 3.6 1.82 1.56-3.73.76.32-1.9 4.54-4.4-2.22zM13.33 34.05l-2-.41 1.44-6.88a1 1 0 0 1 1.17-.76l7.19 1.13-.31 2-6.25-1z"}}),s("path",{attrs:{d:"M35.34 23.34a1 1 0 0 1-.4-.08l-6.65-3 .82-1.82L34.88 21l2.76-5.41 1.78.91-3.18 6.3a1 1 0 0 1-.9.54z",fill:"currentFill"}})])]),t._m(0)]),s("div",{staticClass:"level-right"},[s("span",{staticClass:"title is-3"},[t._v(t._s(t.stats.inprogress))])])])])]),s("div",{staticClass:"column"},[s("router-link",{staticClass:"box",attrs:{to:"/jobs/unassigned?everyone=true"}},[s("div",{staticClass:"level is-mobile"},[s("div",{staticClass:"level-left"},[s("div",{staticClass:"pending-item-icon has-text-info"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"42",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm0 4c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm6 12H6v-1.4c0-2 4-3.1 6-3.1s6 1.1 6 3.1V19z"}})])]),s("div",[s("strong",[t._v("Pending Assign")]),s("div",{staticClass:"help"},[t._v("To be assign to processors")])])]),s("div",{staticClass:"level-right"},[s("span",{staticClass:"title is-3"},[t._v(t._s(t.stats.unassigned))])])])])],1),s("div",{staticClass:"column"},[s("router-link",{staticClass:"box",attrs:{to:"/jobs/recent?status=assigned&everyone=true&defaultFilterType=all"}},[s("div",{staticClass:"level is-mobile"},[s("div",{staticClass:"level-left"},[s("div",{staticClass:"pending-item-icon has-text-success"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"42",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm-2 14l-4-4 1.41-1.41L10 14.17l6.59-6.59L18 9l-8 8z"}})])]),s("div",[s("strong",[t._v("Pending Submit")]),s("div",{staticClass:"help"},[t._v("Order creation in progress")])])]),s("div",{staticClass:"level-right"},[s("span",{staticClass:"title is-3"},[t._v(" "+t._s(t.stats.assigned))])])])])],1),s("div",{staticClass:"column"},[s("router-link",{staticClass:"box",attrs:{to:"/jobs/recent?status=blocked&everyone=true&defaultFilterType=all"}},[s("div",{staticClass:"level is-mobile"},[s("div",{staticClass:"level-left"},[s("div",{staticClass:"pending-item-icon has-text-danger"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"42",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM4 12c0-4.42 3.58-8 8-8 1.85 0 3.55.63 4.9 1.69L5.69 16.9C4.63 15.55 4 13.85 4 12zm8 8c-1.85 0-3.55-.63-4.9-1.69L18.31 7.1C19.37 8.45 20 10.15 20 12c0 4.42-3.58 8-8 8z"}})])]),s("div",[s("strong",[t._v("Blocked tasks")]),s("div",{staticClass:"help"},[t._v("Order to be hold")])])]),s("div",{staticClass:"level-right"},[s("span",{staticClass:"title is-3"},[t._v(t._s(t.stats.blocked))])])])])],1)])])])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("strong",[t._v("In Progress")]),s("div",{staticClass:"help"},[t._v("In progress jobsheets")])])}]},"863e":function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");s("b0c0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("c1df")),r=a(s("36dc")),l={components:{TableJobsheets:r["default"]},data:function(){return{isEditTitle:!1,newTitle:"",isLoading:!1}},computed:{project_id:function(){return this.$route.params.project_id},project:function(){return this.$store.state.om.projects[this.project_id]},jobsheets:function(){return this.project.jobsheets},isEditable:function(){return this.$store.getters.om_has_role(["project_manager","supervisors"])}},methods:{cancelEdit:function(){this.isEditTitle=!1},saveNewTitle:function(){var t=this;this.isLoading||(this.isLoading=!0,this.$store.dispatch("om.projects.updateTitle",{project_id:this.project.PK,title:this.newTitle})["catch"]((function(t){})).then((function(){t.isLoading=!1,t.isEditTitle=!1})))},editTitle:function(){this.isEditable&&(this.newTitle=this.project.name,this.isEditTitle=!0)},loadData:function(){var t=this;return this.$store.dispatch("om.project.load",this.project_id).then((function(){t.$store.dispatch("isLoaded")}))},deleteProject:function(){var t=this;return this.isLoading=!0,this.$store.dispatch("loading"),this.$store.dispatch("om.projects.delete",this.project_id).then((function(){t.$store.dispatch("om.project.list"),t.$router.push("/jobs"),t.$store.dispatch("om.dropfile.all")}))}},mounted:function(){this.loadData()},watch:{project_id:function(t){this.loadData()}},filters:{shortKey:function(t){return n.take(12,t)},formatDate:function(t){return(0,o["default"])(t).format("DD MMM YYYY")},formatTime:function(t){return(0,o["default"])(t).format("hh:mm A")}}};e["default"]=l,t.exports=e.default,t.exports.default=e.default},"87f9":function(t,e,s){t.exports=s.p+"old-oasys-global/img/om_submission.bb7ad287.png"},"883d":function(t,e,s){"use strict";s.r(e);var a=s("39c7"),i=s("c577");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);s("4b62");var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"8a62":function(t,e,s){"use strict";var a=s("19eb"),i=s.n(a);i.a},"8a7d":function(t,e,s){"use strict";var a=s("dbce");s("99af"),s("b0c0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("b17e")),n={props:["file"],data:function(){return{showPopover:!1}},computed:{job:function(){return this.$store.state.om.jobs[this.file.PK]},tasks:function(){if(!this.job)return this.$store.dispatch("om.jobsheet.load",this.file.PK),!1;var t=i.pipe(i.pluck("Status"),i.groupBy(i.identity),i.map(i.length));return t(this.job.tasks)}},methods:{getRejectionReason:function(t){return"REJECTED"!=t.Status?"":"".concat(t.details.rejection.reason,"\n\n-").concat(t.details.rejection.rejectedBy.name,"-")}}};e["default"]=n,t.exports=e.default,t.exports.default=e.default},"8ab8":function(t,e,s){"use strict";s("b0c0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var a={data:function(){return{form:{name:""}}},mounted:function(){this.$refs.name.focus()},computed:{},methods:{handleSubmit:function(){var t=this;this.$validator.validateAll().then((function(e){e?(t.$emit("onsubmit",t.form),t.$emit("close")):alert("Please check validation")}))}}};e["default"]=a,t.exports=e.default,t.exports.default=e.default},"8b61":function(t,e,s){"use strict";s.r(e);var a=s("eed5"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"8ba9":function(t,e,s){"use strict";s.r(e);var a=s("31c1"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"8bcf":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card-content",style:t.$store.getters.secureBg},[s("div",{staticClass:"level",class:{"is-quickview":t.isQuickview}},[s("div",{staticClass:"level-left"},[s("h1",{staticClass:"subtitle is-5 has-text-grey"},[t._v(t._s(t.status)+" tasks")])]),s("div",{staticClass:"level-right"},[!t.selectedTask&&t.filteredTasks.length>0&&!t.selectionMode?s("a",{staticClass:"button is-primary",on:{click:t.selectTasks}},[s("svg",{staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M8 5v14l11-7z"}})]),t._v("\n                 Start session")]):t._e(),t.selectionMode?s("a",{staticClass:"button",on:{click:t.cancelSession}},[s("svg",{staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M6 6h12v12H6z"}})]),t._v("\n\n                 Cancel")]):t._e(),t.selectedTask?s("a",{staticClass:"button is-danger",on:{click:function(e){return t.stopTask()}}},[s("svg",{staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M6 6h12v12H6z"}})]),t._v("\n                 \n                End session\n            ")]):t._e()])]),t.selectionMode?s("div",{staticClass:"navbar is-fixed-bottom animated bounceInUp fast",staticStyle:{"border-top":"solid 1px #00000030","z-index":"36"}},[s("div",{staticClass:"container"},[s("div",{staticClass:"column"},[s("div",{staticClass:"level"},[s("div",{staticClass:"level-left"},[s("div",{staticClass:"level-item"},[s("span",[s("strong",[t._v(t._s(t.totalSelected))]),t._v("  tasks selected")]),s("div",{staticClass:"buttons",staticStyle:{"margin-left":"20px"}},[s("a",{staticClass:"button is-text is-small",on:{click:t.selectAll}},[t._v("Select all")]),s("a",{staticClass:"button is-text is-small",on:{click:t.clearAll}},[t._v("Clear all")])])])]),s("div",{staticClass:"level-right"},[s("div",{staticClass:"level-item"},[s("div",{staticClass:"buttons"},[s("a",{staticClass:"button is-primary",on:{click:t.startSessionNow}},[t._v("Start now")]),s("a",{staticClass:"button is-text",on:{click:t.cancelSession}},[t._v("cancel")])])])])])])])]):t._e(),s("div",{staticClass:"columns",class:{"is-quickview":t.isQuickview}},[t.selectedTask?t._e():s("div",{staticClass:"column"},[t.filteredTasks.length>0?s("div",{staticClass:"card"},[s("table-task",{attrs:{filteredTasks:t.filteredTasks,getProjectName:t.getProjectName,isViewed:t.isViewed,selectedSessionTasks:t.selectedSessionTasks,getProcessorName:t.getProcessorName,viewTask:t.viewTask}})],1):s("div",{staticClass:"columns is-centered"},[s("div",{staticClass:"column is-6 has-text-centered has-text-grey-lighter content ",staticStyle:{padding:"40px"}},[s("p",{staticClass:"animated zoomIn faster"},[s("svg",{staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 503.635 503.635",width:"160"}},[s("path",{attrs:{d:"M503.635 6.284l-150.846-.635-70.939 150.108c-9.755-1.774-19.744-2.858-29.994-2.858-10.273 0-20.286 1.087-30.062 2.868L150.837 5.649 0 6.284l107.797 228.103c-16.284 26.469-25.855 57.615-25.855 91.06 0 95.135 76.221 172.538 169.913 172.538s169.914-77.404 169.914-172.538c0-33.478-9.59-64.654-25.904-91.139L503.635 6.284zM365.231 25.399l107.385.442L382.873 215.7c-20.981-25.786-49.202-45.257-81.453-55.267l63.811-135.034zm-334.212.442l107.375-.442 63.825 135.052c-32.249 10.025-60.464 29.513-81.436 55.311L31.019 25.841zm371.058 299.606c0 84.279-67.385 152.846-150.221 152.846s-150.221-68.567-150.221-152.846c0-84.288 67.385-152.856 150.221-152.856s150.221 68.568 150.221 152.856z"}}),s("path",{attrs:{d:"M359.904 294.659l-72.519-13.827-35.529-66.173-35.519 66.173-72.529 13.827 50.663 54.49-9.5 74.587 66.885-32.24 66.885 32.24-9.5-74.587 50.663-54.49zM251.856 369.63l-42.769 20.625 6.077-47.76-32.683-35.164 46.76-8.923 22.615-42.115 22.625 42.115 46.75 8.923-32.683 35.164 6.077 47.76-42.769-20.625z"}})])]),s("p",{staticClass:"subtitle has-text-grey-light is-3 animated fadeInUp faster",staticStyle:{"animation-delay":"0.2s"}},[t._v("No pending task for you :)")])])])]),t.selectedTask?s("div",{staticClass:"column"},[s("div",{staticClass:"columns"},[s("div",{staticClass:"column"},[s("task-details",{key:t.selectedTask.TaskID,staticClass:"animated fadeInUp faster",attrs:{selectedTask:t.selectedTask}}),s("task-card-float",{attrs:{loading:t.isLoading,monitor:t.monitor,selectedTask:t.selectedTask},on:{completeTask:t.completeTask,blocked:t.blocked,nextTask:t.nextTask,prevTask:t.prevTask,close:t.stopTask}}),t.showBlock?s("blockage",{attrs:{config:t.config,"selected-task":t.selectedTask},on:{close:function(e){t.showBlock=!1},blocked:t.onblocked}}):t._e()],1),s("div",{staticClass:"column is-4 "},[s("network-diagram-upload",{attrs:{resource:t.selectedTask}}),s("div",{staticClass:"level",staticStyle:{"margin-top":"20px"}},[t._m(0),s("div",{},[s("a",{staticClass:"button is-small is-text has-text-grey",on:{click:function(e){t.showCommentForm=!0}}},[t._v("Post comment")])])]),s("comments",{attrs:{showForm:t.showCommentForm,resource:t.selectedTask},on:{close:function(e){t.showCommentForm=!1}}}),s("div",{staticStyle:{height:"200px"}})],1)])]):t._e()]),s("quickview-task",{attrs:{task:t.task},on:{close:function(e){t.task=!1},blocked:t.loadData}})],1)},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{},[s("h3",{staticClass:"subtitle has-text-grey is-5"},[t._v("Comments")])])}]},"8bef":function(t,e,s){"use strict";s.r(e);var a=s("9292"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"8c59":function(t,e,s){"use strict";s.r(e);var a=s("e54a"),i=s("1078");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"8d5c":function(t,e,s){"use strict";s.r(e);var a=s("6153"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},"8d78":function(t,e,s){"use strict";var a=s("dbce"),i=s("4ea4");s("7db0"),s("b0c0"),s("ac1f"),s("5319"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("7f45")),o=i(s("81d3")),r=i(s("ed06")),l=i(s("51ad")),c=a(s("b17e")),d=i(s("d460")),u=i(s("350b")),f=i(s("fe45")),v=i(s("0ee1"));n["default"].tz.setDefault("Asia/Kuala_Lumpur");var h={beforeRouteEnter:function(t,e,s){s((function(t){t.$store.getters.om_role(["processors"])||t.$router.replace("/")}))},components:{Comments:d["default"],TaskCard:o["default"],TaskDetails:l["default"],TaskCardFloat:r["default"],QuickviewTask:u["default"],AttachmentTag:f["default"],TableTask:v["default"]},data:function(){return{isLoading:!1,showCommentForm:!1,config:{blockages:["system issue","open order"]},selectedTask:!1,blockage:{type:"",remarks:""},currentForm:{qoute_no:"",order_no:"",service_no:"",billing_no:""},submitting:!1,users:[],monitor:{startTime:!1,initialTotal:!1,totalCompleted:0,idx:0},showBlock:!1,task:!1}},watch:{selectedTask:function(){this.currentForm={qoute_no:"",order_no:"",service_no:""},this.$store.dispatch("isLoaded")},TaskID:function(t){this.selectedTask=!!t&&this.tasks.find((function(e){return e.TaskID==t})),window.scrollTo(0,0)}},mounted:function(){this.loadData()},computed:{staff_id:function(){return this.$store.state.login.user.staff_id},tasks:function(){return this.$store.state.om.mytasks},filteredTasks:function(){return this.tasks},isCompleted:function(){return this.monitor.initialTotal==this.monitor.totalCompleted},TaskID:function(){return this.$route.params.task_id}},methods:{noop:function(){},getProjectName:function(t){return this.$store.state.om.projects[t].name},viewTask:function(t){this.task=t},submitBlockage:function(){var t=this;this.$validator.validateAll().then((function(e){e&&(t.showBlock=!1,alert("blockage is just a comment and status updater!"))}))},blocked:function(){this.showBlock=!0},completeTask:function(){var t=this;this.isLoading=!0,this.$validator.validateAll().then((function(e){e?t.$store.dispatch("om.tasks.update",{data:t.selectedTask,task_id:t.selectedTask.task_id}).then((function(e){t.$store.dispatch("om.processors.mytasks"),t.monitor.totalCompleted++,t.isCompleted?t.$router.push("/tasks"):t.nextTask()}))["catch"]((function(t){})).then((function(){t.isLoading=!1})):t.isLoading=!1}))},startTask:function(t){this.monitor.startTime=(0,n["default"])(),this.monitor.initialTotal=this.tasks.length,this.monitor.totalCompleted=0,this.monitor.idx=t,this.$router.push("/tasks/".concat(this.tasks[t].TaskID))},stopTask:function(){this.$router.push("/tasks")},nextTask:function(){this.monitor.idx++;var t=this.tasks.length;this.monitor.idx>=t&&(this.monitor.idx=0);var e=this.tasks[this.monitor.idx].TaskID;this.$router.push("/tasks/".concat(e))},prevTask:function(){this.monitor.idx--;var t=this.tasks.length;this.monitor.idx<0&&(this.monitor.idx=t-1);var e=this.tasks[this.monitor.idx].TaskID;this.$router.push("/tasks/".concat(e))},handleUpdate:function(t){var e=this;this.submitting=!0,this.$store.dispatch("om.tasks.update",{data:t,task_id:this.selectedTask.TaskID}).then((function(t){}))["catch"]((function(t){})).then((function(){e.submitting=!1}))},loadData:function(){var t=this;this.$store.dispatch("om.processors.mytasks").then((function(){t.$store.dispatch("isLoaded")})),this.$store.dispatch("om.processors.list").then((function(e){t.users=e})),this.TaskID&&this.$router.push("/tasks")},getProcessorName:function(t){return c.pipe(c.find((function(e){return e.staff_id==t})),(function(t){return(t||{}).name}))(this.users)},moreInfo:function(t){this.$router.push("/tasks/".concat(t.task_id))},copyToCb:function(t){this.$copyText(t).then((function(t){alert("Copied"),console.log(t)}),(function(t){alert("Can not copy"),console.log(t)}))}},filters:{diffNow:function(t){return(0,n["default"])(t).fromNow()},shortKey:function(t){return c.take(12,t)},formatDate:function(t){return(0,n["default"])(t).format("DD MMM YYYY")},formatTime:function(t){return(0,n["default"])(t).format("hh:mm A")}}};e["default"]=h,t.exports=e.default,t.exports.default=e.default},"8d85":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[t.showBlockage?s("blockage",{attrs:{selectedTask:t.task},on:{close:function(e){t.showBlockage=!1},blocked:t.onblocked}}):t._e(),t.showCancellation?s("cancellation",{attrs:{selectedTask:t.task,task:t.task},on:{close:function(e){t.showCancellation=!1},cancelled:t.oncancelled}}):t._e(),t.showPatchFeedback?s("patch-feedback",{attrs:{selectedTask:t.task,task:t.task},on:{close:function(e){t.showPatchFeedback=!1},onpatched:t.onpatched}}):t._e(),s("div",{staticClass:"quickview",class:{"is-active":0!=t.task}},[s("header",{staticClass:"quickview-header"},[s("p",{staticClass:"title"},[t._v("Quickview Task")]),s("span",{staticClass:"delete",attrs:{"data-dismiss":"quickview"},on:{click:function(e){return t.$emit("close")}}})]),s("div",{staticClass:"quickview-body"},[s("div",{staticClass:"tabs is-boxed is-fullwidth"},[s("ul",[s("li",{class:{"is-active":0==t.activeTab}},[s("a",{on:{click:function(e){t.activeTab=0}}},[t._v("Summary")])]),s("li",{class:{"is-active":1==t.activeTab}},[s("a",{on:{click:function(e){t.activeTab=1}}},[t._v("Comments   "),s("span",{staticClass:"tag is-dark"},[t._v(t._s(t.comments.length))])])])])]),0==t.activeTab?s("div",{staticClass:"quickview-block"},[s("div",{staticClass:"card"},[s("div",{staticClass:"column"},[s("network-diagram-upload",{attrs:{resource:t.task}})],1)]),t._m(0),t._l(t.feedback||{},(function(e,a){return s("div",{key:"update."+a,staticClass:"card"},[s("div",{staticClass:"card-content"},[s("span",{staticClass:"heading"},[t._v(t._s(a))]),s("p",[t._v(t._s(t.mapValue(a,e)))])])])})),t.task.feedback?t._e():s("div",{staticClass:"card"},[t._m(1)]),t._m(2),t._l(t.details,(function(e,a){return s("div",{key:a,staticClass:"card"},[s("div",{staticClass:"card-content"},[s("span",{staticClass:"heading"},[t._v(t._s(a))]),s("p",[t._v(t._s(t.mapValue(a,e)))])])])})),s("feature-flag",{attrs:{scope:"developer",noDev:!0}},[s("pre",[t._v(t._s({PK:t.task.PK,SK:t.task.SK}))])])],2):t._e(),1==t.activeTab?s("div",{staticClass:"quickview-block"},[t.loading?s("div",{staticClass:"level"},[t._m(3)]):s("div",[0==t.comments.length?s("div",[t._m(4)]):t._e(),s("div",{staticClass:"card-content"},t._l(t.comments,(function(e){return s("div",{key:e.SK,staticClass:"card animated fadeInRight faster has-ribbon",staticStyle:{"margin-bottom":"20px"}},[t.isBlocked(e)?s("div",{staticClass:"ribbon is-danger is-small"},[t._v(t._s(e.details.comment_type))]):t._e(),s("div",{staticClass:"card-content"},[s("div",{staticClass:"pre-formatted"},[t._v(t._s(e.details.message))]),s("div",{staticClass:"is-size-7 has-text-grey",staticStyle:{"margin-top":"20px"}},[t._v("Posted by "),s("span",{staticClass:"has-text-weight-bold"},[t._v(t._s(e.CreatedBy.name)+" ")]),s("span",{staticClass:"tooltip",attrs:{"data-tooltip":t._f("dateFormat")(e.createdAt)}},[t._v(t._s(t._f("diffNow")(e.createdAt)))])])])])})),0)])]):t._e()]),1==t.activeTab?s("footer",{staticClass:"quickview-footer "},[s("div",{staticClass:"column"},[t.showCommentForm?s("div",{},[s("div",{staticClass:"level"},[t._m(5),s("div",{staticClass:"level-right"},[s("a",{staticClass:"delete ",on:{click:function(e){t.showCommentForm=!1}}})])]),s("div",{staticClass:"field"},[s("div",{staticClass:"control"},[s("textarea",{directives:[{name:"model",rawName:"v-model",value:t.comment,expression:"comment"}],ref:"comment",staticClass:"textarea",attrs:{name:"comment",placeholder:"Comment here"},domProps:{value:t.comment},on:{input:function(e){e.target.composing||(t.comment=e.target.value)}}})])]),s("div",{staticClass:"field"},[s("div",{staticClass:"control"},[t.isSaving?s("button",{staticClass:"button is-fullwidth is-primary is-loading"},[t._v("Post comment")]):s("button",{staticClass:"button is-fullwidth is-primary",on:{click:t.postComment}},[t._v("Post comment")])])])]):s("div",[s("a",{staticClass:"button is-text",on:{click:function(e){t.showCommentForm=!0}}},[t._v("Add comment")])])])]):t._e(),0==t.activeTab?s("footer",{staticClass:"quickview-footer "},[s("div",[t.hasAction?s("div",{staticClass:"dropdown is-up",class:{"is-active":t.showActions}},[s("div",{staticClass:"dropdown-trigger"},[s("button",{staticClass:"button",attrs:{"aria-haspopup":"true","aria-controls":"dropdown-menu"},on:{click:function(e){t.showActions=!t.showActions}}},[s("span",[t._v("Quick action")])])]),s("div",{staticClass:"dropdown-menu",attrs:{id:"dropdown-menu",role:"menu"},on:{click:function(e){t.showActions=!1}}},[s("div",{staticClass:"dropdown-content"},[t.canBlock?s("a",{staticClass:"dropdown-item",on:{click:t.markAsBlocked}},[t._v("\n                                Mark as blocked\n                            ")]):t._e(),t.canCancel?s("a",{staticClass:"dropdown-item",on:{click:function(e){t.showCancellation=!0}}},[t._v("\n                                Sales request to cancel\n                            ")]):t._e(),t.canReraise?s("a",{staticClass:"dropdown-item",on:{click:t.handleShowReraise}},[t._v("\n                                Reraise Order\n                            ")]):t._e(),t.canUpdateFeedback?s("a",{staticClass:"dropdown-item",on:{click:function(e){t.showPatchFeedback=!0}}},[t._v("Patch feedback")]):t._e()])])]):t._e()])]):t._e()])],1)},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card"},[s("div",{staticClass:"card-header"},[s("p",{staticClass:"card-header-title"},[t._v("PROCESSOR UPDATES")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card-content has-text-grey-light"},[s("i",{staticStyle:{}},[t._v("No feedback yet")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card"},[s("div",{staticClass:"card-header"},[s("p",{staticClass:"card-header-title"},[t._v("TASK DETAILS")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"level-item"},[s("span",{staticClass:"button is-text is-loading"})])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"level"},[s("div",{staticClass:"level-item",staticStyle:{"padding-top":"20%"}},[s("span",{staticClass:"title has-text-grey-lighter"},[t._v("No comment yet")])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"level-left"},[s("span",{staticClass:"title is-6"},[t._v("Add comment")])])}]},"8e92":function(t,e,s){"use strict";var a=s("dbce"),i=s("4ea4");s("99af"),s("b0c0"),s("ac1f"),s("5319"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0,s("96cf");var n=i(s("1da1")),o=a(s("b17e")),r=i(s("1146")),l={props:["data","attr","raw"],computed:{users:function(){return o.indexBy(o.prop("staff_id"),this.$store.state.om.processors)}},methods:{handleExport:function(){var t=this;return(0,n["default"])(regeneratorRuntime.mark((function e(){var s,a,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.$store.dispatch("loading"),!t.raw){e.next=3;break}return e.abrupt("return",t.exportExcel(t.raw));case 3:return s=function(t,e){return o.view(o.lensPath(["feedback",t]),e)||o.view(o.lensPath(["details",t]),e)||e[t]},e.next=6,t.$store.dispatch("om.comments.latest",o.map((function(t){return"".concat(t.PK,".").concat(t.SK)}),t.data)).then(o.filter((function(t){return t}))).then(o.indexBy((function(t){return t.PK.replace(/([0-9a-zA-Z]+\.)/,"")})));case 6:a=e.sent,i=o.pipe(o.map((function(e){return{task_id:o.take(12,e.GS2),processor:t.getProcessorName(e.GS1),customer:e.details["Customer Name"],order_type:e.details["Order Type"],ref_no:e.details["Ref No"],qoute_no:s("qoute_no",e),qoute_name:s("Quote Name",e),order_no:s("order_no",e),service_id:s("service_id",e),site_name:e.details["Site ID/Site Name"],sphere_id:s("Sphere ID",e),rfs:s("RFS Date",e),rft:s("RFT Date",e),billing_no:s("billing_no",e),status:e.status,latest_comment:a[e.SK]?a[e.SK].details.message:""}})))(t.data),t.exportExcel(i);case 9:case"end":return e.stop()}}),e)})))()},exportExcel:function(t){var e=r["default"].utils.json_to_sheet(t),s=r["default"].utils.book_new();r["default"].utils.book_append_sheet(s,e,"Export"),r["default"].writeFile(s,"export.xlsx"),this.$store.dispatch("isLoaded")},getProcessorName:function(t){return(this.users[t]||{name:t}).name}}};e["default"]=l,t.exports=e.default,t.exports.default=e.default},"8eea":function(t,e,s){"use strict";s.r(e);var a=s("e411"),i=s("bd2b");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"8f18":function(t,e,s){},"8fc3":function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var a={props:["file"],data:function(){return{showModal:!1,reason:""}},methods:{handleReject:function(){var t=this;this.$validator.validateAll().then((function(e){e&&(t.$emit("reject",{drop_id:t.file.drop_id,reason:t.reason}),t.showModal=!1)}))}}};e["default"]=a,t.exports=e.default,t.exports.default=e.default},9164:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticStyle:{height:"150px"}},[s("div",{staticClass:"navbar is-fixed-bottom animated slideInUp faster",staticStyle:{"border-top":"solid 1px #ccc","z-index":"36"}},[s("div",{staticClass:"container"},[s("div",{staticClass:"column"},[s("div",{staticClass:"level"},[s("div",{staticClass:"level-left"},[s("div",{staticClass:"level-item"},[s("div",[s("b",[t._v(t._s(t.total_selected))]),t._v(" items selected")])]),s("div",{staticClass:"level-item"},[s("div",{staticClass:"buttons"},[s("a",{staticClass:"button is-text is-small",on:{click:t.selectAll}},[t._v("Select All")]),s("a",{staticClass:"button is-text is-small",on:{click:t.clearSelection}},[t._v("Clear all")])])])]),s("div",{staticClass:"level-right"},[s("div",{staticClass:"level-item"},[s("a",{staticClass:"button is-text",on:{click:function(e){t.showWorkload=!0}}},[t._v("Workload distribution")])]),s("div",{staticClass:"level-item"},[s("div",{staticClass:"select",attrs:{placeholder:"Select processor2"}},[s("select",{directives:[{name:"model",rawName:"v-model",value:t.processor_staff_id,expression:"processor_staff_id"}],attrs:{name:"processor",placeholder:"Select processor"},on:{change:function(e){var s=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.processor_staff_id=e.target.multiple?s:s[0]}}},[s("option",{attrs:{value:"",disabled:""}},[t._v("Select processor")]),t._l(t.activeProcessor,(function(e){return s("option",{key:e.staff_id,domProps:{value:e.staff_id}},[t._v(t._s(e.name))])}))],2)])]),s("div",{staticClass:"level-item"},[s("div",{staticClass:"buttons"},[0!=t.total_selected&&t.processor_staff_id?s("a",{staticClass:"button is-primary",class:{"is-loading":t.isLoading},on:{click:t.handleAssign}},[t._v("Assign")]):s("a",{staticClass:"button is-primary",attrs:{disabled:""}},[t._v("Assign")]),s("a",{staticClass:"button is-text",on:{click:function(e){return t.$emit("close")}}},[t._v("Cancel")])])])])])])])]),t.showWorkload?s("div",{staticClass:"modal is-active"},[s("div",{staticClass:"modal-background",on:{click:function(e){t.showWorkload=!1}}}),s("div",{staticClass:"modal-content"},[s("div",{staticClass:"card"},[s("div",{staticClass:"card-content"},[s("workload",{on:{selected:t.handleWorkloadSelected}})],1)])]),s("button",{staticClass:"modal-close is-large",attrs:{"aria-label":"close"},on:{click:function(e){t.showWorkload=!1}}})]):t._e()])},i=[]},"91e5":function(t,e,s){},9292:function(t,e,s){"use strict";var a=s("4ea4");s("b0c0"),s("ac1f"),s("5319"),s("841c"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("043a")),n={components:{RoleSwitcher:i["default"]},data:function(){return{showRoleSwitched:!1,search:""}},computed:{environment:function(){return console.log(Object({NODE_ENV:"production",BASE_URL:"/"}).STAGE),"staging"!=Object({NODE_ENV:"production",BASE_URL:"/"}).STAGE?"DEV":"PROD"},environmentClass:function(){return{"tag is-danger":"DEV"==this.environment,"tag is-primary":"PROD"==this.environment}},user:function(){return this.$store.state.login.user.name},userRoles:function(){return this.$store.state.om.currentUserRoles},currentActiveRole:function(){return this.$store.state.om.currentUserActiveRole||"Visitor"}},methods:{switchRole:function(t){this.$store.dispatch("om.roles.switch",t),this.$router.push("/")},handleSearch:function(){var t=this;this.$validator.validateAll().then((function(e){e&&(t.$router.push("/search?keyword=".concat(t.search)),t.search="")}))}},filters:{formatLabel:function(t){return(t||"").replace("_"," ")}}};e["default"]=n,t.exports=e.default,t.exports.default=e.default},9318:function(t,e,s){"use strict";s.r(e);var a=s("ed48"),i=s("b44b");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);s("d07e");var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},"93ef":function(t,e,s){},"947f":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"box task_box",class:{pointer:"UNASSIGNED"==t.task.Status,"task-selected":t.selectedTasks[t.task.task_id]},on:{click:function(e){return t.$emit("toggleSelectTask",t.task)}}},[s("div",{staticClass:"level"},[s("div",{staticClass:"level-start"},[s("div",{staticClass:"level-item"},[s("div",[s("div",{staticClass:"tags has-addons"},[s("span",{staticClass:"tag",class:{"is-info":"ASSIGNED"==t.task.Status,"is-success":"SUBMITTED"==t.task.Status||"COMPLETED"==t.task.Status}},[t._v(t._s(t.task.Status))]),"UNASSIGNED"!=t.task.GS1?s("span",{staticClass:"tag",on:{click:function(e){return t.handleUnassigned(t.task)}}},[t._v(t._s(t.getStaffName(t.task.GS1)))]):t._e()]),s("span",{staticClass:"title is-4"},[t._v(t._s(t.task.CustomerName))])])])]),s("div",{staticClass:"level-end"},[s("div",{staticClass:"level-item"},[s("div",{staticClass:"hidden"},[s("div",{staticClass:"field has-addons"},[s("p",{staticClass:"control"},[s("a",{staticClass:"button is-small",on:{click:function(e){return e.preventDefault(),e.stopPropagation(),t.moreInfo(t.task)}}},[s("span",[t._v("More info")])])])])])])])]),s("div",{staticClass:"level"},[s("div",{staticClass:"level-start"},[s("div",{staticClass:"level-item"},[s("div",{staticClass:"level-item"},[s("div",[s("div",{staticClass:"field is-grouped is-grouped-multiline"},[s("div",{staticClass:"control"},[s("span",{staticClass:"tags has-addons"},[s("span",{staticClass:"tag is-dark"},[t._v("SEGMENT/UNIT")]),s("span",{staticClass:"tag is-info"},[t._v(t._s(t.task["Segment/Unit"]))])])]),s("div",{staticClass:"control"},[s("span",{staticClass:"tags has-addons"},[s("span",{staticClass:"tag is-dark"},[t._v("Product/Service")]),s("span",{staticClass:"tag is-info"},[t._v(t._s(t.task["Product/Service"]||"-"))])])])]),s("span",{staticClass:"heading has-text-grey-light"},[t._v(t._s(t.task.TaskID))])])])])]),s("div",{staticClass:"level-end"},[s("div",{staticClass:"level-item"},[s("div",{staticClass:"level is-mobile"},[s("div",{staticClass:"level-item"},[s("div",{staticClass:"card",staticStyle:{width:"100%"}},[s("div",{staticStyle:{padding:"5px 10px"}},[s("span",{staticClass:"is-size-7 has-text-grey-light"},[t._v("Date")]),s("p",{staticClass:"has-text-weight-bold"},[t._v(t._s(t.task["Date"]||"-"))])])])]),s("div",{staticClass:"level-item"},[s("div",{staticClass:"card",staticStyle:{width:"100%"}},[s("div",{staticStyle:{padding:"5px 10px"}},[s("span",{staticClass:"is-size-7 has-text-grey-light"},[t._v("RFS/RFT")]),s("p",{staticClass:"has-text-weight-bold"},[t._v(t._s(t.task["RFS/RFT"]||"-"))])])])])])])])])])},i=[]},"94fc":function(t,e,s){"use strict";var a=s("dbce"),i=s("4ea4");s("d81d"),s("d3b7"),s("ddb0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("2909")),o=a(s("b17e")),r=i(s("1321")),l={components:{apexchart:r["default"]},methods:{randomize:function(t,e,s){var a=function(){return Math.round(Math.random()*(s-e))+e};return(0,n["default"])(Array(12).keys()).map((function(e){return e<t?a():0}))}},computed:{perf:function(){var t=o.pluck("data",this.series).map((function(t){return o.sum(t)})),e=o.sum(t);return t.map((function(t){return Math.round(t/e*100)}))},series:function(){return[{name:" < 1 day",data:this.randomize(6,10,30)},{name:" < 3 days",data:this.randomize(6,60,120)},{name:" > 3 days",data:this.randomize(6,0,10)}]}},data:function(){return{chartOptions:{colors:["#64CC6D","#4B9CE7","#EB4C64"],chart:{height:350,type:"bar",stacked:!0},plotOptions:{bar:{dataLabels:{position:"top"},endingShape:"rounded",distributed:!1,columnWidth:"20%"}},dataLabels:{enabled:!1,formatter:function(t){return t+"%"},offsetY:-20,style:{fontSize:"12px",colors:["#304758"]}},xaxis:{categories:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],position:"top",labels:{offsetY:-18},axisBorder:{show:!1},axisTicks:{show:!1},crosshairs:{fill:{type:"gradient",gradient:{colorFrom:"#D8E3F0",colorTo:"#BED1E6",stops:[0,100],opacityFrom:.4,opacityTo:.5}}},tooltip:{enabled:!0,offsetY:-35}},yaxis:{axisBorder:{show:!0},axisTicks:{show:!0},labels:{show:!0},title:{text:"Orders"}}}}}};e["default"]=l,t.exports=e.default,t.exports.default=e.default},9572:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"modal is-active"},[s("div",{staticClass:"modal-background"}),s("div",{staticClass:"modal-content"},[s("div",{staticClass:"card"},[s("div",{staticClass:"card-content"},[s("div",{staticClass:"field"},[s("div",{staticClass:"control"},[s("label",{staticClass:"label"},[t._v("Blockage type")]),s("input",{directives:[{name:"model",rawName:"v-model",value:t.blockage.blockage_type,expression:"blockage.blockage_type"},{name:"validate",rawName:"v-validate",value:{required:!0},expression:"{required:true}"}],attrs:{type:"hidden",name:"blockage_type"},domProps:{value:t.blockage.blockage_type},on:{input:function(e){e.target.composing||t.$set(t.blockage,"blockage_type",e.target.value)}}}),s("div",{staticClass:"select",class:{"is-danger":t.errors.has("blockage_type")}},[s("select",{directives:[{name:"model",rawName:"v-model",value:t.blockage.blockage_type,expression:"blockage.blockage_type"}],attrs:{placeholder:"select type"},on:{change:function(e){var s=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.$set(t.blockage,"blockage_type",e.target.multiple?s:s[0])}}},[s("option",{attrs:{value:"",di:""}},[t._v("Select")]),t._l(t.blockages,(function(e,a){return s("option",{key:a,domProps:{value:e}},[t._v(t._s(e))])}))],2)])]),s("div",{staticClass:"control"},[s("label",{staticClass:"label"},[t._v("Remarks")]),s("textarea",{directives:[{name:"validate",rawName:"v-validate",value:{required:!0},expression:"{required:true}"},{name:"model",rawName:"v-model",value:t.blockage.remarks,expression:"blockage.remarks"}],staticClass:"textarea",class:{"is-danger":t.errors.has("remarks")},attrs:{name:"remarks",placeholder:"Describe the blockage"},domProps:{value:t.blockage.remarks},on:{input:function(e){e.target.composing||t.$set(t.blockage,"remarks",e.target.value)}}}),s("span",{staticClass:"help"},[t._v("Good description will help future you and others understand the reason")])])])]),s("div",{staticClass:"card-content has-background-light"},[s("div",{staticClass:"level"},[s("div",{staticClass:"level-left"}),s("div",{staticClass:"level-right"},[s("div",{staticClass:"buttons"},[s("a",{staticClass:"button is-text ",on:{click:function(e){return t.$emit("close")}}},[t._v("Cancel")]),s("a",{staticClass:"button is-warning",on:{click:t.submitBlockage}},[t._v("UPDATE BLOCKAGE")])])])])])])])])},i=[]},"95e8":function(t,e,s){"use strict";s.r(e);var a=s("2c2b"),i=s("5522");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);s("4998");var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},9691:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("div",{staticClass:"column",staticStyle:{"border-bottom":"solid 1px #ccc"}},[s("div",{staticClass:"level"},[s("div",{staticClass:"level-start"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.filterKeyword,expression:"filterKeyword"}],staticClass:"input is-rounded",attrs:{type:"text",placeholder:"Filter"},domProps:{value:t.filterKeyword},on:{input:function(e){e.target.composing||(t.filterKeyword=e.target.value)}}})]),s("div",{staticClass:"level-end"},[s("div",{staticClass:"field is-grouped"},[s("div",{staticClass:"control"},[s("div",{staticClass:"field has-addons"},[s("div",{staticClass:"control"},[s("a",{staticClass:"button is-small",on:{click:function(e){return t.updateDiff(-1)}}},[t._v("«")])]),s("div",{staticClass:"control"},[s("div",{staticClass:"button is-small is-static"},[t._v(t._s(t.dateRangeDis))])]),s("div",{staticClass:"control"},[s("div",{staticClass:"select is-small"},[s("select",{directives:[{name:"model",rawName:"v-model",value:t.filterType,expression:"filterType"}],on:{change:function(e){var s=Array.prototype.filter.call(e.target.options,(function(t){return t.selected})).map((function(t){var e="_value"in t?t._value:t.value;return e}));t.filterType=e.target.multiple?s:s[0]}}},[s("option",{attrs:{value:"all"}},[t._v("All")]),s("option",{attrs:{value:"day"}},[t._v("Daily")]),s("option",{attrs:{value:"week"}},[t._v("Week")]),s("option",{attrs:{value:"month"}},[t._v("Month")])])])]),s("div",{staticClass:"control"},[s("a",{staticClass:"button is-small",on:{click:function(e){t.filterDiff=0}}},[t._v("Reset")])]),s("div",{staticClass:"control"},[s("a",{staticClass:"button is-small",attrs:{disabled:t.filterDiff>=0},on:{click:function(e){return t.updateDiff(1)}}},[t._v("»")])])])]),s("div",{staticClass:"control"},[s("sorter",{attrs:{data:t.filteredTasksByKeyword,sortfields:{updatedAt:"Updated At","details.TaskID":"Task ID","details.project.id":"Project","details.Customer Name":"Customer Name","details.dropfile.Region":"Region","CreatedBy.name":"Created by",PK:"Jobsheet",GS1:"Processor",Status:"Status"}},on:{sorted:t.onSorted}})],1),s("div",{staticClass:"control"},[s("export-excel",{attrs:{data:t.sortedTasks}})],1)])])])]),s("table",{staticClass:"table is-fullwidth is-hoverable"},[t._m(0),s("tbody",t._l(t.sortedTasks,(function(e,a){return s("tr",{key:a,staticClass:"is-clickable",class:{"is-selected":t.selectedSessionTasks[e.task_id],"is-viewed":t.isViewed(e)},on:{click:function(s){return t.viewTask(e)}}},[s("td",[s("span",{staticClass:"has-text-grey-lighter"},[t._v(t._s(a+1))])]),s("td",[s("p",{staticClass:"has-text-weight-semibold"},[t._v(t._s(t._f("shortKey")(e.TaskID)))]),s("span",{staticClass:"tag is-info"},[t._v(t._s(e.details.dropfile["Request Type"]))])]),s("td",[s("span",{},[t._v(t._s(t.getProjectName(e.details.project.id)))])]),s("td",[s("div",[s("span",{staticClass:"has-text-weight-semibold"},[t._v(t._s(e.details["Customer Name"]))]),s("br"),s("span",{staticClass:"heading"},[t._v("Name")])]),e.details.dropfile.Region?s("div",{staticClass:"headings"},[s("span",[t._v(t._s(e.details.dropfile.Region))]),s("span",{staticClass:"heading"},[t._v("Region")])]):t._e()]),s("td",[e.details.dropfile["Short Description on Request"]?s("div",[s("span",[t._v(t._s(e.details.dropfile["Short Description on Request"]))]),s("span",{staticClass:"heading"},[t._v("Description")])]):t._e(),e.details["Product/Requirement"]?s("div",{staticClass:"headings"},[s("span",[t._v(t._s(e.details["Product/Requirement"]))]),s("span",{staticClass:"heading"},[t._v("Product/Requirement")])]):t._e(),e.details["Request Type"]?s("div",{staticClass:"headings"},[s("span",[t._v(t._s(e.details["Request Type"]))]),s("span",{staticClass:"heading"},[t._v("Request Type")])]):t._e(),e.details["Sub-Order Type (if any)"]?s("div",{staticClass:"headings"},[s("span",[t._v(t._s(e.details["Sub-Order Type (if any)"]))]),s("span",{staticClass:"heading"},[t._v("Sub-Order Type")])]):t._e()]),s("td",[e.details.dropfile["Sales Ref Number"]?s("div",{staticClass:"headings"},[s("span",[t._v(t._s(e.details.dropfile["Sales Ref Number"]))]),s("span",{staticClass:"heading"},[t._v("Sales Ref No.")])]):t._e(),e.details.dropfile["BCS Req ID (if applicable)"]?s("div",{staticClass:"headings"},[s("span",[t._v(t._s(e.details.dropfile["BCS Req ID (if applicable)"]))]),s("span",{staticClass:"heading"},[t._v("BCS Request No.")])]):t._e(),t.odNumber(e)?s("div",[s("a",{attrs:{href:t.odLink(e),target:"_blank",rel:"noopener"}},[t._v(t._s(t.odNumber(e)))]),s("span",{staticClass:"heading"},[t._v("OD No.")])]):t._e()]),s("td",[s("div",{staticClass:"headings"},[s("span",[t._v(t._s(t._f("shortKey")(e.PK)))]),s("span",{staticClass:"heading"},[t._v("Jobsheet ID")])]),s("div",{staticClass:"headings"},[s("span",[t._v(t._s(e.CreatedBy.name))]),s("span",{staticClass:"heading"},[t._v("Created By")])]),s("div",{staticClass:"headings"},[s("span",[t._v(t._s(t._f("diffNow")(e.createdAt)))]),s("span",{staticClass:"heading"},[t._v(t._s(t._f("formatDate")(e.createdAt)))])])]),s("td",{staticClass:"has-text-right"},[s("task-status",{attrs:{task:e}}),"UNASSIGNED"!=e.GS1?s("div",[s("span",{staticClass:"help"},[t._v(t._s(t.getProcessorName(e.GS1)))]),s("span",{staticClass:"heading"},[t._v("Assigned To")])]):t._e()],1)])})),0)])])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("thead",[s("tr",[s("th",{attrs:{width:"2"}},[t._v("#")]),s("th",[t._v("Task")]),s("th",[t._v("Project")]),s("th",[t._v("Customer")]),s("th",[t._v("Request")]),s("th",[t._v("Ref")]),s("th",[t._v("Jobsheet")]),s("th",{staticClass:"has-text-right"},[t._v("Status")])])])}]},"96b0":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"container",staticStyle:{"padding-top":"40px"}},[s("div",{staticClass:"columns is-multiline"},[s("div",{staticClass:"column is-12"},[s("div",{staticClass:"level"},[t._m(0),s("div",{staticClass:"level-end"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.filterKeyword,expression:"filterKeyword"}],staticClass:"input is-rounded",attrs:{type:"text",placeholder:"Filter..."},domProps:{value:t.filterKeyword},on:{input:function(e){e.target.composing||(t.filterKeyword=e.target.value)}}})])])]),s("div",{staticClass:"column is-12"},[s("div",{staticClass:"tabs is-toggle is-toggle-rounded "},[s("ul",[s("li",{class:{"is-active":t.isActive("OPEN")}},[s("a",{on:{click:function(e){return t.setActive("OPEN")}}},[t._v("Open"),s("span",{staticClass:" is-pulled-right tag is-dark has-background-grey-darker",staticStyle:{"margin-left":"5px"}},[s("animated-number",{attrs:{value:t.stats.OPEN,round:!0}})],1)])]),s("li",{class:{"is-active":t.isActive("ON_HOLD"),"is-alert":t.hasOverdueHolds}},[s("a",{on:{click:function(e){return t.setActive("ON_HOLD")}}},[t.hasOverdueHolds?s("i",{staticClass:"icon is-paddingless"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}},[s("path",{attrs:{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}}),s("line",{attrs:{x1:"12",y1:"9",x2:"12",y2:"13"}}),s("line",{attrs:{x1:"12",y1:"17",x2:"12",y2:"17"}})])]):t._e(),t._v("\n                            On hold\n                            "),s("span",{staticClass:" is-pulled-right tag is-dark has-background-grey-darker",staticStyle:{"margin-left":"5px"}},[s("animated-number",{attrs:{value:t.stats.ON_HOLD,round:!0}})],1)])]),s("li",{class:{"is-active":t.isActive("UPLOADED")}},[s("a",{on:{click:function(e){return t.setActive("UPLOADED")}}},[t._v("Created "),s("span",{staticClass:" is-pulled-right tag is-dark has-background-grey-darker",staticStyle:{"margin-left":"5px"}},[s("animated-number",{attrs:{value:t.stats.UPLOADED,round:!0}})],1)])]),s("li",{class:{"is-active":t.isActive("REJECTED")}},[s("a",{on:{click:function(e){return t.setActive("REJECTED")}}},[t._v("Rejected "),s("span",{staticClass:" is-pulled-right tag is-dark has-background-grey-darker",staticStyle:{"margin-left":"5px"}},[s("animated-number",{attrs:{value:t.stats.REJECTED,round:!0}})],1)])]),s("li",{class:{"is-active":t.isActive("TASK_COMPLETED")}},[s("a",{on:{click:function(e){return t.setActive("TASK_COMPLETED")}}},[t._v("Completed "),s("span",{staticClass:" is-pulled-right tag is-dark has-background-grey-darker",staticStyle:{"margin-left":"5px"}},[s("animated-number",{attrs:{value:t.stats.TASK_COMPLETED,round:!0}})],1)])])]),t.lastKey?s("ul",{staticClass:"is-right is-small"},[s("li",{staticClass:"is-small"},[s("a",{staticClass:"is-small button",class:{"is-loading":t.isLoading},on:{click:t.loadMore}},[t._v("Load more")])])]):t._e()])]),s("div",{staticClass:"column is-12"},[s("div",{staticClass:"card"},[t.nofile?s("div",{staticClass:"card-content"},[t._v("\n                    You have no order request yet\n                ")]):t._e(),t.nofile?t._e():s("table",{staticClass:"table is-fullwidth is-hoverable"},[s("thead",[s("tr",[s("td",{attrs:{colspan:"6"}},[s("div",{staticClass:"level"},[s("div",{staticClass:"level-start"}),s("div",{staticClass:"level-end"},[s("sorter",{attrs:{data:t.filteredFiles,sortfields:{"details.filename":"filename","CreatedBy.name":"Created by",Status:"Status",createdAt:"Uploaded date","details.on_hold.duedate":"Due date"},defaultSort:"createdAt"},on:{sorted:t.onSorted}})],1)])])]),t._m(1)]),s("tbody",t._l(t.sortedData,(function(e,a){return s("tr",{key:a},[s("td",{staticClass:"has-text-grey-lighter has-text-centered",staticStyle:{"padding-right":"0"}},[t._v(t._s(a+1))]),s("td",[s("span",{staticClass:"is-pulled-right"},[s("debug-tool",{attrs:{content:{PK:e.drop_id}}})],1),s("a",{on:{click:function(s){return t.downloadFile(e)}}},[t._v(t._s(e.details.subject)+" ")]),s("span",{staticClass:"heading"},[t._v(t._s(t._f("shortKey")(e.drop_id)))])]),s("td",[s("span",[t._v(t._s(e.CreatedBy.name))]),s("span",{staticClass:"heading"},[t._v(t._s(e.CreatedBy.staff_id))])]),s("td",[s("span",{staticClass:"tooltip",attrs:{"data-tooltip":t._f("formatDate")(e.createdAt)}},[t._v(t._s(t._f("fromNow")(e.createdAt)))])]),s("td",[s("drop-status",{attrs:{file:e}})],1),s("td",{staticClass:"has-text-right"},["UPLOADED"==e.Status||"TASK_COMPLETED"==e.Status?s("router-link",{staticClass:"button is-small",attrs:{to:t.jumpToJobsheet(e)}},[t._v("View »")]):s("div",["OPEN"==e.Status?s("reject-file",{staticClass:"dropdown-item",attrs:{file:e},on:{reject:t.handleReject}}):t._e(),"OPEN"==e.Status?s("hold-file",{staticClass:"dropdown-item",attrs:{file:e},on:{onHold:t.handleOnHold}}):t._e()],1),"ON_HOLD"==e.Status?s("div",[s("a",{staticClass:"button is-small",on:{click:function(s){return t.releaseHold(e)}}},[t._v("Release hold")]),s("a",{staticClass:"button is-small",staticStyle:{"margin-top":"4px"},on:{click:function(s){return t.toggleModal(e.drop_id)}}},[t._v("Revise hold date")])]):t._e()],1)])})),0)])])])]),t.showModal?s("div",{staticClass:"modal is-active"},[s("div",{staticClass:"modal-background"}),s("div",{staticClass:"modal-content"},[s("div",{staticClass:"card"},[s("div",{staticClass:"card-content"},[s("form",{on:{submit:function(e){return e.preventDefault(),t.submitRevise(e)}}},[s("div",{staticClass:"columns  has-text-left"},[s("div",{staticClass:"column"},[s("div",{staticClass:"field"},[s("label",{staticClass:"label"},[t._v("Revise on hold date until: "+t._s(t.calendarData.selectedDate))]),s("div",{staticClass:"control card calendar",class:{"is-danger":t.errors.has("duedate")}},[s("FunctionalCalendar",{directives:[{name:"validate",rawName:"v-validate",value:"required",expression:"'required'"}],ref:"Calendar",attrs:{name:"duedate",disabledDates:["beforeToday"],sundayStart:!0,"date-format":"dd/mm/yyyy","is-date-picker":!0,transition:!0,isModal:!1},model:{value:t.calendarData,callback:function(e){t.calendarData=e},expression:"calendarData"}})],1)])]),s("div",{staticClass:"column"},[s("div",{staticClass:"field"},[s("label",{staticClass:"label"},[t._v("Reason")]),s("div",{staticClass:"control"},[s("textarea",{directives:[{name:"model",rawName:"v-model",value:t.reason,expression:"reason"},{name:"validate",rawName:"v-validate",value:"required",expression:"'required'"}],staticClass:"textarea",class:{"is-danger":t.errors.has("reason")},attrs:{name:"reason",rows:"10",placeholder:"Explain why you put this request on hold"},domProps:{value:t.reason},on:{input:function(e){e.target.composing||(t.reason=e.target.value)}}})])])])]),s("div",{staticClass:"field"},[s("div",{staticClass:"control"},[s("div",{staticClass:"buttons"},[s("button",{staticClass:"button is-dark",attrs:{type:"submit"}},[t._v("Revise")]),s("a",{staticClass:"button",on:{click:function(e){t.showModal=t.showCal=!1}}},[t._v("Cancel")])])])])])])])]),s("button",{staticClass:"modal-close is-large",attrs:{"aria-label":"close"},on:{click:function(e){t.showModal=!1}}})]):t._e()])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"level-start"},[s("h1",{staticClass:"title is-5"},[t._v("Dropfiles")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("tr",[s("td",{attrs:{colspan:"2"}},[t._v("Drop Request")]),s("td",[t._v("Requestor")]),s("td",[t._v("Created date")]),s("td",[t._v("Status")]),s("td")])}]},9984:function(t,e,s){"use strict";var a=s("bc7d"),i=s.n(a);i.a},"99bc":function(t,e,s){"use strict";var a=s("dbce");s("99af"),s("4160"),s("c975"),s("b0c0"),s("159b"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("b17e")),n={data:function(){return{filterKeyword:"",workload:{},asc:!0}},computed:{processors:function(){return this.$store.state.om.processors},sortedWorkload:function(){var t=this,e=this.asc?i.ascend:i.descend;return i.pipe(i.map((function(e){var s=!!t.workload[e.staff_id],a=t.workload[e.staff_id]||{},i=a.BLOCKED||0,n=(a.ASSIGNED||0)+(a.ASSIGNED_RERAISE||0);return{name:e.name,staff_id:e.staff_id,blocked:i,assigned:n,total:i+n,loaded:s}})),i.sort(e(i.prop("assigned"))),i.filter((function(e){return JSON.stringify(e).indexOf(t.filterKeyword)>-1})))(this.processors)}},mounted:function(){this.loadData()},methods:{getTooltip:function(t){return"Assigned: ".concat(t.assigned," | Blocked: ").concat(t.blocked)},loadData:function(){var t=this;return this.$store.dispatch("om.processors.list").then((function(){t.processors.forEach((function(e){t.$store.dispatch("om.processors.workload",e.staff_id).then((function(s){var a=i.pipe(i.groupBy(i.prop("Status")),i.map((function(t){return t.length})))(s);t.$set(t.workload,e.staff_id,a)}))}))}))}}};e["default"]=n,t.exports=e.default,t.exports.default=e.default},"99fe":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card-content"},[s("div",[s("div",{staticClass:"level",class:{"is-quickview":t.task}},[t._m(0),s("div",[s("div",{staticClass:"field is-grouped"},[s("div",{staticClass:"control"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.everyone,expression:"everyone"}],staticClass:"switch is-rounded  is-rtl",attrs:{id:"switchRoundedDefault2",type:"checkbox",name:"switchRoundedDefault2"},domProps:{checked:Array.isArray(t.everyone)?t._i(t.everyone,null)>-1:t.everyone},on:{change:function(e){var s=t.everyone,a=e.target,i=!!a.checked;if(Array.isArray(s)){var n=null,o=t._i(s,n);a.checked?o<0&&(t.everyone=s.concat([n])):o>-1&&(t.everyone=s.slice(0,o).concat(s.slice(o+1)))}else t.everyone=i}}}),s("label",{staticClass:"is-size-6",attrs:{for:"switchRoundedDefault2"}},[t._v("Everyone task")])]),s("div",{staticClass:"control"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.showBlocked,expression:"showBlocked"}],staticClass:"switch is-rounded  is-rtl",attrs:{id:"switchRoundedDefault3",type:"checkbox",name:"switchRoundedDefault3"},domProps:{checked:Array.isArray(t.showBlocked)?t._i(t.showBlocked,null)>-1:t.showBlocked},on:{change:function(e){var s=t.showBlocked,a=e.target,i=!!a.checked;if(Array.isArray(s)){var n=null,o=t._i(s,n);a.checked?o<0&&(t.showBlocked=s.concat([n])):o>-1&&(t.showBlocked=s.slice(0,o).concat(s.slice(o+1)))}else t.showBlocked=i}}}),s("label",{staticClass:"is-size-6",attrs:{for:"switchRoundedDefault3"}},[t._v("Blocked task")])]),s("div",{staticClass:"control"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.assignment_mode,expression:"assignment_mode"}],staticClass:"switch is-rounded  is-rtl",attrs:{id:"switchRoundedDefault",type:"checkbox",name:"switchRoundedDefault"},domProps:{checked:Array.isArray(t.assignment_mode)?t._i(t.assignment_mode,null)>-1:t.assignment_mode},on:{change:function(e){var s=t.assignment_mode,a=e.target,i=!!a.checked;if(Array.isArray(s)){var n=null,o=t._i(s,n);a.checked?o<0&&(t.assignment_mode=s.concat([n])):o>-1&&(t.assignment_mode=s.slice(0,o).concat(s.slice(o+1)))}else t.assignment_mode=i}}}),s("label",{staticClass:"is-size-6",attrs:{for:"switchRoundedDefault"}},[t._v("Assign task")])])])])]),s("div",{staticClass:"columns",class:{"is-quickview":t.task}},[s("div",{staticClass:"column"},[t.filteredTasks.length>0?s("div",{staticClass:"card"},[s("table-tasks",{attrs:{defaultFilterType:"all",tasks:t.filteredTasks,isSelected:t.isSelected,processors:t.processorsMapping,task:t.task},on:{taskSelected:t.viewTask}})],1):s("div",{staticClass:"columns is-centered"},[s("div",{staticClass:"column is-6 has-text-centered has-text-grey-lighter content ",staticStyle:{padding:"40px"}},[s("p",{staticClass:"animated zoomIn faster"},[s("svg",{staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 503.635 503.635",width:"160"}},[s("path",{attrs:{d:"M503.635 6.284l-150.846-.635-70.939 150.108c-9.755-1.774-19.744-2.858-29.994-2.858-10.273 0-20.286 1.087-30.062 2.868L150.837 5.649 0 6.284l107.797 228.103c-16.284 26.469-25.855 57.615-25.855 91.06 0 95.135 76.221 172.538 169.913 172.538s169.914-77.404 169.914-172.538c0-33.478-9.59-64.654-25.904-91.139L503.635 6.284zM365.231 25.399l107.385.442L382.873 215.7c-20.981-25.786-49.202-45.257-81.453-55.267l63.811-135.034zm-334.212.442l107.375-.442 63.825 135.052c-32.249 10.025-60.464 29.513-81.436 55.311L31.019 25.841zm371.058 299.606c0 84.279-67.385 152.846-150.221 152.846s-150.221-68.567-150.221-152.846c0-84.288 67.385-152.856 150.221-152.856s150.221 68.568 150.221 152.856z"}}),s("path",{attrs:{d:"M359.904 294.659l-72.519-13.827-35.529-66.173-35.519 66.173-72.529 13.827 50.663 54.49-9.5 74.587 66.885-32.24 66.885 32.24-9.5-74.587 50.663-54.49zM251.856 369.63l-42.769 20.625 6.077-47.76-32.683-35.164 46.76-8.923 22.615-42.115 22.625 42.115 46.75 8.923-32.683 35.164 6.077 47.76-42.769-20.625z"}})])]),s("p",{staticClass:"subtitle has-text-grey-light is-3 animated fadeInUp faster",staticStyle:{"animation-delay":"0.2s"}},[t._v("No pending task for you :)")])])])]),s("quickview-task",{attrs:{task:t.task},on:{close:function(e){t.task=!1},blocked:t.loadData}})],1)]),t.assignment_mode?s("div",{staticStyle:{height:"150px"}},[s("task-assignment",{attrs:{processors:t.processors,isLoading:t.isLoading,total_selected:t.total_selected,selectAll:t.selectAll,clearSelection:t.clearSelection},on:{assign:t.handleAssign,close:function(e){t.assignment_mode=!1}}})],1):t._e()])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("h1",{staticClass:"title is-5 has-text-grey"},[t._v("Unassigned Tasks")])])}]},"9b92":function(t,e,s){"use strict";var a=s("dbce");Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("b17e")),n=s("fd90"),o={props:["task"],mixins:[n],data:function(){return{inViewport:{now:!1},loaded:!1}},mounted:function(){},watch:{"inViewport.now":function(t){t&&!this.loaded&&(this.loadData(),this.loaded=!0)}},computed:{attachment:function(){var t=i.lensPath(["om","attachments","network-diagram",this.task.SK,0]);return i.view(t,this.$store.state)},filename:function(){return this.attachment?this.attachment.details.filename:""}},methods:{loadData:function(){this.$store.dispatch("om.attachments.get",{resource:this.task,attachment_type:"network-diagram"})}}};e["default"]=o,t.exports=e.default,t.exports.default=e.default},"9bf6":function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card-content"},[t.project?s("div",[s("div",{staticClass:"level"},[s("div",[s("h1",{staticClass:"title is-5 has-text-grey"},[t._v(t._s(t.project.name)+" Project Overview")])]),s("div")]),s("div",{staticClass:"columns"},[s("div",{staticClass:"column"},[t.jobsheets&&t.jobsheets.length>0?s("div",{staticClass:"card"},[s("table-jobsheets",{attrs:{jobsheets:t.jobsheets}})],1):s("div",{staticClass:"columns is-centered"},[s("div",{staticClass:"column is-6 has-text-centered has-text-grey-lighter content",staticStyle:{padding:"40px"}},[s("p",[s("svg",{staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 492.308 492.308",width:"160"}},[s("path",{attrs:{d:"M316.038 254.298l-14.442-13.385-55.437 59.799-55.438-59.799-14.442 13.385 56.454 60.894-56.454 60.895 14.442 13.384 55.438-59.798 55.437 59.798 14.442-13.384-56.454-60.895z"}}),s("path",{attrs:{d:"M242.173 63.731L181.452 0H0v492.308h492.308V63.731H242.173zm230.442 408.884H19.692v-312.23h452.923v312.23zm0-331.923H19.692v-121H173.01l60.721 63.731h238.885v57.269z"}})])]),s("p",{staticClass:"subtitle has-text-grey-light is-3"},[t._v("No jobsheet uploaded yet in this project")])])])])])]):t._e()])},i=[]},"9ed3":function(t,e,s){"use strict";s.r(e);var a=s("56da"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},a0a7:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card-content"},[t.jobsheet&&t.project?s("div",[s("div",{staticClass:"level",class:{"is-quickview":t.task}},[s("div",[s("router-link",{staticClass:"heading",attrs:{to:"/tasks/projects/"+t.project.PK}},[t._v(" « "+t._s(t.project.name)+" Project")]),s("h1",{staticClass:"title is-5 has-text-grey"},[t._v(t._s(t._f("shortKey")(t.jobsheet.PK)))]),s("span",{staticClass:"heading"},[t._v(t._s(t._f("formatDate")(t.jobsheet.createdAt)))])],1),s("div",[s("div",{staticClass:"field"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.show_all,expression:"show_all"}],staticClass:"switch is-rounded  is-rtl is-small",attrs:{id:"switchRoundedDefault",type:"checkbox",name:"switchRoundedDefault"},domProps:{checked:Array.isArray(t.show_all)?t._i(t.show_all,null)>-1:t.show_all},on:{change:function(e){var s=t.show_all,a=e.target,i=!!a.checked;if(Array.isArray(s)){var n=null,o=t._i(s,n);a.checked?o<0&&(t.show_all=s.concat([n])):o>-1&&(t.show_all=s.slice(0,o).concat(s.slice(o+1)))}else t.show_all=i}}}),s("label",{staticClass:"is-size-7",attrs:{for:"switchRoundedDefault"}},[t._v("Show all tasks")])])])]),s("div",{staticClass:"tabs is-boxed"},[s("ul",[s("li",{class:{"is-active":"tasks"==t.tab}},[s("a",{on:{click:function(e){t.tab="tasks"}}},[s("span",[t._v("Tasks")])])]),s("li",{class:{"is-active":"distributions"==t.tab}},[s("a",{on:{click:function(e){t.tab="distributions"}}},[s("span",[t._v("All Distributions")])])])])]),s("div",{staticClass:"columns",class:{"is-quickview":t.task}},[s("div",{staticClass:"column"},["distributions"==t.tab?s("div",[s("div",{staticClass:"card",staticStyle:{"margin-bottom":"50px"}},[s("table",{staticClass:"table is-fullwidth is-hoverable"},[t._m(0),s("tbody",t._l(t.distributions,(function(e,a){return s("tr",{key:a,class:{"is-selected":t.selectedDistribution==a},on:{click:function(e){t.selectedDistribution=a}}},[s("td",[t._v(t._s(a))]),s("td",[t._v(t._s(t.getProcessorName(a)))]),s("td",[t._v(t._s((e||[]).length))])])})),0)])])]):t._e(),s("span",{staticClass:"help",staticStyle:{padding:"10px"}},[t._v("Showing "),s("b",[t._v(t._s(t.filteredTasks.length)+"/"+t._s(t.totalTasks))]),t._v(" tasks")]),t.filteredTasks.length>0?s("div",{staticClass:"card"},[s("table-task",{attrs:{filteredTasks:t.filteredTasks,getProjectName:t.getProjectName,isViewed:t.noop,selectedSessionTasks:t.noop,getProcessorName:t.getProcessorName,viewTask:t.viewTask}})],1):t._e(),0==t.filteredTasks.length&&"tasks"==t.tab?s("div",{staticClass:"card card-content"},[t._v("\n                    No task assigned to you\n                ")]):t._e()]),s("quickview-task",{attrs:{task:t.task},on:{close:function(e){t.task=!1},blocked:t.loadData}})],1)]):t._e()])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("thead",[s("tr",[s("th",[t._v("Staff ID")]),s("th",[t._v("Name")]),s("th",[t._v("Total tasks")])])])}]},a154:function(t,e,s){"use strict";s.r(e);var a=s("5d84"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},a15a:function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");s("99af"),s("b0c0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("0ee1")),r=a(s("350b")),l=a(s("c3a9")),c={components:{TableTask:o["default"],QuickviewTask:r["default"],RequestProgress:l["default"]},data:function(){return{task:!1,users:[]}},mounted:function(){this.loadData()},methods:{loaded:function(){this.$store.dispatch("isLoaded")},removeFile:function(t){var e=this;confirm("Confirm to delete the file?")&&this.$store.dispatch("om.dropfile.remove",{drop_id:t}).then((function(){e.$router.push("/")}))},loadData:function(){var t=this;this.$store.dispatch("om.processors.list").then((function(e){t.users=e})),this.$store.dispatch("om.jobsheet.load",this.drop_id).then((function(){t.loaded()})),this.$store.dispatch("om.dropfile.find",this.drop_id)},noop:function(){},isViewed:function(t){return this.task==t},getProjectName:function(t){return this.$store.state.om.projects[t].name},viewTask:function(t){this.task=t},getProcessorName:function(t){return n.pipe(n.find((function(e){return e.staff_id==t})),(function(t){return(t||{}).name}))(this.users)},getRejectionReason:function(t){return"REJECTED"!=t.Status?"":"".concat(t.details.rejection.reason,"\n\n-").concat(t.details.rejection.rejectedBy.name,"-")}},computed:{drop_id:function(){return this.$route.params.PK},job:function(){return this.$store.state.om.jobs[this.drop_id]},jobsheet:function(){return this.job?this.job:(this.$store.dispatch("om.jobsheet.load",this.drop_id),!1)},tasks:function(){return this.jobsheet.tasks||[]},dropfile:function(){return this.$store.state.om.dropfile.files[this.drop_id]}}};e["default"]=c,t.exports=e.default,t.exports.default=e.default},a18f:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("div",{staticClass:"modal is-active animated fadeIn faster"},[s("div",{staticClass:"modal-background has-background-primary"}),s("div",{staticClass:"modal-content"},[s("div",{staticClass:"column"},[t._m(0),s("div",{staticClass:"columns is-centered has-text-centered"},[t.hasRole("executives")?s("div",{staticClass:"column is-3 animated faster fadeInUp",staticStyle:{"animation-delay":"0.1s"}},[s("a",{staticClass:"box ",on:{click:function(e){return t.switchTo("executives")}}},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 480 480",width:"100%"}},[s("path",{staticClass:"active-path",attrs:{d:"M448 392H32c-13.256 0-24-10.744-24-24V32C8 18.744 18.744 8 32 8h416c13.256 0 24 10.744 24 24v336c0 13.256-10.744 24-24 24z","data-original":"#B3D5E4","data-old_color":"#B3D5E4",fill:"#ff8c00"}}),s("path",{attrs:{"data-original":"#E4D9B3",fill:"#e4d9b3",d:"M128 40h312v288H128z"}}),s("path",{attrs:{d:"M103.936 268.392c.56-.168 1.12-.336 1.672-.512-.56.176-1.12.344-1.672.512zM110.144 266.248c.552-.216 1.104-.44 1.656-.672-.544.232-1.096.456-1.656.672zM116.272 263.568c.504-.248.992-.512 1.496-.768-.504.256-1 .52-1.496.768zM183.152 139.32c.016.144.048.288.064.432l-.048-.432c-.008-.008-.008 0-.016 0z","data-original":"#D45B5B","data-old_color":"#D45B5B",fill:"#ff6d00"}}),s("path",{staticClass:"active-path",attrs:{"data-original":"#B3D5E4","data-old_color":"#B3D5E4",fill:"#ff8c00",d:"M128 352v-40l-48 24z"}}),s("path",{attrs:{d:"M200 192c-7.272 0-17.064-4.04-25.928-10.64-.624 3.6-1.4 7.144-2.344 10.648V192H200zM183.168 139.312c-.256-2.04-.576-4.048-.944-6.048.368 2 .672 4.016.928 6.056.008 0 .008-.008.016-.008zM128 296v176h88l-16-152zM181.688 130.264c-.544-2.52-1.192-4.992-1.912-7.44.712 2.456 1.376 4.92 1.912 7.44zM147.824 237.8c.128-.152.264-.296.384-.448-.128.152-.256.296-.384.448zM162.304 216.16c.04-.072.08-.152.12-.224-.032.08-.08.152-.12.224zM122.24 260.352c.376-.224.736-.472 1.104-.696-.368.232-.736.472-1.104.696zM148.208 237.352c5.472-6.568 10.176-13.664 14.096-21.184-3.912 7.512-8.632 14.616-14.096 21.184zM184 152c0-3.16-.192-6.264-.472-9.344.272 3.08.472 6.192.472 9.344zM175.408 171.704c.368-3.552.592-7.128.592-10.736 0 3.608-.272 7.176-.592 10.736zM162.432 215.936c3.952-7.616 7.056-15.624 9.296-23.928-2.24 8.304-5.36 16.328-9.296 23.928zM178.136 117.824c-.296-.832-.6-1.664-.912-2.488.312.824.624 1.656.912 2.488zM97.736 270.008c.496-.112.992-.208 1.488-.32-.488.112-.992.208-1.488.32z","data-original":"#D45B5B","data-old_color":"#D45B5B",fill:"#ff6d00"}}),s("path",{staticClass:"active-path",attrs:{"data-original":"#B3D5E4","data-old_color":"#B3D5E4",fill:"#ff8c00",d:"M32 312v40l48-16z"}}),s("path",{attrs:{d:"M60.768 269.68c.496.12.992.216 1.488.32-.488-.104-.992-.2-1.488-.32zM67.272 270.968c.288.04.568.072.856.112-.288-.04-.576-.064-.856-.112zM54.392 267.88c.552.184 1.112.344 1.672.512-.552-.168-1.112-.336-1.672-.512zM91.872 271.08c.288-.04.568-.064.856-.112-.28.048-.568.072-.856.112zM48.2 265.576c.552.232 1.104.456 1.656.672-.56-.216-1.112-.44-1.656-.672zM42.24 262.8c.496.256.992.52 1.496.768-.504-.248-1-.512-1.496-.768zM36.656 259.656c.368.232.736.472 1.104.696-.368-.224-.736-.464-1.104-.696zM179.776 122.824c-.496-1.696-1.064-3.344-1.64-5.008.584 1.656 1.144 3.32 1.64 5.008zM168.56 97.68c3.416 5.56 6.312 11.48 8.664 17.656-2.336-6.192-5.24-12.088-8.664-17.656zM174.072 181.36c.552-3.2 1.04-6.4 1.336-9.656-.336 3.24-.784 6.456-1.336 9.656z","data-original":"#D45B5B","data-old_color":"#D45B5B",fill:"#ff6d00"}}),s("path",{staticClass:"active-path",attrs:{d:"M208 120l40 24c0-26.512-8-56-48-56-14.152 0-24.232 3.736-31.432 9.68 3.424 5.568 6.32 11.464 8.664 17.656.312.824.616 1.656.912 2.488.576 1.656 1.144 3.312 1.64 5.008.72 2.448 1.368 4.92 1.912 7.44.208.992.36 2 .536 3 .368 2 .688 4.008.944 6.048 10.976-3.392 21.456-9.208 24.824-19.32z","data-original":"#B3D5E4","data-old_color":"#B3D5E4",fill:"#ff8c00"}}),s("g",[s("path",{attrs:{d:"M182.224 133.264c-.184-1-.328-2.008-.536-3 .216.992.352 2.008.536 3zM183.528 142.656c-.088-.976-.2-1.936-.312-2.904.112.96.224 1.928.312 2.904z","data-original":"#D45B5B","data-old_color":"#D45B5B",fill:"#ff6d00"}})]),s("path",{attrs:{d:"M200 192c16.968 0 48-21.488 48-48l-40-24c-3.368 10.112-13.848 15.928-24.832 19.312l.048.432c.112.968.224 1.928.312 2.904.28 3.088.472 6.192.472 9.352v8h-8.048c0 .328.048.64.048.968 0 3.608-.224 7.184-.592 10.736-.296 3.248-.784 6.456-1.336 9.656C182.936 187.96 192.728 192 200 192z","data-original":"#FFFFFF",fill:"#fff"}}),s("path",{attrs:{"data-original":"#D45B5B","data-old_color":"#D45B5B",fill:"#ff6d00",d:"M200 248l16-16-16-40-16 40z"}}),s("g",[s("path",{staticClass:"active-path",attrs:{d:"M303.328 197.784c-9.576 6.384-15.328 17.128-15.328 28.64V280h128v-53.576c0-11.512-5.752-22.256-15.328-28.64-5.656-3.776-12.296-5.784-19.096-5.784H352l16 40-16 16-16-16 16-40h-29.576c-6.8 0-13.44 2.008-19.096 5.784zM360 120l40 24c0-26.512-8-56-48-56s-48 29.488-48 56c0 0 48 0 56-24z","data-original":"#B3D5E4","data-old_color":"#B3D5E4",fill:"#ff8c00"}})]),s("path",{attrs:{d:"M400 144l-40-24c-8 24-56 24-56 24 0 26.512 31.032 48 48 48s48-21.488 48-48z","data-original":"#FFFFFF",fill:"#fff"}}),s("g",[s("path",{attrs:{"data-original":"#D45B5B","data-old_color":"#D45B5B",fill:"#ff6d00",d:"M352 248l16-16-16-40-16 40zM62.264 270.008c1.664.368 3.328.704 5.008.968-1.68-.264-3.344-.608-5.008-.968zM99.232 269.68c1.584-.376 3.144-.816 4.704-1.288-1.56.472-3.128.912-4.704 1.288zM56.064 268.392c1.56.472 3.128.912 4.704 1.288-1.576-.376-3.144-.816-4.704-1.288zM92.728 270.968c1.68-.264 3.344-.6 5.008-.968-1.664.368-3.328.712-5.008.968zM105.608 267.88c1.528-.496 3.04-1.04 4.544-1.632-1.504.584-3.024 1.136-4.544 1.632zM43.728 263.568c1.472.72 2.96 1.384 4.464 2.008-1.496-.624-2.992-1.288-4.464-2.008zM37.76 260.352c1.472.872 2.96 1.672 4.472 2.448-1.504-.776-3-1.576-4.472-2.448zM49.856 266.248c1.504.584 3.016 1.136 4.544 1.632-1.528-.496-3.048-1.048-4.544-1.632zM32 256.608c1.52 1.088 3.08 2.08 4.656 3.048-1.576-.968-3.136-1.968-4.656-3.048zM91.872 271.08c-7.872 1.136-15.872 1.136-23.744 0 7.872 1.144 15.872 1.144 23.744 0zM111.8 265.576c1.504-.624 2.992-1.288 4.464-2.008-1.464.72-2.96 1.384-4.464 2.008z"}})]),s("path",{attrs:{d:"M128 312v-55.392c-1.52 1.088-3.08 2.08-4.656 3.048-.368.232-.736.472-1.104.696-1.472.872-2.96 1.672-4.472 2.448-.496.256-.992.52-1.496.768-1.472.72-2.96 1.384-4.464 2.008-.552.232-1.104.456-1.656.672-1.504.584-3.016 1.136-4.544 1.632-.552.184-1.112.344-1.672.512-1.56.472-3.128.912-4.704 1.288-.496.12-.992.216-1.488.32-1.664.368-3.328.704-5.008.968-.288.04-.568.072-.856.112-7.88 1.136-15.872 1.136-23.744 0-.288-.04-.568-.064-.856-.112-1.68-.264-3.344-.6-5.008-.968-.496-.112-.992-.208-1.488-.32-1.584-.376-3.144-.816-4.704-1.288-.56-.168-1.12-.336-1.672-.512-1.528-.496-3.04-1.04-4.544-1.632-.552-.216-1.104-.44-1.656-.672-1.504-.624-2.992-1.288-4.464-2.008-.504-.248-.992-.512-1.496-.768-1.512-.776-3-1.576-4.472-2.448-.376-.224-.736-.472-1.104-.696-1.592-.968-3.152-1.968-4.672-3.048V312l48 24 48-24z","data-original":"#FFFFFF",fill:"#fff"}}),s("g",[s("path",{attrs:{d:"M117.76 262.8c1.512-.776 3-1.576 4.472-2.448-1.464.872-2.96 1.672-4.472 2.448zM128 256.608c-1.52 1.088-3.08 2.08-4.656 3.048 1.576-.968 3.136-1.968 4.656-3.048z","data-original":"#D45B5B","data-old_color":"#D45B5B",fill:"#ff6d00"}})]),s("g",[s("path",{staticClass:"active-path",attrs:{"data-original":"#B3D5E4","data-old_color":"#B3D5E4",fill:"#ff8c00",d:"M80 336l24 88-24 48h48V352zM80 336l-48 16v120h48l-24-48z"}})]),s("path",{attrs:{"data-original":"#D45B5B","data-old_color":"#D45B5B",fill:"#ff6d00",d:"M80 336l-24 88 24 48 24-48z"}}),s("path",{staticClass:"active-path",attrs:{d:"M151.328 197.784c-9.576 6.384-15.328 17.128-15.328 28.64V280h128v-53.576c0-11.512-5.752-22.256-15.328-28.64-5.656-3.776-12.296-5.784-19.096-5.784H200l16 40-16 16-16-16 16-40h-29.576c-6.8 0-13.44 2.008-19.096 5.784z","data-original":"#B3D5E4","data-old_color":"#B3D5E4",fill:"#ff8c00"}}),s("path",{attrs:{d:"M8 72v160s16 40 72 40 88-72 88-72l7.952-40H184s0-112-96-112C40 48 8 72 8 72z","data-original":"#FFFFFF",fill:"#fff"}}),s("g",[s("path",{staticClass:"active-path",attrs:{"data-original":"#B3D5E4","data-old_color":"#B3D5E4",fill:"#ff8c00",d:"M352 440H232v-48h80zM472 472H232v-32h200z"}})]),s("g",[s("path",{attrs:{d:"M280 226.424V280c0 4.424 3.576 8 8 8h128c4.424 0 8-3.576 8-8v-53.576c0-14.216-7.064-27.416-18.888-35.296-5.496-3.672-11.712-5.92-18.2-6.736C398.776 174.112 408 159.712 408 144c0-52.896-30.456-64-56-64s-56 11.104-56 64c0 15.712 9.224 30.112 21.096 40.392-6.488.816-12.712 3.064-18.2 6.736C287.064 199.008 280 212.2 280 226.424zm72-12.888l6.608 16.528-6.608 6.624-6.608-6.616L352 213.536zm-38.888-61.952c13.696-1.128 38.424-5.272 49.92-20.44l28.592 17.16C388.416 167.896 364.28 184 352 184c-11.592 0-33.728-14.336-38.888-32.416zM352 96c22.704 0 35.176 10.736 38.776 33.136l-26.656-15.992c-2.088-1.248-4.624-1.488-6.888-.64-2.28.832-4.048 2.672-4.816 4.968-4.048 12.144-25.904 16.736-39.968 18.072C314.584 108.72 327.232 96 352 96zm-44.232 108.44c4.352-2.912 9.416-4.44 14.656-4.44h17.76l-11.616 29.032c-1.184 2.968-.496 6.36 1.776 8.624l16 16c1.56 1.56 3.608 2.344 5.656 2.344s4.096-.784 5.656-2.344l16-16c2.264-2.264 2.96-5.656 1.776-8.624L363.816 200h17.76c5.232 0 10.304 1.528 14.656 4.44C403.6 209.344 408 217.56 408 226.424V272H296v-45.576c0-8.864 4.4-17.08 11.768-21.984z","data-original":"#23374E",fill:"#23374e"}}),s("path",{attrs:{d:"M440 0H40C17.944 0 0 17.944 0 40h16c0-13.232 10.768-24 24-24h400c13.232 0 24 10.768 24 24v320c0 13.232-10.768 24-24 24H224v16h84.248l26.672 32H224v16h205.192l20 16H224v16h248c3.4 0 6.424-2.152 7.544-5.352s.104-6.776-2.544-8.896l-40-32c-1.424-1.136-3.176-1.752-5-1.752h-76.248l-26.664-32H440c22.056 0 40-17.944 40-40V40c0-22.056-17.944-40-40-40z","data-original":"#23374E",fill:"#23374e"}}),s("path",{attrs:{d:"M224 336h216c4.424 0 8-3.576 8-8V40c0-4.424-3.576-8-8-8H168v16h264v272H224v16z","data-original":"#23374E",fill:"#23374e"}}),s("path",{attrs:{d:"M202.528 312.416L136 290.24v-29.672c.696-.544 1.416-1.032 2.104-1.6 1.136-.944 2.128-2.024 3.224-3 .064-.056.128-.12.192-.184 3.984-3.576 7.696-7.376 11.168-11.368l.912-1.056c10.872-12.808 19.112-27.488 24.208-43.36h10.384l-11.616 29.032c-1.184 2.968-.496 6.36 1.776 8.624l16 16c1.552 1.56 3.6 2.344 5.648 2.344s4.096-.784 5.656-2.344l16-16c2.264-2.264 2.96-5.656 1.776-8.624L211.816 200h17.76c5.232 0 10.304 1.528 14.656 4.44C251.6 209.344 256 217.56 256 226.424V272H152v16h112c4.424 0 8-3.576 8-8v-53.576c0-14.216-7.064-27.416-18.888-35.296-5.496-3.672-11.712-5.92-18.2-6.736C246.776 174.112 256 159.712 256 144c0-52.896-30.456-64-56-64-11.344 0-21.128 2.24-29.24 6.632C150.4 58.448 117.352 40 80 40 50.952 40 23.424 51.072 2.464 71.176l11.08 11.544C31.496 65.496 55.104 56 80 56c34.912 0 65.44 18.792 82.248 46.736 2.992 4.992 5.504 10.208 7.544 15.584.304.816.616 1.632.904 2.456.528 1.52 1.032 3.056 1.48 4.6.44 1.52.824 3.064 1.192 4.616.384 1.6.72 3.208 1.016 4.824.328 1.808.616 3.632.84 5.48v.024c.176 1.416.296 2.84.408 4.272h.008c.176 2.456.36 4.912.36 7.408h-24c-22.056 0-40-17.944-40-40 0-2.432-1.104-4.72-2.992-6.24-1.896-1.512-4.36-2.08-6.744-1.568l-4.824 1.072c-32.696 7.272-64.104 19.216-93.336 35.504l.048.08c-.016.008-.032.016-.056.024l7.8 13.968c26.784-14.944 55.352-26.048 84.96-33.032C101.504 148.024 124.472 168 152 168h15.648c-.288 3.776-.72 7.704-1.464 11.992-.048.288-.008.568-.024.856-.504 2.816-1.112 5.6-1.824 8.352-.096.248-.264.464-.336.728-5.64 20.952-17.168 39.672-33.328 54.128l.008.008c-.96.856-1.84 1.784-2.832 2.616-1.456 1.216-2.968 2.328-4.496 3.416-3.128 2.232-6.4 4.168-9.752 5.864-.216.104-.424.224-.64.328-3.288 1.616-6.664 2.976-10.104 4.08l-.784.24c-3.488 1.08-7.032 1.92-10.616 2.48-.104.016-.2.024-.304.04-7.408 1.12-14.952 1.12-22.36 0-.08-.008-.16-.016-.24-.032-3.608-.56-7.176-1.408-10.688-2.496-.232-.072-.464-.136-.688-.216-3.464-1.112-6.872-2.48-10.184-4.112-.184-.088-.368-.192-.552-.28-3.376-1.696-6.656-3.648-9.8-5.888-.008 0-.008-.008-.016-.008-1.52-1.088-3.032-2.2-4.488-3.416-6.856-5.712-13.016-12.24-18.296-19.408L.968 236.776c6.04 8.184 13.08 15.664 20.92 22.2.68.568 1.408 1.056 2.104 1.6v29.672l-18.528 6.176 5.064 15.168L24 307.104V464H8v16h208c2.264 0 4.432-.96 5.944-2.648s2.248-3.936 2.008-6.184l-16-152c-.328-3.128-2.448-5.76-5.424-6.752zM200 184c-4.496 0-10.936-2.424-17.2-6.36.288-2.184.472-4.384.648-6.584.088-1 .256-2.072.32-3.056H184c4.424 0 8-3.576 8-8v-8c0-2.464-.208-4.872-.368-7.296 8.568-3.448 15.128-7.944 19.392-13.56l28.6 17.16C236.416 167.896 212.28 184 200 184zm0 52.688l-6.608-6.616L200 213.536l6.608 16.528-6.608 6.624zM200 96c22.704 0 35.176 10.736 38.776 33.136l-26.656-15.992c-2.088-1.248-4.616-1.488-6.888-.64-2.272.832-4.048 2.664-4.816 4.968-1.6 4.784-6.04 8.272-11 10.792-.128-.6-.32-1.176-.456-1.776-.208-.88-.448-1.752-.68-2.632-.912-3.496-1.976-6.912-3.208-10.264-.216-.584-.416-1.16-.64-1.736-1.536-3.984-3.248-7.88-5.216-11.632C184.816 97.392 191.672 96 200 96zM95.48 423.16L80 454.112 64.52 423.16 80 366.408l15.48 56.752zM120 340.896l-19.144-6.376L120 324.944v15.952zm-80 0v-15.952l19.144 9.576L40 340.896zm19.176-63.376c.36.088.72.152 1.08.232 2.936.656 5.888 1.16 8.864 1.52.328.04.656.088.984.128 3.288.36 6.584.608 9.888.608s6.6-.248 9.888-.608c.328-.04.656-.088.984-.128 2.976-.36 5.928-.864 8.864-1.52.36-.08.72-.152 1.08-.232 6.424-1.512 12.712-3.728 18.752-6.664.136-.064.288-.112.424-.176v36.376l-40 20-40-20V270.68c.136.072.288.112.424.176 6.064 2.936 12.344 5.152 18.768 6.664zM40 357.768l28.352-9.448-20.072 73.584c-.512 1.896-.32 3.92.56 5.68L67.056 464H40V357.768zM92.944 464l18.208-36.424c.88-1.76 1.08-3.784.56-5.68L91.64 348.312l28.36 9.456V464H92.944zM136 464V307.104l56.584 18.856L207.12 464H136z","data-original":"#23374E",fill:"#23374e"}})])]),s("span",{staticClass:"heading"},[t._v("Executive "),t.isActive("executives")?s("span",{staticClass:"has-text-success",staticStyle:{"font-weight":"bolder"}},[t._v("✓")]):t._e()])])]):t._e(),t.hasRole("processors")?s("div",{staticClass:"column is-3 animated faster fadeInUp",staticStyle:{"animation-delay":"0.2s"}},[s("a",{staticClass:"box ",on:{click:function(e){return t.switchTo("processors")}}},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 480 480",width:"100%"}},[s("path",{staticClass:"active-path",attrs:{d:"M364 232h-60v104c0 13.254-10.746 24-24 24h-76v112h240V312c0-44.184-35.816-80-80-80zm0 0",fill:"#f60","data-original":"#A8DBA8","data-old_color":"#FF8C00"}}),s("path",{attrs:{d:"M328 8c-66.273 0-120 53.727-120 120 0 38.48 18.078 51.121 46.238 55.04-4.11-9.833-6.23-20.384-6.238-31.04v-48h160v48c-.008 10.656-2.129 21.207-6.238 31.04C429.922 179.12 448 166.48 448 128 448 61.727 394.273 8 328 8zm0 0",fill:"#ff8c00","data-original":"#79BD9A","data-old_color":"#79BD9A"}}),s("path",{attrs:{d:"M304 228.32c7.762 2.453 15.86 3.692 24 3.68 5.371-.004 10.73-.543 16-1.602-15.121-3.05-29.02-10.445-40-21.277zm0 0M248 104v48c.008 11.008 2.266 21.898 6.64 32H280c2.418 0 4.82.379 7.121 1.121C282.43 174.707 280 163.418 280 152v-16h128v-32zm0 0",fill:"#fcdcb4","data-original":"#CFF09E","data-old_color":"#EFCDA2"}}),s("path",{attrs:{d:"M364 232h-60v42.559c7.488 3.601 15.691 5.46 24 5.441 26.922.008 50.04-19.145 55.04-45.602-6.228-1.57-12.618-2.375-19.04-2.398zm0 0",fill:"#fcdcb4","data-original":"#CFF09E","data-old_color":"#EFCDA2"}}),s("path",{attrs:{d:"M328 232c-8.14.012-16.238-1.227-24-3.68v13.602c4.406 3.922 10.102 6.086 16 6.078h16c12.742.02 23.277-9.918 24-22.64-10.102 4.374-20.992 6.632-32 6.64zm0 0",fill:"#fcdcb4","data-original":"#CFF09E","data-old_color":"#EFCDA2"}}),s("path",{staticClass:"active-path",attrs:{d:"M16 440h448c4.418 0 8 3.582 8 8v16c0 4.418-3.582 8-8 8H16c-4.418 0-8-3.582-8-8v-16c0-4.418 3.582-8 8-8zm0 0",fill:"#f60","data-original":"#A8DBA8","data-old_color":"#FF8C00"}}),s("path",{attrs:{d:"M80 408h176c4.418 0 8 3.582 8 8v16c0 4.418-3.582 8-8 8H80c-4.418 0-8-3.582-8-8v-16c0-4.418 3.582-8 8-8zm0 0M152 328v-8c-.04-5.605 1.395-11.125 4.16-16-16.16 1.953-28.277 15.723-28.16 32v72h80v-16.48c-32.008-4.032-56.012-31.258-56-63.52zm0 0",fill:"#fcdcb4","data-original":"#CFF09E","data-old_color":"#EFCDA2"}}),s("path",{attrs:{d:"M128 336c-.008-7.023 2.3-13.855 6.559-19.441C92.887 305.105 64.004 267.219 64 224v-40h-8c-13.254 0-24 10.746-24 24v128c0 13.254 10.746 24 24 24h72zm0 0M203.68 320c2.82 4.863 4.308 10.379 4.32 16v24h72c13.254 0 24-10.746 24-24v-16zm0 0",fill:"#fcdcb4","data-original":"#CFF09E","data-old_color":"#EFCDA2"}}),s("path",{attrs:{d:"M320 392h104c4.418 0 8 3.582 8 8v32c0 4.418-3.582 8-8 8H320c-8.836 0-16-7.164-16-16v-16c0-8.836 7.164-16 16-16zm0 0M360 344h104c4.418 0 8 3.582 8 8v32c0 4.418-3.582 8-8 8H360c-8.836 0-16-7.164-16-16v-16c0-8.836 7.164-16 16-16zm0 0",fill:"#ff8c00","data-original":"#79BD9A","data-old_color":"#79BD9A"}}),s("path",{attrs:{d:"M424 344h40c4.418 0 8 3.582 8 8v32c0 4.418-3.582 8-8 8h-40zm0 0M320 392h48v48h-48c-8.836 0-16-7.164-16-16v-16c0-8.836 7.164-16 16-16zm0 0",fill:"#fcdcb4","data-original":"#CFF09E","data-old_color":"#EFCDA2"}}),s("g",{attrs:{fill:"#3b8686"}},[s("path",{attrs:{d:"M304 144c0 4.418-3.582 8-8 8s-8-3.582-8-8 3.582-8 8-8 8 3.582 8 8zm0 0M368 144c0 4.418-3.582 8-8 8s-8-3.582-8-8 3.582-8 8-8 8 3.582 8 8zm0 0M320 58.32c18.559-6.465 39.11.942 49.281 17.758 1.41 2.383 3.953 3.867 6.719 3.922 1.457-.023 2.887-.406 4.16-1.121 3.77-2.281 4.988-7.18 2.719-10.957-14.102-23.36-42.64-33.64-68.399-24.64-4.152 1.523-6.285 6.124-4.761 10.277 1.527 4.156 6.129 6.285 10.281 4.761zm0 0M472 464H8c-4.418 0-8 3.582-8 8s3.582 8 8 8h464c4.418 0 8-3.582 8-8s-3.582-8-8-8zm0 0M224 240c4.418 0 8-3.582 8-8s-3.582-8-8-8H120c-4.418 0-8 3.582-8 8s3.582 8 8 8zm0 0M184 256h-24c-4.418 0-8 3.582-8 8s3.582 8 8 8h24c4.418 0 8-3.582 8-8s-3.582-8-8-8zm0 0","data-original":"#000000","data-old_color":"#504A4A",fill:"#4f4949"}}),s("path",{attrs:{d:"M472 400c4.418 0 8-3.582 8-8s-3.582-8-8-8h-40v-32h40c4.418 0 8-3.582 8-8s-3.582-8-8-8h-20v-24c-.05-40.488-27.719-75.715-67.04-85.36-2.562-.64-5.12-1.12-8-1.519 12.997-8.715 23.415-20.762 30.161-34.883C429.281 186.32 456 174.718 456 128 456 57.309 398.691 0 328 0S200 57.309 200 128c0 24 7.121 38.8 17.04 48H56c-17.672 0-32 14.328-32 32v128c0 17.672 14.328 32 32 32h64v32H88c-13.254 0-24 10.746-24 24v8H8c-4.418 0-8 3.582-8 8s3.582 8 8 8h464c4.418 0 8-3.582 8-8s-3.582-8-8-8h-32v-32zm-72-248c.25 21.98-9.668 42.848-26.871 56.531C355.926 222.22 333.363 227.187 312 222v-14c0-17.672-14.328-32-32-32h-19.762c-2.8-7.691-4.234-15.813-4.238-24v-40h144zm-26.96 88.64c-6.966 18.852-24.942 31.368-45.04 31.36-5.457 0-10.867-.95-16-2.8V240h52c2.941.031 5.879.215 8.8.559zM216 128c0-61.855 50.145-112 112-112s112 50.145 112 112c0 28-9.84 39.281-26.48 44.32 1.64-6.648 2.472-13.472 2.48-20.32v-48c0-4.418-3.582-8-8-8H248c-4.418 0-8 3.582-8 8v48c.02 6.848.852 13.668 2.48 20.32C225.84 167.281 216 156 216 128zM56 352c-8.836 0-16-7.164-16-16V208c0-8.836 7.164-16 16-16h224c8.836 0 16 7.164 16 16v128c0 8.836-7.164 16-16 16h-64v-16c0-22.09-17.91-40-40-40h-16c-22.09 0-40 17.91-40 40v16zm144-16v64h-64v-64c0-13.254 10.746-24 24-24h16c13.254 0 24 10.746 24 24zm56 96H80v-8c0-4.418 3.582-8 8-8h160c4.418 0 8 3.582 8 8zm41.441 0H272v-8c0-13.254-10.746-24-24-24h-32v-32h64c17.672 0 32-14.328 32-32v-50.078c5.223 1.375 10.602 2.07 16 2.078 27.45-.043 51.813-17.586 60.559-43.602C417.008 254.723 435.965 281.734 436 312v24h-76c-13.254 0-24 10.746-24 24v16c.023 2.73.508 5.434 1.441 8H320c-13.254 0-24 10.746-24 24v16c.023 2.73.508 5.434 1.441 8zM360 432h-40c-4.418 0-8-3.582-8-8v-16c0-4.418 3.582-8 8-8h40zm-8-56v-16c0-4.418 3.582-8 8-8h56v32h-56c-4.418 0-8-3.582-8-8zm72 56h-48v-32h48zm0 0","data-original":"#000000","data-old_color":"#504A4A",fill:"#4f4949"}})])]),s("span",{staticClass:"heading"},[t._v("Processor "),t.isActive("processors")?s("span",{staticClass:"has-text-success",staticStyle:{"font-weight":"bolder"}},[t._v("✓")]):t._e()])])]):t._e(),t.hasRole("supervisors")?s("div",{staticClass:"column is-3 animated faster fadeInUp",staticStyle:{"animation-delay":"0.3s"}},[s("a",{staticClass:"box ",on:{click:function(e){return t.switchTo("supervisors")}}},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"-15 0 512 512",width:"100%"}},[s("path",{attrs:{d:"M142.719 166.34L75.16 68.496 7.602 166.34h24.75v338.058h85.617V166.34zm0 0",fill:"#79bbe8","data-original":"#79BBE8"}}),s("path",{attrs:{d:"M198.27 395.168l-67.56-97.84-67.558 97.84h24.75v109.23h85.618v-109.23zm0 0",fill:"#f74866","data-original":"#F74866"}}),s("path",{attrs:{d:"M473.977 105.445l-67.559-97.84-67.559 97.84h24.75v398.953h85.614V105.445zm0 0",fill:"#ffc627","data-original":"#FFC627","data-old_color":"#F74866"}}),s("path",{attrs:{d:"M302.336 417.34h41.168c21.105 0 38.215 17.105 38.215 38.215v49.113H166.332v-49.113c0-21.11 17.11-38.215 38.215-38.215h41.168",fill:"#ff8c00","data-original":"#515F75","data-old_color":"#FF7C00"}}),s("path",{attrs:{d:"M226.938 417.34l24.878 87.328h44.422l24.883-87.328",fill:"#f2f2f2","data-original":"#F2F2F2"}}),s("path",{attrs:{d:"M302.336 368.273v49.07l-28.309 15.95-8.746-4.926-19.562-11.023v-49.098l19.562.004zm0 0",fill:"#f2f2f2","data-original":"#F9C2AF","data-old_color":"#F9C2AF"}}),s("path",{attrs:{d:"M251.973 504.668l22.496-27.79 21.77 27.79",fill:"#f74866","data-original":"#F74866"}}),s("path",{attrs:{d:"M265.281 368.25l-19.562-.008v49.102l19.562 11.023v-22.703c2.89.281 5.82.445 8.785.445 9.883 0 19.395-1.613 28.27-4.593v-33.243zm0 0M185.207 316.613c-14.742 0-26.691-10.695-26.691-23.89 0-13.196 11.949-23.891 26.691-23.891h177.727c14.738 0 26.687 10.695 26.687 23.89 0 13.196-11.95 23.891-26.687 23.891",fill:"#f9c2af","data-original":"#EA9E8F","data-old_color":"#EA9E8F"}}),s("path",{attrs:{d:"M362.934 216.543v88.867c0 49.078-39.79 88.863-88.868 88.863-49.074 0-88.863-39.785-88.863-88.863v-88.867",fill:"#f2f2f2","data-original":"#F9C2AF","data-old_color":"#F9C2AF"}}),s("path",{attrs:{d:"M273.438 394.27c-24.29-.165-46.258-10.079-62.208-26.024-16.082-16.086-26.027-38.297-26.027-62.836v-88.867h40.012v88.867c0 24.54 5.465 46.75 14.305 62.836 8.707 15.832 20.675 25.715 33.917 26.024zm0 0",fill:"#f9c2af","data-original":"#EA9E8F","data-old_color":"#EA9E8F"}}),s("path",{attrs:{d:"M340.906 240.688c-32.199 11.605-63.37 7.699-85.422 1.55 0 0-.007 0-.007-.008-19.2-5.347-31.473-12.406-31.473-12.406s-8.106 39.004-38.8 39.004c0 0-20.419-36.121-19.778-69.543.332-17.328 14.62-30.293 30.863-30.293 3.035 0 6.137.45 9.227 1.414 8.699-23.36 31.68-38.144 56.562-36.633 3.875.243 7.774.551 11.688.907.75.058 1.504.129 2.254.199l.23.023c40.613 3.813 81.754 11.239 105.926-15.5 0 0 39.949 92.028-41.27 121.285zm0 0",fill:"#ff8c00","data-original":"#515F75","data-old_color":"#FF7C00"}}),s("path",{staticClass:"active-path",attrs:{d:"M262.078 133.777c3.875.239 7.774.547 11.688.903-35.836-1.512-64.305 77.406-18.29 107.554-19.199-5.351-31.472-12.406-31.472-12.406s-8.106 39-38.8 39c0 0-20.419-36.121-19.778-69.539.332-17.332 14.62-30.293 30.863-30.293 3.035 0 6.137.45 9.227 1.41 8.699-23.36 31.68-38.144 56.562-36.629zm0 0",fill:"#ff7c00","data-original":"#3B485B","data-old_color":"#FF8C00"}}),s("path",{attrs:{d:"M254.55 452.77l18.887 24.109 19.516-24.11-18.926-19.476z",fill:"#f74866","data-original":"#F74866"}}),s("path",{attrs:{d:"M276.266 134.91c-.082-.015-.16-.023-.246-.027.078.008.156.012.23.023zm0 0",fill:"#ff8c00","data-original":"#F4AF9D","data-old_color":"#FF7C00"}}),s("path",{attrs:{d:"M480.234 101.121l-67.562-97.84C411.25 1.227 408.914 0 406.414 0s-4.836 1.227-6.258 3.285l-67.55 97.836c-1.61 2.328-1.794 5.352-.477 7.856 1.312 2.5 3.906 4.07 6.73 4.07h17.145v14.297c-18.574 6.14-42.266 3.726-66.926 1.199-8.703-.89-17.703-1.813-26.543-2.352-26.246-1.597-50.183 12.461-61.527 35.485-9.57-1.2-19.211 1.23-27.238 7.023-9.903 7.145-15.715 18.242-15.95 30.438-.472 24.808 9.653 50.441 15.946 63.898-13.301 4.336-22.856 16.004-22.856 29.688 0 15.527 12.305 28.46 28.426 31.023 3.598 18.785 12.7 36.059 26.52 49.879 9.367 9.367 20.324 16.563 32.261 21.352v14.753h-33.574c-8.55 0-16.559 2.364-23.418 6.461v-13.414h17.145c2.824 0 5.418-1.566 6.734-4.07 1.312-2.504 1.129-5.527-.477-7.855l-67.554-97.848c-1.418-2.055-3.758-3.281-6.258-3.281-1.93 0-3.758.738-5.149 2.015v-117.8h17.157c2.824 0 5.418-1.567 6.73-4.07 1.313-2.505 1.133-5.528-.476-7.856L81.414 64.176c-1.418-2.055-3.758-3.285-6.258-3.285-2.496 0-4.836 1.23-6.258 3.285l-67.55 97.84c-1.606 2.324-1.79 5.347-.477 7.851 1.313 2.504 3.906 4.07 6.734 4.07H24.75v76.25c0 4.2 3.402 7.606 7.602 7.606 4.199 0 7.605-3.406 7.605-7.605v-83.852c0-4.203-3.406-7.606-7.605-7.606H22.094L75.156 81.88l53.074 76.851h-10.27c-4.198 0-7.6 3.403-7.6 7.606v147.086l-53.465 77.43c-1.606 2.324-1.79 5.351-.477 7.855 1.316 2.504 3.906 4.07 6.734 4.07h17.145v94.016h-40.34V280.605c0-4.199-3.406-7.601-7.605-7.601-4.2 0-7.602 3.402-7.602 7.601v223.79c0 4.199 3.402 7.605 7.602 7.605h416.867c4.199 0 7.605-3.406 7.605-7.605v-49.403c0-4.199-3.406-7.605-7.605-7.605s-7.602 3.406-7.602 7.605v41.801H389.32v-41.246c0-14.504-6.707-27.832-18.105-36.461v-95.8c14.918-3.411 26.012-15.817 26.012-30.563s-11.094-27.149-26.012-30.563v-29.523c9.476-7.657 16.664-17.059 21.46-28.164 17.118-39.633-2.675-86.137-3.527-88.094-1.027-2.371-3.191-4.059-5.742-4.477-2.554-.422-5.14.485-6.87 2.403-1.669 1.847-3.45 3.496-5.325 4.984v-13.848c0-4.199-3.402-7.605-7.602-7.605h-10.257l53.062-76.852 53.07 76.852H449.22c-4.2 0-7.602 3.406-7.602 7.605v319.133c0 4.2 3.403 7.606 7.602 7.606s7.605-3.407 7.605-7.606V113.047h17.153c2.828 0 5.421-1.57 6.734-4.07 1.312-2.504 1.133-5.532-.477-7.856zM166.121 292.723c0-6.676 4.73-12.418 11.48-14.93v27.613c0 .754.012 1.508.028 2.258-6.762-2.508-11.508-8.258-11.508-14.941zm26.688 12.683v-29.68c12.039-2.242 22.246-9.886 29.82-22.48 2.676-4.453 4.633-8.894 6.012-12.605 18.605 8.468 65.21 25.082 114.843 7.203 4.176-1.504 8.118-3.203 11.844-5.07v62.632c0 44.809-36.453 81.266-81.262 81.266-21.703 0-42.109-8.453-57.457-23.8s-23.8-35.755-23.8-57.466zM343.504 409.73H309.94v-14.773c19.149-7.7 35.274-21.367 46.067-38.695v55.203c-4.043-1.145-8.246-1.735-12.504-1.735zm-6.598 60.5l-18.707 4.676c-2.656.664-4.75 2.707-5.484 5.348-.73 2.64.015 5.469 1.953 7.406l9.406 9.406h-17.761l20.546-72.125h8.262zm-101.57 10.024c-.73-2.64-2.828-4.684-5.484-5.348l-18.707-4.676 1.785-45.289h8.265l20.551 72.125h-17.77l9.407-9.406c1.937-1.937 2.683-4.765 1.953-7.406zm8.379-55.313L261.566 435l-16.882 16.883-7.676-26.942zm60.629 0h6.703l-7.676 26.942L286.488 435zm-30.317 19.106l8.957 8.953-9.449 11.672-8.844-11.29zm-20.168 20.168l10.368 13.234-9.067 11.196-5.707-20.028zm39.938-.403l4.808 4.805-5.59 19.61-9.331-11.918zm-19.426 25.274l6.25 7.98h-12.71zm-.305-87.207c7.094 0 14.008-.777 20.668-2.234v13.253l-20.707 11.664-20.703-11.664v-13.246c6.739 1.47 13.684 2.227 20.742 2.227zM95.504 496.793V395.172c0-4.2-3.406-7.602-7.606-7.602H77.645l53.07-76.863 53.062 76.863H173.52c-4.2 0-7.602 3.403-7.602 7.602v35.785c-4.543 7.11-7.191 15.547-7.191 24.59v41.246zm78.434-41.246c0-14.516 10.16-26.695 23.742-29.82l-1.977 50.078c-.14 3.601 2.262 6.804 5.754 7.68l11.676 2.917-10.664 10.664h-28.531zm200.175 41.52h-28.531l-10.664-10.665 11.676-2.918c3.492-.875 5.894-4.078 5.754-7.68l-1.977-50.074c3.254.747 6.375 2.012 9.234 3.786 9.086 5.629 14.508 15.359 14.508 26.03zm7.907-204.344c0 6.449-4.426 12.015-10.805 14.652V278.07c6.379 2.637 10.805 8.207 10.805 14.653zm-3.23-160.043c4.398 14.597 10.097 42.21-.075 65.765-6.973 16.14-20.563 27.95-40.387 35.09-56.074 20.207-110.008-10-110.539-10.3-2.11-1.215-4.672-1.337-6.89-.344s-3.836 2.992-4.34 5.37c-.067.31-6.399 28.926-26.946 32.583-5.07-10.176-17.054-36.91-16.586-61.414.141-7.371 3.657-14.078 9.645-18.399 4.05-2.922 8.781-4.433 13.601-4.433 2.329 0 4.68.351 6.985 1.07 3.855 1.2 7.976-.824 9.387-4.61 7.546-20.277 27.226-33.007 48.968-31.687 8.524.52 17.364 1.426 25.914 2.3 33 3.38 66.989 6.864 91.262-10.991zm0 0","data-original":"#000000","data-old_color":"#000000",fill:"#3b485b"}}),s("path",{attrs:{d:"M305.68 306.152c4.199 0 7.605-3.406 7.605-7.605v-12.348c0-4.199-3.406-7.605-7.605-7.605-4.2 0-7.606 3.406-7.606 7.605v12.348c0 4.2 3.406 7.605 7.606 7.605zm0 0M250.059 298.547v-12.348c0-4.199-3.403-7.605-7.602-7.605s-7.605 3.406-7.605 7.605v12.348c0 4.2 3.406 7.605 7.605 7.605s7.602-3.406 7.602-7.605zm0 0M301.246 331.379c-2.969-2.969-7.785-2.969-10.754 0-4.387 4.387-10.219 6.8-16.426 6.8-6.203 0-12.035-2.413-16.421-6.8-2.97-2.969-7.786-2.969-10.754 0s-2.97 7.785 0 10.754c7.492 7.492 17.336 11.238 27.175 11.238 9.844 0 19.688-3.746 27.18-11.238 2.969-2.969 2.969-7.785 0-10.754zm0 0","data-original":"#000000","data-old_color":"#000000",fill:"#3b485b"}})]),s("span",{staticClass:"heading"},[t._v("Supervisor "),t.isActive("supervisors")?s("span",{staticClass:"has-text-success",staticStyle:{"font-weight":"bolder"}},[t._v("✓")]):t._e()])])]):t._e(),t.hasRole("sales")?s("div",{staticClass:"column is-3 animated faster fadeInUp",staticStyle:{"animation-delay":"0.4s"}},[s("a",{staticClass:"box ",on:{click:function(e){return t.switchTo("sales")}}},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"-8 0 480 480",width:"100%"}},[s("path",{attrs:{d:"M208 86h8c13.254 0 24 10.746 24 24s-10.746 24-24 24h-8zm0 0M72 136h-8c-13.254 0-24-10.746-24-24s10.746-24 24-24h8zm0 0",fill:"#f7bb8f","data-original":"#F7BB8F"}}),s("path",{attrs:{d:"M432 8H312c-13.254 0-24 10.746-24 24v128c0 13.254 10.746 24 24 24h48v40l40-40h32c13.254 0 24-10.746 24-24V32c0-13.254-10.746-24-24-24zm0 0",fill:"#f2f6f7","data-original":"#87CED9","data-old_color":"#F1F6F7"}}),s("g",{attrs:{fill:"#3181bd"}},[s("path",{staticClass:"active-path",attrs:{d:"M312 40h72v16h-72zm0 0M400 40h32v16h-32zm0 0M312 72h120v16H312zm0 0M312 104h32v16h-32zm0 0M360 104h72v16h-72zm0 0M312 136h72v16h-72zm0 0","data-original":"#000000","data-old_color":"#494D5F",fill:"#484c5e"}})]),s("path",{attrs:{d:"M8 480V304c0-26.508 21.492-48 48-48h176c26.508 0 48 21.492 48 48v176",fill:"#f2f3f3","data-original":"#D6D9DB","data-old_color":"#F0F1F2"}}),s("path",{attrs:{d:"M104 168h80v88h-80zm0 0",fill:"#fff0e6","data-original":"#FEC9A3","data-old_color":"#FDEFE5"}}),s("path",{attrs:{d:"M136 256h16v152h-16zm0 0",fill:"#f60","data-original":"#4398D1","data-old_color":"#FF7500"}}),s("path",{attrs:{d:"M232 256h-48l-40 152-40-152H56c-26.508 0-48 21.492-48 48v176h272V304c0-26.508-21.492-48-48-48zm0 0",fill:"#ff9b00","data-original":"#22689C","data-old_color":"#FFA300"}}),s("path",{attrs:{d:"M144 408l-64-72 8-24-24-8 24-48h16l40 152 64-72-8-24 24-8-24-48h-16zm0 0M128 430h16v16h-16zm0 0",fill:"#ff6d00","data-original":"#0A4A7A","data-old_color":"#FF7500"}}),s("path",{attrs:{d:"M248 328h208v152H248zm0 0",fill:"#ff6d00","data-original":"#753D07","data-old_color":"#FF7500"}}),s("path",{attrs:{d:"M456 392l-104 32-104-32v-64h208zm0 0",fill:"#ff7c00","data-original":"#834A14","data-old_color":"#FF7500"}}),s("path",{attrs:{d:"M344 384h16v16h-16zm0 0",fill:"#ffc24f","data-original":"#FFC24F"}}),s("path",{attrs:{d:"M392 328h-16v-24h-48v24h-16v-24c0-8.836 7.164-16 16-16h48c8.836 0 16 7.164 16 16zm0 0M344 400h16v16h-16zm0 0",fill:"#ff6d00","data-original":"#753D07","data-old_color":"#FF7500"}}),s("path",{attrs:{d:"M232 328h16v152h-16zm0 0",fill:"#ff5e00","data-original":"#1E5E8C","data-old_color":"#003399"}}),s("path",{attrs:{d:"M104 176v45.855c24.21 16.192 55.79 16.192 80 0V176zm0 0",fill:"#f7bb8f","data-original":"#F7BB8F"}}),s("path",{attrs:{d:"M144 216c-39.766 0-72-32.234-72-72V48h144v96c0 39.766-32.234 72-72 72zm0 0",fill:"#fff0e6","data-original":"#FEC9A3","data-old_color":"#FDEFE5"}}),s("path",{attrs:{d:"M184.441 8H56v16c0 17.672 14.328 32 32 32h112l16 40 5.719-45.77c1.433-11.492-2.52-23.007-10.711-31.199C203.965 11.977 194.41 8.011 184.44 8zm0 0M192 108c0 6.629-5.371 12-12 12s-12-5.371-12-12 5.371-12 12-12 12 5.371 12 12zm0 0M120 108c0 6.629-5.371 12-12 12s-12-5.371-12-12 5.371-12 12-12 12 5.371 12 12zm0 0M142.207 168c-4.828.004-9.625-.766-14.207-2.29l-2.527-.847 5.054-15.199 2.536.84c10.28 3.469 21.636.793 29.28-6.902l11.313 11.308c-8.324 8.375-19.64 13.086-31.449 13.09zm0 0",fill:"#ff7c00","data-original":"#834A14","data-old_color":"#FF7500"}}),s("path",{staticClass:"active-path",attrs:{d:"M432 0H312c-17.672 0-32 14.328-32 32v128c0 17.672 14.328 32 32 32h40v32c0 3.234 1.95 6.152 4.938 7.39s6.43.551 8.718-1.734L403.312 192H432c17.672 0 32-14.328 32-32V32c0-17.672-14.328-32-32-32zm16 160c0 8.836-7.164 16-16 16h-32c-2.121 0-4.156.844-5.656 2.344L368 204.688V184c0-4.418-3.582-8-8-8h-48c-8.836 0-16-7.164-16-16V32c0-8.836 7.164-16 16-16h120c8.836 0 16 7.164 16 16zm0 0","data-original":"#000000","data-old_color":"#494D5F",fill:"#484c5e"}}),s("path",{staticClass:"active-path",attrs:{d:"M312 40h72v16h-72zm0 0M400 40h32v16h-32zm0 0M312 72h120v16H312zm0 0M312 104h32v16h-32zm0 0M360 104h72v16h-72zm0 0M312 136h72v16h-72zm0 0M56 248c-30.914.035-55.965 25.086-56 56v176h16V304c.027-22.082 17.918-39.973 40-40h19.055L56.84 300.426c-1.047 2.078-1.14 4.508-.25 6.66.89 2.152 2.672 3.809 4.883 4.539l16.406 5.465-5.465 16.414c-.906 2.715-.297 5.707 1.602 7.848l64 72c.12.136.312.191.441.32.527.512 1.125.953 1.773 1.305.258.156.528.293.801.414 1.899.855 4.07.855 5.969 0 .273-.121.543-.258.8-.414.65-.352 1.247-.793 1.774-1.305.13-.129.32-.184.442-.32l64-72c1.898-2.141 2.507-5.133 1.601-7.848l-5.465-16.414 16.407-5.465c2.21-.73 3.996-2.387 4.882-4.54.891-2.151.801-4.581-.25-6.66L212.945 264H232c22.082.027 39.973 17.918 40 40h16c-.035-30.914-25.086-55.965-56-56h-40v-40.152c20.121-15.059 31.977-38.715 32-63.848 17.242.008 31.375-13.68 31.922-30.91.55-17.235-12.684-31.79-29.89-32.883l3.624-29.008c1.793-13.957-3.011-27.957-12.992-37.879C208.094 4.813 196.516.027 184.441 0H56c-4.418 0-8 3.582-8 8v16c.035 12.64 6.063 24.512 16.246 32H64v24c-17.672 0-32 14.328-32 32s14.328 32 32 32c.023 25.133 11.879 48.79 32 63.848V248zm168-144v-7.504l.063-.496c8.84.02 15.988 7.195 15.968 16.031-.015 8.836-7.195 15.989-16.031 15.969zM64 128c-8.836 0-16-7.164-16-16s7.164-16 16-16zm72 218.168L114.398 264H136zM112 248v-30.727c20.39 8.97 43.61 8.97 64 0V248zm40 16h21.602L152 346.168zm43.055 0l17.664 35.328-15.246 5.078c-4.192 1.403-6.453 5.93-5.059 10.121l6.531 19.618-38.699 43.527L190.168 264zM64 24v-8h120.441c7.836.023 15.352 3.137 20.91 8.664 6.473 6.441 9.587 15.52 8.434 24.574l-1.883 14.992-4.468-11.199C206.219 49.992 203.274 48 200 48H88c-13.254 0-24-10.746-24-24zm16 120V63.2c2.633.534 5.313.804 8 .8h106.586L208 97.543V144c0 35.348-28.652 64-64 64s-64-28.652-64-64zm17.832 120l29.922 113.672-38.7-43.527 6.532-19.618c1.394-4.191-.867-8.718-5.059-10.12l-15.246-5.079L92.945 264zm0 0","data-original":"#000000","data-old_color":"#494D5F",fill:"#484c5e"}}),s("path",{staticClass:"active-path",attrs:{d:"M192 108c0 6.629-5.371 12-12 12s-12-5.371-12-12 5.371-12 12-12 12 5.371 12 12zm0 0M120 108c0 6.629-5.371 12-12 12s-12-5.371-12-12 5.371-12 12-12 12 5.371 12 12zm0 0M142.207 168c11.809-.004 23.125-4.715 31.45-13.09l-11.313-11.308c-7.645 7.695-19 10.37-29.281 6.902l-2.536-.84-5.054 15.2 2.527.847c4.582 1.523 9.379 2.293 14.207 2.289zm0 0M136 432h16v16h-16zm0 0M456 320h-64v-16c0-8.836-7.164-16-16-16h-48c-8.836 0-16 7.164-16 16v16h-64c-4.418 0-8 3.582-8 8v144c0 4.418 3.582 8 8 8h208c4.418 0 8-3.582 8-8V328c0-4.418-3.582-8-8-8zm-128-16h48v16h-48zm120 160H256v-61.168l93.648 28.8c1.532.47 3.172.47 4.704 0l93.648-28.8zm0-77.91l-96 29.543-96-29.543V336h192zm0 0","data-original":"#000000","data-old_color":"#494D5F",fill:"#484c5e"}}),s("path",{staticClass:"active-path",attrs:{d:"M344 384h16v16h-16zm0 0","data-original":"#000000","data-old_color":"#494D5F",fill:"#484c5e"}})]),s("span",{staticClass:"heading"},[t._v("Sales "),t.isActive("sales")?s("span",{staticClass:"has-text-success",staticStyle:{"font-weight":"bolder"}},[t._v("✓")]):t._e()])])]):t._e()])])]),s("button",{staticClass:"modal-close is-large",attrs:{"aria-label":"close"},on:{click:function(e){return t.$emit("close")}}})])])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"columns"},[s("div",{staticClass:"column has-text-centered"},[s("h1",{staticClass:"is-size-5 has-text-white animated fadeInUp faster"},[t._v("Switch Role")])])])}]},a2a5:function(t,e,s){"use strict";s.r(e);var a=s("4887"),i=s("da1a");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},a5c1:function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");s("4de4"),s("caad"),s("c975"),s("d3b7"),s("ac1f"),s("3ca3"),s("5319"),s("ddb0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("4a7a")),r=a(s("1d43")),l={beforeRouteEnter:function(t,e,s){s((function(t){t.$store.getters.om_role(["supervisors"])||t.isAdmin||t.$router.replace("/settings")}))},components:{VSelect:o["default"],TemplateUploader:r["default"]},data:function(){return{newType:"",currentUser:{},isAddingUser:!1,currentRole:"",capabilities:{manage_project:{},user_access:{}},userlistClass:{},addUserClass:{},isSaving:!1,removed:"",selectedRegion:"",regions:["TM US","TM EMEA","ASEAN","TM HK","TM SG","MESCA","TM SG","NORTH ASIA","ANP","VOICE","INTERNATIONAL ALLIANCE","A2P","VOICE DATA","GSP / GITN"]}},mounted:function(){var t=this;this.currentRole=n.head(this.tabs),Promise.all([this.$store.dispatch("user-management.load"),this.$store.dispatch("om.roles.load")]).then((function(){t.$store.dispatch("isLoaded")}))},computed:{isError:function(){return this.errors.has("newType")||this.isDuplicate},isDuplicate:function(){return this.blockages.indexOf(this.newType)>-1},roles:function(){return this.$store.state.om.user_roles||[]},tabs:function(){return n.keys(this.roles)},currentRoleUsers:function(){return this.roles[this.currentRole]},users:function(){return this.$store.state["user-management"].users||[]},blockages:function(){return n.values(this.$store.state.om.configs.blockages||[])},filteredBlockages:function(){var t=this;return this.blockages.filter((function(e){return e!==t.removed}))},isAdmin:function(){return["S54377","S53977"].includes(this.$store.state.login.user.staff_id)}},methods:{addNewType:function(){var t=this;if(!this.isError){var e=n.uniq(n.concat(this.blockages,[this.newType]));this.isSaving=!0,this.$store.dispatch("om.config.update",{type:"blockages",values:e}).then((function(){t.isSaving=!1})),this.newType=""}},removeType:function(t){this.removed=t,this.$store.dispatch("om.config.update",{type:"blockages",values:this.filteredBlockages})},changeStatus:function(t,e){var s=this;this.newStatus="Active"==t?"Inactive":"Active",this.$store.dispatch("om.roles.changeStatus",{role:this.currentRole,staff_id:e,status:this.newStatus}).then((function(){s.$store.dispatch("om.roles.currentUser",s.$store.state.login.user.staff_id)})),this.roles[this.currentRole]},addUser:function(){var t=this;if("sales"==this.currentRole){if(null==this.selectedRegion)return void alert("No region has selected");this.currentUser["region"]=this.selectedRegion}this.$set(this.currentUser,"status","Active"),this.$store.dispatch("om.roles.addUser",{role:this.currentRole,user:this.currentUser}).then((function(){t.$store.dispatch("om.roles.currentUser",t.$store.state.login.user.staff_id)})),this.currentUser={}},removeUser:function(t){var e=this;this.$store.dispatch("om.roles.removeUser",{role:this.currentRole,staff_id:t}).then((function(){e.$store.dispatch("om.roles.currentUser",e.$store.state.login.user.staff_id)}))},showAddUser:function(){this.userlistClass="animated fadeOutLeft faster float",this.addUserClass="animated fadeInRight faster",this.isAddingUser=!0},closeAddUser:function(){this.userlistClass="animated fadeInLeft faster",this.addUserClass="animated fadeOutRight faster float",this.isAddingUser=!0}},filters:{formatLabel:function(t){return t.replace("_"," ")}}};e["default"]=l,t.exports=e.default,t.exports.default=e.default},a60e:function(t,e,s){"use strict";s.r(e);var a=s("8e92"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},a7bf:function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");s("99af"),s("b0c0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("c1df")),r=a(s("0ee1")),l=a(s("350b")),c=(s("0993"),{components:{TableTask:r["default"],QuickviewTask:l["default"]},data:function(){return{isLoading:!1,results:[],users:[],task:!1,newkeyword:"",startDate:(0,o["default"])(),endDate:(0,o["default"])()}},mounted:function(){var t=this;this.doSearch(),this.$store.dispatch("om.processors.list").then((function(e){t.users=e}))},watch:{keyword:function(){this.doSearch()}},computed:{keyword:function(){return this.$route.query.keyword},newStartDate:function(){return this.$route.query.startDate}},methods:{searchMore:function(){this.handleSearch(this.startDate.clone().subtract(2,"weeks").format("YYYY-MM-DD"))},handleSearch:function(t){console.log(t,"dasda"),this.$store.dispatch("loading"),this.$router.push("?keyword=".concat(this.newkeyword,"&startDate=").concat(t||"")),this.doSearch()},doSearch:function(){var t=this;this.newkeyword=this.keyword,console.log("searching..."),this.$store.dispatch("om.search",{keyword:this.keyword,startDate:this.newStartDate}).then((function(e){t.isLoading=!1,t.results=e.results,t.startDate=(0,o["default"])(e.startDate),t.endDate=(0,o["default"])(e.endDate),t.$store.dispatch("isLoaded")}))},noop:function(){},isViewed:function(t){return this.task==t},getProjectName:function(t){return this.$store.state.om.projects[t].name},viewTask:function(t){this.task=t},getProcessorName:function(t){return n.pipe(n.find((function(e){return e.staff_id==t})),(function(t){return(t||{}).name}))(this.users)}}});e["default"]=c,t.exports=e.default,t.exports.default=e.default},aab4:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("section",{staticClass:"column"},[s("div",{staticClass:"columns is-centered"},[s("div",{staticClass:"column is-6"},[s("div",{staticClass:"card"},[t._m(0),s("div",{},[s("div",{staticClass:"tabs is-marginless"},[s("ul",{staticClass:"menu-list"},t._l(t.tabs,(function(e,a){return s("li",{key:a,staticClass:"is-capitalized",on:{click:function(s){t.currentRole=e}}},[s("a",{class:{"is-active bg-tm-orange":t.currentRole==e}},[t._v(t._s(t._f("formatLabel")(e)))])])})),0)]),s("div",{staticClass:"has-background-white-bis",class:t.userlistClass},[s("div",{staticClass:"card-content"},[s("div",{staticClass:"level"},[s("div",{staticStyle:{width:"100%","padding-right":"10px"}},[s("v-select",{staticClass:"select2 is-fullwidth has-background-white",staticStyle:{width:"100%"},attrs:{label:"name",options:t.users},scopedSlots:t._u([{key:"option",fn:function(e){return[s("section",[s("div",{staticClass:"level heading is-marginless"},[s("div",{staticClass:"level-left"},[s("div",{staticClass:"level-item"},[t._v(t._s(e.staff_id)+" ")])]),s("div",{staticClass:"level-right"},[e.email?s("div",{staticClass:"level-item"},[t._v(t._s(e.email)+"\n                                                        ")]):s("div",{staticClass:"level-item has-text-primary has-text-weight-semibold"},[t._v("no email registered")])])]),s("span",[t._v(t._s(e.name))])])]}}]),model:{value:t.currentUser,callback:function(e){t.currentUser=e},expression:"currentUser"}})],1),"sales"==t.currentRole?s("div",{staticStyle:{width:"50%","padding-right":"10px"}},[s("v-select",{staticClass:"select2 is-fullwidth has-background-white",attrs:{options:t.regions,label:"Select an option"},model:{value:t.selectedRegion,callback:function(e){t.selectedRegion=e},expression:"selectedRegion"}})],1):t._e(),s("div",{},[s("a",{staticClass:"button is-primary",on:{click:t.addUser}},[t._v("Add user")])])])]),s("div",{staticStyle:{"padding-top":"20px"}},[s("table",{staticClass:"table is-fullwidth is-hoverable"},[s("tbody",t._l(t.currentRoleUsers,(function(e){return s("tr",{key:e.staff_id},[s("td",[s("span",{staticClass:"is-pulled-right button is-small is-danger is-outlined",on:{click:function(s){return t.removeUser(e.staff_id)}}},[t._v("Remove")]),"Active"==e.status?s("span",{staticClass:"is-pulled-right button is-small is-success is-outlined",staticStyle:{"margin-right":"2px"},on:{click:function(s){return t.changeStatus(e.status,e.staff_id)}}},[t._v(t._s(e.status))]):s("span",{staticClass:"is-pulled-right button is-small is-dark is-outlined",staticStyle:{"margin-right":"2px"},on:{click:function(s){return t.changeStatus(e.status,e.staff_id)}}},[t._v(t._s(e.status))]),t._v("\n\n                                            "+t._s(e.name)+"\n\n                                            "),"sales"==e.role?s("a",{staticClass:"has-text-info has-text-weight-semibold"},[t._v("( "+t._s(e.region)+" )")]):t._e(),s("br"),e.email?s("span",{staticClass:"heading"},[t._v("( "+t._s(e.staff_id)+" ) "+t._s(e.email))]):s("span",{staticClass:"heading has-text-primary has-text-weight-semibold"},[t._v("no\n                                                email registered")])])])})),0)])])])])])]),s("div",{staticClass:"column"},[s("div",{staticClass:"card",staticStyle:{"margin-bottom":"25px"}},[t._m(1),s("div",{staticClass:"card-content"},[s("template-uploader")],1)]),s("div",{staticClass:"card"},[t._m(2),s("table",{staticClass:"table is-fullwidth is-marginless"},[s("tbody",t._l(t.filteredBlockages,(function(e,a){return s("tr",{key:a},[s("td",[s("a",{staticClass:"is-pulled-right delete",on:{click:function(s){return t.removeType(e)}}}),t._v("\n                                "+t._s(e)+"\n                            ")])])})),0)]),s("div",{staticClass:"card-content has-background-light"},[s("div",{staticClass:"field has-addons"},[s("div",{staticClass:"control "},[s("input",{directives:[{name:"validate",rawName:"v-validate",value:"required",expression:"'required'"},{name:"model",rawName:"v-model",value:t.newType,expression:"newType"}],staticClass:"input",class:{"is-danger":t.isError},attrs:{name:"newType",type:"text",placeholder:"New Blockage Type"},domProps:{value:t.newType},on:{input:function(e){e.target.composing||(t.newType=e.target.value)}}})]),s("div",{staticClass:"control"},[t.isSaving?s("a",{staticClass:"button is-primary is-loading"},[t._v("Add blockage type")]):s("a",{staticClass:"button is-primary",on:{click:t.addNewType}},[t._v("Add blockage type")])])])])])])])])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card-header has-background-light"},[s("p",{staticClass:"card-header-title"},[t._v("Access Controls")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card-header has-background-light"},[s("p",{staticClass:"card-header-title"},[t._v("Jobsheet Template")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card-header has-background-light"},[s("p",{staticClass:"card-header-title"},[t._v("Blockage Types")])])}]},ab66:function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");s("b0c0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("0ee1")),r=a(s("350b")),l={components:{TableTask:o["default"],QuickviewTask:r["default"]},data:function(){return{tasks:[],users:[],task:!1}},computed:{processor_id:function(){return this.$route.params.staff_id}},methods:{loadData:function(){var t=this;this.$store.dispatch("om.processors.list").then((function(e){t.users=e})),this.$store.dispatch("om.processors.workload",this.processor_id).then((function(e){t.tasks=e,t.$store.dispatch("isLoaded")}))},noop:function(){},isViewed:function(t){return this.task==t},getProjectName:function(t){return this.$store.state.om.projects[t].name},viewTask:function(t){this.task=t},getProcessorName:function(t){return n.pipe(n.find((function(e){return e.staff_id==t})),(function(t){return(t||{}).name}))(this.users)}},mounted:function(){this.loadData()},watch:{processor_id:function(){this.loadData()}}};e["default"]=l,t.exports=e.default,t.exports.default=e.default},ac33:function(t,e,s){"use strict";s.r(e);var a=s("8bcf"),i=s("0b0a");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);s("9984");var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"7b8e0d74",null);e["default"]=r.exports},ac74:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("a",{staticClass:"button is-small is-outlined is-fullwidth",on:{click:t.toggleModal}},[t._v("Put on hold")]),t.showModal?s("div",{staticClass:"modal is-active"},[s("div",{staticClass:"modal-background"}),s("div",{staticClass:"modal-content"},[s("div",{staticClass:"card"},[s("div",{staticClass:"card-content"},[s("form",{on:{submit:function(e){return e.preventDefault(),t.handleReject(e)}}},[s("div",{staticClass:"columns  has-text-left"},[s("div",{staticClass:"column"},[s("div",{staticClass:"field"},[s("label",{staticClass:"label"},[t._v("Put on hold until: "+t._s(t.calendarData.selectedDate))]),s("div",{staticClass:"control card calendar",class:{"is-danger":t.errors.has("duedate")}},[s("FunctionalCalendar",{directives:[{name:"validate",rawName:"v-validate",value:"required",expression:"'required'"}],staticClass:"is-danger",attrs:{name:"duedate",sundayStart:!0,"date-format":"dd/mm/yyyy","is-date-picker":!0,transition:!1,isModal:!1},model:{value:t.calendarData,callback:function(e){t.calendarData=e},expression:"calendarData"}})],1)])]),s("div",{staticClass:"column"},[s("div",{staticClass:"field"},[s("label",{staticClass:"label"},[t._v("Reason")]),s("div",{staticClass:"control"},[s("textarea",{directives:[{name:"model",rawName:"v-model",value:t.reason,expression:"reason"},{name:"validate",rawName:"v-validate",value:"required",expression:"'required'"}],staticClass:"textarea",class:{"is-danger":t.errors.has("reason")},attrs:{name:"reason",rows:"10",placeholder:"Explain why you put this request on hold"},domProps:{value:t.reason},on:{input:function(e){e.target.composing||(t.reason=e.target.value)}}})])])])]),s("div",{staticClass:"field"},[s("div",{staticClass:"control"},[s("div",{staticClass:"buttons"},[s("button",{staticClass:"button is-dark",attrs:{type:"submit"}},[t._v("Put on hold")]),s("a",{staticClass:"button is-text ",on:{click:function(e){t.showModal=t.showCal=!1}}},[t._v("Cancel")])])])])])])])]),s("button",{staticClass:"modal-close is-large",attrs:{"aria-label":"close"},on:{click:function(e){t.showModal=!1}}})]):t._e()])},i=[]},ac7e:function(t,e,s){"use strict";var a=s("e3a6"),i=s.n(a);i.a},b133:function(t,e,s){"use strict";var a=s("4ea4");Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("0179"));s("c4fc");var n={props:["data","columns"],data:function(){return{spreadsheet:!1,options:{data:this.data||[[]],columns:[{type:"text",title:"Car",width:"120px"},{type:"text",title:"Car",width:"120px"},{type:"dropdown",title:"Make",width:"250px",source:["Alfa Romeo","Audi","Bmw","Honda"]},{type:"text",title:"Car",width:"120px"},{type:"calendar",title:"Available",width:"250px"},{type:"text",title:"Car",width:"120px"},{type:"text",title:"Car",width:"120px"},{type:"text",title:"Car",width:"120px"},{type:"text",title:"Car",width:"120px"},{type:"text",title:"Car",width:"120px"},{type:"text",title:"Car",width:"120px"},{type:"text",title:"Car",width:"120px"},{type:"text",title:"Car",width:"120px"},{type:"dropdown",title:"Make",width:"250px",source:["Alfa Romeo","Audi","Bmw","Honda"]},{type:"calendar",title:"Available",width:"250px"},{type:"checkbox",title:"Stock",width:"80px"},{type:"numeric",title:"Price",width:"100px",mask:"$ #,###.00",decimal:"."},{type:"color",title:"last",width:"100px",render:"square"}]}}},computed:{columnsx:function(){return this.$store.getters.om_requestTypes}},mounted:function(){this.spreadsheet=(0,i["default"])(this.$refs.spreadsheet,this.options)}};e["default"]=n,t.exports=e.default,t.exports.default=e.default},b172:function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");s("99af"),s("498a"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("c1df")),r=a(s("3f9b")),l=a(s("0916")),c=a(s("cbc3")),d=a(s("26d3")),u=a(s("3463")),f={components:{NetworkDiagramUpload:c["default"],Blockage:r["default"],Cancellation:l["default"],PatchFeedback:u["default"]},props:["task"],data:function(){return{activeTab:0,comments:[],loading:!0,comment:"",showCommentForm:!1,isSaving:!1,showActions:!1,showBlockage:!1,showCancellation:!1,showReraise:!1,showPatchFeedback:!1,patched:!1}},watch:{task:function(t){this.loadComments(),this.comment="",this.activeTab=0},showCommentForm:function(t){var e=this;t&&(this.comment="",this.$nextTick((function(){e.$refs.comment.focus()})))}},computed:{details:function(){return this.$store.getters.om_taskDetails(this.task.details)},canBlock:function(){return!!this.task&&(this.$store.getters.om_role(["supervisors","processors","executives"])&&(0,d["default"])(this.task).canTransTo("BLOCKED"))},canCancel:function(){return!!this.task&&(this.$store.getters.om_role(["supervisors","processors","executives"])&&((0,d["default"])(this.task).canTransTo("CANCELLED")||(0,d["default"])(this.task).canTransTo("CANCEL_SUBMITTED")))},canReraise:function(){return!!this.task&&(this.$store.getters.om_role(["processors"])&&(0,d["default"])(this.task).canTransTo("RERAISED"))},canUpdateFeedback:function(){return this.$store.getters.om_canPatchFeedback(this.task)},hasAction:function(){return this.canBlock||this.canCancel||this.canReraise||this.canUpdateFeedback},feedback:function(){return this.patched?n.omit(["logs"],this.patched.feedback):n.omit(["logs"],this.task.feedback)}},methods:{handleUpdateFeedback:function(){this.$router.push("/tasks/".concat(this.task.TaskID,"/patch"))},handleShowReraise:function(){this.$router.push("/tasks/".concat(this.task.TaskID))},onpatched:function(t){var e=this;this.showPatchFeedback=!1,this.patched=t,this.$store.dispatch("om.comments.post",{PK:"".concat(this.task.PK,".").concat(this.task.SK),type:"Feedback Patched",message:"Order no and service no patched.\n\nOld order no : ".concat(t.feedback.old_order_no,"\nNew order no : ").concat(t.feedback.order_no,"\n\nOld service ID : ").concat(t.feedback.old_service_id,"\nNew service ID : ").concat(t.feedback.service_id,"\n")}).then((function(){e.loadComments(!0)})),alert("Data patched")},oncancelled:function(){this.$emit("blocked"),this.$emit("close")},markAsBlocked:function(){this.showBlockage=!0},onblocked:function(){this.$emit("blocked"),this.$emit("close")},loadComments:function(t){var e=this;t||(this.loading=!0),this.task.PK&&this.task.SK&&this.$store.dispatch("om.comments.get","".concat(this.task.PK,".").concat(this.task.SK)).then((function(t){e.loading=!1,e.comments=t}))},postComment:function(){var t=this;this.isSaving=!0,this.$store.dispatch("om.comments.post",{PK:"".concat(this.task.PK,".").concat(this.task.SK),message:this.comment.trim()}).then((function(){t.isSaving=!1,t.showCommentForm=!1,t.loadComments(!0)}))},isBlocked:function(t){return t.details.comment_type&&"notes"!=t.details.comment_type},mapValue:function(t,e){var s={project:function(t){return t.id},TaskID:function(t){return n.take(12,t)}},a=s[t]||n.identity;return a(e)}},filters:{diffNow:function(t){return(0,o["default"])(t).fromNow()},dateFormat:function(t){return(0,o["default"])(t).format("DD MMM YYYY, hh:mm A")}}};e["default"]=f,t.exports=e.default,t.exports.default=e.default},b2b5:function(t,e,s){"use strict";s.r(e);var a=s("d529"),i=s("1b52");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},b44b:function(t,e,s){"use strict";s.r(e);var a=s("0525"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},b474:function(t,e,s){"use strict";s.r(e);var a=s("ee0c");for(var i in a)["default"].indexOf(i)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(i);var n,o,r=s("2877"),l=Object(r["a"])(a["default"],n,o,!1,null,null,null);e["default"]=l.exports},b4cd:function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");s("4de4"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("eb2d")),r={props:["isLoading","total_selected","selectAll","clearSelection","processors"],components:{Workload:o["default"]},data:function(){return{showWorkload:!1,processor_staff_id:""}},computed:{processor:function(){var t=this;return n.find((function(e){return e.staff_id==t.processor_staff_id}),this.processors)},activeProcessor:function(){return this.processors.filter((function(t){return"Active"===t.status}))}},methods:{handleWorkloadSelected:function(t){this.processor_staff_id=t.staff_id,this.showWorkload=!1},handleAssign:function(){return this.$emit("assign",this.processor)}}};e["default"]=r,t.exports=e.default,t.exports.default=e.default},b5ac:function(t,e,s){"use strict";s.r(e);var a=s("105f"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},b5d9:function(t,e,s){"use strict";var a=s("fc74"),i=s.n(a);i.a},b6db:function(t,e,s){"use strict";s.r(e);var a=s("05d1"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},b7d4:function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");s("c975"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("793c")),r={props:["records","jobsheet_id"],components:{SelectDropfile:o["default"]},data:function(){return{rid:0,isVerified:{},bypass:!1,drop_id:!1,isLoading:!1}},computed:{showFloat:function(){return this.scrollY>100},scrollY:function(){return this.$store.state.scrollY},currentRecord:function(){return this.records[this.rid]||{}},total_reviewed:function(){return n.pipe(n.values,n.sum)(this.isVerified)},noPrevRecord:function(){return 0==this.rid},noNextRecord:function(){return this.rid>=this.records.length-1},dropfiles:function(){return this.$store.state.om.dropfile.files},currentRecordTemplate:function(){var t=this.$store.getters.om_taskTemplate(this.currentRecord.RequestType);return n.pipe(n.indexBy(n.identity),n.map((function(){return"<MISSING>"})))(t)},displayedRecord:function(){return n.mergeAll([this.currentRecordTemplate,this.currentRecord])}},watch:{rid:function(){window.scrollTo(0,0)}},mounted:function(){this.loadDropfiles(),window.addEventListener("keyup",this.handleKeyevent)},beforeDestroy:function(){window.removeEventListener("keyup",this.handleKeyevent)},methods:{invalidRow:function(t,e){var s=["CustomerName","TaskID","RequestType"];return s.indexOf(t)>-1?"ok":"<MISSING>"==e?"has-background-warning":this.currentRecordTemplate[t]?"ok":"has-background-warning"},checkHash:function(t){return this.$store.getters.om_taskCheck({TaskID:t.TaskID,details:t})},handleKeyevent:function(t){39==t.keyCode&&this.nextRecord(),37==t.keyCode&&this.prevRecord()},dropfileSelected:function(t){this.drop_id=t},loadDropfiles:function(){this.$store.dispatch("om.dropfile.all")},verified:function(t){this.$set(this.isVerified,t,!0)},prevRecord:function(){this.rid--,this.rid<0&&(this.rid=0)},nextRecord:function(){this.rid++,this.rid>=this.records.length&&(this.rid=this.records.length-1)},handleAccept:function(){(this.total_reviewed==this.records.length||this.bypass)&&this.drop_id&&(this.isLoading=!0,this.$emit("accept",this.drop_id))}},filters:{shortKey:function(t){return n.take(12,t)}}};e["default"]=r,t.exports=e.default,t.exports.default=e.default},b85e:function(t,e,s){"use strict";var a=s("dbce"),i=s("4ea4");s("b0c0"),s("d3b7"),s("4795"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0,s("96cf");var n=i(s("1da1")),o=a(s("b17e")),r=function(t){return new Promise((function(e){setTimeout(e,t)}))},l={data:function(){return{dropzoneStatus:!1,file:!1,filename:!1,message:""}},computed:{tpl:function(){return this.$store.state.om.configs.template?{version:this.$store.state.om.configs.template.version,file:{SK:"attachment.template.template.config",details:{filename:this.$store.state.om.configs["attachment.template.template.config"].filename}}}:{version:0,file:{details:{}}}}},methods:{downloadTemplate:function(){var t=this;return(0,n["default"])(regeneratorRuntime.mark((function e(){var s;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$store.dispatch("om.attachments.download",t.tpl.file);case 2:s=e.sent,window.open(s,"_BLANK");case 4:case"end":return e.stop()}}),e)})))()},handleDropover:function(){this.dropzoneStatus="over"},handleFileChange:function(t){var e=this;console.log("drop!"),t.stopPropagation(),t.preventDefault(),t.dataTransfer&&t.dataTransfer.files&&(this.file=o.head(t.dataTransfer.files),this.filename=this.file.name),!t.dataTransfer&&t.target&&t.target.files&&(this.file=o.head(t.target.files),this.filename=this.file.name),this.dropzoneStatus=!1,this.dropzoneStatus="uploading",this.$store.dispatch("om.dropfile.drop",{file:this.file}).then((function(){return e.dropzoneStatus="uploaded",e.$emit("uploaded",e.file),r(1e3)})).then((function(){e.dropzoneStatus=!1}))["catch"]((function(t){return e.message="".concat(t),e.dropzoneStatus="error",r(2e3).then((function(){e.dropzoneStatus=!1}))}))},handleDropleaves:function(){this.dropzoneStatus=!1}}};e["default"]=l,t.exports=e.default,t.exports.default=e.default},ba14:function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticStyle:{"padding-top":"58px"}},[s("aside",{staticClass:"menu sidebarmenu"},[s("ul",{staticClass:"menu-list"},[s("li",{key:"dashboard",staticClass:"animated zoomIn faster"},[s("router-link",{staticClass:"navbar-item tooltip is-tooltip-right",attrs:{"data-tooltip":"Dashboard",to:"/dashboard"}},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"}})])])],1),s("feature-flag",[s("li",{key:"masterlist",staticClass:"animated zoomIn faster"},[s("router-link",{staticClass:"navbar-item tooltip is-tooltip-right",attrs:{"data-tooltip":"Masterlist",to:"/masterlist"}},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}},[s("line",{attrs:{x1:"8",y1:"6",x2:"21",y2:"6"}}),s("line",{attrs:{x1:"8",y1:"12",x2:"21",y2:"12"}}),s("line",{attrs:{x1:"8",y1:"18",x2:"21",y2:"18"}}),s("line",{attrs:{x1:"3",y1:"6",x2:"3",y2:"6"}}),s("line",{attrs:{x1:"3",y1:"12",x2:"3",y2:"12"}}),s("line",{attrs:{x1:"3",y1:"18",x2:"3",y2:"18"}})])])],1)]),t.$store.getters.om_role("processors")?s("li",{key:"tasks",staticClass:"animated zoomIn faster"},[s("router-link",{staticClass:"navbar-item tooltip is-tooltip-right",attrs:{"data-tooltip":"My tasks",to:"/tasks"}},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M19 3H4.99c-1.11 0-1.98.9-1.98 2L3 19c0 1.1.88 2 1.99 2H19c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 12h-4c0 1.66-1.35 3-3 3s-3-1.34-3-3H4.99V5H19v10zm-3-5h-2V7h-4v3H8l4 4 4-4z"}})])])],1):t._e(),t.$store.getters.om_role(["executives","project_manager","supervisors"])?s("li",{key:"dropfiles",staticClass:"animated zoomIn faster"},[s("router-link",{staticClass:"navbar-item tooltip is-tooltip-right",attrs:{"data-tooltip":"Drop requests",to:"/dropfiles"}},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M19 3H4.99c-1.11 0-1.98.9-1.98 2L3 19c0 1.1.88 2 1.99 2H19c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 12h-4c0 1.66-1.35 3-3 3s-3-1.34-3-3H4.99V5H19v10zm-3-5h-2V7h-4v3H8l4 4 4-4z"}})])])],1):t._e(),t.$store.getters.om_role(["executives","project_manager","supervisors"])?s("li",{key:"job-distribution",staticClass:"animated zoomIn faster"},[s("router-link",{staticClass:"navbar-item tooltip is-tooltip-right",attrs:{"data-tooltip":"Job distributions",to:"/jobs"}},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm-4 4H9v-2h6v2zm4-8H9V5h10v2z"}})])])],1):t._e(),s("li",{key:"settings",staticClass:"animated zoomIn faster"},[s("router-link",{staticClass:"navbar-item tooltip is-tooltip-right",attrs:{"data-tooltip":"Settings",to:"/settings/config"}},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M19.43 12.98c.04-.32.07-.64.07-.98s-.03-.66-.07-.98l2.11-1.65c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.3-.61-.22l-2.49 1c-.52-.4-1.08-.73-1.69-.98l-.38-2.65C14.46 2.18 14.25 2 14 2h-4c-.25 0-.46.18-.49.42l-.38 2.65c-.61.25-1.17.59-1.69.98l-2.49-1c-.23-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64l2.11 1.65c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65c-.19.15-.24.42-.12.64l2 3.46c.**********.61.22l2.49-1c.52.4 1.08.73 1.69.98l.38 2.65c.***********.49.42h4c.25 0 .46-.18.49-.42l.38-2.65c.61-.25 1.17-.59 1.69-.98l2.49 1c.23.09.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.65zM12 15.5c-1.93 0-3.5-1.57-3.5-3.5s1.57-3.5 3.5-3.5 3.5 1.57 3.5 3.5-1.57 3.5-3.5 3.5z"}})])])],1)],1)])])},i=[],n=s("2877"),o={},r=Object(n["a"])(o,a,i,!1,null,null,null);e["default"]=r.exports},ba2a:function(t,e,s){"use strict";s.r(e);var a=s("2d96"),i=s("8bef");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},bc3d:function(t,e,s){"use strict";s.r(e);var a=s("c4c6"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},bc7d:function(t,e,s){},bd2b:function(t,e,s){"use strict";s.r(e);var a=s("672a"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},bdb0:function(t,e,s){"use strict";s.r(e);var a=s("9bf6"),i=s("9ed3");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},bf97:function(t,e,s){"use strict";s.r(e);var a=s("501c"),i=s("05e1");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},c00a:function(t,e,s){},c076:function(t,e,s){"use strict";s.r(e);var a=s("99fe"),i=s("38d6");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},c10f:function(t,e,s){"use strict";var a=s("4ea4");s("caad"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("f94e")),n={components:{SectionLayout:i["default"]},methods:{migrate:function(){confirm("are you sure want to trigger migration?")&&this.$store.dispatch("om.migrate")}},computed:{isAdmin:function(){return["S54377","S53977"].includes(this.$store.state.login.user.staff_id)}}};e["default"]=n,t.exports=e.default,t.exports.default=e.default},c192:function(t,e,s){"use strict";s.r(e);var a=s("66fe"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},c3a9:function(t,e,s){"use strict";s.r(e);var a=s("1c73"),i=s("1905");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},c431:function(t,e,s){},c4c6:function(t,e,s){"use strict";var a=s("4ea4");Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("8eea")),n=a(s("16d3")),o=a(s("6d92")),r=a(s("d9b7")),l={components:{PersonalDashboard:i["default"],SalesDashboard:r["default"],SupervisorDashboard:o["default"],ExecutiveDashboard:n["default"]},mounted:function(){this.$store.dispatch("isLoaded")},methods:{canViewDashboard:function(t){return this.$store.getters.om_role(t)}},computed:{user:function(){return this.$store.state.login.user},secureBg:function(){return this.$store.getters.secureBg}}};e["default"]=l,t.exports=e.default,t.exports.default=e.default},c4ea:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("div",{staticClass:"field"},[s("div",{staticClass:"control"},[s("a",{staticClass:"button is-fullwidth",on:{click:function(e){t.showModal=!0}}},[t._v("\n                "+t._s(t.label)+"\n            ")])])]),s("div",{staticClass:"modal",class:{"is-active":t.showModal}},[s("div",{staticClass:"modal-background"}),s("div",{staticClass:"modal-content",staticStyle:{width:"auto"}},[s("div",{staticClass:"card"},[s("div",{staticClass:"card-content"},[t._m(0),s("table",{staticClass:"table is-hoverable is-bordered is-fullwidth"},[s("tbody",[t._l(t.dropfiles,(function(e){return s("tr",{key:e.PK,staticClass:"is-clickable",on:{click:function(s){return t.selectDropfile(e)}}},[s("td",[s("div",[s("p",{staticClass:"heading"},[t._v("Dropfile Id:")]),s("p",[t._v(t._s(t._f("shortKey")(e.PK)))])])]),s("td",[s("div",[s("p",{staticClass:"heading"},[t._v("Subject:")]),s("p",[t._v(t._s(e.details.subject))])])]),s("td",[s("div",[s("p",{staticClass:"heading"},[t._v("Created By:")]),s("p",[t._v(t._s(e.CreatedBy.name))])])]),s("td",[s("div",[s("p",{staticClass:"heading"},[t._v("Created At:")]),s("p",[t._v(t._s(t._f("fromNow")(e.createdAt)))])])])])})),s("tr",{staticClass:"is-clickable",on:{click:function(e){return t.selectDropfile(!1)}}},[s("td",{attrs:{colspan:"4"}},[t._v("\n                                    No associated dropfile\n                                ")])])],2)])])])]),s("button",{staticClass:"modal-close is-large",attrs:{"aria-label":"close"},on:{click:function(e){t.showModal=!1}}})])])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"field"},[s("label",{staticClass:"label",attrs:{for:""}},[t._v("Select dropfile")])])}]},c577:function(t,e,s){"use strict";s.r(e);var a=s("b133"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},c687:function(t,e,s){"use strict";s("4de4"),s("d81d"),s("ace4"),s("d3b7"),s("498a"),s("5cc6"),s("9a8c"),s("a975"),s("735e"),s("c1ac"),s("d139"),s("3a7b"),s("d5d6"),s("82f8"),s("e91f"),s("60bd"),s("5f96"),s("3280"),s("3fcc"),s("ca91"),s("25a1"),s("cd26"),s("3c5d"),s("2954"),s("649e"),s("219c"),s("170b"),s("b39a"),s("72f7"),s("ddb0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var a=s("1146"),i=s("b17e"),n=s("58b7"),o=s("7f45");function r(t){return new Promise((function(e,s){var i=!0,n=new FileReader;n.onload=function(t){var s=t.target.result;i||(s=new Uint8Array(s));var n=a.read(s,{type:i?"binary":"array"});e(n)},i?n.readAsBinaryString(t):n.readAsArrayBuffer(f)}))}o().tz("Asia/Kuala_Lumpur");var l=function(t){return n(JSON.stringify(t))+"."+o().unix()},c=function(t){return console.log("TaskID",l(t))||l(t)},d=i.omit(["Home"]),u=function(t){return function(e){return!t[e]||i.isEmpty(t[e])}};function v(t){var e=i.pipe(d,i.mapObjIndexed((function(t,e){var s=a.utils.sheet_to_json(t,{header:1,raw:!1}),i=0,n=s[i].map((function(t){return t.trim()}));return n}))),s=e(t.Sheets);return s}function h(t){var e=i.pipe(d,i.mapObjIndexed((function(t,e){var s=a.utils.sheet_to_json(t,{header:1,raw:!1}),n=0,o=1;if(console.log("sheet #".concat(e," : data"),s),console.log("sheet #".concat(e," : isEmpty"),s[n]),!u(s,n))return[];var r=s[n].map((function(t){return t.trim()})),l=2,d=i.pipe(i.splitAt(o),(function(t){return t[o]}),i.filter((function(t){return t.length>l})))(s);return console.log("sheet #".concat(e," : header"),r),console.log("sheet #".concat(e," : rows"),d),d.map((function(t){return i.zipObj(r,t)})).map((function(t){return i.mergeAll([t,{CustomerName:t["Customer Name"],TaskID:c(t),RequestType:e}])}))})),i.values,i.flatten),s=e(t.Sheets);return console.log("formatted",i.pipe(i.map(i.keys),i.flatten,i.groupBy(i.identity),i.map((function(t){return t.length})))(s)),s}var p={read:function(t){return r(t).then((function(t){return h(t)}))},headers:function(t){return r(t).then((function(t){return v(t)}))}};e["default"]=p,t.exports=e.default,t.exports.default=e.default},cb96:function(t,e,s){"use strict";s.r(e);var a=s("82cf"),i=s("2fc9");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},cbc3:function(t,e,s){"use strict";s.r(e);var a=s("081d"),i=s("786a");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);s("00d3");var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},ccdd:function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");s("99af"),s("4de4"),s("c975"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("c1df")),r={props:["dropfile","jobsheet"],computed:{jobsheetUploaded:function(){return this.jobsheet.tasks},tasks:function(){return this.jobsheet.tasks||[]},totalTask:function(){return this.tasks.length},unassignedTasks:function(){return this.tasks.filter((function(t){return"UNASSIGNED"==t.Status}))},openTasks:function(){var t=function(t){return["UNASSIGNED","ASSIGNED","BLOCKED"].indexOf(t.Status)>-1};return this.tasks.filter(t)},allAssigned:function(){return 0==this.unassignedTasks.length},lastTaskDistribution:function(){return n.pipe(n.sort(n.descend(n.prop("updatedAt"))),n.head,n.prop("updatedAt"))(this.tasks)},steps:function(){if(!this.dropfile)return[];var t=[{label:"Email dropped",tooltip:this.dropfile.details.filename,time:this.dropfile.createdAt}];return"REJECTED"==this.dropfile.Status?(t.push({label:"Rejected",tooltip:this.dropfile.details.rejection.reason,time:this.dropfile.updatedAt,class:"is-danger"}),t.push({label:"REJECTED",isTag:!0,class:"is-danger",time:this.dropfile.updatedAt}),t):this.jobsheetUploaded?(t.push({label:"Jobsheet Uploaded",tooltip:"".concat(this.jobsheet.tasks.length," task uploaded"),time:this.jobsheet.createdAt}),this.allAssigned?(t.push({label:"Task distributed",tooltip:"".concat(this.jobsheet.tasks.length," orders distributed"),time:this.lastTaskDistribution}),this.openTasks.length>0?(t.push({label:"Ordering in progress",tooltip:"".concat(this.openTasks.length,"/").concat(this.totalTask," tasks still in progress")}),t):(t.push({label:"Tasks completed",tooltip:"".concat(this.jobsheet.tasks.length," tasks completed"),time:this.jobsheet.updatedAt}),t.push({label:"Complete",isTag:!0,time:this.jobsheet.updatedAt}),t)):(t.push({label:"Pending distribution",tooltip:"".concat(this.tasks.length-this.unassignedTasks.length,"/").concat(this.tasks.length," assigned")}),t)):(t.push({label:"Pending review"}),t)}},filters:{fromNow:function(t){return(0,o["default"])(t).fromNow()},formatDate:function(t){return(0,o["default"])(t).format("DD MMM YYYY, hh:mmA")}}};e["default"]=r,t.exports=e.default,t.exports.default=e.default},d03b:function(t,e,s){},d07e:function(t,e,s){"use strict";var a=s("d7a4"),i=s.n(a);i.a},d1fd:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"container"},[s("div",{staticClass:"columns is-multiline"},[s("div",{staticClass:"column is-3"},[s("pending-tasks")],1),t._e(),s("div",{staticClass:"column"},[s("workload")],1)])])},i=[]},d42e:function(t,e,s){"use strict";var a=s("4ea4");Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("7517")),n=a(s("5c5d")),o=a(s("fb2d")),r=a(s("e55b")),l={components:{PendingTasks:i["default"],OrderPerf:n["default"],YearPerf:o["default"],Workload:r["default"]}};e["default"]=l,t.exports=e.default,t.exports.default=e.default},d459:function(t,e,s){"use strict";s.r(e);var a=s("9164"),i=s("2912");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},d460:function(t,e,s){"use strict";s.r(e);var a=s("2065"),i=s("b6db");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);s("ac7e");var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},d529:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[t._m(0),s("div",{},[s("template-tabs"),s("iframe",{staticStyle:{width:"100%",height:"calc(100vh - 190px)"},attrs:{src:"/#/masterlist/sheet"}})],1)])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card-content"},[s("h1",{staticClass:"title"},[t._v("Masterlist")])])}]},d689:function(t,e,s){"use strict";s.r(e);var a=s("5fb3"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},d766:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("div",{staticClass:"card",staticStyle:{height:"460px"}},[s("div",{staticClass:"card-content"},[s("div",{staticClass:"hero-bosdy"},[s("span",{staticClass:"heading"},[t._v(t._s(t.title))]),s("apexchart",{attrs:{type:"donut",options:t.chartOptions,series:t.series,height:"300"}})],1)]),s("div",{staticClass:"card-footer"},[s("div",{staticClass:"card-footer-item "},[s("div",{staticClass:"has-text-centered "},[s("span",{staticClass:"heading has-text-success"},[t._v(" < 1 day ")]),s("span",{staticClass:"title is-3 has-text-success"},[t._v(t._s(t.series[0]))])])]),s("div",{staticClass:"card-footer-item"},[s("div",{staticClass:"has-text-centered"},[s("span",{staticClass:"heading has-text-info"},[t._v(" < 3 days ")]),s("span",{staticClass:"title is-3 has-text-info"},[t._v(t._s(t.series[1]))])])]),s("div",{staticClass:"card-footer-item"},[s("div",{staticClass:"has-text-centered"},[s("span",{staticClass:"heading has-text-danger"},[t._v(" > 3 days ")]),s("span",{staticClass:"title is-3 has-text-danger"},[t._v(t._s(t.series[2]))])])])])])])},i=[]},d7a4:function(t,e,s){},d974:function(t,e,s){"use strict";s.r(e);var a=s("ffdb"),i=s("11aa");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},d9b7:function(t,e,s){"use strict";s.r(e);var a=s("5a8b"),i=s("f181");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},da1a:function(t,e,s){"use strict";s.r(e);var a=s("5276"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},da46:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"container",style:t.$store.getters.secure_bg},[s("div",{staticClass:"columns"},[s("div",{staticClass:"column"},[s("router-link",{staticClass:"box",attrs:{to:"/dropfiles?status=OPEN"}},[s("div",{staticClass:"level is-mobile"},[s("div",{staticClass:"level-left"},[s("div",{staticClass:"pending-item-icon has-text-warning"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"42",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 14v-2.47l6.88-6.88c.2-.2.51-.2.71 0l1.77 1.77c.2.2.2.51 0 .71L8.47 14H6zm12 0h-7.5l2-2H18v2z"}})])]),s("div",[s("strong",[t._v("Open Requests")]),s("div",{staticClass:"help"},[t._v("Pending requests to be review")])])]),s("div",{staticClass:"level-right"},[s("span",{staticClass:"title is-1"},[t._v(t._s(t.stats.open))])])])])],1),s("div",{staticClass:"column"},[s("router-link",{staticClass:"box",attrs:{to:"/dropfiles?status=ON_HOLD"}},[s("div",{staticClass:"level is-mobile"},[s("div",{staticClass:"level-left"},[t.stats.onHoldOverdue>0?s("div",{staticClass:"pending-item-icon has-text-danger animated flash infinite"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"42",height:"42",viewBox:"0 0 24 24",fill:"none",stroke:"#FF6600","stroke-width":"3","stroke-linecap":"round","stroke-linejoin":"round"}},[s("polygon",{attrs:{fill:"#FF6600",points:"7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2"}})])]):s("div",{staticClass:"pending-item-icon has-text-primary"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"42",height:"42",viewBox:"0 0 24 24",fill:"none",stroke:"#FF6600","stroke-width":"3","stroke-linecap":"round","stroke-linejoin":"round"}},[s("polygon",{attrs:{fill:"#FF6600",points:"7.86 2 16.14 2 22 7.86 22 16.14 16.14 22 7.86 22 2 16.14 2 7.86 7.86 2"}})])]),s("div",[s("strong",[t._v("On-hold Requests")]),s("div",{staticClass:"help"},[t._v("Pending requests to be review")])])]),s("div",{staticClass:"level-right"},[s("span",{staticClass:"title is-1"},[t._v(t._s(t.stats.onHold))])])])])],1),s("div",{staticClass:"column"},[s("a",{staticClass:"box",on:{click:function(e){return t.showJobsheet("me")}}},[s("div",{staticClass:"level is-mobile"},[s("div",{staticClass:"level-left"},[s("div",{staticClass:"pending-item-icon has-text-info"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"42",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm0 4c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm6 12H6v-1.4c0-2 4-3.1 6-3.1s6 1.1 6 3.1V19z"}})])]),t._m(0)]),s("div",{staticClass:"level-right"},[s("span",{staticClass:"title is-1"},[t._v(t._s(t.myJobsheets.length))])])])])]),s("div",{staticClass:"column"},[s("a",{staticClass:"box",on:{click:function(e){return t.showJobsheet("others")}}},[s("div",{staticClass:"level is-mobile"},[s("div",{staticClass:"level-left"},[s("div",{staticClass:"pending-item-icon has-text-success"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"42",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm-2 14l-4-4 1.41-1.41L10 14.17l6.59-6.59L18 9l-8 8z"}})])]),t._m(1)]),s("div",{staticClass:"level-right"},[s("span",{staticClass:"title is-1"},[t._v(" "+t._s(t.othersJobsheets.length))])])])])])]),t.displayedJobsheets.length>0?s("div",{staticClass:"card"},[s("table-jobsheets",{attrs:{jobsheets:t.displayedJobsheets}})],1):t._e()])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("strong",[t._v("Created by Me")]),s("div",{staticClass:"help"},[t._v("Jobsheet created by Me")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("strong",[t._v("Created by Others")]),s("div",{staticClass:"help"},[t._v("Jobsheet created by Team")])])}]},da5a:function(t,e,s){"use strict";s.r(e);var a=s("4be0"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},da6b:function(t,e,s){"use strict";s.r(e);var a=s("a0a7"),i=s("26ae");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},db00:function(t,e,s){"use strict";var a=s("6b1e"),i=s.n(a);i.a},dbc6:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("section",[s("div",{staticClass:"hero bg-tm-orange is-fullheight"},[t.hasNotification?s("div",{staticClass:"notification animated  faster",class:[t.notification.type,t.animation],staticStyle:{position:"fixed",width:"100vw"}},[s("button",{staticClass:"delete"}),t._v("\n            "+t._s(t.notification.message)+"\n        ")]):t._e(),s("div",{staticClass:"hero-body"},[s("div",{staticClass:"container"},[s("div",{staticClass:"columns is-centered"},[s("div",{staticClass:"column is-4",staticStyle:{"max-width":"100%"}},[t.show?t._e():s("checking",{on:{success:function(e){return t.$emit("success")},reset:t.handleReset,error:t.onError}}),s("div",{staticClass:"card animated faster",class:{zoomIn:t.show,hide:!t.show}},[s("div",{staticClass:"card-content has-text-white"},[s("p",{staticClass:"title is-4"},[t._v("\n                                Password-less Login\n                                ")]),s("p",{staticClass:"is-6 has-text-grey"},[t._v("Enter your Login ID and we will send verification link to your email.")]),s("form",{on:{submit:function(e){return e.preventDefault(),t.handleLogin(e)}}},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.form.staff_id,expression:"form.staff_id"}],staticClass:"input",attrs:{type:"text",placeholder:"Login ID"},domProps:{value:t.form.staff_id},on:{input:function(e){e.target.composing||t.$set(t.form,"staff_id",e.target.value)}}}),s("hr"),s("button",{staticClass:"button has-no-border bg-tm-orange is-fullwidth has-text-white is-medium",class:{"is-loading":t.isLoading},attrs:{type:"submit"}},[t._v("LOGIN")])])])])],1)])])])]),s("div",{staticClass:"is-7 has-text-white",staticStyle:{position:"fixed",bottom:"0",padding:"5px"}},[s("small",[t._v("Build: "+t._s(t.build))])])])},i=[]},ddca:function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("36dc")),r={components:{TableJobsheets:o["default"]},data:function(){return{showInprogress:!1}},mounted:function(){this.loadData()},computed:{user:function(){return this.$store.state.login.user},openDropfiles:function(){return 10},jobsheetsInprogress:function(){var t=this,e=function(e){return e.CreatedBy.staff_id==t.user.staff_id};return n.pipe(n.values,n.filter((function(t){return"OPEN"==t.Status&&e(t)})))(this.$store.state.om.jobs)},stats:function(){return this.$store.state.om.stats}},methods:{showJobsheet:function(t){this.showInprogress=!0},loadData:function(){this.$store.dispatch("om.jobs.load"),this.$store.dispatch("om.stats.load")}}};e["default"]=r,t.exports=e.default,t.exports.default=e.default},de99:function(t,e,s){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var a=s("81e7"),i=(0,a.getStorage)("local"),n=i;e["default"]=n,t.exports=e.default,t.exports.default=e.default},e25b:function(t,e,s){"use strict";s.r(e);var a=s("68d7"),i=s("6616");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);s("4b10");var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,"890c3898",null);e["default"]=r.exports},e2e8:function(t,e,s){"use strict";s.r(e);var a=s("fb19"),i=s("6416");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);s("b5d9");var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},e3a6:function(t,e,s){},e411:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"container",style:t.$store.getters.secure_bg},[s("div",{staticClass:"columns"},[s("div",{staticClass:"column"},[s("router-link",{staticClass:"box",attrs:{to:"/tasks/open"}},[s("div",{staticClass:"level is-mobile"},[s("div",{staticClass:"level-left"},[s("div",{staticClass:"pending-item-icon has-text-warning"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"42",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 14v-2.47l6.88-6.88c.2-.2.51-.2.71 0l1.77 1.77c.2.2.2.51 0 .71L8.47 14H6zm12 0h-7.5l2-2H18v2z"}})])]),s("div",[s("strong",[t._v("Open Tasks")]),s("div",{staticClass:"help"},[t._v("Total task for submission")])])]),s("div",{staticClass:"level-right"},[s("span",{staticClass:"title is-1"},[t._v(t._s(t.stats.open))])])])])],1),s("div",{staticClass:"column"},[s("router-link",{staticClass:"box",attrs:{to:"/tasks/recent"}},[s("div",{staticClass:"level is-mobile"},[s("div",{staticClass:"level-left"},[s("div",{staticClass:"pending-item-icon has-text-success"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"42",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm-2 14l-4-4 1.41-1.41L10 14.17l6.59-6.59L18 9l-8 8z"}})])]),s("div",[s("strong",[t._v("Submited Today")]),s("div",{staticClass:"help"},[t._v("Order submitted today")])])]),s("div",{staticClass:"level-right"},[s("span",{staticClass:"title is-1"},[t._v(" "+t._s(t.stats.todayOrder))])])])])],1),s("div",{staticClass:"column"},[s("router-link",{staticClass:"box",attrs:{to:"/tasks/open?status=blocked"}},[s("div",{staticClass:"level is-mobile"},[s("div",{staticClass:"level-left"},[s("div",{staticClass:"pending-item-icon has-text-danger"},[s("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"42",viewBox:"0 0 24 24"}},[s("path",{attrs:{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM4 12c0-4.42 3.58-8 8-8 1.85 0 3.55.63 4.9 1.69L5.69 16.9C4.63 15.55 4 13.85 4 12zm8 8c-1.85 0-3.55-.63-4.9-1.69L18.31 7.1C19.37 8.45 20 10.15 20 12c0 4.42-3.58 8-8 8z"}})])]),s("div",[s("strong",[t._v("Blocked tasks")]),s("div",{staticClass:"help"},[t._v("Order to be hold")])])]),s("div",{staticClass:"level-right"},[s("span",{staticClass:"title is-1"},[t._v(t._s(t.stats.blocked))])])])])],1)])])},i=[]},e429:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"modal is-active"},[s("div",{staticClass:"modal-background"}),s("div",{staticClass:"modal-content"},[s("div",{staticClass:"card"},[s("div",{staticClass:"card-content"},[t.hasOrder?s("div",{staticClass:"notification"},[s("table",{staticClass:"table is-fullwidth"},[s("tbody",[s("tr",[s("th",{attrs:{c:""}},[t._v("Order No:")]),s("td",[t._v(t._s(t.task.feedback.order_no)+" ")])]),s("tr",[s("th",[t._v("Service Id:")]),s("td",[t._v(t._s(t.task.feedback.service_id)+" ")])]),t.task.feedback["OD Number"]?s("tr",[s("th",[t._v("OD No:")]),s("td",[s("a",{attrs:{href:t.task.feedback["OD Link"]}},[t._v(t._s(t.task.feedback["OD Number"]))])])]):t._e()])])]):t._e(),s("div",{staticClass:"field"},[s("div",{staticClass:"control"},[s("label",{staticClass:"label"},[t._v("Cancellation Remarks")]),s("textarea",{directives:[{name:"validate",rawName:"v-validate",value:{required:!0},expression:"{required:true}"},{name:"model",rawName:"v-model",value:t.cancellation.remarks,expression:"cancellation.remarks"}],staticClass:"textarea",class:{"is-danger":t.errors.has("remarks")},attrs:{name:"remarks",placeholder:"Notes of cancellation request"},domProps:{value:t.cancellation.remarks},on:{input:function(e){e.target.composing||t.$set(t.cancellation,"remarks",e.target.value)}}}),s("span",{staticClass:"help"},[t._v("Good description will help future you and others understand the reason.")])])])]),s("div",{staticClass:"card-content has-background-light"},[s("div",{staticClass:"level"},[s("div",{staticClass:"level-left"}),s("div",{staticClass:"level-right"},[s("div",{staticClass:"buttons"},[s("a",{staticClass:"button is-text ",on:{click:function(e){return t.$emit("close")}}},[t._v("close")]),s("a",{staticClass:"button is-danger",class:{"is-loading":t.isLoading},on:{click:t.submitcancellation}},[t._v(t._s(t.cancelWhat))])])])])])])])])},i=[]},e4d2:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("a",{staticClass:"button is-small is-danger is-outlined is-fullwidth",on:{click:function(e){t.showModal=!0}}},[t._v("Reject")]),t.showModal?s("div",{staticClass:"modal is-active"},[s("div",{staticClass:"modal-background"}),s("div",{staticClass:"modal-content"},[s("div",{staticClass:"card"},[s("div",{staticClass:"card-content"},[s("form",{on:{submit:function(e){return e.preventDefault(),t.handleReject(e)}}},[s("div",{staticClass:"field"},[s("div",{staticClass:"control"},[s("textarea",{directives:[{name:"model",rawName:"v-model",value:t.reason,expression:"reason"},{name:"validate",rawName:"v-validate",value:"required",expression:"'required'"}],staticClass:"textarea",class:{"is-danger":t.errors.has("reason")},attrs:{name:"reason",placeholder:"Reason for rejection"},domProps:{value:t.reason},on:{input:function(e){e.target.composing||(t.reason=e.target.value)}}})])]),s("div",{staticClass:"field"},[s("div",{staticClass:"control"},[s("div",{staticClass:"buttons"},[s("button",{staticClass:"button is-danger",attrs:{type:"submit"}},[t._v("Reject")]),s("a",{staticClass:"button is-text ",on:{click:function(e){t.showModal=!1}}},[t._v("Cancel")])])])])])])])]),s("button",{staticClass:"modal-close is-large",attrs:{"aria-label":"close"},on:{click:function(e){t.showModal=!1}}})]):t._e()])},i=[]},e54a:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"container section"},[s("div",{staticClass:"card"},[s("div",{staticClass:"card-content"},[s("form",{on:{submit:function(e){return e.preventDefault(),t.handleSearch()}}},[s("div",{staticClass:"field has-addons"},[s("div",{staticClass:"control is-expanded"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.newkeyword,expression:"newkeyword"}],staticClass:"input is-rounded is-large ",attrs:{type:"text",placeholder:"Order ID"},domProps:{value:t.newkeyword},on:{input:function(e){e.target.composing||(t.newkeyword=e.target.value)}}})]),s("div",{staticClass:"control"},[s("button",{staticClass:"button is-link is-large is-rounded",class:{"is-loading":t.isLoading},attrs:{type:"submit",href:"#"}},[t._v("Search")])])]),s("div",{staticClass:"field is-grouped"},[s("div",{staticClass:"control"},[s("div",{staticClass:"field"},[t._v("\n                            Search in records between "),s("strong",[t._v(t._s(t.startDate.format("YYYY-MM-DD")))]),t._v(" to "),s("strong",[t._v(t._s(t.endDate.format("YYYY-MM-DD")))])])]),s("div",{staticClass:"control"},[s("div",{staticClass:"field"},[s("a",{staticClass:"button is-small is-primary",on:{click:t.searchMore}},[t._v("Search More")])])])])])])]),s("div",{staticClass:"card"},[t.results.length>0?s("table-task",{attrs:{filteredTasks:t.results,getProjectName:t.getProjectName,isViewed:t.isViewed,selectedSessionTasks:t.noop,getProcessorName:t.getProcessorName,viewTask:t.viewTask}}):t._e(),0==t.results.length?s("div",{staticClass:"card-content"},[t._v("\n            No result found\n        ")]):t._e()],1),s("quickview-task",{attrs:{task:t.task},on:{close:function(e){t.task=!1}}})],1)},i=[]},e55b:function(t,e,s){"use strict";s.r(e);var a=s("232d"),i=s("70df");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},e757:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("div",{staticClass:"modal is-active"},[s("div",{staticClass:"modal-background"}),s("div",{staticClass:"modal-content animated fadeInUp faster"},[s("div",{staticClass:"card "},[s("div",{staticClass:"card-header"},[s("p",{staticClass:"card-header-title"},[t._v("New project")]),s("a",{staticClass:"card-header-icon",attrs:{"aria-label":"more options"},on:{click:function(e){return t.$emit("close")}}},[t._m(0)])]),s("div",{staticClass:"card-content"},[s("div",{staticClass:"field"},[s("div",{staticClass:"control"},[s("input",{directives:[{name:"validate",rawName:"v-validate",value:{required:!0},expression:"{required:true}"},{name:"model",rawName:"v-model",value:t.form.name,expression:"form.name"}],ref:"name",staticClass:"input",class:{"is-danger":t.errors.has("name")},attrs:{"data-vv-as":"Project name",type:"text",placeholder:"Project name",name:"name"},domProps:{value:t.form.name},on:{input:function(e){e.target.composing||t.$set(t.form,"name",e.target.value)}}})])])]),s("div",{staticClass:"card-content has-background-light"},[s("div",{staticClass:"level"},[s("div",{staticClass:"level-left"}),s("div",{staticClass:"level-right"},[s("div",{staticClass:"buttons"},[s("a",{staticClass:"button is-text has-text-grey ",on:{click:function(e){return t.$emit("close")}}},[t._v("Cancel")]),s("a",{staticClass:"button is-primary",on:{click:t.handleSubmit}},[t._v("Create")])])])])])])])])])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("span",{staticClass:"icon"},[s("i",{staticClass:"delete",attrs:{"aria-hidden":"true"}})])}]},e7f7:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return t.selectedTask?s("div",[s("div",{staticClass:"card"},[s("div",{staticClass:"card-content has-background-light"},[s("span",{staticClass:"is-pulled-right"},[s("span",{staticClass:"tag",class:{"is-info":"ASSIGNED"==t.selectedTask.status,"is-warning":"UNASSIGNED"==t.selectedTask.status,"is-danger":"BLOCKED"==t.selectedTask.status,"is-dark":"CANCEL_SUBMITTED"==t.selectedTask.status,"is-success":"SUBMITTED"==t.selectedTask.status||"COMPLETED"==t.selectedTask.status}},[t._v(t._s(t.selectedTask.status))])]),s("p",[s("span",{staticClass:"heading"},[t._v(" "+t._s(t.selectedTask.GS2)+" ")]),s("span",{staticClass:"title is-6"},[t._v(" "+t._s(t.selectedTask.details["Customer Name"])+" ")]),s("br"),t._v("\n                "+t._s(t.selectedTask["Product/Service"])+" "),s("br")]),s("div",{staticClass:"tags",staticStyle:{"margin-top":"20px"}},[s("span",{staticClass:"tag is-link"},[t._v(t._s(t.selectedTask.RequestType))]),s("span",{staticClass:"tag is-link"},[t._v(t._s(t.selectedTask["Segment/Unit"]||"TODO Segment/Unit"))])])]),s("div",{staticClass:"card-content"},t._l(t.details,(function(e,a){return s("div",{key:a,staticClass:"columns"},[s("div",{staticClass:"column is-4 has-text-right"},[t._v(t._s(a))]),s("div",{staticClass:"column is-8"},[s("div",{staticClass:"field is-expanded"},[s("div",{staticClass:"field"},[s("div",{staticClass:"control is-expanded has-icons-right"},[s("div",{staticClass:"box",staticStyle:{padding:"5px 10px"}},[s("a",{staticClass:"button is-small is-dark is-pulled-right",class:t.copyClass(a),staticStyle:{"font-size":"10px"},on:{click:function(s){return t.copyToCb(e,a)}}},[t.copyStates[a]?t._e():s("span",[t._v("Copy")]),t.copyStates[a]?s("span",[t._v("Copied")]):t._e()]),t._v("\n                                    "+t._s(e||" ")+"\n                                ")])])])])])])})),0),s("div",{staticClass:"card-content has-background-light"},[s("p",{staticClass:"is-size-7"},[t._v("\n                Created by "+t._s(t.selectedTask.CreatedBy.name)+" on "+t._s(t._f("diffNow")(t.selectedTask.createdAt))+"\n            ")])])])]):t._e()},i=[]},e9a4:function(t,e,s){"use strict";s.r(e);var a=s("339f"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},ea37:function(t,e,s){"use strict";var a=s("dbce"),i=s("4ea4");s("99af"),s("c975"),s("0d03"),s("b0c0"),s("ac1f"),s("5319"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0,s("96cf");var n=i(s("1da1")),o=a(s("b17e")),r=i(s("c1df")),l=i(s("17b2")),c=i(s("7530")),d=i(s("2948")),u=i(s("0c96")),f=i(s("044d")),v=i(s("a2a5")),h=s("479b"),p={beforeRouteEnter:function(t,e,s){s((function(t){t.$store.getters.om_role(["executives","project_manager","supervisors"])||t.$router.replace("/")}))},components:{Sorter:d["default"],holdFile:c["default"],DebugTool:v["default"],rejectFile:l["default"],DropStatus:u["default"],AnimatedNumber:f["default"],FunctionalCalendar:h.FunctionalCalendar},data:function(){return{filterKeyword:"",sortedData:[],isLoading:!1,activeTab:"OPEN",showModal:!1,reason:"",showCal:!1,calendarData:"",today:new Date}},mounted:function(){this.$route.query.status&&this.setActive(this.$route.query.status),this.$store.dispatch("isLoaded"),this.$store.dispatch("om.dropfile.all"),this.$store.dispatch("om.dropfile.onholds")},methods:{toggleModal:function(t){this.drop_id=t,this.showModal=!0,this.calendarData=(0,r["default"])()},submitRevise:function(){var t=this;this.$validator.validateAll().then((function(e){e&&(null!=t.calendarData.selectedDate?(t.$store.dispatch("on.dropfile.hold",{drop_id:t.drop_id,reason:t.reason,duedate:t.calendarData.selectedDate}),t.showModal=!1):alert("No date selected"))}))},releaseHold:function(t){confirm("Are you sure want to release this file?")&&this.$store.dispatch("om.dropfile.release",t).then((function(){alert("The file released")}))},setActive:function(t){this.activeTab=t},isActive:function(t){return this.activeTab==t},loadMore:function(){var t=this;this.isLoading=!0,this.$store.dispatch("om.dropfile.all").then((function(){return t.isLoading=!1}))},onSorted:function(t){this.sortedData=t},isRejected:function(t){return"rejected"==t.status},isPending:function(t){return"open"==t.status},downloadFile:function(t){var e=this;return(0,n["default"])(regeneratorRuntime.mark((function s(){var a;return regeneratorRuntime.wrap((function(s){while(1)switch(s.prev=s.next){case 0:return s.next=2,e.$store.dispatch("om.dropfile.download",{drop_id:t.drop_id});case 2:a=s.sent,window.open(a,"_BLANK");case 4:case"end":return s.stop()}}),s)})))()},handleReject:function(t){var e=t.drop_id,s=t.reason;this.$store.dispatch("om.dropfile.reject",{drop_id:e,reason:s})},handleOnHold:function(t){var e=t.drop_id,s=t.reason,a=t.duedate;this.$store.dispatch("on.dropfile.hold",{drop_id:e,reason:s,duedate:a})},jumpToJobsheet:function(t){var e="/jobs/projects/".concat(t.details.uploading.project_id,"/").concat(t.PK);return e}},computed:{overDueHolds:function(){return o.pipe(o.filter((function(t){return"ON_HOLD"==t.Status&&t.details.on_hold.isOverdue})))(this.files)},hasOverdueHolds:function(){return o.keys(this.overDueHolds).length>0},stats:function(){return o.pipe(o.values,o.groupBy(o.prop("Status")),o.map((function(t){return t.length})))(this.files)},filteredFiles:function(){var t=this;return o.pipe(o.filter((function(e){return e.Status==t.activeTab&&JSON.stringify(e).toLowerCase().indexOf(t.filterKeyword.toLowerCase())>-1})),o.values)(this.files)},user:function(){return this.$store.state.login.user},files:function(){return this.$store.state.om.dropfile.files},nofile:function(){return 0==o.keys(this.files).length},lastKey:function(){return this.$store.state.om.dropfile.lastKey}},filters:{fromNow:function(t){return(0,r["default"])(t).fromNow()},formatDate:function(t){return(0,r["default"])(t).format("DD MMM YYYY, hh:mm A")},shortKey:function(t){return o.take(12,t||"")},dropBy:function(t){return(t.CreatedBy||{}).name||t.GS2}}};e["default"]=p,t.exports=e.default,t.exports.default=e.default},ea75:function(t,e,s){"use strict";s.r(e);var a=s("b7d4"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},eb2d:function(t,e,s){"use strict";s.r(e);var a=s("82a6"),i=s("a154");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},eb4f:function(t,e,s){"use strict";s.r(e);var a=s("55d3"),i=s("6e86");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},eb90:function(t,e,s){"use strict";var a=s("dbce"),i=s("4ea4");s("99af"),s("4de4"),s("c975"),s("d81d"),s("b0c0"),s("d3b7"),s("ac1f"),s("3ca3"),s("5319"),s("ddb0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("3835"));s("96cf");var o=i(s("1da1")),r=i(s("ade3")),l=(i(s("be94")),i(s("bc3a"))),c=s("11c1"),d=i(s("7f45")),u=a(s("b17e")),f=(i(s("2b0e")),i(s("1e3a"))),v=s("58b7");d["default"].tz.setDefault("Asia/Kuala_Lumpur");var h="staging",p=function(t){return"production"==h?"".concat("https://dlqawfi443.execute-api.ap-southeast-1.amazonaws.com/production").concat(t):"".concat("https://pydyfgbm33.execute-api.us-east-1.amazonaws.com/staging").concat(t)},m={modules:{dropfile:f["default"]},state:{stats:{},configs:!1,current:{file:"",tasks:!1,project:!1},ui:{project:{newForm:!1}},loadings:{projects:!1},jobs:{},projects:{},unassigned:[],mytasks:{},comments:{},processors:[],user_roles:!1,currentUserRoles:!1,currentUserActiveRole:!1,attachments:{}},getters:{om_taskCheck:function(t,e){return function(t){var s=u.pipe(e.om_taskDetails,u.omit(["RequestType","project","TaskID"]),u.map((function(t){return""==t?void 0:t})))(t.details);return t.TaskID.replace(/\..*/,"")==v(JSON.stringify(s))}},om_requestTypes:function(t){return t.configs["template.columns.".concat((t.configs.template||{}).version)]},om_taskTemplate:function(t){return function(e){var s=t.configs.template.version;return u.view(u.lensPath(["configs","template.columns.".concat(s),e]),t)||[]}},om_taskDetails:function(t){return function(e){if(!e)return[];var s=t.configs.template.version,a=u.view(u.lensPath(["configs","template.columns.".concat(s),e.RequestType]),t)||[],i=u.concat(["TaskID","RequestType"],a);return i?u.mergeAll(i.map((function(t){return(0,r["default"])({},t,e[t])}))):e}},om_comments:function(t){return function(e,s){return u.pipe(u.filter((function(t){return t.resource_id==s&&t.resource_type==e})),u.values,u.sort(u.descend(u.prop("createdAt"))))(t.comments)}},om_assignedProjects:function(t){return u.pipe(u.flatten,u.groupBy(u.prop("GS2")),u.map(u.length))(t.mytasks||[])},om_role:function(t){return function(e){return u.flatten([e]).indexOf(t.currentUserActiveRole)>-1}},om_has_role:function(t){return function(e){return u.intersection(u.flatten([e]),t.currentUserRoles).length>0}},om_canPatchFeedback:function(t,e){return function(t){var a=s("26d3")(t);return(a.canTransTo("CANCEL_SUBMITTED")||a.isIn("CANCEL_SUBMITTED"))&&e.om_role(["supervisors"])}},api:function(t){return function(t){return p(t)}}},mutations:{SET_STATS:function(t,e){t.stats=e},UPDATE_UI:function(t,e){t.ui=Object.assign({},t.ui,e)},UPDATE_PROJECTS:function(t,e){t.projects=e},CURRENT_PROJECT:function(t,e){t.proj.project=e},SET_UNASSIGNED:function(t,e){t.unassigned=e},MY_TASKS:function(t,e){t.mytasks=e},ADD_COMMENTS:function(t,e){t.comments=u.mergeAll([t.comments,u.indexBy(u.prop("CommentID"),e)])},SET_PROCESSORS:function(t,e){t.processors=e},SET_JOB:function(t,e){t.jobs=u.mergeAll([t.jobs,(0,r["default"])({},e.request_id,e)])},SET_JOBS:function(t,e){t.jobs=u.indexBy(u.prop("PK"),e)},START_LOADING:function(t,e){t.loadings[e]=!0},STOP_LOADING:function(t,e){t.loadings[e]=!1},SET_USERROLE:function(t,e){t.user_roles=Object.assign({processors:{},executives:{},supervisors:{},project_manager:{},sales:{}},e)},ADD_USERROLE:function(t,e){var s=e.role,a=e.user;t.user_roles[s][a.staff_id]=a},REMOVE_USERROLE:function(t,e){var s=e.role,a=e.staff_id;t.user_roles[s]=u.omit([a],t.user_roles[s])},SET_CURRENTUSER_ROLES:function(t,e){t.currentUserRoles=e},SET_CURRENTUSER_ROLE:function(t,e){t.currentUserActiveRole=e},SET_CONFIG:function(t,e){t.configs=e},SET_ATTACHMENT:function(t,e){var s=e.attachment_type,a=e.key,i=e.attachments,n=u.lensPath([s,a]);t.attachments=u.set(n,i,t.attachments)}},actions:{"om.stats.load":function(t){return l["default"].get(p("/obase/stats")).then((function(e){return t.commit("SET_STATS",e.data.stats),e.data.stats}))},"om.reports.load":function(t){return l["default"].get(p("/obase/reports")).then((function(t){return t.data.data}))},"om.init":function(t){t.dispatch("om.project.list"),t.dispatch("om.processors.list"),t.dispatch("om.config.load")},"om.search":function(t,e){var s=e.keyword,a=e.startDate;return l["default"].post(p("/obase/search"),{keyword:s,startDate:a}).then((function(t){return t.data}))},"om.config.load":function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t.state.configs&&!e||t.commit("SET_CONFIG",l["default"].get(p("/obase/config")).then((function(e){t.commit("SET_CONFIG",e.data.configs)})))},"om.config.update":function(t,e){var s=e.type,a=e.values;return l["default"].post(p("/obase/config"),{type:s,values:a}).then((function(){return t.dispatch("om.config.load",!0)}))},"om.ui.update":function(t,e){t.commit("UPDATE_UI",e)},"om.jobsheet.delete":function(t,e){return l["default"]["delete"](p("/obase/jobs/".concat(e)))},"om.jobsheet.load":function(t,e){return t.dispatch("om.jobs.find",e)},"om.jobsheet.getHashId":function(t,e){return(0,c.v4)()},"om.jobsheet.checkDuplicate":function(t,e){var s=v(JSON.stringify(e.records));return t.dispatch("om.jobs.find",s).then((function(t){if(t)throw"Jobsheet has been uploaded";return s}))},"om.jobsheet.accept":function(t,e){return(0,o["default"])(regeneratorRuntime.mark((function s(){var a;return regeneratorRuntime.wrap((function(s){while(1)switch(s.prev=s.next){case 0:return s.next=2,t.dispatch("om.jobsheet.archive",e.file);case 2:return a=s.sent,s.abrupt("return",t.dispatch("om.job.create",{job_id:e.drop_id||v(JSON.stringify(e.records)),records:e.records,project_id:e.project_id,dropfile:e.dropfile,file:a}));case 4:case"end":return s.stop()}}),s)})))()},"om.jobsheet.archive":function(t,e){console.log("UPLOAD FILE TO S3",e)},"om.job.create":function(t,e){var s=e.job_id,a=e.records,i=e.project_id,n=e.file,o=e.dropfile;return l["default"].post(p("/obase/jobs"),{job_id:s,records:a,project_id:i,file:n,dropfile:o}).then((function(e){return t.dispatch("om.jobs.load"),e.data.jobsheet}))},"om.jobs.load":function(t){return l["default"].get(p("/obase/jobs")).then((function(e){return t.commit("SET_JOBS",e.data.jobs),e.data.jobs}))},"om.jobs.unassigned":function(t){return l["default"].get(p("/obase/jobs/unassigned")).then((function(e){t.commit("SET_UNASSIGNED",e.data.tasks.filter((function(t){return"UNASSIGNED"==t.Status})))}))},"om.jobs.allunassigned":function(t){return l["default"].get(p("/obase/jobs/unassigned")).then((function(e){t.commit("SET_UNASSIGNED",e.data.tasks.filter((function(t){return"UNASSIGNED"==t.Status||"BLOCKED"==t.Status})))}))},"om.jobs.find":function(t,e){return l["default"].get(p("/obase/jobs/".concat(e))).then((function(e){if(e.data.job)return t.commit("SET_JOB",e.data.job),e.data.job}))["catch"]((function(t){if(t.response&&404==t.response.status)return!1;throw t}))},"om.roles.currentUser":function(t,e){if(t.state.currentUserRoles)return Promise.resolve(t.state.currentUserRoles);var s=l["default"].get(p("/obase/roles/".concat(e))).then((function(e){t.commit("SET_CURRENTUSER_ROLES",e.data.roles)}));return t.commit("SET_CURRENTUSER_ROLES",s),s},"om.roles.addUser":function(t,e){if(e.user.staff_id){var s=l["default"].post(p("/obase/roles"),e).then((function(e){var s=l["default"].get(p("/obase/roles")).then((function(e){return t.commit("SET_USERROLE",e.data.roles),e.data.roles}));t.commit("SET_USERROLE",s)}));return s}},"om.roles.removeUser":function(t,e){return t.commit("REMOVE_USERROLE",e),l["default"]["delete"](p("/obase/roles?staff_id=".concat(e.staff_id,"&role=").concat(e.role)))},"om.roles.changeStatus":function(t,e){var s=l["default"].put(p("/obase/roles"),e).then((function(e){var s=l["default"].get(p("/obase/roles")).then((function(e){return t.commit("SET_USERROLE",e.data.roles),e.data.roles}));t.commit("SET_USERROLE",s)}));return s},"om.roles.load":function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t.state.user_roles&&!e)return Promise.resolve(t.state.user_roles);var s=l["default"].get(p("/obase/roles")).then((function(e){return t.commit("SET_USERROLE",e.data.roles),e.data.roles}));return t.commit("SET_USERROLE",s),s},"om.roles.switch":function(t,e){-1==t.state.currentUserRoles.indexOf(e)&&(e=u.head(t.state.currentUserRoles)),t.commit("SET_CURRENTUSER_ROLE",e)},"om.processors.recentTasks":function(t){return l["default"].get(p("/obase/processors/recent-tasks")).then((function(e){return t.commit("MY_TASKS",e.data.tasks),e.data.tasks}))},"om.processors.list":function(t){return t.dispatch("om.roles.load").then((function(){return t.commit("SET_PROCESSORS",u.values(t.state.user_roles.processors)),t.state.processors}))},"om.processors.workload":function(t,e){return l["default"].get(p("/obase/processors/".concat(e))).then((function(t){return t.data.tasks}))},"om.processor.assign":function(t,e){var s=e.processor,a=e.tasks;return l["default"].post(p("/obase/processors/assign"),{processor:s,tasks:a}).then((function(t){return alert("".concat(a.length," task(s) assigned to ").concat(s.name)),t}))},"om.processors.mytasks":function(t,e){return l["default"].get(p("/obase/processors/mytasks")).then((function(e){return t.commit("MY_TASKS",e.data.tasks),e.data.tasks})),l["default"].get(p("/obase/processors/mytasks?limit=100")).then((function(e){return t.commit("MY_TASKS",e.data.tasks),e.data.tasks}))},"om.tasks.recent":function(t){return l["default"].get(p("/obase/tasks/recent")).then((function(t){return t.data.tasks}))},"om.tasks.load":function(t,e){return l["default"].get(p("/obase/tasks/".concat(e))).then((function(t){return t.data.task}))},"om.tasks.blockage":function(t,e){var a=e.task,i=e.blockage;if(t.getters.om_role(["supervisors","processors","executives"])){var o=s("26d3")(a);if(o.canTransTo("BLOCKED"))return Promise.all([t.dispatch("om.comments.post",{PK:"".concat(a.PK,".").concat(a.SK),message:i.remarks,type:i.blockage_type}),l["default"].put(p("/obase/tasks/".concat(a.task_id)),i)]).then((function(t){var e=(0,n["default"])(t,2),s=(e[0],e[1]);return s.data.task}));alert("".concat(a.Status," cannot be block"))}else alert("You are not allowed to block")},"om.tasks.patch":function(t,e){if(t.getters.om_canPatchFeedback(e.task))return l["default"].put(p("/obase/tasks/".concat(e.task.task_id)),{type:"patch",data:e.data}).then((function(t){return t.data.task}));alert("Unauthorized")},"om.tasks.cancel":function(t,e){var a=e.task,i=e.cancellation;if(t.getters.om_role(["supervisors","processors","executives"])){var o="cancellation"==i.type?"CANCELLED":"CANCEL_SUBMITTED",r=s("26d3")(a);if(r.canTransTo(o))return Promise.all([t.dispatch("om.comments.post",{PK:"".concat(a.PK,".").concat(a.SK),message:i.remarks,type:"Cancellation"}),l["default"].put(p("/obase/tasks/".concat(a.task_id)),i)]).then((function(t){var e=(0,n["default"])(t,2),s=(e[0],e[1]);return s.data.task}));alert("".concat(a.Status," cannot be ").concat(o))}else alert("You are not allowed to cancel")},"om.tasks.update":function(t,e){return l["default"].post(p("/obase/tasks/".concat(e.task_id)),e.data).then((function(t){return t.data.task}))},"om.projects.updateTitle":function(t,e){var s=e.project_id,a=e.title;return l["default"].put(p("/obase/projects/".concat(s)),{title:a}).then((function(e){t.dispatch("om.project.load",s)}))},"om.projects.delete":function(t,e){return l["default"]["delete"](p("/obase/projects/".concat(e))).then((function(t){return t.data}))},"om.project.create":function(t,e){return l["default"].post(p("/obase/projects"),e).then((function(e){return t.dispatch("om.project.list"),e.data.project}))},"om.project.list":function(t){return t.commit("START_LOADING","projects"),l["default"].get(p("/obase/projects")).then((function(e){return t.commit("STOP_LOADING","projects"),t.commit("UPDATE_PROJECTS",u.indexBy(u.prop("PK"),e.data.projects)),e.data.projects}))},"om.project.load":function(t,e){return l["default"].get(p("/obase/projects/".concat(e))).then((function(e){return t.commit("UPDATE_PROJECTS",u.mergeAll([t.state.projects,(0,r["default"])({},e.data.project.PK,e.data.project)])),e.data.project}))},"om.migrate":function(t,e){return l["default"].get(p("/obase/migrate")).then((function(t){console.log(t.data),alert(t.data.message)}))},"om.comments.get":function(t,e){return l["default"].get(p("/obase/comments/".concat(e))).then((function(t){return t.data.comments}))},"om.comments.post":function(t,e){return l["default"].post(p("/obase/comments/".concat(e.PK)),{message:e.message,time:(0,d["default"])().unix(),type:e.type||"notes"}).then((function(t){return t.data}))},"om.comments.latest":function(t,e){return l["default"].post(p("/obase/comments/latest"),{ids:e}).then((function(t){return t.data.comments}))},"om.attachments.get":function(t,e){var s=e.resource,a=e.attachment_type;return l["default"].get(p("/obase/attachments/".concat(s.PK,"?SK=").concat(s.SK,"&attachment_type=").concat(a))).then((function(e){return t.commit("SET_ATTACHMENT",{attachment_type:a,key:s.SK,attachments:e.data.attachments}),e.data.attachments}))},"om.attachments.download":function(t,e){return l["default"].post(p("/obase/attachments/download"),e).then((function(t){return t.data.signed_url}))},"om.attachments.upload":function(t,e){var s=e.file,a=e.resource,i=e.attachment_type;return(0,o["default"])(regeneratorRuntime.mark((function e(){var n,r,c,u,f;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=l["default"].defaults.headers.common["Authorization"],i||(i=(0,d["default"])().unix()),r=s.type,e.next=5,l["default"].post(p("/obase/attachments"),{PK:a.PK,SK:a.SK,attachment_type:i,contentType:r}).then((function(t){return t.data}));case 5:return c=e.sent,u=c.signed_url,f=c.key,delete l["default"].defaults.headers.common["Authorization"],console.log("GET SIGNED URL"),e.abrupt("return",l["default"].put(u,s,{headers:{"Content-Type":r}})["catch"]((function(t){return{error:t}})).then(function(){var e=(0,o["default"])(regeneratorRuntime.mark((function e(o){var c;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(l["default"].defaults.headers.common["Authorization"]=n,!o.error){e.next=3;break}throw o.error;case 3:return e.next=5,l["default"].post(p("/obase/attachments/".concat(a.PK)),{key:f,contentType:r,attachment_type:i,filename:s.name}).then((function(t){return t.data.attachment}));case 5:return c=e.sent,t.commit("SET_ATTACHMENT",{attachment_type:i,key:a.SK,attachments:[c]}),e.abrupt("return",c);case 8:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()));case 11:case"end":return e.stop()}}),e)})))()}}},_=m;e["default"]=_,t.exports=e.default,t.exports.default=e.default},ed06:function(t,e,s){"use strict";s.r(e);var a=s("6d6b"),i=s("0c91");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},ed48:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[t._m(0),s("div",{staticClass:"column"},[s("div",{staticClass:"columns"},[s("div",{staticClass:"column fullheight-scroller has-background-white-bis is-paddingless",staticStyle:{"border-right":"solid 1px hsl(0, 0%, 86%)"}},[s("div",{staticClass:"column"},[s("aside",{staticClass:"menu",staticStyle:{"padding-bottom":"40px"}},[s("p",{staticClass:"menu-label"},[t._v("\n                        Tasks\n                    ")]),s("ul",{staticClass:"menu-list"},[s("li",[s("router-link",{attrs:{to:"/jobs/unassigned"}},[t._v("Unassigned tasks")])],1),s("li",[s("router-link",{attrs:{to:"/jobs/recent"}},[t._v("Recent tasks")])],1)]),s("div",{staticClass:"menu-label"},[s("div",{staticClass:"level"},[s("div",[t._v("Projects")]),t.$store.getters.om_has_role(["project_manager","supervisors","executives"])?s("a",{staticClass:"is-pulled-right is-small button",on:{click:function(e){t.projectForm=!0}}},[t._v("+")]):t._e()])]),s("ul",{staticClass:"menu-list"},[s("li",{staticStyle:{"margin-bottom":"10px"}},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.filterProject,expression:"filterProject"}],staticClass:"input is-rounded is-small",attrs:{type:"text",placeholder:"filter"},domProps:{value:t.filterProject},on:{input:function(e){e.target.composing||(t.filterProject=e.target.value)}}})]),t._l(t.projects,(function(e){return s("li",{key:e.PK},[s("router-link",{attrs:{to:"/jobs/projects/"+e.PK}},[t._v(t._s(e.name))])],1)})),t.isLoading("projects")?s("li",[s("span",{staticClass:"button is-text is-loading is-fullwidth"})]):t._e()],2),t._m(1),s("ul",{staticClass:"menu-list"},[s("li",[s("div",{staticClass:"control",staticStyle:{"padding-bottom":"10px"}},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.processor_filter,expression:"processor_filter"}],staticClass:"is-search input is-rounded  is-small",attrs:{type:"text",placeholder:"Search processor"},domProps:{value:t.processor_filter},on:{input:function(e){e.target.composing||(t.processor_filter=e.target.value)}}})])]),t._l(t.filtered_processors,(function(e){return s("li",{key:e.staff_id},[s("router-link",{staticClass:"is-size-7",attrs:{to:"/jobs/processors/"+e.staff_id}},[t._v(t._s(e.name))])],1)}))],2)])])]),s("div",{staticClass:"column fullheight-scroller-2 is-10 is-hidden-mobile",on:{scroll:t.onscroll}},[s("router-view")],1),t.projectForm?s("new-project-form",{on:{close:function(e){t.projectForm=!1},onsubmit:t.handleCreateProject}}):t._e()],1)])])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"Processor"},[s("div",{staticClass:"card-content has-background-light"},[s("div",{staticClass:"level"},[s("div",[s("span",[s("span",{staticClass:"title is-5"},[t._v("Job Distribution")])])])])])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"menu-label"},[s("div",{staticClass:"level"},[s("div",[t._v("Processors")])])])}]},ee0c:function(t,e,s){"use strict";s.r(e);var a=s("7c89"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},eed5:function(t,e,s){"use strict";var a=s("4ea4"),i=s("dbce");s("4de4"),s("4160"),s("b0c0"),s("d3b7"),s("3ca3"),s("159b"),s("ddb0"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var n=i(s("b17e")),o=a(s("c1df")),r=a(s("f137")),l=a(s("350b")),c=a(s("26d3")),d=a(s("d459")),u={components:{TableTasks:r["default"],QuickviewTask:l["default"],TaskAssignment:d["default"]},data:function(){return{tab:"tasks",task:!1,assignment_mode:!1,selected_tasks:{},processor:!1,isLoading:!1,selectedDistribution:!1,everyone:!1,tasks:[],defaultFilterType:"week"}},computed:{defaultFilter:function(){return this.$route.query.status?'"Status":"'.concat(this.$route.query.status):""},me:function(){return this.$store.state.login.user},filteredTasks:function(){var t=this;return this.tasks.filter((function(e){return!!t.everyone||e.CreatedBy.staff_id==t.me.staff_id}))},total_selected:function(){return n.pipe(n.values,n.sum)(this.selected_tasks)},processors:function(){return this.$store.state.om.processors},processorsMapping:function(){return n.indexBy(n.prop("staff_id"),this.processors)}},methods:{getProcessorName:function(t){return(this.processorsMapping[t]||{name:""}).name},handleAssign:function(t){var e=this;if(!this.isLoading){this.isLoading=!0;var s=this.tasks.filter((function(t){return e.selected_tasks[t.TaskID]}));this.$store.dispatch("om.processor.assign",{processor:t,tasks:s}).then((function(a){s.forEach((function(e){e.Status="UNASSIGNED"==e.Status?"ASSIGNED":e.Status,e.GS1=t.staff_id})),e.isLoading=!1,e.selected_tasks={},e.processor=""}))["catch"]((function(t){e.isLoading=!1}))}},selectAll:function(){this.$set(this,"selected_tasks",n.pipe(n.indexBy(n.prop("TaskID")),n.map((function(){return!0})))(this.filteredTasks))},clearSelection:function(){this.selected_tasks={}},isSelected:function(t){return this.assignment_mode&&this.selected_tasks[t.TaskID]},loadData:function(){var t=this;return Promise.all([this.$store.dispatch("om.jobs.unassigned"),this.$store.dispatch("om.processors.list"),this.$store.dispatch("om.tasks.recent").then((function(e){t.tasks=e}))])},canAssign:function(t){return(0,c["default"])(t).canTransTo("ASSIGNED")||(0,c["default"])(t).canTransTo("ASSIGNED_RERAISE")},viewTask:function(t){if(this.assignment_mode){if(!this.canAssign(t))return void alert("You cannot assign this task");this.$set(this.selected_tasks,t.TaskID,!this.selected_tasks[t.TaskID])}else this.task=t}},mounted:function(){var t=this;this.defaultFilterType=this.$route.query.defaultFilterType||"week",this.everyone=this.$route.query.everyone,this.loadData().then((function(){t.$store.dispatch("isLoaded")}))},filters:{shortKey:function(t){return n.take(12,t)},formatDate:function(t){return(0,o["default"])(t).format("DD MMM YYYY")}}};e["default"]=u,t.exports=e.default,t.exports.default=e.default},f00e:function(t,e,s){"use strict";s.r(e);var a=s("739c"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},f137:function(t,e,s){"use strict";s.r(e);var a=s("697d"),i=s("5d92");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},f181:function(t,e,s){"use strict";s.r(e);var a=s("4688"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},f1c3:function(t,e,s){"use strict";var a=s("dbce");s("ac1f"),s("1276"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=a(s("b17e")),n={props:["data","sortfields","defaultSort"],data:function(){return{sortBy:"descend",sortKey:"updatedAt"}},computed:{sortedPath:function(){return this.sortKey.split(".")},sortedData:function(){return i.sort(i[this.sortBy](i.view(i.lensPath(this.sortedPath)||99999)),this.data)}},mounted:function(){this.defaultSort&&(this.sortKey=this.defaultSort),this.updateData()},methods:{sort:function(){this.sortBy="ascend"==this.sortBy?"descend":"ascend"},updateData:function(){this.$emit("sorted",this.sortedData)}},watch:{sortedData:function(t){this.updateData()}}};e["default"]=n,t.exports=e.default,t.exports.default=e.default},f28e:function(t,e,s){"use strict";var a=s("30e7"),i=s.n(a);i.a},f45e:function(t,e,s){"use strict";var a=s("222f"),i=s.n(a);i.a},f488:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("form",{staticClass:"form form-horizontal",on:{submit:function(e){return e.preventDefault(),t.handleSubmit(e)}}},[s("div",{staticClass:"notification"},[s("p",[t._v("Current template:  "),s("a",{on:{click:t.downloadTemplate}},[t._v(t._s(t.tpl.file.details.filename))])]),s("p",[t._v("Current version: "+t._s(t.tpl.version))])]),s("div",{staticClass:"field has-addons"},[t._m(0),s("div",{staticClass:"control"},[s("input",{directives:[{name:"model",rawName:"v-model",value:t.version,expression:"version"},{name:"validate",rawName:"v-validate",value:"required",expression:"'required'"}],staticClass:"input",class:{"is-danger":t.errors.first("version")},attrs:{type:"text",placeholder:t.tpl.version,name:"version"},domProps:{value:t.version},on:{input:function(e){e.target.composing||(t.version=e.target.value)}}})]),s("div",{staticClass:"control"},[s("div",{staticClass:"file",class:{"is-danger":t.errors.first("file")}},[s("label",{staticClass:"file-label"},[s("input",{directives:[{name:"validate",rawName:"v-validate",value:"required",expression:"'required'"}],staticClass:"file-input",attrs:{type:"file",name:"file"},on:{change:t.analyzeFile}}),s("span",{staticClass:"file-cta"},[s("span",{staticClass:"file-label"},[t._v("\n                                "+t._s(t.filename||"Select the template")+"\n                            ")])])])])])]),s("div",{staticClass:"field"},[s("div",{staticClass:"buttons"},[s("button",{staticClass:"button is-primary",class:{"is-loading":t.isSaving},attrs:{type:"submit"}},[t._v("Upload template")])])])])])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"control"},[s("div",{staticClass:"button is-static"},[t._v("\n                    Version\n                ")])])}]},f760:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{attrs:{"in-viewport-once":!0}},[t.attachment?s("span",{staticClass:"tag tooltip",attrs:{"data-tooltip":t.filename}},[s("svg",{staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"16"}},[s("path",{attrs:{d:"M18.08 12.42l-6.18 6.19a4.25 4.25 0 0 1-6-6l8-8a2.57 2.57 0 0 1 3.54 0 2.52 2.52 0 0 1 0 3.54l-6.9 6.89A.75.75 0 1 1 9.42 14l5.13-5.12a1 1 0 0 0-1.42-1.42L8 12.6a2.74 2.74 0 0 0 0 3.89 2.82 2.82 0 0 0 3.89 0l6.89-6.9a4.5 4.5 0 0 0-6.36-6.36l-8 8A6.25 6.25 0 0 0 13.31 20l6.19-6.18a1 1 0 1 0-1.42-1.42z"}})])]):t._e()])},i=[]},f94e:function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("div",{staticClass:"sectionheader",staticStyle:{"border-bottom":"solid 1px hsl(0, 0%, 86%)"}},[s("div",{staticClass:"card-content has-background-light"},[s("div",{staticClass:"level"},[t._t("header",[t._m(0)])],2)])]),s("div",{staticClass:"column"},[s("div",{staticClass:"columns"},[s("div",{staticClass:"column fullheight-scroller has-background-white-bis is-paddingless",staticStyle:{"border-right":"solid 1px hsl(0, 0%, 86%)"}},[s("div",{staticClass:"column"},[s("aside",{staticClass:"menu",staticStyle:{"padding-bottom":"40px","min-height":"80vh"}},[t._t("menu")],2)])]),s("div",{staticClass:"column fullheight-scroller-2 is-10 is-hidden-mobile"},[s("router-view")],1)])])])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("span",[s("span",{staticClass:"title is-5"},[t._v("TITLE")])])])}],n=(s("db00"),s("2877")),o={},r=Object(n["a"])(o,a,i,!1,null,null,null);e["default"]=r.exports},fade:function(t,e,s){"use strict";var a=s("c00a"),i=s.n(a);i.a},fb19:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card"},[s("div",{staticClass:"card-content"},[s("div",{staticClass:"field"},[s("div",{staticClass:"file is-centered is-boxed is-fullwidth",class:{"dropzone is-info":"over"==t.dropzoneStatus,"is-success":"uploaded"==t.dropzoneStatus,"is-danger":"error"==t.dropzoneStatus},on:{dragover:function(e){return e.preventDefault(),t.handleDropover(e)},drop:function(e){return e.preventDefault(),t.handleFileChange(e)},dragleave:t.handleDropleaves}},[s("label",{staticClass:"file-label has-text-grey"},[s("input",{staticClass:"file-inputx",attrs:{type:"file",name:"resume"},on:{change:t.handleFileChange}}),s("span",{staticClass:"file-cta",staticStyle:{"min-height":"160px"}},[t.dropzoneStatus?t._e():s("svg",{staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 492.308 492.308",width:"64"}},[s("path",{attrs:{d:"M246.154 161.913L142.885 273.308l14.442 13.384 78.981-85.196V411.5H256V201.495l78.986 85.197 14.442-13.384z"}}),s("path",{attrs:{d:"M242.168 63.726L181.447 0H0v492.308h492.308V63.726h-250.14zm230.447 408.889H19.692V19.692h153.322l60.721 63.726h238.88v389.197z"}})]),"over"==t.dropzoneStatus?s("svg",{staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 492.308 492.308",width:"64"}},[s("path",{attrs:{d:"M332.298 65.827L215.106 184.332 160 128.615l-14 13.847 69.106 69.87L346.298 79.673z"}}),s("path",{attrs:{d:"M400.808 0H91.5L.567 257.365 0 258.952v233.356h492.308V258.957L400.808 0zM105.423 19.692h281.462l81.656 231.101H304.442v9.846c0 32.558-26.144 59.048-58.288 59.048s-58.288-26.49-58.288-59.048v-9.846H23.769l81.654-231.101zm367.192 452.923H19.692v-39.413h452.923v39.413zm0-59.105H19.692V270.486h149.087c4.817 38.788 37.673 68.894 77.375 68.894s72.558-30.106 77.375-68.894h149.087V413.51z"}})]):t._e(),"uploading"==t.dropzoneStatus?s("span",{staticClass:"button is-loading is-fullwidth is-text"}):t._e(),"error"==t.dropzoneStatus?s("span",[s("svg",{staticClass:"animated rollIn",staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"64"}},[s("circle",{attrs:{cx:"15.5",cy:"9.5",r:"1.5"}}),s("circle",{attrs:{cx:"8.5",cy:"9.5",r:"1.5"}}),s("path",{attrs:{d:"M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm0-6c-2.33 0-4.32 1.45-5.12 3.5h1.67c.69-1.19 1.97-2 3.45-2s2.75.81 3.45 2h1.67c-.8-2.05-2.79-3.5-5.12-3.5z"}})])]):t._e(),"uploaded"==t.dropzoneStatus?s("span",[s("svg",{staticClass:"animated bounceIn faster",staticStyle:{fill:"currentColor"},attrs:{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"64"}},[s("path",{attrs:{d:"M14.72 8.79l-4.29 4.3-1.65-1.65a1 1 0 1 0-1.41 1.41l2.35 2.36a1 1 0 0 0 .71.29 1 1 0 0 0 .7-.29l5-5a1 1 0 0 0 0-1.42 1 1 0 0 0-1.41 0zM12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm0 18a8 8 0 1 1 8-8 8 8 0 0 1-8 8z"}})])]):t._e(),s("span",{staticClass:"file-label has-text-centered",staticStyle:{"margin-top":"10px"}},[t.dropzoneStatus?t._e():s("span",[t._v("Upload or Drop "),s("br"),t._v("Order template file here")]),"over"==t.dropzoneStatus?s("span",[t._v("Drop file here")]):t._e(),"uploading"==t.dropzoneStatus?s("span",[t._v("Uploading...")]):t._e(),"uploaded"==t.dropzoneStatus?s("span",[t._v("Uploaded")]):t._e(),"error"==t.dropzoneStatus?s("span",[t._v(t._s(t.message))]):t._e()])])])])])]),s("div",{staticClass:"card-footer"},[s("a",{staticClass:"card-footer-item",on:{click:t.downloadTemplate}},[t._v("\n            Download template file (ver "+t._s(t.tpl.version)+")\n        ")])])])},i=[]},fb2d:function(t,e,s){"use strict";s.r(e);var a=s("2273"),i=s("6fe9");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},fba7:function(t,e,s){"use strict";s.r(e);var a=s("8ab8"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},fc74:function(t,e,s){},fca5:function(t,e,s){"use strict";s.r(e);var a=s("57f0"),i=s("6608");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);s("4562");var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},fd5b:function(t,e,s){"use strict";var a=s("4ea4");s("ac1f"),s("5319"),s("4795"),Object.defineProperty(e,"__esModule",{value:!0}),e["default"]=void 0;var i=s("b5ae"),n=s("1dce"),o=a(s("506f")),r={props:["app"],mixins:[n.validationMixin],components:{Checking:o["default"]},data:function(){return{form:{staff_id:"",callback_url:"",app:this.app},isLoading:!1,notification:{},animation:!1}},validations:{form:{staff_id:{required:i.required}}},computed:{show:function(){return"logout"===this.$store.getters.login_status},build:function(){return"2024-12-05T01:16:57.105Z"},hasNotification:function(){return void 0!=this.notification.message},currentUrl:function(){return window.location.href.replace(/\/+#.*/,"")},sso:function(){return this.$route.query.sso}},watch:{sso:function(t){t&&(this.$store.commit("UPDATE_TEMP_TOKEN",{token:t,phrase:"Checking..."}),window.location=this.currentUrl)}},methods:{handleReset:function(){this.$store.dispatch("login.logout")},onError:function(t){this.notify({message:t,type:"is-danger"}),this.handleReset()},handleLogin:function(){var t=this;this.$v.form.$touch(),this.$v.form.$error?this.notify({message:"Please provide valid Login ID",type:"is-danger"}):(this.isLoading=!0,this.$store.dispatch("login.attempt",this.form).then((function(e){t.isLoading=!1,t.notify({message:e.message,type:"is-info"})}))["catch"]((function(e){var s=(e.response||{data:{}}).data.message||"".concat(e);setTimeout((function(){t.isLoading=!1,t.notify({message:s,type:"is-danger"})}),400)})))},notify:function(t){var e=this;this.notification.timer&&(clearTimeout(this.notification.timer),clearTimeout(this.notification.animation));var s=setTimeout((function(){e.notification={}}),2500),a=setTimeout((function(){e.animation="slideOutUp"}),2e3);this.notification=Object.assign({},t,{timer:s,animation:a}),this.animation="slideInDown"}},mounted:function(){this.$emit("mounted"),this.form.callback_url=this.currentUrl}};e["default"]=r,t.exports=e.default,t.exports.default=e.default},fd73:function(t,e,s){"use strict";s.r(e);var a=s("123d"),i=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return a[t]}))}(n);e["default"]=i.a},fe45:function(t,e,s){"use strict";s.r(e);var a=s("f760"),i=s("5661");for(var n in i)["default"].indexOf(n)<0&&function(t){s.d(e,t,(function(){return i[t]}))}(n);var o=s("2877"),r=Object(o["a"])(i["default"],a["a"],a["b"],!1,null,null,null);e["default"]=r.exports},fe95:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement;t._self._c;return t._m(0)},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"column"},[s("div",{staticClass:"level"},[s("div",[s("span",{staticClass:"heading"},[t._v("USER ACCOUNT")]),s("span",{staticClass:"help"},[t._v("CI/CD Test")])])])])}]},ff13:function(t,e,s){"use strict";var a=s("c431"),i=s.n(a);i.a},ffdb:function(t,e,s){"use strict";s.d(e,"a",(function(){return a})),s.d(e,"b",(function(){return i}));var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("section-layout",[s("template",{slot:"header"},[s("div",[s("span",[s("span",{staticClass:"title is-5"},[t._v("Settings")])])])]),s("template",{slot:"menu"},[s("ul",{staticClass:"menu-list"},[t.$store.getters.om_role("supervisors")||t.isAdmin?s("li",[s("router-link",{attrs:{to:"/settings/config"}},[t._v("Config")])],1):t._e()])])],2)},i=[]}});
//# sourceMappingURL=app.324717ae.js.map