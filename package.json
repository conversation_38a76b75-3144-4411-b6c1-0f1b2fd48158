{"name": "simidigital", "author": "SSBS Team", "version": "1.0.0", "description": "Single Interface, Multiple Information Portal for TM Global Internal Use.", "private": true, "scripts": {"dev": "NODE_ENV=development next dev", "build": "next build", "start": "next start", "export": "yarn build && next export -o _static", "lint": "eslint --ext .js,.jsx .", "lint:fix": "eslint --fix --ext .js,.jsx .", "prettier": "prettier --write \"src/**/*.js\"", "clear-all": "rm -rf .next .swc _static node_modules", "re-start": "rm -rf .next .swc _static node_modules && yarn install && yarn dev", "re-build": "rm -rf .next .swc _static node_modules && yarn install && yarn build"}, "dependencies": {"@cyntler/react-doc-viewer": "^1.17.0", "@emotion/cache": "^11.10.5", "@emotion/react": "^11.13.0", "@emotion/server": "^11.10.0", "@emotion/styled": "^11.13.0", "@hookform/resolvers": "^2.9.11", "@iconify/react": "^4.1.0", "@material-ui/core": "^4.12.4", "@mui/icons-material": "^5.11.11", "@mui/lab": "^5.0.0-alpha.120", "@mui/material": "^5.16.6", "@mui/styles": "^5.11.13", "@mui/x-data-grid": "^7.26.0", "@mui/x-date-pickers": "^6.9.2", "@ramonak/react-progress-bar": "^5.0.3", "@reduxjs/toolkit": "^1.9.5", "apexcharts": "^4.5.0", "axios": "^1.3.3", "date-fns": "^2.29.3", "dayjs": "^1.11.9", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "encodeurl": "^1.0.2", "framer-motion": "^9.0.4", "highcharts": "^12.2.0", "highcharts-react-official": "^3.2.1", "html-react-parser": "^5.1.10", "html-to-draftjs": "^1.5.0", "json-2-csv": "^5.0.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "leaflet": "^1.9.4", "lodash": "^4.17.21", "lucide-react": "^0.473.0", "moment": "^2.29.4", "moment-business-days": "^1.2.0", "next": "^14.2.5", "next-seo": "^6.6.0", "next-themes": "^0.2.1", "notistack": "^2.0.8", "nprogress": "^0.2.0", "papaparse": "^5.4.1", "prop-types": "^15.8.1", "qrcode.react": "^3.1.0", "ramda": "^0.27.1", "random-number-in-range": "^1.0.2", "randomcolor": "^0.6.2", "react": "^18.2.0", "react-apexcharts": "^1.7.0", "react-dom": "^18.2.0", "react-draft-wysiwyg": "^1.15.0", "react-draggable": "^4.4.6", "react-dropzone": "^14.2.3", "react-easy-crop": "^5.0.5", "react-hook-form": "^7.43.1", "react-joyride": "^2.9.3", "react-konva": "^19.0.1", "react-lazy-load-image-component": "^1.5.6", "react-leaflet": "^4.2.1", "react-loader-spinner": "^5.4.5", "react-material-ui-carousel": "^3.4.2", "react-qr-reader": "^3.0.0-beta-1", "react-redux": "^8.1.2", "react-swipeable": "^7.0.1", "react-to-pdf": "^1.0.1", "react-type-animation": "^3.1.0", "sass": "^1.76.0", "sharp": "^0.33.4", "simplebar-react": "^3.2.1", "stylis": "^4.1.3", "stylis-plugin-rtl": "^2.1.1", "tailwind-merge": "^1.14.0", "tailwind-scrollbar": "^3.0.5", "xlsx": "^0.18.5", "yup": "^1.0.0"}, "devDependencies": {"@babel/core": "^7.21.0", "@babel/eslint-parser": "^7.19.1", "@next/eslint-plugin-next": "^13.4.19", "autoprefixer": "^10.4.14", "eslint": "^8.34.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.6.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.25", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.5.4", "tailwindcss": "^3.3.2", "typescript": "^4.9.5"}}