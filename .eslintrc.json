{"extends": "next/core-web-vitals", "rules": {"react/prop-types": "off", "arrow-body-style": "off", "react/self-closing-comp": "off", "react/button-has-type": "off", "jsx-a11y/no-static-element-interactions": "off", "jsx-a11y/click-events-have-key-events": "off", "jsx-a11y/label-has-associated-control": "off", "no-nested-ternary": "off", "import/order": "off", "import/no-unresolved": "off", "import/extensions": "off", "no-unused-vars": "warn", "no-else-return": "off", "prefer-const": "warn", "one-var": "off", "no-shadow": "off", "react/jsx-boolean-value": "off"}}