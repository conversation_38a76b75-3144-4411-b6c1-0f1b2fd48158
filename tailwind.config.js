/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{js,ts,jsx,tsx,mdx}'],
  theme: {
    extend: {
      fontFamily: {
        Roboto: ['Roboto', 'sans-serif'],
        RobotoMedium: ['RobotoMedium', 'sans-serif'],
        RobotoRegular: ['RobotoRegular', 'sans-serif'],
        HkGrotesk: ['HkGrotesk', 'sans-serif'],
      },
      boxShadow: {
        dashboardCard: '0px 4px 18px rgba(47, 43, 61, 0.1)',
        dashboardCardDark: '0px 4px 18px rgba(255, 255, 255, 0.5)',
      },
      container: {
        center: true,
        padding: '1rem',
      },
      colors: {
        primary: process.env.NEXT_PUBLIC_PRIMARY,
        secondary: process.env.NEXT_PUBLIC_SECONDARY,
        admin: process.env.NEXT_PUBLIC_ADMIN,
        hsba: process.env.NEXT_PUBLIC_HSBA,
        payme: process.env.NEXT_PUBLIC_PAYME,
        ef: process.env.NEXT_PUBLIC_EF,
        loa: process.env.NEXT_PUBLIC_LOA,
        capry: process.env.NEXT_PUBLIC_CAPRY,
        solid: process.env.NEXT_PUBLIC_SOLID,
        fa: process.env.NEXT_PUBLIC_FA,
        pmcare: process.env.NEXT_PUBLIC_PMCARE,
        event: process.env.NEXT_PUBLIC_EVENT,
        itaf: process.env.NEXT_PUBLIC_ITAF,
        voting: process.env.NEXT_PUBLIC_VOTING,
        sdp: process.env.NEXT_PUBLIC_SDP,
        calendar: process.env.NEXT_PUBLIC_CALENDAR,
        support: process.env.NEXT_PUBLIC_SUPPORT,
        noc: process.env.NEXT_PUBLIC_NOC,
        oasys: process.env.NEXT_PUBLIC_OASYS,
        reporting: process.env.NEXT_PUBLIC_REPORTING,
        finsight: process.env.NEXT_PUBLIC_FINSIGHT,
        'ocm-tracker': process.env.NEXT_PUBLIC_OCM_TRACKER,
      },
      animation: {
        'pulse-custom': 'pulse var(--pulse-duration) cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        pulse: {
          '0%, 100%': { opacity: 1 },
          '50%': { opacity: 0.5 },
        },
      },
    },
  },
  darkMode: 'class',
  // eslint-disable-next-line
  plugins: [require('tailwind-scrollbar')],
};
