# CLAUDE.md - SimiDigital Project Documentation

## Overview
This is the CLAUDE.md file for the SimiDigital project - a comprehensive digital platform containing multiple business modules.

## FA (Financial Analysis) Module

### **System Overview**
The Financial Analysis (FA) module is a comprehensive project management and financial analysis platform designed for business investment evaluation and approval workflows.

### **Core Features**

#### **1. Dashboard (`/fa/index.js:28-191`)**
- **Summary Statistics**: Displays project counts by status (Pending Approval, Pending Endorsement, Pending Sitting, Completed)
- **Role-based Access Control**: Admin users see all projects, regular users see only their projects and those where they are approvers
- **Enhanced UI**: Modern gradient design with animated statistics cards and responsive layout

#### **2. Project Management (`/components/fa/AllProjects.js`)**
- **Complete CRUD Operations**: Create, read, update, delete projects
- **Project Filtering**: Search and filter projects by various criteria
- **Budget Categories**: Support for multiple budget ranges (below RM 100k, RM 100k-500k, RM 500k-5M, above RM 5M)
- **Status Management**: Draft, approval, endorsement, sitting, completed statuses

#### **3. Project Details (`/pages/fa/project/[projectId].js`)**
- **Hierarchical Navigation**: Project → Scenarios → Financial Analysis
- **Dynamic Access Control**: Edit permissions based on draft status and user roles
- **Approval Workflow Integration**: Multi-stage approval process with stepper UI
- **Financial Analysis**: Comprehensive financial modeling and scenario planning

#### **4. Scenario Management**
- **Multiple Scenarios**: Support for different financial scenarios per project
- **Scenario Comparison**: Side-by-side analysis of different financial models
- **Dynamic Calculations**: Real-time financial calculations and projections

### **Technical Architecture**

#### **API Endpoints (`/utils/fa.js:5`)**
- **Base Endpoint**: `/rNEDFtjfnYqxXwD1r8bNh48NTgXzVe`
- **RESTful Operations**: Full CRUD support for projects, scenarios, approvals, and history

#### **State Management (`/utils/store/faReducer.js`)**
- **Redux Toolkit**: Centralized state management with async thunks
- **Project Data**: `fetchProjectData()` for individual project details
- **Scenarios**: `fetchAllScenarios()` and `fetchScenarioData()` for scenario management
- **Loading States**: Built-in loading and error state management

#### **Data Models**
- **Projects**: name, budget_range, status, created_by_staff_id, viewer_staff_id_list
- **Scenarios**: project-linked financial models with calculations
- **Approvals**: Multi-level approval workflow with endorser, reviewer, approver roles
- **History**: Audit trail of all project actions

#### **Access Control (`/layouts/module/fa.js:29-145`)**
- **Role-based Permissions**: Admin/User role distinction
- **Project-level Security**: Viewer access control and approval-based permissions
- **Pattern Matching**: Route-level authorization using regex patterns
- **Error Boundaries**: Comprehensive error handling with `ErrorBoundary` component

### **Key Components**

#### **Financial Analysis Engine (`/components/fa/FinancialAnalysis.js`)**
- **Dynamic Calculations**: Real-time financial modeling
- **Number Formatting**: International currency and percentage formatting
- **Period Management**: Yearly/Monthly display modes with conversion utilities
- **Performance Optimized**: Memoized calculations and loading states

#### **Approval Workflow (`/components/fa/ui/EnhancedApprovalWorkflow.js`)**
- **Multi-stage Process**: Endorsement → Review → Approval → Sitting
- **User Selection**: Dynamic approver selection with role-based filtering
- **Status Tracking**: Visual stepper component showing approval progress
- **Action History**: Complete audit trail of approval actions

#### **UI Components (`/components/fa/ui/`)**
- **Enhanced Components**: Modern, reusable UI components with shadcn/ui integration
- **Loading States**: Skeleton loaders and spinners for better UX
- **Error States**: Network, data, and calculation error handling
- **Responsive Design**: Mobile-first design with Tailwind CSS

### **Module Navigation**
- **Dashboard** (`/fa`): Overview and statistics
- **Rate Management** (`/fa/rate-management`): Admin-only rate configuration
- **Guidelines** (`/fa/guidelines`): Documentation and procedures
- **Access Management** (`/fa/access-management`): Admin-only user permissions

### **Security Features**
- **Input Sanitization**: Built-in XSS protection and data validation
- **CSRF Protection**: Token-based request validation
- **Role-based Authorization**: Granular permission system
- **Audit Logging**: Complete action history tracking

### **Key Strengths**
1. **Comprehensive Workflow Management** - End-to-end project lifecycle support
2. **Advanced Financial Modeling** - Sophisticated calculation engine with scenario support
3. **Robust Access Control** - Multi-level security and permission system
4. **Modern UI/UX** - Contemporary design with excellent user experience
5. **Audit Compliance** - Complete history tracking and approval workflows
6. **Performance Optimized** - Efficient state management and loading strategies
7. **Error Resilience** - Comprehensive error boundaries and handling

### **Development Notes**
- Built with Next.js and React with Redux Toolkit for state management
- Uses Material-UI and Tailwind CSS for consistent styling
- Implements modern React patterns (hooks, memoization, error boundaries)
- Follows security best practices with input validation and sanitization
- Includes comprehensive error handling and user feedback systems

---

## Other Modules
*[Additional module documentation will be added as modules are analyzed]*