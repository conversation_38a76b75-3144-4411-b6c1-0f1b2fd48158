---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: simi-portal
  namespace: portal
  labels:
    app: simi-portal
spec:
  replicas: 1
  selector:
    matchLabels:
      app: simi-portal
  template:
    metadata:
      labels:
        app: simi-portal
    spec:
      imagePullSecrets:
        - name: acr-ee
      containers:
        - name: simi-portal
          image: simi-digital-registry-registry-vpc.ap-southeast-3.cr.aliyuncs.com/simi/<IMAGE>
          ports:
            - containerPort: 3000

---
apiVersion: v1
kind: Service
metadata:
  name: simi-portal
  namespace: portal
  labels:
    app: simi-portal
spec:
  type: ClusterIP
  selector:
    app: simi-portal
  ports:
    - port: 80
      protocol: TCP
      targetPort: 3000

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-portal
  namespace: portal
  annotations:
    kubernetes.io/ingress.class: 'nginx'
spec:
  tls:
    - hosts:
        - simidigital.tm.com.my
      secretName: simidigital-ingress-tls
  rules:
    - host: simidigital.tm.com.my
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: simi-portal
                port:
                  number: 3000
