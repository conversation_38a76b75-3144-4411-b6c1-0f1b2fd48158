---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: simi-auth
  namespace: simi
  labels:
    app: simi-auth
spec:
  replicas: 1
  selector:
    matchLabels:
      app: simi-auth
  template:
    metadata:
      labels:
        app: simi-auth
    spec:
      containers:
      - name: simi-auth
        image: <IMAGE>
        ports:
        - containerPort: 3000
          name: simi-auth

---
apiVersion: v1
kind: Service
metadata:
    name: simi-auth
    namespace: simi
    labels:
        app: simi-auth
spec:
    type: ClusterIP
    selector:
        app: simi-auth
    ports:
    - port: 80
      protocol: TCP  
      targetPort: 3000

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-auth
  namespace: simi
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
    - hosts:
        - aum.backend.simidigital.app
      secretName: aum-backend-simidigital-app-tls
  rules:
    - host: aum.backend.simidigital.app
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: simi-auth
                port:
                  number: 3000